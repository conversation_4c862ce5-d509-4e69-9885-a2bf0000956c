# HRUN 测试平台本地部署指南

## 🎉 部署完成状态

您的HRUN测试平台已成功部署到本地环境！

## 📋 系统配置

### 数据库配置
- **数据库类型**: MySQL 9.3
- **数据库名**: HRUN
- **用户名**: root
- **密码**: test123456
- **主机**: localhost
- **端口**: 3306

### Redis配置
- **主机**: localhost
- **端口**: 6379
- **用途**: 缓存和Celery任务队列

### 环境配置
- **Python版本**: 3.13
- **Django版本**: 3.2.13+
- **Vue.js版本**: 3.x
- **Node.js版本**: 最新LTS

## 🌐 访问地址

- **前端界面**: http://localhost:8080
- **后端API**: http://localhost:8000
- **管理后台**: http://localhost:8000/admin

## 🔑 默认账户

- **管理员用户名**: admin
- **管理员密码**: 123456

## 🚀 启动服务

### 启动后端服务
```bash
cd backend
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000
```

### 启动前端服务
```bash
cd frontend
npm run serve
```

## 🔧 环境变量配置

### 开发环境 (.env.development)
```
VUE_APP_API_BASE_URL=http://localhost:8000
VUE_APP_WS_BASE_URL=ws://localhost:8000
```

### 生产环境 (.env.production)
```
VUE_APP_API_BASE_URL=http://*************:8000
VUE_APP_WS_BASE_URL=ws://*************:8000
```

## 📝 重要说明

1. **Python兼容性**: 由于使用Python 3.13，部分API文档功能被临时禁用
2. **PyMySQL配置**: 已配置PyMySQL替代mysqlclient以解决兼容性问题
3. **环境变量**: 前端API地址现在通过环境变量动态配置

## 🛠️ 故障排除

### 如果前端无法连接后端
1. 检查后端服务是否正常运行在8000端口
2. 确认前端环境变量配置正确
3. 重启前端服务以加载新的环境变量

### 如果数据库连接失败
1. 确认MySQL服务正在运行
2. 检查数据库用户名和密码
3. 确认数据库HRUN已创建

### 如果Redis连接失败
1. 启动Redis服务: `brew services start redis`
2. 检查Redis是否运行在6379端口

## 📞 技术支持

如需技术支持，请检查：
1. 服务进程状态
2. 日志文件错误信息
3. 网络连接状态
4. 环境变量配置

---
部署时间: $(date)
部署版本: 本地开发版本
