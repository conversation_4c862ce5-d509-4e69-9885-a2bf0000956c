# HRUN测试平台
## 平台地址
- 在线预览
  <a href="http://119.29.101.15:5002/" target="_blank">http://119.29.101.15:5002/</a>

- Gitee地址
    <a href="https://gitee.com/hsjtest/hrun" target="_blank">https://gitee.com/hsjtest/hrun</a>

- GitHub地址
    <a href="https://github.com/heshaojun2021/hrun" target="_blank">https://github.com/heshaojun2021/hrun</a>

- GitCode地址
    <a href="https://gitcode.com/heshaojun/hrun" target="_blank">https://gitcode.com/heshaojun/hrun</a>
## 🔷backend

#### 软件架构
###### 软件架构说明
```
基于 python3 + Django + celery + mariadb + redis
```

#### 安装教程
###### 手动部署
```
cd /backend
1、创建虚拟环境执行依赖包：pip install -r requirements.txt
2、backend/primaryApp/settings/dev.py或pro.py修改自己的数据库和Redis配置信息
3、数据库迁移：python manage.py makemigrations
4、数据库执行迁移文件：python manage.py migrate
5、脚本初始化执行:
python manage.py shell -c "
import django
from django.apps import apps
from django.db import models

# 动态创建Project模型
if 'projects' not in apps.app_configs:
    class TempProject(models.Model):
        class Meta:
            app_label = 'projects'
    
    apps.app_configs['projects'] = type('TempConfig', (), {
        'label': 'projects',
        'models': {'project': TempProject}
    })

# 正常初始化
django.setup()

from django.contrib.auth import get_user_model
try:
    from apps.projects.models import Project
except RuntimeError:
    # 如果仍然失败，使用动态创建的模型
    Project = apps.get_model('projects', 'Project')

print('=== 初始化开始 ===')

# 创建管理员
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '13888888888', '123456')
    print('管理员创建成功: admin/123456')
else:
    print('管理员已存在')

# 创建示例项目
try:
    leader = User.objects.get(username='admin')
    if not Project.objects.filter(name='示例项目').exists():
        Project.objects.create(name='示例项目', desc='这是一个示例项目', leader=leader)
        print('项目创建成功')
    else:
        print('示例项目已存在')
except Exception as e:
    print('错误:', str(e))

print('=== 初始化完成 ===')
"
6、运行Django服务：python manage.py runserver
```
###### 自动部署
```
1、服务器中安装docker和docker-compose
2、运行 sh deploy.sh
```
#### 使用说明
###### static配置文件生成
```
python manage.py collectstatic
```
###### 数据库迁移
```
python manage.py makemigrations
```
###### 数据库执行迁移文件
```
python manage.py migrate
```
###### 运行Django服务
```
python manage.py runserver
```


## 🔷frontend

#### 软件架构
###### 软件架构说明
```
基于 js + vue3 + element-plus + echarts
```
###### 安装node.js
```
官网地址：https://nodejs.org/en
```

###### 项目初始化
```
npm install
```

###### 开发或测试环境启动
```
npm run serve
```

###### 生产环境发布打包
```
npm run build
```
#### 目录结构
```
dist -- 存放生产部署打包配置
node_modules -- 项目所需的各种依赖包和模块
public -- 浏览器展示的启动标签等信息
src：
    api -- 存放所有的接口
    assets -- 存放全局的js、css、图片等信息
    components -- 存放公共组件
    plugins -- element的配置文件
    router -- path配置
    store -- 数据共享等配置
    views -- 各个页面的vue代码存放
```
## 瞅一眼吧
![img.png](git_img/img.png)
![img_1.png](git_img/img_1.png)
![img_2.png](git_img/img_2.png)
![img_3.png](git_img/img_3.png)
![img_8.png](git_img/img_8.png)
![img_4.png](git_img/img_4.png)
![img_6.png](git_img/img_6.png)
![img_9.png](git_img/img_9.png)
![img_11.png](git_img/img_11.png)
![img_10.png](git_img/img_10.png)
![img_12.png](git_img/img_12.png)


## 贡献
欢迎参与贡献，欢迎提issue，联系微信：W1565560575

## 规划
目前平台还在持续开发中，后续会持续更新，敬请期待！

## 感谢
```
如果觉得对你的项目有帮助请点个star。这将是对我极大的鼓励与支持, 平台会持续迭代更新。
```
## 请我喝杯咖啡
<img src="git_img/img_14.png" width="400" height="500" alt="图片14">
