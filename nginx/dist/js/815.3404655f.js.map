{"version": 3, "file": "js/815.3404655f.js", "mappings": "6LAEOA,MAAA,kB,+FAALC,EAAAA,EAAAA,IAkBM,MAlBNC,EAkBM,C,aAjBJC,EAAAA,EAAAA,IAAmB,UAAf,cAAU,I,aACdA,EAAAA,EAAAA,IAAmB,SAAhB,gBAAY,KACfC,EAAAA,EAAAA,IAIWC,EAAA,C,WAHAC,EAAAC,a,qCAAAD,EAAAC,aAAYC,GACrBC,YAAY,SACZT,MAAA,uC,wBAEFI,EAAAA,EAAAA,IAAsEM,EAAA,CAA3DC,KAAK,UAAWC,QAAOC,EAAAC,kB,kBAAkB,IAAMC,EAAA,KAAAA,EAAA,K,QAAN,a,wCAEpDZ,EAAAA,EAAAA,IAOM,OAPDH,MAAA,uBAAyB,EAC5BG,EAAAA,EAAAA,IAAc,UAAV,UACJA,EAAAA,EAAAA,IAIK,YAHHA,EAAAA,EAAAA,IAA0D,YAAtDA,EAAAA,EAAAA,IAAiD,KAA9Ca,KAAK,gCAA+B,aAC3Cb,EAAAA,EAAAA,IAA0D,YAAtDA,EAAAA,EAAAA,IAAiD,KAA9Ca,KAAK,gCAA+B,aAC3Cb,EAAAA,EAAAA,IAA0D,YAAtDA,EAAAA,EAAAA,IAAiD,KAA9Ca,KAAK,gCAA+B,gB,mBAOnD,GACEC,KAAM,mBACNC,IAAAA,GACE,MAAO,CACLX,aAAc,IAElB,EACAY,QAAS,CACPL,gBAAAA,GACOM,KAAKb,aAIVa,KAAKC,QAAQC,KAAK,CAChBL,KAAM,2BACNM,OAAQ,CAAEC,GAAIJ,KAAKb,gBALnBa,KAAKK,SAASC,MAAM,UAOxB,I,WCpCJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,O", "sources": ["webpack://frontend-web/./src/views/TestReportDetail.vue", "webpack://frontend-web/./src/views/TestReportDetail.vue?7eb0"], "sourcesContent": ["<!-- 这是一个临时测试页面，用于测试报告详情页面的路由 -->\n<template>\n  <div style=\"padding: 20px;\">\n    <h3>性能报告详情页面测试</h3>\n    <p>请输入报告ID进行测试：</p>\n    <el-input \n      v-model=\"testReportId\" \n      placeholder=\"输入报告ID\" \n      style=\"width: 200px; margin-right: 10px;\">\n    </el-input>\n    <el-button type=\"primary\" @click=\"goToReportDetail\">查看报告详情</el-button>\n    \n    <div style=\"margin-top: 20px;\">\n      <h4>测试链接：</h4>\n      <ul>\n        <li><a href=\"#/PerformanceResult-Detail/1\">报告ID=1</a></li>\n        <li><a href=\"#/PerformanceResult-Detail/2\">报告ID=2</a></li>\n        <li><a href=\"#/PerformanceResult-Detail/3\">报告ID=3</a></li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TestReportDetail',\n  data() {\n    return {\n      testReportId: '1'\n    }\n  },\n  methods: {\n    goToReportDetail() {\n      if (!this.testReportId) {\n        this.$message.error('请输入报告ID')\n        return\n      }\n      this.$router.push({ \n        name: 'PerformanceResult-Detail', \n        params: { id: this.testReportId }\n      })\n    }\n  }\n}\n</script>", "import { render } from \"./TestReportDetail.vue?vue&type=template&id=a7418cb8\"\nimport script from \"./TestReportDetail.vue?vue&type=script&lang=js\"\nexport * from \"./TestReportDetail.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__"], "names": ["style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createVNode", "_component_el_input", "$data", "testReportId", "$event", "placeholder", "_component_el_button", "type", "onClick", "$options", "goToReportDetail", "_cache", "href", "name", "data", "methods", "this", "$router", "push", "params", "id", "$message", "error", "__exports__", "render"], "sourceRoot": ""}