"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[323],{39323:function(e,l,t){t.r(l),t.d(l,{default:function(){return j}});var s=t(56768),a=t(24232);const o={class:"report-push-container"},i={class:"header-actions"},_={class:"table-data"},r={class:"push-type-tag"},d=["innerHTML"],n={class:"webhook-text"},u={class:"user-tags"},c={class:"pagination-container"},p={class:"custom-radio-group"},h={class:"push-icon-wrapper"},k=["innerHTML"],m={class:"push-icon-wrapper"},g=["innerHTML"],b={class:"push-icon-wrapper"},f=["innerHTML"],w=["innerHTML"],F={key:0,class:"webhook-tip"},v={class:"dialog-footer"},y={class:"custom-radio-group"},T={class:"push-icon-wrapper"},E=["innerHTML"],L={class:"push-icon-wrapper"},C=["innerHTML"],P={class:"push-icon-wrapper"},M=["innerHTML"],D=["innerHTML"],W={key:0,class:"webhook-tip"},O={class:"dialog-footer"};function I(e,l,t,I,U,H){const A=(0,s.g2)("el-button"),j=(0,s.g2)("el-table-column"),V=(0,s.g2)("el-tag"),R=(0,s.g2)("el-tooltip"),x=(0,s.g2)("el-table"),K=(0,s.g2)("el-pagination"),B=(0,s.g2)("el-scrollbar"),q=(0,s.g2)("el-card"),X=(0,s.g2)("el-input"),z=(0,s.g2)("el-form-item"),$=(0,s.g2)("el-link"),N=(0,s.g2)("el-option"),S=(0,s.g2)("el-select"),Q=(0,s.g2)("el-form"),Y=(0,s.g2)("el-dialog"),G=(0,s.gN)("loading");return(0,s.uX)(),(0,s.CE)("div",o,[(0,s.Lk)("div",i,[(0,s.bF)(A,{onClick:H.clickAdd,type:"primary",icon:U.Plus,class:"add-button"},{default:(0,s.k6)(()=>l[18]||(l[18]=[(0,s.eW)("新增推送信息")])),_:1,__:[18]},8,["onClick","icon"])]),(0,s.bF)(q,{class:"table-card"},{default:(0,s.k6)(()=>[(0,s.bF)(B,null,{default:(0,s.k6)(()=>[(0,s.Lk)("div",_,[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(x,{data:U.hookList,stripe:"",style:{width:"100%"},"empty-text":"暂无数据",border:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:(0,s.k6)(()=>[(0,s.bF)(j,{label:"序号",align:"center",width:"60"},{default:(0,s.k6)(e=>[(0,s.Lk)("span",null,(0,a.v_)(e.$index+1),1)]),_:1}),(0,s.bF)(j,{label:"推送名称",prop:"name",align:"center"}),(0,s.bF)(j,{label:"推送类型",prop:"pushType",align:"center"},{default:(0,s.k6)(e=>[(0,s.bF)(V,{type:H.getPushTypeTagType(e.row.pushType),effect:"plain"},{default:(0,s.k6)(()=>[(0,s.Lk)("span",r,[(0,s.Lk)("span",{innerHTML:H.getIconSvg(e.row.pushType),class:"iconfont"},null,8,d),(0,s.eW)(" "+(0,a.v_)(H.getPushTypeName(e.row.pushType)),1)])]),_:2},1032,["type"])]),_:1}),(0,s.bF)(j,{label:"hook地址",prop:"webhook",align:"center"},{default:(0,s.k6)(e=>[(0,s.bF)(R,{placement:"top-start",effect:"dark",content:e.row.webhook},{default:(0,s.k6)(()=>[(0,s.Lk)("div",n,(0,a.v_)(e.row.webhook.length>25?e.row.webhook.slice(0,25)+"...":e.row.webhook),1)]),_:2},1032,["content"])]),_:1}),(0,s.bF)(j,{label:"接收人",prop:"user_ids",align:"center"},{default:(0,s.k6)(e=>[(0,s.Lk)("div",u,[e.row.user_ids.length>2?((0,s.uX)(),(0,s.Wv)(R,{key:0,content:e.row.user_ids.join(", "),placement:"top"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",null,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(e.row.user_ids.slice(0,2),(e,l)=>((0,s.uX)(),(0,s.Wv)(V,{key:l,size:"small",class:"user-tag"},{default:(0,s.k6)(()=>[(0,s.eW)((0,a.v_)(e),1)]),_:2},1024))),128)),(0,s.bF)(V,{size:"small",type:"info"},{default:(0,s.k6)(()=>[(0,s.eW)("+"+(0,a.v_)(e.row.user_ids.length-2),1)]),_:2},1024)])]),_:2},1032,["content"])):((0,s.uX)(!0),(0,s.CE)(s.FK,{key:1},(0,s.pI)(e.row.user_ids,(e,l)=>((0,s.uX)(),(0,s.Wv)(V,{key:l,size:"small",class:"user-tag"},{default:(0,s.k6)(()=>[(0,s.eW)((0,a.v_)(e),1)]),_:2},1024))),128))])]),_:1}),(0,s.bF)(j,{label:"测试计划",prop:"testPlan.name",align:"center"}),(0,s.bF)(j,{label:"创建时间",align:"center"},{default:(0,s.k6)(l=>[(0,s.eW)((0,a.v_)(e.$tools.rTime(l.row.create_time)),1)]),_:1}),(0,s.bF)(j,{label:"操作",width:"200",align:"center"},{default:(0,s.k6)(e=>[(0,s.bF)(A,{onClick:l=>H.clickEdit(e.row),type:"primary",icon:U.Edit,circle:"",title:"编辑"},null,8,["onClick","icon"]),(0,s.bF)(A,{onClick:l=>H.delHook(e.row.id),type:"danger",icon:U.Delete,circle:"",title:"删除"},null,8,["onClick","icon"])]),_:1})]),_:1},8,["data"])),[[G,U.isLoading]])]),(0,s.Lk)("div",c,[(0,s.bF)(K,{background:"",layout:"total, prev, pager, next, jumper",onCurrentChange:H.currentPages,"default-page-size":100,total:U.pages.count,"current-page":U.pages.current,"next-text":"下一页","prev-text":"上一页"},null,8,["onCurrentChange","total","current-page"])])]),_:1})]),_:1}),(0,s.bF)(Y,{modelValue:U.addDlg,"onUpdate:modelValue":l[8]||(l[8]=e=>U.addDlg=e),title:"新增推送信息",width:"40%","custom-class":"push-dialog",required:!0,style:{"text-align":"left"},"before-close":H.clearValidation,top:"5vh"},{footer:(0,s.k6)(()=>[(0,s.Lk)("span",v,[(0,s.bF)(A,{onClick:H.clearValidation,size:"default"},{default:(0,s.k6)(()=>l[24]||(l[24]=[(0,s.eW)("取消")])),_:1,__:[24]},8,["onClick"]),(0,s.bF)(A,{type:"primary",onClick:H.AddInter,size:"default"},{default:(0,s.k6)(()=>l[25]||(l[25]=[(0,s.eW)("确定")])),_:1,__:[25]},8,["onClick"])])]),default:(0,s.k6)(()=>[(0,s.bF)(Q,{model:U.addForm,rules:U.rulesHook,ref:"HookRef","label-width":"120px",style:{"max-width":"600px"}},{default:(0,s.k6)(()=>[(0,s.bF)(z,{prop:"name",label:"推送名称"},{default:(0,s.k6)(()=>[(0,s.bF)(X,{modelValue:U.addForm.name,"onUpdate:modelValue":l[0]||(l[0]=e=>U.addForm.name=e),maxlength:"50",minlength:"1",placeholder:"请输入推送名称"},null,8,["modelValue"])]),_:1}),(0,s.bF)(z,{prop:"pushType",label:"推送类型"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",p,[(0,s.Lk)("div",{class:(0,a.C4)(["custom-radio-btn",{active:"wechat"===U.addForm.pushType}]),onClick:l[1]||(l[1]=e=>U.addForm.pushType="wechat")},[(0,s.Lk)("span",h,[(0,s.Lk)("span",{innerHTML:U.svgIcons.wechat,class:"iconfont"},null,8,k),l[19]||(l[19]=(0,s.eW)(" 企业微信 "))])],2),(0,s.Lk)("div",{class:(0,a.C4)(["custom-radio-btn",{active:"feishu"===U.addForm.pushType}]),onClick:l[2]||(l[2]=e=>U.addForm.pushType="feishu")},[(0,s.Lk)("span",m,[(0,s.Lk)("span",{innerHTML:U.svgIcons.feishu,class:"iconfont"},null,8,g),l[20]||(l[20]=(0,s.eW)(" 飞书 "))])],2),(0,s.Lk)("div",{class:(0,a.C4)(["custom-radio-btn",{active:"dingtalk"===U.addForm.pushType}]),onClick:l[3]||(l[3]=e=>U.addForm.pushType="dingtalk")},[(0,s.Lk)("span",b,[(0,s.Lk)("span",{innerHTML:U.svgIcons.dingtalk,class:"iconfont"},null,8,f),l[21]||(l[21]=(0,s.eW)(" 钉钉 "))])],2)])]),_:1}),(0,s.bF)(z,{prop:"webhook",label:"webhook地址"},{default:(0,s.k6)(()=>[(0,s.bF)(X,{modelValue:U.addForm.webhook,"onUpdate:modelValue":l[4]||(l[4]=e=>U.addForm.webhook=e),minlength:"3",placeholder:"请输入webhook地址","show-word-limit":""},{prefix:(0,s.k6)(()=>[(0,s.Lk)("span",{innerHTML:H.getIconSvg(U.addForm.pushType),class:"iconfont"},null,8,w)]),_:1},8,["modelValue"]),U.addForm.pushType?((0,s.uX)(),(0,s.CE)("div",F,[(0,s.bF)($,{type:"primary",href:H.getWebhookHelpUrl(U.addForm.pushType),target:"_blank"},{default:(0,s.k6)(()=>[l[22]||(l[22]=(0,s.Lk)("i",{class:"el-icon-question"},null,-1)),(0,s.eW)(" 如何获取"+(0,a.v_)(H.getPushTypeName(U.addForm.pushType))+"的webhook地址? ",1)]),_:1,__:[22]},8,["href"])])):(0,s.Q3)("",!0)]),_:1}),(0,s.bF)(z,{prop:"testPlan_id",label:"测试计划"},{default:(0,s.k6)(()=>[(0,s.bF)(S,{modelValue:U.addForm.testPlan_id,"onUpdate:modelValue":l[5]||(l[5]=e=>U.addForm.testPlan_id=e),placeholder:"请选择测试计划",style:{width:"100%"}},{default:(0,s.k6)(()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(e.testPlans,e=>((0,s.uX)(),(0,s.Wv)(N,{label:e.name,value:e.id,key:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,s.bF)(z,{label:"接收人",prop:"user_ids"},{default:(0,s.k6)(()=>[(0,s.bF)(S,{multiple:"",modelValue:U.addForm.user_ids,"onUpdate:modelValue":l[6]||(l[6]=e=>U.addForm.user_ids=e),placeholder:"请选择接收人",style:{width:"100%"},"collapse-tags":"","collapse-tags-tooltip":"",onChange:l[7]||(l[7]=e=>H.handleReceiversChange("add"))},{default:(0,s.k6)(()=>[((0,s.uX)(),(0,s.Wv)(N,{label:"@all",value:"@all",key:"@all",disabled:U.addForm.user_ids.length>0&&!U.addForm.user_ids.includes("@all")},null,8,["disabled"])),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(H.filteredUsers,e=>((0,s.uX)(),(0,s.Wv)(N,{label:e.weChat_name,value:e.weChat_name,key:e.id,disabled:U.addForm.user_ids.includes("@all")},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"]),l[23]||(l[23]=(0,s.Lk)("div",{class:"form-tip"},"选择 @all 将通知所有人，与其他接收人互斥",-1))]),_:1,__:[23]})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","before-close"]),(0,s.bF)(Y,{modelValue:U.editDlg,"onUpdate:modelValue":l[17]||(l[17]=e=>U.editDlg=e),title:"修改推送信息",width:"40%","custom-class":"push-dialog",required:!0,style:{"text-align":"left"},"before-close":H.clearValidation,top:"5vh"},{footer:(0,s.k6)(()=>[(0,s.Lk)("span",O,[(0,s.bF)(A,{onClick:H.clearValidation,size:"default"},{default:(0,s.k6)(()=>l[31]||(l[31]=[(0,s.eW)("取消")])),_:1,__:[31]},8,["onClick"]),(0,s.bF)(A,{type:"primary",onClick:H.EditInter,size:"default"},{default:(0,s.k6)(()=>l[32]||(l[32]=[(0,s.eW)("确定")])),_:1,__:[32]},8,["onClick"])])]),default:(0,s.k6)(()=>[(0,s.bF)(Q,{model:U.editForm,rules:U.rulesHook,ref:"HookRef","label-width":"120px",style:{"max-width":"600px"}},{default:(0,s.k6)(()=>[(0,s.bF)(z,{prop:"name",label:"推送名称"},{default:(0,s.k6)(()=>[(0,s.bF)(X,{modelValue:U.editForm.name,"onUpdate:modelValue":l[9]||(l[9]=e=>U.editForm.name=e),maxlength:"50",minlength:"1",placeholder:"请输入推送名称"},null,8,["modelValue"])]),_:1}),(0,s.bF)(z,{prop:"pushType",label:"推送类型"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",y,[(0,s.Lk)("div",{class:(0,a.C4)(["custom-radio-btn",{active:"wechat"===U.editForm.pushType}]),onClick:l[10]||(l[10]=e=>U.editForm.pushType="wechat")},[(0,s.Lk)("span",T,[(0,s.Lk)("span",{innerHTML:U.svgIcons.wechat,class:"iconfont"},null,8,E),l[26]||(l[26]=(0,s.eW)(" 企业微信 "))])],2),(0,s.Lk)("div",{class:(0,a.C4)(["custom-radio-btn",{active:"feishu"===U.editForm.pushType}]),onClick:l[11]||(l[11]=e=>U.editForm.pushType="feishu")},[(0,s.Lk)("span",L,[(0,s.Lk)("span",{innerHTML:U.svgIcons.feishu,class:"iconfont"},null,8,C),l[27]||(l[27]=(0,s.eW)(" 飞书 "))])],2),(0,s.Lk)("div",{class:(0,a.C4)(["custom-radio-btn",{active:"dingtalk"===U.editForm.pushType}]),onClick:l[12]||(l[12]=e=>U.editForm.pushType="dingtalk")},[(0,s.Lk)("span",P,[(0,s.Lk)("span",{innerHTML:U.svgIcons.dingtalk,class:"iconfont"},null,8,M),l[28]||(l[28]=(0,s.eW)(" 钉钉 "))])],2)])]),_:1}),(0,s.bF)(z,{prop:"webhook",label:"webhook地址"},{default:(0,s.k6)(()=>[(0,s.bF)(X,{modelValue:U.editForm.webhook,"onUpdate:modelValue":l[13]||(l[13]=e=>U.editForm.webhook=e),minlength:"3",placeholder:"请输入webhook地址","show-word-limit":""},{prefix:(0,s.k6)(()=>[(0,s.Lk)("span",{innerHTML:H.getIconSvg(U.editForm.pushType),class:"iconfont"},null,8,D)]),_:1},8,["modelValue"]),U.editForm.pushType?((0,s.uX)(),(0,s.CE)("div",W,[(0,s.bF)($,{type:"primary",href:H.getWebhookHelpUrl(U.editForm.pushType),target:"_blank"},{default:(0,s.k6)(()=>[l[29]||(l[29]=(0,s.Lk)("i",{class:"el-icon-question"},null,-1)),(0,s.eW)(" 如何获取"+(0,a.v_)(H.getPushTypeName(U.editForm.pushType))+"的webhook地址? ",1)]),_:1,__:[29]},8,["href"])])):(0,s.Q3)("",!0)]),_:1}),(0,s.bF)(z,{prop:"testPlan_id",label:"测试计划"},{default:(0,s.k6)(()=>[(0,s.bF)(S,{modelValue:U.editForm.testPlan_id,"onUpdate:modelValue":l[14]||(l[14]=e=>U.editForm.testPlan_id=e),placeholder:"请选择测试计划",style:{width:"100%"}},{default:(0,s.k6)(()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(e.testPlans,e=>((0,s.uX)(),(0,s.Wv)(N,{label:e.name,value:e.id,key:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,s.bF)(z,{label:"接收人",prop:"user_ids"},{default:(0,s.k6)(()=>[(0,s.bF)(S,{multiple:"",modelValue:U.editForm.user_ids,"onUpdate:modelValue":l[15]||(l[15]=e=>U.editForm.user_ids=e),placeholder:"请选择接收人",style:{width:"100%"},"collapse-tags":"","collapse-tags-tooltip":"",onChange:l[16]||(l[16]=e=>H.handleReceiversChange("edit"))},{default:(0,s.k6)(()=>[((0,s.uX)(),(0,s.Wv)(N,{label:"@all",value:"@all",key:"@all",disabled:U.editForm.user_ids.length>0&&!U.editForm.user_ids.includes("@all")},null,8,["disabled"])),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(H.filteredUsers,e=>((0,s.uX)(),(0,s.Wv)(N,{label:e.weChat_name,value:e.weChat_name,key:e.id,disabled:U.editForm.user_ids.includes("@all")},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"]),l[30]||(l[30]=(0,s.Lk)("div",{class:"form-tip"},"选择 @all 将通知所有人，与其他接收人互斥",-1))]),_:1,__:[30]})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","before-close"])])}var U=t(98261),H=t(71241);const A=(0,H.A)(U.A,[["render",I],["__scopeId","data-v-ca66abb8"]]);var j=A},98261:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){var core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(18111),core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es_iterator_filter_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(22489),core_js_modules_es_iterator_filter_js__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es_iterator_filter_js__WEBPACK_IMPORTED_MODULE_1__),core_js_modules_es_iterator_for_each_js__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(7588),core_js_modules_es_iterator_for_each_js__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_es_iterator_for_each_js__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_es_iterator_map_js__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(61701),core_js_modules_es_iterator_map_js__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_es_iterator_map_js__WEBPACK_IMPORTED_MODULE_3__),vuex__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(60782),_element_plus_icons_vue__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(57477),element_plus__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(51219),element_plus__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(12933);__webpack_exports__.A={data(){return{hookList:"",pages:"",addDlg:!1,isLoading:!1,editDlg:!1,Plus:_element_plus_icons_vue__WEBPACK_IMPORTED_MODULE_5__.Plus,Edit:_element_plus_icons_vue__WEBPACK_IMPORTED_MODULE_5__.Edit,Delete:_element_plus_icons_vue__WEBPACK_IMPORTED_MODULE_5__.Delete,pushTypeOptions:[{value:"wechat",label:"企业微信"},{value:"feishu",label:"飞书"},{value:"dingtalk",label:"钉钉"}],svgIcons:{wechat:'<svg t="1626188374737" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7171" width="16" height="16"><path d="M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5.3-3.1 11-5 17.2-5 3.2 0 6.4 0.5 9.5 1.4 33.1 9.5 68.8 14.8 105.7 14.8 6 0 11.9-0.1 17.8-0.4-7.1-21-10.9-43.1-10.9-66 0-135.8 132.2-245.8 295.3-245.8z m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1z m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1 43.2 19.3 43.2 43.1-19.4 43.1-43.2 43.1z" p-id="7172" fill="#07c160"></path><path d="M866.7 792.7c56.9-41.2 93.2-102 93.2-169.7 0-124-120.8-224.5-269.9-224.5-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3 2.6-0.8 5.2-1.2 7.9-1.2 5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.3 1.7 5.2 1.7 5 0 9.3-4.1 9.3-9.1 0-2.1-0.8-4.1-1.4-6.1-0.4-1.3-7.7-28.8-11.4-42.9-0.5-2-0.9-3.8-0.9-5.7 0.1-5.9 3.1-11.2 7.5-14.3z m-126.7-138.5c-19.5 0-35.5-16-35.5-35.6 0-19.5 16-35.6 35.5-35.6s35.5 16 35.5 35.6c0 19.6-16 35.6-35.5 35.6z m175.4 0c-19.5 0-35.5-16-35.5-35.6 0-19.5 16-35.6 35.5-35.6s35.5 16 35.5 35.6c0 19.6-16 35.6-35.5 35.6z" p-id="7173" fill="#07c160"></path></svg>',feishu:'<svg t="1629787228583" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2770" width="16" height="16"><path d="M512 55.296C261.376 55.296 58.368 258.304 58.368 508.928c0 250.56 203.008 453.632 453.632 453.632s453.632-203.008 453.632-453.632c0-250.56-203.008-453.632-453.632-453.632z m-32.256 592.384c-45.056 42.496-108.032 68.608-177.664 68.608-8.192 0-16.384-0.512-24.064-1.536-11.776-1.024-22.528-6.656-28.672-16.384-6.144-9.728-8.192-21.504-4.096-32.768 7.168-20.992 20.48-40.448 38.4-56.832-15.872-19.968-25.088-44.544-25.088-71.168 0-63.488 51.2-114.688 114.688-114.688 27.648 0 55.808 10.24 85.504 29.696 18.944-67.584 80.896-117.248 154.624-117.248 88.576 0 160.256 71.68 160.256 160.256 0.512 81.408-62.464 148.992-142.848 154.624-3.072 0.512-6.656 0.512-9.728 0.512-56.832 2.048-106.496-15.36-141.312-53.248z" fill="#3370FF"></path></svg>',dingtalk:'<svg t="1626188493827" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9783" width="16" height="16"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m227 385.3c-1 4.2-3.5 10.4-7 17.8h0.1l-0.4 0.7c-20.3 43.1-73.1 127.7-73.1 127.7s-0.1-0.2-0.3-0.5l-15.5 26.8h74.5L575.1 810l32.3-128h-58.6l20.4-84.7c-16.5 3.9-35.9 9.4-59 16.8 0 0-31.2 18.2-89.9-35 0 0-39.6-34.7-16.6-43.4 9.8-3.7 47.4-8.4 77-12.3 40-5.4 64.6-8.2 64.6-8.2S422 517 392.7 512.5c-29.3-4.6-66.4-53.1-74.3-95.8 0 0-12.2-23.4 26.3-12.3 38.5 11.1 197.9 43.2 197.9 43.2s-207.4-63.3-221.2-78.7c-13.8-15.4-40.6-84.2-37.1-126.5 0 0 1.5-10.5 12.4-7.7 0 0 153.3 69.7 258.1 107.9 104.8 37.9 195.9 57.3 184.2 106.7z" p-id="9784" fill="#1890ff"></path></svg>'},addForm:{name:"",webhook:"",user_ids:[],project_id:"",testPlan_id:"",pushType:"wechat"},editForm:{name:"",webhook:"",user_ids:[],project_id:"",testPlan_id:"",pushType:"wechat"},rulesHook:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],pushType:[{required:!0,message:"请选择推送类型",trigger:"change"}],webhook:[{required:!0,message:"请输入webhook地址",trigger:"blur"}],testPlan_id:[{required:!0,message:"请选择测试计划",trigger:"blur"}],user_ids:[{type:"array",required:!0,message:"请选择至少一个接收人",trigger:"change"}]}}},computed:{...(0,vuex__WEBPACK_IMPORTED_MODULE_4__.aH)(["pro","testPlans","Users"]),filteredUsers(){const e=this.Users.filter(e=>null!==e.weChat_name);return e}},methods:{...(0,vuex__WEBPACK_IMPORTED_MODULE_4__.i0)(["getAllUser"]),handleReceiversChange(e){const l="add"===e?this.addForm:this.editForm;l.user_ids.includes("@all")&&(l.user_ids=["@all"]),0===l.user_ids.length&&this.$refs.HookRef.validateField("user_ids")},getPushTypeName(e){const l={wechat:"企业微信",feishu:"飞书",dingtalk:"钉钉"};return l[e]||"未知"},getPushTypeTagType(e){const l={wechat:"success",feishu:"info",dingtalk:"warning"};return l[e]||"info"},getPushTypeIconClass(e){const l={wechat:"icon-wechat",feishu:"icon-feishu",dingtalk:"icon-dingtalk"};return l[e]||""},getWebhookHelpUrl(e){const l={wechat:"https://developer.work.weixin.qq.com/document/path/91770",feishu:"https://open.feishu.cn/document/ukTMukTMukTM/ucTM5YjL3ETO24yNxkjN",dingtalk:"https://open.dingtalk.com/document/robots/custom-robot-access"};return l[e]||"#"},getIconSvg(e){return this.svgIcons[e]||""},clickAdd(){this.addDlg=!0,this.addForm={name:"",webhook:"",user_ids:[],project_id:this.pro.id,testPlan_id:"",pushType:"wechat"}},clickEdit(e){this.editDlg=!0,this.editForm={...e},this.editForm.pushType||(this.editForm.pushType="wechat"),this.editForm.project_id=this.pro.id},delHook(e){element_plus__WEBPACK_IMPORTED_MODULE_7__.s.confirm("此操作将永久删除该推送信息, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const l=await this.$api.deleteHook(e);204===l.status&&((0,element_plus__WEBPACK_IMPORTED_MODULE_6__.nk)({type:"success",message:"删除成功!"}),this.getAllHook(this.pro))}).catch(()=>{(0,element_plus__WEBPACK_IMPORTED_MODULE_6__.nk)({type:"info",message:"已取消删除"})})},clearValidation(){this.addDlg=!1,this.editDlg=!1,this.$refs.HookRef.clearValidate()},async getAllHook(){this.isLoading=!0;const response=await this.$api.getHooks(this.pro.id);200===response.status&&(this.hookList=response.data.result,this.pages=response.data,this.hookList.forEach(record=>{const userIDs=eval(record.user_ids);record.user_ids=userIDs,record.pushType||(record.pushType="wechat")})),this.isLoading=!1},async AddInter(){this.$refs.HookRef.validate(async e=>{if(!e)return;const l={...this.addForm},t=l.user_ids.map(e=>`'${e}'`);l.user_ids=`[${t.join(",")}]`;const s=await this.$api.createHook(l);201===s.status&&((0,element_plus__WEBPACK_IMPORTED_MODULE_6__.nk)({type:"success",message:"添加成功",duration:1e3}),this.addForm={name:"",webhook:"",user_ids:[],project_id:"",testPlan_id:"",pushType:"wechat"},this.addDlg=!1,this.getAllHook())})},async EditInter(){this.$refs.HookRef.validate(async e=>{if(!e)return;const l={...this.editForm},t=l.user_ids.map(e=>`'${e}'`);l.user_ids=`[${t.join(",")}]`;const s=await this.$api.updateHook(l.id,l);200===s.status&&((0,element_plus__WEBPACK_IMPORTED_MODULE_6__.nk)({type:"success",message:"修改成功",duration:1e3}),this.editForm={name:"",webhook:"",user_ids:[],project_id:"",testPlan_id:"",pushType:"wechat"},this.editDlg=!1,this.getAllHook())})},currentPages(e){this.getAllHook(e),this.hookList.page=e}},created(){this.getAllHook(),this.getAllUser()}}}}]);
//# sourceMappingURL=323.abca7316.js.map