{"version": 3, "file": "js/838.b033fe3e.js", "mappings": "wMACOA,MAAM,sB,GAEJA,MAAM,oB,GAIEA,MAAM,gB,GACJA,MAAM,qB,GAGNA,MAAM,a,GACJA,MAAM,c,GAQVA,MAAM,gB,GACJA,MAAM,qB,GAGNA,MAAM,a,GACJA,MAAM,c,GAQVA,MAAM,gB,GACJA,MAAM,oB,GAGNA,MAAM,a,GACJA,MAAM,c,GAQVA,MAAM,gB,GACJA,MAAM,mB,GAGNA,MAAM,a,GACJA,MAAM,c,GAqDlBA,MAAM,kB,GACJA,MAAM,gB,GAKNA,MAAM,iB,GA4BAA,MAAM,oB,GAiBHA,MAAM,e,GAmDTA,MAAM,e,GACHA,MAAM,gB,SAEiBA,MAAM,kB,GAQhCA,MAAM,e,GACHA,MAAM,gB,GAaTA,MAAM,e,GACHA,MAAM,gB,GAeTA,MAAM,a,GAEFA,MAAM,a,GAINA,MAAM,a,GASTA,MAAM,iB,GAOPA,MAAM,kB,GAyBZA,MAAM,wB,s6BArSfC,EAAAA,EAAAA,IAoTM,MApTNC,EAoTM,EAlTJC,EAAAA,EAAAA,IAuDM,MAvDNC,EAuDM,EAtDJC,EAAAA,EAAAA,IAqDSC,EAAA,CArDAC,OAAQ,IAAE,C,iBACjB,IAYS,EAZTF,EAAAA,EAAAA,IAYSG,EAAA,CAZAC,KAAM,GAAC,C,iBACd,IAUU,EAVVJ,EAAAA,EAAAA,IAUUK,EAAA,CAVDC,OAAO,QAAQX,MAAM,a,kBAC5B,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARNS,EAQM,EAPJT,EAAAA,EAAAA,IAEM,MAFNU,EAEM,EADJR,EAAAA,EAAAA,IAAwCS,EAAA,M,iBAA/B,IAAqB,EAArBT,EAAAA,EAAAA,IAAqBU,K,SAEhCZ,EAAAA,EAAAA,IAGM,MAHNa,EAGM,EAFJb,EAAAA,EAAAA,IAAwD,MAAxDc,GAAwDC,EAAAA,EAAAA,IAA7BC,EAAAC,WAAWC,WAAS,G,aAC/ClB,EAAAA,EAAAA,IAAiC,OAA5BH,MAAM,cAAa,OAAG,U,eAKnCK,EAAAA,EAAAA,IAYSG,EAAA,CAZAC,KAAM,GAAC,C,iBACd,IAUU,EAVVJ,EAAAA,EAAAA,IAUUK,EAAA,CAVDC,OAAO,QAAQX,MAAM,a,kBAC5B,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARNmB,EAQM,EAPJnB,EAAAA,EAAAA,IAEM,MAFNoB,EAEM,EADJlB,EAAAA,EAAAA,IAA8BS,EAAA,M,iBAArB,IAAW,EAAXT,EAAAA,EAAAA,IAAWmB,K,SAEtBrB,EAAAA,EAAAA,IAGM,MAHNsB,EAGM,EAFJtB,EAAAA,EAAAA,IAAsD,MAAtDuB,GAAsDR,EAAAA,EAAAA,IAA3BC,EAAAC,WAAWO,SAAO,G,aAC7CxB,EAAAA,EAAAA,IAAiC,OAA5BH,MAAM,cAAa,OAAG,U,eAKnCK,EAAAA,EAAAA,IAYSG,EAAA,CAZAC,KAAM,GAAC,C,iBACd,IAUU,EAVVJ,EAAAA,EAAAA,IAUUK,EAAA,CAVDC,OAAO,QAAQX,MAAM,a,kBAC5B,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARNyB,EAQM,EAPJzB,EAAAA,EAAAA,IAEM,MAFN0B,EAEM,EADJxB,EAAAA,EAAAA,IAAwCS,EAAA,M,iBAA/B,IAAqB,EAArBT,EAAAA,EAAAA,IAAqByB,K,SAEhC3B,EAAAA,EAAAA,IAGM,MAHN4B,EAGM,EAFJ5B,EAAAA,EAAAA,IAAqD,MAArD6B,GAAqDd,EAAAA,EAAAA,IAA1BC,EAAAC,WAAWa,QAAM,G,aAC5C9B,EAAAA,EAAAA,IAAgC,OAA3BH,MAAM,cAAa,MAAE,U,eAKlCK,EAAAA,EAAAA,IAYSG,EAAA,CAZAC,KAAM,GAAC,C,iBACd,IAUU,EAVVJ,EAAAA,EAAAA,IAUUK,EAAA,CAVDC,OAAO,QAAQX,MAAM,a,kBAC5B,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARN+B,EAQM,EAPJ/B,EAAAA,EAAAA,IAEM,MAFNgC,EAEM,EADJ9B,EAAAA,EAAAA,IAAmCS,EAAA,M,iBAA1B,IAAgB,EAAhBT,EAAAA,EAAAA,IAAgB+B,K,SAE3BjC,EAAAA,EAAAA,IAGM,MAHNkC,EAGM,EAFJlC,EAAAA,EAAAA,IAAoD,MAApDmC,GAAoDpB,EAAAA,EAAAA,IAAzBC,EAAAC,WAAWmB,OAAK,G,aAC3CpC,EAAAA,EAAAA,IAAiC,OAA5BH,MAAM,cAAa,OAAG,U,yBASvCK,EAAAA,EAAAA,IAwCUK,EAAA,CAxCDV,MAAM,eAAa,C,iBAC1B,IAsCU,EAtCVK,EAAAA,EAAAA,IAsCUmC,GAAA,CAtCAC,QAAQ,EAAMzC,MAAM,e,kBAC5B,IAae,EAbfK,EAAAA,EAAAA,IAaeqC,EAAA,CAbDC,MAAM,QAAM,C,iBACxB,IAWE,EAXFtC,EAAAA,EAAAA,IAWEuC,EAAA,C,WAVSzB,EAAA0B,OAAOC,S,qCAAP3B,EAAA0B,OAAOC,SAAQC,GACxBC,KAAK,gBACL,oBAAkB,OAClB,kBAAgB,OAChB,eAAa,sBACZ,eAAc7B,EAAA8B,mBACdC,UAAW/B,EAAA+B,UACZ,kBAAgB,IACfC,WAAW,EACZnD,MAAM,e,2DAGVK,EAAAA,EAAAA,IAMeqC,EAAA,CANDC,MAAM,QAAM,C,iBACxB,IAIW,EAJXtC,EAAAA,EAAAA,IAIW+C,GAAA,C,WAJQjC,EAAA0B,OAAOQ,S,qCAAPlC,EAAA0B,OAAOQ,SAAQN,GAAEO,YAAY,UAAUH,UAAA,I,CAC7CI,QAAMC,EAAAA,EAAAA,IACf,IAA6B,EAA7BnD,EAAAA,EAAAA,IAA6BS,EAAA,M,iBAApB,IAAU,EAAVT,EAAAA,EAAAA,IAAUoD,M,wCAIzBpD,EAAAA,EAAAA,IAOeqC,EAAA,CAPDC,MAAM,QAAM,C,iBACxB,IAKY,EALZtC,EAAAA,EAAAA,IAKYqD,GAAA,CALDC,MAAA,gB,WAA8BxC,EAAA0B,OAAOe,O,qCAAPzC,EAAA0B,OAAOe,OAAMb,GAAEO,YAAY,QAAQH,UAAA,I,kBAC1E,IAAiC,EAAjC9C,EAAAA,EAAAA,IAAiCwD,GAAA,CAAtBlB,MAAM,KAAKmB,MAAM,MAC5BzD,EAAAA,EAAAA,IAAmCwD,GAAA,CAAxBlB,MAAM,MAAMmB,MAAM,OAC7BzD,EAAAA,EAAAA,IAAmCwD,GAAA,CAAxBlB,MAAM,MAAMmB,MAAM,OAC7BzD,EAAAA,EAAAA,IAAqCwD,GAAA,CAA1BlB,MAAM,OAAOmB,MAAM,S,gCAGlCzD,EAAAA,EAAAA,IAOeqC,EAAA,M,iBANb,IAEY,EAFZrC,EAAAA,EAAAA,IAEY0D,GAAA,CAFDf,KAAK,UAAWgB,QAAOC,EAAAC,YAAaC,MAAA,I,kBAC7C,IAA6B,EAA7B9D,EAAAA,EAAAA,IAA6BS,EAAA,M,iBAApB,IAAU,EAAVT,EAAAA,EAAAA,IAAUoD,M,2BAAU,U,4BAE/BpD,EAAAA,EAAAA,IAEY0D,GAAA,CAFAC,QAAOC,EAAAG,YAAaD,MAAA,I,kBAC9B,IAA8B,EAA9B9D,EAAAA,EAAAA,IAA8BS,EAAA,M,iBAArB,IAAW,EAAXT,EAAAA,EAAAA,IAAWgE,M,2BAAU,U,oDAOtClE,EAAAA,EAAAA,IAcM,MAdNmE,EAcM,EAbJnE,EAAAA,EAAAA,IAIM,MAJNoE,EAIM,EAHJlE,EAAAA,EAAAA,IAEY0D,GAAA,CAFDf,KAAK,UAAWgB,QAAOC,EAAAO,SAAUL,MAAA,I,kBAC1C,IAAmC,EAAnC9D,EAAAA,EAAAA,IAAmCS,EAAA,M,iBAA1B,IAAgB,EAAhBT,EAAAA,EAAAA,IAAgB+B,K,2BAAU,Y,8BAGvCjC,EAAAA,EAAAA,IAOM,MAPNsE,EAOM,EANJpE,EAAAA,EAAAA,IAEY0D,GAAA,CAFDW,OAAA,GAAQV,QAAOC,EAAAU,a,kBACxB,IAAmC,EAAnCtE,EAAAA,EAAAA,IAAmCS,EAAA,M,iBAA1B,IAAgB,EAAhBT,EAAAA,EAAAA,IAAgBuE,M,6BAE3BvE,EAAAA,EAAAA,IAEY0D,GAAA,CAFDW,OAAA,IAAM,C,iBACf,IAA8B,EAA9BrE,EAAAA,EAAAA,IAA8BS,EAAA,M,iBAArB,IAAW,EAAXT,EAAAA,EAAAA,IAAWwE,M,mBAM1BxE,EAAAA,EAAAA,IA2LUK,EAAA,CA3LDV,MAAM,aAAaW,OAAO,S,kBACjC,IAyKW,E,qBAzKXmE,EAAAA,EAAAA,IAyKWC,GAAA,CAxKTC,IAAI,QACJ,2BACCC,KAAM9D,EAAA+D,WACPvB,MAAA,eACAwB,KAAK,UACJC,QAAQ,EACT,aAAW,OACVC,kBAAkBpB,EAAAqB,sBAClB,iBAAgBrB,EAAAsB,kBACjB,eAAa,OAEbC,OAAO,O,kBAEP,IASkB,EATlBnF,EAAAA,EAAAA,IASkBoF,GAAA,CATD9C,MAAM,OAAO+C,KAAK,aAAa,YAAU,MAAM,4B,CACnDC,SAAOnC,EAAAA,EAAAA,IAMVoC,GANiB,EACvBzF,EAAAA,EAAAA,IAKM,MALN0F,EAKM,CAJgC,WAArBD,EAAME,IAAIlC,S,WAAzBkB,EAAAA,EAAAA,IAAyGhE,EAAA,C,MAA3Dd,MAAM,uB,kBAAsB,IAAqB,EAArBK,EAAAA,EAAAA,IAAqBU,K,OACtD,YAArB6E,EAAME,IAAIlC,S,WAA9BkB,EAAAA,EAAAA,IAA8GhE,EAAA,C,MAA1Dd,MAAM,sB,kBAAqB,IAAqB,EAArBK,EAAAA,EAAAA,IAAqByB,K,OAC3D,WAArB8D,EAAME,IAAIlC,S,WAA9BkB,EAAAA,EAAAA,IAAoGhE,EAAA,C,MAAjDd,MAAM,uB,kBAAsB,IAAW,EAAXK,EAAAA,EAAAA,IAAWmB,K,wBAC1FrB,EAAAA,EAAAA,IAAuC,aAAAe,EAAAA,EAAAA,IAA9B0E,EAAME,IAAIC,YAAU,O,OAInC1F,EAAAA,EAAAA,IAkBkBoF,GAAA,CAlBD9C,MAAM,KAAK+C,KAAK,eAAeM,MAAM,MAAMC,MAAM,U,CACrDN,SAAOnC,EAAAA,EAAAA,IAGPoC,GAHc,CACkB,MAA3BA,EAAME,IAAII,e,WAAxBpB,EAAAA,EAAAA,IAESqB,GAAA,C,MAFqCnD,KAAK,UAAUoD,OAAO,QAAQjB,KAAK,S,kBAC/E,IAAwC,EAAxC9E,EAAAA,EAAAA,IAAwCS,EAAA,M,iBAA/B,IAAqB,EAArBT,EAAAA,EAAAA,IAAqBU,K,6BAAU,Y,eAEI,OAA3B6E,EAAME,IAAII,e,WAA7BpB,EAAAA,EAAAA,IAESqB,GAAA,C,MAF2CnD,KAAK,SAASoD,OAAO,QAAQjB,KAAK,S,kBACpF,IAAwC,EAAxC9E,EAAAA,EAAAA,IAAwCS,EAAA,M,iBAA/B,IAAqB,EAArBT,EAAAA,EAAAA,IAAqByB,K,6BAAU,a,eAEI,MAA3B8D,EAAME,IAAII,e,WAA7BpB,EAAAA,EAAAA,IAKSqB,GAAA,C,MAL0CnD,KAAK,UAAUoD,OAAO,QAAQjB,KAAK,S,kBACpF,IAGO,EAHPhF,EAAAA,EAAAA,IAGO,OAHPkG,EAGO,EAFLhG,EAAAA,EAAAA,IAAiDS,EAAA,CAAxCd,MAAM,cAAY,C,iBAAC,IAAW,EAAXK,EAAAA,EAAAA,IAAWmB,K,6BAAU,c,oBAIrDsD,EAAAA,EAAAA,IAESqB,GAAA,C,MAFMnD,KAAK,OAAOoD,OAAO,QAAQjB,KAAK,S,kBAC7C,IAA2C,E,iBAAxClB,EAAAqC,cAAcV,EAAME,IAAII,eAAY,K,qBAI7C7F,EAAAA,EAAAA,IAIkBoF,GAAA,CAJD9C,MAAM,OAAO,YAAU,MAAM,4B,CACjCgD,SAAOnC,EAAAA,EAAAA,IAC2CoC,GADpC,E,iBACpBA,EAAME,IAAIS,MAAMlD,UAAYuC,EAAME,IAAIzC,UAAY,KAAJ,K,OAGrDhD,EAAAA,EAAAA,IAIkBoF,GAAA,CAJD9C,MAAM,OAAOqD,MAAM,MAAMC,MAAM,U,CACnCN,SAAOnC,EAAAA,EAAAA,IACqDoC,GAD9C,E,iBACpB3B,EAAAuC,gBAAgBZ,EAAME,IAAIS,MAAME,UAAYb,EAAME,IAAIW,WAAQ,K,OAGrEpG,EAAAA,EAAAA,IAIkBoF,GAAA,CAJD9C,MAAM,OAAOqD,MAAM,MAAMC,MAAM,U,CACnCN,SAAOnC,EAAAA,EAAAA,IACmBoC,GADZ,E,iBACpBA,EAAME,IAAIY,cAAgB,KAAJ,K,OAG7BrG,EAAAA,EAAAA,IAIkBoF,GAAA,CAJD9C,MAAM,OAAOqD,MAAM,MAAMC,MAAM,U,CACnCN,SAAOnC,EAAAA,EAAAA,IACOoC,GADA,E,iBACpBA,EAAME,IAAIa,SAAO,K,OAGxBtG,EAAAA,EAAAA,IAIkBoF,GAAA,CAJD9C,MAAM,OAAOqD,MAAM,MAAMC,MAAM,U,CACnCN,SAAOnC,EAAAA,EAAAA,IAC2BoC,GADpB,E,iBACpB3B,EAAA2C,aAAahB,EAAME,IAAIe,gBAAa,K,OAG3CxG,EAAAA,EAAAA,IAMkBoF,GAAA,CAND9C,MAAM,MAAMqD,MAAM,MAAMC,MAAM,U,CAClCN,SAAOnC,EAAAA,EAAAA,IAGToC,GAHgB,EACvBzF,EAAAA,EAAAA,IAEO,QAFAH,OAAK8G,EAAAA,EAAAA,IAAE7C,EAAA8C,oBAAoB9C,EAAA+C,eAAepB,EAAME,S,QAClD7B,EAAA+C,eAAepB,EAAME,MAAO,KACjC,K,OAGJzF,EAAAA,EAAAA,IAIkBoF,GAAA,CAJD9C,MAAM,SAASqD,MAAM,MAAMC,MAAM,U,CACrCN,SAAOnC,EAAAA,EAAAA,IACmCoC,GAD5B,E,iBACpB3B,EAAAgD,mBAAmBrB,EAAME,IAAIoB,kBAAe,K,OAKnD7G,EAAAA,EAAAA,IAUkBoF,GAAA,CAVD9C,MAAM,QAAQqD,MAAM,MAAMC,MAAM,U,CACpCN,SAAOnC,EAAAA,EAAAA,IAOVoC,GAPiB,EACvBzF,EAAAA,EAAAA,IAMM,MANNgH,EAMM,EALJhH,EAAAA,EAAAA,IAA+D,OAA/DiH,GAA+DlG,EAAAA,EAAAA,IAAjC0E,EAAME,IAAIuB,QAAU,KAAJ,GAEnCzB,EAAME,IAAIuB,S,WAArBpH,EAAAA,EAAAA,IAEM,MAFNqH,EAEM,EADJnH,EAAAA,EAAAA,IAAgG,OAA3FH,MAAM,WAAY2D,OAAK4D,EAAAA,EAAAA,IAAA,CAAAvB,MAAA,GAAcwB,KAAKC,IAAIC,OAAO9B,EAAME,IAAIuB,QAAM,a,sCAKlFhH,EAAAA,EAAAA,IAakBoF,GAAA,CAbD9C,MAAM,WAAWqD,MAAM,MAAMC,MAAM,U,CACvCN,SAAOnC,EAAAA,EAAAA,IAUVoC,GAViB,EACvBzF,EAAAA,EAAAA,IASM,MATNwH,EASM,EARJxH,EAAAA,EAAAA,IAA0E,OAA1EyH,GAA0E1G,EAAAA,EAAAA,IAA5C+C,EAAA4D,iBAAiBjC,EAAME,IAAIgC,SAAM,GAEvDlC,EAAME,IAAIgC,S,WADlBhD,EAAAA,EAAAA,IAMeiD,GAAA,C,MAJZC,WAAYN,OAAO9B,EAAME,IAAIgC,QAC7BG,MAAOhE,EAAAiE,YAAYtC,EAAME,IAAIgC,QAC7B,eAAc,EACd,aAAW,G,2DAKpBzH,EAAAA,EAAAA,IAakBoF,GAAA,CAbD9C,MAAM,UAAUqD,MAAM,MAAMC,MAAM,U,CACtCN,SAAOnC,EAAAA,EAAAA,IAUVoC,GAViB,EACvBzF,EAAAA,EAAAA,IASM,MATNgI,EASM,EARJhI,EAAAA,EAAAA,IAA6E,OAA7EiI,GAA6ElH,EAAAA,EAAAA,IAA/C+C,EAAA4D,iBAAiBjC,EAAME,IAAIuC,YAAS,GAE1DzC,EAAME,IAAIuC,Y,WADlBvD,EAAAA,EAAAA,IAMeiD,GAAA,C,MAJZC,WAAYN,OAAO9B,EAAME,IAAIuC,WAC7BJ,MAAOhE,EAAAqE,eAAe1C,EAAME,IAAIuC,WAChC,eAAc,EACd,aAAW,G,2DAOpBhI,EAAAA,EAAAA,IAYkBoF,GAAA,CAZD9C,MAAM,OAAO,YAAU,MAAMsD,MAAM,U,CACvCN,SAAOnC,EAAAA,EAAAA,IASVoC,GATiB,EACvBzF,EAAAA,EAAAA,IAQM,MARNoI,EAQM,EAPJlI,EAAAA,EAAAA,IAEamI,GAAA,CAFAC,QAASC,EAAAC,OAAOC,MAAMhD,EAAME,IAAI+C,WAAajD,EAAME,IAAIgD,YAAaC,UAAU,O,kBACzF,IAA0H,EAA1H5I,EAAAA,EAAAA,IAA0H,MAA1H6I,EAA0H,EAAnG3I,EAAAA,EAAAA,IAA+BS,EAAA,M,iBAAtB,IAAY,EAAZT,EAAAA,EAAAA,IAAY4I,M,eAAU,KAAC/H,EAAAA,EAAAA,IAAG+C,EAAAiF,WAAWtD,EAAME,IAAI+C,WAAajD,EAAME,IAAIgD,aAAU,O,sCAElH3I,EAAAA,EAAAA,IAAmC,OAA9BH,MAAM,kBAAiB,KAAC,KAC7BK,EAAAA,EAAAA,IAEamI,GAAA,CAFAC,QAASC,EAAAC,OAAOC,MAAMhD,EAAME,IAAIqD,SAAWvD,EAAME,IAAIsD,UAAWL,UAAU,O,kBACrF,IAAsH,EAAtH5I,EAAAA,EAAAA,IAAsH,MAAtHkJ,EAAsH,EAA/FhJ,EAAAA,EAAAA,IAA+BS,EAAA,M,iBAAtB,IAAY,EAAZT,EAAAA,EAAAA,IAAY4I,M,eAAU,KAAC/H,EAAAA,EAAAA,IAAG+C,EAAAiF,WAAWtD,EAAME,IAAIqD,SAAWvD,EAAME,IAAIsD,WAAQ,O,kCAMpH/I,EAAAA,EAAAA,IAKkBoF,GAAA,CALD9C,MAAM,MAAM+C,KAAK,WAAWM,MAAM,MAAMC,MAAM,U,CAClDN,SAAOnC,EAAAA,EAAAA,IACyIoC,GADlI,EACvBvF,EAAAA,EAAAA,IAAyJiJ,GAAA,CAA7InE,KAAM,GAAKoE,IAAKtF,EAAAuF,aAAa5D,EAAME,IAAI2D,UAAY7D,EAAME,IAAI4D,U,kBAAU,IAA0D,E,iBAAvDzF,EAAA0F,YAAY/D,EAAME,IAAI2D,UAAY7D,EAAME,IAAI4D,UAAO,K,oBACzIvJ,EAAAA,EAAAA,IAAwF,OAAxFyJ,GAAwF1I,EAAAA,EAAAA,IAAzD0E,EAAME,IAAI2D,UAAY7D,EAAME,IAAI4D,SAAW,MAAJ,K,OAK1ErJ,EAAAA,EAAAA,IAuBkBoF,GAAA,CAvBD9C,MAAM,KAAKqD,MAAM,MAAM6D,MAAM,S,CACjClE,SAAOnC,EAAAA,EAAAA,IAoBVoC,GApBiB,EACvBzF,EAAAA,EAAAA,IAmBM,MAnBN2J,EAmBM,EAlBJzJ,EAAAA,EAAAA,IAEY0D,GAAA,CAFDf,KAAK,UAAUmC,KAAK,QAAQhB,MAAA,GAAOH,QAAKjB,GAAEkB,EAAA8F,UAAUnE,EAAME,M,kBACnE,IAA2B,EAA3BzF,EAAAA,EAAAA,IAA2BS,EAAA,M,iBAAlB,IAAQ,EAART,EAAAA,EAAAA,IAAQ2J,M,6BAAU,U,gCAE7B3J,EAAAA,EAAAA,IAEY0D,GAAA,CAFDf,KAAK,UAAUmC,KAAK,QAAQhB,MAAA,GAAOH,QAAKjB,GAAEkB,EAAAgG,aAAarE,EAAME,M,kBACtE,IAA+B,EAA/BzF,EAAAA,EAAAA,IAA+BS,EAAA,M,iBAAtB,IAAY,EAAZT,EAAAA,EAAAA,IAAY6J,M,6BAAU,U,gCAEjC7J,EAAAA,EAAAA,IAWc8J,GAAA,CAXDC,QAAQ,SAAO,CAIfC,UAAQ7G,EAAAA,EAAAA,IACjB,IAImB,EAJnBnD,EAAAA,EAAAA,IAImBiK,GAAA,M,iBAHjB,IAEmB,EAFnBjK,EAAAA,EAAAA,IAEmBkK,GAAA,CAFAvG,QAAKjB,GAAEkB,EAAAuG,cAAc5E,EAAME,IAAI2E,IAAK9G,MAAA,kC,kBACrD,IAA6B,EAA7BtD,EAAAA,EAAAA,IAA6BS,EAAA,M,iBAApB,IAAU,EAAVT,EAAAA,EAAAA,IAAUqK,M,6BAAU,U,+DANnC,IAEY,EAFZrK,EAAAA,EAAAA,IAEY0D,GAAA,CAFDJ,MAAA,uBAA0BX,KAAK,OAAOmC,KAAK,QAAQhB,MAAA,I,kBAAM,IAChE,C,uBADgE,SAChE9D,EAAAA,EAAAA,IAAuDS,EAAA,CAA9Cd,MAAM,kBAAgB,C,iBAAC,IAAa,EAAbK,EAAAA,EAAAA,IAAasK,M,2GAjJ9CxJ,EAAAyJ,iBAiKbzK,EAAAA,EAAAA,IAaM,MAbN0K,EAaM,EAZJxK,EAAAA,EAAAA,IAWgByK,GAAA,CAVdC,WAAA,GACAC,OAAO,0CACNC,gBAAgBhH,EAAAiH,aAChBC,aAAalH,EAAAmH,iBACb,aAAY,CAAC,GAAI,GAAI,IAAK,KAC1B,oBAAmB,GACnB7I,MAAOpB,EAAAkK,MAAMC,MACb,eAAcnK,EAAAkK,MAAME,QACpB,uBAAqB,G,uKC3ShC,G,SAAA,CACEtG,IAAAA,GACE,MAAO,CAELuG,QAAS,CAAC,EAGVC,SAAU,GAGVC,eAAe,EAGfC,cAAe,KAEnB,EAEAC,SAAU,CAIRC,YAAAA,GACE,OAAOC,KAAKC,QAAQC,OAAOC,OAASH,KAAKH,aAC3C,EAKAO,cAAAA,GACE,OAAKJ,KAAKD,cAAiBC,KAAKL,SAASU,OAClCL,KAAKL,SAASW,KAAKC,GAAOA,EAAI5B,KAAOqB,KAAKD,cADO,IAE1D,EAKAS,cAAAA,GACE,OAAOR,KAAKI,gBAAgBK,MAAQ,OACtC,EAKAC,cAAAA,GACE,QAASV,KAAKD,YAChB,EAKAY,UAAAA,GACE,OAAOX,KAAKL,SAASiB,IAAIL,IAAO,CAC9B1J,MAAO0J,EAAIE,KACXzI,MAAOuI,EAAI5B,GACXkC,SAAyB,aAAfN,EAAIzI,SAElB,GAGFgJ,QAAS,CAKP,mBAAMC,CAAcC,EAAY,MAC9B,IACE,MAAMC,EAAMD,GAAahB,KAAKkB,iBAC9B,IAAKD,EAEH,YADAE,QAAQC,KAAK,oBAIf,MAAMC,QAAiBrB,KAAKsB,KAAKC,YAAYN,GACrB,MAApBI,EAASvJ,SACXkI,KAAKL,SAAW0B,EAASlI,MAAQ,IAG5B6G,KAAKD,cAAgBC,KAAKL,SAASU,OAAS,GAC/CL,KAAKwB,UAAUxB,KAAKL,SAAS,GAAGhB,IAGtC,CAAE,MAAO8C,GACPN,QAAQM,MAAM,YAAaA,GAC3BzB,KAAK0B,SAAS,CACZxK,KAAM,QACNyK,QAAS,WACTC,SAAU,KAEd,CACF,EAOA,kBAAMC,CAAa1B,EAAQ,KAAMa,EAAY,MAC3C,IACE,MAAMc,EAAM3B,GAASH,KAAKD,aACpBkB,EAAMD,GAAahB,KAAKkB,iBAE9B,IAAKY,IAAQb,EAEX,OADAE,QAAQC,KAAK,gBACN,KAGT,MAAMC,QAAiBrB,KAAKsB,KAAKS,WAAWD,EAAKb,GACjD,GAAwB,MAApBI,EAASvJ,OAEX,OADAkI,KAAKN,QAAU2B,EAASlI,MAAQ,CAAC,EAC1B6G,KAAKN,OAEhB,CAAE,MAAO+B,GACPN,QAAQM,MAAM,YAAaA,GAC3BzB,KAAK0B,SAAS,CACZxK,KAAM,QACNyK,QAAS,WACTC,SAAU,KAEd,CACA,OAAO,IACT,EAMAJ,SAAAA,CAAUrB,GACRH,KAAKH,cAAgBM,EAGjBH,KAAKC,QAAUD,KAAKC,OAAO+B,QAC7BhC,KAAKC,OAAO+B,OAAO,YAAa7B,GAIlCH,KAAKiC,MAAM,eAAgB9B,GAGvBA,GACFH,KAAK6B,aAAa1B,EAEtB,EAMA,oBAAM+B,CAAe/B,EAAQ,MAC3B,MAAM2B,EAAM3B,GAASH,KAAKD,aACrB+B,SAUC9B,KAAK6B,aAAaC,GAGxB9B,KAAKJ,eAAgB,GAZnBI,KAAK0B,SAAS,CACZxK,KAAM,UACNyK,QAAS,SACTC,SAAU,KAUhB,EAKAO,cAAAA,GACEnC,KAAKJ,eAAgB,CACvB,EAMAwC,aAAAA,CAAc1C,EAAU,MACtB,MAAM2C,EAAO3C,GAAWM,KAAKN,QACxB2C,GAUDrC,KAAKC,QAAUD,KAAKC,OAAO+B,QAC7BhC,KAAKC,OAAO+B,OAAO,gBAAiBK,GAItCrC,KAAKsC,QAAQC,KAAK,CAAE9B,KAAM,aAdxBT,KAAK0B,SAAS,CACZxK,KAAM,UACNyK,QAAS,UACTC,SAAU,KAYhB,EAOAY,iBAAAA,CAAkB9C,EAAU,MAC1B,MAAM2C,EAAO3C,GAAWM,KAAKN,QACvB+C,EAAS,GAEf,IAAKJ,EAEH,OADAI,EAAOF,KAAK,WACL,CAAEG,SAAS,EAAOD,UAItBJ,EAAK5B,MACRgC,EAAOF,KAAK,WAITF,EAAKM,iBAAgE,IAA7CC,OAAOC,KAAKR,EAAKM,iBAAiBtC,QAC7DoC,EAAOF,KAAK,WAId,MAAMO,EAAaT,EAAKM,iBAAmB,CAAC,EACtCI,EAAUD,EAAWE,UAAYF,EAAWC,QAKlD,OAJKA,GACHN,EAAOF,KAAK,YAGP,CACLG,QAA2B,IAAlBD,EAAOpC,OAChBoC,SAEJ,EAQAQ,kBAAAA,CAAmBC,EAAY,CAAC,EAAGhM,EAAO,UACxC,OAAKgM,GAAkC,kBAAdA,EAElBN,OAAOO,QAAQD,GAAWtC,IAAI,EAAEwC,EAAKpL,MAAW,CACrDoL,MACApL,QACAd,OACAmM,aAAcrD,KAAKsD,oBAAoBtL,MANe,EAQ1D,EAOAsL,mBAAAA,CAAoBtL,GAClB,OAAc,OAAVA,QAA4BuL,IAAVvL,EAA4B,GAC7B,kBAAVA,EAA2BwL,KAAKC,UAAUzL,GAChC,kBAAVA,GAAsBA,EAAMqI,OAAS,GACvCrI,EAAM0L,UAAU,EAAG,IAAM,MAE3BC,OAAO3L,EAChB,EAQA4L,cAAAA,CAAeR,EAAKS,EAAe,MACjC,OAAK7D,KAAKN,QAGNM,KAAKN,QAAQoE,4BAAqEP,IAA5CvD,KAAKN,QAAQoE,sBAAsBV,GACpEpD,KAAKN,QAAQoE,sBAAsBV,GAIxCpD,KAAKN,QAAQiD,sBAAyDY,IAAtCvD,KAAKN,QAAQiD,gBAAgBS,GACxDpD,KAAKN,QAAQiD,gBAAgBS,GAG/BS,EAZmBA,CAa5B,EAOAE,cAAAA,CAAe5D,EAAQ,MACrB,MAAM2B,EAAM3B,GAASH,KAAKD,aAC1B,IAAK+B,IAAQ9B,KAAKL,SAASU,OAAQ,OAAO,EAE1C,MAAME,EAAMP,KAAKL,SAASW,KAAK0D,GAAKA,EAAErF,KAAOmD,GAC7C,OAAOvB,GAAsB,aAAfA,EAAIzI,MACpB,EAOAmM,gBAAAA,CAAiBnM,GACf,MAAMoM,EAAY,CAChBC,OAAQ,UACRC,SAAU,UACVvD,SAAU,SACVwD,QAAS,QAEX,OAAOH,EAAUpM,IAAW,MAC9B,GAIF,aAAMwM,GAEAtE,KAAKkB,wBACDlB,KAAKe,eAEf,EAEAwD,MAAO,CAELrD,iBAAkB,CAChBsD,OAAAA,CAAQC,GACFA,GACFzE,KAAKe,cAAc0D,EAEvB,EACAC,WAAW,M,WDXjB,GACEC,OAAQ,CAACC,GACTC,WAAY,CACVC,QAAO,UAAEC,OAAM,SAAEC,aAAY,eAAEC,KAAI,OAAEC,SAAQ,WAAEC,OAAM,SACrDC,aAAY,eAAEC,QAAO,UAAEC,WAAU,aAAEC,KAAI,OAAEC,SAAQ,WACjDC,kBAAiB,oBAAEC,kBAAiB,oBAAEC,QAAO,UAAEC,aAAY,eAC3DC,QAAO,UAAEC,UAAS,YAAEC,YAAW,cAAEC,SAAQ,WAAEC,WAAUA,EAAAA,YAEvD9M,IAAAA,GACE,MAAO,CACLoG,MAAO,CACLE,QAAS,EACTD,MAAO,EACP0G,SAAU,IAEZpH,cAAc,EACd1F,WAAY,GACZ+M,gBAAiB,CACfC,gBAAiB,GACjBC,gBAAiBrG,KAAKxG,uBAExBzC,OAAO,CACLQ,SAAU,GACVO,OAAQ,GACRd,SAAU,CAACgJ,KAAKsG,iBAAiB,IAAIC,MAAK,IAAIA,MAAOC,UAAY,SACvDxG,KAAKsG,iBAAiB,IAAIC,MAAQ,KAE9CpP,mBAAoB,CAAC,sBAAuB,uBAC5CC,UAAW,CACT,CACEqP,KAAM,KACNzO,MAAQA,KACN,MAAM0O,EAAM,IAAIH,KACVI,EAAQ,IAAIJ,KAGlB,OAFAI,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,KAGnB,CACED,KAAM,MACNzO,MAAQA,KACN,MAAM0O,EAAM,IAAIH,KACVI,EAAQ,IAAIJ,KAIlB,OAHAI,EAAME,QAAQH,EAAII,UAAY,GAC9BH,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,KAGnB,CACED,KAAM,MACNzO,MAAQA,KACN,MAAM0O,EAAM,IAAIH,KACVI,EAAQ,IAAIJ,KAIlB,OAHAI,EAAME,QAAQH,EAAII,UAAY,GAC9BH,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,KAGpB,CACGD,KAAM,OACNzO,MAAQA,KACN,MAAM0O,EAAM,IAAIH,KACVI,EAAQ,IAAIJ,KAIlB,OAHAI,EAAMI,SAASL,EAAIM,WAAa,GAChCL,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,MAIrBpR,WAAY,CACVmB,MAAO,EACPlB,UAAW,EACXM,QAAS,EACTM,OAAQ,GAKd,EACA8Q,OAAAA,GACEjH,KAAKkH,eACP,EACApH,SAAU,KACLqH,EAAAA,EAAAA,IAAS,CACVC,IAAKlH,GAASA,EAAMkH,OAGxBtG,QAAS,CAEPrH,iBAAAA,EAAkB,IAAEO,IAClB,MAAmB,WAAfA,EAAIlC,OACC,cAEF,EACT,EAGAwH,gBAAAA,CAAiBjG,GACf2G,KAAKT,MAAM2G,SAAW7M,EACtB2G,KAAKqH,cACP,EAGAxO,WAAAA,GACEmH,KAAKlB,cAAe,EACpBwI,WAAW,KACTtH,KAAKlB,cAAe,GACpByI,EAAAA,EAAAA,IAAU,CACRrQ,KAAM,UACNyK,QAAS,QACTC,SAAU,QAEX,IACL,EAGAxE,UAAAA,CAAWoK,GACT,IAAKA,EAAS,MAAO,IACrB,MAAMC,EAAO,IAAIlB,KAAKiB,GACtB,MAAO,GAAGC,EAAKC,iBAAiB/D,OAAO8D,EAAKT,WAAa,GAAGW,SAAS,EAAG,QAAQhE,OAAO8D,EAAKX,WAAWa,SAAS,EAAG,QAAQhE,OAAO8D,EAAKG,YAAYD,SAAS,EAAG,QAAQhE,OAAO8D,EAAKI,cAAcF,SAAS,EAAG,MAC/M,EAGAvL,WAAAA,CAAYpE,GACV,MAAM8P,EAAMlM,OAAO5D,GACnB,OAAI8P,EAAM,GAAW,UACjBA,EAAM,GAAW,UACd,SACT,EAGAtL,cAAAA,CAAexE,GACb,MAAM8P,EAAMlM,OAAO5D,GACnB,OAAI8P,EAAM,GAAW,UACjBA,EAAM,GAAW,UACd,SACT,EAGApK,YAAAA,CAAaqK,GAEX,MAAO,EACT,EAGAlK,WAAAA,CAAYkK,GACV,OAAKA,EACEA,EAASC,OAAO,GAAGC,cADJ,EAExB,EAGAf,aAAAA,GACElH,KAAKkI,iBACLlI,KAAKqH,cACP,EAGA,kBAAMA,GACJrH,KAAKlB,cAAe,EACpB,IACE,MAAMqJ,EAAS,CACbC,WAAYpI,KAAKoH,IAAIzI,GACrB0J,KAAMrI,KAAKT,MAAME,QACjB6I,UAAWtI,KAAKT,MAAM2G,UAIpBlG,KAAKjJ,OAAOQ,WACd4Q,EAAO5Q,SAAWyI,KAAKjJ,OAAOQ,UAE5ByI,KAAKjJ,OAAOe,SACdqQ,EAAO/N,aAAe4F,KAAKjJ,OAAOe,OAAOyQ,QAAQ,MAAO,KAEtDvI,KAAKjJ,OAAOC,UAA4C,IAAhCgJ,KAAKjJ,OAAOC,SAASqJ,SAC/C8H,EAAOnL,WAAagD,KAAKjJ,OAAOC,SAAS,GACzCmR,EAAO7K,SAAW0C,KAAKjJ,OAAOC,SAAS,IAGzC,MAAMqK,QAAiBrB,KAAKsB,KAAKkH,eAAeL,GAEhD,GAAwB,MAApB9G,EAASvJ,OAAgB,CAC3B,IAAIsB,EAAaiI,EAASlI,KAAKsP,QAAUpH,EAASlI,KAAKA,MAAQ,GAG/DC,EAAaA,EAAWwH,IAAI8H,IAAG,IAC1BA,EAEHtO,aAAcsO,EAAKtO,cAAgBsO,EAAK5Q,OAExCP,SAAUmR,EAAKnR,UAAYmR,EAAKjO,MAAMlD,UAAYmR,EAAKjI,KACvD9F,SAAU+N,EAAK/N,UAAY+N,EAAKjO,MAAME,SACtCC,aAAc8N,EAAK9N,cAAgB8N,EAAKjO,MAAMG,aAE9C+N,OAAQD,EAAKC,QAAUD,EAAKjO,MAAMkE,IAAM+J,EAAKE,QAE7C/N,QAAS6N,EAAK7N,QAEd8C,SAAU+K,EAAK/K,UAAY+K,EAAK9K,SAAW8K,EAAKG,MAAMd,SAEtDhL,UAAW2L,EAAK3L,WAAa2L,EAAK1L,YAAc0L,EAAKI,WACrDzL,QAASqL,EAAKrL,SAAWqL,EAAKpL,UAAYoL,EAAKK,WAE/C/M,OAAQ0M,EAAK1M,OAASJ,OAAO8M,EAAK1M,QAAQgN,QAAQ,GAAK,KACvDzM,UAAWmM,EAAKnM,UAAYX,OAAO8M,EAAKnM,WAAWyM,QAAQ,GAAK,KAChE5N,gBAAiBsN,EAAKtN,gBAAkBQ,OAAO8M,EAAKtN,iBAAmB,KACvEG,OAAQmN,EAAKnN,OAASK,OAAO8M,EAAKnN,QAAQyN,QAAQ,GAAK,KACvDjO,cAAe2N,EAAK3N,cAAgBa,OAAO8M,EAAK3N,eAAiB,KACjEkO,gBAAiBP,EAAKO,gBAAkBrN,OAAO8M,EAAKO,iBAAmB,KACvEC,eAAgBR,EAAKQ,eAAiBtN,OAAO8M,EAAKQ,gBAAkB,QAGtElJ,KAAK5G,WAAaA,EAClB4G,KAAKT,MAAMC,MAAQ6B,EAASlI,KAAKqG,OAAS6B,EAASlI,KAAK1C,OAAS,EACjEuJ,KAAKT,MAAME,QAAU4B,EAASlI,KAAKsG,SAAW4B,EAASlI,KAAKkP,MAAQ,CACtE,CACF,CAAE,MAAO5G,GACPN,QAAQM,MAAM,YAAaA,IAC3B8F,EAAAA,EAAAA,IAAU,CACRrQ,KAAM,QACNyK,QAAS,cAAgBF,EAAMJ,UAAUlI,MAAMwI,SAAWF,EAAME,SAAW,QAC3EC,SAAU,KAEd,CAAE,QACA5B,KAAKlB,cAAe,CACtB,CACF,EAGA,oBAAMoJ,GACJ,IACE,MAAMC,EAAS,CACbC,WAAYpI,KAAKoH,IAAIzI,IAGjB0C,QAAiBrB,KAAKsB,KAAK6H,cAAchB,GAEvB,MAApB9G,EAASvJ,SACXkI,KAAK1K,WAAa+L,EAASlI,KAE/B,CAAE,MAAOsI,GACPN,QAAQM,MAAM,YAAaA,EAC7B,CACF,EAEAjI,qBAAAA,CAAsB4P,GACpBpJ,KAAKmG,gBAAgBC,gBAAkBgD,EAASxI,IAAI8H,GAAQA,EAAK/J,GACnE,EAEAS,YAAAA,CAAaiK,GACXrJ,KAAKT,MAAME,QAAU4J,EACrBrJ,KAAKqH,cACP,EAEAiC,uBAAAA,CAAwB9B,EAAS+B,GAC/B,MAAMC,EAASC,EAAQ,MACjBC,EAAIF,EAAOG,GAAGnC,EAAS+B,GAC7B,OAAOG,EAAEE,OAAO,sBAClB,EAEAtD,gBAAAA,CAAiBmB,EAAMoC,GAAW,GAChC,MAAMC,EAAOrC,EAAKC,cACZqC,EAAQpG,OAAO8D,EAAKT,WAAa,GAAGW,SAAS,EAAG,KAChDqC,EAAMrG,OAAO8D,EAAKX,WAAWa,SAAS,EAAG,KAC/C,IAAIsC,EAAOC,EAASC,EAYpB,OAVIN,GACFI,EAAQ,KACRC,EAAU,KACVC,EAAU,OAEVF,EAAQ,KACRC,EAAU,KACVC,EAAU,MAGL,GAAGL,KAAQC,KAASC,KAAOC,KAASC,KAAWC,GACxD,EAEA7R,WAAAA,GACE0H,KAAKjJ,OAAS,CACZQ,SAAU,GACVO,OAAQ,GACRd,SAAU,CAACgJ,KAAKsG,iBAAiB,IAAIC,MAAK,IAAIA,MAAOC,UAAY,SACnDxG,KAAKsG,iBAAiB,IAAIC,MAAQ,KAElDvG,KAAKT,MAAME,QAAU,EACrBO,KAAKqH,cACP,EAEAjP,WAAAA,GACE4H,KAAKT,MAAME,QAAU,EACrBO,KAAKqH,cACP,EAEApJ,SAAAA,CAAUjE,GACHA,GAAQA,EAAI2E,GAIjBqB,KAAKsC,QAAQC,KAAK,CAChB9B,KAAM,2BACN0H,OAAQ,CAAExJ,GAAI3E,EAAI2E,MALlBqB,KAAK0B,SAASD,MAAM,WAOxB,EAEA/I,QAAAA,GAEEsH,KAAKsC,QAAQC,KAAK,0BACpB,EASA6H,iBAAAA,CAAkBC,EAAQrS,GACxB,IAAKA,GAAmB,IAAVA,EAAa,MAAO,IAElC,OAAQqS,GACN,IAAK,kBACH,OAAOrS,EAAQ,IAAO,GAAGA,MAAY,IAAIA,EAAM,KAAMgR,QAAQ,MAC/D,IAAK,cACL,IAAK,SACL,IAAK,YACH,MAAO,GAAGpN,OAAO5D,GAAOgR,QAAQ,MAClC,IAAK,gBACH,OAAOpN,OAAO5D,GAAOsS,iBACvB,QACE,OAAO1O,OAAO5D,GAAOgR,QAAQ,GAEnC,EAGAuB,cAAAA,CAAeF,EAAQrS,GACrB,OAAQqS,GACN,IAAK,kBACH,OAAOrS,EAAQ,IAAO,UAAYA,EAAQ,IAAM,UAAY,UAC9D,IAAK,SACH,OAAOA,EAAQ,IAAM,UAAYA,EAAQ,GAAK,UAAY,UAC5D,IAAK,cACH,OAAOA,EAAQ,GAAK,UAAYA,EAAQ,GAAK,UAAY,UAC3D,QACE,MAAO,UAEb,EAIA,kBAAMmG,CAAanE,GACjB,IACE,IAAKA,IAAQA,EAAI2E,GAMf,YALA4I,EAAAA,EAAAA,IAAU,CACRrQ,KAAM,QACNyK,QAAS,iBACTC,SAAU,OAKd2F,EAAAA,EAAAA,IAAU,CACRrQ,KAAM,OACNyK,QAAS,eACTC,SAAU,MAGZ,MAAMP,QAAiBrB,KAAKsB,KAAKkJ,mBAAmBxQ,EAAI2E,IAExD,GAAwB,MAApB0C,EAASvJ,OAAgB,CAC3B,MAAM2S,EAAqBpJ,EAASqJ,QAAQ,uBAC5C,IAAIC,EAAW,cAGf,GAAIF,EAAoB,CACtB,MAAMG,EAAgBH,EAAmBI,MAAM,wBAC3CD,GAAiBA,EAAc,KACjCD,EAAWC,EAAc,GAE7B,MAEED,EAAW,GAAG3Q,EAAIC,YAAc,gBAGlC,MAAM6Q,EAAO,IAAIC,KAAK,CAAC1J,EAASlI,MAAO,CAAEjC,KAAM,sEACzC8T,EAAOC,SAASC,cAAc,KACpCF,EAAKG,KAAOC,IAAIC,gBAAgBP,GAChCE,EAAKM,SAAWX,EAChBK,EAAKO,SAELhE,EAAAA,EAAAA,IAAU,CACRrQ,KAAM,UACNyK,QAAS,OACTC,SAAU,KAEd,CACF,CAAE,MAAOH,IACP8F,EAAAA,EAAAA,IAAU,CACRrQ,KAAM,QACNyK,QAAS,UAAYF,EAAME,SAAW,QACtCC,SAAU,KAEd,CACF,EAEA,mBAAMlD,CAAcC,GAClB,IACE,MAAM0C,QAAiBrB,KAAKsB,KAAKkK,cAAc7M,GAEvB,MAApB0C,EAASvJ,UACXyP,EAAAA,EAAAA,IAAU,CACRrQ,KAAM,UACNyK,QAAS,SAEX3B,KAAKqH,eACLrH,KAAKkI,iBAET,CAAE,MAAOzG,GACO,WAAVA,IACF8F,EAAAA,EAAAA,IAAU,CACRrQ,KAAM,QACNyK,QAAS,UAAYF,EAAME,SAAW,SAG5C,CACF,EAGA5F,gBAAAA,CAAiB/D,GACf,IAAKA,GAAmB,IAAVA,EAAa,MAAO,IAClC,MAAM8P,EAAMlM,OAAO5D,GACnB,OAAO8P,EAAIkB,QAAQ,GAAK,GAC1B,EAGAlO,YAAAA,CAAagN,GACX,OAAKA,GAAe,IAARA,EACLA,EAAIwC,iBADmB,GAEhC,EAGArP,mBAAAA,CAAoBwQ,GAClB,MAAM3D,EAAMlM,OAAO6P,GACnB,OAAI3D,GAAO,GAAW,oBAClBA,GAAO,GAAW,sBACf,kBACT,EAGA5M,cAAAA,CAAelB,GACb,IAAKA,EAAIe,eAAuC,IAAtBf,EAAIe,cAAqB,OAAO,EAC1D,MAAM2Q,EAAe1R,EAAIe,eAAiBf,EAAIkP,gBAAkB,GAC1DuC,EAAQC,EAAe1R,EAAIe,cAAiB,IAClD,OAAO0Q,EAAKzC,QAAQ,EACtB,EAGA7N,kBAAAA,CAAmBwQ,GACjB,IAAKA,EAAM,MAAO,IAClB,MAAM7D,EAAMlM,OAAO+P,GACnB,OAAI7D,EAAM,IAAaA,EAAIkB,QAAQ,GAAK,MAChClB,EAAM,KAAMkB,QAAQ,GAAK,GACnC,EAGA4C,cAAAA,CAAehK,GACb,IAAKA,EAAU,MAAO,IACtB,MAAMuI,EAAUzO,KAAKmQ,MAAMjK,EAAW,KAChCsI,EAAUxO,KAAKmQ,MAAM1B,EAAU,IAC/BF,EAAQvO,KAAKmQ,MAAM3B,EAAU,IAEnC,OAAID,EAAQ,EACH,GAAGA,MAAUC,EAAU,OAAOC,EAAU,MACtCD,EAAU,EACZ,GAAGA,MAAYC,EAAU,MAEzB,GAAGA,IAEd,EAGA3P,aAAAA,CAAc1C,GACZ,MAAMoM,EAAY,CAChB,EAAK,MACL,EAAK,MACL,GAAM,QAER,OAAOA,EAAUpM,IAAW,MAC9B,EAGA4C,eAAAA,CAAgBxD,GACd,IAAKA,GAAiB,IAATA,EAAY,MAAO,IAChC,MAAM4U,EAAU,CACd,EAAK,OACL,EAAK,OACL,GAAM,OACN,GAAM,QAER,OAAOA,EAAQnI,OAAOzM,KAAU,GAClC,EAGA6U,iBAAAA,CAAkBC,GAChB,IAAKA,GAAuB,IAAZA,EAAe,MAAO,IACtC,MAAMC,EAAa,CACjB,EAAK,OACL,EAAK,OACL,GAAM,OACN,GAAM,QAER,OAAOA,EAAWtI,OAAOqI,KAAa,GACxC,EAKAE,gBAAAA,CAAiBpU,GACf,MAAMgU,EAAU,CACd,EAAK,UACL,EAAK,UACL,GAAM,UAER,OAAOA,EAAQhU,IAAW,MAC5B,EAGAqU,sBAAAA,GACE,IAAKnM,KAAKoM,qBAAqBC,gBAAgBC,MAAO,MAAO,GAE7D,MAAMA,EAAQtM,KAAKoM,qBAAqBC,eAAeC,MACjDC,EAAU,CAAC,kBAAmB,SAAU,cAAe,SAAU,YAAa,iBAC9EC,EAAe,CACnBpR,gBAAiB,aACjBG,OAAQ,QACRkR,YAAa,SACbzQ,OAAQ,WACRO,UAAW,UACXxB,cAAe,QAGjB,OAAOwR,EAAQ3L,IAAIyJ,IACjB,MAAMqC,EAASJ,EAAM1L,IAAInG,GAAQA,EAAK4P,IAAW,GAC3CsC,EAAY3M,KAAK4M,mBAAmBvC,EAAQqC,GAElD,MAAO,CACLrC,OAAQmC,EAAanC,GACrBqC,OAAQA,EAAO9L,IAAI5I,GAASgI,KAAKoK,kBAAkBC,EAAQrS,IAC3D2U,YACAE,UAAW7M,KAAKoK,kBAAkBC,EAAQqC,EAAOC,MAGvD,EAGAG,mBAAAA,CAAoBzC,EAAQrS,GAC1B,IAAKgI,KAAKoM,qBAAqBC,gBAAgBC,MAAO,OAAO,EAE7D,MAAMA,EAAQtM,KAAKoM,qBAAqBC,eAAeC,MACjDI,EAASJ,EAAM1L,IAAInG,GAAQA,EAAK4P,IAAW,GAC3C0C,EAAWrR,KAAKsR,OAAON,EAAOO,OAAOC,GAAKA,EAAI,IAEpD,OAAiB,IAAbH,EAAuB,EACpBrR,KAAKC,IAAK3D,EAAQ+U,EAAY,IAAK,IAC5C,EAGAI,WAAAA,GACE,IAAKnN,KAAKoM,qBAAqBC,gBAAgBC,MAAO,MAAO,CAAC,EAE9D,MAAMA,EAAQtM,KAAKoM,qBAAqBC,eAAeC,MACvD,OAAOtM,KAAKoN,wBAAwBd,EACtC,GAGFhI,OAAAA,GAEA,G,WEn4BF,MAAM+I,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceResult.vue", "webpack://frontend-web/./src/mixins/environmentMixin.js", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceResult.vue?fea1"], "sourcesContent": ["<template>\n  <div class=\"performance-result\">\n    <!-- 顶部卡片统计信息 -->\n    <div class=\"statistics-cards\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <el-card shadow=\"hover\" class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon success\">\n                <el-icon><CircleCheckFilled /></el-icon>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.completed }}</div>\n                <div class=\"stat-label\">已完成</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card shadow=\"hover\" class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon running\">\n                <el-icon><Loading /></el-icon>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.running }}</div>\n                <div class=\"stat-label\">执行中</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card shadow=\"hover\" class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon failed\">\n                <el-icon><CircleCloseFilled /></el-icon>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.failed }}</div>\n                <div class=\"stat-label\">失败</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card shadow=\"hover\" class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon total\">\n                <el-icon><DataAnalysis /></el-icon>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.total }}</div>\n                <div class=\"stat-label\">总报告</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 搜索区域 -->\n    <el-card class=\"search-card\">\n      <el-form :inline=\"true\" class=\"search-form\">\n        <el-form-item label=\"时间范围\">\n          <el-date-picker\n            v-model=\"search.dataTime\"\n            type=\"datetimerange\"\n            start-placeholder=\"开始时间\"\n            end-placeholder=\"结束时间\"\n            value-format=\"YYYY-MM-DD HH:mm:ss\"\n            :default-time=\"defaultTimeOptions\"\n            :shortcuts=\"shortcuts\"\n            range-separator=\"至\"\n            :clearable=\"false\"\n            class=\"date-picker\"\n          />\n        </el-form-item>\n        <el-form-item label=\"任务名称\">\n          <el-input v-model=\"search.taskName\" placeholder=\"请输入任务名称\" clearable>\n            <template #prefix>\n              <el-icon><Search /></el-icon>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"报告状态\">\n          <el-select style=\"width: 120px\" v-model=\"search.status\" placeholder=\"请选择状态\" clearable>\n            <el-option label=\"全部\" value=\"\" />\n            <el-option label=\"已完成\" value=\"0\" />\n            <el-option label=\"执行中\" value=\"1\" />\n            <el-option label=\"运行失败\" value=\"99\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"clickSearch\" plain>\n            <el-icon><Search /></el-icon>查询\n          </el-button>\n          <el-button @click=\"clearSearch\" plain>\n            <el-icon><Refresh /></el-icon>重置\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 操作工具栏 -->\n    <div class=\"action-toolbar\">\n      <div class=\"left-actions\">\n        <el-button type=\"primary\" @click=\"taskDiff\" plain>\n          <el-icon><DataAnalysis /></el-icon>任务对比\n        </el-button>\n      </div>\n      <div class=\"right-actions\">\n        <el-button circle @click=\"refreshData\">\n          <el-icon><RefreshRight /></el-icon>\n        </el-button>\n        <el-button circle>\n          <el-icon><Setting /></el-icon>\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 表格区域 -->\n    <el-card class=\"table-card\" shadow=\"never\">\n      <el-table\n        ref=\"table\"\n        highlight-current-row\n        :data=\"reportList\"\n        style=\"width: 100%\"\n        size=\"default\"\n        :border=\"false\"\n        empty-text=\"暂无数据\"\n        @selection-change=\"handleSelectionChange\"\n        :row-class-name=\"tableRowClassName\"\n        table-layout=\"auto\"\n        v-loading=\"tableLoading\"\n        height=\"500\"\n      >\n        <el-table-column label=\"报告名称\" prop=\"reportName\" min-width=\"200\" show-overflow-tooltip>\n          <template #default=\"scope\">\n            <div class=\"report-name-cell\">\n              <el-icon v-if=\"scope.row.status === '报告-已完成'\" class=\"status-icon success\"><CircleCheckFilled /></el-icon>\n              <el-icon v-else-if=\"scope.row.status === '报告-运行失败'\" class=\"status-icon failed\"><CircleCloseFilled /></el-icon>\n              <el-icon v-else-if=\"scope.row.status === '报告-执行中'\" class=\"status-icon running\"><Loading /></el-icon>\n              <span>{{ scope.row.reportName }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" prop=\"reportStatus\" width=\"120\" align=\"center\">\n          <template #default=\"scope\">\n            <el-tag v-if=\"scope.row.reportStatus === '0'\" type=\"success\" effect=\"light\" size=\"small\">\n              <el-icon><CircleCheckFilled /></el-icon> 已完成\n            </el-tag>\n            <el-tag v-else-if=\"scope.row.reportStatus === '99'\" type=\"danger\" effect=\"light\" size=\"small\">\n              <el-icon><CircleCloseFilled /></el-icon> 运行失败\n            </el-tag>\n            <el-tag v-else-if=\"scope.row.reportStatus === '1'\" type=\"primary\" effect=\"light\" size=\"small\">\n              <span class=\"running-tag\">\n                <el-icon class=\"is-loading\"><Loading /></el-icon>\n                执行中\n              </span>\n            </el-tag>\n            <el-tag v-else type=\"info\" effect=\"light\" size=\"small\">\n              {{ getStatusText(scope.row.reportStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"任务名称\" min-width=\"150\" show-overflow-tooltip>\n          <template #default=\"scope\">\n            {{ scope.row.task?.taskName || scope.row.taskName || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"任务模式\" width=\"100\" align=\"center\">\n          <template #default=\"scope\">\n            {{ getTaskTypeText(scope.row.task?.taskType || scope.row.taskType) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"压测模式\" width=\"120\" align=\"center\">\n          <template #default=\"scope\">\n            {{ scope.row.pressureMode || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"测试环境\" width=\"120\" align=\"center\">\n          <template #default=\"scope\">\n            {{ scope.row.envName }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"总请求数\" width=\"120\" align=\"center\">\n          <template #default=\"scope\">\n            {{ formatNumber(scope.row.totalRequests) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"成功率\" width=\"100\" align=\"center\">\n          <template #default=\"scope\">\n            <span :class=\"getSuccessRateClass(getSuccessRate(scope.row))\">\n              {{ getSuccessRate(scope.row) }}%\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"平均响应时间\" width=\"130\" align=\"center\">\n          <template #default=\"scope\">\n            {{ formatResponseTime(scope.row.avgResponseTime) }}\n          </template>\n        </el-table-column>\n\n        <!-- 性能指标区域 - 带小图表 -->\n        <el-table-column label=\"平均RPS\" width=\"120\" align=\"center\">\n          <template #default=\"scope\">\n            <div class=\"metric-cell\">\n              <span class=\"metric-value\">{{ scope.row.avgTps || '-' }}</span>\n              <!-- 添加TPS迷你条形图 -->\n              <div v-if=\"scope.row.avgTps\" class=\"mini-bar-chart\">\n                <div class=\"mini-bar\" :style=\"{ width: `${Math.min(Number(scope.row.avgTps)/2, 100)}%` }\"></div>\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"运行完成时CPU\" width=\"120\" align=\"center\">\n          <template #default=\"scope\">\n            <div class=\"metric-cell\">\n              <span class=\"metric-value\">{{ formatPercentage(scope.row.avgCpu) }}</span>\n              <el-progress\n                v-if=\"scope.row.avgCpu\"\n                :percentage=\"Number(scope.row.avgCpu)\"\n                :color=\"getCpuColor(scope.row.avgCpu)\"\n                :stroke-width=\"4\"\n                :show-text=\"false\"\n              ></el-progress>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"运行完成时内存\" width=\"120\" align=\"center\">\n          <template #default=\"scope\">\n            <div class=\"metric-cell\">\n              <span class=\"metric-value\">{{ formatPercentage(scope.row.avgMemory) }}</span>\n              <el-progress\n                v-if=\"scope.row.avgMemory\"\n                :percentage=\"Number(scope.row.avgMemory)\"\n                :color=\"getMemoryColor(scope.row.avgMemory)\"\n                :stroke-width=\"4\"\n                :show-text=\"false\"\n              ></el-progress>\n            </div>\n          </template>\n        </el-table-column>\n\n        <!-- 时间信息 -->\n        <el-table-column label=\"执行时间\" min-width=\"300\" align=\"center\">\n          <template #default=\"scope\">\n            <div class=\"time-info\">\n              <el-tooltip :content=\"$tools.rTime(scope.row.startTime || scope.row.start_time)\" placement=\"top\">\n                <div class=\"time-item\"><el-icon><Calendar /></el-icon> {{ formatDate(scope.row.startTime || scope.row.start_time) }}</div>\n              </el-tooltip>\n              <div class=\"time-separator\">至</div>\n              <el-tooltip :content=\"$tools.rTime(scope.row.endTime || scope.row.end_time)\" placement=\"top\">\n                <div class=\"time-item\"><el-icon><Calendar /></el-icon> {{ formatDate(scope.row.endTime || scope.row.end_time) }}</div>\n              </el-tooltip>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"执行人\" prop=\"executor\" width=\"100\" align=\"center\">\n          <template #default=\"scope\">\n            <el-avatar :size=\"24\" :src=\"getAvatarUrl(scope.row.executor || scope.row.creator)\">{{ getInitials(scope.row.executor || scope.row.creator) }}</el-avatar>\n            <span class=\"executor-name\">{{ scope.row.executor || scope.row.creator || '未知' }}</span>\n          </template>\n        </el-table-column>\n\n        <!-- 操作按钮 -->\n        <el-table-column label=\"操作\" width=\"190\" fixed=\"right\">\n          <template #default=\"scope\">\n            <div class=\"action-buttons\">\n              <el-button type=\"success\" size=\"small\" plain @click=\"clickView(scope.row)\">\n                <el-icon><View /></el-icon>查看\n              </el-button>\n              <el-button type=\"primary\" size=\"small\" plain @click=\"exportReport(scope.row)\">\n                <el-icon><Download /></el-icon>导出\n              </el-button>\n              <el-dropdown trigger=\"click\">\n                <el-button style=\"margin-left: 10px\" type=\"info\" size=\"small\" plain>\n                  更多<el-icon class=\"el-icon--right\"><ArrowDown /></el-icon>\n                </el-button>\n                <template #dropdown>\n                  <el-dropdown-menu>\n                    <el-dropdown-item @click=\"delPresetting(scope.row.id)\" style=\"color: var(--el-color-danger)\">\n                      <el-icon><Delete /></el-icon>删除\n                    </el-dropdown-item>\n                  </el-dropdown-menu>\n                </template>\n              </el-dropdown>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @current-change=\"currentPages\"\n          @size-change=\"handleSizeChange\"\n          :page-sizes=\"[20, 50, 100, 200]\"\n          :default-page-size=\"20\"\n          :total=\"pages.count\"\n          :current-page=\"pages.current\"\n          :hide-on-single-page=\"false\"\n        >\n        </el-pagination>\n      </div>\n    </el-card>\n  </div>\n\n  <!-- 任务对比对话框已移除，改用独立页面 -->\n</template>\n\n<script>\nimport { ElMessage } from \"element-plus\";\nimport {mapState} from \"vuex\";\nimport environmentMixin from '@/mixins/environmentMixin';\nimport {\n  Refresh, Search, DataAnalysis, View, Download, Delete,\n  RefreshRight, Setting, FullScreen, Plus, Calendar, Timer,\n  CircleCheckFilled, CircleCloseFilled, Loading, DocumentCopy,\n  EditPen, ArrowDown, TrendCharts, PieChart, InfoFilled\n} from '@element-plus/icons-vue';\n\nexport default {\n  mixins: [environmentMixin],\n  components: {\n    Refresh, Search, DataAnalysis, View, Download, Delete,\n    RefreshRight, Setting, FullScreen, Plus, Calendar,\n    CircleCheckFilled, CircleCloseFilled, Loading, DocumentCopy,\n    EditPen, ArrowDown, TrendCharts, PieChart, InfoFilled\n  },\n  data() {\n    return {\n      pages: {\n        current: 1,\n        count: 0,\n        pageSize: 20\n      },\n      tableLoading: false,\n      reportList: [],\n      selectionConfig: {\n        selectedRowKeys: [],\n        selectionChange: this.handleSelectionChange\n      },\n      search:{\n        taskName: '',\n        status: '',\n        dataTime: [this.getFormattedDate(new Date(new Date().getTime() - 6 * 24 * 60 * 60 * 1000)),\n                  this.getFormattedDate(new Date(), true)],\n      },\n      defaultTimeOptions: ['0000-01-01 00:00:00', '0000-01-01 23:59:59'],\n      shortcuts: [\n        {\n          text: '今天',\n          value: (() => {\n            const end = new Date();\n            const start = new Date();\n            start.setHours(0, 0, 0);\n            end.setHours(23, 59, 59);\n            return [start, end];\n          })\n        },\n        {\n          text: '近三天',\n          value: (() => {\n            const end = new Date();\n            const start = new Date();\n            start.setDate(end.getDate() - 2);\n            start.setHours(0, 0, 0);\n            end.setHours(23, 59, 59);\n            return [start, end];\n          })\n        },\n        {\n          text: '近七天',\n          value: (() => {\n            const end = new Date();\n            const start = new Date();\n            start.setDate(end.getDate() - 6);\n            start.setHours(0, 0, 0);\n            end.setHours(23, 59, 59);\n            return [start, end];\n          })\n        },\n       {\n          text: '近一个月',\n          value: (() => {\n            const end = new Date();\n            const start = new Date();\n            start.setMonth(end.getMonth() - 1);\n            start.setHours(0, 0, 0); // 设置时分秒为 00:00:00\n            end.setHours(23, 59, 59); // 设置时分秒为 23:59:59\n            return [start, end];\n          })\n        }],\n      // 统计数据\n      statistics: {\n        total: 0,\n        completed: 0,\n        running: 0,\n        failed: 0\n      },\n\n      // 对话框数据已移除，使用独立的任务对比页面\n    }\n  },\n  mounted() {\n    this.initTableData();\n  },\n  computed: {\n    ...mapState({\n      pro: state => state.pro\n    })\n  },\n  methods: {\n    // 表格行样式\n    tableRowClassName({ row }) {\n      if (row.status === '报告-执行中') {\n        return 'running-row';\n      }\n      return '';\n    },\n\n    // 处理分页大小变化\n    handleSizeChange(size) {\n      this.pages.pageSize = size;\n      this.getTableData();\n    },\n\n    // 刷新数据\n    refreshData() {\n      this.tableLoading = true;\n      setTimeout(() => {\n        this.tableLoading = false;\n        ElMessage({\n          type: 'success',\n          message: '数据已刷新',\n          duration: 1500\n        });\n      }, 800);\n    },\n\n    // 格式化日期\n    formatDate(dateStr) {\n      if (!dateStr) return '-';\n      const date = new Date(dateStr);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n    },\n\n    // 获取CPU颜色\n    getCpuColor(value) {\n      const num = Number(value);\n      if (num < 60) return '#67C23A';\n      if (num < 80) return '#E6A23C';\n      return '#F56C6C';\n    },\n\n    // 获取内存颜色\n    getMemoryColor(value) {\n      const num = Number(value);\n      if (num < 50) return '#67C23A';\n      if (num < 75) return '#E6A23C';\n      return '#F56C6C';\n    },\n\n    // 获取用户头像\n    getAvatarUrl(username) {\n      // 实际项目中可以根据用户名从后端获取头像\n      return '';\n    },\n\n    // 获取用户名首字母\n    getInitials(username) {\n      if (!username) return '';\n      return username.charAt(0).toUpperCase();\n    },\n\n    // 初始化表格数据和小图表\n    initTableData() {\n      this.loadStatistics();\n      this.getTableData();\n    },\n\n    // 获取表格数据\n    async getTableData() {\n      this.tableLoading = true;\n      try {\n        const params = {\n          project_id: this.pro.id,\n          page: this.pages.current,\n          page_size: this.pages.pageSize\n        };\n\n        // 添加搜索条件\n        if (this.search.taskName) {\n          params.taskName = this.search.taskName;\n        }\n        if (this.search.status) {\n          params.reportStatus = this.search.status.replace('报告-', '');\n        }\n        if (this.search.dataTime && this.search.dataTime.length === 2) {\n          params.start_time = this.search.dataTime[0];\n          params.end_time = this.search.dataTime[1];\n        }\n\n        const response = await this.$api.getTaskReports(params);\n\n        if (response.status === 200) {\n          let reportList = response.data.result || response.data.data || [];\n\n          // 数据处理：确保字段映射正确\n          reportList = reportList.map(item => ({\n            ...item,\n            // 确保状态字段正确映射\n            reportStatus: item.reportStatus || item.status,\n            // 确保任务信息正确映射 - 重要：保留原始的task对象\n            taskName: item.taskName || item.task?.taskName || item.name,\n            taskType: item.taskType || item.task?.taskType,\n            pressureMode: item.pressureMode || item.task?.pressureMode,\n            // 确保任务ID正确映射\n            taskId: item.taskId || item.task?.id || item.task_id,\n            // 确保环境信息正确映射\n            envName: item.envName,\n            // 确保执行人信息正确映射\n            executor: item.executor || item.creator || item.user?.username,\n            // 确保时间字段正确映射\n            startTime: item.startTime || item.start_time || item.createTime,\n            endTime: item.endTime || item.end_time || item.updateTime,\n            // 确保性能指标正确映射且保留小数\n            avgCpu: item.avgCpu ? Number(item.avgCpu).toFixed(2) : null,\n            avgMemory: item.avgMemory ? Number(item.avgMemory).toFixed(2) : null,\n            avgResponseTime: item.avgResponseTime ? Number(item.avgResponseTime) : null,\n            avgTps: item.avgTps ? Number(item.avgTps).toFixed(2) : null,\n            totalRequests: item.totalRequests ? Number(item.totalRequests) : null,\n            successRequests: item.successRequests ? Number(item.successRequests) : null,\n            failedRequests: item.failedRequests ? Number(item.failedRequests) : null\n          }));\n\n          this.reportList = reportList;\n          this.pages.count = response.data.count || response.data.total || 0;\n          this.pages.current = response.data.current || response.data.page || 1;\n        }\n      } catch (error) {\n        console.error('获取报告列表失败:', error);\n        ElMessage({\n          type: 'error',\n          message: '获取报告列表失败: ' + (error.response?.data?.message || error.message || '未知错误'),\n          duration: 3000\n        });\n      } finally {\n        this.tableLoading = false;\n      }\n    },\n\n    // 加载统计数据\n    async loadStatistics() {\n      try {\n        const params = {\n          project_id: this.pro.id\n        };\n\n        const response = await this.$api.getTaskReport(params);\n\n        if (response.status === 200) {\n          this.statistics = response.data;\n        }\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n      }\n    },\n\n    handleSelectionChange(selected) {\n      this.selectionConfig.selectedRowKeys = selected.map(item => item.id);\n    },\n\n    currentPages(currentPage) {\n      this.pages.current = currentPage;\n      this.getTableData();\n    },\n\n    convertToTimeZoneFormat(dateStr, timeZone) {\n      const moment = require('moment-timezone');\n      const m = moment.tz(dateStr, timeZone);\n      return m.format('YYYY-MM-DD HH:mm:ss'); // 格式化为年月日时分秒\n    },\n\n    getFormattedDate(date, endOfDay = false) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      let hours, minutes, seconds;\n\n      if (endOfDay) {\n        hours = '23';\n        minutes = '59';\n        seconds = '59';\n      } else {\n        hours = '00';\n        minutes = '00';\n        seconds = '00';\n      }\n\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n\n    clearSearch() {\n      this.search = {\n        taskName: '',\n        status: '',\n        dataTime: [this.getFormattedDate(new Date(new Date().getTime() - 6 * 24 * 60 * 60 * 1000)),\n                      this.getFormattedDate(new Date(), true)]\n      };\n      this.pages.current = 1;\n      this.getTableData();\n    },\n\n    clickSearch() {\n      this.pages.current = 1;\n      this.getTableData();\n    },\n\n    clickView(row) {\n      if (!row || !row.id) {\n        this.$message.error('报告ID不能为空')\n        return\n      }\n      this.$router.push({\n        name: 'PerformanceResult-Detail',\n        params: { id: row.id }\n      });\n    },\n\n    taskDiff() {\n      // 跳转到专门的任务对比页面\n      this.$router.push('/performance/comparison')\n    },\n\n    // 对比相关方法已移除，使用独立页面\n\n    // 对比HTML构建方法已移除，使用独立页面\n\n    // 通用格式化方法保留\n\n    // 格式化指标值\n    formatMetricValue(metric, value) {\n      if (!value && value !== 0) return '-';\n\n      switch (metric) {\n        case 'avgResponseTime':\n          return value < 1000 ? `${value}ms` : `${(value/1000).toFixed(2)}s`;\n        case 'successRate':\n        case 'avgCpu':\n        case 'avgMemory':\n          return `${Number(value).toFixed(1)}%`;\n        case 'totalRequests':\n          return Number(value).toLocaleString();\n        default:\n          return Number(value).toFixed(1);\n      }\n    },\n\n    // 获取指标颜色\n    getMetricColor(metric, value) {\n      switch (metric) {\n        case 'avgResponseTime':\n          return value > 1000 ? '#f56c6c' : value > 500 ? '#e6a23c' : '#67c23a';\n        case 'avgTps':\n          return value > 100 ? '#67c23a' : value > 50 ? '#e6a23c' : '#f56c6c';\n        case 'successRate':\n          return value > 95 ? '#67c23a' : value > 80 ? '#e6a23c' : '#f56c6c';\n        default:\n          return '#409eff';\n      }\n    },\n\n    // 对比专用方法已删除\n\n    async exportReport(row) {\n      try {\n        if (!row || !row.id) {\n          ElMessage({\n            type: 'error',\n            message: '导出失败: 报告ID不能为空',\n            duration: 3000\n          });\n          return;\n        }\n\n        ElMessage({\n          type: 'info',\n          message: '报告导出中，请稍候...',\n          duration: 2000\n        });\n\n        const response = await this.$api.exportSingleReport(row.id);\n\n        if (response.status === 200) {\n          const contentDisposition = response.headers['content-disposition'];\n          let filename = '性能测试报告.xlsx';\n          \n          // 尝试从header获取文件名\n          if (contentDisposition) {\n            const filenameMatch = contentDisposition.match(/filename=\"?([^\"]*)\"?/);\n            if (filenameMatch && filenameMatch[1]) {\n              filename = filenameMatch[1];\n            }\n          } else {\n            // 使用报告名称作为文件名\n            filename = `${row.reportName || '性能测试报告'}.xlsx`;\n          }\n\n          const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\n          const link = document.createElement('a');\n          link.href = URL.createObjectURL(blob);\n          link.download = filename;\n          link.click();\n\n          ElMessage({\n            type: 'success',\n            message: '导出成功',\n            duration: 2000\n          });\n        }\n      } catch (error) {\n        ElMessage({\n          type: 'error',\n          message: '导出失败: ' + (error.message || '未知错误'),\n          duration: 3000\n        });\n      }\n    },\n\n    async delPresetting(id) {\n      try {\n        const response = await this.$api.delTaskReport(id);\n\n        if (response.status === 204) {\n          ElMessage({\n            type: 'success',\n            message: '删除成功'\n          });\n          this.getTableData();\n          this.loadStatistics();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          ElMessage({\n            type: 'error',\n            message: '删除失败: ' + (error.message || '未知错误')\n          });\n        }\n      }\n    },\n\n    // 格式化百分比（保留两位小数）\n    formatPercentage(value) {\n      if (!value && value !== 0) return '-';\n      const num = Number(value);\n      return num.toFixed(2) + '%';\n    },\n\n    // 格式化数字\n    formatNumber(num) {\n      if (!num && num !== 0) return '-';\n      return num.toLocaleString();\n    },\n\n    // 获取成功率样式\n    getSuccessRateClass(rate) {\n      const num = Number(rate);\n      if (num >= 95) return 'success-rate-high';\n      if (num >= 80) return 'success-rate-medium';\n      return 'success-rate-low';\n    },\n\n    // 获取成功率\n    getSuccessRate(row) {\n      if (!row.totalRequests || row.totalRequests === 0) return 0;\n      const successCount = row.totalRequests - (row.failedRequests || 0);\n      const rate = (successCount / row.totalRequests) * 100;\n      return rate.toFixed(1);\n    },\n\n    // 格式化响应时间\n    formatResponseTime(time) {\n      if (!time) return '-';\n      const num = Number(time);\n      if (num < 1000) return num.toFixed(0) + 'ms';\n      return (num / 1000).toFixed(2) + 's';\n    },\n\n    // 格式化持续时间\n    formatDuration(duration) {\n      if (!duration) return '-';\n      const seconds = Math.floor(duration / 1000);\n      const minutes = Math.floor(seconds / 60);\n      const hours = Math.floor(minutes / 60);\n\n      if (hours > 0) {\n        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;\n      } else if (minutes > 0) {\n        return `${minutes}m ${seconds % 60}s`;\n      } else {\n        return `${seconds}s`;\n      }\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        '0': '已完成',\n        '1': '执行中',\n        '99': '运行失败'\n      };\n      return statusMap[status] || '未知状态';\n    },\n\n    // 获取任务类型文本\n    getTaskTypeText(type) {\n      if (!type && type !== 0) return '-';\n      const typeMap = {\n        '1': '普通任务',\n        '2': '定时任务',\n        '10': '普通任务',\n        '20': '定时任务'\n      };\n      return typeMap[String(type)] || '-';\n    },\n\n    // 获取运行模式文本\n    getRunPatternText(pattern) {\n      if (!pattern && pattern !== 0) return '-';\n      const patternMap = {\n        '1': '并发模式',\n        '2': '阶梯模式',\n        '10': '并发模式',\n        '20': '阶梯模式'\n      };\n      return patternMap[String(pattern)] || '-';\n    },\n\n    // 对话框相关方法\n\n    // 获取状态标签类型\n    getStatusTagType(status) {\n      const typeMap = {\n        '0': 'success',\n        '1': 'primary',\n        '99': 'danger'\n      };\n      return typeMap[status] || 'info';\n    },\n\n    // 获取对比表格数据\n    getComparisonTableData() {\n      if (!this.comparisonDialogData.comparisonData?.tasks) return [];\n\n      const tasks = this.comparisonDialogData.comparisonData.tasks;\n      const metrics = ['avgResponseTime', 'avgTps', 'successRate', 'avgCpu', 'avgMemory', 'totalRequests'];\n      const metricLabels = {\n        avgResponseTime: '平均响应时间(ms)',\n        avgTps: '平均TPS',\n        successRate: '成功率(%)',\n        avgCpu: '平均CPU(%)',\n        avgMemory: '平均内存(%)',\n        totalRequests: '总请求数'\n      };\n\n      return metrics.map(metric => {\n        const values = tasks.map(task => task[metric] || 0);\n        const bestIndex = this.getBestMetricIndex(metric, values);\n\n        return {\n          metric: metricLabels[metric],\n          values: values.map(value => this.formatMetricValue(metric, value)),\n          bestIndex,\n          bestValue: this.formatMetricValue(metric, values[bestIndex])\n        };\n      });\n    },\n\n    // 获取指标百分比\n    getMetricPercentage(metric, value) {\n      if (!this.comparisonDialogData.comparisonData?.tasks) return 0;\n\n      const tasks = this.comparisonDialogData.comparisonData.tasks;\n      const values = tasks.map(task => task[metric] || 0);\n      const maxValue = Math.max(...values.filter(v => v > 0));\n\n      if (maxValue === 0) return 0;\n      return Math.min((value / maxValue) * 100, 100);\n    },\n\n    // 获取最佳任务\n    getBestTask() {\n      if (!this.comparisonDialogData.comparisonData?.tasks) return {};\n\n      const tasks = this.comparisonDialogData.comparisonData.tasks;\n      return this.findBestPerformanceTask(tasks);\n    },\n  },\n\n  created() {\n\n  }\n}\n</script>\n\n<style scoped>\n.performance-result {\n  padding: 16px;\n  background-color: var(--el-bg-color-page, #f2f3f5);\n  min-height: calc(100vh - 100px);\n}\n\n/* 统计卡片样式 */\n.statistics-cards {\n  margin-bottom: 16px;\n}\n\n.stat-card {\n  transition: all 0.3s;\n  cursor: pointer;\n  border-radius: 8px;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n}\n\n.stat-content {\n  display: flex;\n  align-items: center;\n}\n\n.stat-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  margin-right: 16px;\n}\n\n.stat-icon.success {\n  background-color: rgba(103, 194, 58, 0.1);\n  color: #67C23A;\n}\n\n.stat-icon.running {\n  background-color: rgba(64, 158, 255, 0.1);\n  color: #409EFF;\n}\n\n.stat-icon.failed {\n  background-color: rgba(245, 108, 108, 0.1);\n  color: #F56C6C;\n}\n\n.stat-icon.total {\n  background-color: rgba(121, 187, 255, 0.1);\n  color: #79BBFF;\n}\n\n.stat-info {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  line-height: 1.2;\n  color: var(--el-text-color-primary);\n}\n\n.stat-label {\n  font-size: 14px;\n  color: var(--el-text-color-secondary);\n  margin-top: 4px;\n}\n\n/* 搜索区域样式 */\n.search-card {\n  margin-bottom: 16px;\n  border-radius: 8px;\n}\n\n.search-form {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.date-picker {\n  width: 340px;\n}\n\n/* 操作工具栏 */\n.action-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n/* 表格样式 */\n.table-card {\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n.running-row {\n  background-color: rgba(64, 158, 255, 0.05);\n}\n\n.report-name-cell {\n  display: flex;\n  align-items: center;\n}\n\n.status-icon {\n  margin-right: 8px;\n  font-size: 16px;\n}\n\n.status-icon.success {\n  color: #67C23A;\n}\n\n.status-icon.failed {\n  color: #F56C6C;\n}\n\n.status-icon.running {\n  color: #409EFF;\n}\n\n.metric-cell {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 100%;\n}\n\n.metric-value {\n  margin-bottom: 5px;\n  font-weight: 600;\n}\n\n/* 添加迷你条形图样式 */\n.mini-bar-chart {\n  width: 100%;\n  height: 4px;\n  background-color: rgba(64, 158, 255, 0.1);\n  border-radius: 2px;\n  overflow: hidden;\n}\n\n.mini-bar {\n  height: 100%;\n  background-color: #409EFF;\n  border-radius: 2px;\n}\n\n/* 确保el-progress组件正确显示 */\n.metric-cell :deep(.el-progress) {\n  width: 100%;\n}\n\n.metric-cell :deep(.el-progress-bar) {\n  width: 100%;\n}\n\n.metric-cell :deep(.el-progress-bar__outer) {\n  height: 4px !important;\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.metric-cell :deep(.el-progress-bar__inner) {\n  transition: width 0.6s ease;\n}\n\n.time-info {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-size: 12px;\n}\n\n.time-item {\n  display: flex;\n  align-items: center;\n}\n\n.time-item .el-icon {\n  margin-right: 4px;\n}\n\n.time-separator {\n  margin: 0 8px;\n  color: var(--el-text-color-secondary);\n}\n\n.executor-name {\n  margin-left: 8px;\n  vertical-align: middle;\n}\n\n.action-buttons {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.running-tag {\n  display: flex;\n  align-items: center;\n}\n\n.running-tag .el-icon {\n  margin-right: 5px;\n}\n\n/* 分页样式 */\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n/* 响应式适配 */\n@media (max-width: 1200px) {\n  .search-form {\n    flex-direction: column;\n  }\n}\n\n/* 成功率样式 */\n.success-rate-high {\n  color: #67C23A;\n  font-weight: 600;\n}\n\n.success-rate-medium {\n  color: #E6A23C;\n  font-weight: 600;\n}\n\n.success-rate-low {\n  color: #F56C6C;\n  font-weight: 600;\n}\n\n/* 对比对话框样式 */\n.comparison-dialog-content {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.comparison-section {\n  margin-bottom: 20px;\n}\n\n.comparison-section-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: 600;\n  color: #409eff;\n}\n\n.comparison-section-title .el-icon {\n  margin-right: 8px;\n}\n\n.task-overview-card {\n  height: 100%;\n}\n\n.task-overview-card h4 {\n  margin: 0 0 10px 0;\n  color: #303133;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.task-overview-info {\n  font-size: 12px;\n}\n\n.overview-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 5px;\n}\n\n.overview-label {\n  color: #606266;\n  font-weight: 500;\n}\n\n.overview-value {\n  color: #303133;\n}\n\n/* 对比相关样式已移除 */\n</style>", "/**\n * 环境配置管理 Mixin\n * 提供环境选择、配置获取等通用功能\n */\n\nexport default {\n  data() {\n    return {\n      // 环境信息\n      envInfo: {},\n      \n      // 环境列表\n      testEnvs: [],\n      \n      // 显示环境详情对话框\n      showEnvDialog: false,\n      \n      // 当前选择的环境ID\n      selectedEnvId: null\n    }\n  },\n\n  computed: {\n    /**\n     * 当前环境ID（从Vuex获取）\n     */\n    currentEnvId() {\n      return this.$store?.state?.envId || this.selectedEnvId;\n    },\n\n    /**\n     * 当前环境信息\n     */\n    currentEnvInfo() {\n      if (!this.currentEnvId || !this.testEnvs.length) return null;\n      return this.testEnvs.find(env => env.id === this.currentEnvId);\n    },\n\n    /**\n     * 当前环境名称\n     */\n    currentEnvName() {\n      return this.currentEnvInfo?.name || '未选择环境';\n    },\n\n    /**\n     * 是否已选择环境\n     */\n    hasSelectedEnv() {\n      return !!this.currentEnvId;\n    },\n\n    /**\n     * 环境选择选项\n     */\n    envOptions() {\n      return this.testEnvs.map(env => ({\n        label: env.name,\n        value: env.id,\n        disabled: env.status === 'disabled'\n      }));\n    }\n  },\n\n  methods: {\n    /**\n     * 获取测试环境列表\n     * @param {string} projectId - 项目ID\n     */\n    async fetchTestEnvs(projectId = null) {\n      try {\n        const pid = projectId || this.currentProjectId;\n        if (!pid) {\n          console.warn('项目ID未定义，无法获取环境列表');\n          return;\n        }\n\n        const response = await this.$api.getTestEnvs(pid);\n        if (response.status === 200) {\n          this.testEnvs = response.data || [];\n          \n          // 如果当前没有选择环境且有可用环境，选择第一个\n          if (!this.currentEnvId && this.testEnvs.length > 0) {\n            this.selectEnv(this.testEnvs[0].id);\n          }\n        }\n      } catch (error) {\n        console.error('获取环境列表失败:', error);\n        this.$message({\n          type: 'error',\n          message: '获取环境列表失败',\n          duration: 2000\n        });\n      }\n    },\n\n    /**\n     * 获取环境详细信息\n     * @param {string} envId - 环境ID\n     * @param {string} projectId - 项目ID\n     */\n    async fetchEnvInfo(envId = null, projectId = null) {\n      try {\n        const eid = envId || this.currentEnvId;\n        const pid = projectId || this.currentProjectId;\n        \n        if (!eid || !pid) {\n          console.warn('环境ID或项目ID未定义');\n          return null;\n        }\n\n        const response = await this.$api.getEnvInfo(eid, pid);\n        if (response.status === 200) {\n          this.envInfo = response.data || {};\n          return this.envInfo;\n        }\n      } catch (error) {\n        console.error('获取环境信息失败:', error);\n        this.$message({\n          type: 'error',\n          message: '获取环境信息失败',\n          duration: 2000\n        });\n      }\n      return null;\n    },\n\n    /**\n     * 选择环境\n     * @param {string} envId - 环境ID\n     */\n    selectEnv(envId) {\n      this.selectedEnvId = envId;\n      \n      // 更新Vuex状态（如果有）\n      if (this.$store && this.$store.commit) {\n        this.$store.commit('selectEnv', envId);\n      }\n      \n      // 触发环境选择事件\n      this.$emit('env-selected', envId);\n      \n      // 自动获取环境详情\n      if (envId) {\n        this.fetchEnvInfo(envId);\n      }\n    },\n\n    /**\n     * 显示环境详情\n     * @param {string} envId - 环境ID（可选）\n     */\n    async showEnvDetails(envId = null) {\n      const eid = envId || this.currentEnvId;\n      if (!eid) {\n        this.$message({\n          type: 'warning',\n          message: '请先选择环境',\n          duration: 2000\n        });\n        return;\n      }\n\n      // 获取环境详情\n      await this.fetchEnvInfo(eid);\n      \n      // 显示环境详情对话框\n      this.showEnvDialog = true;\n    },\n\n    /**\n     * 关闭环境详情对话框\n     */\n    closeEnvDialog() {\n      this.showEnvDialog = false;\n    },\n\n    /**\n     * 编辑环境配置\n     * @param {object} envInfo - 环境信息\n     */\n    editEnvConfig(envInfo = null) {\n      const info = envInfo || this.envInfo;\n      if (!info) {\n        this.$message({\n          type: 'warning',\n          message: '环境信息不存在',\n          duration: 2000\n        });\n        return;\n      }\n\n      // 存储环境信息到Vuex（如果有）\n      if (this.$store && this.$store.commit) {\n        this.$store.commit('selectEnvInfo', info);\n      }\n\n      // 跳转到环境配置页面\n      this.$router.push({ name: 'testenv' });\n    },\n\n    /**\n     * 验证环境配置\n     * @param {object} envInfo - 环境信息\n     * @returns {object} 验证结果\n     */\n    validateEnvConfig(envInfo = null) {\n      const info = envInfo || this.envInfo;\n      const errors = [];\n\n      if (!info) {\n        errors.push('环境信息不存在');\n        return { isValid: false, errors };\n      }\n\n      // 检查基本配置\n      if (!info.name) {\n        errors.push('环境名称未配置');\n      }\n\n      // 检查全局变量\n      if (!info.global_variable || Object.keys(info.global_variable).length === 0) {\n        errors.push('全局变量未配置');\n      }\n\n      // 检查基础URL\n      const globalVars = info.global_variable || {};\n      const baseUrl = globalVars.base_url || globalVars.baseUrl;\n      if (!baseUrl) {\n        errors.push('基础URL未配置');\n      }\n\n      return {\n        isValid: errors.length === 0,\n        errors\n      };\n    },\n\n    /**\n     * 格式化环境变量显示\n     * @param {object} variables - 变量对象\n     * @param {string} type - 变量类型\n     * @returns {Array}\n     */\n    formatEnvVariables(variables = {}, type = 'global') {\n      if (!variables || typeof variables !== 'object') return [];\n\n      return Object.entries(variables).map(([key, value]) => ({\n        key,\n        value,\n        type,\n        displayValue: this.formatVariableValue(value)\n      }));\n    },\n\n    /**\n     * 格式化变量值显示\n     * @param {any} value - 变量值\n     * @returns {string}\n     */\n    formatVariableValue(value) {\n      if (value === null || value === undefined) return '';\n      if (typeof value === 'object') return JSON.stringify(value);\n      if (typeof value === 'string' && value.length > 50) {\n        return value.substring(0, 50) + '...';\n      }\n      return String(value);\n    },\n\n    /**\n     * 获取环境变量\n     * @param {string} key - 变量键\n     * @param {any} defaultValue - 默认值\n     * @returns {any}\n     */\n    getEnvVariable(key, defaultValue = null) {\n      if (!this.envInfo) return defaultValue;\n\n      // 优先从debug变量获取\n      if (this.envInfo.debug_global_variable && this.envInfo.debug_global_variable[key] !== undefined) {\n        return this.envInfo.debug_global_variable[key];\n      }\n\n      // 从全局变量获取\n      if (this.envInfo.global_variable && this.envInfo.global_variable[key] !== undefined) {\n        return this.envInfo.global_variable[key];\n      }\n\n      return defaultValue;\n    },\n\n    /**\n     * 检查环境是否可用\n     * @param {string} envId - 环境ID\n     * @returns {boolean}\n     */\n    isEnvAvailable(envId = null) {\n      const eid = envId || this.currentEnvId;\n      if (!eid || !this.testEnvs.length) return false;\n\n      const env = this.testEnvs.find(e => e.id === eid);\n      return env && env.status !== 'disabled';\n    },\n\n    /**\n     * 获取环境状态标签类型\n     * @param {string} status - 环境状态\n     * @returns {string}\n     */\n    getEnvStatusType(status) {\n      const statusMap = {\n        active: 'success',\n        inactive: 'warning',\n        disabled: 'danger',\n        pending: 'info'\n      };\n      return statusMap[status] || 'info';\n    }\n  },\n\n  // 生命周期钩子\n  async created() {\n    // 自动获取环境列表\n    if (this.currentProjectId) {\n      await this.fetchTestEnvs();\n    }\n  },\n\n  watch: {\n    // 监听项目变化，重新获取环境列表\n    currentProjectId: {\n      handler(newProjectId) {\n        if (newProjectId) {\n          this.fetchTestEnvs(newProjectId);\n        }\n      },\n      immediate: false\n    }\n  }\n};", "import { render } from \"./PerformanceResult.vue?vue&type=template&id=39eb011f&scoped=true\"\nimport script from \"./PerformanceResult.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceResult.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceResult.vue?vue&type=style&index=0&id=39eb011f&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-39eb011f\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_card", "shadow", "_hoisted_3", "_hoisted_4", "_component_el_icon", "_component_CircleCheckFilled", "_hoisted_5", "_hoisted_6", "_toDisplayString", "$data", "statistics", "completed", "_hoisted_7", "_hoisted_8", "_component_Loading", "_hoisted_9", "_hoisted_10", "running", "_hoisted_11", "_hoisted_12", "_component_CircleCloseFilled", "_hoisted_13", "_hoisted_14", "failed", "_hoisted_15", "_hoisted_16", "_component_DataAnalysis", "_hoisted_17", "_hoisted_18", "total", "_component_el_form", "inline", "_component_el_form_item", "label", "_component_el_date_picker", "search", "dataTime", "$event", "type", "defaultTimeOptions", "shortcuts", "clearable", "_component_el_input", "taskName", "placeholder", "prefix", "_withCtx", "_component_Search", "_component_el_select", "style", "status", "_component_el_option", "value", "_component_el_button", "onClick", "$options", "clickSearch", "plain", "clearSearch", "_component_Refresh", "_hoisted_19", "_hoisted_20", "taskDiff", "_hoisted_21", "circle", "refreshData", "_component_RefreshRight", "_component_Setting", "_createBlock", "_component_el_table", "ref", "data", "reportList", "size", "border", "onSelectionChange", "handleSelectionChange", "tableRowClassName", "height", "_component_el_table_column", "prop", "default", "scope", "_hoisted_22", "row", "reportName", "width", "align", "reportStatus", "_component_el_tag", "effect", "_hoisted_23", "getStatusText", "task", "getTaskTypeText", "taskType", "pressureMode", "envName", "formatNumber", "totalRequests", "_normalizeClass", "getSuccessRateClass", "getSuccessRate", "formatResponseTime", "avgResponseTime", "_hoisted_24", "_hoisted_25", "avgTps", "_hoisted_26", "_normalizeStyle", "Math", "min", "Number", "_hoisted_27", "_hoisted_28", "formatPercentage", "avgCpu", "_component_el_progress", "percentage", "color", "getCpuColor", "_hoisted_29", "_hoisted_30", "avgMemory", "getMemoryColor", "_hoisted_31", "_component_el_tooltip", "content", "_ctx", "$tools", "rTime", "startTime", "start_time", "placement", "_hoisted_32", "_component_Calendar", "formatDate", "endTime", "end_time", "_hoisted_33", "_component_el_avatar", "src", "getAvatarUrl", "executor", "creator", "getInitials", "_hoisted_34", "fixed", "_hoisted_35", "clickView", "_component_View", "exportReport", "_component_Download", "_component_el_dropdown", "trigger", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "delPresetting", "id", "_component_Delete", "_component_ArrowDown", "tableLoading", "_hoisted_36", "_component_el_pagination", "background", "layout", "onCurrentChange", "currentPages", "onSizeChange", "handleSizeChange", "pages", "count", "current", "envInfo", "testEnvs", "showEnvDialog", "selectedEnvId", "computed", "currentEnvId", "this", "$store", "state", "envId", "currentEnvInfo", "length", "find", "env", "currentEnvName", "name", "hasSelectedEnv", "envOptions", "map", "disabled", "methods", "fetchTestEnvs", "projectId", "pid", "currentProjectId", "console", "warn", "response", "$api", "getTestEnvs", "selectEnv", "error", "$message", "message", "duration", "fetchEnvInfo", "eid", "getEnvInfo", "commit", "$emit", "showEnvDetails", "closeEnvDialog", "editEnvConfig", "info", "$router", "push", "validateEnvConfig", "errors", "<PERSON><PERSON><PERSON><PERSON>", "global_variable", "Object", "keys", "globalVars", "baseUrl", "base_url", "formatEnvVariables", "variables", "entries", "key", "displayValue", "formatVariableValue", "undefined", "JSON", "stringify", "substring", "String", "getEnvVariable", "defaultValue", "debug_global_variable", "isEnvAvailable", "e", "getEnvStatusType", "statusMap", "active", "inactive", "pending", "created", "watch", "handler", "newProjectId", "immediate", "mixins", "environmentMixin", "components", "Refresh", "Search", "DataAnalysis", "View", "Download", "Delete", "RefreshRight", "Setting", "FullScreen", "Plus", "Calendar", "CircleCheckFilled", "CircleCloseFilled", "Loading", "DocumentCopy", "EditPen", "ArrowDown", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "InfoFilled", "pageSize", "selectionConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectionChange", "getFormattedDate", "Date", "getTime", "text", "end", "start", "setHours", "setDate", "getDate", "setMonth", "getMonth", "mounted", "initTableData", "mapState", "pro", "getTableData", "setTimeout", "ElMessage", "dateStr", "date", "getFullYear", "padStart", "getHours", "getMinutes", "num", "username", "char<PERSON>t", "toUpperCase", "loadStatistics", "params", "project_id", "page", "page_size", "replace", "getTaskReports", "result", "item", "taskId", "task_id", "user", "createTime", "updateTime", "toFixed", "successRequests", "failedRequests", "getTaskReport", "selected", "currentPage", "convertToTimeZoneFormat", "timeZone", "moment", "require", "m", "tz", "format", "endOfDay", "year", "month", "day", "hours", "minutes", "seconds", "formatMetricValue", "metric", "toLocaleString", "getMetricColor", "exportSingleReport", "contentDisposition", "headers", "filename", "filenameMatch", "match", "blob", "Blob", "link", "document", "createElement", "href", "URL", "createObjectURL", "download", "click", "delTaskReport", "rate", "successCount", "time", "formatDuration", "floor", "typeMap", "getRunPatternText", "pattern", "patternMap", "getStatusTagType", "getComparisonTableData", "comparisonDialogData", "comparisonData", "tasks", "metrics", "metricLabels", "successRate", "values", "bestIndex", "getBestMetricIndex", "bestValue", "getMetricPercentage", "maxValue", "max", "filter", "v", "getBestTask", "findBestPerformanceTask", "__exports__", "render"], "sourceRoot": ""}