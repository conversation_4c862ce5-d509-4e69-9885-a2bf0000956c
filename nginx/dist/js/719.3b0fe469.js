"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[719],{23719:function(e,l,t){t.r(l),t.d(l,{default:function(){return G}});var a=t(56768),u=t(24232),s=t(90144),n=t(51219),o=t(60782),r=t(81387);const d={class:"blind-test-container"},i={class:"title"},c={class:"query_model"},p={class:"buttons"},_={class:"operation-buttons"},k={style:{"font-weight":"bold"}},b={key:0},m={key:1},v={key:2},f={key:3},g={key:4},h={class:"pagination-block"},y={key:0,class:"blind-test-dialog"},F={class:"interface-info"},w={class:"interface-test-form"},L={class:"dialog-footer"},x={key:0,class:"test-result-container"},C={class:"result-header"},W={class:"result-content"},E={class:"result-panel"},T={class:"json-content"},I={class:"result-panel"},V={class:"diff-analysis"},S={key:0},X={key:1},P={class:"diff-items"},z={class:"test-logs"},U={class:"log-time"},D={class:"log-time"},K={class:"dialog-footer"},O='{\n  "name": "API盲测示例",\n  "status": 200,\n  "data": {\n    "id": 1001,\n    "value": "示例值"\n  }\n}';var R={__name:"BlindTest",setup(e){const l=(0,o.Pj)(),t=((0,r.rd)(),(0,a.EW)(()=>l.state.projectId),(0,s.KR)({result:[],count:0,current:1,size:10})),R=(0,s.KR)(!1),A=(0,s.Kh)({name:"",method:"",url:"",status:""}),Q=((0,s.KR)([]),(0,s.KR)(null)),G=(0,s.KR)(!1),J=(0,s.KR)(!1),j=(0,s.KR)(!1),N=(0,s.Kh)({expected_result:[{required:!0,message:"请输入预期结果",trigger:"blur"}]}),q=(0,s.Kh)({interface_id:"",params:{},headers:{},expected_result:"",actual_result:"",status:"pending"}),H=[{label:"全部",value:""},{label:"GET",value:"GET"},{label:"POST",value:"POST"},{label:"PUT",value:"PUT"},{label:"PATCH",value:"PATCH"},{label:"DELETE",value:"DELETE"}],M=[{label:"全部",value:""},{label:"待测试",value:"pending"},{label:"通过",value:"pass"},{label:"失败",value:"fail"}],B=((0,s.KR)({query:{page:1,size:10},body:{name:"测试数据",value:123},path:{id:456}}),[{id:1,name:"获取用户列表",method:"GET",url:"/api/users",create_time:(new Date).toISOString(),status:"pending",expected_result:null,actual_result:null},{id:2,name:"创建新用户",method:"POST",url:"/api/users",create_time:(new Date).toISOString(),status:"pass",expected_result:'{"code":200,"message":"用户创建成功","data":{"id":1003}}',actual_result:'{"code":200,"message":"用户创建成功","data":{"id":1003}}'},{id:3,name:"更新用户信息",method:"PUT",url:"/api/users/:id",create_time:(new Date).toISOString(),status:"fail",expected_result:'{"code":200,"message":"更新成功"}',actual_result:'{"code":400,"message":"参数错误"}'},{id:4,name:"删除用户",method:"DELETE",url:"/api/users/:id",create_time:(new Date).toISOString(),status:"pending",expected_result:null,actual_result:null},{id:5,name:"用户登录",method:"POST",url:"/api/login",create_time:(new Date).toISOString(),status:"pass",expected_result:'{"code":200,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}',actual_result:'{"code":200,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}'}]),$=()=>{R.value=!0,setTimeout(()=>{const e=B.filter(e=>{const l=!A.name||e.name.includes(A.name),t=!A.method||e.method===A.method,a=!A.url||e.url.includes(A.url),u=!A.status||e.status===A.status;return l&&t&&a&&u});t.value={result:e,count:e.length,current:1,size:10},R.value=!1},500)},Y=()=>{A.name="",A.method="",A.url="",A.status="",$()},Z=()=>{$()},ee=e=>{t.value.size=e,$()},le=e=>{t.value.current=e,$()},te=e=>{G.value=!0,Q.value=e,q.interface_id=e.id,e.expected_result?q.expected_result=e.expected_result:q.expected_result=O,q.actual_result="",q.status="pending"},ae=()=>{R.value=!0,setTimeout(()=>{const e=Math.random()>.5;e?(q.actual_result=q.expected_result,q.status="pass",n.nk.success("测试通过")):(q.actual_result='{\n        "code": 400,\n        "message": "参数错误",\n        "errors": ["参数格式不正确", "缺少必要参数"]\n      }',q.status="fail",n.nk.error("测试失败"));const l=t.value.result.findIndex(e=>e.id===Q.value.id);-1!==l&&(t.value.result[l].expected_result=q.expected_result,t.value.result[l].actual_result=q.actual_result,t.value.result[l].status=q.status),R.value=!1,J.value=!0,G.value=!1},1e3)},ue=e=>{e.actual_result?(Q.value=e,q.expected_result=e.expected_result,q.actual_result=e.actual_result,J.value=!0):n.nk.warning("该接口尚未测试")},se=()=>{G.value=!1,J.value=!1,j.value=!1},ne=()=>{n.nk.success("测试报告导出成功")},oe=()=>{R.value=!0,setTimeout(()=>{n.nk.success("批量测试已完成"),$(),R.value=!1},2e3)};return(0,a.sV)(()=>{$()}),(e,l)=>{const s=(0,a.g2)("el-input"),n=(0,a.g2)("el-option"),o=(0,a.g2)("el-select"),r=(0,a.g2)("el-button"),O=(0,a.g2)("el-table-column"),j=(0,a.g2)("el-tag"),B=(0,a.g2)("el-table"),$=(0,a.g2)("el-pagination"),re=(0,a.g2)("el-descriptions-item"),de=(0,a.g2)("el-descriptions"),ie=(0,a.g2)("el-form-item"),ce=(0,a.g2)("el-tab-pane"),pe=(0,a.g2)("el-tabs"),_e=(0,a.g2)("el-form"),ke=(0,a.g2)("el-dialog"),be=(0,a.g2)("el-alert"),me=(0,a.g2)("el-col"),ve=(0,a.g2)("el-row"),fe=(0,a.g2)("el-empty"),ge=(0,a.g2)("el-timeline-item"),he=(0,a.g2)("el-timeline"),ye=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.Lk)("div",d,[(0,a.Lk)("div",i,[l[21]||(l[21]=(0,a.Lk)("h2",null,"接口盲测",-1)),(0,a.Lk)("div",c,[(0,a.Lk)("span",null,[l[8]||(l[8]=(0,a.eW)("接口名称 ")),(0,a.bF)(s,{modelValue:A.name,"onUpdate:modelValue":l[0]||(l[0]=e=>A.name=e),placeholder:"请输入接口名称",autocomplete:"off",maxlength:"30",clearable:!0,style:{width:"200px"}},null,8,["modelValue"])]),(0,a.Lk)("span",null,[l[9]||(l[9]=(0,a.eW)("请求类型 ")),(0,a.bF)(o,{modelValue:A.method,"onUpdate:modelValue":l[1]||(l[1]=e=>A.method=e),placeholder:"请选择请求类型",clearable:!0,style:{width:"200px"}},{default:(0,a.k6)(()=>[((0,a.uX)(),(0,a.CE)(a.FK,null,(0,a.pI)(H,e=>(0,a.bF)(n,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),(0,a.Lk)("span",null,[l[10]||(l[10]=(0,a.eW)("接口地址 ")),(0,a.bF)(s,{modelValue:A.url,"onUpdate:modelValue":l[2]||(l[2]=e=>A.url=e),placeholder:"请输入接口地址",autocomplete:"off",maxlength:"30",clearable:!0,style:{width:"200px"}},null,8,["modelValue"])]),(0,a.Lk)("span",null,[l[11]||(l[11]=(0,a.eW)("测试状态 ")),(0,a.bF)(o,{modelValue:A.status,"onUpdate:modelValue":l[3]||(l[3]=e=>A.status=e),placeholder:"请选择测试状态",clearable:!0,style:{width:"200px"}},{default:(0,a.k6)(()=>[((0,a.uX)(),(0,a.CE)(a.FK,null,(0,a.pI)(M,e=>(0,a.bF)(n,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),(0,a.Lk)("span",p,[(0,a.bF)(r,{onClick:Y,icon:"Refresh"},{default:(0,a.k6)(()=>l[12]||(l[12]=[(0,a.eW)("重置")])),_:1,__:[12]}),(0,a.bF)(r,{type:"success",onClick:Z,icon:"Search"},{default:(0,a.k6)(()=>l[13]||(l[13]=[(0,a.eW)("查询")])),_:1,__:[13]})])]),(0,a.Lk)("div",_,[(0,a.bF)(r,{type:"primary",onClick:oe,icon:"VideoPlay"},{default:(0,a.k6)(()=>l[14]||(l[14]=[(0,a.eW)("批量盲测")])),_:1,__:[14]}),(0,a.bF)(r,{type:"success",onClick:ne,icon:"DocumentCopy"},{default:(0,a.k6)(()=>l[15]||(l[15]=[(0,a.eW)("导出测试报告")])),_:1,__:[15]})]),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(B,{data:t.value.result,"empty-text":"暂无数据",border:"",stripe:""},{default:(0,a.k6)(()=>[(0,a.bF)(O,{label:"序号",align:"center",width:"60"},{default:(0,a.k6)(e=>[(0,a.Lk)("span",null,(0,u.v_)(e.$index+1),1)]),_:1}),(0,a.bF)(O,{label:"接口名称",prop:"name",align:"center","show-overflow-tooltip":""}),(0,a.bF)(O,{label:"请求类型",align:"center",width:"100"},{default:(0,a.k6)(e=>[(0,a.Lk)("div",k,["POST"===e.row.method?((0,a.uX)(),(0,a.CE)("span",b,[(0,a.bF)(j,{color:"#49cc90",size:"large"},{default:(0,a.k6)(()=>[(0,a.eW)((0,u.v_)(e.row.method),1)]),_:2},1024)])):(0,a.Q3)("",!0),"GET"===e.row.method?((0,a.uX)(),(0,a.CE)("span",m,[(0,a.bF)(j,{color:"#61affe",size:"large"},{default:(0,a.k6)(()=>[(0,a.eW)((0,u.v_)(e.row.method),1)]),_:2},1024)])):(0,a.Q3)("",!0),"PUT"===e.row.method?((0,a.uX)(),(0,a.CE)("span",v,[(0,a.bF)(j,{color:"#fca130",size:"large"},{default:(0,a.k6)(()=>[(0,a.eW)((0,u.v_)(e.row.method),1)]),_:2},1024)])):(0,a.Q3)("",!0),"PATCH"===e.row.method?((0,a.uX)(),(0,a.CE)("span",f,[(0,a.bF)(j,{color:"#50e3c2",size:"large"},{default:(0,a.k6)(()=>[(0,a.eW)((0,u.v_)(e.row.method),1)]),_:2},1024)])):(0,a.Q3)("",!0),"DELETE"===e.row.method?((0,a.uX)(),(0,a.CE)("span",g,[(0,a.bF)(j,{color:"#f93e3e",size:"large"},{default:(0,a.k6)(()=>[(0,a.eW)((0,u.v_)(e.row.method),1)]),_:2},1024)])):(0,a.Q3)("",!0)])]),_:1}),(0,a.bF)(O,{label:"接口地址",prop:"url","show-overflow-tooltip":"",align:"center"}),(0,a.bF)(O,{label:"测试状态",align:"center",width:"100"},{default:(0,a.k6)(e=>["pending"===e.row.status?((0,a.uX)(),(0,a.Wv)(j,{key:0,type:"info"},{default:(0,a.k6)(()=>l[16]||(l[16]=[(0,a.eW)("待测试")])),_:1,__:[16]})):"pass"===e.row.status?((0,a.uX)(),(0,a.Wv)(j,{key:1,type:"success"},{default:(0,a.k6)(()=>l[17]||(l[17]=[(0,a.eW)("通过")])),_:1,__:[17]})):"fail"===e.row.status?((0,a.uX)(),(0,a.Wv)(j,{key:2,type:"danger"},{default:(0,a.k6)(()=>l[18]||(l[18]=[(0,a.eW)("失败")])),_:1,__:[18]})):(0,a.Q3)("",!0)]),_:1}),(0,a.bF)(O,{label:"创建时间",align:"center"},{default:(0,a.k6)(e=>[(0,a.eW)((0,u.v_)(new Date(e.row.create_time).toLocaleString()),1)]),_:1}),(0,a.bF)(O,{label:"操作",align:"center",width:"220"},{default:(0,a.k6)(e=>[(0,a.bF)(r,{onClick:l=>te(e.row),size:"small",type:"primary",plain:""},{default:(0,a.k6)(()=>l[19]||(l[19]=[(0,a.eW)("开始盲测")])),_:2,__:[19]},1032,["onClick"]),(0,a.bF)(r,{onClick:l=>ue(e.row),size:"small",type:"success",plain:""},{default:(0,a.k6)(()=>l[20]||(l[20]=[(0,a.eW)("查看结果")])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ye,R.value]]),(0,a.Lk)("div",h,[(0,a.bF)($,{background:"",layout:"total, sizes, prev, pager, next, jumper","page-sizes":[10,20,50,100],onSizeChange:ee,onCurrentChange:le,total:t.value.count,"current-page":t.value.current,"next-text":"下一页","prev-text":"上一页"},null,8,["total","current-page"])])])]),(0,a.bF)(ke,{modelValue:G.value,"onUpdate:modelValue":l[5]||(l[5]=e=>G.value=e),title:"API盲测",width:"60%","before-close":se},{footer:(0,a.k6)(()=>[(0,a.Lk)("span",L,[(0,a.bF)(r,{onClick:se},{default:(0,a.k6)(()=>l[22]||(l[22]=[(0,a.eW)("取消")])),_:1,__:[22]}),(0,a.bF)(r,{type:"primary",onClick:ae},{default:(0,a.k6)(()=>l[23]||(l[23]=[(0,a.eW)("执行测试")])),_:1,__:[23]})])]),default:(0,a.k6)(()=>[Q.value?((0,a.uX)(),(0,a.CE)("div",y,[(0,a.Lk)("div",F,[(0,a.bF)(de,{title:"接口信息",column:2,border:""},{default:(0,a.k6)(()=>[(0,a.bF)(re,{label:"接口名称"},{default:(0,a.k6)(()=>[(0,a.eW)((0,u.v_)(Q.value.name),1)]),_:1}),(0,a.bF)(re,{label:"请求方式"},{default:(0,a.k6)(()=>[(0,a.bF)(j,{type:"GET"===Q.value.method?"primary":"POST"===Q.value.method?"success":"PUT"===Q.value.method?"warning":"DELETE"===Q.value.method?"danger":"info"},{default:(0,a.k6)(()=>[(0,a.eW)((0,u.v_)(Q.value.method),1)]),_:1},8,["type"])]),_:1}),(0,a.bF)(re,{label:"接口地址",span:2},{default:(0,a.k6)(()=>[(0,a.eW)((0,u.v_)(Q.value.url),1)]),_:1})]),_:1})]),(0,a.Lk)("div",w,[(0,a.bF)(_e,{model:q,rules:N,"label-position":"top"},{default:(0,a.k6)(()=>[(0,a.bF)(pe,{type:"border-card"},{default:(0,a.k6)(()=>[(0,a.bF)(ce,{label:"请求参数"},{default:(0,a.k6)(()=>[(0,a.bF)(ie,{label:"请求头"}),(0,a.bF)(ie,{label:"请求体"})]),_:1}),(0,a.bF)(ce,{label:"预期结果"},{default:(0,a.k6)(()=>[(0,a.bF)(ie,{label:"预期响应结果",prop:"expected_result"},{default:(0,a.k6)(()=>[(0,a.bF)(s,{modelValue:q.expected_result,"onUpdate:modelValue":l[4]||(l[4]=e=>q.expected_result=e),type:"textarea",rows:10,placeholder:"请输入预期响应结果（JSON格式）"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])])])):(0,a.Q3)("",!0)]),_:1},8,["modelValue"]),(0,a.bF)(ke,{modelValue:J.value,"onUpdate:modelValue":l[7]||(l[7]=e=>J.value=e),title:"测试结果",width:"70%","before-close":se},{footer:(0,a.k6)(()=>[(0,a.Lk)("span",K,[(0,a.bF)(r,{onClick:se},{default:(0,a.k6)(()=>l[32]||(l[32]=[(0,a.eW)("关闭")])),_:1,__:[32]}),(0,a.bF)(r,{type:"primary",onClick:l[6]||(l[6]=e=>te(Q.value))},{default:(0,a.k6)(()=>l[33]||(l[33]=[(0,a.eW)("重新测试")])),_:1,__:[33]})])]),default:(0,a.k6)(()=>[Q.value?((0,a.uX)(),(0,a.CE)("div",x,[(0,a.Lk)("div",C,[(0,a.bF)(be,{title:"pass"===q.status?"测试通过":"测试失败",type:"pass"===q.status?"success":"error",closable:!1,"show-icon":""},null,8,["title","type"])]),(0,a.Lk)("div",W,[(0,a.bF)(pe,{type:"border-card"},{default:(0,a.k6)(()=>[(0,a.bF)(ce,{label:"结果对比"},{default:(0,a.k6)(()=>[(0,a.bF)(ve,{gutter:20},{default:(0,a.k6)(()=>[(0,a.bF)(me,{span:12},{default:(0,a.k6)(()=>[(0,a.Lk)("div",E,[l[24]||(l[24]=(0,a.Lk)("h3",null,"预期结果",-1)),(0,a.Lk)("pre",T,(0,u.v_)(q.expected_result),1)])]),_:1}),(0,a.bF)(me,{span:12},{default:(0,a.k6)(()=>[(0,a.Lk)("div",I,[l[25]||(l[25]=(0,a.Lk)("h3",null,"实际结果",-1)),(0,a.Lk)("pre",{class:(0,u.C4)(["json-content",{"error-content":"fail"===q.status}])},(0,u.v_)(q.actual_result),3)])]),_:1})]),_:1})]),_:1}),(0,a.bF)(ce,{label:"差异分析"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",V,[l[29]||(l[29]=(0,a.Lk)("h3",null,"差异分析结果",-1)),"pass"===q.status?((0,a.uX)(),(0,a.CE)("div",S,[(0,a.bF)(fe,{description:"没有发现差异"})])):((0,a.uX)(),(0,a.CE)("div",X,[(0,a.bF)(be,{title:"发现差异",type:"warning",closable:!1,"show-icon":""}),(0,a.Lk)("div",P,[(0,a.bF)(B,{data:[{key:"code",expected:"200",actual:"400",status:"different"},{key:"message",expected:"成功",actual:"参数错误",status:"different"},{key:"data",expected:"对象",actual:"不存在",status:"missing"}],border:""},{default:(0,a.k6)(()=>[(0,a.bF)(O,{label:"字段",prop:"key"}),(0,a.bF)(O,{label:"预期值",prop:"expected"}),(0,a.bF)(O,{label:"实际值",prop:"actual"}),(0,a.bF)(O,{label:"状态",prop:"status"},{default:(0,a.k6)(e=>["different"===e.row.status?((0,a.uX)(),(0,a.Wv)(j,{key:0,type:"danger"},{default:(0,a.k6)(()=>l[26]||(l[26]=[(0,a.eW)("不一致")])),_:1,__:[26]})):"missing"===e.row.status?((0,a.uX)(),(0,a.Wv)(j,{key:1,type:"warning"},{default:(0,a.k6)(()=>l[27]||(l[27]=[(0,a.eW)("缺失")])),_:1,__:[27]})):((0,a.uX)(),(0,a.Wv)(j,{key:2,type:"success"},{default:(0,a.k6)(()=>l[28]||(l[28]=[(0,a.eW)("一致")])),_:1,__:[28]}))]),_:1})]),_:1})])]))])]),_:1}),(0,a.bF)(ce,{label:"测试日志"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",z,[(0,a.bF)(he,null,{default:(0,a.k6)(()=>[(0,a.bF)(ge,{timestamp:"测试开始",placement:"top",type:"primary"},{default:(0,a.k6)(()=>[l[30]||(l[30]=(0,a.Lk)("p",null,"开始执行API盲测",-1)),(0,a.Lk)("p",U,(0,u.v_)((new Date).toLocaleString()),1)]),_:1,__:[30]}),(0,a.bF)(ge,{timestamp:"发送请求",placement:"top",type:"info"},{default:(0,a.k6)(()=>[l[31]||(l[31]=(0,a.Lk)("p",null,"向服务器发送API请求",-1)),(0,a.Lk)("p",null,"URL: "+(0,u.v_)(Q.value.url),1),(0,a.Lk)("p",null,"Method: "+(0,u.v_)(Q.value.method),1)]),_:1,__:[31]}),(0,a.bF)(ge,{timestamp:"pass"===q.status?"测试通过":"测试失败",placement:"top",type:"pass"===q.status?"success":"danger"},{default:(0,a.k6)(()=>[(0,a.Lk)("p",null,(0,u.v_)("pass"===q.status?"响应结果与预期一致":"响应结果与预期不一致"),1),(0,a.Lk)("p",D,(0,u.v_)((new Date).toLocaleString()),1)]),_:1},8,["timestamp","type"])]),_:1})])]),_:1})]),_:1})])])):(0,a.Q3)("",!0)]),_:1},8,["modelValue"])],64)}}},A=t(71241);const Q=(0,A.A)(R,[["__scopeId","data-v-19d702e0"]]);var G=Q}}]);
//# sourceMappingURL=719.3b0fe469.js.map