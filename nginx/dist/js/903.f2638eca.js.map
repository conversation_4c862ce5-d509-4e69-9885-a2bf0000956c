{"version": 3, "file": "js/903.f2638eca.js", "mappings": "83CAiYA,MAAM,MAAEA,IAAUC,EAAAA,EAAAA,OAAwB,CAAED,MAAO,MAK7CE,GAAQC,EAAAA,EAAAA,MACRC,GAAUC,EAAAA,EAAAA,IAAS,IAAMH,EAAMI,MAAMC,KAGrCC,IAAOC,EAAAA,EAAAA,IAAI,IACXC,IAAcD,EAAAA,EAAAA,IAAI,MAClBE,IAAUF,EAAAA,EAAAA,IAAI,MACdG,IAAeH,EAAAA,EAAAA,IAAI,IACnBI,IAAcJ,EAAAA,EAAAA,IAAI,IAClBK,IAAgBL,EAAAA,EAAAA,IAAI,OACpBM,IAAeN,EAAAA,EAAAA,KAAI,GACnBO,IAAmBP,EAAAA,EAAAA,KAAI,GACvBQ,IAAsBR,EAAAA,EAAAA,KAAI,GAC1BS,IAAgBT,EAAAA,EAAAA,KAAI,GACpBU,IAAYV,EAAAA,EAAAA,IAAI,MAChBW,IAAYX,EAAAA,EAAAA,IAAI,MAGhBY,IAAcZ,EAAAA,EAAAA,IAAI,GAClBa,IAAWb,EAAAA,EAAAA,IAAI,IAGfc,GAAa,CACjB,MAAO,CAAEC,KAAM,SAAUC,KAAM,UAAWC,MAAO,WACjD,MAAO,CAAEF,KAAM,UAAWC,KAAM,UAAWC,MAAO,eAClD,OAAQ,CAAEF,KAAM,UAAWC,KAAM,cAAeC,MAAO,aACvD,OAAQ,CAAEF,KAAM,OAAQC,KAAM,SAAUC,MAAO,eAC/C,MAAO,CAAEF,KAAM,OAAQC,KAAM,cAAeC,MAAO,WAI/CC,GAAgB,CACpB,CAAEC,MAAO,MAAOC,MAAO,MAAOJ,KAAM,WACpC,CAAEG,MAAO,MAAOC,MAAO,MAAOJ,KAAM,WACpC,CAAEG,MAAO,OAAQC,MAAO,OAAQJ,KAAM,eACtC,CAAEG,MAAO,OAAQC,MAAO,OAAQJ,KAAM,UACtC,CAAEG,MAAO,MAAOC,MAAO,MAAOJ,KAAM,gBAIhCK,IAAarB,EAAAA,EAAAA,IAAI,CACrBsB,GAAI,GACJC,OAAQ,GACRC,OAAQ,KAIJC,IAAe7B,EAAAA,EAAAA,IAAS,KACrB,CACL8B,QAAS3B,GAAKqB,MAAMO,OAAOC,GAAsB,QAAfA,EAAIL,QACtCM,WAAY9B,GAAKqB,MAAMO,OAAOC,GAAsB,QAAfA,EAAIL,QACzCO,UAAW/B,GAAKqB,MAAMO,OAAOC,GAAsB,SAAfA,EAAIL,QACxCQ,YAAahC,GAAKqB,MAAMO,OAAOC,GAAsB,SAAfA,EAAIL,QAC1CS,OAAQjC,GAAKqB,MAAMO,OAAOC,GAAsB,QAAfA,EAAIL,WAInCU,IAAYrC,EAAAA,EAAAA,IAAS,KAClB,CACLsC,MAAOnC,GAAKqB,MAAMe,OAClBT,QAASD,GAAaL,MAAMM,QAAQS,OACpCN,WAAYJ,GAAaL,MAAMS,WAAWM,OAC1CL,UAAWL,GAAaL,MAAMU,UAAUK,OACxCJ,YAAaN,GAAaL,MAAMW,YAAYI,OAC5CH,OAAQP,GAAaL,MAAMY,OAAOG,UAKhCC,IAAYxC,EAAAA,EAAAA,IAAS,IAAM,CAC/B,CAAEmB,KAAM,QAASC,KAAM,UAAWI,MAAOa,GAAUb,MAAMc,MAAOf,MAAO,SACvE,CAAEJ,KAAM,UAAWC,KAAM,UAAWI,MAAOa,GAAUb,MAAMM,QAASP,MAAO,OAC3E,CAAEJ,KAAM,cAAeC,KAAM,UAAWI,MAAOa,GAAUb,MAAMS,WAAYV,MAAO,OAClF,CAAEJ,KAAM,YAAaC,KAAM,cAAeI,MAAOa,GAAUb,MAAMU,UAAWX,MAAO,OACnF,CAAEJ,KAAM,cAAeC,KAAM,SAAUI,MAAOa,GAAUb,MAAMW,YAAaZ,MAAO,QAClF,CAAEJ,KAAM,SAAUC,KAAM,cAAeI,MAAOa,GAAUb,MAAMY,OAAQb,MAAO,SAIzEkB,IAAqBzC,EAAAA,EAAAA,IAAS,KAClC,IAAKQ,GAAYgB,MAAO,OAAOjB,GAAaiB,MAE5C,MAAMkB,EAAQlC,GAAYgB,MAAMmB,cAChC,OAAOpC,GAAaiB,MAAMO,OAAOC,GAC/BA,EAAIY,KAAKD,cAAcE,SAASH,IAChCV,EAAIc,cAAcH,cAAcE,SAASH,IACzCK,OAAOf,EAAIN,IAAImB,SAASH,MAKtBM,IAAchD,EAAAA,EAAAA,IAAS,KAC3B,MAAMiD,GAAcjC,GAAYQ,MAAQ,GAAKP,GAASO,MAChD0B,EAAWD,EAAahC,GAASO,MACvC,OAAOiB,GAAmBjB,MAAM2B,MAAMF,EAAYC,KAI9CE,GAAYC,UAChB,GAAKtD,EAAQyB,OAAOE,GAApB,CAEAhB,GAAac,OAAQ,EACrB,IACE,IAAK7B,IAAUA,EAAM2D,KAEnB,YADAC,EAAAA,GAAUC,MAAM,aAIlB,MAAMC,QAAiB9D,EAAM2D,KAAKI,QAAQ,CACxC3D,QAASA,EAAQyB,MAAME,KAGD,MAApB+B,EAAS9B,SACXxB,GAAKqB,MAAQiC,EAASE,KACtBC,GAAWnD,GAAce,OAE7B,CAAE,MAAOgC,GACPD,EAAAA,GAAUC,MAAM,aAChBK,QAAQL,MAAM,wBAAyBA,EACzC,CAAE,QACA9C,GAAac,OAAQ,CACvB,CAtB8B,GAyB1BoC,GAAc7B,IAIlB,OAHAtB,GAAce,MAAQO,EACtBf,GAAYQ,MAAQ,EAEZO,GACN,IAAK,UACHxB,GAAaiB,MAAQK,GAAaL,MAAMM,QACxC,MACF,IAAK,aACHvB,GAAaiB,MAAQK,GAAaL,MAAMS,WACxC,MACF,IAAK,YACH1B,GAAaiB,MAAQK,GAAaL,MAAMU,UACxC,MACF,IAAK,cACH3B,GAAaiB,MAAQK,GAAaL,MAAMW,YACxC,MACF,IAAK,SACH5B,GAAaiB,MAAQK,GAAaL,MAAMY,OACxC,MACF,QACE7B,GAAaiB,MAAQrB,GAAKqB,QAI1BsC,GAAcT,UAClBhD,GAAYmB,MAAQQ,EACpBrB,GAAiBa,OAAQ,EAEzB,IACE,IAAK7B,IAAUA,EAAM2D,KAAM,OAE3B,MAAMG,QAAiB9D,EAAM2D,KAAKS,WAAW,CAAE/B,IAAKA,EAAIN,KAChC,MAApB+B,EAAS9B,QAAkB8B,EAASE,KAAKpB,OAAS,EACpDjC,GAAQkB,MAAQiC,EAASE,KAEzBrD,GAAQkB,MAAQ,EAEpB,CAAE,MAAOgC,GACPD,EAAAA,GAAUC,MAAM,eAChBK,QAAQL,MAAM,4BAA6BA,GAC3ClD,GAAQkB,MAAQ,EAClB,GAGIwC,GAAoBhC,IACxBP,GAAWD,MAAQ,CACjBE,GAAIM,EAAIN,GACRC,OAAQK,EAAIL,OACZC,OAAQ,IAEVhB,GAAoBY,OAAQ,GAGxByC,GAAYZ,UAChB,GAAK5B,GAAWD,MAAME,GAEtB,GAAKD,GAAWD,MAAMI,OAAOsC,OAA7B,CAKArD,GAAcW,OAAQ,EACtB,IACE,IAAK7B,IAAUA,EAAM2D,KAAM,OAE3B,MAAMG,QAAiB9D,EAAM2D,KAAKW,UAAUxC,GAAWD,MAAME,GAAID,GAAWD,OAE5E,GAAwB,MAApBiC,EAAS9B,SACX4B,EAAAA,GAAUY,QAAQ,CAChBC,QAAS,YACTjD,KAAM,UACNkD,SAAU,MAEZzD,GAAoBY,OAAQ,QAGtB4B,KAGFzC,GAAiBa,OAASnB,GAAYmB,OAAO,CAC/C,MAAM8C,QAAa3E,EAAM2D,KAAKS,WAAW,CAAE/B,IAAK3B,GAAYmB,MAAME,KAClE,GAAoB,MAAhB4C,EAAK3C,OAAgB,CACvBrB,GAAQkB,MAAQ8C,EAAKX,KAGrB,MAAMY,EAAapE,GAAKqB,MAAMgD,KAAKC,GAAKA,EAAE/C,KAAOrB,GAAYmB,MAAME,IAC/D6C,IACFlE,GAAYmB,MAAQ+C,EAExB,CACF,CAEJ,CAAE,MAAOf,GACPD,EAAAA,GAAUC,MAAM,aAChBK,QAAQL,MAAM,wBAAyBA,EACzC,CAAE,QACA3C,GAAcW,OAAQ,CACxB,CAtCA,MAFE+B,EAAAA,GAAUmB,QAAQ,YA2ChBC,GAAYtB,UAChB3C,GAAac,OAAQ,EACrB,IACE,IAAK7B,IAAUA,EAAM2D,KAAM,OAE3B,MAAMG,QAAiB9D,EAAM2D,KAAKqB,UAAUjD,GAEpB,MAApB+B,EAAS9B,SACX4B,EAAAA,GAAUY,QAAQ,CAChBC,QAAS,OACTjD,KAAM,UACNkD,SAAU,YAENjB,KAGF/C,GAAYmB,OAASnB,GAAYmB,MAAME,KAAOA,IAChDf,GAAiBa,OAAQ,GAG/B,CAAE,MAAOgC,GACPD,EAAAA,GAAUC,MAAM,WAChBK,QAAQL,MAAM,wBAAyBA,EACzC,CAAE,QACA9C,GAAac,OAAQ,CACvB,GAGIoD,GAAeA,KACnB,IAAK9D,GAAUU,QAAUT,GAAUS,MAAO,OAC1C,IAAK7B,IAAUA,EAAMkF,OAAQ,OAG7B,MAAMC,EAAY,CAChB3E,GAAKqB,MAAMe,OACXV,GAAaL,MAAMU,UAAUK,OAC7BV,GAAaL,MAAMS,WAAWM,OAC9BV,GAAaL,MAAMM,QAAQS,OAC3BV,GAAaL,MAAMW,YAAYI,OAC/BV,GAAaL,MAAMY,OAAOG,QAGtBwC,EAAc,CAAC,QAAS,OAAQ,MAAO,MAAO,OAAQ,OAG5DpF,EAAMkF,OAAOG,OAAOlE,GAAUU,MAAOsD,EAAWC,GAGhDpF,EAAMkF,OAAOI,OAAOlE,GAAUS,MAAO,CACnC,CAAEA,MAAOK,GAAaL,MAAMU,UAAUK,OAAQ2C,KAAM,QACpD,CAAE1D,MAAOK,GAAaL,MAAMS,WAAWM,OAAQ2C,KAAM,OACrD,CAAE1D,MAAOK,GAAaL,MAAMM,QAAQS,OAAQ2C,KAAM,OAClD,CAAE1D,MAAOK,GAAaL,MAAMW,YAAYI,OAAQ2C,KAAM,QACtD,CAAE1D,MAAOK,GAAaL,MAAMY,OAAOG,OAAQ2C,KAAM,UAK/CC,GAAoBC,IACxBnE,GAASO,MAAQ4D,GAGbC,GAAuBC,IAC3BtE,GAAYQ,MAAQ8D,GAIhBC,GAAiB5D,GACdT,GAAWS,IAASR,MAAQ,OAO/BqE,GAAkB7D,GACfT,GAAWS,IAASN,OAAS,UAGhCoE,GAAmBC,GACnBA,EAAO7C,SAAS,QAAgB,UAChC6C,EAAO7C,SAAS,OAAe,UAC/B6C,EAAO7C,SAAS,OAAe,SAC/B6C,EAAO7C,SAAS,MAAc,OAC3B,UAGH8C,GAAmBD,GACnBA,EAAO7C,SAAS,QAAgB,cAChC6C,EAAO7C,SAAS,OAAe,UAC/B6C,EAAO7C,SAAS,OAAe,UAC/B6C,EAAO7C,SAAS,MAAc,cAC9B6C,EAAO7C,SAAS,QAAgB,SAC7B,aAGH+C,GAAcA,EAAGC,SACd,sBAAsBL,GAAeK,EAAIlE,WAIlDmE,EAAAA,EAAAA,IAAUzC,gBACFD,MACN2C,EAAAA,EAAAA,IAAS,KACPnB,UAKJoB,EAAAA,EAAAA,IAAM,IAAM7F,GAAKqB,MAAO,MACtBuE,EAAAA,EAAAA,IAAS,KACPnB,QAED,CAAEqB,MAAM,KAGXD,EAAAA,EAAAA,IAAMxF,GAAa,KACjBQ,GAAYQ,MAAQ,IAItB,MAAM0E,GAASC,GACRxG,GAAUA,EAAMyG,OACdzG,EAAMyG,OAAOF,MAAMC,GADUA,EAKhCE,GAASF,GACRxG,GAAUA,EAAMyG,OACdzG,EAAMyG,OAAOC,MAAQ1G,EAAMyG,OAAOC,MAAMF,GAAQxG,EAAMyG,OAAOF,MAAMC,GADtCA,E,quBAtuBpCG,EAAAA,EAAAA,IAiXM,MAjXNC,EAiXM,EAhXJC,EAAAA,EAAAA,IA+WeC,GAAA,CA/WDC,OAAO,sBAAoB,C,iBACvC,IAwNM,EAxNNC,EAAAA,EAAAA,IAwNM,MAxNNC,EAwNM,EAtNJD,EAAAA,EAAAA,IAYM,MAZNE,EAYM,C,eAXJF,EAAAA,EAAAA,IAEM,OAFDtF,MAAM,eAAa,EACtBsF,EAAAA,EAAAA,IAAkC,MAA9BtF,MAAM,cAAa,Y,KAEzBsF,EAAAA,EAAAA,IAOM,MAPNG,EAOM,EANJN,EAAAA,EAAAA,IAKWO,EAAA,CALAvF,MAAOa,GAAAb,MAAUM,QAAUkF,OAA8B,IAAtB3E,GAAAb,MAAUM,QAAeT,MAAM,iB,kBAC3E,IAGY,EAHZmF,EAAAA,EAAAA,IAGYS,EAAA,CAHD9F,KAAK,SAASiE,KAAK,QAAQ/D,MAAM,c,kBAC1C,IAA8B,EAA9BmF,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAC,EAAAA,Y,OACpBT,EAAAA,EAAAA,IAAwC,YAAlC,QAAIU,EAAAA,EAAAA,IAAGhF,GAAAb,MAAUM,SAAO,K,wCAOtC6E,EAAAA,EAAAA,IAgBM,MAhBNW,EAgBM,EAfJd,EAAAA,EAAAA,IAcSe,GAAA,CAdAC,OAAQ,IAAE,C,iBACC,IAAkC,G,aAApDlB,EAAAA,EAAAA,IAYSmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAZiClF,GAAAhB,MAAS,CAAzBmG,EAAMC,M,WAAhCC,EAAAA,EAAAA,IAYSC,EAAA,CAZAC,KAAM,EAAuCC,IAAKJ,G,kBACzD,IAUM,EAVNjB,EAAAA,EAAAA,IAUM,OAVDtF,OAAK4G,EAAAA,EAAAA,IAAA,CAAC,YAAW,aAAsBN,EAAKxG,U,EAC/CwF,EAAAA,EAAAA,IAIM,MAJNuB,EAIM,EAHJ1B,EAAAA,EAAAA,IAEUU,EAAA,M,iBADR,IAA6B,G,WAA7BW,EAAAA,EAAAA,KAA6BM,EAAAA,EAAAA,IAAbR,EAAKvG,U,cAGzBuF,EAAAA,EAAAA,IAGM,MAHNyB,EAGM,EAFJzB,EAAAA,EAAAA,IAA8C,MAA9C0B,GAA8ChB,EAAAA,EAAAA,IAAnBM,EAAKnG,OAAK,IACrCmF,EAAAA,EAAAA,IAA8C,MAA9C2B,GAA8CjB,EAAAA,EAAAA,IAAnBM,EAAKpG,OAAK,M,mCAQ/CoF,EAAAA,EAAAA,IA6BM,MA7BN4B,EA6BM,EA5BJ/B,EAAAA,EAAAA,IA2BSe,GAAA,CA3BAC,OAAQ,IAAE,C,iBACjB,IAYS,EAZThB,EAAAA,EAAAA,IAYSsB,EAAA,CAZAC,KAAM,IAAE,C,iBACf,IAUM,EAVNpB,EAAAA,EAAAA,IAUM,MAVN6B,EAUM,EATJ7B,EAAAA,EAAAA,IAOM,MAPN8B,EAOM,C,eANJ9B,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAIM,MAJN+B,EAIM,EAHJlC,EAAAA,EAAAA,IAEYS,EAAA,CAFD7B,KAAK,QAAQuD,KAAA,I,kBACtB,IAA8B,EAA9BnC,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAyB,EAAAA,Y,mBAI1BjC,EAAAA,EAAAA,IAAiD,OAA5CtF,MAAM,gB,QAAoB,YAAJjB,IAAIU,I,qBAGnC0F,EAAAA,EAAAA,IAYSsB,EAAA,CAZAC,KAAM,IAAE,C,iBACf,IAUM,EAVNpB,EAAAA,EAAAA,IAUM,MAVNkC,EAUM,EATJlC,EAAAA,EAAAA,IAOM,MAPNmC,EAOM,C,eANJnC,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAIM,MAJNoC,EAIM,EAHJvC,EAAAA,EAAAA,IAEYS,EAAA,CAFD7B,KAAK,QAAQuD,KAAA,I,kBACtB,IAA8B,EAA9BnC,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAyB,EAAAA,Y,mBAI1BjC,EAAAA,EAAAA,IAAiD,OAA5CtF,MAAM,gB,QAAoB,YAAJjB,IAAIW,I,+BAOvC4F,EAAAA,EAAAA,IAmJM,MAnJNqC,EAmJM,EAlJJrC,EAAAA,EAAAA,IA2BM,MA3BNsC,EA2BM,EA1BJtC,EAAAA,EAAAA,IAGM,MAHNuC,EAGM,C,eAFJvC,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAAsD,OAAtDwC,EAA0B,MAAE9B,EAAAA,EAAAA,IAAGlH,GAAAqB,MAAKe,QAAS,KAAE,MAGjDoE,EAAAA,EAAAA,IAoBM,MApBNyC,EAoBM,EAnBJ5C,EAAAA,EAAAA,IAOiB6C,GAAA,C,WAPQ5I,GAAAe,M,qCAAAf,GAAae,MAAA8H,GAAElE,KAAK,QAASmE,SAAMC,EAAA,KAAAA,EAAA,GAAGC,GAAQ7F,GAAW6F,K,kBAChF,IAAiD,EAAjDjD,EAAAA,EAAAA,IAAiDkD,GAAA,CAAhCnI,MAAM,OAAK,C,iBAAC,IAAEiI,EAAA,MAAAA,EAAA,M,QAAF,S,eAC7BhD,EAAAA,EAAAA,IAAsDkD,GAAA,CAArCnI,MAAM,WAAS,C,iBAAC,IAAGiI,EAAA,MAAAA,EAAA,M,QAAH,U,eACjChD,EAAAA,EAAAA,IAAyDkD,GAAA,CAAxCnI,MAAM,cAAY,C,iBAAC,IAAGiI,EAAA,MAAAA,EAAA,M,QAAH,U,eACpChD,EAAAA,EAAAA,IAAwDkD,GAAA,CAAvCnI,MAAM,aAAW,C,iBAAC,IAAGiI,EAAA,MAAAA,EAAA,M,QAAH,U,eACnChD,EAAAA,EAAAA,IAA2DkD,GAAA,CAA1CnI,MAAM,eAAa,C,iBAAC,IAAIiI,EAAA,MAAAA,EAAA,M,QAAJ,W,eACrChD,EAAAA,EAAAA,IAAqDkD,GAAA,CAApCnI,MAAM,UAAQ,C,iBAAC,IAAGiI,EAAA,MAAAA,EAAA,M,QAAH,U,wCAGlChD,EAAAA,EAAAA,IASWmD,GAAA,C,WARAnJ,GAAAgB,M,qCAAAhB,GAAWgB,MAAA8H,GACpBM,YAAY,WACZvI,MAAM,eACNwI,UAAA,I,CAEWC,QAAMC,EAAAA,EAAAA,IACf,IAA6B,EAA7BvD,EAAAA,EAAAA,IAA6BU,EAAA,M,iBAApB,IAAU,EAAVV,EAAAA,EAAAA,KAAUW,EAAAA,EAAAA,IAAA6C,EAAAA,W,yDAM3BnC,EAAAA,EAAAA,IAwGWoC,GAAA,CAvGRtG,KAAMX,GAAAxB,MACP0I,MAAA,eACA,UAAQ,KAEP,iBAAgBtE,GAChBuE,WAAWrG,GACZzC,MAAM,a,kBAEN,IAQkB,EARlBmF,EAAAA,EAAAA,IAQkB4D,GAAA,CARDjJ,KAAK,UAAQ,CACjBkJ,SAAON,EAAAA,EAAAA,IAAEO,GAAK,EACvB3D,EAAAA,EAAAA,IAIM,MAJN4D,EAIM,EAHJ5D,EAAAA,EAAAA,IAAmD,U,eAAhDA,EAAAA,EAAAA,IAAuB,cAAf,UAAM,K,QAAS,KAACU,EAAAA,EAAAA,IAAGiD,EAAMzE,IAAIjD,MAAI,MAC5C+D,EAAAA,EAAAA,IAA2D,U,eAAxDA,EAAAA,EAAAA,IAAsB,cAAd,SAAK,K,QAAS,KAACU,EAAAA,EAAAA,IAAGiD,EAAMzE,IAAI/C,eAAa,MACpD6D,EAAAA,EAAAA,IAAgE,U,eAA7DA,EAAAA,EAAAA,IAAsB,cAAd,SAAK,K,QAAS,KAACU,EAAAA,EAAAA,IAAGnB,GAAMoE,EAAMzE,IAAI2E,cAAW,S,OAK9DhE,EAAAA,EAAAA,IAIkB4D,GAAA,CAJD7I,MAAM,GAAGkJ,MAAM,M,CACnBJ,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvB/D,EAAAA,EAAAA,IAA4F,OAAvFtF,OAAK4G,EAAAA,EAAAA,IAAA,CAAC,iBAAgB,cAAyBzC,GAAekF,EAAM7E,IAAIlE,W,iBAIjF6E,EAAAA,EAAAA,IAA4C4D,GAAA,CAA3BO,KAAK,KAAKpJ,MAAM,UAEjCiF,EAAAA,EAAAA,IAIkB4D,GAAA,CAJDO,KAAK,OAAOpJ,MAAM,QAAQ,4B,CAC9B8I,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvB/D,EAAAA,EAAAA,IAAiD,MAAjDiE,GAAiDvD,EAAAA,EAAAA,IAAvBqD,EAAM7E,IAAIjD,MAAI,K,OAI5C4D,EAAAA,EAAAA,IAA2F4D,GAAA,CAA1EO,KAAK,gBAAgBpJ,MAAM,OAAO,YAAU,MAAM,8BAEnEiF,EAAAA,EAAAA,IAOkB4D,GAAA,CAPD7I,MAAM,QAAM,CAChB8I,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvB/D,EAAAA,EAAAA,IAGM,MAHNkE,EAGM,EAFJrE,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQsE,M,OACjBnE,EAAAA,EAAAA,IAA+C,aAAAU,EAAAA,EAAAA,IAAtCnB,GAAMwE,EAAM7E,IAAI2E,cAAW,O,OAK1ChE,EAAAA,EAAAA,IAWkB4D,GAAA,CAXD7I,MAAM,KAAKkJ,MAAM,MAAMM,MAAM,U,CACjCV,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvBlE,EAAAA,EAAAA,IAOSwE,GAAA,CANN7J,KAAMoE,GAAcmF,EAAM7E,IAAIlE,QAC/BsJ,OAAO,QACP5J,OAAK4G,EAAAA,EAAAA,IAAA,CAAC,aAAY,UACEzC,GAAekF,EAAM7E,IAAIlE,W,kBAE7C,IAAmC,EAAnCgF,EAAAA,EAAAA,IAAmC,aAAAU,EAAAA,EAAAA,IAA1BqD,EAAM7E,IAAIlE,QAAM,K,qCAK/B6E,EAAAA,EAAAA,IA8CkB4D,GAAA,CA9CDK,MAAM,MAAMM,MAAM,SAASG,MAAM,QAAQ3J,MAAM,M,CACnD8I,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvB/D,EAAAA,EAAAA,IA0CM,MA1CNwE,EA0CM,EAzCJ3E,EAAAA,EAAAA,IASa4E,GAAA,CATDC,QAAQ,OAAOC,UAAU,O,kBACnC,IAOY,EAPZ9E,EAAAA,EAAAA,IAOYS,EAAA,CANVsE,OAAA,GACApK,KAAK,UACLqK,MAAA,GACCC,SAAKC,EAAAA,EAAAA,IAAApC,GAAOxF,GAAY4G,EAAM7E,KAAG,W,kBAElC,IAA2B,EAA3BW,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,KAAQW,EAAAA,EAAAA,IAAAwE,EAAAA,S,6CAIrBnF,EAAAA,EAAAA,IASa4E,GAAA,CATDC,QAAQ,OAAOC,UAAU,O,kBACnC,IAOY,EAPZ9E,EAAAA,EAAAA,IAOYS,EAAA,CANVsE,OAAA,GACApK,KAAK,UACLqK,MAAA,GACCC,SAAKC,EAAAA,EAAAA,IAAApC,GAAOtF,GAAiB0G,EAAM7E,KAAG,W,kBAEvC,IAA2B,EAA3BW,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,KAAQW,EAAAA,EAAAA,IAAAyE,EAAAA,S,6CAIrBpF,EAAAA,EAAAA,IAkBa4E,GAAA,CAlBDC,QAAQ,QAAQC,UAAU,O,kBACpC,IAgBgB,EAhBhB9E,EAAAA,EAAAA,IAgBgBqF,GAAA,CAfdC,MAAM,cACN,sBAAoB,KACpB,qBAAmB,KAClBC,UAAOzC,GAAE3E,GAAU+F,EAAM7E,IAAInE,K,CAEnBsK,WAASjC,EAAAA,EAAAA,IAClB,IAOY,EAPZvD,EAAAA,EAAAA,IAOYS,EAAA,CANVsE,OAAA,GACApK,KAAK,SACLqK,MAAA,GACCC,QAAKjC,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBAEX,IAA6B,EAA7BlF,EAAAA,EAAAA,IAA6BU,EAAA,M,iBAApB,IAAU,EAAVV,EAAAA,EAAAA,KAAUW,EAAAA,EAAAA,IAAA8E,EAAAA,W,yFA5FtBvL,GAAAc,UAsGbmF,EAAAA,EAAAA,IAUM,MAVNuF,EAUM,EATJ1F,EAAAA,EAAAA,IAQE2F,GAAA,CAPQ,eAAcnL,GAAAQ,M,sCAAAR,GAAWQ,MAAA8H,GACzB,YAAWrI,GAAAO,M,mCAAAP,GAAQO,MAAA8H,GAC1B,aAAY,CAAC,GAAI,GAAI,GAAI,IAC1B8C,OAAO,0CACN9J,MAAO/B,GAAAiB,MAAae,OACpB8J,aAAalH,GACbmH,gBAAgBjH,I,oDAOzBmB,EAAAA,EAAAA,IAmGY+F,GAAA,C,WAlGD5L,GAAAa,M,qCAAAb,GAAgBa,MAAA8H,GACzBkD,UAAU,MACVpH,KAAK,MACL,sBACA,eAAa,qB,CAEFqH,QAAM1C,EAAAA,EAAAA,IACf,IAUM,EAVNpD,EAAAA,EAAAA,IAUM,MAVN+F,EAUM,C,eATJ/F,EAAAA,EAAAA,IAAmC,MAA/BtF,MAAM,gBAAe,SAAK,IAEtBhB,GAAAmB,Q,WADRqG,EAAAA,EAAAA,IAOSmD,GAAA,C,MALN7J,KAAMoE,GAAclF,GAAAmB,MAAYG,QACjCsJ,OAAO,OACP5J,MAAM,qB,kBAEN,IAAwB,E,iBAArBhB,GAAAmB,MAAYG,QAAM,K,uDAK3B,IA6Ee,EA7Ef6E,EAAAA,EAAAA,IA6EeC,GAAA,CA7EDC,OAAO,uBAAqB,C,iBACxC,IA2EM,CA3EKrG,GAAAmB,Q,WAAX8E,EAAAA,EAAAA,IA2EM,MA3ENqG,EA2EM,EA1EJnG,EAAAA,EAAAA,IAgBUoG,GAAA,CAhBDvL,MAAM,wBAAwBwL,OAAO,S,CACjCJ,QAAM1C,EAAAA,EAAAA,IACf,IAEM,EAFNpD,EAAAA,EAAAA,IAEM,MAFNmG,EAEM,EADJnG,EAAAA,EAAAA,IAA+C,YAA3CH,EAAAA,EAAAA,IAAiCU,EAAA,M,iBAAxB,IAAc,EAAdV,EAAAA,EAAAA,KAAcW,EAAAA,EAAAA,IAAA4F,EAAAA,e,6BAAU,gB,iBAIzC,IAQkB,EARlBvG,EAAAA,EAAAA,IAQkBwG,GAAA,CARAC,OAAQ,EAAGC,OAAA,I,kBAC3B,IAAgF,EAAhF1G,EAAAA,EAAAA,IAAgF2G,GAAA,CAA1D5L,MAAM,UAAQ,C,iBAAC,IAAoB,E,iBAAjBlB,GAAAmB,MAAYE,IAAE,K,OACtD8E,EAAAA,EAAAA,IAA8D2G,GAAA,CAAxC5L,MAAM,OAAK,C,iBAAC,IAAKiI,EAAA,MAAAA,EAAA,M,QAAL,Y,eAClChD,EAAAA,EAAAA,IAAyF2G,GAAA,CAAnE5L,MAAM,QAAM,C,iBAAC,IAA+B,E,iBAA5BlB,GAAAmB,MAAYsB,eAAa,K,OAC/D0D,EAAAA,EAAAA,IAA8F2G,GAAA,CAAxE5L,MAAM,QAAM,C,iBAAC,IAAoC,E,iBAAjC2E,GAAM7F,GAAAmB,MAAYgJ,cAAW,K,OACnEhE,EAAAA,EAAAA,IAEuB2G,GAAA,CAFD5L,MAAM,QAASwG,KAAM,G,kBACzC,IAAyD,EAAzDpB,EAAAA,EAAAA,IAAyD,MAAzDyG,GAAyD/F,EAAAA,EAAAA,IAAzBhH,GAAAmB,MAAYoB,MAAI,K,uBAKtD4D,EAAAA,EAAAA,IAOUoG,GAAA,CAPDvL,MAAM,6BAA6BwL,OAAO,S,CACtCJ,QAAM1C,EAAAA,EAAAA,IACf,IAEM,EAFNpD,EAAAA,EAAAA,IAEM,MAFN0G,EAEM,EADJ1G,EAAAA,EAAAA,IAA8C,YAA1CH,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAmG,EAAAA,Y,6BAAU,kB,iBAGtC,IAA6D,EAA7D9G,EAAAA,EAAAA,IAA6D+G,EAAAA,EAAA,CAApDC,OAAQnN,GAAAmB,MAAYiM,KAAOC,SAAS,G,2BAGhCpN,GAAAkB,OAAWlB,GAAAkB,MAAQe,OAAS,I,WAA3CsF,EAAAA,EAAAA,IAqCU+E,GAAA,C,MArCoCvL,MAAM,4BAA4BwL,OAAO,S,CAC1EJ,QAAM1C,EAAAA,EAAAA,IACf,IAEM,EAFNpD,EAAAA,EAAAA,IAEM,MAFNgH,EAEM,EADJhH,EAAAA,EAAAA,IAA4C,YAAxCH,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAmG,EAAAA,Y,6BAAU,gB,iBAItC,IA6Bc,EA7Bd9G,EAAAA,EAAAA,IA6BcoH,GAAA,M,iBA3BV,IAAoC,G,aADtCtH,EAAAA,EAAAA,IA2BmBmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA1BWpH,GAAAkB,MAAO,CAA3BqM,EAAUjG,M,WADpBC,EAAAA,EAAAA,IA2BmBiG,GAAA,CAzBhB9F,IAAKJ,EACLmG,UAAW1H,GAAMwH,EAASrD,aAC1BrJ,KAAMsE,GAAgBoI,EAASnI,QAC/BsI,QAAQ,EACR5I,KAAgB,IAAVwC,EAAc,QAAU,U,kBAE/B,IAkBU,EAlBVpB,EAAAA,EAAAA,IAkBUoG,GAAA,CAlBDvL,MAAM,sBAAoB,C,iBACjC,IAGK,EAHLsF,EAAAA,EAAAA,IAGK,KAHLsH,EAGK,EAFHzH,EAAAA,EAAAA,IAAuEU,EAAA,M,iBAA9D,IAAoD,G,WAApDW,EAAAA,EAAAA,KAAoDM,EAAAA,EAAAA,IAApCxC,GAAgBkI,EAASnI,a,oBAAqB,KACvE2B,EAAAA,EAAAA,IAAGwG,EAASnI,QAAM,KAGXmI,EAASjM,S,WAAlB0E,EAAAA,EAAAA,IAA2E,IAA3E4H,GAA2E7G,EAAAA,EAAAA,IAAtBwG,EAASjM,QAAM,K,gBAEpE+E,EAAAA,EAAAA,IASM,MATNwH,EASM,EARJxH,EAAAA,EAAAA,IAGO,OAHPyH,EAGO,EAFL5H,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,KAAQW,EAAAA,EAAAA,IAAAkH,EAAAA,S,eAAU,KAC3BhH,EAAAA,EAAAA,IAAGwG,EAASS,aAAW,MAEzB3H,EAAAA,EAAAA,IAGO,OAHP4H,EAGO,EAFL/H,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQsE,M,eAAU,KAC3BzD,EAAAA,EAAAA,IAAGnB,GAAM2H,EAASrD,cAAW,S,0FAQzC3C,EAAAA,EAAAA,IAAmE2G,GAAA,C,MAAlDC,YAAY,SAAU,aAAY,QAEnD9H,EAAAA,EAAAA,IAKM,MALN+H,EAKM,EAJJlI,EAAAA,EAAAA,IAA0ES,EAAA,CAA/D7B,KAAK,UAAWqG,QAAKjC,EAAA,KAAAA,EAAA,GAAAF,GAAE3I,GAAAa,OAAmB,I,kBAAO,IAAEgI,EAAA,MAAAA,EAAA,M,QAAF,S,eAC5DhD,EAAAA,EAAAA,IAEYS,EAAA,CAFD7B,KAAK,UAAUjE,KAAK,UAAWsK,QAAKjC,EAAA,KAAAA,EAAA,GAAAF,GAAEtF,GAAiB3D,GAAAmB,S,kBAChE,IAA2B,EAA3BgF,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,KAAQW,EAAAA,EAAAA,IAAAyE,EAAAA,S,6BAAU,a,qEAQrCpF,EAAAA,EAAAA,IA4CYmI,GAAA,C,WA3CD/N,GAAAY,M,uCAAAZ,GAAmBY,MAAA8H,GAC5BwC,MAAM,UACNrB,MAAM,MACN,sBACA,eAAa,iB,CA+BFmE,QAAM7E,EAAAA,EAAAA,IACf,IAKM,EALNpD,EAAAA,EAAAA,IAKM,MALNkI,EAKM,EAJJrI,EAAAA,EAAAA,IAA8DS,EAAA,CAAlDwE,QAAKjC,EAAA,MAAAA,EAAA,IAAAF,GAAE1I,GAAAY,OAAsB,I,kBAAO,IAAEgI,EAAA,MAAAA,EAAA,M,QAAF,S,eAChDhD,EAAAA,EAAAA,IAEYS,EAAA,CAFD9F,KAAK,UAAWsK,QAAOxH,GAAY6K,QAASjO,GAAAW,O,kBACrD,IAA4B,EAA5BgF,EAAAA,EAAAA,IAA4BU,EAAA,M,iBAAnB,IAAS,EAATV,EAAAA,EAAAA,KAASW,EAAAA,EAAAA,IAAA4H,EAAAA,U,6BAAU,a,iDAjClC,IA2BU,EA3BVvI,EAAAA,EAAAA,IA2BUwI,GAAA,CA3BAC,MAAOxN,GAAAD,MAAY,iBAAe,MAAM,iBAAYH,MAAM,e,kBAClE,IAee,EAffmF,EAAAA,EAAAA,IAee0I,GAAA,CAfD3N,MAAM,QAAM,C,iBACxB,IAaM,EAbNoF,EAAAA,EAAAA,IAaM,MAbNwI,EAaM,G,WAZJ7I,EAAAA,EAAAA,IAWMmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAVoBpG,GAAa,CAA7BK,EAAQqG,KADlBrB,EAAAA,EAAAA,IAWM,OATHqB,IAAKA,EACN3G,OAAK4G,EAAAA,EAAAA,IAAA,CAAC,gBAAe,QACDxG,GAAAD,MAAWG,SAAWA,EAAOH,SAChDiK,QAAKnC,GAAE7H,GAAAD,MAAWG,OAASA,EAAOH,O,EAEnCgF,EAAAA,EAAAA,IAEUU,EAAA,CAFD7F,MAAM,eAAa,C,iBAC1B,IAA+B,G,WAA/BwG,EAAAA,EAAAA,KAA+BM,EAAAA,EAAAA,IAAfxG,EAAOP,U,YAEzBuF,EAAAA,EAAAA,IAAkD,MAAlDyI,GAAkD/H,EAAAA,EAAAA,IAArB1F,EAAOJ,OAAK,I,uBAK/CiF,EAAAA,EAAAA,IAQe0I,GAAA,CARD3N,MAAM,QAAM,C,iBACxB,IAME,EANFiF,EAAAA,EAAAA,IAMEmD,GAAA,C,WALSlI,GAAAD,MAAWI,O,qCAAXH,GAAAD,MAAWI,OAAM0H,GAC1BnI,KAAK,WACJkO,SAAU,CAAAC,QAAA,EAAAC,QAAA,GACX3F,YAAY,aACZvI,MAAM,gB,qGC7VpB,MAAMmO,IAA2B,QAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,S,0ICSUnO,MAAM,e,GAGP6I,MAAA,mB,aAsCA7I,MAAM,e,SAM6B6I,MAAA,mB,SAOLA,MAAA,mB,SACKA,MAAA,mB,SAC3BA,MAAA,mB,SAK0BA,MAAA,mB,SAC1BA,MAAA,mB,SASRA,MAAA,0D,GAcI7I,MAAM,iB,0ZArGhBmF,EAAAA,EAAAA,IAsFQiJ,EAAA,CAtFC,cAAY,KAAKvF,MAAA,uBAA2B/I,KAAK,cAAcK,MAAM,KAAK4D,KAAK,Q,kBACzF,IAYgB,CAZkB,OAAfsK,EAAAlC,OAAOrM,O,WAA1B0G,EAAAA,EAAAA,IAYgB8H,EAAA,C,MAZyBpO,MAAM,MAAM2D,KAAK,M,kBACtD,IAUM,CAVKwK,EAAAlC,OAAOoC,kB,WAAlBtJ,EAAAA,EAAAA,IAUM,MAAAC,EAAA,CATOmJ,EAAAlC,OAAOoC,gBAAgB,gBAAgB/M,SAAS,sB,WAA3DyD,EAAAA,EAAAA,IAGM,MAAAM,EAAA,EADJJ,EAAAA,EAAAA,IAA4FqJ,EAAA,CAAnFC,UAAU,E,WAAeJ,EAAAlC,OAAOuC,c,qCAAPL,EAAAlC,OAAOuC,cAAazG,GAAE0G,KAAK,OAAOC,MAAM,U,uCAE5E3J,EAAAA,EAAAA,IAIM,MAAAO,EAAA,EAHJL,EAAAA,EAAAA,IAEeC,EAAA,CAFDC,OAAO,QAAUwJ,QAAK1G,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAA0G,EAA1GlF,EAAAA,EAAAA,IAA0GqJ,EAAA,CAAjGC,UAAU,EAAMK,UAAQT,EAAAlC,OAAOuC,cAAeC,KAAK,OAAOC,MAAM,SAASvJ,OAAO,S,6EAKjE,OAAfgJ,EAAAlC,OAAOrM,O,WAA1B0G,EAAAA,EAAAA,IAWc8H,EAAA,C,MAX2BpO,MAAM,MAAM2D,KAAK,M,kBACtD,IASe,EATfsB,EAAAA,EAAAA,IASeC,EAAA,CATDC,OAAO,QAASwJ,QAAK1G,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAOI,CAP2BgE,EAAAlC,OAAOoC,kB,WAAtCtJ,EAAAA,EAAAA,IAOI,MAPJQ,EAOI,G,aANLR,EAAAA,EAAAA,IAKMmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IALsBgI,EAAAlC,OAAOoC,gBAAe,CAArCpO,EAAOwG,M,WAApB1B,EAAAA,EAAAA,IAKM,aAJLE,EAAAA,EAAAA,IAGSwE,EAAA,CAHDd,MAAA,qBAAyB/I,KAAK,Q,kBACrC,IAAgD,EAAhDwF,EAAAA,EAAAA,IAAgD,IAAhDW,GAAgDD,EAAAA,EAAAA,IAAlBW,EAAM,OAAH,IACjCrB,EAAAA,EAAAA,IAAwB,aAAAU,EAAAA,EAAAA,IAAf7F,GAAK,K,yEAMgB,OAAfkO,EAAAlC,OAAOrM,O,WAA1B0G,EAAAA,EAAAA,IA4Bc8H,EAAA,C,MA5B2BpO,MAAM,OAAO2D,KAAK,M,kBACvD,IA0Be,EA1BfsB,EAAAA,EAAAA,IA0BeC,EAAA,CA1BDC,OAAO,QAASwJ,QAAK1G,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAwBI,CAxBOgE,EAAAlC,OAAO4C,gB,WAAlB9J,EAAAA,EAAAA,IAwBI,MAAA4B,EAAA,EAvBL1B,EAAAA,EAAAA,IAsBc6J,EAAA,C,WAtBQC,EAAAC,Y,qCAAAD,EAAAC,YAAWjH,GAAEjI,MAAM,e,kBACxC,IAMmB,EANnBmF,EAAAA,EAAAA,IAMmBgK,EAAA,CANDtL,KAAK,KAAG,CACd4G,OAAK/B,EAAAA,EAAAA,IACf,IAAcP,EAAA,KAAAA,EAAA,KAAd7C,EAAAA,EAAAA,IAAc,SAAX,WAAO,M,iBAEX,IAA+C,EAA/CA,EAAAA,EAAAA,IAA+C,WAA1C,qBAAiBU,EAAAA,EAAAA,IAAGqI,EAAAlC,OAAOiD,QAAM,IACtC9J,EAAAA,EAAAA,IAAyC,WAApC,kBAAcU,EAAAA,EAAAA,IAAGqI,EAAAlC,OAAOkD,KAAG,K,OAEjClK,EAAAA,EAAAA,IAOmBgK,EAAA,CAPDtL,KAAK,KAAG,CACd4G,OAAK/B,EAAAA,EAAAA,IACf,IAAsBP,EAAA,MAAAA,EAAA,MAAtB7C,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEd,IAA8C,G,aAAnDL,EAAAA,EAAAA,IAEMmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFsBgI,EAAAlC,OAAOmD,gBAAe,CAArCnP,EAAOwG,M,WAApB1B,EAAAA,EAAAA,IAEM,aADLK,EAAAA,EAAAA,IAAsC,aAAAU,EAAAA,EAAAA,IAA7BW,EAAM,MAAQxG,GAAK,O,eAG9BgF,EAAAA,EAAAA,IAKmBgK,EAAA,CALDtL,KAAK,KAAG,CACd4G,OAAK/B,EAAAA,EAAAA,IACf,IAAsBP,EAAA,MAAAA,EAAA,MAAtB7C,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEnB,IAAuC,EAAvCA,EAAAA,EAAAA,IAAuC,aAAAU,EAAAA,EAAAA,IAA9BqI,EAAAlC,OAAO4C,eAAa,K,oFAMjC5J,EAAAA,EAAAA,IAYcmJ,EAAA,CAZDpO,MAAM,MAAI,C,iBACtB,IAUe,EAVfiF,EAAAA,EAAAA,IAUeC,EAAA,CAVDC,OAAO,QAASwJ,QAAK1G,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAQM,EARN/E,EAAAA,EAAAA,IAQM,MARNyB,EAQM,G,aAPL9B,EAAAA,EAAAA,IAMMmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANuBgI,EAAAlC,OAAOoD,SAAQ,CAA/BjJ,EAAMC,M,WAAnBtB,EAAAA,EAAAA,IAMM,YAL8C,UAAZqB,EAAK,K,WAA5CE,EAAAA,EAAAA,IAAmFmD,EAAA,C,MAA3Ed,MAAA,sB,kBAAqD,IAAa,E,iBAAVvC,EAAK,IAAD,K,YACZ,YAAZA,EAAK,K,WAAjDE,EAAAA,EAAAA,IAAwGmD,EAAA,C,MAAhGd,MAAA,qBAA2D/I,KAAK,W,kBAAU,IAAa,E,iBAAVwG,EAAK,IAAD,K,YACjC,UAAZA,EAAK,K,WAAjDE,EAAAA,EAAAA,IAAqGmD,EAAA,C,MAA7Fd,MAAA,qBAAyD/I,KAAK,U,kBAAS,IAAa,E,iBAAVwG,EAAK,IAAD,K,YAC9B,SAAZA,EAAK,K,WAAjDE,EAAAA,EAAAA,IAAqGmD,EAAA,C,MAA7Fd,MAAA,qBAAwD/I,KAAK,W,kBAAU,IAAa,E,iBAAVwG,EAAK,IAAD,K,YAC1D,WAAZA,EAAK,K,WAArBrB,EAAAA,EAAAA,IAAiF,MAAjF+B,GAAiFhB,EAAAA,EAAAA,IAAhBM,EAAK,IAAD,K,4CAKzEnB,EAAAA,EAAAA,IAMcmJ,EAAA,CANDkB,SAAA,IAAQ,CACTtP,OAAKwI,EAAAA,EAAAA,IACf,IAAkG,CAArE,OAAjB2F,EAAAlC,OAAOvN,Q,WAAnBqG,EAAAA,EAAAA,IAAkG,OAAlGgC,GAAkGjB,EAAAA,EAAAA,IAAA,YAAtBqI,EAAAlC,OAAOvN,OAAK,IACtD,OAAjByP,EAAAlC,OAAOvN,Q,WAAxBqG,EAAAA,EAAAA,IAAuG,OAAvGiC,GAAuGlB,EAAAA,EAAAA,IAAA,YAAtBqI,EAAAlC,OAAOvN,OAAK,M,WAC7FqG,EAAAA,EAAAA,IAA8D,OAA9DkC,GAA8DnB,EAAAA,EAAAA,IAAtBqI,EAAAlC,OAAOvN,OAAK,M,MAGpB,OAAfyP,EAAAlC,OAAOrM,O,WAA1B0G,EAAAA,EAAAA,IAKc8H,EAAA,C,MAL2BkB,SAAA,I,CAC7BtP,OAAKwI,EAAAA,EAAAA,IACf,IAA4G,CAAhG2F,EAAAlC,OAAOsD,aAAe,M,WAAlCxK,EAAAA,EAAAA,IAA4G,OAA5GmC,GAA4GpB,EAAAA,EAAAA,IAAA,YAA5BqI,EAAAlC,OAAOsD,aAAW,M,WAClGxK,EAAAA,EAAAA,IAAkF,OAAlFoC,GAAkFrB,EAAAA,EAAAA,IAAA,YAA5BqI,EAAAlC,OAAOsD,aAAW,M,wBAG1EtK,EAAAA,EAAAA,IAIcmJ,EAAA,CAJDkB,SAAA,IAAQ,CACTtP,OAAKwI,EAAAA,EAAAA,IACf,IAAiC,E,2BAAlB2F,EAAAlC,OAAOuD,UAAQ,K,cAIuD,OAAjBrB,EAAAlC,OAAOvN,OAAkByP,EAAAhC,U,WAA7FpH,EAAAA,EAAAA,IAEM,MAFNuC,EAEM,EADJrC,EAAAA,EAAAA,IAAqFS,EAAA,CAAxEwE,QAAOuF,EAAAC,cAAe9P,KAAK,UAAUqK,MAAA,GAAMpG,KAAK,Q,kBAAO,IAAKoE,EAAA,MAAAA,EAAA,M,QAAL,Y,gDAGtEhD,EAAAA,EAAAA,IAeYmI,EAAA,CAfD7C,MAAM,Q,WAAiBwE,EAAAY,U,qCAAAZ,EAAAY,UAAS5H,GAAEmB,MAAM,MAAO,eAAcuG,EAAAG,mB,CAS3DvC,QAAM7E,EAAAA,EAAAA,IACf,IAGM,EAHNpD,EAAAA,EAAAA,IAGM,MAHNmC,EAGM,EAFJtC,EAAAA,EAAAA,IAAqDS,EAAA,CAAzCwE,QAAOuF,EAAAG,mBAAiB,C,iBAAE,IAAG3H,EAAA,MAAAA,EAAA,M,QAAH,U,6BACtChD,EAAAA,EAAAA,IAA0DS,EAAA,CAA/C9F,KAAK,UAAWsK,QAAOuF,EAAAI,S,kBAAS,IAAG5H,EAAA,MAAAA,EAAA,M,QAAH,U,iDAX/C,IAOU,EAPVhD,EAAAA,EAAAA,IAOUwI,EAAA,CAPAC,MAAOqB,EAAAe,SAAO,C,iBACtB,IAIe,EAJf7K,EAAAA,EAAAA,IAIe0I,EAAA,CAJD3N,MAAM,QAAM,C,iBACxB,IAEY,EAFZiF,EAAAA,EAAAA,IAEY8K,EAAA,CAFDlM,KAAK,Q,WAAiBkL,EAAAe,QAAQE,U,qCAARjB,EAAAe,QAAQE,UAASjI,GAAEM,YAAY,WAAWM,MAAA,gB,kBACT,IAA0B,G,aAA1F5D,EAAAA,EAAAA,IAAsHmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAAvC4I,EAAAkB,WAARC,K,WAAvE5J,EAAAA,EAAAA,IAAsH6J,EAAA,CAA1GnQ,MAAOkQ,EAAKvM,KAAO,IAAMuM,EAAKf,IAAMlP,MAAOiQ,EAAK/P,GAAgCsG,IAAKyJ,EAAK/P,I,oEAG1G8E,EAAAA,EAAAA,IAAiK0I,EAAA,CAAnJ3N,MAAM,SAAO,C,iBAAC,IAAsH,EAAtHiF,EAAAA,EAAAA,IAAsHmD,EAAA,CAA3G0F,SAAU,CAAAC,QAAA,EAAAC,QAAA,G,WAAqCe,EAAAe,QAAQzO,K,qCAAR0N,EAAAe,QAAQzO,KAAI0G,GAAEnI,KAAK,WAAWwQ,aAAa,O,0HAczI,GACCrH,MAAO,CACNkD,OAAQ,CACPnD,QAAS,CAAC,GAEXqD,QAAS,CACRrD,SAAS,IAGXrK,SAAU,KACN4R,EAAAA,EAAAA,IAAS,CAAC,SAEdC,WAAY,CACXC,OAAMA,EAAAA,GAEPnO,IAAAA,GACC,MAAO,CACN4M,YAAa,CAAC,IAAK,IAAK,KAExBW,WAAW,EAEXG,QAAS,CACRE,UAAW,KACX3O,KAAM,GACN6K,KAAM,GACN9L,OAAQ,OAEN6P,WAAW,GAEhB,EACAO,QAAS,CACR,aAAMX,GACLY,KAAKX,QAAQtR,QAAUiS,KAAK9R,IAAIwB,GAChCsQ,KAAKX,QAAQ5D,KAAOuE,KAAKxE,OACzB,MAAM/J,QAAiBuO,KAAK1O,KAAK2O,WAAWD,KAAKX,SACzB,MAApB5N,EAAS9B,SACZqQ,KAAKE,SAAS,CACb/Q,KAAM,UACNiD,QAAS,UACTC,SAAU,MAEX2N,KAAKd,WAAY,EACjBc,KAAKX,QAAU,CACdE,UAAW,KACX3O,KAAM,GACN6K,KAAM,GACN9L,OAAQ,OAGX,EAEEwP,iBAAAA,GACEa,KAAKd,WAAY,EACjBc,KAAKX,QAAU,CAChBE,UAAW,KACX3O,KAAM,GACN6K,KAAM,GACN9L,OAAQ,MAEP,EAGF,mBAAMsP,GACJ,MAAMxN,QAAiBuO,KAAK1O,KAAK6O,mBACT,MAApB1O,EAAS9B,SACXqQ,KAAKR,WAAa/N,EAASE,KAC3BqO,KAAKd,WAAY,EAErB,I,WChLJ,MAAM1B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS4C,KAEpE,O", "sources": ["webpack://frontend-web/./src/views/BugManage.vue", "webpack://frontend-web/./src/views/BugManage.vue?cef3", "webpack://frontend-web/./src/components/common/caseResult.vue", "webpack://frontend-web/./src/components/common/caseResult.vue?0f1a"], "sourcesContent": ["<template>\n  <div class=\"bug-dashboard\">\n    <el-scrollbar height=\"calc(100vh - 55px)\">\n      <div class=\"dashboard-container\">\n        <!-- 顶部区域 -->\n        <div class=\"dashboard-header\">\n          <div class=\"header-left\">\n            <h1 class=\"page-title\">Bug 管理</h1>\n          </div>\n          <div class=\"header-right\">\n            <el-badge :value=\"bugCounts.pending\" :hidden=\"bugCounts.pending === 0\" class=\"pending-badge\">\n              <el-button type=\"danger\" size=\"large\" class=\"action-btn\">\n                <el-icon><Warning /></el-icon>\n                <span>待处理 {{ bugCounts.pending }}</span>\n              </el-button>\n            </el-badge>\n          </div>\n        </div>\n\n        <!-- 统计卡片区 -->\n        <div class=\"stats-section\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"4\" v-for=\"(item, index) in statCards\" :key=\"index\">\n              <div class=\"stat-card\" :class=\"`stat-card-${item.type}`\">\n                <div class=\"stat-icon\">\n                  <el-icon>\n                    <component :is=\"item.icon\" />\n                  </el-icon>\n                </div>\n                <div class=\"stat-data\">\n                  <div class=\"stat-value\">{{ item.value }}</div>\n                  <div class=\"stat-label\">{{ item.label }}</div>\n                </div>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n\n        <!-- 图表区域 -->\n        <div class=\"charts-section\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"12\">\n              <div class=\"chart-wrapper\">\n                <div class=\"chart-header\">\n                  <h3>Bug 分布</h3>\n                  <div class=\"chart-actions\">\n                    <el-button size=\"small\" text>\n                      <el-icon><Refresh /></el-icon>\n                    </el-button>\n                  </div>\n                </div>\n                <div class=\"chart-content\" ref=\"chart1Box\"></div>\n              </div>\n            </el-col>\n            <el-col :span=\"12\">\n              <div class=\"chart-wrapper\">\n                <div class=\"chart-header\">\n                  <h3>状态占比</h3>\n                  <div class=\"chart-actions\">\n                    <el-button size=\"small\" text>\n                      <el-icon><Refresh /></el-icon>\n                    </el-button>\n                  </div>\n                </div>\n                <div class=\"chart-content\" ref=\"chart2Box\"></div>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n\n        <!-- Bug列表区域 -->\n        <div class=\"bug-list-section\">\n          <div class=\"list-header\">\n            <div class=\"title-section\">\n              <h2>BUG 列表</h2>\n              <span class=\"bug-counter\">共 {{ bugs.length }} 项</span>\n            </div>\n\n            <div class=\"filter-section\">\n              <el-radio-group v-model=\"currentFilter\" size=\"large\" @change=\"(val) => filterBugs(val)\">\n                <el-radio-button label=\"all\">全部</el-radio-button>\n                <el-radio-button label=\"pending\">待处理</el-radio-button>\n                <el-radio-button label=\"inProgress\">处理中</el-radio-button>\n                <el-radio-button label=\"completed\">已完成</el-radio-button>\n                <el-radio-button label=\"unnecessary\">无需处理</el-radio-button>\n                <el-radio-button label=\"closed\">已关闭</el-radio-button>\n              </el-radio-group>\n\n              <el-input\n                v-model=\"searchQuery\"\n                placeholder=\"搜索Bug...\"\n                class=\"search-input\"\n                clearable\n              >\n                <template #prefix>\n                  <el-icon><Search /></el-icon>\n                </template>\n              </el-input>\n            </div>\n          </div>\n\n          <el-table\n            :data=\"displayBugs\"\n            style=\"width: 100%\"\n            row-key=\"id\"\n            v-loading=\"tableLoading\"\n            :row-class-name=\"getRowClass\"\n            @row-click=\"showBugInfo\"\n            class=\"bug-table\"\n          >\n            <el-table-column type=\"expand\">\n              <template #default=\"props\">\n                <div class=\"bug-expand-detail\">\n                  <p><strong>Bug描述:</strong> {{ props.row.desc }}</p>\n                  <p><strong>所属接口:</strong> {{ props.row.interface_url }}</p>\n                  <p><strong>提交时间:</strong> {{ rTime(props.row.create_time) }}</p>\n                </div>\n              </template>\n            </el-table-column>\n\n            <el-table-column label=\"\" width=\"50\">\n              <template #default=\"scope\">\n                <div class=\"bug-status-dot\" :class=\"'bug-status-' + getStatusClass(scope.row.status)\"></div>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"id\" label=\"接口名称\"   />\n\n            <el-table-column prop=\"desc\" label=\"Bug描述\" show-overflow-tooltip>\n              <template #default=\"scope\">\n                <div class=\"bug-title\">{{ scope.row.desc }}</div>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"interface_url\" label=\"所属接口\" min-width=\"150\" show-overflow-tooltip />\n\n            <el-table-column label=\"提交时间\" >\n              <template #default=\"scope\">\n                <div class=\"time-info\">\n                  <el-icon><Time /></el-icon>\n                  <span>{{ rTime(scope.row.create_time) }}</span>\n                </div>\n              </template>\n            </el-table-column>\n\n            <el-table-column label=\"状态\" width=\"120\" align=\"center\">\n              <template #default=\"scope\">\n                <el-tag\n                  :type=\"getStatusType(scope.row.status)\"\n                  effect=\"light\"\n                  class=\"status-tag\"\n                  :class=\"'status-' + getStatusClass(scope.row.status)\"\n                >\n                  <span>{{ scope.row.status }}</span>\n                </el-tag>\n              </template>\n            </el-table-column>\n\n            <el-table-column width=\"140\" align=\"center\" fixed=\"right\" label=\"操作\">\n              <template #default=\"scope\">\n                <div class=\"action-buttons\">\n                  <el-tooltip content=\"查看详情\" placement=\"top\">\n                    <el-button\n                      circle\n                      type=\"primary\"\n                      plain\n                      @click.stop=\"showBugInfo(scope.row)\"\n                    >\n                      <el-icon><View /></el-icon>\n                    </el-button>\n                  </el-tooltip>\n\n                  <el-tooltip content=\"更新状态\" placement=\"top\">\n                    <el-button\n                      circle\n                      type=\"warning\"\n                      plain\n                      @click.stop=\"openUpdateDialog(scope.row)\"\n                    >\n                      <el-icon><Edit /></el-icon>\n                    </el-button>\n                  </el-tooltip>\n\n                  <el-tooltip content=\"删除Bug\" placement=\"top\">\n                    <el-popconfirm\n                      title=\"确定要删除此Bug吗?\"\n                      confirm-button-text=\"确定\"\n                      cancel-button-text=\"取消\"\n                      @confirm=\"deleteBug(scope.row.id)\"\n                    >\n                      <template #reference>\n                        <el-button\n                          circle\n                          type=\"danger\"\n                          plain\n                          @click.stop\n                        >\n                          <el-icon><Delete /></el-icon>\n                        </el-button>\n                      </template>\n                    </el-popconfirm>\n                  </el-tooltip>\n                </div>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <div class=\"pagination-wrapper\">\n            <el-pagination\n              v-model:current-page=\"currentPage\"\n              v-model:page-size=\"pageSize\"\n              :page-sizes=\"[10, 20, 30, 50]\"\n              layout=\"total, sizes, prev, pager, next, jumper\"\n              :total=\"filteredBugs.length\"\n              @size-change=\"handleSizeChange\"\n              @current-change=\"handleCurrentChange\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- Bug详情抽屉 -->\n      <el-drawer\n        v-model=\"bugDetailVisible\"\n        direction=\"rtl\"\n        size=\"55%\"\n        destroy-on-close\n        custom-class=\"bug-detail-drawer\"\n      >\n        <template #header>\n          <div class=\"drawer-header\">\n            <h2 class=\"drawer-title\">Bug详情</h2>\n            <el-tag\n              v-if=\"selectedBug\"\n              :type=\"getStatusType(selectedBug.status)\"\n              effect=\"dark\"\n              class=\"drawer-status-tag\"\n            >\n              {{ selectedBug.status }}\n            </el-tag>\n          </div>\n        </template>\n\n        <el-scrollbar height=\"calc(100vh - 120px)\">\n          <div v-if=\"selectedBug\" class=\"bug-detail-content\">\n            <el-card class=\"detail-card info-card\" shadow=\"hover\">\n              <template #header>\n                <div class=\"card-header\">\n                  <h3><el-icon><InfoFilled /></el-icon> 基本信息</h3>\n                </div>\n              </template>\n\n              <el-descriptions :column=\"2\" border>\n                <el-descriptions-item label=\"Bug ID\">{{ selectedBug.id }}</el-descriptions-item>\n                <el-descriptions-item label=\"提交者\">admin</el-descriptions-item>\n                <el-descriptions-item label=\"所属接口\">{{ selectedBug.interface_url }}</el-descriptions-item>\n                <el-descriptions-item label=\"提交时间\">{{ rTime(selectedBug.create_time) }}</el-descriptions-item>\n                <el-descriptions-item label=\"Bug描述\" :span=\"2\">\n                  <div class=\"bug-description\">{{ selectedBug.desc }}</div>\n                </el-descriptions-item>\n              </el-descriptions>\n            </el-card>\n\n            <el-card class=\"detail-card test-info-card\" shadow=\"hover\">\n              <template #header>\n                <div class=\"card-header\">\n                  <h3><el-icon><Tickets /></el-icon> 用例执行信息</h3>\n                </div>\n              </template>\n              <Result :result=\"selectedBug.info\" :showbtn=\"false\"></Result>\n            </el-card>\n\n            <el-card v-if=\"bugLogs && bugLogs.length > 0\" class=\"detail-card timeline-card\" shadow=\"hover\">\n              <template #header>\n                <div class=\"card-header\">\n                  <h3><el-icon><Tickets /></el-icon> 处理记录</h3>\n                </div>\n              </template>\n\n              <el-timeline>\n                <el-timeline-item\n                  v-for=\"(activity, index) in bugLogs\"\n                  :key=\"index\"\n                  :timestamp=\"rDate(activity.create_time)\"\n                  :type=\"getTimelineType(activity.handle)\"\n                  :hollow=\"true\"\n                  :size=\"index === 0 ? 'large' : 'normal'\"\n                >\n                  <el-card class=\"timeline-item-card\">\n                    <h4 class=\"activity-title\">\n                      <el-icon><component :is=\"getActivityIcon(activity.handle)\" /></el-icon>\n                      {{ activity.handle }}\n                    </h4>\n\n                    <p v-if=\"activity.remark\" class=\"activity-remark\">{{ activity.remark }}</p>\n\n                    <div class=\"activity-footer\">\n                      <span class=\"operator\">\n                        <el-icon><User /></el-icon>\n                        {{ activity.update_user }}\n                      </span>\n                      <span class=\"time\">\n                        <el-icon><Time /></el-icon>\n                        {{ rTime(activity.create_time) }}\n                      </span>\n                    </div>\n                  </el-card>\n                </el-timeline-item>\n              </el-timeline>\n            </el-card>\n\n            <el-empty v-else description=\"暂无处理记录\" :image-size=\"200\"></el-empty>\n\n            <div class=\"drawer-actions\">\n              <el-button size=\"default\" @click=\"bugDetailVisible = false\">关闭</el-button>\n              <el-button size=\"default\" type=\"primary\" @click=\"openUpdateDialog(selectedBug)\">\n                <el-icon><Edit /></el-icon> 更新状态\n              </el-button>\n            </div>\n          </div>\n        </el-scrollbar>\n      </el-drawer>\n\n      <!-- 更新Bug状态对话框 -->\n      <el-dialog\n        v-model=\"updateDialogVisible\"\n        title=\"更新Bug状态\"\n        width=\"50%\"\n        destroy-on-close\n        custom-class=\"update-dialog\"\n      >\n        <el-form :model=\"updateForm\" label-position=\"top\" status-icon class=\"update-form\">\n          <el-form-item label=\"选择状态\">\n            <div class=\"status-options\">\n              <div\n                v-for=\"(status, key) in statusOptions\"\n                :key=\"key\"\n                class=\"status-option\"\n                :class=\"{ 'active': updateForm.status === status.value }\"\n                @click=\"updateForm.status = status.value\"\n              >\n                <el-icon class=\"option-icon\">\n                  <component :is=\"status.icon\" />\n                </el-icon>\n                <div class=\"option-label\">{{ status.label }}</div>\n              </div>\n            </div>\n          </el-form-item>\n\n          <el-form-item label=\"处理备注\">\n            <el-input\n              v-model=\"updateForm.remark\"\n              type=\"textarea\"\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\n              placeholder=\"请输入处理备注...\"\n              class=\"remark-input\"\n            />\n          </el-form-item>\n        </el-form>\n\n        <template #footer>\n          <div class=\"dialog-footer\">\n            <el-button @click=\"updateDialogVisible = false\">取消</el-button>\n            <el-button type=\"primary\" @click=\"updateBug\" :loading=\"updateLoading\">\n              <el-icon><Check /></el-icon> 确认更新\n            </el-button>\n          </div>\n        </template>\n      </el-dialog>\n    </el-scrollbar>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch, nextTick, reactive, getCurrentInstance } from 'vue'\nimport { useStore } from 'vuex'\nimport Result from '../components/common/caseResult.vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  Warning, View, Edit, Delete, User, Tickets,\n  Loading, CircleCheck, WarningFilled, CircleClose, \n  Remove, Search, Refresh, Check, InfoFilled\n} from '@element-plus/icons-vue'\n\n// 获取当前实例，在 setup 阶段保存代理对象\nconst { proxy } = getCurrentInstance() || { proxy: null }\n// 获取工具函数\nconst tools = proxy?.$tools || {}\n\n// Store\nconst store = useStore()\nconst project = computed(() => store.state.pro)\n\n// 数据\nconst bugs = ref([])\nconst selectedBug = ref(null)\nconst bugLogs = ref(null)\nconst filteredBugs = ref([])\nconst searchQuery = ref('')\nconst currentFilter = ref('all')\nconst tableLoading = ref(false)\nconst bugDetailVisible = ref(false)\nconst updateDialogVisible = ref(false)\nconst updateLoading = ref(false)\nconst chart1Box = ref(null)\nconst chart2Box = ref(null)\n\n// 分页\nconst currentPage = ref(1)\nconst pageSize = ref(10)\n\n// Bug状态映射\nconst STATUS_MAP = {\n  '待处理': { type: 'danger', icon: 'Warning', class: 'pending' },\n  '处理中': { type: 'warning', icon: 'Loading', class: 'in-progress' },\n  '处理完成': { type: 'success', icon: 'CircleCheck', class: 'completed' },\n  '无需处理': { type: 'info', icon: 'Remove', class: 'unnecessary' },\n  '已关闭': { type: 'info', icon: 'CircleClose', class: 'closed' }\n}\n\n// 状态选项\nconst statusOptions = [\n  { label: '待处理', value: '待处理', icon: 'Warning' },\n  { label: '处理中', value: '处理中', icon: 'Loading' },\n  { label: '处理完成', value: '处理完成', icon: 'CircleCheck' },\n  { label: '无需处理', value: '无需处理', icon: 'Remove' },\n  { label: '已关闭', value: '已关闭', icon: 'CircleClose' }\n]\n\n// 表单数据\nconst updateForm = ref({\n  id: '',\n  status: '',\n  remark: ''\n})\n\n// 计算属性\nconst bugsByStatus = computed(() => {\n  return {\n    pending: bugs.value.filter(bug => bug.status === '待处理'),\n    inProgress: bugs.value.filter(bug => bug.status === '处理中'),\n    completed: bugs.value.filter(bug => bug.status === '处理完成'),\n    unnecessary: bugs.value.filter(bug => bug.status === '无需处理'),\n    closed: bugs.value.filter(bug => bug.status === '已关闭')\n  }\n})\n\nconst bugCounts = computed(() => {\n  return {\n    total: bugs.value.length,\n    pending: bugsByStatus.value.pending.length,\n    inProgress: bugsByStatus.value.inProgress.length,\n    completed: bugsByStatus.value.completed.length,\n    unnecessary: bugsByStatus.value.unnecessary.length,\n    closed: bugsByStatus.value.closed.length\n  }\n})\n\n// 统计卡片数据\nconst statCards = computed(() => [\n  { type: 'total', icon: 'Tickets', value: bugCounts.value.total, label: 'Bug总数' },\n  { type: 'pending', icon: 'Warning', value: bugCounts.value.pending, label: '待处理' },\n  { type: 'in-progress', icon: 'Loading', value: bugCounts.value.inProgress, label: '处理中' },\n  { type: 'completed', icon: 'CircleCheck', value: bugCounts.value.completed, label: '已完成' },\n  { type: 'unnecessary', icon: 'Remove', value: bugCounts.value.unnecessary, label: '无需处理' },\n  { type: 'closed', icon: 'CircleClose', value: bugCounts.value.closed, label: '已关闭' }\n])\n\n// 搜索过滤\nconst searchFilteredBugs = computed(() => {\n  if (!searchQuery.value) return filteredBugs.value\n\n  const query = searchQuery.value.toLowerCase()\n  return filteredBugs.value.filter(bug =>\n    bug.desc.toLowerCase().includes(query) ||\n    bug.interface_url.toLowerCase().includes(query) ||\n    String(bug.id).includes(query)\n  )\n})\n\n// 分页展示的Bug\nconst displayBugs = computed(() => {\n  const startIndex = (currentPage.value - 1) * pageSize.value\n  const endIndex = startIndex + pageSize.value\n  return searchFilteredBugs.value.slice(startIndex, endIndex)\n})\n\n// 方法\nconst fetchBugs = async () => {\n  if (!project.value?.id) return\n\n  tableLoading.value = true\n  try {\n    if (!proxy || !proxy.$api) {\n      ElMessage.error('API 初始化失败')\n      return\n    }\n\n    const response = await proxy.$api.getBugs({\n      project: project.value.id\n    })\n\n    if (response.status === 200) {\n      bugs.value = response.data\n      filterBugs(currentFilter.value)\n    }\n  } catch (error) {\n    ElMessage.error('获取Bug列表失败')\n    console.error('Failed to fetch bugs:', error)\n  } finally {\n    tableLoading.value = false\n  }\n}\n\nconst filterBugs = (filter) => {\n  currentFilter.value = filter\n  currentPage.value = 1 // 重置分页\n\n  switch (filter) {\n    case 'pending':\n      filteredBugs.value = bugsByStatus.value.pending\n      break\n    case 'inProgress':\n      filteredBugs.value = bugsByStatus.value.inProgress\n      break\n    case 'completed':\n      filteredBugs.value = bugsByStatus.value.completed\n      break\n    case 'unnecessary':\n      filteredBugs.value = bugsByStatus.value.unnecessary\n      break\n    case 'closed':\n      filteredBugs.value = bugsByStatus.value.closed\n      break\n    default:\n      filteredBugs.value = bugs.value\n  }\n}\n\nconst showBugInfo = async (bug) => {\n  selectedBug.value = bug\n  bugDetailVisible.value = true\n\n  try {\n    if (!proxy || !proxy.$api) return\n\n    const response = await proxy.$api.getBugLogs({ bug: bug.id })\n    if (response.status === 200 && response.data.length > 0) {\n      bugLogs.value = response.data\n    } else {\n      bugLogs.value = []\n    }\n  } catch (error) {\n    ElMessage.error('获取Bug处理记录失败')\n    console.error('Failed to fetch bug logs:', error)\n    bugLogs.value = []\n  }\n}\n\nconst openUpdateDialog = (bug) => {\n  updateForm.value = {\n    id: bug.id,\n    status: bug.status,\n    remark: ''\n  }\n  updateDialogVisible.value = true\n}\n\nconst updateBug = async () => {\n  if (!updateForm.value.id) return\n\n  if (!updateForm.value.remark.trim()) {\n    ElMessage.warning('请输入处理备注')\n    return\n  }\n\n  updateLoading.value = true\n  try {\n    if (!proxy || !proxy.$api) return\n\n    const response = await proxy.$api.updateBug(updateForm.value.id, updateForm.value)\n\n    if (response.status === 200) {\n      ElMessage.success({\n        message: 'Bug状态更新成功',\n        type: 'success',\n        duration: 2000\n      })\n      updateDialogVisible.value = false\n\n      // 刷新数据\n      await fetchBugs()\n\n      // 如果详情抽屉打开，刷新日志\n      if (bugDetailVisible.value && selectedBug.value) {\n        const logs = await proxy.$api.getBugLogs({ bug: selectedBug.value.id })\n        if (logs.status === 200) {\n          bugLogs.value = logs.data\n\n          // 更新选中的Bug信息\n          const updatedBug = bugs.value.find(b => b.id === selectedBug.value.id)\n          if (updatedBug) {\n            selectedBug.value = updatedBug\n          }\n        }\n      }\n    }\n  } catch (error) {\n    ElMessage.error('更新Bug状态失败')\n    console.error('Failed to update bug:', error)\n  } finally {\n    updateLoading.value = false\n  }\n}\n\nconst deleteBug = async (id) => {\n  tableLoading.value = true\n  try {\n    if (!proxy || !proxy.$api) return\n\n    const response = await proxy.$api.deleteBug(id)\n\n    if (response.status === 204) {\n      ElMessage.success({\n        message: '删除成功',\n        type: 'success',\n        duration: 2000\n      })\n      await fetchBugs()\n\n      // 如果删除的Bug正在显示详情，关闭抽屉\n      if (selectedBug.value && selectedBug.value.id === id) {\n        bugDetailVisible.value = false\n      }\n    }\n  } catch (error) {\n    ElMessage.error('删除Bug失败')\n    console.error('Failed to delete bug:', error)\n  } finally {\n    tableLoading.value = false\n  }\n}\n\nconst renderCharts = () => {\n  if (!chart1Box.value || !chart2Box.value) return\n  if (!proxy || !proxy.$chart) return\n\n  // 准备图表数据\n  const chartData = [\n    bugs.value.length,\n    bugsByStatus.value.completed.length,\n    bugsByStatus.value.inProgress.length,\n    bugsByStatus.value.pending.length,\n    bugsByStatus.value.unnecessary.length,\n    bugsByStatus.value.closed.length\n  ]\n\n  const chartLabels = ['Bug总数', '处理完成', '处理中', '待处理', '无需处理', '已关闭']\n\n  // 渲染柱状图\n  proxy.$chart.chart1(chart1Box.value, chartData, chartLabels)\n\n  // 渲染饼图\n  proxy.$chart.chart2(chart2Box.value, [\n    { value: bugsByStatus.value.completed.length, name: '处理完成' },\n    { value: bugsByStatus.value.inProgress.length, name: '处理中' },\n    { value: bugsByStatus.value.pending.length, name: '待处理' },\n    { value: bugsByStatus.value.unnecessary.length, name: '无需处理' },\n    { value: bugsByStatus.value.closed.length, name: '已关闭' }\n  ])\n}\n\n// 分页处理\nconst handleSizeChange = (size) => {\n  pageSize.value = size\n}\n\nconst handleCurrentChange = (page) => {\n  currentPage.value = page\n}\n\n// 辅助函数\nconst getStatusType = (status) => {\n  return STATUS_MAP[status]?.type || 'info'\n}\n\nconst getStatusIcon = (status) => {\n  return STATUS_MAP[status]?.icon || 'InfoFilled'\n}\n\nconst getStatusClass = (status) => {\n  return STATUS_MAP[status]?.class || 'default'\n}\n\nconst getTimelineType = (handle) => {\n  if (handle.includes('处理完成')) return 'success'\n  if (handle.includes('处理中')) return 'warning'\n  if (handle.includes('待处理')) return 'danger'\n  if (handle.includes('关闭')) return 'info'\n  return 'primary'\n}\n\nconst getActivityIcon = (handle) => {\n  if (handle.includes('处理完成')) return 'CircleCheck'\n  if (handle.includes('处理中')) return 'Loading'\n  if (handle.includes('待处理')) return 'Warning'\n  if (handle.includes('关闭')) return 'CircleClose'\n  if (handle.includes('无需处理')) return 'Remove'\n  return 'InfoFilled'\n}\n\nconst getRowClass = ({ row }) => {\n  return `bug-row bug-status-${getStatusClass(row.status)}`\n}\n\n// 生命周期钩子\nonMounted(async () => {\n  await fetchBugs()\n  nextTick(() => {\n    renderCharts()\n  })\n})\n\n// 监听bugs变化更新图表\nwatch(() => bugs.value, () => {\n  nextTick(() => {\n    renderCharts()\n  })\n}, { deep: true })\n\n// 监听搜索，重置分页\nwatch(searchQuery, () => {\n  currentPage.value = 1\n})\n\n// 在模板中使用 $tools.rTime 的地方，替换为我们的本地函数\nconst rTime = (time) => {\n  if (!proxy || !proxy.$tools) return time\n  return proxy.$tools.rTime(time)\n}\n\n// 在模板中使用 $tools.rDate 的地方，替换为我们的本地函数\nconst rDate = (time) => {\n  if (!proxy || !proxy.$tools) return time\n  return proxy.$tools.rDate ? proxy.$tools.rDate(time) : proxy.$tools.rTime(time)\n}\n</script>\n\n<style scoped>\n/* 整体样式 */\n.bug-dashboard {\n  height: 100%;\n  background-color: #f8fafc;\n  color: #334155;\n}\n\n.dashboard-container {\n  padding: 24px;\n}\n\n/* 头部区域 */\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 32px;\n}\n\n.header-left {\n  display: flex;\n  flex-direction: column;\n}\n\n.page-title {\n  margin: 0;\n  font-size: 32px;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 4px;\n  background: linear-gradient(90deg, #3b82f6, #7c3aed);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n\n.sub-title {\n  color: #64748b;\n  font-size: 16px;\n}\n\n.pending-badge :deep(.el-badge__content) {\n  background-color: #ef4444;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 20px;\n  font-weight: 600;\n}\n\n/* 统计卡片区域 */\n.stats-section {\n  margin-bottom: 32px;\n}\n\n.stat-card {\n  display: flex;\n  align-items: center;\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n  height: 100px;\n  overflow: hidden;\n  position: relative;\n  border-left: 5px solid #3b82f6;\n}\n\n.stat-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\n}\n\n.stat-card-total {\n  border-left-color: #3b82f6;\n}\n\n.stat-card-pending {\n  border-left-color: #ef4444;\n}\n\n.stat-card-in-progress {\n  border-left-color: #f59e0b;\n}\n\n.stat-card-completed {\n  border-left-color: #10b981;\n}\n\n.stat-card-unnecessary {\n  border-left-color: #64748b;\n}\n\n.stat-card-closed {\n  border-left-color: #475569;\n}\n\n.stat-icon {\n  background: rgba(59, 130, 246, 0.1);\n  border-radius: 50%;\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  margin-right: 16px;\n  flex-shrink: 0;\n}\n\n.stat-card-total .stat-icon {\n  background: rgba(59, 130, 246, 0.1);\n  color: #3b82f6;\n}\n\n.stat-card-pending .stat-icon {\n  background: rgba(239, 68, 68, 0.1);\n  color: #ef4444;\n}\n\n.stat-card-in-progress .stat-icon {\n  background: rgba(245, 158, 11, 0.1);\n  color: #f59e0b;\n}\n\n.stat-card-completed .stat-icon {\n  background: rgba(16, 185, 129, 0.1);\n  color: #10b981;\n}\n\n.stat-card-unnecessary .stat-icon {\n  background: rgba(100, 116, 139, 0.1);\n  color: #64748b;\n}\n\n.stat-card-closed .stat-icon {\n  background: rgba(71, 85, 105, 0.1);\n  color: #475569;\n}\n\n.stat-icon :deep(.el-icon) {\n  font-size: 24px;\n}\n\n.stat-data {\n  flex-grow: 1;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: 700;\n  color: #1e293b;\n  line-height: 1.2;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #64748b;\n}\n\n/* 图表区域 */\n.charts-section {\n  margin-bottom: 32px;\n}\n\n.chart-wrapper {\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  height: 360px;\n  display: flex;\n  flex-direction: column;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.chart-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.chart-content {\n  flex-grow: 1;\n  width: 100%;\n}\n\n/* Bug列表区域 */\n.bug-list-section {\n  margin-bottom: 32px;\n}\n\n.list-header {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n\n.title-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.title-section h2 {\n  margin: 0;\n  font-size: 24px;\n  color: #1e293b;\n  font-weight: 600;\n}\n\n.bug-counter {\n  background-color: #e2e8f0;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 14px;\n  color: #475569;\n  font-weight: 500;\n}\n\n.filter-section {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n\n.search-input {\n  width: 300px;\n}\n\n/* Bug表格 */\n.bug-table {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n}\n\n.bug-table :deep(th.el-table__cell) {\n  background-color: #f1f5f9;\n  color: #475569;\n  font-weight: 600;\n}\n\n.bug-table :deep(.el-table__row) {\n  transition: all 0.2s ease;\n}\n\n.bug-table :deep(.el-table__row:hover) {\n  background-color: #f8fafc;\n}\n\n/* 确保表格内容垂直居中 */\n.bug-table :deep(.el-table__cell) {\n  vertical-align: middle;\n}\n\n/* 确保固定列正确显示 */\n.bug-table :deep(.el-table__fixed-right) {\n  height: auto !important;\n  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05);\n}\n\n/* 响应式调整 */\n@media screen and (max-width: 768px) {\n  .bug-table {\n    width: 100%;\n    overflow-x: auto;\n  }\n  \n  .action-buttons {\n    justify-content: center;\n  }\n\n  /* 固定列样式优化 */\n  .bug-table :deep(.el-table__fixed-right) {\n    right: 0 !important;\n  }\n  \n  /* 确保按钮在小屏幕上间距合理且对齐 */\n  .action-buttons :deep(.el-button) {\n    margin: 0;\n    height: 30px;\n    width: 30px;\n  }\n}\n\n/* 超小屏幕设备 */\n@media screen and (max-width: 480px) {\n  .action-buttons {\n    flex-direction: column;\n    gap: 4px;\n  }\n  \n  .action-buttons :deep(.el-button) {\n    height: 28px;\n    width: 28px;\n  }\n}\n\n.bug-status-dot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  margin: 0 auto;\n}\n\n.bug-status-pending {\n  background-color: #ef4444;\n}\n\n.bug-status-in-progress {\n  background-color: #f59e0b;\n}\n\n.bug-status-completed {\n  background-color: #10b981;\n}\n\n.bug-status-unnecessary {\n  background-color: #64748b;\n}\n\n.bug-status-closed {\n  background-color: #475569;\n}\n\n.bug-row {\n  cursor: pointer;\n}\n\n.bug-row.bug-status-pending:hover {\n  background-color: rgba(239, 68, 68, 0.05) !important;\n}\n\n.bug-row.bug-status-in-progress:hover {\n  background-color: rgba(245, 158, 11, 0.05) !important;\n}\n\n.bug-row.bug-status-completed:hover {\n  background-color: rgba(16, 185, 129, 0.05) !important;\n}\n\n.bug-row.bug-status-unnecessary:hover {\n  background-color: rgba(100, 116, 139, 0.05) !important;\n}\n\n.bug-row.bug-status-closed:hover {\n  background-color: rgba(71, 85, 105, 0.05) !important;\n}\n\n.bug-title {\n  font-weight: 500;\n  color: #1e293b;\n}\n\n.time-info {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  color: #64748b;\n}\n\n.status-tag {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 4px;\n  min-width: 90px;\n  font-weight: 600;\n}\n\n.status-tag :deep(.el-icon) {\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n}\n\n.status-pending {\n  color: #ef4444;\n  background-color: rgba(239, 68, 68, 0.1);\n  border-color: rgba(239, 68, 68, 0.2);\n}\n\n.status-in-progress {\n  color: #f59e0b;\n  background-color: rgba(245, 158, 11, 0.1);\n  border-color: rgba(245, 158, 11, 0.2);\n}\n\n.status-completed {\n  color: #10b981;\n  background-color: rgba(16, 185, 129, 0.1);\n  border-color: rgba(16, 185, 129, 0.2);\n}\n\n.status-unnecessary {\n  color: #64748b;\n  background-color: rgba(100, 116, 139, 0.1);\n  border-color: rgba(100, 116, 139, 0.2);\n}\n\n.status-closed {\n  color: #475569;\n  background-color: rgba(71, 85, 105, 0.1);\n  border-color: rgba(71, 85, 105, 0.2);\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 8px;\n}\n\n.action-buttons :deep(.el-button) {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  height: 32px;\n  width: 32px;\n  position: relative;\n}\n\n.action-buttons :deep(.el-button .el-icon) {\n  font-size: 16px;\n  margin: 0;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n/* 确保Popconfirm组件内的按钮也正确对齐 */\n:deep(.el-popconfirm__action) {\n  display: flex;\n  justify-content: flex-end;\n  gap: 8px;\n  margin-top: 12px;\n}\n\n.bug-expand-detail {\n  padding: 16px;\n  background-color: #f8fafc;\n  border-radius: 8px;\n  margin: 0 20px 20px;\n}\n\n.bug-expand-detail p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.pagination-wrapper {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 24px;\n}\n\n/* Bug详情抽屉 */\n:deep(.bug-detail-drawer .el-drawer__header) {\n  margin-bottom: 0;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.drawer-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n}\n\n.drawer-title {\n  margin: 0;\n  font-size: 24px;\n  color: #1e293b;\n  font-weight: 600;\n}\n\n.drawer-status-tag {\n  font-size: 14px;\n  font-weight: 600;\n  padding: 6px 12px;\n}\n\n.bug-detail-content {\n  padding: 24px;\n}\n\n.detail-card {\n  margin-bottom: 24px;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1e293b;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.card-header :deep(.el-icon) {\n  font-size: 18px;\n}\n\n.bug-description {\n  line-height: 1.6;\n  color: #475569;\n}\n\n.timeline-item-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.activity-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-top: 0;\n  margin-bottom: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.activity-title :deep(.el-icon) {\n  font-size: 18px;\n}\n\n.activity-remark {\n  color: #475569;\n  line-height: 1.6;\n  margin: 12px 0;\n  padding: 12px;\n  background-color: #f8fafc;\n  border-radius: 6px;\n  font-size: 14px;\n}\n\n.activity-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 12px;\n  font-size: 14px;\n  color: #64748b;\n}\n\n.operator {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  color: #3b82f6;\n  font-weight: 500;\n}\n\n.time {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.drawer-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  margin-top: 32px;\n}\n\n.drawer-actions :deep(.el-button .el-icon) {\n  margin-right: 4px;\n  font-size: 16px;\n}\n\n/* 状态更新对话框 */\n:deep(.update-dialog .el-dialog__header) {\n  padding: 20px 24px;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n:deep(.update-dialog .el-dialog__body) {\n  padding: 24px;\n}\n\n.update-form {\n  margin-top: 12px;\n}\n\n.status-options {\n  display: flex;\n  justify-content: space-between;\n  gap: 12px;\n  margin-bottom: 20px;\n  width: 100%;\n}\n\n.status-option {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 16px;\n  width: 100%;\n  background-color: #f8fafc;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border: 2px solid transparent;\n}\n\n.status-option:hover {\n  background-color: #f1f5f9;\n}\n\n.status-option.active {\n  background-color: #eff6ff;\n  border-color: #3b82f6;\n}\n\n.option-icon {\n  font-size: 24px;\n  margin-bottom: 8px;\n}\n\n.option-label {\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.remark-input {\n  margin-top: 8px;\n}\n\n:deep(.el-textarea__inner) {\n  padding: 12px;\n  line-height: 1.6;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n.dialog-footer :deep(.el-button .el-icon) {\n  margin-right: 4px;\n  font-size: 16px;\n}\n\n/* Element Plus 图标样式修复 */\n:deep(.el-icon) {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 针对Element Plus按钮的特殊处理 */\n:deep(.el-button.is-circle) {\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-buttons :deep(.el-button .el-icon) {\n  font-size: 16px;\n  margin: 0;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 1em;\n  height: 1em;\n}\n</style>\n", "import script from \"./BugManage.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./BugManage.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./BugManage.vue?vue&type=style&index=0&id=8cb5ea16&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-8cb5ea16\"]])\n\nexport default __exports__", "<template>\n\t  <el-tabs model-value=\"rb\" style=\"min-height: 300px;\" type=\"border-card\" value=\"rb\" size=\"mini\">\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应体\" name=\"rb\">\n      <div v-if=\"result.response_header\">\n        <div v-if=\"result.response_header['Content-Type'].includes('application/json')\">\n          <!-- 如果 Content-Type 是 application/json，渲染 JSON 格式的 Editor -->\n          <Editor :readOnly=\"true\" v-model=\"result.response_body\" lang=\"json\" theme=\"chrome\"></Editor>\n        </div>\n        <div v-else>\n          <el-scrollbar height=\"400px\"  @wheel.stop>\n            <Editor :readOnly=\"true\" v-html=\"result.response_body\" lang=\"html\" theme=\"chrome\" height=\"400px\"></Editor>\n          </el-scrollbar>\n        </div>\n      </div>\n    </el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应头\" name=\"rh\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div class=\"tab-box-sli\" v-if=\"result.response_header\">\n\t\t\t\t<div v-for=\"(value, key) in result.response_header\">\n\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" type=\"info\">\n\t\t\t\t\t\t<b style=\"color: #747474;\">{{ key + ' : ' }}</b>\n\t\t\t\t\t\t<span>{{ value }}</span>\n\t\t\t\t\t</el-tag>\n\t\t\t\t</div>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"请求信息\" name=\"rq\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div v-if=\"result.requests_body\">\n\t\t\t\t<el-collapse v-model=\"activeNames\" class=\"tab-box-sli\">\n\t\t\t\t\t<el-collapse-item name=\"1\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>General</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div>Request Method : {{ result.method }}</div>\n\t\t\t\t\t\t<div>Request URL : {{ result.url }}</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"2\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Headers</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div v-for=\"(value, key) in result.requests_header\">\n\t\t\t\t\t\t\t<span>{{ key + ' : ' + value }}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"3\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Payload</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<span>{{ result.requests_body }}</span>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t</el-collapse>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane label=\"日志\">\n\t\t\t<el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t\t<div class=\"tab-box-sli\">\n\t\t\t\t\t<div v-for=\"(item, index) in result.log_data\">\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-if=\"item[0] === 'DEBUG'\" >{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'WARNING'\" type=\"warning\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'ERROR'\" type=\"danger\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'INFO'\" type=\"success\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<pre v-else-if=\"item[0] === 'EXCEPT'\" style=\"color: #d60000;\">{{ item[1] }}</pre>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.state === '成功'\" style=\"color: #00AA7F;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else-if=\"result.state === '失败'\" style=\"color: #d18d17;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else style=\"color: #ff0000;\">{{ result.state }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.status_cede <= 300\" style=\"color: #00AA7F;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t\t<span v-else style=\"color: #ff5500;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t{{ 'Time : ' + result.run_time }}\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t</el-tabs>\n    <div style=\"margin-top: 10px;width: 100%;text-align: center;\" v-if=\"result.state === '失败' && showbtn\">\n      <el-button  @click=\"getInterfaces\" type=\"success\" plain size=\"mini\">提交bug</el-button>\n    </div>\n    <!-- 添加bug的弹框 -->\n    <el-dialog title=\"提交bug\" v-model=\"addBugDlg\" width=\"40%\" :before-close=\"closeDialogResult\">\n      <el-form :model=\"bugForm\">\n        <el-form-item label=\"所属接口\">\n          <el-select size=\"small\" v-model=\"bugForm.interface\" placeholder=\"bug对应的接口\" style=\"width: 100%;\">\n            <el-option :label=\"iter.name + ' ' + iter.url\" :value=\"iter.id\" v-for=\"iter in interfaces\" :key=\"iter.id\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"bug描述\"><el-input :autosize=\"{ minRows: 3, maxRows: 4 }\" v-model=\"bugForm.desc\" type=\"textarea\" autocomplete=\"off\"></el-input></el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"closeDialogResult\">取 消</el-button>\n          <el-button type=\"success\" @click=\"saveBug\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n</template>\n\n<script>\nimport Editor from './Editor.vue';\nimport { mapState } from 'vuex';\nexport default {\n\tprops: {\n\t\tresult: {\n\t\t\tdefault: {}\n\t\t},\n\t\tshowbtn: {\n\t\t\tdefault: true\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState(['pro'])\n\t},\n\tcomponents: {\n\t\tEditor\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveNames: ['1', '2', '3'],\n\t\t\t// 提交bug的显示窗口\n\t\t\taddBugDlg: false,\n\t\t\t// 添加bug的表单\n\t\t\tbugForm: {\n\t\t\t\tinterface: null,\n\t\t\t\tdesc: '',\n\t\t\t\tinfo: '',\n\t\t\t\tstatus: '待处理'\n\t\t\t},\n      interfaces:[]\n\t\t};\n\t},\n\tmethods: {\n\t\tasync saveBug() {\n\t\t\tthis.bugForm.project = this.pro.id;\n\t\t\tthis.bugForm.info = this.result;\n\t\t\tconst response = await this.$api.createBugs(this.bugForm);\n\t\t\tif (response.status === 201) {\n\t\t\t\tthis.$message({\n\t\t\t\t\ttype: 'success',\n\t\t\t\t\tmessage: 'bug提交成功',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\tthis.addBugDlg = false;\n\t\t\t\tthis.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n\t\t\t}\n\t\t},\n    // 取消按钮时重置输入信息\n    closeDialogResult() {\n      this.addBugDlg = false;\n      this.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n      },\n\n    // 获取接口列表\n    async getInterfaces() {\n      const response = await this.$api.getNewInterfaces();\n      if (response.status === 200) {\n        this.interfaces = response.data\n        this.addBugDlg = true\n      }\n    }\n\t}\n};\n</script>\n\n<style></style>\n", "import { render } from \"./caseResult.vue?vue&type=template&id=3a14eb2a\"\nimport script from \"./caseResult.vue?vue&type=script&lang=js\"\nexport * from \"./caseResult.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__"], "names": ["proxy", "getCurrentInstance", "store", "useStore", "project", "computed", "state", "pro", "bugs", "ref", "selectedBug", "bugLogs", "filteredBugs", "searchQuery", "currentFilter", "tableLoading", "bugDetailVisible", "updateDialogVisible", "updateLoading", "chart1Box", "chart2Box", "currentPage", "pageSize", "STATUS_MAP", "type", "icon", "class", "statusOptions", "label", "value", "updateForm", "id", "status", "remark", "bugsByStatus", "pending", "filter", "bug", "inProgress", "completed", "unnecessary", "closed", "bugCounts", "total", "length", "statCards", "searchFilteredBugs", "query", "toLowerCase", "desc", "includes", "interface_url", "String", "displayBugs", "startIndex", "endIndex", "slice", "fetchBugs", "async", "$api", "ElMessage", "error", "response", "getBugs", "data", "filterBugs", "console", "showBugInfo", "getBugLogs", "openUpdateDialog", "updateBug", "trim", "success", "message", "duration", "logs", "updatedBug", "find", "b", "warning", "deleteBug", "<PERSON><PERSON><PERSON><PERSON>", "$chart", "chartData", "chartLabels", "chart1", "chart2", "name", "handleSizeChange", "size", "handleCurrentChange", "page", "getStatusType", "getStatusClass", "getTimelineType", "handle", "getActivityIcon", "getRowClass", "row", "onMounted", "nextTick", "watch", "deep", "rTime", "time", "$tools", "rDate", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_scrollbar", "height", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_component_el_badge", "hidden", "_component_el_button", "_component_el_icon", "_unref", "Warning", "_toDisplayString", "_hoisted_5", "_component_el_row", "gutter", "_Fragment", "_renderList", "item", "index", "_createBlock", "_component_el_col", "span", "key", "_normalizeClass", "_hoisted_6", "_resolveDynamicComponent", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "text", "Refresh", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_el_radio_group", "$event", "onChange", "_cache", "val", "_component_el_radio_button", "_component_el_input", "placeholder", "clearable", "prefix", "_withCtx", "Search", "_component_el_table", "style", "onRowClick", "_component_el_table_column", "default", "props", "_hoisted_22", "create_time", "width", "scope", "prop", "_hoisted_23", "_hoisted_24", "_component_Time", "align", "_component_el_tag", "effect", "fixed", "_hoisted_25", "_component_el_tooltip", "content", "placement", "circle", "plain", "onClick", "_withModifiers", "View", "Edit", "_component_el_popconfirm", "title", "onConfirm", "reference", "Delete", "_hoisted_26", "_component_el_pagination", "layout", "onSizeChange", "onCurrentChange", "_component_el_drawer", "direction", "header", "_hoisted_27", "_hoisted_28", "_component_el_card", "shadow", "_hoisted_29", "InfoFilled", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "_hoisted_30", "_hoisted_31", "Tickets", "Result", "result", "info", "showbtn", "_hoisted_32", "_component_el_timeline", "activity", "_component_el_timeline_item", "timestamp", "hollow", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "User", "update_user", "_hoisted_37", "_component_el_empty", "description", "_hoisted_38", "_component_el_dialog", "footer", "_hoisted_42", "loading", "Check", "_component_el_form", "model", "_component_el_form_item", "_hoisted_39", "_hoisted_41", "autosize", "minRows", "maxRows", "__exports__", "_component_el_tabs", "$props", "_component_el_tab_pane", "response_header", "_component_Editor", "readOnly", "response_body", "lang", "theme", "onWheel", "innerHTML", "requests_body", "_component_el_collapse", "$data", "activeNames", "_component_el_collapse_item", "method", "url", "requests_header", "log_data", "disabled", "status_cede", "run_time", "$options", "getInterfaces", "addBugDlg", "closeDialogResult", "saveBug", "bugForm", "_component_el_select", "interface", "interfaces", "iter", "_component_el_option", "autocomplete", "mapState", "components", "Editor", "methods", "this", "createBugs", "$message", "getNewInterfaces", "render"], "sourceRoot": ""}