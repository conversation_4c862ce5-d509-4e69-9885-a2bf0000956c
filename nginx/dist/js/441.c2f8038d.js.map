{"version": 3, "file": "js/441.c2f8038d.js", "mappings": "wMACOA,MAAM,uB,GAEJA,MAAM,kB,GACJA,MAAM,e,GAEJA,MAAM,qB,GAORA,MAAM,oB,GAkBRA,MAAM,kB,GAMIA,MAAM,e,GAKRC,IAAI,WAAWD,MAAM,mB,GAOnBA,MAAM,e,GAKRC,IAAI,gBAAgBD,MAAM,mB,GAUxBA,MAAM,e,GAKRC,IAAI,WAAWD,MAAM,mB,GAOnBA,MAAM,e,GAKRC,IAAI,cAAcD,MAAM,mB,GAOtBA,MAAM,e,GAKRC,IAAI,eAAeD,MAAM,mB,GAUvBA,MAAM,e,sWAtGvBE,EAAAA,EAAAA,IA4HM,MA5HNC,EA4HM,EA1HJC,EAAAA,EAAAA,IAyBM,MAzBNC,EAyBM,EAxBJD,EAAAA,EAAAA,IAQM,MARNE,EAQM,C,aAPJF,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAKM,MALNG,EAKM,EAJJC,EAAAA,EAAAA,IAGSC,EAAA,CAHAC,KAA2B,cAArBC,EAAAC,iBAAmC,UAAY,SAAUC,OAAO,Q,kBAC7E,IAAiC,EAAjCL,EAAAA,EAAAA,IAAiCM,EAAA,M,iBAAxB,IAAc,EAAdN,EAAAA,EAAAA,IAAcO,K,eAAU,KACjCC,EAAAA,EAAAA,IAAwB,cAArBL,EAAAC,iBAAmC,MAAQ,OAA3B,K,sBAIzBR,EAAAA,EAAAA,IAcM,MAdNa,EAcM,EAbJT,EAAAA,EAAAA,IAYkBU,EAAA,M,iBAXhB,IAMY,EANZV,EAAAA,EAAAA,IAMYW,EAAA,CALTT,KAAMC,EAAAS,aAAe,SAAW,UAChCC,QAAOC,EAAAC,iBACPC,QAASb,EAAAa,S,kBACV,IAA0E,EAA1EhB,EAAAA,EAAAA,IAA0EM,EAAA,M,iBAAjE,IAAkC,CAAhBH,EAAAS,e,WAAgBK,EAAAA,EAAAA,IAAqBC,EAAA,CAAAC,IAAA,O,WAAvDF,EAAAA,EAAAA,IAAkCG,EAAA,CAAAD,IAAA,O,eAA+B,KAC1EX,EAAAA,EAAAA,IAAGL,EAAAS,aAAe,OAAS,QAAZ,K,sCAEjBZ,EAAAA,EAAAA,IAGYW,EAAA,CAHAE,QAAOC,EAAAO,aAAW,C,iBAC5B,IAA8B,EAA9BrB,EAAAA,EAAAA,IAA8BM,EAAA,M,iBAArB,IAAW,EAAXN,EAAAA,EAAAA,IAAWsB,K,2BAAU,W,wCAQtC1B,EAAAA,EAAAA,IA6FM,MA7FN2B,EA6FM,EA3FJvB,EAAAA,EAAAA,IAwBSwB,EAAA,CAxBAC,OAAQ,IAAE,C,iBACjB,IAUS,EAVTzB,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHNmC,EAGM,C,aAFJnC,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZI,EAAAA,EAAAA,IAA2DC,EAAA,CAAnD+B,KAAK,SAAO,C,iBAAC,IAA6B,E,iBAA1B7B,EAAA8B,YAAYC,QAAU,GAAJ,K,2BAG9C,IAAkD,EAAlDtC,EAAAA,EAAAA,IAAkD,MAAlDuC,EAAkD,Y,eAItDnC,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHNwC,EAGM,C,aAFJxC,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVI,EAAAA,EAAAA,IAAsEC,EAAA,CAA9D+B,KAAK,SAAO,C,iBAAC,IAAsC,E,iBAAnC7B,EAAA8B,YAAYI,iBAAmB,GAAI,KAAE,K,2BAGjE,IAAuD,EAAvDzC,EAAAA,EAAAA,IAAuD,MAAvD0C,EAAuD,Y,uBAM7DtC,EAAAA,EAAAA,IAoCSwB,EAAA,CApCAC,OAAQ,GAAIc,MAAA,uB,kBACnB,IAUS,EAVTvC,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHN4C,EAGM,C,aAFJ5C,EAAAA,EAAAA,IAAoB,YAAd,WAAO,KACbI,EAAAA,EAAAA,IAAgFC,EAAA,CAAxE+B,KAAK,SAAO,C,iBAAC,IAAiD,E,iBAA9C7B,EAAAsC,cAAcC,KAAKC,SAASC,QAAQ,IAAM,GAAI,IAAC,K,2BAG3E,IAAkD,EAAlDhD,EAAAA,EAAAA,IAAkD,MAAlDiD,EAAkD,Y,eAItD7C,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHNkD,EAGM,C,aAFJlD,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXI,EAAAA,EAAAA,IAAmFC,EAAA,CAA3E+B,KAAK,SAAO,C,iBAAC,IAAoD,E,iBAAjD7B,EAAAsC,cAAcM,QAAQJ,SAASC,QAAQ,IAAM,GAAI,IAAC,K,2BAG9E,IAAqD,EAArDhD,EAAAA,EAAAA,IAAqD,MAArDoD,EAAqD,Y,eAIzDhD,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHNqD,EAGM,C,aAFJrD,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVI,EAAAA,EAAAA,IAAuFC,EAAA,CAA/E+B,KAAK,SAAO,C,iBAAC,IAAyD,E,iBAAtDlB,EAAAoC,YAAY/C,EAAAsC,cAAcU,SAASC,YAAc,IAAJ,K,2BAGzE,IAAsD,EAAtDxD,EAAAA,EAAAA,IAAsD,MAAtDyD,EAAsD,Y,uBAM5DrD,EAAAA,EAAAA,IAwBSwB,EAAA,CAxBDe,MAAA,uBAAyB,C,iBAC/B,IAsBS,EAtBTvC,EAAAA,EAAAA,IAsBS0B,EAAA,CAtBAC,KAAM,IAAE,C,iBACf,IAoBU,EApBV3B,EAAAA,EAAAA,IAoBU4B,EAAA,CApBDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHN0D,EAGM,C,aAFJ1D,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVI,EAAAA,EAAAA,IAA2DW,EAAA,CAAhDqB,KAAK,QAASnB,QAAOC,EAAAyC,W,kBAAW,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,gDAG/C,IAYW,EAZXxD,EAAAA,EAAAA,IAYWyD,EAAA,CAZAC,KAAMvD,EAAAwD,WAAWC,OAAO,IAAK5B,KAAK,QAAQ6B,OAAA,I,kBACnD,IAIkB,EAJlB7D,EAAAA,EAAAA,IAIkB8D,EAAA,CAJDC,KAAK,YAAYC,MAAM,KAAKC,MAAM,O,CACtCC,SAAOpC,EAAAA,EAAAA,IAChB,EADoBqC,SAAG,E,qBAChBC,KAAKD,EAAIE,WAAWC,sBAAkB,K,OAGjDtE,EAAAA,EAAAA,IAAyD8D,EAAA,CAAxCC,KAAK,SAASC,MAAM,MAAMC,MAAM,SACjDjE,EAAAA,EAAAA,IAAuE8D,EAAA,CAAtDC,KAAK,kBAAkBC,MAAM,WAAWC,MAAM,SAC/DjE,EAAAA,EAAAA,IAAiE8D,EAAA,CAAhDC,KAAK,gBAAgBC,MAAM,OAAOC,MAAM,SACzDjE,EAAAA,EAAAA,IAA+D8D,EAAA,CAA9CC,KAAK,YAAYC,MAAM,SAASC,MAAM,SACvDjE,EAAAA,EAAAA,IAAgE8D,EAAA,CAA/CC,KAAK,aAAaC,MAAM,SAASC,MAAM,SACxDjE,EAAAA,EAAAA,IAAkE8D,EAAA,CAAjDC,KAAK,gBAAgBC,MAAM,QAAQC,MAAM,U,gIAiBxE,GACEM,KAAM,qBACNC,WAAY,CACVC,WAAU,aAAEC,UAAS,YAAEC,WAAU,aAAEC,QAAOA,EAAAA,SAE5ClB,IAAAA,GACE,MAAO,CACLmB,UAAW,KACXzE,iBAAkB,eAClBQ,cAAc,EACdI,SAAS,EACTiB,YAAa,CAAC,EACdQ,cAAe,CAAC,EAChBkB,WAAY,GACZmB,OAAQ,CAAC,EACTC,cAAe,GAEfC,kBAAmB,EACnBC,qBAAsB,EACtBC,kBAAmB,IACnBC,eAAgB,KAChBC,eAAgB,KAChBC,kBAAmB,IACnBC,sBAAsB,EACtBC,uBAAwB,CAAC,IAAM,IAAM,IAAM,IAAO,KAEtD,EACAC,SAAU,KACLC,EAAAA,EAAAA,IAAS,CACVC,cAAeC,GAASA,EAAMD,qBAAiB,GAAKE,OAAOC,OAAOC,UAGtEC,OAAAA,GACEC,KAAKC,aACLD,KAAKE,mBAGLC,SAASC,iBAAiB,mBAAoBJ,KAAKK,wBAGnDC,OAAOF,iBAAiB,QAASJ,KAAKO,mBACtCD,OAAOF,iBAAiB,OAAQJ,KAAKQ,iBACvC,EAEAC,aAAAA,GAEET,KAAKV,sBAAuB,EAG5BU,KAAKU,mBAGLV,KAAKW,iBAGLX,KAAKY,gBAGLT,SAASU,oBAAoB,mBAAoBb,KAAKK,wBACtDC,OAAOO,oBAAoB,QAASb,KAAKO,mBACzCD,OAAOO,oBAAoB,OAAQb,KAAKQ,iBAC1C,EAGAM,SAAAA,GACgC,iBAA1Bd,KAAK5F,kBAAwC4F,KAAKV,sBACpDU,KAAKE,kBAET,EAEAa,WAAAA,GAEEf,KAAKgB,iBACP,EACAC,QAAS,CACPhB,UAAAA,GACED,KAAKkB,UAAU,KACblB,KAAKmB,eACLnB,KAAKoB,oBACLpB,KAAKqB,eACLrB,KAAKsB,kBACLtB,KAAKuB,oBAET,EAEAJ,YAAAA,GACE,MAAMK,EAAWxB,KAAKyB,MAAMC,SAC5B,IAAKF,EAAU,OAEfxB,KAAKlB,OAAO6C,IAAMC,EAAAA,GAAaJ,GAC/B,MAAMK,EAAS,CACbC,MAAO,CAAEC,KAAM,SAAUC,KAAM,UAC/BC,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,OAC9B8D,OAAQ,CAAC,CACP9D,KAAM,MACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,WACpBC,UAAW,CAAED,MAAO,8BAGxBxC,KAAKlB,OAAO6C,IAAIe,UAAUb,EAC5B,EAEAT,iBAAAA,GACE,MAAMI,EAAWxB,KAAKyB,MAAMkB,cAC5B,IAAKnB,EAAU,OAEfxB,KAAKlB,OAAO8D,SAAWhB,EAAAA,GAAaJ,GACpC,MAAMK,EAAS,CACbC,MAAO,CAAEC,KAAM,OAAQC,KAAM,UAC7BC,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,MAC9B8D,OAAQ,CAAC,CACP9D,KAAM,SACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,cAGxBxC,KAAKlB,OAAO8D,SAASF,UAAUb,EACjC,EAEAR,YAAAA,GACE,MAAMG,EAAWxB,KAAKyB,MAAMoB,SAC5B,IAAKrB,EAAU,OAEfxB,KAAKlB,OAAOpC,IAAMkF,EAAAA,GAAaJ,GAC/B,MAAMK,EAAS,CACbI,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,IAAKuE,IAAK,KACxCT,OAAQ,CAAC,CACP9D,KAAM,MACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,WACpBC,UAAW,CAAED,MAAO,8BAGxBxC,KAAKlB,OAAOpC,IAAIgG,UAAUb,EAC5B,EAEAP,eAAAA,GACE,MAAME,EAAWxB,KAAKyB,MAAMsB,YAC5B,IAAKvB,EAAU,OAEfxB,KAAKlB,OAAO/B,OAAS6E,EAAAA,GAAaJ,GAClC,MAAMK,EAAS,CACbI,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,IAAKuE,IAAK,KACxCT,OAAQ,CAAC,CACP9D,KAAM,KACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,WACpBC,UAAW,CAAED,MAAO,+BAGxBxC,KAAKlB,OAAO/B,OAAO2F,UAAUb,EAC/B,EAEAN,gBAAAA,GACE,MAAMC,EAAWxB,KAAKyB,MAAMuB,aAC5B,IAAKxB,EAAU,OAEfxB,KAAKlB,OAAO3B,QAAUyE,EAAAA,GAAaJ,GACnC,MAAMK,EAAS,CACbI,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,MAC9B8D,OAAQ,CACN,CACE9D,KAAM,KACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,YAEtB,CACEjE,KAAM,KACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,UAAWtI,KAAM,aAI3C8F,KAAKlB,OAAO3B,QAAQuF,UAAUb,EAChC,EAEA3B,gBAAAA,GAEE,GAAIF,KAAKV,qBAAsB,OAE/B,IAAKU,KAAKN,cAER,YADAuD,EAAAA,GAAUC,MAAM,WAKlBlD,KAAKU,mBAEL,MAAMyC,EAA0C,WAA7B7C,OAAO8C,SAASC,SAAwB,OAAS,MAC9DC,EAAQ,GAAGH,MAAe7C,OAAO8C,SAASG,+BAA+BvD,KAAKN,iBAEpF,IACEM,KAAKnB,UAAY,IAAI2E,UAAUF,GAE/BtD,KAAKnB,UAAU4E,OAASzD,KAAK0D,oBAC7B1D,KAAKnB,UAAU8E,UAAY3D,KAAK4D,uBAChC5D,KAAKnB,UAAUgF,QAAU7D,KAAK8D,qBAC9B9D,KAAKnB,UAAUkF,QAAU/D,KAAKgE,qBAG9BhE,KAAKiE,kBAAoBC,WAAW,KAC9BlE,KAAKnB,WAAamB,KAAKnB,UAAUsF,aAAeX,UAAUY,aAC5DpE,KAAKnB,UAAUwF,QACfrE,KAAKsE,4BAEN,IAEL,CAAE,MAAOpB,GACPqB,QAAQrB,MAAM,mBAAoBA,GAClClD,KAAKwE,sBAAsBtB,EAC7B,CACF,EAEAQ,mBAAAA,GACM1D,KAAKV,uBAETU,KAAK5F,iBAAmB,YACxB4F,KAAKhB,kBAAoB,EACzBgB,KAAKyE,sBAELxB,EAAAA,GAAUyB,QAAQ,iBAGd1E,KAAKiE,oBACPU,aAAa3E,KAAKiE,mBAClBjE,KAAKiE,kBAAoB,MAI3BjE,KAAK4E,iBAGL5E,KAAK6E,mBACP,EAEAjB,sBAAAA,CAAuBkB,GACrB,IAAI9E,KAAKV,qBAET,IACE,MAAM5B,EAAOqH,KAAKC,MAAMF,EAAMpH,MAC9BsC,KAAKiF,wBAAwBvH,EAC/B,CAAE,MAAOwF,GACPqB,QAAQrB,MAAM,mBAAoBA,EACpC,CACF,EAEAY,oBAAAA,CAAqBgB,GACf9E,KAAKV,uBAETU,KAAK5F,iBAAmB,eACxB4F,KAAKpF,cAAe,EACpBoF,KAAKkF,gBAGDlF,KAAKiE,oBACPU,aAAa3E,KAAKiE,mBAClBjE,KAAKiE,kBAAoB,MAG3BM,QAAQY,IAAI,iBAAkBL,EAAMM,KAAMN,EAAMO,QAG7B,MAAfP,EAAMM,MAAgC,OAAfN,EAAMM,OAC/BnC,EAAAA,GAAUqC,QAAQ,iBAClBtF,KAAKuF,oBAET,EAEAvB,oBAAAA,CAAqBd,GACflD,KAAKV,uBAETiF,QAAQrB,MAAM,eAAgBA,GAC9BlD,KAAK5F,iBAAmB,eAGpB4F,KAAKiE,oBACPU,aAAa3E,KAAKiE,mBAClBjE,KAAKiE,kBAAoB,MAE7B,EAEAK,uBAAAA,GACEC,QAAQiB,KAAK,iBACbvC,EAAAA,GAAUC,MAAM,iBAChBlD,KAAK5F,iBAAmB,eACxB4F,KAAKuF,kBACP,EAEAf,qBAAAA,CAAsBtB,GACpBqB,QAAQrB,MAAM,iBAAkBA,GAChCD,EAAAA,GAAUC,MAAM,iBAChBlD,KAAK5F,iBAAmB,eACxB4F,KAAKuF,kBACP,EAEAN,uBAAAA,CAAwBvH,GAEtB,GAAkB,SAAdA,EAAKxD,KAKT,OAAQwD,EAAKxD,MACX,IAAK,yBACHqK,QAAQY,IAAI,QAASzH,EAAK+H,SAC1B,MACF,IAAK,iBACHzF,KAAK0F,kBAAkBhI,EAAKA,MAC5B,MACF,IAAK,qBACHsC,KAAK2F,sBAAsBjI,EAAKA,MAChC,MACF,IAAK,eACHuF,EAAAA,GAAUyB,QAAQ,WAClB,MACF,IAAK,iBACHzB,EAAAA,GAAUyB,QAAQ,WAClB1E,KAAKpF,cAAe,EACpB,MACF,IAAK,cACHqI,EAAAA,GAAUC,MAAM,WAAaxF,EAAKwF,OAClClD,KAAKpF,cAAe,EACpB,MACF,IAAK,QACHqI,EAAAA,GAAUC,MAAMxF,EAAK+H,SACrB,MACF,QACElB,QAAQiB,KAAK,oBAAqB9H,EAAKxD,WA7BzC8F,KAAK4F,aAAexH,KAAKyH,KA+B7B,EAEAN,gBAAAA,GACE,GAAIvF,KAAKV,sBAAwBU,KAAKhB,mBAAqBgB,KAAKf,qBAI9D,YAHIe,KAAKhB,mBAAqBgB,KAAKf,sBACjCgE,EAAAA,GAAUC,MAAM,4BAKpBlD,KAAKyE,sBAGL,MAAMqB,EAAeC,KAAKC,IAAIhG,KAAKhB,kBAAmBgB,KAAKT,uBAAuB0G,OAAS,GACrFC,EAAQlG,KAAKT,uBAAuBuG,GAE1CvB,QAAQY,IAAI,OAAOnF,KAAKhB,kBAAoB,YAAYkH,OAExDlG,KAAKb,eAAiB+E,WAAW,KAC1BlE,KAAKV,uBACRU,KAAKhB,oBACLgB,KAAKE,qBAENgG,EACL,EAEAtB,cAAAA,GACE5E,KAAKkF,gBAELlF,KAAKZ,eAAiB+G,YAAY,KAC5BnG,KAAKnB,WAAamB,KAAKnB,UAAUsF,aAAeX,UAAU4C,MAC5DpG,KAAKnB,UAAUwH,KAAKtB,KAAKuB,UAAU,CAAEpM,KAAM,WAE5C8F,KAAKX,kBACV,EAEA6F,aAAAA,GACMlF,KAAKZ,iBACPmH,cAAcvG,KAAKZ,gBACnBY,KAAKZ,eAAiB,KAE1B,EAEAqF,mBAAAA,GACMzE,KAAKb,iBACPwF,aAAa3E,KAAKb,gBAClBa,KAAKb,eAAiB,KAE1B,EAEAwB,cAAAA,GACEX,KAAKyE,sBACLzE,KAAKkF,gBAEDlF,KAAKiE,oBACPU,aAAa3E,KAAKiE,mBAClBjE,KAAKiE,kBAAoB,KAE7B,EAEAvD,gBAAAA,GACMV,KAAKnB,YAEPmB,KAAKnB,UAAU4E,OAAS,KACxBzD,KAAKnB,UAAU8E,UAAY,KAC3B3D,KAAKnB,UAAUgF,QAAU,KACzB7D,KAAKnB,UAAUkF,QAAU,KAGrB/D,KAAKnB,UAAUsF,aAAeX,UAAU4C,MACxCpG,KAAKnB,UAAUsF,aAAeX,UAAUY,YAC1CpE,KAAKnB,UAAUwF,MAAM,IAAM,qBAG7BrE,KAAKnB,UAAY,MAGnBmB,KAAKkF,gBACLlF,KAAKyE,qBACP,EAEAzD,eAAAA,GAEMhB,KAAKnB,WACPmB,KAAKnB,UAAUwF,MAAM,IAAM,yBAE7BrE,KAAKkF,gBACLlF,KAAKyE,qBACP,EAGApE,sBAAAA,GACML,KAAKV,uBAELa,SAASqG,OAEXxG,KAAKgB,kBAGyB,iBAA1BhB,KAAK5F,mBACP4F,KAAKhB,kBAAoB,EACzBgB,KAAKE,oBAGX,EAGAK,iBAAAA,GACMP,KAAKV,sBAEqB,iBAA1BU,KAAK5F,mBACP4F,KAAKhB,kBAAoB,EACzBgB,KAAKE,mBAET,EAEAM,gBAAAA,GAGE+D,QAAQY,IAAI,SACd,EAEAsB,mBAAAA,GAEEzG,KAAKU,kBACP,EAEAgF,iBAAAA,CAAkBhI,GAChB,IAAKA,GAAQsC,KAAKV,qBAAsB,OAGxCU,KAAK/D,YAAc,IAAKyB,GAGxB,MAAMW,GAAY,IAAID,MAAOsI,cACvBC,EAAe,CACnBtI,YACAnC,OAAQwB,EAAKkJ,SAAW,EACxBvK,gBAAiBqB,EAAKrB,iBAAmB,EACzCwK,cAAenJ,EAAKoJ,gBAAkB,EACtCC,UAAWrJ,EAAKsJ,YAAc,EAC9BC,WAAYjH,KAAKvD,cAAcC,KAAKC,SAAW,EAC/CuK,cAAelH,KAAKvD,cAAcM,QAAQJ,SAAW,EACrDwK,YAAanH,KAAKvD,cAAcU,SAASC,YAAc,EACvDgK,YAAapH,KAAKvD,cAAcU,SAASkK,YAAc,GAIzDrH,KAAKsH,aAAaX,GAGlB3G,KAAKuH,qBACP,EAEA5B,qBAAAA,CAAsBjI,GACfA,IAAQsC,KAAKV,uBAEA,qBAAd5B,EAAKxD,KAEP8F,KAAKvD,cAAgB,IAAKiB,EAAKA,MAG/BsC,KAAK/D,YAAc,IAAK+D,KAAK/D,eAAgByB,GAG/CsC,KAAKuH,sBACP,EAEAD,YAAAA,CAAaE,GAIX,GAHAxH,KAAKrC,WAAW8J,KAAKD,GAGjBxH,KAAKrC,WAAWsI,OAASjG,KAAKjB,cAAe,CAC/C,MAAM2I,EAAa1H,KAAKrC,WAAWsI,OAASjG,KAAKjB,cACjDiB,KAAKrC,WAAWgK,OAAO,EAAGD,EAC5B,CACF,EAEAH,mBAAAA,GAEMvH,KAAK4H,kBACPjD,aAAa3E,KAAK4H,kBAGpB5H,KAAK4H,iBAAmB1D,WAAW,KAC5BlE,KAAKV,sBACRU,KAAK6H,gBAEN,IACL,EAEAA,YAAAA,GACE,GAAI7H,KAAKV,uBAAyBU,KAAKrC,WAAWsI,OAAQ,OAG1D,MAAM6B,EAAQ9H,KAAKrC,WAAWoK,IAAIC,GAAK,IAAI5J,KAAK4J,EAAE3J,WAAWC,sBAG7D0B,KAAKiI,oBAAoBH,EAC3B,EAEAG,mBAAAA,CAAoBH,GAClB,MAAMI,EAAe,CACnB,CACEC,MAAOnI,KAAKlB,OAAO6C,IACnBjE,KAAMsC,KAAKrC,WAAWoK,IAAIC,GAAKA,EAAE9L,SAEnC,CACEiM,MAAOnI,KAAKlB,OAAO8D,SACnBlF,KAAMsC,KAAKrC,WAAWoK,IAAIC,GAAKA,EAAE3L,kBAEnC,CACE8L,MAAOnI,KAAKlB,OAAOpC,IACnBgB,KAAMsC,KAAKrC,WAAWoK,IAAIC,GAAKA,EAAEf,aAEnC,CACEkB,MAAOnI,KAAKlB,OAAO/B,OACnBW,KAAMsC,KAAKrC,WAAWoK,IAAIC,GAAKA,EAAEd,iBAKrCgB,EAAaE,QAAQ,EAAGD,QAAOzK,WAC7B,GAAIyK,GAASA,EAAMzF,UACjB,IACEyF,EAAMzF,UAAU,CACdP,MAAO,CAAEzE,KAAMoK,GACfzF,OAAQ,CAAC,CAAE3E,KAAMA,MAChB,GAAO,EACZ,CAAE,MAAOwF,GACPqB,QAAQiB,KAAK,UAAWtC,EAC1B,IAKJlD,KAAKqI,oBACP,EAGAA,kBAAAA,GACE,IAAKrI,KAAKlB,OAAO3B,QAAS,OAE1B,MAAM2K,EAAQ9H,KAAKrC,WAAWoK,IAAIC,GAAK,IAAI5J,KAAK4J,EAAE3J,WAAWC,sBACvDgK,EAAkBtI,KAAKrC,WAAWoK,IAAIC,IAC1C,MAAMO,EAAQP,EAAEb,aAAenH,KAAKvD,cAAcU,SAASC,YAAc,EACzE,OAAQmL,EAAQ,KAAO,MAAM3L,QAAQ,KAEjC4L,EAAkBxI,KAAKrC,WAAWoK,IAAIC,IAC1C,MAAMO,EAAQP,EAAEZ,aAAepH,KAAKvD,cAAcU,SAASkK,YAAc,EACzE,OAAQkB,EAAQ,KAAO,MAAM3L,QAAQ,KAGvCoD,KAAKlB,OAAO3B,QAAQuF,UAAU,CAC5BP,MAAO,CAAEzE,KAAMoK,GACfzF,OAAQ,CACN,CAAE3E,KAAM4K,GACR,CAAE5K,KAAM8K,KAGd,EAEAzN,gBAAAA,GACE,GAA8B,cAA1BiF,KAAK5F,iBAEP,YADA6I,EAAAA,GAAUC,MAAM,gBAIlBlD,KAAKhF,SAAU,EACf,MAAMyN,EAASzI,KAAKpF,aAAe,kBAAoB,mBAEvDoF,KAAKnB,UAAUwH,KAAKtB,KAAKuB,UAAU,CAAEpM,KAAMuO,KAE3CvE,WAAW,KACTlE,KAAKpF,cAAgBoF,KAAKpF,aAC1BoF,KAAKhF,SAAU,GACd,IACL,EAEA6J,gBAAAA,GACgC,cAA1B7E,KAAK5F,kBACP4F,KAAKnB,UAAUwH,KAAKtB,KAAKuB,UAAU,CAAEpM,KAAM,uBAE/C,EAEAmB,WAAAA,GACE2E,KAAK6E,mBACL5B,EAAAA,GAAUyB,QAAQ,QACpB,EAEAnH,SAAAA,GAEEyC,KAAKrC,WAAWsI,OAAS,EACzBjG,KAAK/D,YAAc,CAAC,EACpB+D,KAAKvD,cAAgB,CAAC,EAGtBuD,KAAK6H,eACL5E,EAAAA,GAAUyB,QAAQ,QACpB,EAEAxH,WAAAA,CAAYqL,GACV,GAAc,IAAVA,EAAa,MAAO,MACxB,MAAMG,EAAI,KACJC,EAAQ,CAAC,IAAK,KAAM,KAAM,MAC1BC,EAAI7C,KAAK8C,MAAM9C,KAAKZ,IAAIoD,GAASxC,KAAKZ,IAAIuD,IAChD,OAAOI,YAAYP,EAAQxC,KAAKgD,IAAIL,EAAGE,IAAIhM,QAAQ,IAAM,IAAM+L,EAAMC,EACvE,EAEAhI,aAAAA,GAEMZ,KAAK4H,mBACPjD,aAAa3E,KAAK4H,kBAClB5H,KAAK4H,iBAAmB,MAI1BoB,OAAOC,KAAKjJ,KAAKlB,QAAQsJ,QAAQjN,IAC/B,MAAMgN,EAAQnI,KAAKlB,OAAO3D,GAC1B,GAAIgN,GAAkC,oBAAlBA,EAAMe,QACxB,IACEf,EAAMe,SACR,CAAE,MAAOhG,GACPqB,QAAQiB,KAAK,QAAQrK,SAAY+H,EACnC,IAKJlD,KAAKlB,OAAS,CAAC,CACjB,I,WC3yBJ,MAAMqK,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceMonitor.vue", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceMonitor.vue?a92e"], "sourcesContent": ["<template>\r\n  <div class=\"performance-monitor\">\r\n    <!-- 顶部状态区域 -->\r\n    <div class=\"monitor-header\">\r\n      <div class=\"header-info\">\r\n        <h2>性能实时监控</h2>\r\n        <div class=\"connection-status\">\r\n          <el-tag :type=\"connectionStatus === 'connected' ? 'success' : 'danger'\" effect=\"dark\">\r\n            <el-icon><Connection /></el-icon>\r\n            {{ connectionStatus === 'connected' ? '已连接' : '未连接' }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n      <div class=\"monitor-controls\">\r\n        <el-button-group>\r\n          <el-button \r\n            :type=\"isMonitoring ? 'danger' : 'primary'\" \r\n            @click=\"toggleMonitoring\"\r\n            :loading=\"loading\">\r\n            <el-icon><VideoPlay v-if=\"!isMonitoring\" /><VideoPause v-else /></el-icon>\r\n            {{ isMonitoring ? '停止监控' : '开始监控' }}\r\n          </el-button>\r\n          <el-button @click=\"refreshData\">\r\n            <el-icon><Refresh /></el-icon>\r\n            刷新\r\n          </el-button>\r\n        </el-button-group>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 监控面板 -->\r\n    <div class=\"monitor-panels\">\r\n      <!-- 性能指标面板 -->\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"monitor-card\">\r\n            <template #header>\r\n              <div class=\"card-header\">\r\n                <span>TPS 趋势</span>\r\n                <el-tag size=\"small\">{{ currentData.avgTps || 0 }}</el-tag>\r\n              </div>\r\n            </template>\r\n            <div ref=\"tpsChart\" class=\"chart-container\"></div>\r\n          </el-card>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-card class=\"monitor-card\">\r\n            <template #header>\r\n              <div class=\"card-header\">\r\n                <span>响应时间</span>\r\n                <el-tag size=\"small\">{{ currentData.avgResponseTime || 0 }}ms</el-tag>\r\n              </div>\r\n            </template>\r\n            <div ref=\"responseChart\" class=\"chart-container\"></div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 系统资源面板 -->\r\n      <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n        <el-col :span=\"8\">\r\n          <el-card class=\"monitor-card\">\r\n            <template #header>\r\n              <div class=\"card-header\">\r\n                <span>CPU 使用率</span>\r\n                <el-tag size=\"small\">{{ currentSystem.cpu?.percent?.toFixed(1) || 0 }}%</el-tag>\r\n              </div>\r\n            </template>\r\n            <div ref=\"cpuChart\" class=\"chart-container\"></div>\r\n          </el-card>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"8\">\r\n          <el-card class=\"monitor-card\">\r\n            <template #header>\r\n              <div class=\"card-header\">\r\n                <span>内存使用率</span>\r\n                <el-tag size=\"small\">{{ currentSystem.memory?.percent?.toFixed(1) || 0 }}%</el-tag>\r\n              </div>\r\n            </template>\r\n            <div ref=\"memoryChart\" class=\"chart-container\"></div>\r\n          </el-card>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"8\">\r\n          <el-card class=\"monitor-card\">\r\n            <template #header>\r\n              <div class=\"card-header\">\r\n                <span>网络流量</span>\r\n                <el-tag size=\"small\">{{ formatBytes(currentSystem.network?.bytes_recv || 0) }}</el-tag>\r\n              </div>\r\n            </template>\r\n            <div ref=\"networkChart\" class=\"chart-container\"></div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 详细数据表格 -->\r\n      <el-row style=\"margin-top: 20px;\">\r\n        <el-col :span=\"24\">\r\n          <el-card class=\"monitor-card\">\r\n            <template #header>\r\n              <div class=\"card-header\">\r\n                <span>实时数据</span>\r\n                <el-button size=\"small\" @click=\"clearData\">清空数据</el-button>\r\n              </div>\r\n            </template>\r\n            <el-table :data=\"recentData.slice(-10)\" size=\"small\" stripe>\r\n              <el-table-column prop=\"timestamp\" label=\"时间\" width=\"200\">\r\n                <template #default=\"{ row }\">\r\n                  {{ new Date(row.timestamp).toLocaleTimeString() }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"avgTps\" label=\"TPS\" width=\"100\" />\r\n              <el-table-column prop=\"avgResponseTime\" label=\"响应时间(ms)\" width=\"120\" />\r\n              <el-table-column prop=\"totalRequests\" label=\"总请求数\" width=\"120\" />\r\n              <el-table-column prop=\"errorRate\" label=\"错误率(%)\" width=\"100\" />\r\n              <el-table-column prop=\"cpuPercent\" label=\"CPU(%)\" width=\"100\" />\r\n              <el-table-column prop=\"memoryPercent\" label=\"内存(%)\" width=\"100\" />\r\n            </el-table>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ElMessage } from \"element-plus\";\r\nimport { mapState } from \"vuex\";\r\nimport {\r\n  Connection, VideoPlay, VideoPause, Refresh\r\n} from '@element-plus/icons-vue';\r\nimport * as echarts from 'echarts';\r\n\r\nexport default {\r\n  name: 'PerformanceMonitor',\r\n  components: {\r\n    Connection, VideoPlay, VideoPause, Refresh\r\n  },\r\n  data() {\r\n    return {\r\n      websocket: null,\r\n      connectionStatus: 'disconnected',\r\n      isMonitoring: false,\r\n      loading: false,\r\n      currentData: {},\r\n      currentSystem: {},\r\n      recentData: [],\r\n      charts: {},\r\n      maxDataPoints: 50,\r\n      // WebSocket连接管理\r\n      reconnectAttempts: 0,\r\n      maxReconnectAttempts: 5,\r\n      reconnectInterval: 5000,\r\n      reconnectTimer: null,\r\n      heartbeatTimer: null,\r\n      heartbeatInterval: 30000,\r\n      isComponentDestroyed: false,\r\n      connectionRetryBackoff: [1000, 2000, 5000, 10000, 30000] // 重连退避策略\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      currentTaskId: state => state.currentTaskId || this.$route.params.taskId\r\n    })\r\n  },\r\n  mounted() {\r\n    this.initCharts();\r\n    this.connectWebSocket();\r\n    \r\n    // 添加页面可见性监听，优化性能\r\n    document.addEventListener('visibilitychange', this.handleVisibilityChange);\r\n    \r\n    // 添加窗口焦点监听\r\n    window.addEventListener('focus', this.handleWindowFocus);\r\n    window.addEventListener('blur', this.handleWindowBlur);\r\n  },\r\n  \r\n  beforeUnmount() {\r\n    // 标记组件已销毁\r\n    this.isComponentDestroyed = true;\r\n    \r\n    // 清理WebSocket连接\r\n    this.cleanupWebSocket();\r\n    \r\n    // 清理定时器\r\n    this.clearAllTimers();\r\n    \r\n    // 清理图表\r\n    this.destroyCharts();\r\n    \r\n    // 移除事件监听器\r\n    document.removeEventListener('visibilitychange', this.handleVisibilityChange);\r\n    window.removeEventListener('focus', this.handleWindowFocus);\r\n    window.removeEventListener('blur', this.handleWindowBlur);\r\n  },\r\n  \r\n  // 添加组件激活/停用处理（用于keep-alive）\r\n  activated() {\r\n    if (this.connectionStatus === 'disconnected' && !this.isComponentDestroyed) {\r\n      this.connectWebSocket();\r\n    }\r\n  },\r\n  \r\n  deactivated() {\r\n    // 组件被缓存时暂停连接\r\n    this.pauseConnection();\r\n  },\r\n  methods: {\r\n    initCharts() {\r\n      this.$nextTick(() => {\r\n        this.initTpsChart();\r\n        this.initResponseChart();\r\n        this.initCpuChart();\r\n        this.initMemoryChart();\r\n        this.initNetworkChart();\r\n      });\r\n    },\r\n\r\n    initTpsChart() {\r\n      const chartDom = this.$refs.tpsChart;\r\n      if (!chartDom) return;\r\n      \r\n      this.charts.tps = echarts.init(chartDom);\r\n      const option = {\r\n        title: { text: 'TPS 趋势', left: 'center' },\r\n        tooltip: { trigger: 'axis' },\r\n        xAxis: { type: 'category', data: [] },\r\n        yAxis: { type: 'value', name: 'TPS' },\r\n        series: [{\r\n          name: 'TPS',\r\n          type: 'line',\r\n          smooth: true,\r\n          data: [],\r\n          lineStyle: { color: '#409EFF' },\r\n          areaStyle: { color: 'rgba(64, 158, 255, 0.3)' }\r\n        }]\r\n      };\r\n      this.charts.tps.setOption(option);\r\n    },\r\n\r\n    initResponseChart() {\r\n      const chartDom = this.$refs.responseChart;\r\n      if (!chartDom) return;\r\n      \r\n      this.charts.response = echarts.init(chartDom);\r\n      const option = {\r\n        title: { text: '响应时间', left: 'center' },\r\n        tooltip: { trigger: 'axis' },\r\n        xAxis: { type: 'category', data: [] },\r\n        yAxis: { type: 'value', name: '毫秒' },\r\n        series: [{\r\n          name: '平均响应时间',\r\n          type: 'line',\r\n          smooth: true,\r\n          data: [],\r\n          lineStyle: { color: '#67C23A' }\r\n        }]\r\n      };\r\n      this.charts.response.setOption(option);\r\n    },\r\n\r\n    initCpuChart() {\r\n      const chartDom = this.$refs.cpuChart;\r\n      if (!chartDom) return;\r\n      \r\n      this.charts.cpu = echarts.init(chartDom);\r\n      const option = {\r\n        tooltip: { trigger: 'axis' },\r\n        xAxis: { type: 'category', data: [] },\r\n        yAxis: { type: 'value', name: '%', max: 100 },\r\n        series: [{\r\n          name: 'CPU',\r\n          type: 'line',\r\n          smooth: true,\r\n          data: [],\r\n          lineStyle: { color: '#E6A23C' },\r\n          areaStyle: { color: 'rgba(230, 162, 60, 0.3)' }\r\n        }]\r\n      };\r\n      this.charts.cpu.setOption(option);\r\n    },\r\n\r\n    initMemoryChart() {\r\n      const chartDom = this.$refs.memoryChart;\r\n      if (!chartDom) return;\r\n      \r\n      this.charts.memory = echarts.init(chartDom);\r\n      const option = {\r\n        tooltip: { trigger: 'axis' },\r\n        xAxis: { type: 'category', data: [] },\r\n        yAxis: { type: 'value', name: '%', max: 100 },\r\n        series: [{\r\n          name: '内存',\r\n          type: 'line',\r\n          smooth: true,\r\n          data: [],\r\n          lineStyle: { color: '#F56C6C' },\r\n          areaStyle: { color: 'rgba(245, 108, 108, 0.3)' }\r\n        }]\r\n      };\r\n      this.charts.memory.setOption(option);\r\n    },\r\n\r\n    initNetworkChart() {\r\n      const chartDom = this.$refs.networkChart;\r\n      if (!chartDom) return;\r\n      \r\n      this.charts.network = echarts.init(chartDom);\r\n      const option = {\r\n        tooltip: { trigger: 'axis' },\r\n        xAxis: { type: 'category', data: [] },\r\n        yAxis: { type: 'value', name: 'MB' },\r\n        series: [\r\n          {\r\n            name: '接收',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: [],\r\n            lineStyle: { color: '#909399' }\r\n          },\r\n          {\r\n            name: '发送',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: [],\r\n            lineStyle: { color: '#909399', type: 'dashed' }\r\n          }\r\n        ]\r\n      };\r\n      this.charts.network.setOption(option);\r\n    },\r\n\r\n    connectWebSocket() {\r\n      // 如果组件已销毁，不进行连接\r\n      if (this.isComponentDestroyed) return;\r\n      \r\n      if (!this.currentTaskId) {\r\n        ElMessage.error('未找到任务ID');\r\n        return;\r\n      }\r\n\r\n      // 清理现有连接\r\n      this.cleanupWebSocket();\r\n\r\n      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\r\n      const wsUrl = `${wsProtocol}//${window.location.host}/ws/performance/monitor/${this.currentTaskId}/`;\r\n      \r\n      try {\r\n        this.websocket = new WebSocket(wsUrl);\r\n        \r\n        this.websocket.onopen = this.handleWebSocketOpen;\r\n        this.websocket.onmessage = this.handleWebSocketMessage;\r\n        this.websocket.onclose = this.handleWebSocketClose;\r\n        this.websocket.onerror = this.handleWebSocketError;\r\n        \r\n        // 设置连接超时\r\n        this.connectionTimeout = setTimeout(() => {\r\n          if (this.websocket && this.websocket.readyState === WebSocket.CONNECTING) {\r\n            this.websocket.close();\r\n            this.handleConnectionTimeout();\r\n          }\r\n        }, 10000);\r\n        \r\n      } catch (error) {\r\n        console.error('WebSocket连接创建失败:', error);\r\n        this.handleConnectionError(error);\r\n      }\r\n    },\r\n\r\n    handleWebSocketOpen() {\r\n      if (this.isComponentDestroyed) return;\r\n      \r\n      this.connectionStatus = 'connected';\r\n      this.reconnectAttempts = 0;\r\n      this.clearReconnectTimer();\r\n      \r\n      ElMessage.success('WebSocket连接成功');\r\n      \r\n      // 清理连接超时\r\n      if (this.connectionTimeout) {\r\n        clearTimeout(this.connectionTimeout);\r\n        this.connectionTimeout = null;\r\n      }\r\n      \r\n      // 启动心跳检测\r\n      this.startHeartbeat();\r\n      \r\n      // 获取当前状态\r\n      this.getCurrentStatus();\r\n    },\r\n\r\n    handleWebSocketMessage(event) {\r\n      if (this.isComponentDestroyed) return;\r\n      \r\n      try {\r\n        const data = JSON.parse(event.data);\r\n        this.processWebSocketMessage(data);\r\n      } catch (error) {\r\n        console.error('WebSocket消息解析失败:', error);\r\n      }\r\n    },\r\n\r\n    handleWebSocketClose(event) {\r\n      if (this.isComponentDestroyed) return;\r\n      \r\n      this.connectionStatus = 'disconnected';\r\n      this.isMonitoring = false;\r\n      this.stopHeartbeat();\r\n      \r\n      // 清理连接超时\r\n      if (this.connectionTimeout) {\r\n        clearTimeout(this.connectionTimeout);\r\n        this.connectionTimeout = null;\r\n      }\r\n      \r\n      console.log('WebSocket连接关闭:', event.code, event.reason);\r\n      \r\n      // 只有在非正常关闭时才尝试重连\r\n      if (event.code !== 1000 && event.code !== 1001) {\r\n        ElMessage.warning('WebSocket连接断开');\r\n        this.attemptReconnect();\r\n      }\r\n    },\r\n\r\n    handleWebSocketError(error) {\r\n      if (this.isComponentDestroyed) return;\r\n      \r\n      console.error('WebSocket错误:', error);\r\n      this.connectionStatus = 'disconnected';\r\n      \r\n      // 清理连接超时\r\n      if (this.connectionTimeout) {\r\n        clearTimeout(this.connectionTimeout);\r\n        this.connectionTimeout = null;\r\n      }\r\n    },\r\n\r\n    handleConnectionTimeout() {\r\n      console.warn('WebSocket连接超时');\r\n      ElMessage.error('WebSocket连接超时');\r\n      this.connectionStatus = 'disconnected';\r\n      this.attemptReconnect();\r\n    },\r\n\r\n    handleConnectionError(error) {\r\n      console.error('WebSocket连接错误:', error);\r\n      ElMessage.error('WebSocket连接错误');\r\n      this.connectionStatus = 'disconnected';\r\n      this.attemptReconnect();\r\n    },\r\n\r\n    processWebSocketMessage(data) {\r\n      // 心跳响应\r\n      if (data.type === 'pong') {\r\n        this.lastPongTime = Date.now();\r\n        return;\r\n      }\r\n      \r\n      switch (data.type) {\r\n        case 'connection_established':\r\n          console.log('连接建立:', data.message);\r\n          break;\r\n        case 'current_status':\r\n          this.updateCurrentData(data.data);\r\n          break;\r\n        case 'performance_update':\r\n          this.updatePerformanceData(data.data);\r\n          break;\r\n        case 'test_started':\r\n          ElMessage.success('性能测试已开始');\r\n          break;\r\n        case 'test_completed':\r\n          ElMessage.success('性能测试已完成');\r\n          this.isMonitoring = false;\r\n          break;\r\n        case 'test_failed':\r\n          ElMessage.error('性能测试失败: ' + data.error);\r\n          this.isMonitoring = false;\r\n          break;\r\n        case 'error':\r\n          ElMessage.error(data.message);\r\n          break;\r\n        default:\r\n          console.warn('未知的WebSocket消息类型:', data.type);\r\n      }\r\n    },\r\n\r\n    attemptReconnect() {\r\n      if (this.isComponentDestroyed || this.reconnectAttempts >= this.maxReconnectAttempts) {\r\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\r\n          ElMessage.error('WebSocket重连失败，已达到最大重试次数');\r\n        }\r\n        return;\r\n      }\r\n\r\n      this.clearReconnectTimer();\r\n      \r\n      // 使用退避策略计算重连延迟\r\n      const backoffIndex = Math.min(this.reconnectAttempts, this.connectionRetryBackoff.length - 1);\r\n      const delay = this.connectionRetryBackoff[backoffIndex];\r\n      \r\n      console.log(`准备第 ${this.reconnectAttempts + 1} 次重连，延迟 ${delay}ms`);\r\n      \r\n      this.reconnectTimer = setTimeout(() => {\r\n        if (!this.isComponentDestroyed) {\r\n          this.reconnectAttempts++;\r\n          this.connectWebSocket();\r\n        }\r\n      }, delay);\r\n    },\r\n\r\n    startHeartbeat() {\r\n      this.stopHeartbeat();\r\n      \r\n      this.heartbeatTimer = setInterval(() => {\r\n        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\r\n          this.websocket.send(JSON.stringify({ type: 'ping' }));\r\n        }\r\n      }, this.heartbeatInterval);\r\n    },\r\n\r\n    stopHeartbeat() {\r\n      if (this.heartbeatTimer) {\r\n        clearInterval(this.heartbeatTimer);\r\n        this.heartbeatTimer = null;\r\n      }\r\n    },\r\n\r\n    clearReconnectTimer() {\r\n      if (this.reconnectTimer) {\r\n        clearTimeout(this.reconnectTimer);\r\n        this.reconnectTimer = null;\r\n      }\r\n    },\r\n\r\n    clearAllTimers() {\r\n      this.clearReconnectTimer();\r\n      this.stopHeartbeat();\r\n      \r\n      if (this.connectionTimeout) {\r\n        clearTimeout(this.connectionTimeout);\r\n        this.connectionTimeout = null;\r\n      }\r\n    },\r\n\r\n    cleanupWebSocket() {\r\n      if (this.websocket) {\r\n        // 移除事件监听器防止内存泄漏\r\n        this.websocket.onopen = null;\r\n        this.websocket.onmessage = null;\r\n        this.websocket.onclose = null;\r\n        this.websocket.onerror = null;\r\n        \r\n        // 关闭连接\r\n        if (this.websocket.readyState === WebSocket.OPEN || \r\n            this.websocket.readyState === WebSocket.CONNECTING) {\r\n          this.websocket.close(1000, 'Component cleanup');\r\n        }\r\n        \r\n        this.websocket = null;\r\n      }\r\n      \r\n      this.stopHeartbeat();\r\n      this.clearReconnectTimer();\r\n    },\r\n\r\n    pauseConnection() {\r\n      // 暂停连接但不清理资源（用于keep-alive）\r\n      if (this.websocket) {\r\n        this.websocket.close(1000, 'Component deactivated');\r\n      }\r\n      this.stopHeartbeat();\r\n      this.clearReconnectTimer();\r\n    },\r\n\r\n    // 页面可见性变化处理\r\n    handleVisibilityChange() {\r\n      if (this.isComponentDestroyed) return;\r\n      \r\n      if (document.hidden) {\r\n        // 页面隐藏时暂停连接\r\n        this.pauseConnection();\r\n      } else {\r\n        // 页面显示时恢复连接\r\n        if (this.connectionStatus === 'disconnected') {\r\n          this.reconnectAttempts = 0;\r\n          this.connectWebSocket();\r\n        }\r\n      }\r\n    },\r\n\r\n    // 窗口焦点处理\r\n    handleWindowFocus() {\r\n      if (this.isComponentDestroyed) return;\r\n      \r\n      if (this.connectionStatus === 'disconnected') {\r\n        this.reconnectAttempts = 0;\r\n        this.connectWebSocket();\r\n      }\r\n    },\r\n\r\n    handleWindowBlur() {\r\n      // 窗口失焦时可以选择性地暂停某些操作\r\n      // 这里保持连接，只是记录日志\r\n      console.log('窗口失去焦点');\r\n    },\r\n\r\n    disconnectWebSocket() {\r\n      // 向后兼容的方法\r\n      this.cleanupWebSocket();\r\n    },\r\n\r\n    updateCurrentData(data) {\r\n      if (!data || this.isComponentDestroyed) return;\r\n      \r\n      // 深拷贝数据以防止引用导致的内存泄漏\r\n      this.currentData = { ...data };\r\n      \r\n      // 添加到历史数据，使用优化的内存管理\r\n      const timestamp = new Date().toISOString();\r\n      const newDataPoint = {\r\n        timestamp,\r\n        avgTps: data.avg_tps || 0,\r\n        avgResponseTime: data.avgResponseTime || 0,\r\n        totalRequests: data.total_requests || 0,\r\n        errorRate: data.error_rate || 0,\r\n        cpuPercent: this.currentSystem.cpu?.percent || 0,\r\n        memoryPercent: this.currentSystem.memory?.percent || 0,\r\n        networkRecv: this.currentSystem.network?.bytes_recv || 0,\r\n        networkSent: this.currentSystem.network?.bytes_sent || 0\r\n      };\r\n      \r\n      // 使用更高效的数组管理\r\n      this.addDataPoint(newDataPoint);\r\n      \r\n      // 批量更新图表，减少重绘频率\r\n      this.scheduleChartUpdate();\r\n    },\r\n\r\n    updatePerformanceData(data) {\r\n      if (!data || this.isComponentDestroyed) return;\r\n      \r\n      if (data.type === 'system_resources') {\r\n        // 深拷贝系统数据\r\n        this.currentSystem = { ...data.data };\r\n      } else {\r\n        // 合并性能数据\r\n        this.currentData = { ...this.currentData, ...data };\r\n      }\r\n      \r\n      this.scheduleChartUpdate();\r\n    },\r\n\r\n    addDataPoint(dataPoint) {\r\n      this.recentData.push(dataPoint);\r\n      \r\n      // 当数据点超过最大限制时，批量删除旧数据而不是逐个删除\r\n      if (this.recentData.length > this.maxDataPoints) {\r\n        const excessData = this.recentData.length - this.maxDataPoints;\r\n        this.recentData.splice(0, excessData);\r\n      }\r\n    },\r\n\r\n    scheduleChartUpdate() {\r\n      // 使用防抖来减少图表更新频率\r\n      if (this.chartUpdateTimer) {\r\n        clearTimeout(this.chartUpdateTimer);\r\n      }\r\n      \r\n      this.chartUpdateTimer = setTimeout(() => {\r\n        if (!this.isComponentDestroyed) {\r\n          this.updateCharts();\r\n        }\r\n      }, 100); // 100ms防抖\r\n    },\r\n\r\n    updateCharts() {\r\n      if (this.isComponentDestroyed || !this.recentData.length) return;\r\n      \r\n      // 批量处理数据，减少DOM操作\r\n      const times = this.recentData.map(d => new Date(d.timestamp).toLocaleTimeString());\r\n      \r\n      // 使用批处理更新所有图表\r\n      this.updateChartsInBatch(times);\r\n    },\r\n\r\n    updateChartsInBatch(times) {\r\n      const chartUpdates = [\r\n        {\r\n          chart: this.charts.tps,\r\n          data: this.recentData.map(d => d.avgTps)\r\n        },\r\n        {\r\n          chart: this.charts.response,\r\n          data: this.recentData.map(d => d.avgResponseTime)\r\n        },\r\n        {\r\n          chart: this.charts.cpu,\r\n          data: this.recentData.map(d => d.cpuPercent)\r\n        },\r\n        {\r\n          chart: this.charts.memory,\r\n          data: this.recentData.map(d => d.memoryPercent)\r\n        }\r\n      ];\r\n\r\n      // 批量更新图表以提高性能\r\n      chartUpdates.forEach(({ chart, data }) => {\r\n        if (chart && chart.setOption) {\r\n          try {\r\n            chart.setOption({\r\n              xAxis: { data: times },\r\n              series: [{ data: data }]\r\n            }, false, true); // 使用notMerge=false, lazyUpdate=true优化性能\r\n          } catch (error) {\r\n            console.warn('图表更新失败:', error);\r\n          }\r\n        }\r\n      });\r\n      \r\n      // 单独更新网络图表（因为有两个数据系列）\r\n      this.updateNetworkChart();\r\n    },\r\n\r\n    // 更新网络图表\r\n    updateNetworkChart() {\r\n      if (!this.charts.network) return;\r\n      \r\n      const times = this.recentData.map(d => new Date(d.timestamp).toLocaleTimeString());\r\n      const networkRecvData = this.recentData.map(d => {\r\n        const bytes = d.networkRecv || this.currentSystem.network?.bytes_recv || 0;\r\n        return (bytes / 1024 / 1024).toFixed(2); // 转换为MB\r\n      });\r\n      const networkSentData = this.recentData.map(d => {\r\n        const bytes = d.networkSent || this.currentSystem.network?.bytes_sent || 0;\r\n        return (bytes / 1024 / 1024).toFixed(2); // 转换为MB\r\n      });\r\n      \r\n      this.charts.network.setOption({\r\n        xAxis: { data: times },\r\n        series: [\r\n          { data: networkRecvData },\r\n          { data: networkSentData }\r\n        ]\r\n      });\r\n    },\r\n\r\n    toggleMonitoring() {\r\n      if (this.connectionStatus !== 'connected') {\r\n        ElMessage.error('WebSocket未连接');\r\n        return;\r\n      }\r\n      \r\n      this.loading = true;\r\n      const action = this.isMonitoring ? 'stop_monitoring' : 'start_monitoring';\r\n      \r\n      this.websocket.send(JSON.stringify({ type: action }));\r\n      \r\n      setTimeout(() => {\r\n        this.isMonitoring = !this.isMonitoring;\r\n        this.loading = false;\r\n      }, 1000);\r\n    },\r\n\r\n    getCurrentStatus() {\r\n      if (this.connectionStatus === 'connected') {\r\n        this.websocket.send(JSON.stringify({ type: 'get_current_status' }));\r\n      }\r\n    },\r\n\r\n    refreshData() {\r\n      this.getCurrentStatus();\r\n      ElMessage.success('数据已刷新');\r\n    },\r\n\r\n    clearData() {\r\n      // 清空数据数组，释放内存\r\n      this.recentData.length = 0;\r\n      this.currentData = {};\r\n      this.currentSystem = {};\r\n      \r\n      // 立即更新图表\r\n      this.updateCharts();\r\n      ElMessage.success('数据已清空');\r\n    },\r\n\r\n    formatBytes(bytes) {\r\n      if (bytes === 0) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    },\r\n\r\n    destroyCharts() {\r\n      // 清理图表更新定时器\r\n      if (this.chartUpdateTimer) {\r\n        clearTimeout(this.chartUpdateTimer);\r\n        this.chartUpdateTimer = null;\r\n      }\r\n      \r\n      // 销毁所有图表实例并清理引用\r\n      Object.keys(this.charts).forEach(key => {\r\n        const chart = this.charts[key];\r\n        if (chart && typeof chart.dispose === 'function') {\r\n          try {\r\n            chart.dispose();\r\n          } catch (error) {\r\n            console.warn(`销毁图表 ${key} 时出错:`, error);\r\n          }\r\n        }\r\n      });\r\n      \r\n      // 清空图表对象\r\n      this.charts = {};\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.performance-monitor {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: calc(100vh - 100px);\r\n}\r\n\r\n.monitor-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: white;\r\n  padding: 16px 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.header-info h2 {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.monitor-controls {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.monitor-panels {\r\n  flex: 1;\r\n}\r\n\r\n.monitor-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-weight: 600;\r\n}\r\n\r\n.chart-container {\r\n  height: 300px;\r\n  width: 100%;\r\n}\r\n\r\n:deep(.el-card__header) {\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n:deep(.el-card__body) {\r\n  padding: 20px;\r\n}\r\n\r\n.connection-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n</style>", "import { render } from \"./PerformanceMonitor.vue?vue&type=template&id=e2def50a&scoped=true\"\nimport script from \"./PerformanceMonitor.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceMonitor.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceMonitor.vue?vue&type=style&index=0&id=e2def50a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-e2def50a\"]])\n\nexport default __exports__"], "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_tag", "type", "$data", "connectionStatus", "effect", "_component_el_icon", "_component_Connection", "_toDisplayString", "_hoisted_5", "_component_el_button_group", "_component_el_button", "isMonitoring", "onClick", "$options", "toggleMonitoring", "loading", "_createBlock", "_component_VideoPause", "key", "_component_VideoPlay", "refreshData", "_component_Refresh", "_hoisted_6", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_card", "header", "_withCtx", "_hoisted_7", "size", "currentData", "avgTps", "_hoisted_8", "_hoisted_9", "avgResponseTime", "_hoisted_10", "style", "_hoisted_11", "currentSystem", "cpu", "percent", "toFixed", "_hoisted_12", "_hoisted_13", "memory", "_hoisted_14", "_hoisted_15", "formatBytes", "network", "bytes_recv", "_hoisted_16", "_hoisted_17", "clearData", "_cache", "_component_el_table", "data", "recentData", "slice", "stripe", "_component_el_table_column", "prop", "label", "width", "default", "row", "Date", "timestamp", "toLocaleTimeString", "name", "components", "Connection", "VideoPlay", "VideoPause", "Refresh", "websocket", "charts", "maxDataPoints", "reconnectAttempts", "maxReconnectAttempts", "reconnectInterval", "reconnectTimer", "heartbeatTimer", "heartbeatInterval", "isComponentDestroyed", "connectionRetryBackoff", "computed", "mapState", "currentTaskId", "state", "$route", "params", "taskId", "mounted", "this", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectWebSocket", "document", "addEventListener", "handleVisibilityChange", "window", "handleWindowFocus", "handleWindowBlur", "beforeUnmount", "cleanupWebSocket", "clearAllTimers", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "activated", "deactivated", "pauseConnection", "methods", "$nextTick", "initTpsChart", "initResponseChart", "initCpuChart", "initMemoryChart", "initNetworkChart", "chartDom", "$refs", "tpsChart", "tps", "echarts", "option", "title", "text", "left", "tooltip", "trigger", "xAxis", "yAxis", "series", "smooth", "lineStyle", "color", "areaStyle", "setOption", "responseChart", "response", "cpuChart", "max", "memoryChart", "networkChart", "ElMessage", "error", "wsProtocol", "location", "protocol", "wsUrl", "host", "WebSocket", "onopen", "handleWebSocketOpen", "onmessage", "handleWebSocketMessage", "onclose", "handleWebSocketClose", "onerror", "handleWebSocketError", "connectionTimeout", "setTimeout", "readyState", "CONNECTING", "close", "handleConnectionTimeout", "console", "handleConnectionError", "clearReconnectTimer", "success", "clearTimeout", "startHeartbeat", "getCurrentStatus", "event", "JSON", "parse", "processWebSocketMessage", "stopHeartbeat", "log", "code", "reason", "warning", "attemptReconnect", "warn", "message", "updateCurrentData", "updatePerformanceData", "lastPongTime", "now", "backoffIndex", "Math", "min", "length", "delay", "setInterval", "OPEN", "send", "stringify", "clearInterval", "hidden", "disconnectWebSocket", "toISOString", "newDataPoint", "avg_tps", "totalRequests", "total_requests", "errorRate", "error_rate", "cpuPer<PERSON>", "memoryPercent", "networkRecv", "networkSent", "bytes_sent", "addDataPoint", "scheduleChartUpdate", "dataPoint", "push", "excessData", "splice", "chartUpdateTimer", "updateCharts", "times", "map", "d", "updateChartsInBatch", "chartUpdates", "chart", "for<PERSON>ach", "updateNetworkChart", "networkRecvData", "bytes", "networkSentData", "action", "k", "sizes", "i", "floor", "parseFloat", "pow", "Object", "keys", "dispose", "__exports__", "render"], "sourceRoot": ""}