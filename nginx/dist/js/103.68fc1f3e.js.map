{"version": 3, "file": "js/103.68fc1f3e.js", "mappings": "wTAEA,MAAMA,EAAgB,2BAChBC,EAAeA,CAACC,EAAOC,EAAUC,EAAiBC,EAAW,MACjE,MAAMC,EAAiBJ,EAAMK,MAAM,KACnC,GAA0B,MAAtBL,EAAMM,MAAM,EAAG,GAAY,CAC7B,GAAIF,EAAeG,OAAS,GAAKH,EAAeG,OAAS,EACvD,OAAO,KAETJ,EAAWC,EAAeI,QAAQF,MAAM,EAC1C,CACA,GAAIF,EAAeG,OAAS,IAAMH,EAAeG,OAC/C,OAAO,KAET,GAAIH,EAAeG,OAAS,EAAG,CAC7B,MAAME,EAAQL,EAAeM,MACvBC,EAASP,EAAeM,MACxBE,EAAS,CAEbT,SAAUC,EAAeG,OAAS,EAAIH,EAAe,GAAKD,EAC1DQ,SACAE,KAAMJ,GAER,OAAOR,IAAaa,EAAiBF,GAAU,KAAOA,CACxD,CACA,MAAMC,EAAOT,EAAe,GACtBW,EAAgBF,EAAKR,MAAM,KACjC,GAAIU,EAAcR,OAAS,EAAG,CAC5B,MAAMK,EAAS,CACbT,WACAQ,OAAQI,EAAcP,QACtBK,KAAME,EAAcC,KAAK,MAE3B,OAAOf,IAAaa,EAAiBF,GAAU,KAAOA,CACxD,CACA,GAAIV,GAAgC,KAAbC,EAAiB,CACtC,MAAMS,EAAS,CACbT,WACAQ,OAAQ,GACRE,QAEF,OAAOZ,IAAaa,EAAiBF,EAAQV,GAAmB,KAAOU,CACzE,CACA,OAAO,MAEHE,EAAmBA,CAACG,EAAMf,MACzBe,OAKHf,GAAmC,KAAhBe,EAAKN,QAAmBM,EAAKN,UAAaM,EAAKJ,MAGhEK,EAAwBC,OAAOC,OACnC,CACEC,KAAM,EACNC,IAAK,EACLC,MAAO,GACPC,OAAQ,KAGNC,EAA6BN,OAAOC,OAAO,CAC/CM,OAAQ,EACRC,OAAO,EACPC,OAAO,IAEHC,EAAmBV,OAAOC,OAAO,IAClCF,KACAO,IAECK,EAA2BX,OAAOC,OAAO,IAC1CS,EACHE,KAAM,GACNC,QAAQ,IAGV,SAASC,EAAyBC,EAAMC,GACtC,MAAMvB,EAAS,CAAC,GACXsB,EAAKN,SAAWO,EAAKP,QACxBhB,EAAOgB,OAAQ,IAEZM,EAAKP,SAAWQ,EAAKR,QACxBf,EAAOe,OAAQ,GAEjB,MAAMD,IAAWQ,EAAKR,QAAU,IAAMS,EAAKT,QAAU,IAAM,EAI3D,OAHIA,IACFd,EAAOc,OAASA,GAEXd,CACT,CAEA,SAASwB,EAAcC,EAAQC,GAC7B,MAAM1B,EAASqB,EAAyBI,EAAQC,GAChD,IAAK,MAAMC,KAAOT,EACZS,KAAOd,EACLc,KAAOF,KAAYE,KAAO3B,KAC5BA,EAAO2B,GAAOd,EAA2Bc,IAElCA,KAAOD,EAChB1B,EAAO2B,GAAOD,EAAMC,GACXA,KAAOF,IAChBzB,EAAO2B,GAAOF,EAAOE,IAGzB,OAAO3B,CACT,CAEA,SAAS4B,EAAaC,EAAMC,GAC1B,MAAMC,EAAQF,EAAKE,MACbC,EAAUH,EAAKG,SAA2BzB,OAAO0B,OAAO,MACxDC,EAA2B3B,OAAO0B,OAAO,MAC/C,SAASE,EAAQlC,GACf,GAAI8B,EAAM9B,GACR,OAAOiC,EAASjC,GAAQ,GAE1B,KAAMA,KAAQiC,GAAW,CACvBA,EAASjC,GAAQ,KACjB,MAAMwB,EAASO,EAAQ/B,IAAS+B,EAAQ/B,GAAMwB,OACxCrC,EAAQqC,GAAUU,EAAQV,GAC5BrC,IACF8C,EAASjC,GAAQ,CAACwB,GAAQW,OAAOhD,GAErC,CACA,OAAO8C,EAASjC,EAClB,CAEA,OADCM,OAAO8B,KAAKN,GAAOK,OAAO7B,OAAO8B,KAAKL,IAAWM,QAAQH,GACnDD,CACT,CAEA,SAASK,EAAoBV,EAAM5B,EAAMuC,GACvC,MAAMT,EAAQF,EAAKE,MACbC,EAAUH,EAAKG,SAA2BzB,OAAO0B,OAAO,MAC9D,IAAIQ,EAAe,CAAC,EACpB,SAASC,EAAM7C,GACb4C,EAAejB,EACbO,EAAMlC,IAAUmC,EAAQnC,GACxB4C,EAEJ,CAGA,OAFAC,EAAMzC,GACNuC,EAAKF,QAAQI,GACNlB,EAAcK,EAAMY,EAC7B,CAEA,SAASE,EAAad,EAAMe,GAC1B,MAAMd,EAAQ,GACd,GAAoB,kBAATD,GAA2C,kBAAfA,EAAKE,MAC1C,OAAOD,EAELD,EAAKgB,qBAAqBC,OAC5BjB,EAAKgB,UAAUP,QAASrC,IACtB2C,EAAS3C,EAAM,MACf6B,EAAMiB,KAAK9C,KAGf,MAAMuC,EAAOZ,EAAaC,GAC1B,IAAK,MAAM5B,KAAQuC,EAAM,CACvB,MAAMQ,EAAOR,EAAKvC,GACd+C,IACFJ,EAAS3C,EAAMsC,EAAoBV,EAAM5B,EAAM+C,IAC/ClB,EAAMiB,KAAK9C,GAEf,CACA,OAAO6B,CACT,CAEA,MAAMmB,EAA2B,CAC/B1D,SAAU,GACVyC,QAAS,CAAC,EACVa,UAAW,CAAC,KACTvC,GAEL,SAAS4C,EAAmBF,EAAMG,GAChC,IAAK,MAAMC,KAAQD,EACjB,GAAIC,KAAQJ,UAAeA,EAAKI,YAAiBD,EAASC,GACxD,OAAO,EAGX,OAAO,CACT,CACA,SAASC,EAAuBC,GAC9B,GAAmB,kBAARA,GAA4B,OAARA,EAC7B,OAAO,KAET,MAAMzB,EAAOyB,EACb,GAA2B,kBAAhBzB,EAAK9B,SAAwBuD,EAAIvB,OAA8B,kBAAduB,EAAIvB,MAC9D,OAAO,KAET,IAAKmB,EAAmBI,EAAKL,GAC3B,OAAO,KAET,MAAMlB,EAAQF,EAAKE,MACnB,IAAK,MAAM9B,KAAQ8B,EAAO,CACxB,MAAM1B,EAAO0B,EAAM9B,GACnB,IAEGA,GACoB,kBAAdI,EAAKc,OACX+B,EACC7C,EACAa,GAGF,OAAO,IAEX,CACA,MAAMc,EAAUH,EAAKG,SAA2BzB,OAAO0B,OAAO,MAC9D,IAAK,MAAMhC,KAAQ+B,EAAS,CAC1B,MAAM3B,EAAO2B,EAAQ/B,GACfwB,EAASpB,EAAKoB,OACpB,IAEGxB,GACiB,kBAAXwB,IAAwBM,EAAMN,KAAYO,EAAQP,KACxDyB,EACC7C,EACAa,GAGF,OAAO,IAEX,CACA,OAAOW,CACT,CAEA,MAAM0B,EAA8BhD,OAAO0B,OAAO,MAClD,SAASuB,EAAWjE,EAAUQ,GAC5B,MAAO,CACLR,WACAQ,SACAgC,MAAuBxB,OAAO0B,OAAO,MACrCwB,QAAyB,IAAIC,IAEjC,CACA,SAASC,EAAWpE,EAAUQ,GAC5B,MAAM6D,EAAkBL,EAAYhE,KAAcgE,EAAYhE,GAA4BgB,OAAO0B,OAAO,OACxG,OAAO2B,EAAgB7D,KAAY6D,EAAgB7D,GAAUyD,EAAWjE,EAAUQ,GACpF,CACA,SAAS8D,EAAWC,EAASjC,GAC3B,OAAKwB,EAAuBxB,GAGrBc,EAAad,EAAM,CAAC5B,EAAMI,KAC3BA,EACFyD,EAAQ/B,MAAM9B,GAAQI,EAEtByD,EAAQL,QAAQM,IAAI9D,KANf,EASX,CACA,SAAS+D,EAAiBF,EAAS7D,EAAMI,GACvC,IACE,GAAyB,kBAAdA,EAAKc,KAEd,OADA2C,EAAQ/B,MAAM9B,GAAQ,IAAKI,IACpB,CAEX,CAAE,MAAO4D,GACT,CACA,OAAO,CACT,CAkBA,IAAIC,GAAc,EAClB,SAASC,EAAiBC,GAIxB,MAHqB,mBAAVA,IACTF,EAAcE,GAETF,CACT,CACA,SAASG,EAAYpE,GACnB,MAAMI,EAAuB,kBAATJ,EAAoBd,EAAac,GAAM,EAAMiE,GAAejE,EAChF,GAAII,EAAM,CACR,MAAMyD,EAAUH,EAAWtD,EAAKd,SAAUc,EAAKN,QACzCuE,EAAWjE,EAAKJ,KACtB,OAAO6D,EAAQ/B,MAAMuC,KAAcR,EAAQL,QAAQc,IAAID,GAAY,UAAO,EAC5E,CACF,CACA,SAASE,EAAQvE,EAAM4B,GACrB,MAAMxB,EAAOlB,EAAac,GAAM,EAAMiE,GACtC,IAAK7D,EACH,OAAO,EAET,MAAMyD,EAAUH,EAAWtD,EAAKd,SAAUc,EAAKN,QAC/C,OAAI8B,EACKmC,EAAiBF,EAASzD,EAAKJ,KAAM4B,IAE5CiC,EAAQL,QAAQM,IAAI1D,EAAKJ,OAClB,EAEX,CACA,SAASwE,EAAc5C,EAAMtC,GAC3B,GAAoB,kBAATsC,EACT,OAAO,EAKT,GAHwB,kBAAbtC,IACTA,EAAWsC,EAAKtC,UAAY,IAE1B2E,IAAgB3E,IAAasC,EAAK9B,OAAQ,CAC5C,IAAI2E,GAAQ,EASZ,OARIrB,EAAuBxB,KACzBA,EAAK9B,OAAS,GACd4C,EAAad,EAAM,CAAC5B,EAAMI,KACpBmE,EAAQvE,EAAMI,KAChBqE,GAAQ,MAIPA,CACT,CACA,MAAM3E,EAAS8B,EAAK9B,OACpB,IAAKG,EAAiB,CACpBX,WACAQ,SACAE,KAAM,MAEN,OAAO,EAET,MAAM6D,EAAUH,EAAWpE,EAAUQ,GACrC,QAAS8D,EAAWC,EAASjC,EAC/B,CAYA,MAAM8C,EAAgCpE,OAAOC,OAAO,CAClDG,MAAO,KACPC,OAAQ,OAEJgE,EAA4BrE,OAAOC,OAAO,IAE3CmE,KAEA9D,IAGCgE,EAAa,4BACbC,EAAY,4BAClB,SAASC,EAAcC,EAAMC,EAAOC,GAClC,GAAc,IAAVD,EACF,OAAOD,EAGT,GADAE,EAAYA,GAAa,IACL,kBAATF,EACT,OAAOG,KAAKC,KAAKJ,EAAOC,EAAQC,GAAaA,EAE/C,GAAoB,kBAATF,EACT,OAAOA,EAET,MAAMK,EAAWL,EAAKvF,MAAMoF,GAC5B,GAAiB,OAAbQ,IAAsBA,EAAS1F,OACjC,OAAOqF,EAET,MAAMM,EAAW,GACjB,IAAIC,EAAOF,EAASzF,QAChB4F,EAAWV,EAAUW,KAAKF,GAC9B,MAAO,EAAM,CACX,GAAIC,EAAU,CACZ,MAAME,EAAMC,WAAWJ,GACnBK,MAAMF,GACRJ,EAASvC,KAAKwC,GAEdD,EAASvC,KAAKoC,KAAKC,KAAKM,EAAMT,EAAQC,GAAaA,EAEvD,MACEI,EAASvC,KAAKwC,GAGhB,GADAA,EAAOF,EAASzF,aACH,IAAT2F,EACF,OAAOD,EAASlF,KAAK,IAEvBoF,GAAYA,CACd,CACF,CAEA,SAASK,EAAaC,EAASC,EAAM,QACnC,IAAIC,EAAO,GACX,MAAMC,EAAQH,EAAQI,QAAQ,IAAMH,GACpC,MAAOE,GAAS,EAAG,CACjB,MAAME,EAAQL,EAAQI,QAAQ,IAAKD,GAC7BG,EAAMN,EAAQI,QAAQ,KAAOH,GACnC,IAAe,IAAXI,IAAyB,IAATC,EAClB,MAEF,MAAMC,EAASP,EAAQI,QAAQ,IAAKE,GACpC,IAAgB,IAAZC,EACF,MAEFL,GAAQF,EAAQpG,MAAMyG,EAAQ,EAAGC,GAAKE,OACtCR,EAAUA,EAAQpG,MAAM,EAAGuG,GAAOK,OAASR,EAAQpG,MAAM2G,EAAS,EACpE,CACA,MAAO,CACLL,OACAF,UAEJ,CACA,SAASS,EAAoBP,EAAMF,GACjC,OAAOE,EAAO,SAAWA,EAAO,UAAYF,EAAUA,CACxD,CACA,SAASU,EAAerF,EAAMgF,EAAOC,GACnC,MAAM3G,EAAQoG,EAAa1E,GAC3B,OAAOoF,EAAoB9G,EAAMuG,KAAMG,EAAQ1G,EAAMqG,QAAUM,EACjE,CAEA,MAAMK,EAAkBrH,GAAoB,UAAVA,GAA+B,cAAVA,GAAmC,SAAVA,EAChF,SAASsH,EAAUrG,EAAMsG,GACvB,MAAMC,EAAW,IACZ3F,KACAZ,GAECwG,EAAqB,IACtBjC,KACA+B,GAECG,EAAM,CACVrG,KAAMmG,EAASnG,KACfC,IAAKkG,EAASlG,IACdC,MAAOiG,EAASjG,MAChBC,OAAQgG,EAAShG,QAEnB,IAAIO,EAAOyF,EAASzF,KACpB,CAACyF,EAAUC,GAAoBvE,QAASyE,IACtC,MAAMC,EAAkB,GAClBhG,EAAQ+F,EAAM/F,MACdD,EAAQgG,EAAMhG,MACpB,IAkBIkG,EAlBAC,EAAWH,EAAMjG,OAuBrB,OAtBIE,EACED,EACFmG,GAAY,GAEZF,EAAgBjE,KACd,cAAgB+D,EAAInG,MAAQmG,EAAIrG,MAAM0G,WAAa,KAAO,EAAIL,EAAIpG,KAAKyG,WAAa,KAEtFH,EAAgBjE,KAAK,eACrB+D,EAAIpG,IAAMoG,EAAIrG,KAAO,GAEdM,IACTiG,EAAgBjE,KACd,cAAgB,EAAI+D,EAAIrG,MAAM0G,WAAa,KAAOL,EAAIlG,OAASkG,EAAIpG,KAAKyG,WAAa,KAEvFH,EAAgBjE,KAAK,eACrB+D,EAAIpG,IAAMoG,EAAIrG,KAAO,GAGnByG,EAAW,IACbA,GAAuC,EAA3B/B,KAAKiC,MAAMF,EAAW,IAEpCA,GAAsB,EACdA,GACN,KAAK,EACHD,EAAYH,EAAIlG,OAAS,EAAIkG,EAAIpG,IACjCsG,EAAgBK,QACd,aAAeJ,EAAUE,WAAa,IAAMF,EAAUE,WAAa,KAErE,MACF,KAAK,EACHH,EAAgBK,QACd,eAAiBP,EAAInG,MAAQ,EAAImG,EAAIrG,MAAM0G,WAAa,KAAOL,EAAIlG,OAAS,EAAIkG,EAAIpG,KAAKyG,WAAa,KAExG,MACF,KAAK,EACHF,EAAYH,EAAInG,MAAQ,EAAImG,EAAIrG,KAChCuG,EAAgBK,QACd,cAAgBJ,EAAUE,WAAa,IAAMF,EAAUE,WAAa,KAEtE,MAEAD,EAAW,IAAM,IACfJ,EAAIrG,OAASqG,EAAIpG,MACnBuG,EAAYH,EAAIrG,KAChBqG,EAAIrG,KAAOqG,EAAIpG,IACfoG,EAAIpG,IAAMuG,GAERH,EAAInG,QAAUmG,EAAIlG,SACpBqG,EAAYH,EAAInG,MAChBmG,EAAInG,MAAQmG,EAAIlG,OAChBkG,EAAIlG,OAASqG,IAGbD,EAAgBrH,SAClBwB,EAAOqF,EACLrF,EACA,iBAAmB6F,EAAgB5G,KAAK,KAAO,KAC/C,WAIN,MAAMkH,EAAsBT,EAAmBlG,MACzC4G,EAAuBV,EAAmBjG,OAC1C4G,EAAWV,EAAInG,MACf8G,EAAYX,EAAIlG,OACtB,IAAID,EACAC,EACwB,OAAxB0G,GACF1G,EAAkC,OAAzB2G,EAAgC,MAAiC,SAAzBA,EAAkCE,EAAYF,EAC/F5G,EAAQoE,EAAcnE,EAAQ4G,EAAWC,KAEzC9G,EAAgC,SAAxB2G,EAAiCE,EAAWF,EACpD1G,EAAkC,OAAzB2G,EAAgCxC,EAAcpE,EAAO8G,EAAYD,GAAqC,SAAzBD,EAAkCE,EAAYF,GAEtI,MAAMG,EAAa,CAAC,EACdC,EAAUA,CAACvE,EAAMhE,KAChBqH,EAAerH,KAClBsI,EAAWtE,GAAQhE,EAAM+H,aAG7BQ,EAAQ,QAAShH,GACjBgH,EAAQ,SAAU/G,GAClB,MAAMgH,EAAU,CAACd,EAAIrG,KAAMqG,EAAIpG,IAAK8G,EAAUC,GAE9C,OADAC,EAAWE,QAAUA,EAAQxH,KAAK,KAC3B,CACLsH,aACAE,UACAzG,OAEJ,CAEA,MAAM0G,EAAQ,gBACRC,EAAe,YAAcC,KAAKC,MAAMb,SAAS,KAAuB,SAAhBhC,KAAK8C,SAAsB,GAAGd,SAAS,IACrG,IAAIe,EAAU,EACd,SAASC,EAAWhH,EAAMpB,EAAS+H,GACjC,MAAMM,EAAM,GACZ,IAAIC,EACJ,MAAOA,EAAQR,EAAMS,KAAKnH,GACxBiH,EAAIrF,KAAKsF,EAAM,IAEjB,IAAKD,EAAIzI,OACP,OAAOwB,EAET,MAAMoH,EAAS,UAA4B,SAAhBpD,KAAK8C,SAAsBF,KAAKC,OAAOb,SAAS,IAY3E,OAXAiB,EAAI9F,QAASkG,IACX,MAAMC,EAA0B,oBAAX1I,EAAwBA,EAAOyI,GAAMzI,GAAUmI,KAAWf,WACzEuB,EAAYF,EAAGG,QAAQ,sBAAuB,QACpDxH,EAAOA,EAAKwH,QAGV,IAAIC,OAAO,WAAaF,EAAY,mBAAoB,KACxD,KAAOD,EAAQF,EAAS,QAG5BpH,EAAOA,EAAKwH,QAAQ,IAAIC,OAAOL,EAAQ,KAAM,IACtCpH,CACT,CAEA,MAAM2C,EAA0BvD,OAAO0B,OAAO,MAC9C,SAAS4G,EAAatJ,EAAUyD,GAC9Bc,EAAQvE,GAAYyD,CACtB,CACA,SAAS8F,EAAavJ,GACpB,OAAOuE,EAAQvE,IAAauE,EAAQ,GACtC,CAEA,SAASiF,EAAgBC,GACvB,IAAIC,EACJ,GAAgC,kBAArBD,EAAOC,UAChBA,EAAY,CAACD,EAAOC,gBAGpB,GADAA,EAAYD,EAAOC,YACbA,aAAqBnG,SAAWmG,EAAUtJ,OAC9C,OAAO,KAGX,MAAMK,EAAS,CAEbiJ,YAEAC,KAAMF,EAAOE,MAAQ,IAErBC,OAAQH,EAAOG,QAAU,IAEzBrI,OAAQkI,EAAOlI,QAAU,IAEzBsI,QAASJ,EAAOI,SAAW,IAE3BnB,QAA0B,IAAlBe,EAAOf,OAEfhC,MAAO+C,EAAO/C,OAAS,EAEvBoD,kBAA8C,IAA5BL,EAAOK,kBAE3B,OAAOrJ,CACT,CACA,MAAMsJ,EAAgC/I,OAAO0B,OAAO,MAC9CsH,EAAqB,CACzB,4BACA,0BAEIC,EAAc,GACpB,MAAOD,EAAmB5J,OAAS,EACC,IAA9B4J,EAAmB5J,QAGjBwF,KAAK8C,SAAW,GAFpBuB,EAAYzG,KAAKwG,EAAmB3J,SAKlC4J,EAAYzG,KAAKwG,EAAmBzJ,OAO1C,SAAS2J,EAAelK,EAAUmK,GAChC,MAAMC,EAASZ,EAAgBW,GAC/B,OAAe,OAAXC,IAGJL,EAAc/J,GAAYoK,GACnB,EACT,CACA,SAASC,EAAarK,GACpB,OAAO+J,EAAc/J,EACvB,CAbA+J,EAAc,IAAMP,EAAgB,CAClCE,UAAW,CAAC,8BAA8B7G,OAAOoH,KAiBnD,MAAMK,EAAcA,KAClB,IAAIjH,EACJ,IAEE,GADAA,EAAWkH,MACa,oBAAblH,EACT,OAAOA,CAEX,CAAE,MAAOqB,GACT,GAEF,IAAI8F,EAAcF,IAOlB,SAASG,GAAmBzK,EAAUQ,GACpC,MAAM4J,EAASC,EAAarK,GAC5B,IAAKoK,EACH,OAAO,EAET,IAAI3J,EACJ,GAAK2J,EAAOR,OAEL,CACL,IAAIc,EAAgB,EACpBN,EAAOV,UAAU3G,QAASU,IACxB,MAAMkH,EAAOlH,EACbiH,EAAgB9E,KAAKgF,IAAIF,EAAeC,EAAKvK,UAE/C,MAAMyK,EAAMrK,EAAS,eACrBC,EAAS2J,EAAOR,OAASc,EAAgBN,EAAOT,KAAKvJ,OAASyK,EAAIzK,MACpE,MATEK,EAAS,EAUX,OAAOA,CACT,CACA,SAASqK,GAAYC,GACnB,OAAkB,MAAXA,CACT,CACA,MAAMC,GAAUA,CAAChL,EAAUQ,EAAQgC,KACjC,MAAMyI,EAAU,GACVC,EAAYT,GAAmBzK,EAAUQ,GACzC2K,EAAO,QACb,IAAI1H,EAAO,CACT0H,OACAnL,WACAQ,SACAgC,MAAO,IAELpC,EAAS,EAgBb,OAfAoC,EAAMO,QAAQ,CAACrC,EAAMgG,KACnBtG,GAAUM,EAAKN,OAAS,EACpBA,GAAU8K,GAAaxE,EAAQ,IACjCuE,EAAQzH,KAAKC,GACbA,EAAO,CACL0H,OACAnL,WACAQ,SACAgC,MAAO,IAETpC,EAASM,EAAKN,QAEhBqD,EAAKjB,MAAMgB,KAAK9C,KAElBuK,EAAQzH,KAAKC,GACNwH,GAET,SAASG,GAAQpL,GACf,GAAwB,kBAAbA,EAAuB,CAChC,MAAMoK,EAASC,EAAarK,GAC5B,GAAIoK,EACF,OAAOA,EAAOT,IAElB,CACA,MAAO,GACT,CACA,MAAM0B,GAAOA,CAACV,EAAMW,EAAQjI,KAC1B,IAAKmH,EAEH,YADAnH,EAAS,QAAS,KAGpB,IAAIsG,EAAOyB,GAAQE,EAAOtL,UAC1B,OAAQsL,EAAOH,MACb,IAAK,QAAS,CACZ,MAAM3K,EAAS8K,EAAO9K,OAChBgC,EAAQ8I,EAAO9I,MACf+I,EAAY/I,EAAM3B,KAAK,KACvB2K,EAAY,IAAIC,gBAAgB,CACpCjJ,MAAO+I,IAET5B,GAAQnJ,EAAS,SAAWgL,EAAU5D,WACtC,KACF,CACA,IAAK,SAAU,CACb,MAAM8D,EAAMJ,EAAOI,IACnB/B,GAA4B,MAApB+B,EAAIvL,MAAM,EAAG,GAAauL,EAAIvL,MAAM,GAAKuL,EACjD,KACF,CACA,QAEE,YADArI,EAAS,QAAS,KAGtB,IAAIsI,EAAe,IACnBnB,EAAYG,EAAOhB,GAAMiC,KAAMC,IAC7B,MAAMd,EAASc,EAASd,OACxB,GAAe,MAAXA,EAOJ,OADAY,EAAe,IACRE,EAASC,OANdC,WAAW,KACT1I,EAASyH,GAAYC,GAAU,QAAU,OAAQA,OAMpDa,KAAMtJ,IACa,kBAATA,GAA8B,OAATA,EAUhCyJ,WAAW,KACT1I,EAAS,UAAWf,KAVpByJ,WAAW,KACI,MAATzJ,EACFe,EAAS,QAASf,GAElBe,EAAS,OAAQsI,OAQtBK,MAAM,KACP3I,EAAS,OAAQsI,MAGfM,GAAiB,CACrBjB,WACAK,SAGF,SAASa,GAAU1J,GACjB,MAAM/B,EAAS,CACb0L,OAAQ,GACRjI,QAAS,GACTkI,QAAS,IAEL7H,EAA0BvD,OAAO0B,OAAO,MAC9CF,EAAM6J,KAAK,CAACC,EAAGC,IACTD,EAAEtM,WAAauM,EAAEvM,SACZsM,EAAEtM,SAASwM,cAAcD,EAAEvM,UAEhCsM,EAAE9L,SAAW+L,EAAE/L,OACV8L,EAAE9L,OAAOgM,cAAcD,EAAE/L,QAE3B8L,EAAE5L,KAAK8L,cAAcD,EAAE7L,OAEhC,IAAI+L,EAAW,CACbzM,SAAU,GACVQ,OAAQ,GACRE,KAAM,IA2BR,OAzBA8B,EAAMO,QAASjC,IACb,GAAI2L,EAAS/L,OAASI,EAAKJ,MAAQ+L,EAASjM,SAAWM,EAAKN,QAAUiM,EAASzM,WAAac,EAAKd,SAC/F,OAEFyM,EAAW3L,EACX,MAAMd,EAAWc,EAAKd,SAChBQ,EAASM,EAAKN,OACdE,EAAOI,EAAKJ,KACZ2D,EAAkBE,EAAQvE,KAAcuE,EAAQvE,GAA4BgB,OAAO0B,OAAO,OAC1FgK,EAAerI,EAAgB7D,KAAY6D,EAAgB7D,GAAU4D,EAAWpE,EAAUQ,IAChG,IAAImM,EAEFA,EADEjM,KAAQgM,EAAalK,MAChB/B,EAAO0L,OACM,KAAX3L,GAAiBkM,EAAaxI,QAAQc,IAAItE,GAC5CD,EAAOyD,QAEPzD,EAAO2L,QAEhB,MAAM3I,EAAO,CACXzD,WACAQ,SACAE,QAEFiM,EAAKnJ,KAAKC,KAELhD,CACT,CAEA,SAASmM,GAAeC,EAAU5D,GAChC4D,EAAS9J,QAASwB,IAChB,MAAMuI,EAAQvI,EAAQwI,gBAClBD,IACFvI,EAAQwI,gBAAkBD,EAAME,OAAQC,GAAQA,EAAIhE,KAAOA,KAGjE,CACA,SAASiE,GAAgB3I,GAClBA,EAAQ4I,uBACX5I,EAAQ4I,sBAAuB,EAC/BpB,WAAW,KACTxH,EAAQ4I,sBAAuB,EAC/B,MAAML,EAAQvI,EAAQwI,gBAAkBxI,EAAQwI,gBAAgB5M,MAAM,GAAK,GAC3E,IAAK2M,EAAM1M,OACT,OAEF,IAAIgN,GAAa,EACjB,MAAMpN,EAAWuE,EAAQvE,SACnBQ,EAAS+D,EAAQ/D,OACvBsM,EAAM/J,QAASU,IACb,MAAMjB,EAAQiB,EAAKjB,MACb6K,EAAY7K,EAAM4J,QAAQhM,OAChCoC,EAAM4J,QAAU5J,EAAM4J,QAAQY,OAAQlM,IACpC,GAAIA,EAAKN,SAAWA,EAClB,OAAO,EAET,MAAME,EAAOI,EAAKJ,KAClB,GAAI6D,EAAQ/B,MAAM9B,GAChB8B,EAAM2J,OAAO3I,KAAK,CAChBxD,WACAQ,SACAE,aAEG,KAAI6D,EAAQL,QAAQc,IAAItE,GAQ7B,OADA0M,GAAa,GACN,EAPP5K,EAAM0B,QAAQV,KAAK,CACjBxD,WACAQ,SACAE,QAKJ,CACA,OAAO,IAEL8B,EAAM4J,QAAQhM,SAAWiN,IACtBD,GACHR,GAAe,CAACrI,GAAUd,EAAKwF,IAEjCxF,EAAKJ,SACHb,EAAM2J,OAAOhM,MAAM,GACnBqC,EAAM0B,QAAQ/D,MAAM,GACpBqC,EAAM4J,QAAQjM,MAAM,GACpBsD,EAAK6J,YAMjB,CACA,IAAIC,GAAY,EAChB,SAASC,GAAcnK,EAAUb,EAAOiL,GACtC,MAAMxE,EAAKsE,KACLD,EAAQV,GAAec,KAAK,KAAMD,EAAgBxE,GACxD,IAAKzG,EAAM4J,QAAQhM,OACjB,OAAOkN,EAET,MAAM7J,EAAO,CACXwF,KACAzG,QACAa,WACAiK,SAKF,OAHAG,EAAe1K,QAASwB,KACrBA,EAAQwI,kBAAoBxI,EAAQwI,gBAAkB,KAAKvJ,KAAKC,KAE5D6J,CACT,CAEA,SAASK,GAAYhB,EAAM7M,GAAW,EAAM6E,GAAc,GACxD,MAAMlE,EAAS,GAOf,OANAkM,EAAK5J,QAASU,IACZ,MAAM3C,EAAuB,kBAAT2C,EAAoB7D,EAAa6D,EAAM3D,EAAU6E,GAAelB,EAChF3C,GACFL,EAAO+C,KAAK1C,KAGTL,CACT,CAGA,IAAImN,GAAgB,CAClBlE,UAAW,GACXhD,MAAO,EACPmD,QAAS,IACTtI,OAAQ,IACRmH,QAAQ,EACRoB,kBAAkB,GAIpB,SAAS+D,GAAUzD,EAAQ0D,EAASC,EAAOC,GACzC,MAAMC,EAAiB7D,EAAOV,UAAUtJ,OAClC8N,EAAa9D,EAAO1B,OAAS9C,KAAKiC,MAAMjC,KAAK8C,SAAWuF,GAAkB7D,EAAO1D,MACvF,IAAIgD,EACJ,GAAIU,EAAO1B,OAAQ,CACjB,IAAIiE,EAAOvC,EAAOV,UAAUvJ,MAAM,GAClCuJ,EAAY,GACZ,MAAOiD,EAAKvM,OAAS,EAAG,CACtB,MAAM+N,EAAYvI,KAAKiC,MAAMjC,KAAK8C,SAAWiE,EAAKvM,QAClDsJ,EAAUlG,KAAKmJ,EAAKwB,IACpBxB,EAAOA,EAAKxM,MAAM,EAAGgO,GAAWtL,OAAO8J,EAAKxM,MAAMgO,EAAY,GAChE,CACAzE,EAAYA,EAAU7G,OAAO8J,EAC/B,MACEjD,EAAYU,EAAOV,UAAUvJ,MAAM+N,GAAYrL,OAAOuH,EAAOV,UAAUvJ,MAAM,EAAG+N,IAElF,MAAME,EAAY5F,KAAKC,MACvB,IAEI4F,EAFAtD,EAAS,UACTuD,EAAc,EAEdC,EAAQ,KACRC,EAAQ,GACRC,EAAgB,GAIpB,SAASC,IACHH,IACFI,aAAaJ,GACbA,EAAQ,KAEZ,CACA,SAASjB,IACQ,YAAXvC,IACFA,EAAS,WAEX2D,IACAF,EAAMzL,QAASU,IACO,YAAhBA,EAAKsH,SACPtH,EAAKsH,OAAS,aAGlByD,EAAQ,EACV,CACA,SAASI,EAAUvL,EAAUwL,GACvBA,IACFJ,EAAgB,IAEM,oBAAbpL,GACToL,EAAcjL,KAAKH,EAEvB,CACA,SAASyL,IACP,MAAO,CACLV,YACAN,UACA/C,SACAuD,cACAS,eAAgBP,EAAMpO,OACtBwO,YACAtB,QAEJ,CACA,SAAS0B,IACPjE,EAAS,SACT0D,EAAc1L,QAASM,IACrBA,OAAS,EAAQgL,IAErB,CACA,SAASY,IACPT,EAAMzL,QAASU,IACO,YAAhBA,EAAKsH,SACPtH,EAAKsH,OAAS,aAGlByD,EAAQ,EACV,CACA,SAASU,EAAezL,EAAMoI,EAAUvJ,GACtC,MAAM6M,EAAuB,YAAbtD,EAEhB,OADA2C,EAAQA,EAAMxB,OAAQoC,GAAWA,IAAW3L,GACpCsH,GACN,IAAK,UACH,MACF,IAAK,SACH,GAAIoE,IAAY/E,EAAON,iBACrB,OAEF,MACF,QACE,OAEJ,GAAiB,UAAb+B,EAGF,OAFAwC,EAAY/L,OACZ0M,IAGF,GAAIG,EASF,OARAd,EAAY/L,OACPkM,EAAMpO,SACJsJ,EAAUtJ,OAGbiP,IAFAL,MASN,GAFAN,IACAO,KACK7E,EAAO1B,OAAQ,CAClB,MAAMhC,EAAQ0D,EAAOV,UAAU/C,QAAQlD,EAAK6L,WAC7B,IAAX5I,GAAgBA,IAAU0D,EAAO1D,QACnC0D,EAAO1D,MAAQA,EAEnB,CACAqE,EAAS,YACT0D,EAAc1L,QAASM,IACrBA,EAASf,IAEb,CACA,SAAS+M,IACP,GAAe,YAAXtE,EACF,OAEF2D,IACA,MAAMY,EAAW5F,EAAUrJ,QAC3B,QAAiB,IAAbiP,EACF,OAAId,EAAMpO,YACRmO,EAAQxC,WAAW,KACjB2C,IACe,YAAX3D,IACFkE,IACAD,MAED5E,EAAOP,eAGZmF,IAGF,MAAMvL,EAAO,CACXsH,OAAQ,UACRuE,WACAjM,SAAUA,CAACkM,EAASjN,KAClB4M,EAAezL,EAAM8L,EAASjN,KAGlCkM,EAAMhL,KAAKC,GACX6K,IACAC,EAAQxC,WAAWsD,EAAUjF,EAAO7I,QACpCwM,EAAMuB,EAAUxB,EAASrK,EAAKJ,SAChC,CAEA,MAlIoB,oBAAT2K,GACTS,EAAcjL,KAAKwK,GAgIrBjC,WAAWsD,GACJP,CACT,CAGA,SAASU,GAAeC,GACtB,MAAMrF,EAAS,IACVwD,MACA6B,GAEL,IAAIC,EAAU,GACd,SAASC,IACPD,EAAUA,EAAQ1C,OAAQvJ,GAA2B,YAAlBA,IAAOsH,OAC5C,CACA,SAASgD,EAAMD,EAAS8B,EAAeC,GACrC,MAAMC,EAASjC,GACbzD,EACA0D,EACA8B,EACA,CAACtN,EAAMyN,KACLJ,IACIE,GACFA,EAAavN,EAAMyN,KAKzB,OADAL,EAAQlM,KAAKsM,GACNA,CACT,CACA,SAASE,EAAK3M,GACZ,OAAOqM,EAAQM,KAAMnQ,GACZwD,EAASxD,KACZ,IACR,CACA,MAAMoQ,EAAW,CACflC,QACAiC,OACAE,SAAWxJ,IACT0D,EAAO1D,MAAQA,GAEjByJ,SAAUA,IAAM/F,EAAO1D,MACvBiJ,WAEF,OAAOM,CACT,CAEA,SAASG,KACT,CACA,MAAMC,GAAkCrP,OAAO0B,OAAO,MACtD,SAAS4N,GAAmBtQ,GAC1B,IAAKqQ,GAAgBrQ,GAAW,CAC9B,MAAMoK,EAASC,EAAarK,GAC5B,IAAKoK,EACH,OAEF,MAAMmG,EAAaf,GAAepF,GAC5BoG,EAAkB,CACtBpG,SACAmG,cAEFF,GAAgBrQ,GAAYwQ,CAC9B,CACA,OAAOH,GAAgBrQ,EACzB,CACA,SAASyQ,GAAaC,EAAQ3C,EAAO1K,GACnC,IAAIkN,EACAlF,EACJ,GAAsB,kBAAXqF,EAAqB,CAC9B,MAAMC,EAAMpH,EAAamH,GACzB,IAAKC,EAEH,OADAtN,OAAS,EAAQ,KACV+M,GAET/E,EAAOsF,EAAItF,KACX,MAAMuF,EAASN,GAAmBI,GAC9BE,IACFL,EAAaK,EAAOL,WAExB,KAAO,CACL,MAAMnG,EAASZ,EAAgBkH,GAC/B,GAAItG,EAAQ,CACVmG,EAAaf,GAAepF,GAC5B,MAAMyG,EAAYH,EAAOhH,UAAYgH,EAAOhH,UAAU,GAAK,GACrDiH,EAAMpH,EAAasH,GACrBF,IACFtF,EAAOsF,EAAItF,KAEf,CACF,CACA,OAAKkF,GAAelF,EAIbkF,EAAWxC,MAAMA,EAAO1C,EAAMhI,EAA9BkN,GAA0CjD,OAH/CjK,OAAS,EAAQ,KACV+M,GAGX,CAEA,SAASU,KACT,CACA,SAASC,GAAexM,GACjBA,EAAQyM,kBACXzM,EAAQyM,iBAAkB,EAC1BjF,WAAW,KACTxH,EAAQyM,iBAAkB,EAC1B9D,GAAgB3I,KAGtB,CACA,SAAS0M,GAAqBzO,GAC5B,MAAM0O,EAAQ,GACRC,EAAU,GAIhB,OAHA3O,EAAMO,QAASrC,KACZA,EAAKoI,MAAMnJ,GAAiBuR,EAAQC,GAAS3N,KAAK9C,KAE9C,CACLwQ,QACAC,UAEJ,CACA,SAASC,GAAoB7M,EAAS/B,EAAOF,GAC3C,SAAS+O,IACP,MAAMjF,EAAU7H,EAAQ+M,aACxB9O,EAAMO,QAASrC,IACT0L,GACFA,EAAQmF,OAAO7Q,GAEZ6D,EAAQ/B,MAAM9B,IACjB6D,EAAQL,QAAQM,IAAI9D,IAG1B,CACA,GAAI4B,GAAwB,kBAATA,EACjB,IACE,MAAMkP,EAASlN,EAAWC,EAASjC,GACnC,IAAKkP,EAAOpR,OAEV,YADAiR,GAGJ,CAAE,MAAO3M,GACP+M,QAAQ1B,MAAMrL,EAChB,CAEF2M,IACAN,GAAexM,EACjB,CACA,SAASmN,GAA2B7F,EAAUxI,GACxCwI,aAAoB8F,QACtB9F,EAASD,KAAMtJ,IACbe,EAASf,KACR0J,MAAM,KACP3I,EAAS,QAGXA,EAASwI,EAEb,CACA,SAAS+F,GAAarN,EAAS/B,GACxB+B,EAAQsN,YAGXtN,EAAQsN,YAActN,EAAQsN,YAAYhP,OAAOL,GAAO6J,OAFxD9H,EAAQsN,YAAcrP,EAInB+B,EAAQuN,iBACXvN,EAAQuN,gBAAiB,EACzB/F,WAAW,KACTxH,EAAQuN,gBAAiB,EACzB,MAAM,SAAE9R,EAAQ,OAAEQ,GAAW+D,EACvBwN,EAASxN,EAAQsN,YAEvB,UADOtN,EAAQsN,aACVE,IAAWA,EAAO3R,OACrB,OAEF,MAAM4R,EAAmBzN,EAAQ0N,SACjC,GAAI1N,EAAQ2N,YAAcH,EAAO3R,OAAS,IAAM4R,GAO9C,YANAN,GACEnN,EAAQ2N,UAAUH,EAAQvR,EAAQR,GACjCsC,IACC8O,GAAoB7M,EAASwN,EAAQzP,KAK3C,GAAI0P,EAaF,YAZAD,EAAOhP,QAASrC,IACd,MAAMmL,EAAWmG,EAAiBtR,EAAMF,EAAQR,GAChD0R,GAA2B7F,EAAWvJ,IACpC,MAAM6P,EAAU7P,EAAO,CACrB9B,SACAgC,MAAO,CACL,CAAC9B,GAAO4B,IAER,KACJ8O,GAAoB7M,EAAS,CAAC7D,GAAOyR,OAK3C,MAAM,MAAEjB,EAAK,QAAEC,GAAYF,GAAqBc,GAIhD,GAHIZ,EAAQ/Q,QACVgR,GAAoB7M,EAAS4M,EAAS,OAEnCD,EAAM9Q,OACT,OAEF,MAAMuQ,EAAMnQ,EAAOsI,MAAMnJ,GAAiB4J,EAAavJ,GAAY,KACnE,IAAK2Q,EAEH,YADAS,GAAoB7M,EAAS2M,EAAO,MAGtC,MAAM5F,EAASqF,EAAI3F,QAAQhL,EAAUQ,EAAQ0Q,GAC7C5F,EAAOvI,QAASU,IACdgN,GAAazQ,EAAUyD,EAAOnB,IAC5B8O,GAAoB7M,EAASd,EAAKjB,MAAOF,SAKnD,CACA,MAAM4P,GAAYA,CAAC1P,EAAOa,KACxB,MAAM+O,EAAezE,GAAYnL,GAAO,EAAMoC,KACxCyN,EAAcnG,GAAUkG,GAC9B,IAAKC,EAAYjG,QAAQhM,OAAQ,CAC/B,IAAIkS,GAAe,EAanB,OAZIjP,GACF0I,WAAW,KACLuG,GACFjP,EACEgP,EAAYlG,OACZkG,EAAYnO,QACZmO,EAAYjG,QACZ0E,MAKD,KACLwB,GAAe,EAEnB,CACA,MAAMC,EAA2BvR,OAAO0B,OAAO,MACzC8P,EAAU,GAChB,IAAIC,EAAcC,EA6BlB,OA5BAL,EAAYjG,QAAQrJ,QAASjC,IAC3B,MAAM,SAAEd,EAAQ,OAAEQ,GAAWM,EAC7B,GAAIN,IAAWkS,GAAc1S,IAAayS,EACxC,OAEFA,EAAezS,EACf0S,EAAalS,EACbgS,EAAQhP,KAAKY,EAAWpE,EAAUQ,IAClC,MAAMmS,EAAmBJ,EAASvS,KAAcuS,EAASvS,GAA4BgB,OAAO0B,OAAO,OAC9FiQ,EAAiBnS,KACpBmS,EAAiBnS,GAAU,MAG/B6R,EAAYjG,QAAQrJ,QAASjC,IAC3B,MAAM,SAAEd,EAAQ,OAAEQ,EAAM,KAAEE,GAASI,EAC7ByD,EAAUH,EAAWpE,EAAUQ,GAC/BoS,EAAerO,EAAQ+M,eAAiB/M,EAAQ+M,aAA+B,IAAInN,KACpFyO,EAAa5N,IAAItE,KACpBkS,EAAapO,IAAI9D,GACjB6R,EAASvS,GAAUQ,GAAQgD,KAAK9C,MAGpC8R,EAAQzP,QAASwB,IACf,MAAMoI,EAAO4F,EAAShO,EAAQvE,UAAUuE,EAAQ/D,QAC5CmM,EAAKvM,QACPwR,GAAarN,EAASoI,KAGnBtJ,EAAWmK,GAAcnK,EAAUgP,EAAaG,GAAW1B,IAgCpE,SAAS+B,GAAoBjP,EAAUH,GACrC,MAAMhD,EAAS,IACVmD,GAEL,IAAK,MAAMxB,KAAOqB,EAAM,CACtB,MAAM5D,EAAQ4D,EAAKrB,GACb0Q,SAAmBjT,EACrBuC,KAAOgD,GACK,OAAVvF,GAAkBA,IAAwB,WAAdiT,GAAwC,WAAdA,MACxDrS,EAAO2B,GAAOvC,GAEPiT,WAAqBrS,EAAO2B,KACrC3B,EAAO2B,GAAe,WAARA,EAAmBvC,EAAQ,EAAIA,EAEjD,CACA,OAAOY,CACT,CAEA,MAAMsS,GAAY,SAClB,SAASC,GAAeC,EAAQC,GAC9BA,EAAKhT,MAAM6S,IAAWhQ,QAASoQ,IAC7B,MAAMtT,EAAQsT,EAAIpM,OAClB,OAAQlH,GACN,IAAK,aACHoT,EAAOxR,OAAQ,EACf,MACF,IAAK,WACHwR,EAAOzR,OAAQ,EACf,QAGR,CAEA,SAAS4R,GAAiBvT,EAAOwT,EAAe,GAC9C,MAAMC,EAAQzT,EAAMuJ,QAAQ,aAAc,IAC1C,SAASuG,EAAQ4D,GACf,MAAOA,EAAS,EACdA,GAAU,EAEZ,OAAOA,EAAS,CAClB,CACA,GAAc,KAAVD,EAAc,CAChB,MAAMnN,EAAMqN,SAAS3T,GACrB,OAAOwG,MAAMF,GAAO,EAAIwJ,EAAQxJ,EAClC,CAAO,GAAImN,IAAUzT,EAAO,CAC1B,IAAIK,EAAQ,EACZ,OAAQoT,GACN,IAAK,IACHpT,EAAQ,GACR,MACF,IAAK,MACHA,EAAQ,GAEZ,GAAIA,EAAO,CACT,IAAIiG,EAAMC,WAAWvG,EAAMM,MAAM,EAAGN,EAAMO,OAASkT,EAAMlT,SACzD,OAAIiG,MAAMF,GACD,GAETA,GAAYjG,EACLiG,EAAM,IAAM,EAAIwJ,EAAQxJ,GAAO,EACxC,CACF,CACA,OAAOkN,CACT,CAEA,SAASI,GAAW7R,EAAMuG,GACxB,IAAIuL,GAAgD,IAA5B9R,EAAK+E,QAAQ,UAAmB,GAAK,8CAC7D,IAAK,MAAMgN,KAAQxL,EACjBuL,GAAqB,IAAMC,EAAO,KAAOxL,EAAWwL,GAAQ,IAE9D,MAAO,0CAA4CD,EAAoB,IAAM9R,EAAO,QACtF,CAEA,SAASgS,GAAgBC,GACvB,OAAOA,EAAIzK,QAAQ,KAAM,KAAKA,QAAQ,KAAM,OAAOA,QAAQ,KAAM,OAAOA,QAAQ,KAAM,OAAOA,QAAQ,KAAM,OAAOA,QAAQ,OAAQ,IACpI,CACA,SAAS0K,GAAUD,GACjB,MAAO,sBAAwBD,GAAgBC,EACjD,CACA,SAASE,GAASF,GAChB,MAAO,QAAUC,GAAUD,GAAO,IACpC,CAEA,MAAMG,GAAoC,IACnC3O,EACH4O,QAAQ,GAMNC,GAAc,CAChB,MAAS,6BACT,cAAe,+BACf,eAAe,EACf,KAAQ,OAKNC,GAAc,CAChBC,QAAS,gBAEPC,GAAgB,CAClBC,gBAAiB,gBAEfC,GAAe,CACjBD,gBAAiB,eAGfE,GAAa,CACfC,MAAO,aACPC,OAAQ,YACRC,KAAM,aAEJC,GAAe,CACjBC,WAAYR,GACZS,KAAMT,GACNU,WAAYR,IAEhB,IAAK,MAAM/T,MAAUoU,GAAc,CAC/B,MAAMjI,EAAOiI,GAAapU,IAC1B,IAAK,MAAMqD,KAAQ2Q,GACf7H,EAAKnM,GAASqD,GAAQ2Q,GAAW3Q,EAEzC,CAKA,MAAMmR,GAAuB,CAAC,EAa9B,SAASC,GAAQpV,GACb,OAAOA,GAASA,EAAMiJ,MAAM,cAAgB,KAAO,GACvD,CAdA,CAAC,aAAc,YAAY/F,QAASvC,IAChC,MAAMmT,EAAOnT,EAAOL,MAAM,EAAG,GAAK,OAElC6U,GAAqBxU,EAAS,SAAWmT,EAEzCqB,GAAqBxU,EAAOL,MAAM,EAAG,GAAK,SAAWwT,EAErDqB,GAAqBxU,EAAS,QAAUmT,IAW5C,MAAMuB,GAASA,CAEfpU,EAEA0G,KAEI,MAAMJ,EAAiByL,GAAoBmB,GAAmCxM,GACxE2N,EAAiB,IAAKjB,IAEtBkB,EAAO5N,EAAM4N,MAAQ,MAErBC,EAAQ,CAAC,EACTC,EAAa9N,EAAM6N,MACnBE,EAAoC,kBAAfD,GAA6BA,aAAsB/R,MAExE,CAAC,EADD+R,EAGN,IAAK,IAAIlT,KAAOoF,EAAO,CACnB,MAAM3H,EAAQ2H,EAAMpF,GACpB,QAAc,IAAVvC,EAGJ,OAAQuC,GAEJ,IAAK,OACL,IAAK,QACL,IAAK,SACL,IAAK,OACL,IAAK,MACD,MAEJ,IAAK,SACL,IAAK,QACL,IAAK,QACDgF,EAAehF,IACD,IAAVvC,GAA4B,SAAVA,GAA8B,IAAVA,EAC1C,MAEJ,IAAK,OACoB,kBAAVA,GACPmT,GAAe5L,EAAgBvH,GAEnC,MAEJ,IAAK,QACDwV,EAAMG,MAAQ3V,EACd,MAEJ,IAAK,SACoB,kBAAVA,EACPuH,EAAehF,GAAOgR,GAAiBvT,GAEjB,kBAAVA,IACZuH,EAAehF,GAAOvC,GAE1B,MAEJ,IAAK,aACL,IAAK,eAEa,IAAVA,GAA4B,SAAVA,UACXsV,EAAe,eAE1B,MACJ,QAAS,CACL,MAAMM,EAAQT,GAAqB5S,GAC/BqT,GAEc,IAAV5V,GAA4B,SAAVA,GAA8B,IAAVA,IACtCuH,EAAeqO,IAAS,QAGoB,IAA3CzB,GAAkC5R,KAEvC+S,EAAe/S,GAAOvC,EAE9B,EAER,CAEA,MAAM4D,EAAO0D,EAAUrG,EAAMsG,GACvBsO,EAAgBjS,EAAK0E,WAK3B,GAHIf,EAAe6M,SACfoB,EAAMM,cAAgB,YAEb,QAATP,EAAgB,CAEhBD,EAAeE,MAAQ,IAChBA,KACAE,GAGPvU,OAAO4U,OAAOT,EAAgBO,GAE9B,IAAIG,EAAe,EACf5M,EAAKzB,EAAMyB,GAQf,MAPkB,kBAAPA,IAEPA,EAAKA,EAAGG,QAAQ,KAAM,MAG1B+L,EAAe,aAAevM,EAAWnF,EAAK7B,KAAMqH,EAAK,IAAMA,EAAK,KAAO4M,IAAiB,eAErFC,EAAAA,EAAAA,GAAE,MAAOX,EACpB,CAEA,MAAM,KAAEvT,EAAI,MAAER,EAAK,OAAEC,GAAWP,EAC1BiV,EAAmB,SAATX,GACF,OAATA,IAA0D,IAAlCxT,EAAK+E,QAAQ,gBAEpCqP,EAAOvC,GAAW7R,EAAM,IACvB8T,EACHtU,MAAOA,EAAQ,GACfC,OAAQA,EAAS,KAYrB,OATA8T,EAAeE,MAAQ,IAChBA,EACH,QAAStB,GAASiC,GAClB,MAASf,GAAQS,EAActU,OAC/B,OAAU6T,GAAQS,EAAcrU,WAC7B8S,MACC4B,EAAU1B,GAAgBE,MAC3BgB,IAEAO,EAAAA,EAAAA,GAAE,OAAQX,IA6BrB,GANAvQ,GAAiB,GAEjB0E,EAAa,GAAI2C,IAIO,qBAAbgK,UAA8C,qBAAXC,OAAwB,CAClE,MAAMC,EAAUD,OAEhB,QAA+B,IAA3BC,EAAQC,eAA2B,CACnC,MAAMC,EAAUF,EAAQC,eAClB1R,EAAM,iCACW,kBAAZ2R,GAAoC,OAAZA,IAC9BA,aAAmB9S,MAAQ8S,EAAU,CAACA,IAAUtT,QAASU,IACtD,KAGoB,kBAATA,GACM,OAATA,GACAA,aAAgBF,OAEM,kBAAfE,EAAKjB,OACW,kBAAhBiB,EAAKjD,SAEX0E,EAAczB,KACfgO,QAAQ1B,MAAMrL,EAEtB,CACA,MAAO4R,GACH7E,QAAQ1B,MAAMrL,EAClB,GAGZ,CAEA,QAAiC,IAA7ByR,EAAQI,iBAA6B,CACrC,MAAMC,EAAYL,EAAQI,iBAC1B,GAAyB,kBAAdC,GAAwC,OAAdA,EACjC,IAAK,IAAIpU,KAAOoU,EAAW,CACvB,MAAM9R,EAAM,oBAAsBtC,EAAM,gBACxC,IACI,MAAMvC,EAAQ2W,EAAUpU,GACxB,GAAqB,kBAAVvC,IACNA,QACmB,IAApBA,EAAM6J,UACN,SAECQ,EAAe9H,EAAKvC,IACrB4R,QAAQ1B,MAAMrL,EAEtB,CACA,MAAO4R,IACH7E,QAAQ1B,MAAMrL,EAClB,CACJ,CAER,CACJ,CAIA,MAAM+R,GAAY,IACX/U,EACHE,KAAM,IAEJ8U,IAAOC,EAAAA,EAAAA,IAAgB,CAEzBC,cAAc,EAEdtU,IAAAA,GACI,MAAO,CAEHuU,MAAO,GAEPC,aAAc,KAEdC,aAAa,EAEbpO,QAAS,EAEjB,EACAqO,OAAAA,GAEIC,KAAKF,aAAc,CACvB,EACAG,SAAAA,GACID,KAAKE,cACT,EACAC,QAAS,CACLD,YAAAA,GACQF,KAAKH,eACLG,KAAKH,aAAaxJ,QAClB2J,KAAKH,aAAe,KAE5B,EAEAO,OAAAA,CAAQvW,EAAMwW,EAAQC,GAElB,GAAoB,kBAATzW,GACE,OAATA,GACqB,kBAAdA,EAAKc,KAIZ,OAFAqV,KAAKJ,MAAQ,GACbI,KAAKE,eACE,CACH7U,KAAMxB,GAId,IAAIiE,EACJ,GAAoB,kBAATjE,GAC0C,QAAhDiE,EAAWnF,EAAakB,GAAM,GAAO,IAEtC,OADAmW,KAAKE,eACE,KAGX,IAAI7U,EAAOwC,EAAYC,GACvB,IAAKzC,EAgBD,OAdK2U,KAAKH,cAAgBG,KAAKH,aAAapW,OAASI,IAEjDmW,KAAKE,eACLF,KAAKJ,MAAQ,GACA,OAATvU,IAEA2U,KAAKH,aAAe,CAChBpW,KAAMI,EACNwM,MAAO4E,GAAU,CAACnN,GAAW,KACzBkS,KAAKtO,eAKd,KAWX,GARAsO,KAAKE,eACDF,KAAKJ,QAAU/V,IACfmW,KAAKJ,MAAQ/V,EACTwW,GACAA,EAAOxW,IAIXyW,EAAW,CAEXjV,EAAOtB,OAAO4U,OAAO,CAAC,EAAGtT,GACzB,MAAMkV,EAAaD,EAAUjV,EAAKV,KAAMmD,EAASrE,KAAMqE,EAASvE,OAAQuE,EAAS/E,UACvD,kBAAfwX,IACPlV,EAAKV,KAAO4V,EAEpB,CAEA,MAAMC,EAAU,CAAC,WAOjB,MANwB,KAApB1S,EAASvE,QACTiX,EAAQjU,KAAK,YAAcuB,EAASvE,QAEd,KAAtBuE,EAAS/E,UACTyX,EAAQjU,KAAK,YAAcuB,EAAS/E,UAEjC,CAAEsC,OAAMmV,UACnB,GAGJvC,MAAAA,GAEI+B,KAAKtO,QACL,MAAMnB,EAAQyP,KAAKS,OAEb5W,EAAOmW,KAAKF,aAAevP,EAAMmQ,IACjCV,KAAKI,QAAQ7P,EAAM1G,KAAM0G,EAAMoQ,OAAQpQ,EAAM+P,WAC7C,KAEN,IAAKzW,EACD,OAAOoU,GAAOuB,GAAWjP,GAG7B,IAAIqQ,EAAWrQ,EAUf,OATI1G,EAAK2W,UACLI,EAAW,IACJrQ,EACHsQ,OAAkC,kBAAnBtQ,EAAM,SACfA,EAAM,SAAW,IACjB,IAAM1G,EAAK2W,QAAQ5W,KAAK,OAI/BqU,GAAO,IACPxT,KACAZ,EAAKwB,MACTuV,EACP,G", "sources": ["webpack://frontend-web/./node_modules/@iconify/vue/dist/iconify.mjs"], "sourcesContent": ["import { h, defineComponent } from 'vue';\n\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled\n  // Check name: cannot be empty\n  ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\n\nconst defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\nfunction mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\nfunction mergeIconData(parent, child) {\n  const result = mergeIconTransformations(parent, child);\n  for (const key in defaultExtendedIconProps) {\n    if (key in defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\nfunction getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = mergeIconData(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return mergeIconData(data, currentProps);\n}\n\nfunction parseIconSet(data, callback) {\n  const names = [];\n  if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n    return names;\n  }\n  if (data.not_found instanceof Array) {\n    data.not_found.forEach((name) => {\n      callback(name, null);\n      names.push(name);\n    });\n  }\n  const tree = getIconsTree(data);\n  for (const name in tree) {\n    const item = tree[name];\n    if (item) {\n      callback(name, internalGetIconData(data, name, item));\n      names.push(name);\n    }\n  }\n  return names;\n}\n\nconst optionalPropertyDefaults = {\n  provider: \"\",\n  aliases: {},\n  not_found: {},\n  ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n  for (const prop in defaults) {\n    if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction quicklyValidateIconSet(obj) {\n  if (typeof obj !== \"object\" || obj === null) {\n    return null;\n  }\n  const data = obj;\n  if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n    return null;\n  }\n  if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n    return null;\n  }\n  const icons = data.icons;\n  for (const name in icons) {\n    const icon = icons[name];\n    if (\n      // Name cannot be empty\n      !name || // Must have body\n      typeof icon.body !== \"string\" || // Check other props\n      !checkOptionalProps(\n        icon,\n        defaultExtendedIconProps\n      )\n    ) {\n      return null;\n    }\n  }\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  for (const name in aliases) {\n    const icon = aliases[name];\n    const parent = icon.parent;\n    if (\n      // Name cannot be empty\n      !name || // Parent must be set and point to existing icon\n      typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || // Check other props\n      !checkOptionalProps(\n        icon,\n        defaultExtendedIconProps\n      )\n    ) {\n      return null;\n    }\n  }\n  return data;\n}\n\nconst dataStorage = /* @__PURE__ */ Object.create(null);\nfunction newStorage(provider, prefix) {\n  return {\n    provider,\n    prefix,\n    icons: /* @__PURE__ */ Object.create(null),\n    missing: /* @__PURE__ */ new Set()\n  };\n}\nfunction getStorage(provider, prefix) {\n  const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));\n  return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));\n}\nfunction addIconSet(storage, data) {\n  if (!quicklyValidateIconSet(data)) {\n    return [];\n  }\n  return parseIconSet(data, (name, icon) => {\n    if (icon) {\n      storage.icons[name] = icon;\n    } else {\n      storage.missing.add(name);\n    }\n  });\n}\nfunction addIconToStorage(storage, name, icon) {\n  try {\n    if (typeof icon.body === \"string\") {\n      storage.icons[name] = { ...icon };\n      return true;\n    }\n  } catch (err) {\n  }\n  return false;\n}\nfunction listIcons(provider, prefix) {\n  let allIcons = [];\n  const providers = typeof provider === \"string\" ? [provider] : Object.keys(dataStorage);\n  providers.forEach((provider2) => {\n    const prefixes = typeof provider2 === \"string\" && typeof prefix === \"string\" ? [prefix] : Object.keys(dataStorage[provider2] || {});\n    prefixes.forEach((prefix2) => {\n      const storage = getStorage(provider2, prefix2);\n      allIcons = allIcons.concat(\n        Object.keys(storage.icons).map(\n          (name) => (provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name\n        )\n      );\n    });\n  });\n  return allIcons;\n}\n\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n  if (typeof allow === \"boolean\") {\n    simpleNames = allow;\n  }\n  return simpleNames;\n}\nfunction getIconData(name) {\n  const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n  if (icon) {\n    const storage = getStorage(icon.provider, icon.prefix);\n    const iconName = icon.name;\n    return storage.icons[iconName] || (storage.missing.has(iconName) ? null : void 0);\n  }\n}\nfunction addIcon(name, data) {\n  const icon = stringToIcon(name, true, simpleNames);\n  if (!icon) {\n    return false;\n  }\n  const storage = getStorage(icon.provider, icon.prefix);\n  if (data) {\n    return addIconToStorage(storage, icon.name, data);\n  } else {\n    storage.missing.add(icon.name);\n    return true;\n  }\n}\nfunction addCollection(data, provider) {\n  if (typeof data !== \"object\") {\n    return false;\n  }\n  if (typeof provider !== \"string\") {\n    provider = data.provider || \"\";\n  }\n  if (simpleNames && !provider && !data.prefix) {\n    let added = false;\n    if (quicklyValidateIconSet(data)) {\n      data.prefix = \"\";\n      parseIconSet(data, (name, icon) => {\n        if (addIcon(name, icon)) {\n          added = true;\n        }\n      });\n    }\n    return added;\n  }\n  const prefix = data.prefix;\n  if (!validateIconName({\n    provider,\n    prefix,\n    name: \"a\"\n  })) {\n    return false;\n  }\n  const storage = getStorage(provider, prefix);\n  return !!addIconSet(storage, data);\n}\nfunction iconLoaded(name) {\n  return !!getIconData(name);\n}\nfunction getIcon(name) {\n  const result = getIconData(name);\n  return result ? {\n    ...defaultIconProps,\n    ...result\n  } : result;\n}\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ...defaultIconTransformations\n});\n\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nfunction splitSVGDefs(content, tag = \"defs\") {\n  let defs = \"\";\n  const index = content.indexOf(\"<\" + tag);\n  while (index >= 0) {\n    const start = content.indexOf(\">\", index);\n    const end = content.indexOf(\"</\" + tag);\n    if (start === -1 || end === -1) {\n      break;\n    }\n    const endEnd = content.indexOf(\">\", end);\n    if (endEnd === -1) {\n      break;\n    }\n    defs += content.slice(start + 1, end).trim();\n    content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n  }\n  return {\n    defs,\n    content\n  };\n}\nfunction mergeDefsAndContent(defs, content) {\n  return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n  const split = splitSVGDefs(body);\n  return mergeDefsAndContent(split.defs, start + split.content + end);\n}\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ...defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ...defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = wrapSVGContent(\n        body,\n        '<g transform=\"' + transformations.join(\" \") + '\">',\n        \"</g>\"\n      );\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = calculateSize(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  const viewBox = [box.left, box.top, boxWidth, boxHeight];\n  attributes.viewBox = viewBox.join(\" \");\n  return {\n    attributes,\n    viewBox,\n    body\n  };\n}\n\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n  storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n  return storage[provider] || storage[\"\"];\n}\n\nfunction createAPIConfig(source) {\n  let resources;\n  if (typeof source.resources === \"string\") {\n    resources = [source.resources];\n  } else {\n    resources = source.resources;\n    if (!(resources instanceof Array) || !resources.length) {\n      return null;\n    }\n  }\n  const result = {\n    // API hosts\n    resources,\n    // Root path\n    path: source.path || \"/\",\n    // URL length limit\n    maxURL: source.maxURL || 500,\n    // Timeout before next host is used.\n    rotate: source.rotate || 750,\n    // Timeout before failing query.\n    timeout: source.timeout || 5e3,\n    // Randomise default API end point.\n    random: source.random === true,\n    // Start index\n    index: source.index || 0,\n    // Receive data after time out (used if time out kicks in first, then API module sends data anyway).\n    dataAfterTimeout: source.dataAfterTimeout !== false\n  };\n  return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n  \"https://api.simplesvg.com\",\n  \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile (fallBackAPISources.length > 0) {\n  if (fallBackAPISources.length === 1) {\n    fallBackAPI.push(fallBackAPISources.shift());\n  } else {\n    if (Math.random() > 0.5) {\n      fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n      fallBackAPI.push(fallBackAPISources.pop());\n    }\n  }\n}\nconfigStorage[\"\"] = createAPIConfig({\n  resources: [\"https://api.iconify.design\"].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n  const config = createAPIConfig(customConfig);\n  if (config === null) {\n    return false;\n  }\n  configStorage[provider] = config;\n  return true;\n}\nfunction getAPIConfig(provider) {\n  return configStorage[provider];\n}\nfunction listAPIProviders() {\n  return Object.keys(configStorage);\n}\n\nconst detectFetch = () => {\n  let callback;\n  try {\n    callback = fetch;\n    if (typeof callback === \"function\") {\n      return callback;\n    }\n  } catch (err) {\n  }\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n  fetchModule = fetch2;\n}\nfunction getFetch() {\n  return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n  const config = getAPIConfig(provider);\n  if (!config) {\n    return 0;\n  }\n  let result;\n  if (!config.maxURL) {\n    result = 0;\n  } else {\n    let maxHostLength = 0;\n    config.resources.forEach((item) => {\n      const host = item;\n      maxHostLength = Math.max(maxHostLength, host.length);\n    });\n    const url = prefix + \".json?icons=\";\n    result = config.maxURL - maxHostLength - config.path.length - url.length;\n  }\n  return result;\n}\nfunction shouldAbort(status) {\n  return status === 404;\n}\nconst prepare = (provider, prefix, icons) => {\n  const results = [];\n  const maxLength = calculateMaxLength(provider, prefix);\n  const type = \"icons\";\n  let item = {\n    type,\n    provider,\n    prefix,\n    icons: []\n  };\n  let length = 0;\n  icons.forEach((name, index) => {\n    length += name.length + 1;\n    if (length >= maxLength && index > 0) {\n      results.push(item);\n      item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n      };\n      length = name.length;\n    }\n    item.icons.push(name);\n  });\n  results.push(item);\n  return results;\n};\nfunction getPath(provider) {\n  if (typeof provider === \"string\") {\n    const config = getAPIConfig(provider);\n    if (config) {\n      return config.path;\n    }\n  }\n  return \"/\";\n}\nconst send = (host, params, callback) => {\n  if (!fetchModule) {\n    callback(\"abort\", 424);\n    return;\n  }\n  let path = getPath(params.provider);\n  switch (params.type) {\n    case \"icons\": {\n      const prefix = params.prefix;\n      const icons = params.icons;\n      const iconsList = icons.join(\",\");\n      const urlParams = new URLSearchParams({\n        icons: iconsList\n      });\n      path += prefix + \".json?\" + urlParams.toString();\n      break;\n    }\n    case \"custom\": {\n      const uri = params.uri;\n      path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n      break;\n    }\n    default:\n      callback(\"abort\", 400);\n      return;\n  }\n  let defaultError = 503;\n  fetchModule(host + path).then((response) => {\n    const status = response.status;\n    if (status !== 200) {\n      setTimeout(() => {\n        callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n      });\n      return;\n    }\n    defaultError = 501;\n    return response.json();\n  }).then((data) => {\n    if (typeof data !== \"object\" || data === null) {\n      setTimeout(() => {\n        if (data === 404) {\n          callback(\"abort\", data);\n        } else {\n          callback(\"next\", defaultError);\n        }\n      });\n      return;\n    }\n    setTimeout(() => {\n      callback(\"success\", data);\n    });\n  }).catch(() => {\n    callback(\"next\", defaultError);\n  });\n};\nconst fetchAPIModule = {\n  prepare,\n  send\n};\n\nfunction sortIcons(icons) {\n  const result = {\n    loaded: [],\n    missing: [],\n    pending: []\n  };\n  const storage = /* @__PURE__ */ Object.create(null);\n  icons.sort((a, b) => {\n    if (a.provider !== b.provider) {\n      return a.provider.localeCompare(b.provider);\n    }\n    if (a.prefix !== b.prefix) {\n      return a.prefix.localeCompare(b.prefix);\n    }\n    return a.name.localeCompare(b.name);\n  });\n  let lastIcon = {\n    provider: \"\",\n    prefix: \"\",\n    name: \"\"\n  };\n  icons.forEach((icon) => {\n    if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n      return;\n    }\n    lastIcon = icon;\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    const name = icon.name;\n    const providerStorage = storage[provider] || (storage[provider] = /* @__PURE__ */ Object.create(null));\n    const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));\n    let list;\n    if (name in localStorage.icons) {\n      list = result.loaded;\n    } else if (prefix === \"\" || localStorage.missing.has(name)) {\n      list = result.missing;\n    } else {\n      list = result.pending;\n    }\n    const item = {\n      provider,\n      prefix,\n      name\n    };\n    list.push(item);\n  });\n  return result;\n}\n\nfunction removeCallback(storages, id) {\n  storages.forEach((storage) => {\n    const items = storage.loaderCallbacks;\n    if (items) {\n      storage.loaderCallbacks = items.filter((row) => row.id !== id);\n    }\n  });\n}\nfunction updateCallbacks(storage) {\n  if (!storage.pendingCallbacksFlag) {\n    storage.pendingCallbacksFlag = true;\n    setTimeout(() => {\n      storage.pendingCallbacksFlag = false;\n      const items = storage.loaderCallbacks ? storage.loaderCallbacks.slice(0) : [];\n      if (!items.length) {\n        return;\n      }\n      let hasPending = false;\n      const provider = storage.provider;\n      const prefix = storage.prefix;\n      items.forEach((item) => {\n        const icons = item.icons;\n        const oldLength = icons.pending.length;\n        icons.pending = icons.pending.filter((icon) => {\n          if (icon.prefix !== prefix) {\n            return true;\n          }\n          const name = icon.name;\n          if (storage.icons[name]) {\n            icons.loaded.push({\n              provider,\n              prefix,\n              name\n            });\n          } else if (storage.missing.has(name)) {\n            icons.missing.push({\n              provider,\n              prefix,\n              name\n            });\n          } else {\n            hasPending = true;\n            return true;\n          }\n          return false;\n        });\n        if (icons.pending.length !== oldLength) {\n          if (!hasPending) {\n            removeCallback([storage], item.id);\n          }\n          item.callback(\n            icons.loaded.slice(0),\n            icons.missing.slice(0),\n            icons.pending.slice(0),\n            item.abort\n          );\n        }\n      });\n    });\n  }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n  const id = idCounter++;\n  const abort = removeCallback.bind(null, pendingSources, id);\n  if (!icons.pending.length) {\n    return abort;\n  }\n  const item = {\n    id,\n    icons,\n    callback,\n    abort\n  };\n  pendingSources.forEach((storage) => {\n    (storage.loaderCallbacks || (storage.loaderCallbacks = [])).push(item);\n  });\n  return abort;\n}\n\nfunction listToIcons(list, validate = true, simpleNames = false) {\n  const result = [];\n  list.forEach((item) => {\n    const icon = typeof item === \"string\" ? stringToIcon(item, validate, simpleNames) : item;\n    if (icon) {\n      result.push(icon);\n    }\n  });\n  return result;\n}\n\n// src/config.ts\nvar defaultConfig = {\n  resources: [],\n  index: 0,\n  timeout: 2e3,\n  rotate: 750,\n  random: false,\n  dataAfterTimeout: false\n};\n\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n  const resourcesCount = config.resources.length;\n  const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n  let resources;\n  if (config.random) {\n    let list = config.resources.slice(0);\n    resources = [];\n    while (list.length > 1) {\n      const nextIndex = Math.floor(Math.random() * list.length);\n      resources.push(list[nextIndex]);\n      list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n    }\n    resources = resources.concat(list);\n  } else {\n    resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n  }\n  const startTime = Date.now();\n  let status = \"pending\";\n  let queriesSent = 0;\n  let lastError;\n  let timer = null;\n  let queue = [];\n  let doneCallbacks = [];\n  if (typeof done === \"function\") {\n    doneCallbacks.push(done);\n  }\n  function resetTimer() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function abort() {\n    if (status === \"pending\") {\n      status = \"aborted\";\n    }\n    resetTimer();\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function subscribe(callback, overwrite) {\n    if (overwrite) {\n      doneCallbacks = [];\n    }\n    if (typeof callback === \"function\") {\n      doneCallbacks.push(callback);\n    }\n  }\n  function getQueryStatus() {\n    return {\n      startTime,\n      payload,\n      status,\n      queriesSent,\n      queriesPending: queue.length,\n      subscribe,\n      abort\n    };\n  }\n  function failQuery() {\n    status = \"failed\";\n    doneCallbacks.forEach((callback) => {\n      callback(void 0, lastError);\n    });\n  }\n  function clearQueue() {\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function moduleResponse(item, response, data) {\n    const isError = response !== \"success\";\n    queue = queue.filter((queued) => queued !== item);\n    switch (status) {\n      case \"pending\":\n        break;\n      case \"failed\":\n        if (isError || !config.dataAfterTimeout) {\n          return;\n        }\n        break;\n      default:\n        return;\n    }\n    if (response === \"abort\") {\n      lastError = data;\n      failQuery();\n      return;\n    }\n    if (isError) {\n      lastError = data;\n      if (!queue.length) {\n        if (!resources.length) {\n          failQuery();\n        } else {\n          execNext();\n        }\n      }\n      return;\n    }\n    resetTimer();\n    clearQueue();\n    if (!config.random) {\n      const index = config.resources.indexOf(item.resource);\n      if (index !== -1 && index !== config.index) {\n        config.index = index;\n      }\n    }\n    status = \"completed\";\n    doneCallbacks.forEach((callback) => {\n      callback(data);\n    });\n  }\n  function execNext() {\n    if (status !== \"pending\") {\n      return;\n    }\n    resetTimer();\n    const resource = resources.shift();\n    if (resource === void 0) {\n      if (queue.length) {\n        timer = setTimeout(() => {\n          resetTimer();\n          if (status === \"pending\") {\n            clearQueue();\n            failQuery();\n          }\n        }, config.timeout);\n        return;\n      }\n      failQuery();\n      return;\n    }\n    const item = {\n      status: \"pending\",\n      resource,\n      callback: (status2, data) => {\n        moduleResponse(item, status2, data);\n      }\n    };\n    queue.push(item);\n    queriesSent++;\n    timer = setTimeout(execNext, config.rotate);\n    query(resource, payload, item.callback);\n  }\n  setTimeout(execNext);\n  return getQueryStatus;\n}\n\n// src/index.ts\nfunction initRedundancy(cfg) {\n  const config = {\n    ...defaultConfig,\n    ...cfg\n  };\n  let queries = [];\n  function cleanup() {\n    queries = queries.filter((item) => item().status === \"pending\");\n  }\n  function query(payload, queryCallback, doneCallback) {\n    const query2 = sendQuery(\n      config,\n      payload,\n      queryCallback,\n      (data, error) => {\n        cleanup();\n        if (doneCallback) {\n          doneCallback(data, error);\n        }\n      }\n    );\n    queries.push(query2);\n    return query2;\n  }\n  function find(callback) {\n    return queries.find((value) => {\n      return callback(value);\n    }) || null;\n  }\n  const instance = {\n    query,\n    find,\n    setIndex: (index) => {\n      config.index = index;\n    },\n    getIndex: () => config.index,\n    cleanup\n  };\n  return instance;\n}\n\nfunction emptyCallback$1() {\n}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n  if (!redundancyCache[provider]) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n      return;\n    }\n    const redundancy = initRedundancy(config);\n    const cachedReundancy = {\n      config,\n      redundancy\n    };\n    redundancyCache[provider] = cachedReundancy;\n  }\n  return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n  let redundancy;\n  let send;\n  if (typeof target === \"string\") {\n    const api = getAPIModule(target);\n    if (!api) {\n      callback(void 0, 424);\n      return emptyCallback$1;\n    }\n    send = api.send;\n    const cached = getRedundancyCache(target);\n    if (cached) {\n      redundancy = cached.redundancy;\n    }\n  } else {\n    const config = createAPIConfig(target);\n    if (config) {\n      redundancy = initRedundancy(config);\n      const moduleKey = target.resources ? target.resources[0] : \"\";\n      const api = getAPIModule(moduleKey);\n      if (api) {\n        send = api.send;\n      }\n    }\n  }\n  if (!redundancy || !send) {\n    callback(void 0, 424);\n    return emptyCallback$1;\n  }\n  return redundancy.query(query, send, callback)().abort;\n}\n\nfunction emptyCallback() {\n}\nfunction loadedNewIcons(storage) {\n  if (!storage.iconsLoaderFlag) {\n    storage.iconsLoaderFlag = true;\n    setTimeout(() => {\n      storage.iconsLoaderFlag = false;\n      updateCallbacks(storage);\n    });\n  }\n}\nfunction checkIconNamesForAPI(icons) {\n  const valid = [];\n  const invalid = [];\n  icons.forEach((name) => {\n    (name.match(matchIconName) ? valid : invalid).push(name);\n  });\n  return {\n    valid,\n    invalid\n  };\n}\nfunction parseLoaderResponse(storage, icons, data) {\n  function checkMissing() {\n    const pending = storage.pendingIcons;\n    icons.forEach((name) => {\n      if (pending) {\n        pending.delete(name);\n      }\n      if (!storage.icons[name]) {\n        storage.missing.add(name);\n      }\n    });\n  }\n  if (data && typeof data === \"object\") {\n    try {\n      const parsed = addIconSet(storage, data);\n      if (!parsed.length) {\n        checkMissing();\n        return;\n      }\n    } catch (err) {\n      console.error(err);\n    }\n  }\n  checkMissing();\n  loadedNewIcons(storage);\n}\nfunction parsePossiblyAsyncResponse(response, callback) {\n  if (response instanceof Promise) {\n    response.then((data) => {\n      callback(data);\n    }).catch(() => {\n      callback(null);\n    });\n  } else {\n    callback(response);\n  }\n}\nfunction loadNewIcons(storage, icons) {\n  if (!storage.iconsToLoad) {\n    storage.iconsToLoad = icons;\n  } else {\n    storage.iconsToLoad = storage.iconsToLoad.concat(icons).sort();\n  }\n  if (!storage.iconsQueueFlag) {\n    storage.iconsQueueFlag = true;\n    setTimeout(() => {\n      storage.iconsQueueFlag = false;\n      const { provider, prefix } = storage;\n      const icons2 = storage.iconsToLoad;\n      delete storage.iconsToLoad;\n      if (!icons2 || !icons2.length) {\n        return;\n      }\n      const customIconLoader = storage.loadIcon;\n      if (storage.loadIcons && (icons2.length > 1 || !customIconLoader)) {\n        parsePossiblyAsyncResponse(\n          storage.loadIcons(icons2, prefix, provider),\n          (data) => {\n            parseLoaderResponse(storage, icons2, data);\n          }\n        );\n        return;\n      }\n      if (customIconLoader) {\n        icons2.forEach((name) => {\n          const response = customIconLoader(name, prefix, provider);\n          parsePossiblyAsyncResponse(response, (data) => {\n            const iconSet = data ? {\n              prefix,\n              icons: {\n                [name]: data\n              }\n            } : null;\n            parseLoaderResponse(storage, [name], iconSet);\n          });\n        });\n        return;\n      }\n      const { valid, invalid } = checkIconNamesForAPI(icons2);\n      if (invalid.length) {\n        parseLoaderResponse(storage, invalid, null);\n      }\n      if (!valid.length) {\n        return;\n      }\n      const api = prefix.match(matchIconName) ? getAPIModule(provider) : null;\n      if (!api) {\n        parseLoaderResponse(storage, valid, null);\n        return;\n      }\n      const params = api.prepare(provider, prefix, valid);\n      params.forEach((item) => {\n        sendAPIQuery(provider, item, (data) => {\n          parseLoaderResponse(storage, item.icons, data);\n        });\n      });\n    });\n  }\n}\nconst loadIcons = (icons, callback) => {\n  const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n  const sortedIcons = sortIcons(cleanedIcons);\n  if (!sortedIcons.pending.length) {\n    let callCallback = true;\n    if (callback) {\n      setTimeout(() => {\n        if (callCallback) {\n          callback(\n            sortedIcons.loaded,\n            sortedIcons.missing,\n            sortedIcons.pending,\n            emptyCallback\n          );\n        }\n      });\n    }\n    return () => {\n      callCallback = false;\n    };\n  }\n  const newIcons = /* @__PURE__ */ Object.create(null);\n  const sources = [];\n  let lastProvider, lastPrefix;\n  sortedIcons.pending.forEach((icon) => {\n    const { provider, prefix } = icon;\n    if (prefix === lastPrefix && provider === lastProvider) {\n      return;\n    }\n    lastProvider = provider;\n    lastPrefix = prefix;\n    sources.push(getStorage(provider, prefix));\n    const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));\n    if (!providerNewIcons[prefix]) {\n      providerNewIcons[prefix] = [];\n    }\n  });\n  sortedIcons.pending.forEach((icon) => {\n    const { provider, prefix, name } = icon;\n    const storage = getStorage(provider, prefix);\n    const pendingQueue = storage.pendingIcons || (storage.pendingIcons = /* @__PURE__ */ new Set());\n    if (!pendingQueue.has(name)) {\n      pendingQueue.add(name);\n      newIcons[provider][prefix].push(name);\n    }\n  });\n  sources.forEach((storage) => {\n    const list = newIcons[storage.provider][storage.prefix];\n    if (list.length) {\n      loadNewIcons(storage, list);\n    }\n  });\n  return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon) => {\n  return new Promise((fulfill, reject) => {\n    const iconObj = typeof icon === \"string\" ? stringToIcon(icon, true) : icon;\n    if (!iconObj) {\n      reject(icon);\n      return;\n    }\n    loadIcons([iconObj || icon], (loaded) => {\n      if (loaded.length && iconObj) {\n        const data = getIconData(iconObj);\n        if (data) {\n          fulfill({\n            ...defaultIconProps,\n            ...data\n          });\n          return;\n        }\n      }\n      reject(icon);\n    });\n  });\n};\n\nfunction setCustomIconsLoader(loader, prefix, provider) {\n  getStorage(provider || \"\", prefix).loadIcons = loader;\n}\nfunction setCustomIconLoader(loader, prefix, provider) {\n  getStorage(provider || \"\", prefix).loadIcon = loader;\n}\n\nfunction mergeCustomisations(defaults, item) {\n  const result = {\n    ...defaults\n  };\n  for (const key in item) {\n    const value = item[key];\n    const valueType = typeof value;\n    if (key in defaultIconSizeCustomisations) {\n      if (value === null || value && (valueType === \"string\" || valueType === \"number\")) {\n        result[key] = value;\n      }\n    } else if (valueType === typeof result[key]) {\n      result[key] = key === \"rotate\" ? value % 4 : value;\n    }\n  }\n  return result;\n}\n\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n  flip.split(separator).forEach((str) => {\n    const value = str.trim();\n    switch (value) {\n      case \"horizontal\":\n        custom.hFlip = true;\n        break;\n      case \"vertical\":\n        custom.vFlip = true;\n        break;\n    }\n  });\n}\n\nfunction rotateFromString(value, defaultValue = 0) {\n  const units = value.replace(/^-?[0-9.]*/, \"\");\n  function cleanup(value2) {\n    while (value2 < 0) {\n      value2 += 4;\n    }\n    return value2 % 4;\n  }\n  if (units === \"\") {\n    const num = parseInt(value);\n    return isNaN(num) ? 0 : cleanup(num);\n  } else if (units !== value) {\n    let split = 0;\n    switch (units) {\n      case \"%\":\n        split = 25;\n        break;\n      case \"deg\":\n        split = 90;\n    }\n    if (split) {\n      let num = parseFloat(value.slice(0, value.length - units.length));\n      if (isNaN(num)) {\n        return 0;\n      }\n      num = num / split;\n      return num % 1 === 0 ? cleanup(num) : 0;\n    }\n  }\n  return defaultValue;\n}\n\nfunction iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\nfunction encodeSVGforURL(svg) {\n  return svg.replace(/\"/g, \"'\").replace(/%/g, \"%25\").replace(/#/g, \"%23\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/\\s+/g, \" \");\n}\nfunction svgToData(svg) {\n  return \"data:image/svg+xml,\" + encodeSVGforURL(svg);\n}\nfunction svgToURL(svg) {\n  return 'url(\"' + svgToData(svg) + '\")';\n}\n\nconst defaultExtendedIconCustomisations = {\n    ...defaultIconCustomisations,\n    inline: false,\n};\n\n/**\n * Default SVG attributes\n */\nconst svgDefaults = {\n    'xmlns': 'http://www.w3.org/2000/svg',\n    'xmlns:xlink': 'http://www.w3.org/1999/xlink',\n    'aria-hidden': true,\n    'role': 'img',\n};\n/**\n * Style modes\n */\nconst commonProps = {\n    display: 'inline-block',\n};\nconst monotoneProps = {\n    backgroundColor: 'currentColor',\n};\nconst coloredProps = {\n    backgroundColor: 'transparent',\n};\n// Dynamically add common props to variables above\nconst propsToAdd = {\n    Image: 'var(--svg)',\n    Repeat: 'no-repeat',\n    Size: '100% 100%',\n};\nconst propsToAddTo = {\n    webkitMask: monotoneProps,\n    mask: monotoneProps,\n    background: coloredProps,\n};\nfor (const prefix in propsToAddTo) {\n    const list = propsToAddTo[prefix];\n    for (const prop in propsToAdd) {\n        list[prefix + prop] = propsToAdd[prop];\n    }\n}\n/**\n * Aliases for customisations.\n * In Vue 'v-' properties are reserved, so v-flip must be renamed\n */\nconst customisationAliases = {};\n['horizontal', 'vertical'].forEach((prefix) => {\n    const attr = prefix.slice(0, 1) + 'Flip';\n    // vertical-flip\n    customisationAliases[prefix + '-flip'] = attr;\n    // v-flip\n    customisationAliases[prefix.slice(0, 1) + '-flip'] = attr;\n    // verticalFlip\n    customisationAliases[prefix + 'Flip'] = attr;\n});\n/**\n * Fix size: add 'px' to numbers\n */\nfunction fixSize(value) {\n    return value + (value.match(/^[-0-9.]+$/) ? 'px' : '');\n}\n/**\n * Render icon\n */\nconst render = (\n// Icon must be validated before calling this function\nicon, \n// Partial properties\nprops) => {\n    // Split properties\n    const customisations = mergeCustomisations(defaultExtendedIconCustomisations, props);\n    const componentProps = { ...svgDefaults };\n    // Check mode\n    const mode = props.mode || 'svg';\n    // Copy style\n    const style = {};\n    const propsStyle = props.style;\n    const customStyle = typeof propsStyle === 'object' && !(propsStyle instanceof Array)\n        ? propsStyle\n        : {};\n    // Get element properties\n    for (let key in props) {\n        const value = props[key];\n        if (value === void 0) {\n            continue;\n        }\n        switch (key) {\n            // Properties to ignore\n            case 'icon':\n            case 'style':\n            case 'onLoad':\n            case 'mode':\n            case 'ssr':\n                break;\n            // Boolean attributes\n            case 'inline':\n            case 'hFlip':\n            case 'vFlip':\n                customisations[key] =\n                    value === true || value === 'true' || value === 1;\n                break;\n            // Flip as string: 'horizontal,vertical'\n            case 'flip':\n                if (typeof value === 'string') {\n                    flipFromString(customisations, value);\n                }\n                break;\n            // Color: override style\n            case 'color':\n                style.color = value;\n                break;\n            // Rotation as string\n            case 'rotate':\n                if (typeof value === 'string') {\n                    customisations[key] = rotateFromString(value);\n                }\n                else if (typeof value === 'number') {\n                    customisations[key] = value;\n                }\n                break;\n            // Remove aria-hidden\n            case 'ariaHidden':\n            case 'aria-hidden':\n                // Vue transforms 'aria-hidden' property to 'ariaHidden'\n                if (value !== true && value !== 'true') {\n                    delete componentProps['aria-hidden'];\n                }\n                break;\n            default: {\n                const alias = customisationAliases[key];\n                if (alias) {\n                    // Aliases for boolean customisations\n                    if (value === true || value === 'true' || value === 1) {\n                        customisations[alias] = true;\n                    }\n                }\n                else if (defaultExtendedIconCustomisations[key] === void 0) {\n                    // Copy missing property if it does not exist in customisations\n                    componentProps[key] = value;\n                }\n            }\n        }\n    }\n    // Generate icon\n    const item = iconToSVG(icon, customisations);\n    const renderAttribs = item.attributes;\n    // Inline display\n    if (customisations.inline) {\n        style.verticalAlign = '-0.125em';\n    }\n    if (mode === 'svg') {\n        // Add style\n        componentProps.style = {\n            ...style,\n            ...customStyle,\n        };\n        // Add icon stuff\n        Object.assign(componentProps, renderAttribs);\n        // Counter for ids based on \"id\" property to render icons consistently on server and client\n        let localCounter = 0;\n        let id = props.id;\n        if (typeof id === 'string') {\n            // Convert '-' to '_' to avoid errors in animations\n            id = id.replace(/-/g, '_');\n        }\n        // Add innerHTML and style to props\n        componentProps['innerHTML'] = replaceIDs(item.body, id ? () => id + 'ID' + localCounter++ : 'iconifyVue');\n        // Render icon\n        return h('svg', componentProps);\n    }\n    // Render <span> with style\n    const { body, width, height } = icon;\n    const useMask = mode === 'mask' ||\n        (mode === 'bg' ? false : body.indexOf('currentColor') !== -1);\n    // Generate SVG\n    const html = iconToHTML(body, {\n        ...renderAttribs,\n        width: width + '',\n        height: height + '',\n    });\n    // Generate style\n    componentProps.style = {\n        ...style,\n        '--svg': svgToURL(html),\n        'width': fixSize(renderAttribs.width),\n        'height': fixSize(renderAttribs.height),\n        ...commonProps,\n        ...(useMask ? monotoneProps : coloredProps),\n        ...customStyle,\n    };\n    return h('span', componentProps);\n};\n\n/**\n * Enable cache\n *\n * @deprecated No longer used\n */\nfunction enableCache(storage) {\n    //\n}\n/**\n * Disable cache\n *\n * @deprecated No longer used\n */\nfunction disableCache(storage) {\n    //\n}\n/**\n * Initialise stuff\n */\n// Enable short names\nallowSimpleNames(true);\n// Set API module\nsetAPIModule('', fetchAPIModule);\n/**\n * Browser stuff\n */\nif (typeof document !== 'undefined' && typeof window !== 'undefined') {\n    const _window = window;\n    // Load icons from global \"IconifyPreload\"\n    if (_window.IconifyPreload !== void 0) {\n        const preload = _window.IconifyPreload;\n        const err = 'Invalid IconifyPreload syntax.';\n        if (typeof preload === 'object' && preload !== null) {\n            (preload instanceof Array ? preload : [preload]).forEach((item) => {\n                try {\n                    if (\n                    // Check if item is an object and not null/array\n                    typeof item !== 'object' ||\n                        item === null ||\n                        item instanceof Array ||\n                        // Check for 'icons' and 'prefix'\n                        typeof item.icons !== 'object' ||\n                        typeof item.prefix !== 'string' ||\n                        // Add icon set\n                        !addCollection(item)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            });\n        }\n    }\n    // Set API from global \"IconifyProviders\"\n    if (_window.IconifyProviders !== void 0) {\n        const providers = _window.IconifyProviders;\n        if (typeof providers === 'object' && providers !== null) {\n            for (let key in providers) {\n                const err = 'IconifyProviders[' + key + '] is invalid.';\n                try {\n                    const value = providers[key];\n                    if (typeof value !== 'object' ||\n                        !value ||\n                        value.resources === void 0) {\n                        continue;\n                    }\n                    if (!addAPIProvider(key, value)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            }\n        }\n    }\n}\n/**\n * Empty icon data, rendered when icon is not available\n */\nconst emptyIcon = {\n    ...defaultIconProps,\n    body: '',\n};\nconst Icon = defineComponent({\n    // Do not inherit other attributes: it is handled by render()\n    inheritAttrs: false,\n    // Set initial data\n    data() {\n        return {\n            // Current icon name\n            _name: '',\n            // Loading\n            _loadingIcon: null,\n            // Mounted status\n            iconMounted: false,\n            // Callback counter to trigger re-render\n            counter: 0,\n        };\n    },\n    mounted() {\n        // Mark as mounted\n        this.iconMounted = true;\n    },\n    unmounted() {\n        this.abortLoading();\n    },\n    methods: {\n        abortLoading() {\n            if (this._loadingIcon) {\n                this._loadingIcon.abort();\n                this._loadingIcon = null;\n            }\n        },\n        // Get data for icon to render or null\n        getIcon(icon, onload, customise) {\n            // Icon is an object\n            if (typeof icon === 'object' &&\n                icon !== null &&\n                typeof icon.body === 'string') {\n                // Stop loading\n                this._name = '';\n                this.abortLoading();\n                return {\n                    data: icon,\n                };\n            }\n            // Invalid icon?\n            let iconName;\n            if (typeof icon !== 'string' ||\n                (iconName = stringToIcon(icon, false, true)) === null) {\n                this.abortLoading();\n                return null;\n            }\n            // Load icon\n            let data = getIconData(iconName);\n            if (!data) {\n                // Icon data is not available\n                if (!this._loadingIcon || this._loadingIcon.name !== icon) {\n                    // New icon to load\n                    this.abortLoading();\n                    this._name = '';\n                    if (data !== null) {\n                        // Icon was not loaded\n                        this._loadingIcon = {\n                            name: icon,\n                            abort: loadIcons([iconName], () => {\n                                this.counter++;\n                            }),\n                        };\n                    }\n                }\n                return null;\n            }\n            // Icon data is available\n            this.abortLoading();\n            if (this._name !== icon) {\n                this._name = icon;\n                if (onload) {\n                    onload(icon);\n                }\n            }\n            // Customise icon\n            if (customise) {\n                // Clone data and customise it\n                data = Object.assign({}, data);\n                const customised = customise(data.body, iconName.name, iconName.prefix, iconName.provider);\n                if (typeof customised === 'string') {\n                    data.body = customised;\n                }\n            }\n            // Add classes\n            const classes = ['iconify'];\n            if (iconName.prefix !== '') {\n                classes.push('iconify--' + iconName.prefix);\n            }\n            if (iconName.provider !== '') {\n                classes.push('iconify--' + iconName.provider);\n            }\n            return { data, classes };\n        },\n    },\n    // Render icon\n    render() {\n        // Re-render when counter changes\n        this.counter;\n        const props = this.$attrs;\n        // Get icon data\n        const icon = this.iconMounted || props.ssr\n            ? this.getIcon(props.icon, props.onLoad, props.customise)\n            : null;\n        // Validate icon object\n        if (!icon) {\n            return render(emptyIcon, props);\n        }\n        // Add classes\n        let newProps = props;\n        if (icon.classes) {\n            newProps = {\n                ...props,\n                class: (typeof props['class'] === 'string'\n                    ? props['class'] + ' '\n                    : '') + icon.classes.join(' '),\n            };\n        }\n        // Render icon\n        return render({\n            ...defaultIconProps,\n            ...icon.data,\n        }, newProps);\n    },\n});\n/**\n * Internal API\n */\nconst _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders,\n};\n\nexport { Icon, _api, addAPIProvider, addCollection, addIcon, iconToSVG as buildIcon, calculateSize, disableCache, enableCache, getIcon, iconLoaded as iconExists, iconLoaded, listIcons, loadIcon, loadIcons, replaceIDs, setCustomIconLoader, setCustomIconsLoader };\n"], "names": ["matchIconName", "stringToIcon", "value", "validate", "allowSimpleName", "provider", "colonSeparated", "split", "slice", "length", "shift", "name2", "pop", "prefix", "result", "name", "validateIconName", "dashSeparated", "join", "icon", "defaultIconDimensions", "Object", "freeze", "left", "top", "width", "height", "defaultIconTransformations", "rotate", "vFlip", "hFlip", "defaultIconProps", "defaultExtendedIconProps", "body", "hidden", "mergeIconTransformations", "obj1", "obj2", "mergeIconData", "parent", "child", "key", "getIconsTree", "data", "names", "icons", "aliases", "create", "resolved", "resolve", "concat", "keys", "for<PERSON>ach", "internalGetIconData", "tree", "currentProps", "parse", "parseIconSet", "callback", "not_found", "Array", "push", "item", "optionalPropertyDefaults", "checkOptionalProps", "defaults", "prop", "quicklyValidateIconSet", "obj", "dataStorage", "newStorage", "missing", "Set", "getStorage", "providerStorage", "addIconSet", "storage", "add", "addIconToStorage", "err", "simpleNames", "allowSimpleNames", "allow", "getIconData", "iconName", "has", "addIcon", "addCollection", "added", "defaultIconSizeCustomisations", "defaultIconCustomisations", "unitsSplit", "unitsTest", "calculateSize", "size", "ratio", "precision", "Math", "ceil", "oldParts", "newParts", "code", "isNumber", "test", "num", "parseFloat", "isNaN", "splitSVGDefs", "content", "tag", "defs", "index", "indexOf", "start", "end", "endEnd", "trim", "mergeDefsAndContent", "wrapSVGContent", "isUnsetKeyword", "iconToSVG", "customisations", "fullIcon", "fullCustomisations", "box", "props", "transformations", "tempValue", "rotation", "toString", "floor", "unshift", "customisationsWidth", "customisationsHeight", "boxWidth", "boxHeight", "attributes", "setAttr", "viewBox", "regex", "randomPrefix", "Date", "now", "random", "counter", "replaceIDs", "ids", "match", "exec", "suffix", "id", "newID", "escapedID", "replace", "RegExp", "setAPIModule", "getAPIModule", "createAPIConfig", "source", "resources", "path", "maxURL", "timeout", "dataAfterTimeout", "configStorage", "fallBackAPISources", "fallBackAPI", "addAPIProvider", "customConfig", "config", "getAPIConfig", "detectFetch", "fetch", "fetchModule", "calculateMaxLength", "maxHost<PERSON><PERSON><PERSON>", "host", "max", "url", "shouldAbort", "status", "prepare", "results", "max<PERSON><PERSON><PERSON>", "type", "<PERSON><PERSON><PERSON>", "send", "params", "iconsList", "urlParams", "URLSearchParams", "uri", "defaultError", "then", "response", "json", "setTimeout", "catch", "fetchAPIModule", "sortIcons", "loaded", "pending", "sort", "a", "b", "localeCompare", "lastIcon", "localStorage", "list", "removeCallback", "storages", "items", "loaderCallbacks", "filter", "row", "updateCallbacks", "pendingCallbacksFlag", "hasPending", "<PERSON><PERSON><PERSON><PERSON>", "abort", "idCounter", "storeCallback", "pendingSources", "bind", "listToIcons", "defaultConfig", "<PERSON><PERSON><PERSON><PERSON>", "payload", "query", "done", "resourcesCount", "startIndex", "nextIndex", "startTime", "lastError", "queriesSent", "timer", "queue", "doneCallbacks", "resetTimer", "clearTimeout", "subscribe", "overwrite", "getQueryStatus", "queriesPending", "fail<PERSON><PERSON><PERSON>", "clearQueue", "moduleResponse", "isError", "queued", "execNext", "resource", "status2", "initRedundancy", "cfg", "queries", "cleanup", "query<PERSON><PERSON><PERSON>", "doneCallback", "query2", "error", "find", "instance", "setIndex", "getIndex", "emptyCallback$1", "redundancyCache", "getRedundancyCache", "redundancy", "cachedReundancy", "sendAPIQuery", "target", "api", "cached", "module<PERSON>ey", "emptyCallback", "loadedNewIcons", "iconsLoaderFlag", "checkIconNamesForAPI", "valid", "invalid", "parseLoaderResponse", "checkMissing", "pendingIcons", "delete", "parsed", "console", "parsePossiblyAsyncResponse", "Promise", "loadNewIcons", "iconsToLoad", "iconsQueueFlag", "icons2", "customIconLoader", "loadIcon", "loadIcons", "iconSet", "cleanedIcons", "sortedIcons", "callCallback", "newIcons", "sources", "lastProvider", "lastPrefix", "providerNewIcons", "pendingQueue", "mergeCustomisations", "valueType", "separator", "flipFromString", "custom", "flip", "str", "rotateFromString", "defaultValue", "units", "value2", "parseInt", "iconToHTML", "renderAttribsHTML", "attr", "encodeSVGforURL", "svg", "svgToData", "svgToURL", "defaultExtendedIconCustomisations", "inline", "svgDefaults", "commonProps", "display", "monotoneProps", "backgroundColor", "coloredProps", "propsToAdd", "Image", "Repeat", "Size", "propsToAddTo", "webkitMask", "mask", "background", "customisationAliases", "fixSize", "render", "componentProps", "mode", "style", "propsStyle", "customStyle", "color", "alias", "renderAttribs", "verticalAlign", "assign", "localCounter", "h", "useMask", "html", "document", "window", "_window", "IconifyPreload", "preload", "e", "IconifyProviders", "providers", "emptyIcon", "Icon", "defineComponent", "inheritAttrs", "_name", "_loadingIcon", "iconMounted", "mounted", "this", "unmounted", "abortLoading", "methods", "getIcon", "onload", "customise", "customised", "classes", "$attrs", "ssr", "onLoad", "newProps", "class"], "sourceRoot": ""}