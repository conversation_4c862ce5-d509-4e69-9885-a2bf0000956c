"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[61],{67638:function(e,t,l){l.d(t,{A:function(){return X}});var a=l(56768),s=l(45130),n=l(24232);const i={key:0},r={key:0},o={key:1},d={key:0,class:"tab-box-sli"},c={style:{color:"#747474"}},u={key:0},p={class:"tab-box-sli"},h={key:4,style:{color:"#d60000"}},k={key:0,style:{color:"#00AA7F"}},g={key:1,style:{color:"#d18d17"}},m={key:2,style:{color:"#ff0000"}},b={key:0,style:{color:"#00AA7F"}},_={key:1,style:{color:"#ff5500"}},f={key:0,style:{"margin-top":"10px",width:"100%","text-align":"center"}},v={class:"dialog-footer"};function y(e,t,l,y,C,F){const w=(0,a.g2)("Editor"),L=(0,a.g2)("el-scrollbar"),x=(0,a.g2)("el-tab-pane"),X=(0,a.g2)("el-tag"),P=(0,a.g2)("el-collapse-item"),I=(0,a.g2)("el-collapse"),E=(0,a.g2)("el-tabs"),T=(0,a.g2)("el-button"),D=(0,a.g2)("el-option"),R=(0,a.g2)("el-select"),W=(0,a.g2)("el-form-item"),S=(0,a.g2)("el-input"),$=(0,a.g2)("el-form"),V=(0,a.g2)("el-dialog");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.bF)(E,{"model-value":"rb",style:{"min-height":"300px"},type:"border-card",value:"rb",size:"mini"},{default:(0,a.k6)(()=>["api"==l.result.type?((0,a.uX)(),(0,a.Wv)(x,{key:0,label:"响应体",name:"rb"},{default:(0,a.k6)(()=>[l.result.response_header?((0,a.uX)(),(0,a.CE)("div",i,[l.result.response_header["Content-Type"].includes("application/json")?((0,a.uX)(),(0,a.CE)("div",r,[(0,a.bF)(w,{readOnly:!0,modelValue:l.result.response_body,"onUpdate:modelValue":t[0]||(t[0]=e=>l.result.response_body=e),lang:"json",theme:"chrome"},null,8,["modelValue"])])):((0,a.uX)(),(0,a.CE)("div",o,[(0,a.bF)(L,{height:"400px",onWheel:t[1]||(t[1]=(0,s.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>[(0,a.bF)(w,{readOnly:!0,innerHTML:l.result.response_body,lang:"html",theme:"chrome",height:"400px"},null,8,["innerHTML"])]),_:1})]))])):(0,a.Q3)("",!0)]),_:1})):(0,a.Q3)("",!0),"api"==l.result.type?((0,a.uX)(),(0,a.Wv)(x,{key:1,label:"响应头",name:"rh"},{default:(0,a.k6)(()=>[(0,a.bF)(L,{height:"400px",onWheel:t[2]||(t[2]=(0,s.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>[l.result.response_header?((0,a.uX)(),(0,a.CE)("div",d,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(l.result.response_header,(e,t)=>((0,a.uX)(),(0,a.CE)("div",null,[(0,a.bF)(X,{style:{"margin-top":"3px"},type:"info"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",c,(0,n.v_)(t+" : "),1),(0,a.Lk)("span",null,(0,n.v_)(e),1)]),_:2},1024)]))),256))])):(0,a.Q3)("",!0)]),_:1})]),_:1})):(0,a.Q3)("",!0),"api"==l.result.type?((0,a.uX)(),(0,a.Wv)(x,{key:2,label:"请求信息",name:"rq"},{default:(0,a.k6)(()=>[(0,a.bF)(L,{height:"400px",onWheel:t[4]||(t[4]=(0,s.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>[l.result.requests_body?((0,a.uX)(),(0,a.CE)("div",u,[(0,a.bF)(I,{modelValue:C.activeNames,"onUpdate:modelValue":t[3]||(t[3]=e=>C.activeNames=e),class:"tab-box-sli"},{default:(0,a.k6)(()=>[(0,a.bF)(P,{name:"1"},{title:(0,a.k6)(()=>t[9]||(t[9]=[(0,a.Lk)("b",null,"General",-1)])),default:(0,a.k6)(()=>[(0,a.Lk)("div",null,"Request Method : "+(0,n.v_)(l.result.method),1),(0,a.Lk)("div",null,"Request URL : "+(0,n.v_)(l.result.url),1)]),_:1}),(0,a.bF)(P,{name:"2"},{title:(0,a.k6)(()=>t[10]||(t[10]=[(0,a.Lk)("b",null,"Request Headers",-1)])),default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(l.result.requests_header,(e,t)=>((0,a.uX)(),(0,a.CE)("div",null,[(0,a.Lk)("span",null,(0,n.v_)(t+" : "+e),1)]))),256))]),_:1}),(0,a.bF)(P,{name:"3"},{title:(0,a.k6)(()=>t[11]||(t[11]=[(0,a.Lk)("b",null,"Request Payload",-1)])),default:(0,a.k6)(()=>[(0,a.Lk)("span",null,(0,n.v_)(l.result.requests_body),1)]),_:1})]),_:1},8,["modelValue"])])):(0,a.Q3)("",!0)]),_:1})]),_:1})):(0,a.Q3)("",!0),(0,a.bF)(x,{label:"日志"},{default:(0,a.k6)(()=>[(0,a.bF)(L,{height:"400px",onWheel:t[5]||(t[5]=(0,s.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>[(0,a.Lk)("div",p,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(l.result.log_data,(e,t)=>((0,a.uX)(),(0,a.CE)("div",null,["DEBUG"===e[0]?((0,a.uX)(),(0,a.Wv)(X,{key:0,style:{"margin-top":"3px"}},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(e[1]),1)]),_:2},1024)):"WARNING"===e[0]?((0,a.uX)(),(0,a.Wv)(X,{key:1,style:{"margin-top":"3px"},type:"warning"},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(e[1]),1)]),_:2},1024)):"ERROR"===e[0]?((0,a.uX)(),(0,a.Wv)(X,{key:2,style:{"margin-top":"3px"},type:"danger"},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(e[1]),1)]),_:2},1024)):"INFO"===e[0]?((0,a.uX)(),(0,a.Wv)(X,{key:3,style:{"margin-top":"3px"},type:"success"},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(e[1]),1)]),_:2},1024)):"EXCEPT"===e[0]?((0,a.uX)(),(0,a.CE)("pre",h,(0,n.v_)(e[1]),1)):(0,a.Q3)("",!0)]))),256))])]),_:1})]),_:1}),(0,a.bF)(x,{disabled:""},{label:(0,a.k6)(()=>["成功"===l.result.state?((0,a.uX)(),(0,a.CE)("span",k,(0,n.v_)("Assert : "+l.result.state),1)):"失败"===l.result.state?((0,a.uX)(),(0,a.CE)("span",g,(0,n.v_)("Assert : "+l.result.state),1)):((0,a.uX)(),(0,a.CE)("span",m,(0,n.v_)(l.result.state),1))]),_:1}),"api"==l.result.type?((0,a.uX)(),(0,a.Wv)(x,{key:3,disabled:""},{label:(0,a.k6)(()=>[l.result.status_cede<=300?((0,a.uX)(),(0,a.CE)("span",b,(0,n.v_)("Status : "+l.result.status_cede),1)):((0,a.uX)(),(0,a.CE)("span",_,(0,n.v_)("Status : "+l.result.status_cede),1))]),_:1})):(0,a.Q3)("",!0),(0,a.bF)(x,{disabled:""},{label:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)("Time : "+l.result.run_time),1)]),_:1})]),_:1}),"失败"===l.result.state&&l.showbtn?((0,a.uX)(),(0,a.CE)("div",f,[(0,a.bF)(T,{onClick:F.getInterfaces,type:"success",plain:"",size:"mini"},{default:(0,a.k6)(()=>t[12]||(t[12]=[(0,a.eW)("提交bug")])),_:1,__:[12]},8,["onClick"])])):(0,a.Q3)("",!0),(0,a.bF)(V,{title:"提交bug",modelValue:C.addBugDlg,"onUpdate:modelValue":t[8]||(t[8]=e=>C.addBugDlg=e),width:"40%","before-close":F.closeDialogResult},{footer:(0,a.k6)(()=>[(0,a.Lk)("div",v,[(0,a.bF)(T,{onClick:F.closeDialogResult},{default:(0,a.k6)(()=>t[13]||(t[13]=[(0,a.eW)("取 消")])),_:1,__:[13]},8,["onClick"]),(0,a.bF)(T,{type:"success",onClick:F.saveBug},{default:(0,a.k6)(()=>t[14]||(t[14]=[(0,a.eW)("确 定")])),_:1,__:[14]},8,["onClick"])])]),default:(0,a.k6)(()=>[(0,a.bF)($,{model:C.bugForm},{default:(0,a.k6)(()=>[(0,a.bF)(W,{label:"所属接口"},{default:(0,a.k6)(()=>[(0,a.bF)(R,{size:"small",modelValue:C.bugForm.interface,"onUpdate:modelValue":t[6]||(t[6]=e=>C.bugForm.interface=e),placeholder:"bug对应的接口",style:{width:"100%"}},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(C.interfaces,e=>((0,a.uX)(),(0,a.Wv)(D,{label:e.name+" "+e.url,value:e.id,key:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(W,{label:"bug描述"},{default:(0,a.k6)(()=>[(0,a.bF)(S,{autosize:{minRows:3,maxRows:4},modelValue:C.bugForm.desc,"onUpdate:modelValue":t[7]||(t[7]=e=>C.bugForm.desc=e),type:"textarea",autocomplete:"off"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","before-close"])],64)}var C=l(53629),F=l(60782),w={props:{result:{default:{}},showbtn:{default:!0}},computed:{...(0,F.aH)(["pro"])},components:{Editor:C.A},data(){return{activeNames:["1","2","3"],addBugDlg:!1,bugForm:{interface:null,desc:"",info:"",status:"待处理"},interfaces:[]}},methods:{async saveBug(){this.bugForm.project=this.pro.id,this.bugForm.info=this.result;const e=await this.$api.createBugs(this.bugForm);201===e.status&&(this.$message({type:"success",message:"bug提交成功",duration:1e3}),this.addBugDlg=!1,this.bugForm={interface:null,desc:"",info:"",status:"待处理"})},closeDialogResult(){this.addBugDlg=!1,this.bugForm={interface:null,desc:"",info:"",status:"待处理"}},async getInterfaces(){const e=await this.$api.getNewInterfaces();200===e.status&&(this.interfaces=e.data,this.addBugDlg=!0)}}},L=l(71241);const x=(0,L.A)(w,[["render",y]]);var X=x},90568:function(e,t,l){l.r(t),l.d(t,{default:function(){return $e}});l(44114);var a=l(56768),s=l(45130),n=l(24232);const i={class:"test-plan-dashboard"},r={class:"dashboard-header"},o={class:"dashboard-actions"},d={class:"dashboard-main"},c={class:"plans-sidebar"},u={class:"sidebar-header"},p={class:"plans-list"},h=["onClick"],k={class:"plan-info"},g={class:"plan-name"},m={class:"plan-actions"},b={class:"content-main"},_={key:2,class:"content-layout"},f={class:"scenes-container"},v={class:"section-header"},y={key:0},C={key:1},F={class:"scenes-grid"},w=["onClick"],L={class:"scene-status"},x=["data-status"],X={class:"scene-card-content"},P={class:"scene-card-header"},I={class:"scene-card-description"},E={class:"scene-card-footer"},T={class:"scene-steps"},D={class:"scene-actions"},R={class:"scene-number"},W={class:"data-container"},S={class:"data-overview"},$={class:"data-card pass-rate"},V={class:"data-value"},A={class:"data-card total-runs"},N={class:"data-value"},z={class:"data-card success-runs"},Q={class:"data-value"},B={class:"chart-section"},M={class:"chart-container-wrapper"},U={ref:"chartTable",class:"chart-container"},K={class:"records-section"},q={class:"section-header"},j={class:"records-container"},H={class:"time-cell"},O={class:"status-cell"},G={key:0,class:"status-badge running"},Y={key:1,class:"status-badge completed"},J={key:0,class:"progress-wrapper"},Z={class:"progress-counts"},ee={class:"success-count"},te={class:"total-count"},le={class:"progress-percentage"},ae={key:1,class:"progress-pending"},se={class:"dialog-footer"};function ne(e,t,l,ne,ie,re){const oe=(0,a.g2)("Search"),de=(0,a.g2)("el-icon"),ce=(0,a.g2)("el-button"),ue=(0,a.g2)("el-input"),pe=(0,a.g2)("el-empty"),he=(0,a.g2)("Folder"),ke=(0,a.g2)("Edit"),ge=(0,a.g2)("el-tooltip"),me=(0,a.g2)("Delete"),be=(0,a.g2)("Document"),_e=(0,a.g2)("el-tag"),fe=(0,a.g2)("Timer"),ve=(0,a.g2)("el-table-column"),ye=(0,a.g2)("Check"),Ce=(0,a.g2)("el-progress"),Fe=(0,a.g2)("Loading"),we=(0,a.g2)("el-table"),Le=(0,a.g2)("el-form-item"),xe=(0,a.g2)("el-form"),Xe=(0,a.g2)("el-dialog"),Pe=(0,a.g2)("TestCase"),Ie=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",i,[(0,a.Lk)("div",r,[t[4]||(t[4]=(0,a.Lk)("div",{class:"dashboard-title"},[(0,a.Lk)("h2",null,"测试计划管理")],-1)),(0,a.Lk)("div",o,[(0,a.bF)(ue,{modelValue:ie.filterText,"onUpdate:modelValue":t[0]||(t[0]=e=>ie.filterText=e),placeholder:"搜索计划","prefix-icon":"el-icon-search",clearable:"",class:"search-input",onKeyup:(0,s.jR)(re.handletreeClick,["enter"])},{append:(0,a.k6)(()=>[(0,a.bF)(ce,{onClick:re.handletreeClick},{default:(0,a.k6)(()=>[(0,a.bF)(de,null,{default:(0,a.k6)(()=>[(0,a.bF)(oe)]),_:1})]),_:1},8,["onClick"])]),_:1},8,["modelValue","onKeyup"]),(0,a.bF)(ce,{type:"success",disabled:!ie.scene_list||0===ie.scene_list.length||!ie.planId,onClick:re.runPlan,class:"run-button"},{default:(0,a.k6)(()=>t[3]||(t[3]=[(0,a.eW)(" 运行计划 ")])),_:1,__:[3]},8,["disabled","onClick"])])]),(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",d,[(0,a.Lk)("div",c,[(0,a.Lk)("div",u,[t[6]||(t[6]=(0,a.Lk)("h3",null,"测试计划列表",-1)),(0,a.bF)(ce,{type:"primary",size:"small",onClick:re.addPlan,class:"add-plan-btn"},{default:(0,a.k6)(()=>t[5]||(t[5]=[(0,a.eW)(" 新建计划 ")])),_:1,__:[5]},8,["onClick"])]),0===ie.planList.length?((0,a.uX)(),(0,a.Wv)(pe,{key:0,description:"暂无测试计划","image-size":100},{default:(0,a.k6)(()=>[(0,a.bF)(ce,{type:"primary",onClick:re.addPlan},{default:(0,a.k6)(()=>t[7]||(t[7]=[(0,a.eW)("创建计划")])),_:1,__:[7]},8,["onClick"])]),_:1})):(0,a.Q3)("",!0),(0,a.Lk)("div",p,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ie.planList,e=>((0,a.uX)(),(0,a.CE)("div",{key:e.id,class:(0,n.C4)(["plan-item",{active:ie.planId===e.id}]),onClick:t=>re.selectPlan(e.id)},[(0,a.Lk)("div",k,[(0,a.bF)(de,null,{default:(0,a.k6)(()=>[(0,a.bF)(he)]),_:1}),(0,a.Lk)("span",g,(0,n.v_)(e.name),1)]),(0,a.Lk)("div",m,[(0,a.bF)(ge,{content:"编辑",placement:"top"},{default:(0,a.k6)(()=>[(0,a.bF)(de,{onClick:(0,s.D$)(t=>re.editPlan(e),["stop"]),class:"action-icon"},{default:(0,a.k6)(()=>[(0,a.bF)(ke)]),_:2},1032,["onClick"])]),_:2},1024),(0,a.bF)(ge,{content:"删除",placement:"top"},{default:(0,a.k6)(()=>[(0,a.bF)(de,{onClick:(0,s.D$)(t=>re.delPlan(e.id),["stop"]),class:"action-icon delete-icon"},{default:(0,a.k6)(()=>[(0,a.bF)(me)]),_:2},1032,["onClick"])]),_:2},1024)])],10,h))),128))])]),(0,a.Lk)("div",b,[0===ie.planList.length?((0,a.uX)(),(0,a.Wv)(pe,{key:0,description:"暂无测试计划，请先创建测试计划",class:"center-empty"},{default:(0,a.k6)(()=>[(0,a.bF)(ce,{type:"primary",onClick:re.addPlan},{default:(0,a.k6)(()=>t[8]||(t[8]=[(0,a.eW)("创建测试计划")])),_:1,__:[8]},8,["onClick"])]),_:1})):ie.scene_list&&0!==ie.scene_list.length?((0,a.uX)(),(0,a.CE)("div",_,[(0,a.Lk)("div",f,[(0,a.Lk)("div",v,[(0,a.Lk)("h3",null,[ie.currentPlanName?((0,a.uX)(),(0,a.CE)("span",y,(0,n.v_)(ie.currentPlanName),1)):((0,a.uX)(),(0,a.CE)("span",C,"测试场景"))]),(0,a.bF)(ce,{type:"primary",size:"small",onClick:re.clickAddScene,class:"add-scene-btn"},{default:(0,a.k6)(()=>t[10]||(t[10]=[(0,a.eW)(" 添加场景 ")])),_:1,__:[10]},8,["onClick"])]),(0,a.Lk)("div",F,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ie.scene_list,(e,t)=>((0,a.uX)(),(0,a.CE)("div",{key:e.id,class:"scene-card",onClick:t=>re.clickView(e)},[(0,a.Lk)("div",L,[(0,a.Lk)("div",{class:(0,n.C4)(["status-indicator",re.getSceneStatusClass(e)]),"data-status":re.getSceneStatusText(e)},null,10,x)]),(0,a.Lk)("div",X,[(0,a.Lk)("div",P,[(0,a.Lk)("h4",null,(0,n.v_)(e.name),1)]),(0,a.Lk)("div",I,(0,n.v_)(e.desc||"暂无描述"),1),(0,a.Lk)("div",E,[(0,a.Lk)("div",T,[(0,a.bF)(de,null,{default:(0,a.k6)(()=>[(0,a.bF)(be)]),_:1}),(0,a.Lk)("span",null,(0,n.v_)(e.stepCount||0)+" 步骤",1)]),(0,a.Lk)("div",D,[(0,a.Lk)("div",R,"场景 "+(0,n.v_)(t+1),1),(0,a.bF)(ce,{type:"danger",circle:"",class:"delete-scene-btn",onClick:(0,s.D$)(t=>re.delScene(e.id),["stop"]),title:"删除场景"},{default:(0,a.k6)(()=>[(0,a.bF)(de,null,{default:(0,a.k6)(()=>[(0,a.bF)(me)]),_:1})]),_:2},1032,["onClick"])])])])],8,w))),128))])]),(0,a.Lk)("div",W,[(0,a.Lk)("div",S,[(0,a.Lk)("div",$,[(0,a.Lk)("div",V,(0,n.v_)(re.getAveragePassRate())+"%",1),t[11]||(t[11]=(0,a.Lk)("div",{class:"data-label"},"平均通过率",-1))]),(0,a.Lk)("div",A,[(0,a.Lk)("div",N,(0,n.v_)(ie.records.length),1),t[12]||(t[12]=(0,a.Lk)("div",{class:"data-label"},"总执行次数",-1))]),(0,a.Lk)("div",z,[(0,a.Lk)("div",Q,(0,n.v_)(re.getSuccessfulRuns()),1),t[13]||(t[13]=(0,a.Lk)("div",{class:"data-label"},"成功执行",-1))])]),(0,a.Lk)("div",B,[t[14]||(t[14]=(0,a.Fv)('<div class="section-header" data-v-32196f23><h3 data-v-32196f23>通过率趋势</h3><div class="chart-legend" data-v-32196f23><div class="legend-item" data-v-32196f23><div class="legend-color success" data-v-32196f23></div><div class="legend-text" data-v-32196f23>通过率</div></div></div></div>',1)),(0,a.Lk)("div",M,[(0,a.Lk)("div",U,null,512)])]),(0,a.Lk)("div",K,[(0,a.Lk)("div",q,[t[16]||(t[16]=(0,a.Lk)("h3",null,"执行记录",-1)),(0,a.bF)(_e,{type:"info",size:"small"},{default:(0,a.k6)(()=>t[15]||(t[15]=[(0,a.eW)("近三天")])),_:1,__:[15]})]),(0,a.Lk)("div",j,[(0,a.bF)(we,{data:ie.records,stripe:"",style:{width:"100%"},size:"small","empty-text":"暂无执行记录",class:"records-table"},{default:(0,a.k6)(()=>[(0,a.bF)(ve,{label:"执行时间","min-width":"120"},{default:(0,a.k6)(t=>[(0,a.Lk)("div",H,[(0,a.bF)(de,null,{default:(0,a.k6)(()=>[(0,a.bF)(fe)]),_:1}),(0,a.Lk)("span",null,(0,n.v_)(e.$tools.rTime(t.row.create_time)),1)])]),_:1}),(0,a.bF)(ve,{label:"环境",width:"100"},{default:(0,a.k6)(e=>[(0,a.bF)(_e,{size:"small",effect:"plain"},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(e.row.env_name),1)]),_:2},1024)]),_:1}),(0,a.bF)(ve,{label:"状态",width:"100"},{default:(0,a.k6)(e=>[(0,a.Lk)("div",O,["执行中"===e.row.status?((0,a.uX)(),(0,a.CE)("div",G,t[17]||(t[17]=[(0,a.Lk)("div",{class:"status-indicator-dot"},null,-1),(0,a.Lk)("span",null,"执行中",-1)]))):((0,a.uX)(),(0,a.CE)("div",Y,[(0,a.bF)(de,null,{default:(0,a.k6)(()=>[(0,a.bF)(ye)]),_:1}),t[18]||(t[18]=(0,a.Lk)("span",null,"已完成",-1))]))])]),_:1}),(0,a.bF)(ve,{label:"结果","min-width":"120"},{default:(0,a.k6)(e=>["执行中"!==e.row.status?((0,a.uX)(),(0,a.CE)("div",J,[(0,a.Lk)("div",Z,[(0,a.Lk)("span",ee,(0,n.v_)(e.row.success),1),(0,a.Lk)("span",te,"/ "+(0,n.v_)(e.row.all),1),(0,a.Lk)("span",le,(0,n.v_)(e.row.pass_rate)+"%",1)]),(0,a.bF)(Ce,{percentage:parseFloat(e.row.pass_rate),"stroke-width":6,color:re.getProgressColor(e.row.pass_rate)},null,8,["percentage","color"])])):((0,a.uX)(),(0,a.CE)("div",ae,[(0,a.bF)(de,null,{default:(0,a.k6)(()=>[(0,a.bF)(Fe)]),_:1}),t[19]||(t[19]=(0,a.Lk)("span",null,"进行中...",-1))]))]),_:1}),(0,a.bF)(ve,{label:"报告",width:"80",fixed:"right"},{default:(0,a.k6)(l=>["执行中"!==l.row.status?((0,a.uX)(),(0,a.Wv)(ce,{key:0,type:"primary",link:"",onClick:t=>e.$router.push({name:"report",params:{id:l.row.id}})},{default:(0,a.k6)(()=>t[20]||(t[20]=[(0,a.eW)(" 查看 ")])),_:2,__:[20]},1032,["onClick"])):(0,a.Q3)("",!0)]),_:1})]),_:1},8,["data"])])])])])):((0,a.uX)(),(0,a.Wv)(pe,{key:1,description:"当前计划暂无测试场景",class:"center-empty"},{default:(0,a.k6)(()=>[(0,a.bF)(ce,{type:"primary",onClick:re.clickAddScene},{default:(0,a.k6)(()=>t[9]||(t[9]=[(0,a.eW)("添加测试场景")])),_:1,__:[9]},8,["onClick"])]),_:1}))])])),[[Ie,ie.loading]]),(0,a.bF)(Xe,{modelValue:ie.editDlg,"onUpdate:modelValue":t[2]||(t[2]=e=>ie.editDlg=e),title:ie.planForm.id?"编辑测试计划":"新建测试计划",width:"30%","before-close":re.clickClear,"destroy-on-close":"",top:"20vh",class:"plan-dialog"},{footer:(0,a.k6)(()=>[(0,a.Lk)("span",se,[(0,a.bF)(ce,{onClick:re.clickClear},{default:(0,a.k6)(()=>t[21]||(t[21]=[(0,a.eW)("取消")])),_:1,__:[21]},8,["onClick"]),(0,a.bF)(ce,{type:"primary",onClick:re.savePlan},{default:(0,a.k6)(()=>t[22]||(t[22]=[(0,a.eW)("保存")])),_:1,__:[22]},8,["onClick"])])]),default:(0,a.k6)(()=>[(0,a.bF)(xe,{model:ie.planForm,ref:"treeRef","label-position":"top"},{default:(0,a.k6)(()=>[(0,a.bF)(Le,{label:"计划名称",prop:"name",rules:[{required:!0,message:"请输入计划名称",trigger:"blur"}]},{default:(0,a.k6)(()=>[(0,a.bF)(ue,{modelValue:ie.planForm.name,"onUpdate:modelValue":t[1]||(t[1]=e=>ie.planForm.name=e),placeholder:"请输入计划名称",clearable:"",autofocus:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title","before-close"]),ie.sceneDlg?((0,a.uX)(),(0,a.Wv)(Pe,{key:0,onCloseModal:re.handleCloseModal,planId:ie.planId},null,8,["onCloseModal","planId"])):(0,a.Q3)("",!0)])}l(18111),l(22489),l(20116),l(18237);var ie=l(51219),re=l(93851),oe=l(12933),de=l(60782),ce=l(82484);const ue={style:{"margin-left":"20px"}},pe={style:{"margin-top":"15px","margin-right":"20px"}},he={key:0},ke={key:1},ge={key:0},me={class:"pagination-container"},be={style:{padding:"20px"}},_e={style:{color:"#00aaff"}},fe={style:{color:"#00aa7f"}},ve={style:{color:"orangered"}},ye={style:{color:"#fca130"}},Ce={key:0},Fe={key:0},we={key:0,style:{color:"#00AA7F"}},Le={key:1,style:{color:"#fca130"}},xe={key:2,style:{color:"#F56C6C"}};function Xe(e,t,l,s,i,r){const o=(0,a.g2)("el-button"),d=(0,a.g2)("el-input"),c=(0,a.g2)("el-table-column"),u=(0,a.g2)("router-link"),p=(0,a.g2)("el-tooltip"),h=(0,a.g2)("el-table"),k=(0,a.g2)("el-pagination"),g=(0,a.g2)("el-scrollbar"),m=(0,a.g2)("el-dialog"),b=(0,a.g2)("el-descriptions-item"),_=(0,a.g2)("el-descriptions"),f=(0,a.g2)("caseResult"),v=(0,a.g2)("el-drawer");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.bF)(m,{modelValue:i.sceneDlg,"onUpdate:modelValue":t[1]||(t[1]=e=>i.sceneDlg=e),title:"添加场景",width:"75%","before-close":r.clickClear,top:"0"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",ue,[(0,a.Lk)("div",null,[(0,a.bF)(d,{style:{width:"330px"},modelValue:i.filterText,"onUpdate:modelValue":t[0]||(t[0]=e=>i.filterText=e),placeholder:"请输入用例名称进行搜索",clearable:""},{append:(0,a.k6)(()=>[(0,a.bF)(o,{type:"primary",onClick:r.searchClick},{default:(0,a.k6)(()=>t[3]||(t[3]=[(0,a.eW)("查询")])),_:1,__:[3]},8,["onClick"])]),_:1},8,["modelValue"]),(0,a.Lk)("span",null,[(0,a.bF)(o,{type:"warning",style:{float:"right","margin-right":"19px"},onClick:r.clickClear,icon:i.Close},{default:(0,a.k6)(()=>t[4]||(t[4]=[(0,a.eW)("关闭窗口 ")])),_:1,__:[4]},8,["onClick","icon"]),(0,a.bF)(o,{type:"primary",style:{float:"right","margin-right":"15px"},onClick:r.addScentToPlan,icon:i.Check},{default:(0,a.k6)(()=>t[5]||(t[5]=[(0,a.eW)("确认选择 ")])),_:1,__:[5]},8,["onClick","icon"])])]),(0,a.bF)(g,{height:"calc(100vh - 110px)"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",pe,[(0,a.bF)(h,{data:i.caseList,stripe:"","empty-text":"暂无数据",border:"",onSelectionChange:r.handleSelectionChange},{default:(0,a.k6)(()=>[(0,a.bF)(c,{type:"selection",align:"center",width:"50"}),(0,a.bF)(c,{label:"序号",align:"center",width:"60"},{default:(0,a.k6)(e=>[(0,a.Lk)("span",null,(0,n.v_)(e.$index+1),1)]),_:1}),(0,a.bF)(c,{label:"用例名称",align:"center"},{default:(0,a.k6)(e=>[(0,a.bF)(u,{class:"no-underline",to:"/TestCaseDetail/",style:{color:"#409eff"},onClick:t=>r.clickEdit(e.row)},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(e.row.name),1)]),_:2},1032,["onClick"])]),_:1}),(0,a.bF)(c,{label:"所属项目",prop:"project.name",align:"center"}),(0,a.bF)(c,{label:"步骤数",width:"70",prop:"stepCount",align:"center"}),(0,a.bF)(c,{label:"用例描述",prop:"desc",align:"center"},{default:(0,a.k6)(e=>[(0,a.bF)(p,{class:"item",effect:"dark",content:e.row.desc,placement:"top"},{default:(0,a.k6)(()=>[e.row.desc.length>16?((0,a.uX)(),(0,a.CE)("div",he,(0,n.v_)(e.row.desc.slice(0,16))+"...",1)):((0,a.uX)(),(0,a.CE)("div",ke,(0,n.v_)(e.row.desc),1))]),_:2},1032,["content"])]),_:1}),(0,a.bF)(c,{label:"创建人",prop:"creator",align:"center",width:"120"}),(0,a.bF)(c,{label:"创建时间",align:"center",width:"160"},{default:(0,a.k6)(t=>[(0,a.eW)((0,n.v_)(e.$tools.rTime(t.row.create_time)),1)]),_:1}),(0,a.bF)(c,{label:"更新人",prop:"modifier",align:"center",width:"120"}),(0,a.bF)(c,{label:"更新时间",align:"center",width:"160"},{default:(0,a.k6)(t=>[t.row.update_time?((0,a.uX)(),(0,a.CE)("a",ge,(0,n.v_)(e.$tools.rTime(t.row.update_time)),1)):(0,a.Q3)("",!0)]),_:1}),(0,a.bF)(c,{label:"操作",width:"330",align:"center"},{default:(0,a.k6)(e=>[(0,a.bF)(o,{onClick:t=>r.runCase(e.row),size:"small",type:"primary",icon:i.Promotion},{default:(0,a.k6)(()=>t[6]||(t[6]=[(0,a.eW)("运行")])),_:2,__:[6]},1032,["onClick","icon"]),(0,a.bF)(o,{onClick:t=>r.clickEdit(e.row),size:"small",type:"warning",icon:i.Menu},{default:(0,a.k6)(()=>t[7]||(t[7]=[(0,a.eW)("添加管理步骤")])),_:2,__:[7]},1032,["onClick","icon"]),(0,a.bF)(o,{onClick:t=>r.delCase(e.row.id),size:"small",type:"danger",plain:"",icon:i.Delete},{default:(0,a.k6)(()=>t[8]||(t[8]=[(0,a.eW)("删除")])),_:2,__:[8]},1032,["onClick","icon"])]),_:1})]),_:1},8,["data","onSelectionChange"])]),(0,a.Lk)("div",me,[(0,a.bF)(k,{background:"",layout:"total, prev, pager, next, jumper",onCurrentChange:r.currentPages,"default-page-size":100,total:i.pages.count,"current-page":i.pages.current,"next-text":"下一页","prev-text":"上一页"},null,8,["onCurrentChange","total","current-page"])])]),_:1})])]),_:1},8,["modelValue","before-close"]),(0,a.bF)(v,{modelValue:i.ResultDlg,"onUpdate:modelValue":t[2]||(t[2]=e=>i.ResultDlg=e),"with-header":!1,size:"50%"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",be,[(0,a.bF)(_,{title:"执行结果",border:"",column:4,style:{"text-align":"center"}},{default:(0,a.k6)(()=>[(0,a.bF)(b,{label:"总数"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",_e,(0,n.v_)(i.runScentResult.all),1)]),_:1}),(0,a.bF)(b,{label:"通过"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",fe,(0,n.v_)(i.runScentResult.success),1)]),_:1}),(0,a.bF)(b,{label:"失败"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",ve,(0,n.v_)(i.runScentResult.fail),1)]),_:1}),(0,a.bF)(b,{label:"错误"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",ye,(0,n.v_)(i.runScentResult.error),1)]),_:1})]),_:1}),t[9]||(t[9]=(0,a.Lk)("div",{style:{height:"40px","line-height":"40px"}},[(0,a.Lk)("b",null,"执行详情")],-1)),(0,a.bF)(g,{height:"calc(100vh - 180px)"},{default:(0,a.k6)(()=>[(0,a.bF)(h,{data:i.runScentResult.cases,style:{width:"100%"},"empty-text":"暂无数据"},{default:(0,a.k6)(()=>[(0,a.bF)(c,{type:"expand"},{default:(0,a.k6)(e=>[(0,a.bF)(f,{result:e.row},null,8,["result"])]),_:1}),(0,a.bF)(c,{label:"步骤名",prop:"name"}),(0,a.bF)(c,{label:"请求方法",prop:"method"},{default:(0,a.k6)(e=>["api"===e.row.type?((0,a.uX)(),(0,a.CE)("span",Ce,(0,n.v_)(e.row.method),1)):(0,a.Q3)("",!0)]),_:1}),(0,a.bF)(c,{label:"响应状态码",prop:"status_cede"},{default:(0,a.k6)(e=>["api"===e.row.type?((0,a.uX)(),(0,a.CE)("span",Fe,(0,n.v_)(e.row.status_cede),1)):(0,a.Q3)("",!0)]),_:1}),(0,a.bF)(c,{label:"执行结果",prop:"state","min-width":"40px"},{default:(0,a.k6)(e=>["成功"==e.row.state?((0,a.uX)(),(0,a.CE)("span",we,(0,n.v_)(e.row.state),1)):"错误"==e.row.state?((0,a.uX)(),(0,a.CE)("span",Le,(0,n.v_)(e.row.state),1)):((0,a.uX)(),(0,a.CE)("span",xe,(0,n.v_)(e.row.state),1))]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1},8,["modelValue"])],64)}l(61701);var Pe=l(67638),Ie=l(57477),Ee={props:{planId:String},computed:{...(0,de.aH)(["pro","envId"])},components:{caseResult:Pe.A},data(){return{filterText:"",pages:[],caseList:[],ResultDlg:!1,runScentResult:"",sceneDlg:!0,selectedRows:[],Check:Ie.Check,Close:Ie.Close,Delete:Ie.Delete,Menu:Ie.Menu,Promotion:Ie.Promotion}},methods:{...(0,de.PY)(["CaseInfo"]),searchClick(){this.allTestCase(this.pro.id,this.pages.current,this.filterText)},clickEdit(e){this.$router.push({name:"TestCaseDetail"}),this.CaseInfo(e)},async allTestCase(e,t,l){const a=await this.$api.getTestCase(e,t,l);if(200===a.status){const e=a.data.result;this.pages=a.data;const t=await this.$api.getTestCase_({testplan:this.planId});if(200===t.status){const l=t.data.result;this.caseList=e.filter(e=>!l.map(e=>e.id).includes(e.id)),this.caseList=this.caseList.filter(e=>0!==e.stepCount),this.pages.count=this.caseList.length}}},currentPages(e){this.allTestCase(this.pro.id,e),this.pages.current=e},delCase(e){oe.s.confirm("此操作将永久删除该用例, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const t=await this.$api.delTestCase(e);204===t.status&&((0,ie.nk)({type:"success",message:"删除成功!"}),this.allTestCase(this.pro.id),this.filterText="")}).catch(()=>{(0,ie.nk)({type:"info",message:"已取消删除"})})},async runCase(e){const t=parseInt(e.stepCount);if(console.log(t),t>0)if(this.envId){const t={env:this.envId,scene:e.id};(0,re.df)({title:"开始运行",message:"运行过程中请稍等片刻噢",type:"success",duration:1e3});const l=await this.$api.runCases(e.id,t);200==l.status&&(this.runScentResult=l.data,this.ResultDlg=!0)}else(0,ie.nk)({type:"warning",message:"当前未选中执行环境!",duration:1e3});else(0,ie.nk)({type:"warning",message:"请添加步骤后再运行",duration:1e3})},closeModal(){this.$emit("close-modal")},clickClear(){this.closeModal()},async addScentToPlan(){if(0===this.selectedRows.length)return void(0,ie.nk)({type:"warning",message:"请勾选要添加的测试场景",duration:1e3});let e={};e.scene_ids=[...this.selectedRows];const t=await this.$api.createTestPlanScene(this.planId,e);200===t.status&&(0,ie.nk)({type:"success",message:"添加成功",duration:1e3}),this.closeModal()},handleSelectionChange(e){this.selectedRows=e.map(e=>e.id),console.log(this.selectedRows)}},created(){this.allTestCase(this.pro.id)}},Te=l(71241);const De=(0,Te.A)(Ee,[["render",Xe],["__scopeId","data-v-2d1b3e38"]]);var Re=De,We={components:{Icon:ce.In,TestCase:Re},data(){return{planList:[],planId:"",records:[],planForm:{name:"",id:null},filterText:"",editDlg:!1,scene_list:[],sceneDlg:!1,loading:!1,currentPlanName:"",themeColors:{success:"#67C23A",warning:"#E6A23C",danger:"#F56C6C",info:"#909399"}}},computed:{...(0,de.aH)(["pro","envId"]),defaultProps(){return{children:"children",label:"name"}},chartData:function(){let e=[],t=[];for(let l of this.records)e.push(this.$tools.rTime(l.create_time)),t.push(parseFloat(l.pass_rate).toFixed(2));return{label:e.reverse(),value:t}}},methods:{...(0,de.PY)(["CaseInfo"]),async getAllPlan(e){this.loading=!0;try{let t;t=e?await this.$api.getTestPlans(this.pro.id,e):await this.$api.getTestPlans(this.pro.id),200===t.status&&(this.planList=t.data,this.planList.length>0?this.planId&&this.planList.find(e=>e.id===this.planId)?this.updateCurrentPlanName():this.selectPlan(this.planList[0].id):(this.planId="",this.currentPlanName="",this.scene_list=[],this.records=[]))}catch(t){(0,ie.nk)({type:"error",message:"获取测试计划失败",duration:1500}),console.error("获取测试计划失败",t)}finally{this.loading=!1}},updateCurrentPlanName(){if(this.planId){const e=this.planList.find(e=>e.id===this.planId);e&&(this.currentPlanName=e.name)}else this.currentPlanName=""},async getAllRecord(){try{const e=await this.$api.getTestRecord({plan:this.planId,project:this.pro.id});200===e.status&&(this.records=e.data)}catch(e){console.error("获取执行记录失败",e)}},selectPlan(e){this.planId!==e&&(this.planId=e,this.updateCurrentPlanName(),this.getScenes(e))},async addPlan(){this.planForm={name:"新测试计划",id:null},this.editDlg=!0},editPlan(e){this.planForm={...e},this.editDlg=!0},handleNodeClick(e){this.selectPlan(e.id)},async getScenes(e){this.loading=!0;try{const t=await this.$api.getTestCase_({testplan:e});200===t.status&&(this.scene_list=t.data.result),this.getAllRecord()}catch(t){(0,ie.nk)({type:"error",message:"获取测试场景失败",duration:1500}),console.error("获取测试场景失败",t)}finally{this.loading=!1}},handletreeClick(){this.getAllPlan(this.filterText)},clickClear(){this.editDlg=!1},async savePlan(){if(this.planForm.name)try{let e;if(this.planForm.id)e=await this.$api.updateTestPlan(this.planForm.id,this.planForm);else{const t={project:this.pro.id,name:this.planForm.name};e=await this.$api.createTestPlan(t)}200!==e.status&&201!==e.status||((0,ie.nk)({type:"success",message:this.planForm.id?"保存成功":"创建成功",duration:1500}),this.editDlg=!1,await this.getAllPlan(),!this.planForm.id&&e.data&&e.data.id?this.selectPlan(e.data.id):this.planForm.id===this.planId&&this.updateCurrentPlanName())}catch(e){(0,ie.nk)({type:"error",message:"操作失败",duration:1500}),console.error("保存计划失败",e)}else(0,ie.nk)({type:"warning",message:"请输入计划名称",duration:1500})},async runPlan(){if(this.planId)if(this.envId){const t={env:this.envId,plan:this.planId,types:2};(0,re.df)({title:"开始运行",message:"测试计划正在执行中，请稍候...",type:"success",duration:3e3});try{const e=await this.$api.runPlan(this.planId,t);200===e.status&&this.getAllRecord()}catch(e){(0,ie.nk)({type:"error",message:"运行计划失败",duration:1500}),console.error("运行计划失败",e)}}else(0,ie.nk)({type:"warning",message:"当前未选中执行环境!",duration:1500});else(0,ie.nk)({type:"warning",message:"请先选择测试计划",duration:1500})},delPlan(e){oe.s.confirm("确定要删除该测试计划吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const t=await this.$api.deleteTestPlan(e);204===t.status&&((0,ie.nk)({type:"success",message:"删除成功",duration:1500}),await this.getAllPlan(),e===this.planId&&(this.planId=this.planList.length>0?this.planList[0].id:"",this.planId?this.getScenes(this.planId):(this.scene_list=[],this.records=[])))}catch(t){(0,ie.nk)({type:"error",message:"删除失败",duration:1500}),console.error("删除计划失败",t)}}).catch(()=>{(0,ie.nk)({type:"info",message:"已取消删除",duration:1500})})},clickView(e){this.$router.push({name:"TestCaseDetail"}),e.back_type="plan",this.CaseInfo(e)},handleSceneCommand(e){"delete"===e.type&&this.delScene(e.id)},async delScene(e){try{let t={scene_id:e};const l=await this.$api.deleteTestPlanScene(this.planId,t);200===l.status&&((0,ie.nk)({type:"success",message:"删除成功",duration:1500}),this.getScenes(this.planId))}catch(t){(0,ie.nk)({type:"error",message:"删除失败",duration:1500}),console.error("删除场景失败",t)}},clickAddScene(){this.sceneDlg=!0},handleCloseModal(){this.sceneDlg=!1,this.getScenes(this.planId)},getAveragePassRate(){if(!this.records||0===this.records.length)return 0;const e=this.records.filter(e=>"执行中"!==e.status);if(0===e.length)return 0;const t=e.reduce((e,t)=>e+parseFloat(t.pass_rate),0);return(t/e.length).toFixed(2)},getSuccessfulRuns(){return this.records?this.records.filter(e=>"执行中"!==e.status&&parseFloat(e.pass_rate)>=80).length:0},getProgressColor(e){const t=parseFloat(e);return t>=80?this.themeColors.success:t>=60?this.themeColors.warning:this.themeColors.danger},getSceneStatusClass(e){return"running"===e.status?"running":e.stepCount&&0!==e.stepCount?e.lastResult?e.lastResult.success?"success":"failure":"ready":"not-configured"},getSceneStatusText(e){return"running"===e.status?"执行中":e.stepCount&&0!==e.stepCount?e.lastResult?e.lastResult.success?"执行成功":"执行失败":"准备就绪":"未配置步骤"}},created(){this.getAllPlan()},watch:{records(){this.$nextTick(()=>{this.$refs.chartTable&&this.$chart.chart3(this.$refs.chartTable,this.chartData.value,this.chartData.label)})}}};const Se=(0,Te.A)(We,[["render",ne],["__scopeId","data-v-32196f23"]]);var $e=Se}}]);
//# sourceMappingURL=61.901e73d3.js.map