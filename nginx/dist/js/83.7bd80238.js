"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[83],{29083:function(t,e,a){a.r(e),a.d(e,{default:function(){return kt}});var s=a(56768),l=a(24232);const i={class:"dashboard"},r={class:"card-header"},n={class:"stat-title"},o={key:0,class:"stat-change"},c={class:"percentage increase-icon"},d={key:1,class:"stat-change"},u={class:"percentage decrease-icon"},h={key:2,class:"stat-change"},p={class:"percentage increase-icon"},m={key:3,class:"stat-change"},g={class:"percentage decrease-icon"},f={key:4,class:"job-status"},k={class:"paused-status"},b={class:"card-header-flex"},v={class:"header-title"},y={class:"date-filter-wrapper"},_={class:"date-filter"},w={class:"chart-container"},L={class:"card-header-flex"},C={class:"header-title"},x={class:"chart-container"},F={class:"card-header-flex"},T={class:"header-title"},V={class:"chart-container"},D={class:"card-header-flex"},S={class:"header-title"},A={class:"timeline-container"},W={class:"log-item"},X={class:"log-url"},E={class:"log-details"},$={class:"log-value"},q={class:"log-time"},I={class:"card-header-flex"},z={class:"header-title"},R={class:"chart-container"};function B(t,e,a,B,N,P){const Q=(0,s.g2)("countTo"),j=(0,s.g2)("Top"),M=(0,s.g2)("el-icon"),H=(0,s.g2)("Bottom"),O=(0,s.g2)("el-card"),Y=(0,s.g2)("el-col"),G=(0,s.g2)("el-row"),K=(0,s.g2)("Suitcase"),U=(0,s.g2)("Calendar"),J=(0,s.g2)("el-button"),Z=(0,s.g2)("el-date-picker"),tt=(0,s.g2)("Search"),et=(0,s.g2)("el-popover"),at=(0,s.g2)("ApiChart"),st=(0,s.g2)("DataAnalysis"),lt=(0,s.g2)("WeekLoginChart"),it=(0,s.g2)("PieChart"),rt=(0,s.g2)("BugChart"),nt=(0,s.g2)("Tickets"),ot=(0,s.g2)("el-tag"),ct=(0,s.g2)("el-timeline-item"),dt=(0,s.g2)("el-timeline"),ut=(0,s.g2)("ReportChart"),ht=(0,s.g2)("el-scrollbar"),pt=(0,s.gN)("loading");return(0,s.uX)(),(0,s.Wv)(ht,{height:"calc(100vh - 50px)"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",i,[(0,s.bF)(G,{gutter:12,class:"mb-30"},{default:(0,s.k6)(()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(N.proInfo,(t,a)=>((0,s.uX)(),(0,s.Wv)(Y,{key:a,xs:24,sm:12,md:8,lg:6,xl:3,class:"mb-18"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(O,{shadow:"hover",class:"stat-card hover-scale"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",r,[(0,s.Lk)("div",n,(0,l.v_)(t.name),1),(0,s.bF)(Q,{class:"stat-value","start-val":0,"end-val":t.count,duration:2600},null,8,["end-val"])]),"lastIncrease"===t.changeType?((0,s.uX)(),(0,s.CE)("div",o,[e[1]||(e[1]=(0,s.Lk)("span",null,"自上周增长",-1)),(0,s.Lk)("span",c,[(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(j)]),_:1}),(0,s.eW)((0,l.v_)(t.percentage),1)])])):(0,s.Q3)("",!0),"lastDecrease"===t.changeType?((0,s.uX)(),(0,s.CE)("div",d,[e[2]||(e[2]=(0,s.Lk)("span",null,"自上周下降",-1)),(0,s.Lk)("span",u,[(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(H)]),_:1}),(0,s.eW)((0,l.v_)(t.percentage),1)])])):(0,s.Q3)("",!0),"yastdayIncrease"===t.changeType?((0,s.uX)(),(0,s.CE)("div",h,[e[3]||(e[3]=(0,s.Lk)("span",null,"自昨日增长",-1)),(0,s.Lk)("span",p,[(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(j)]),_:1}),(0,s.eW)((0,l.v_)(t.percentage),1)])])):(0,s.Q3)("",!0),"yastdayDecrease"===t.changeType?((0,s.uX)(),(0,s.CE)("div",m,[e[4]||(e[4]=(0,s.Lk)("span",null,"自昨日下降",-1)),(0,s.Lk)("span",g,[(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(H)]),_:1}),(0,s.eW)((0,l.v_)(t.percentage),1)])])):(0,s.Q3)("",!0),"job"===t.changeType?((0,s.uX)(),(0,s.CE)("div",f,[(0,s.Lk)("span",null,[e[5]||(e[5]=(0,s.eW)("运行中：")),(0,s.Lk)("b",null,(0,l.v_)(t.run_service),1)]),(0,s.Lk)("span",k,[e[6]||(e[6]=(0,s.eW)("已暂停：")),(0,s.Lk)("b",null,(0,l.v_)(t.paused),1)])])):(0,s.Q3)("",!0)]),_:2},1024)),[[pt,N.loading]])]),_:2},1024))),128))]),_:1}),(0,s.bF)(G,{gutter:18,class:"mb-20"},{default:(0,s.k6)(()=>[N.proCase&&N.proCase.length>0?((0,s.uX)(),(0,s.Wv)(Y,{key:0,xs:24,sm:24,md:12,lg:9,class:"mb-18"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(O,{shadow:"hover",class:"chart-card"},{header:(0,s.k6)(()=>[(0,s.Lk)("div",b,[(0,s.Lk)("div",v,[e[7]||(e[7]=(0,s.Lk)("strong",null,"近七日接口维护统计",-1)),(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(K)]),_:1})]),(0,s.Lk)("div",y,[(0,s.bF)(et,{placement:"bottom",trigger:"click",width:"auto","popper-class":"date-filter-popover"},{reference:(0,s.k6)(()=>[(0,s.bF)(J,{type:"primary",size:"small",class:"filter-btn"},{default:(0,s.k6)(()=>[(0,s.bF)(M,{style:{"margin-right":"5px"}},{default:(0,s.k6)(()=>[(0,s.bF)(U)]),_:1}),e[8]||(e[8]=(0,s.eW)(" 时间筛选 "))]),_:1,__:[8]})]),default:(0,s.k6)(()=>[(0,s.Lk)("div",_,[(0,s.bF)(Z,{modelValue:N.dataTime,"onUpdate:modelValue":e[0]||(e[0]=t=>N.dataTime=t),type:"datetimerange","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss","default-time":N.defaultTimeOptions,shortcuts:N.shortcuts,"range-separator":"至",clearable:!0,class:"date-picker"},null,8,["modelValue","default-time","shortcuts"]),(0,s.bF)(J,{type:"primary",onClick:P.submitForm,class:"search-btn",loading:N.loading},{default:(0,s.k6)(()=>[(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(tt)]),_:1}),e[9]||(e[9]=(0,s.eW)("查询 "))]),_:1,__:[9]},8,["onClick","loading"])])]),_:1})])])]),default:(0,s.k6)(()=>[(0,s.Lk)("div",w,[(0,s.bF)(at,{testData:N.proCase},null,8,["testData"])])]),_:1})),[[pt,N.loading]])]),_:1})):(0,s.Q3)("",!0),N.buttonClick&&N.buttonClick.length>0?((0,s.uX)(),(0,s.Wv)(Y,{key:1,xs:24,sm:24,md:12,lg:8,class:"mb-18"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(O,{shadow:"hover",class:"chart-card"},{header:(0,s.k6)(()=>[(0,s.Lk)("div",L,[(0,s.Lk)("div",C,[e[10]||(e[10]=(0,s.Lk)("strong",null,"近七日平台使用频率",-1)),(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(st)]),_:1})])])]),default:(0,s.k6)(()=>[(0,s.Lk)("div",x,[(0,s.bF)(lt,{testData:N.buttonClick},null,8,["testData"])])]),_:1})),[[pt,N.loading]])]),_:1})):(0,s.Q3)("",!0),N.proBug&&N.proBug.length>0?((0,s.uX)(),(0,s.Wv)(Y,{key:2,xs:24,sm:24,md:12,lg:7,class:"mb-18"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(O,{shadow:"hover",class:"chart-card"},{header:(0,s.k6)(()=>[(0,s.Lk)("div",F,[(0,s.Lk)("div",T,[e[11]||(e[11]=(0,s.Lk)("strong",null,"bug处理情况",-1)),(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(it)]),_:1})])])]),default:(0,s.k6)(()=>[(0,s.Lk)("div",V,[(0,s.bF)(rt,{testData:N.proBug},null,8,["testData"])])]),_:1})),[[pt,N.loading]])]),_:1})):(0,s.Q3)("",!0)]),_:1}),(0,s.bF)(G,{gutter:18},{default:(0,s.k6)(()=>[N.mockLog&&N.mockLog.length>0?((0,s.uX)(),(0,s.Wv)(Y,{key:0,xs:24,sm:24,md:12,lg:9,class:"mb-18"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(O,{shadow:"hover",class:"log-card"},{header:(0,s.k6)(()=>[(0,s.Lk)("div",D,[(0,s.Lk)("div",S,[e[12]||(e[12]=(0,s.Lk)("strong",null,"Mock日志",-1)),(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(nt)]),_:1})])])]),default:(0,s.k6)(()=>[(0,s.Lk)("div",A,[(0,s.bF)(dt,null,{default:(0,s.k6)(()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(N.mockLog,(a,i)=>((0,s.uX)(),(0,s.Wv)(ct,{key:i,timestamp:t.$tools.rTime(a.create_time),placement:"top",color:"#0bbd87"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",W,["GET"===a.method?((0,s.uX)(),(0,s.Wv)(ot,{key:0,type:"success",size:"small"},{default:(0,s.k6)(()=>[(0,s.eW)((0,l.v_)(a.method),1)]),_:2},1024)):((0,s.uX)(),(0,s.Wv)(ot,{key:1,size:"small"},{default:(0,s.k6)(()=>[(0,s.eW)((0,l.v_)(a.method),1)]),_:2},1024)),(0,s.Lk)("span",X,(0,l.v_)(a.url),1),(0,s.Lk)("div",E,[e[13]||(e[13]=(0,s.Lk)("span",{class:"log-label"},"调用IP：",-1)),(0,s.Lk)("span",$,(0,l.v_)(a.ip),1),e[14]||(e[14]=(0,s.Lk)("span",{class:"log-label"},"HTTP状态码：",-1)),(0,s.Lk)("span",{class:(0,l.C4)(["log-status",{"status-success":"200"===a.status_code,"status-warning":"400"===a.status_code,"status-error":"500"===a.status_code}])},(0,l.v_)(a.status_code),3),(0,s.Lk)("span",q,(0,l.v_)(a.time_consuming),1)])])]),_:2},1032,["timestamp"]))),128))]),_:1})])]),_:1})),[[pt,N.loading]])]),_:1})):(0,s.Q3)("",!0),N.proReport&&N.proReport.length>0?((0,s.uX)(),(0,s.Wv)(Y,{key:1,xs:24,sm:24,md:12,lg:15,class:"mb-18"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(O,{shadow:"hover",class:"chart-card"},{header:(0,s.k6)(()=>[(0,s.Lk)("div",I,[(0,s.Lk)("div",z,[e[15]||(e[15]=(0,s.Lk)("strong",null,"近三天报告运行情况统计",-1)),(0,s.bF)(M,null,{default:(0,s.k6)(()=>[(0,s.bF)(st)]),_:1})]),e[16]||(e[16]=(0,s.Lk)("div",{class:"header-info"},[(0,s.Lk)("span",null,"通过率(%)")],-1))])]),default:(0,s.k6)(()=>[(0,s.Lk)("div",R,[(0,s.bF)(ut,{testData:N.proReport},null,8,["testData"])])]),_:1})),[[pt,N.loading]])]),_:1})):(0,s.Q3)("",!0)]),_:1})])]),_:1})}var N=a(70683),P=a(64418),Q=a(77962);const j={ref:"echarts",style:{height:"400px"}};function M(t,e,a,l,i,r){return(0,s.uX)(),(0,s.CE)("div",j,null,512)}a(18111),a(61701);var H=a(91006),O={props:{testData:{type:Array,required:!0}},data(){return{option:{color:["#409eff","#67c23a","#e6a23c"],legend:{x:"center",y:"bottom"},tooltip:{},dataset:{dimensions:["product","接口调试","新增接口","新增用例"],source:[]},xAxis:{type:"category"},yAxis:{max:null},series:[{type:"bar"},{type:"bar"},{type:"bar"}]}}},mounted(){this.renderChart()},updated(){this.renderChart()},methods:{renderChart(){this.option.dataset.source=this.testData.map(t=>[t.user,t.interface_debug_count,t.interface_new_count,t.testcase_new_count]);const t=this.$refs.echarts,e=H.Ts(t);e.setOption(this.option)}}},Y=a(71241);const G=(0,Y.A)(O,[["render",M],["__scopeId","data-v-f376b006"]]);var K=G;const U={ref:"chart",style:{height:"400px"}};function J(t,e,a,l,i,r){return(0,s.uX)(),(0,s.CE)("div",U,null,512)}var Z={props:{testData:{type:Array,required:!0}},data(){return{chart:null,option:{xAxis:{type:"category",boundaryGap:!1,axisLine:{lineStyle:{color:"rgb(103, 77, 204)",width:5}},axisTick:{show:!1},axisLabel:{color:"black",margin:15},splitLine:{show:!1},data:[]},yAxis:{type:"value",boundaryGap:[0,"30%"],axisLine:{lineStyle:{color:"rgb(103, 77, 204)",width:5}},axisLabel:{color:"black"},splitLine:{show:!1}},visualMap:{type:"piecewise",show:!1,dimension:0,seriesIndex:0,pieces:[{gt:0,lt:7,color:"#66b1ff"}]},series:[{type:"line",smooth:.6,symbol:"none",lineStyle:{color:"#0d84ff",width:5},areaStyle:{color:new H.fA.W4(0,0,0,1,[{offset:0,color:"#79bbff"},{offset:.5,color:"#a0cfff"},{offset:1,color:"#c6e2ff"}],!1)},data:[]}]}}},mounted(){this.initChart()},updated(){this.initChart()},methods:{initChart(){this.chart=H.Ts(this.$refs.chart),this.option.xAxis.data=this.testData.map(t=>t.date),this.option.series[0].data=this.testData.map(t=>t.clicks),this.chart.setOption(this.option)}}};const tt=(0,Y.A)(Z,[["render",J]]);var et=tt;const at={ref:"chart",style:{height:"400px"}};function st(t,e,a,l,i,r){return(0,s.uX)(),(0,s.CE)("div",at,null,512)}var lt={props:{testData:{type:Array,required:!0}},data(){return{}},mounted(){this.initChart()},updated(){this.initChart()},methods:{initChart(){const t=H.Ts(this.$refs.chart),e={tooltip:{trigger:"item",formatter:"{d}%【{c}条】"},legend:{orient:"vertical",left:"75%",top:"35%"},series:[{name:"Access From",type:"pie",center:["40%","50%"],radius:["50%","60%"],avoidLabelOverlap:!1,padAngle:5,itemStyle:{borderRadius:10},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:20,fontWeight:"bold"}},labelLine:{show:!1},data:this.testData}]};t.setOption(e)}}};const it=(0,Y.A)(lt,[["render",st]]);var rt=it;const nt={ref:"chart",style:{height:"400px"}};function ot(t,e,a,l,i,r){return(0,s.uX)(),(0,s.CE)("div",nt,null,512)}var ct={props:{testData:{type:Array,required:!0}},mounted(){this.initChart()},updated(){this.initChart()},methods:{initChart(){const t=H.Ts(this.$refs.chart),e={grid:{top:50,bottom:10,left:20,right:20,containLabel:!0},tooltip:{trigger:"axis",formatter:t=>{const e=t[0].dataIndex,a=t[0].axisValueLabel,s=t[0].value;return`${this.testData[e].plan_id__name} <br/>${a} <br/> 通过率：${s}%`},axisPointer:{type:"line",lineStyle:{color:"#95d475"}}},xAxis:{type:"category",boundaryGap:!1,axisLabel:{show:!1},axisLine:{lineStyle:{color:"rgb(251, 212, 55)",width:5}},axisTick:{show:!1},data:this.testData.map(t=>t.create_time)},yAxis:{type:"value",nameTextStyle:{color:"#fff",fontSize:12,lineHeight:40},splitLine:{lineStyle:{color:"#eef5f0"}},axisLine:{lineStyle:{}},axisTick:{show:!1}},series:[{name:"通过率",type:"line",smooth:!0,showSymbol:!0,symbolSize:8,zlevel:3,itemStyle:{color:"#67C23A",borderColor:"#a3c8d8"},lineStyle:{width:3,color:"#67C23A"},areaStyle:{color:new H.fA.W4(0,0,0,1,[{offset:0,color:"#95d475"},{offset:.5,color:"#b3e19d"},{offset:1,color:"#d1edc4"}],!1)},data:this.testData.map(t=>parseFloat(t.pass_rate))}]};t.setOption(e)}}};const dt=(0,Y.A)(ct,[["render",ot],["__scopeId","data-v-11044330"]]);var ut=dt,ht=a(60782),pt=a(64230),mt=a(57477),gt={components:{ElCard:N.Ik,ElRow:P.S2,ElCol:Q.uD,ApiChart:K,countTo:pt.A,WeekLoginChart:et,BugChart:rt,ReportChart:ut,Top:mt.Top,Bottom:mt.Bottom,Suitcase:mt.Suitcase,Calendar:mt.Calendar,Search:mt.Search,DataAnalysis:mt.DataAnalysis,PieChart:mt.PieChart,Tickets:mt.Tickets},data(){const t=new Date,e=new Date;e.setTime(e.getTime()-6048e5);const a=t=>{const e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0");return`${e}-${a}-${s} ${0===t.getHours()?"00":t.getHours()}:${0===t.getMinutes()?"00":t.getMinutes()}:${0===t.getSeconds()?"00":t.getSeconds()}`};return{proall:null,proInfo:null,proBug:null,proCase:null,proReport:null,buttonClick:null,mockLog:null,defaultTimeOptions:["00:00:00","23:59:59"],dataTime:[a(e),a(t)],shortcuts:[{text:"过去一周",value:()=>{const t=new Date,e=new Date;return e.setTime(e.getTime()-6048e5),[e,t]}},{text:"过去一个月",value:()=>{const t=new Date,e=new Date;return e.setTime(e.getTime()-2592e6),[e,t]}},{text:"过去三个月",value:()=>{const t=new Date,e=new Date;return e.setTime(e.getTime()-7776e6),[e,t]}}],screenSize:{xs:!1,sm:!1,md:!1,lg:!1,xl:!1},loading:!1}},methods:{...(0,ht.PY)(["selectPro"]),async getProInfo(t,e){this.loading=!0;try{let a={project:this.pro.id};t&&e&&(a={project:this.pro.id,starttime:t,endtime:e});const s=await this.$api.getProjectBoard(a);200===s.status&&(this.proall=s.data,this.proInfo=this.proall.project_info,this.proBug=this.proall.project_bug,this.proCase=this.proall.project_case,this.proReport=this.proall.project_report,this.buttonClick=this.proall.track_button_click,this.mockLog=this.proall.mock_log)}catch(a){console.error("获取项目看板数据失败:",a)}finally{this.loading=!1}},submitForm(){this.dataTime||(this.dataTime=[]);const t=this.dataTime[0],e=this.dataTime[1];this.getProInfo(t,e)},handleResize(){const t=window.innerWidth;this.screenSize={xs:t<576,sm:t>=576&&t<768,md:t>=768&&t<992,lg:t>=992&&t<1200,xl:t>=1200}}},computed:{...(0,ht.aH)(["pro"])},created(){const t=this.dataTime[0],e=this.dataTime[1];this.getProInfo(t,e),this.handleResize(),window.addEventListener("resize",this.handleResize)},beforeUnmount(){window.removeEventListener("resize",this.handleResize)}};const ft=(0,Y.A)(gt,[["render",B],["__scopeId","data-v-e162c9a6"]]);var kt=ft},64230:function(t,e,a){a.d(e,{A:function(){return g}});var s=a(56768),l=a(24232);function i(t,e,a,i,r,n){return(0,s.uX)(),(0,s.CE)("span",null,(0,l.v_)(r.displayValue),1)}let r=0;const n="webkit moz ms o".split(" ");let o,c;const d="undefined"===typeof window;if(d)o=function(){},c=function(){};else{let t;o=window.requestAnimationFrame,c=window.cancelAnimationFrame;for(let e=0;e<n.length;e++){if(o&&c)break;t=n[e],o=o||window[t+"RequestAnimationFrame"],c=c||window[t+"CancelAnimationFrame"]||window[t+"CancelRequestAnimationFrame"]}o&&c||(o=function(t){const e=(new Date).getTime(),a=Math.max(0,16-(e-r)),s=window.setTimeout(()=>{t(e+a)},a);return r=e+a,s},c=function(t){window.clearTimeout(t)})}var u={props:{startVal:{type:Number,required:!1,default:0},endVal:{type:Number,required:!1,default:2017},duration:{type:Number,required:!1,default:3e3},autoplay:{type:Boolean,required:!1,default:!0},decimals:{type:Number,required:!1,default:0,validator(t){return t>=0}},decimal:{type:String,required:!1,default:"."},separator:{type:String,required:!1,default:","},prefix:{type:String,required:!1,default:""},suffix:{type:String,required:!1,default:""},useEasing:{type:Boolean,required:!1,default:!0},easingFn:{type:Function,default(t,e,a,s){return a*(1-Math.pow(2,-10*t/s))*1024/1023+e}}},data(){return{localStartVal:this.startVal,displayValue:this.formatNumber(this.startVal),printVal:null,paused:!1,localDuration:this.duration,startTime:null,timestamp:null,remaining:null,rAF:null}},computed:{countDown(){return this.startVal>this.endVal}},watch:{startVal(){this.autoplay&&this.start()},endVal(){this.autoplay&&this.start()}},mounted(){this.autoplay&&this.start(),this.$emit("mountedCallback")},methods:{start(){this.localStartVal=this.startVal,this.startTime=null,this.localDuration=this.duration,this.paused=!1,this.rAF=o(this.count)},pauseResume(){this.paused?(this.resume(),this.paused=!1):(this.pause(),this.paused=!0)},pause(){c(this.rAF)},resume(){this.startTime=null,this.localDuration=+this.remaining,this.localStartVal=+this.printVal,o(this.count)},reset(){this.startTime=null,c(this.rAF),this.displayValue=this.formatNumber(this.startVal)},count(t){this.startTime||(this.startTime=t),this.timestamp=t;const e=t-this.startTime;this.remaining=this.localDuration-e,this.useEasing?this.countDown?this.printVal=this.localStartVal-this.easingFn(e,0,this.localStartVal-this.endVal,this.localDuration):this.printVal=this.easingFn(e,this.localStartVal,this.endVal-this.localStartVal,this.localDuration):this.countDown?this.printVal=this.localStartVal-(this.localStartVal-this.endVal)*(e/this.localDuration):this.printVal=this.localStartVal+(this.endVal-this.localStartVal)*(e/this.localDuration),this.countDown?this.printVal=this.printVal<this.endVal?this.endVal:this.printVal:this.printVal=this.printVal>this.endVal?this.endVal:this.printVal,this.displayValue=this.formatNumber(this.printVal),e<this.localDuration?this.rAF=o(this.count):this.$emit("callback")},isNumber(t){return!isNaN(parseFloat(t))},formatNumber(t){t=t.toFixed(this.decimals),t+="";const e=t.split(".");let a=e[0];const s=e.length>1?this.decimal+e[1]:"",l=/(\d+)(\d{3})/;if(this.separator&&!this.isNumber(this.separator))while(l.test(a))a=a.replace(l,"$1"+this.separator+"$2");return this.prefix+a+s+this.suffix}},destroyed(){c(this.rAF)}},h=a(71241);const p=(0,h.A)(u,[["render",i]]);var m=p,g=m;"undefined"!==typeof window&&window.Vue&&window.Vue.component("count-to",m)}}]);
//# sourceMappingURL=83.7bd80238.js.map