"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[838],{71838:function(e,t,a){a.r(t),a.d(t,{default:function(){return Q}});var s=a(56768),r=a(24232);const l={class:"performance-result"},n={class:"statistics-cards"},i={class:"stat-content"},o={class:"stat-icon success"},c={class:"stat-info"},u={class:"stat-value"},d={class:"stat-content"},g={class:"stat-icon running"},h={class:"stat-info"},m={class:"stat-value"},p={class:"stat-content"},f={class:"stat-icon failed"},v={class:"stat-info"},b={class:"stat-value"},k={class:"stat-content"},w={class:"stat-icon total"},_={class:"stat-info"},F={class:"stat-value"},y={class:"action-toolbar"},C={class:"left-actions"},D={class:"right-actions"},T={class:"report-name-cell"},S={class:"running-tag"},R={class:"metric-cell"},L={class:"metric-value"},E={key:0,class:"mini-bar-chart"},I={class:"metric-cell"},x={class:"metric-value"},$={class:"metric-cell"},N={class:"metric-value"},M={class:"time-info"},W={class:"time-item"},V={class:"time-item"},P={class:"executor-name"},z={class:"action-buttons"},q={class:"pagination-container"};function A(e,t,a,A,H,j){const U=(0,s.g2)("CircleCheckFilled"),X=(0,s.g2)("el-icon"),Y=(0,s.g2)("el-card"),O=(0,s.g2)("el-col"),B=(0,s.g2)("Loading"),Q=(0,s.g2)("CircleCloseFilled"),K=(0,s.g2)("DataAnalysis"),J=(0,s.g2)("el-row"),Z=(0,s.g2)("el-date-picker"),G=(0,s.g2)("el-form-item"),ee=(0,s.g2)("Search"),te=(0,s.g2)("el-input"),ae=(0,s.g2)("el-option"),se=(0,s.g2)("el-select"),re=(0,s.g2)("el-button"),le=(0,s.g2)("Refresh"),ne=(0,s.g2)("el-form"),ie=(0,s.g2)("RefreshRight"),oe=(0,s.g2)("Setting"),ce=(0,s.g2)("el-table-column"),ue=(0,s.g2)("el-tag"),de=(0,s.g2)("el-progress"),ge=(0,s.g2)("Calendar"),he=(0,s.g2)("el-tooltip"),me=(0,s.g2)("el-avatar"),pe=(0,s.g2)("View"),fe=(0,s.g2)("Download"),ve=(0,s.g2)("ArrowDown"),be=(0,s.g2)("Delete"),ke=(0,s.g2)("el-dropdown-item"),we=(0,s.g2)("el-dropdown-menu"),_e=(0,s.g2)("el-dropdown"),Fe=(0,s.g2)("el-table"),ye=(0,s.g2)("el-pagination"),Ce=(0,s.gN)("loading");return(0,s.uX)(),(0,s.CE)("div",l,[(0,s.Lk)("div",n,[(0,s.bF)(J,{gutter:20},{default:(0,s.k6)(()=>[(0,s.bF)(O,{span:6},{default:(0,s.k6)(()=>[(0,s.bF)(Y,{shadow:"hover",class:"stat-card"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",i,[(0,s.Lk)("div",o,[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(U)]),_:1})]),(0,s.Lk)("div",c,[(0,s.Lk)("div",u,(0,r.v_)(H.statistics.completed),1),t[3]||(t[3]=(0,s.Lk)("div",{class:"stat-label"},"已完成",-1))])])]),_:1})]),_:1}),(0,s.bF)(O,{span:6},{default:(0,s.k6)(()=>[(0,s.bF)(Y,{shadow:"hover",class:"stat-card"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",d,[(0,s.Lk)("div",g,[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(B)]),_:1})]),(0,s.Lk)("div",h,[(0,s.Lk)("div",m,(0,r.v_)(H.statistics.running),1),t[4]||(t[4]=(0,s.Lk)("div",{class:"stat-label"},"执行中",-1))])])]),_:1})]),_:1}),(0,s.bF)(O,{span:6},{default:(0,s.k6)(()=>[(0,s.bF)(Y,{shadow:"hover",class:"stat-card"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",p,[(0,s.Lk)("div",f,[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(Q)]),_:1})]),(0,s.Lk)("div",v,[(0,s.Lk)("div",b,(0,r.v_)(H.statistics.failed),1),t[5]||(t[5]=(0,s.Lk)("div",{class:"stat-label"},"失败",-1))])])]),_:1})]),_:1}),(0,s.bF)(O,{span:6},{default:(0,s.k6)(()=>[(0,s.bF)(Y,{shadow:"hover",class:"stat-card"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",k,[(0,s.Lk)("div",w,[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(K)]),_:1})]),(0,s.Lk)("div",_,[(0,s.Lk)("div",F,(0,r.v_)(H.statistics.total),1),t[6]||(t[6]=(0,s.Lk)("div",{class:"stat-label"},"总报告",-1))])])]),_:1})]),_:1})]),_:1})]),(0,s.bF)(Y,{class:"search-card"},{default:(0,s.k6)(()=>[(0,s.bF)(ne,{inline:!0,class:"search-form"},{default:(0,s.k6)(()=>[(0,s.bF)(G,{label:"时间范围"},{default:(0,s.k6)(()=>[(0,s.bF)(Z,{modelValue:H.search.dataTime,"onUpdate:modelValue":t[0]||(t[0]=e=>H.search.dataTime=e),type:"datetimerange","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss","default-time":H.defaultTimeOptions,shortcuts:H.shortcuts,"range-separator":"至",clearable:!1,class:"date-picker"},null,8,["modelValue","default-time","shortcuts"])]),_:1}),(0,s.bF)(G,{label:"任务名称"},{default:(0,s.k6)(()=>[(0,s.bF)(te,{modelValue:H.search.taskName,"onUpdate:modelValue":t[1]||(t[1]=e=>H.search.taskName=e),placeholder:"请输入任务名称",clearable:""},{prefix:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(ee)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,s.bF)(G,{label:"报告状态"},{default:(0,s.k6)(()=>[(0,s.bF)(se,{style:{width:"120px"},modelValue:H.search.status,"onUpdate:modelValue":t[2]||(t[2]=e=>H.search.status=e),placeholder:"请选择状态",clearable:""},{default:(0,s.k6)(()=>[(0,s.bF)(ae,{label:"全部",value:""}),(0,s.bF)(ae,{label:"已完成",value:"0"}),(0,s.bF)(ae,{label:"执行中",value:"1"}),(0,s.bF)(ae,{label:"运行失败",value:"99"})]),_:1},8,["modelValue"])]),_:1}),(0,s.bF)(G,null,{default:(0,s.k6)(()=>[(0,s.bF)(re,{type:"primary",onClick:j.clickSearch,plain:""},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(ee)]),_:1}),t[7]||(t[7]=(0,s.eW)("查询 "))]),_:1,__:[7]},8,["onClick"]),(0,s.bF)(re,{onClick:j.clearSearch,plain:""},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(le)]),_:1}),t[8]||(t[8]=(0,s.eW)("重置 "))]),_:1,__:[8]},8,["onClick"])]),_:1})]),_:1})]),_:1}),(0,s.Lk)("div",y,[(0,s.Lk)("div",C,[(0,s.bF)(re,{type:"primary",onClick:j.taskDiff,plain:""},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(K)]),_:1}),t[9]||(t[9]=(0,s.eW)("任务对比 "))]),_:1,__:[9]},8,["onClick"])]),(0,s.Lk)("div",D,[(0,s.bF)(re,{circle:"",onClick:j.refreshData},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(ie)]),_:1})]),_:1},8,["onClick"]),(0,s.bF)(re,{circle:""},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(oe)]),_:1})]),_:1})])]),(0,s.bF)(Y,{class:"table-card",shadow:"never"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(Fe,{ref:"table","highlight-current-row":"",data:H.reportList,style:{width:"100%"},size:"default",border:!1,"empty-text":"暂无数据",onSelectionChange:j.handleSelectionChange,"row-class-name":j.tableRowClassName,"table-layout":"auto",height:"500"},{default:(0,s.k6)(()=>[(0,s.bF)(ce,{label:"报告名称",prop:"reportName","min-width":"200","show-overflow-tooltip":""},{default:(0,s.k6)(e=>[(0,s.Lk)("div",T,["报告-已完成"===e.row.status?((0,s.uX)(),(0,s.Wv)(X,{key:0,class:"status-icon success"},{default:(0,s.k6)(()=>[(0,s.bF)(U)]),_:1})):"报告-运行失败"===e.row.status?((0,s.uX)(),(0,s.Wv)(X,{key:1,class:"status-icon failed"},{default:(0,s.k6)(()=>[(0,s.bF)(Q)]),_:1})):"报告-执行中"===e.row.status?((0,s.uX)(),(0,s.Wv)(X,{key:2,class:"status-icon running"},{default:(0,s.k6)(()=>[(0,s.bF)(B)]),_:1})):(0,s.Q3)("",!0),(0,s.Lk)("span",null,(0,r.v_)(e.row.reportName),1)])]),_:1}),(0,s.bF)(ce,{label:"状态",prop:"reportStatus",width:"120",align:"center"},{default:(0,s.k6)(e=>["0"===e.row.reportStatus?((0,s.uX)(),(0,s.Wv)(ue,{key:0,type:"success",effect:"light",size:"small"},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(U)]),_:1}),t[10]||(t[10]=(0,s.eW)(" 已完成 "))]),_:1,__:[10]})):"99"===e.row.reportStatus?((0,s.uX)(),(0,s.Wv)(ue,{key:1,type:"danger",effect:"light",size:"small"},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(Q)]),_:1}),t[11]||(t[11]=(0,s.eW)(" 运行失败 "))]),_:1,__:[11]})):"1"===e.row.reportStatus?((0,s.uX)(),(0,s.Wv)(ue,{key:2,type:"primary",effect:"light",size:"small"},{default:(0,s.k6)(()=>[(0,s.Lk)("span",S,[(0,s.bF)(X,{class:"is-loading"},{default:(0,s.k6)(()=>[(0,s.bF)(B)]),_:1}),t[12]||(t[12]=(0,s.eW)(" 执行中 "))])]),_:1})):((0,s.uX)(),(0,s.Wv)(ue,{key:3,type:"info",effect:"light",size:"small"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(j.getStatusText(e.row.reportStatus)),1)]),_:2},1024))]),_:1}),(0,s.bF)(ce,{label:"任务名称","min-width":"150","show-overflow-tooltip":""},{default:(0,s.k6)(e=>[(0,s.eW)((0,r.v_)(e.row.task?.taskName||e.row.taskName||"-"),1)]),_:1}),(0,s.bF)(ce,{label:"任务模式",width:"100",align:"center"},{default:(0,s.k6)(e=>[(0,s.eW)((0,r.v_)(j.getTaskTypeText(e.row.task?.taskType||e.row.taskType)),1)]),_:1}),(0,s.bF)(ce,{label:"压测模式",width:"120",align:"center"},{default:(0,s.k6)(e=>[(0,s.eW)((0,r.v_)(e.row.pressureMode||"-"),1)]),_:1}),(0,s.bF)(ce,{label:"测试环境",width:"120",align:"center"},{default:(0,s.k6)(e=>[(0,s.eW)((0,r.v_)(e.row.envName),1)]),_:1}),(0,s.bF)(ce,{label:"总请求数",width:"120",align:"center"},{default:(0,s.k6)(e=>[(0,s.eW)((0,r.v_)(j.formatNumber(e.row.totalRequests)),1)]),_:1}),(0,s.bF)(ce,{label:"成功率",width:"100",align:"center"},{default:(0,s.k6)(e=>[(0,s.Lk)("span",{class:(0,r.C4)(j.getSuccessRateClass(j.getSuccessRate(e.row)))},(0,r.v_)(j.getSuccessRate(e.row))+"% ",3)]),_:1}),(0,s.bF)(ce,{label:"平均响应时间",width:"130",align:"center"},{default:(0,s.k6)(e=>[(0,s.eW)((0,r.v_)(j.formatResponseTime(e.row.avgResponseTime)),1)]),_:1}),(0,s.bF)(ce,{label:"平均RPS",width:"120",align:"center"},{default:(0,s.k6)(e=>[(0,s.Lk)("div",R,[(0,s.Lk)("span",L,(0,r.v_)(e.row.avgTps||"-"),1),e.row.avgTps?((0,s.uX)(),(0,s.CE)("div",E,[(0,s.Lk)("div",{class:"mini-bar",style:(0,r.Tr)({width:`${Math.min(Number(e.row.avgTps)/2,100)}%`})},null,4)])):(0,s.Q3)("",!0)])]),_:1}),(0,s.bF)(ce,{label:"运行完成时CPU",width:"120",align:"center"},{default:(0,s.k6)(e=>[(0,s.Lk)("div",I,[(0,s.Lk)("span",x,(0,r.v_)(j.formatPercentage(e.row.avgCpu)),1),e.row.avgCpu?((0,s.uX)(),(0,s.Wv)(de,{key:0,percentage:Number(e.row.avgCpu),color:j.getCpuColor(e.row.avgCpu),"stroke-width":4,"show-text":!1},null,8,["percentage","color"])):(0,s.Q3)("",!0)])]),_:1}),(0,s.bF)(ce,{label:"运行完成时内存",width:"120",align:"center"},{default:(0,s.k6)(e=>[(0,s.Lk)("div",$,[(0,s.Lk)("span",N,(0,r.v_)(j.formatPercentage(e.row.avgMemory)),1),e.row.avgMemory?((0,s.uX)(),(0,s.Wv)(de,{key:0,percentage:Number(e.row.avgMemory),color:j.getMemoryColor(e.row.avgMemory),"stroke-width":4,"show-text":!1},null,8,["percentage","color"])):(0,s.Q3)("",!0)])]),_:1}),(0,s.bF)(ce,{label:"执行时间","min-width":"300",align:"center"},{default:(0,s.k6)(a=>[(0,s.Lk)("div",M,[(0,s.bF)(he,{content:e.$tools.rTime(a.row.startTime||a.row.start_time),placement:"top"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",W,[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(ge)]),_:1}),(0,s.eW)(" "+(0,r.v_)(j.formatDate(a.row.startTime||a.row.start_time)),1)])]),_:2},1032,["content"]),t[13]||(t[13]=(0,s.Lk)("div",{class:"time-separator"},"至",-1)),(0,s.bF)(he,{content:e.$tools.rTime(a.row.endTime||a.row.end_time),placement:"top"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",V,[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(ge)]),_:1}),(0,s.eW)(" "+(0,r.v_)(j.formatDate(a.row.endTime||a.row.end_time)),1)])]),_:2},1032,["content"])])]),_:1}),(0,s.bF)(ce,{label:"执行人",prop:"executor",width:"100",align:"center"},{default:(0,s.k6)(e=>[(0,s.bF)(me,{size:24,src:j.getAvatarUrl(e.row.executor||e.row.creator)},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(j.getInitials(e.row.executor||e.row.creator)),1)]),_:2},1032,["src"]),(0,s.Lk)("span",P,(0,r.v_)(e.row.executor||e.row.creator||"未知"),1)]),_:1}),(0,s.bF)(ce,{label:"操作",width:"190",fixed:"right"},{default:(0,s.k6)(e=>[(0,s.Lk)("div",z,[(0,s.bF)(re,{type:"success",size:"small",plain:"",onClick:t=>j.clickView(e.row)},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(pe)]),_:1}),t[14]||(t[14]=(0,s.eW)("查看 "))]),_:2,__:[14]},1032,["onClick"]),(0,s.bF)(re,{type:"primary",size:"small",plain:"",onClick:t=>j.exportReport(e.row)},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(fe)]),_:1}),t[15]||(t[15]=(0,s.eW)("导出 "))]),_:2,__:[15]},1032,["onClick"]),(0,s.bF)(_e,{trigger:"click"},{dropdown:(0,s.k6)(()=>[(0,s.bF)(we,null,{default:(0,s.k6)(()=>[(0,s.bF)(ke,{onClick:t=>j.delPresetting(e.row.id),style:{color:"var(--el-color-danger)"}},{default:(0,s.k6)(()=>[(0,s.bF)(X,null,{default:(0,s.k6)(()=>[(0,s.bF)(be)]),_:1}),t[17]||(t[17]=(0,s.eW)("删除 "))]),_:2,__:[17]},1032,["onClick"])]),_:2},1024)]),default:(0,s.k6)(()=>[(0,s.bF)(re,{style:{"margin-left":"10px"},type:"info",size:"small",plain:""},{default:(0,s.k6)(()=>[t[16]||(t[16]=(0,s.eW)(" 更多")),(0,s.bF)(X,{class:"el-icon--right"},{default:(0,s.k6)(()=>[(0,s.bF)(ve)]),_:1})]),_:1,__:[16]})]),_:2},1024)])]),_:1})]),_:1},8,["data","onSelectionChange","row-class-name"])),[[Ce,H.tableLoading]]),(0,s.Lk)("div",q,[(0,s.bF)(ye,{background:"",layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:j.currentPages,onSizeChange:j.handleSizeChange,"page-sizes":[20,50,100,200],"default-page-size":20,total:H.pages.count,"current-page":H.pages.current,"hide-on-single-page":!1},null,8,["onCurrentChange","onSizeChange","total","current-page"])])]),_:1})])}a(44114),a(18111),a(22489),a(61701),a(14603),a(47566),a(98721);var H=a(51219),j=a(60782),U=(a(20116),{data(){return{envInfo:{},testEnvs:[],showEnvDialog:!1,selectedEnvId:null}},computed:{currentEnvId(){return this.$store?.state?.envId||this.selectedEnvId},currentEnvInfo(){return this.currentEnvId&&this.testEnvs.length?this.testEnvs.find(e=>e.id===this.currentEnvId):null},currentEnvName(){return this.currentEnvInfo?.name||"未选择环境"},hasSelectedEnv(){return!!this.currentEnvId},envOptions(){return this.testEnvs.map(e=>({label:e.name,value:e.id,disabled:"disabled"===e.status}))}},methods:{async fetchTestEnvs(e=null){try{const t=e||this.currentProjectId;if(!t)return void console.warn("项目ID未定义，无法获取环境列表");const a=await this.$api.getTestEnvs(t);200===a.status&&(this.testEnvs=a.data||[],!this.currentEnvId&&this.testEnvs.length>0&&this.selectEnv(this.testEnvs[0].id))}catch(t){console.error("获取环境列表失败:",t),this.$message({type:"error",message:"获取环境列表失败",duration:2e3})}},async fetchEnvInfo(e=null,t=null){try{const a=e||this.currentEnvId,s=t||this.currentProjectId;if(!a||!s)return console.warn("环境ID或项目ID未定义"),null;const r=await this.$api.getEnvInfo(a,s);if(200===r.status)return this.envInfo=r.data||{},this.envInfo}catch(a){console.error("获取环境信息失败:",a),this.$message({type:"error",message:"获取环境信息失败",duration:2e3})}return null},selectEnv(e){this.selectedEnvId=e,this.$store&&this.$store.commit&&this.$store.commit("selectEnv",e),this.$emit("env-selected",e),e&&this.fetchEnvInfo(e)},async showEnvDetails(e=null){const t=e||this.currentEnvId;t?(await this.fetchEnvInfo(t),this.showEnvDialog=!0):this.$message({type:"warning",message:"请先选择环境",duration:2e3})},closeEnvDialog(){this.showEnvDialog=!1},editEnvConfig(e=null){const t=e||this.envInfo;t?(this.$store&&this.$store.commit&&this.$store.commit("selectEnvInfo",t),this.$router.push({name:"testenv"})):this.$message({type:"warning",message:"环境信息不存在",duration:2e3})},validateEnvConfig(e=null){const t=e||this.envInfo,a=[];if(!t)return a.push("环境信息不存在"),{isValid:!1,errors:a};t.name||a.push("环境名称未配置"),t.global_variable&&0!==Object.keys(t.global_variable).length||a.push("全局变量未配置");const s=t.global_variable||{},r=s.base_url||s.baseUrl;return r||a.push("基础URL未配置"),{isValid:0===a.length,errors:a}},formatEnvVariables(e={},t="global"){return e&&"object"===typeof e?Object.entries(e).map(([e,a])=>({key:e,value:a,type:t,displayValue:this.formatVariableValue(a)})):[]},formatVariableValue(e){return null===e||void 0===e?"":"object"===typeof e?JSON.stringify(e):"string"===typeof e&&e.length>50?e.substring(0,50)+"...":String(e)},getEnvVariable(e,t=null){return this.envInfo?this.envInfo.debug_global_variable&&void 0!==this.envInfo.debug_global_variable[e]?this.envInfo.debug_global_variable[e]:this.envInfo.global_variable&&void 0!==this.envInfo.global_variable[e]?this.envInfo.global_variable[e]:t:t},isEnvAvailable(e=null){const t=e||this.currentEnvId;if(!t||!this.testEnvs.length)return!1;const a=this.testEnvs.find(e=>e.id===t);return a&&"disabled"!==a.status},getEnvStatusType(e){const t={active:"success",inactive:"warning",disabled:"danger",pending:"info"};return t[e]||"info"}},async created(){this.currentProjectId&&await this.fetchTestEnvs()},watch:{currentProjectId:{handler(e){e&&this.fetchTestEnvs(e)},immediate:!1}}}),X=a(57477),Y={mixins:[U],components:{Refresh:X.Refresh,Search:X.Search,DataAnalysis:X.DataAnalysis,View:X.View,Download:X.Download,Delete:X.Delete,RefreshRight:X.RefreshRight,Setting:X.Setting,FullScreen:X.FullScreen,Plus:X.Plus,Calendar:X.Calendar,CircleCheckFilled:X.CircleCheckFilled,CircleCloseFilled:X.CircleCloseFilled,Loading:X.Loading,DocumentCopy:X.DocumentCopy,EditPen:X.EditPen,ArrowDown:X.ArrowDown,TrendCharts:X.TrendCharts,PieChart:X.PieChart,InfoFilled:X.InfoFilled},data(){return{pages:{current:1,count:0,pageSize:20},tableLoading:!1,reportList:[],selectionConfig:{selectedRowKeys:[],selectionChange:this.handleSelectionChange},search:{taskName:"",status:"",dataTime:[this.getFormattedDate(new Date((new Date).getTime()-5184e5)),this.getFormattedDate(new Date,!0)]},defaultTimeOptions:["0000-01-01 00:00:00","0000-01-01 23:59:59"],shortcuts:[{text:"今天",value:()=>{const e=new Date,t=new Date;return t.setHours(0,0,0),e.setHours(23,59,59),[t,e]}},{text:"近三天",value:()=>{const e=new Date,t=new Date;return t.setDate(e.getDate()-2),t.setHours(0,0,0),e.setHours(23,59,59),[t,e]}},{text:"近七天",value:()=>{const e=new Date,t=new Date;return t.setDate(e.getDate()-6),t.setHours(0,0,0),e.setHours(23,59,59),[t,e]}},{text:"近一个月",value:()=>{const e=new Date,t=new Date;return t.setMonth(e.getMonth()-1),t.setHours(0,0,0),e.setHours(23,59,59),[t,e]}}],statistics:{total:0,completed:0,running:0,failed:0}}},mounted(){this.initTableData()},computed:{...(0,j.aH)({pro:e=>e.pro})},methods:{tableRowClassName({row:e}){return"报告-执行中"===e.status?"running-row":""},handleSizeChange(e){this.pages.pageSize=e,this.getTableData()},refreshData(){this.tableLoading=!0,setTimeout(()=>{this.tableLoading=!1,(0,H.nk)({type:"success",message:"数据已刷新",duration:1500})},800)},formatDate(e){if(!e)return"-";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},getCpuColor(e){const t=Number(e);return t<60?"#67C23A":t<80?"#E6A23C":"#F56C6C"},getMemoryColor(e){const t=Number(e);return t<50?"#67C23A":t<75?"#E6A23C":"#F56C6C"},getAvatarUrl(e){return""},getInitials(e){return e?e.charAt(0).toUpperCase():""},initTableData(){this.loadStatistics(),this.getTableData()},async getTableData(){this.tableLoading=!0;try{const e={project_id:this.pro.id,page:this.pages.current,page_size:this.pages.pageSize};this.search.taskName&&(e.taskName=this.search.taskName),this.search.status&&(e.reportStatus=this.search.status.replace("报告-","")),this.search.dataTime&&2===this.search.dataTime.length&&(e.start_time=this.search.dataTime[0],e.end_time=this.search.dataTime[1]);const t=await this.$api.getTaskReports(e);if(200===t.status){let e=t.data.result||t.data.data||[];e=e.map(e=>({...e,reportStatus:e.reportStatus||e.status,taskName:e.taskName||e.task?.taskName||e.name,taskType:e.taskType||e.task?.taskType,pressureMode:e.pressureMode||e.task?.pressureMode,taskId:e.taskId||e.task?.id||e.task_id,envName:e.envName,executor:e.executor||e.creator||e.user?.username,startTime:e.startTime||e.start_time||e.createTime,endTime:e.endTime||e.end_time||e.updateTime,avgCpu:e.avgCpu?Number(e.avgCpu).toFixed(2):null,avgMemory:e.avgMemory?Number(e.avgMemory).toFixed(2):null,avgResponseTime:e.avgResponseTime?Number(e.avgResponseTime):null,avgTps:e.avgTps?Number(e.avgTps).toFixed(2):null,totalRequests:e.totalRequests?Number(e.totalRequests):null,successRequests:e.successRequests?Number(e.successRequests):null,failedRequests:e.failedRequests?Number(e.failedRequests):null})),this.reportList=e,this.pages.count=t.data.count||t.data.total||0,this.pages.current=t.data.current||t.data.page||1}}catch(e){console.error("获取报告列表失败:",e),(0,H.nk)({type:"error",message:"获取报告列表失败: "+(e.response?.data?.message||e.message||"未知错误"),duration:3e3})}finally{this.tableLoading=!1}},async loadStatistics(){try{const e={project_id:this.pro.id},t=await this.$api.getTaskReport(e);200===t.status&&(this.statistics=t.data)}catch(e){console.error("获取统计数据失败:",e)}},handleSelectionChange(e){this.selectionConfig.selectedRowKeys=e.map(e=>e.id)},currentPages(e){this.pages.current=e,this.getTableData()},convertToTimeZoneFormat(e,t){const s=a(2830),r=s.tz(e,t);return r.format("YYYY-MM-DD HH:mm:ss")},getFormattedDate(e,t=!1){const a=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0");let l,n,i;return t?(l="23",n="59",i="59"):(l="00",n="00",i="00"),`${a}-${s}-${r} ${l}:${n}:${i}`},clearSearch(){this.search={taskName:"",status:"",dataTime:[this.getFormattedDate(new Date((new Date).getTime()-5184e5)),this.getFormattedDate(new Date,!0)]},this.pages.current=1,this.getTableData()},clickSearch(){this.pages.current=1,this.getTableData()},clickView(e){e&&e.id?this.$router.push({name:"PerformanceResult-Detail",params:{id:e.id}}):this.$message.error("报告ID不能为空")},taskDiff(){this.$router.push("/performance/comparison")},formatMetricValue(e,t){if(!t&&0!==t)return"-";switch(e){case"avgResponseTime":return t<1e3?`${t}ms`:`${(t/1e3).toFixed(2)}s`;case"successRate":case"avgCpu":case"avgMemory":return`${Number(t).toFixed(1)}%`;case"totalRequests":return Number(t).toLocaleString();default:return Number(t).toFixed(1)}},getMetricColor(e,t){switch(e){case"avgResponseTime":return t>1e3?"#f56c6c":t>500?"#e6a23c":"#67c23a";case"avgTps":return t>100?"#67c23a":t>50?"#e6a23c":"#f56c6c";case"successRate":return t>95?"#67c23a":t>80?"#e6a23c":"#f56c6c";default:return"#409eff"}},async exportReport(e){try{if(!e||!e.id)return void(0,H.nk)({type:"error",message:"导出失败: 报告ID不能为空",duration:3e3});(0,H.nk)({type:"info",message:"报告导出中，请稍候...",duration:2e3});const t=await this.$api.exportSingleReport(e.id);if(200===t.status){const a=t.headers["content-disposition"];let s="性能测试报告.xlsx";if(a){const e=a.match(/filename="?([^"]*)"?/);e&&e[1]&&(s=e[1])}else s=`${e.reportName||"性能测试报告"}.xlsx`;const r=new Blob([t.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),l=document.createElement("a");l.href=URL.createObjectURL(r),l.download=s,l.click(),(0,H.nk)({type:"success",message:"导出成功",duration:2e3})}}catch(t){(0,H.nk)({type:"error",message:"导出失败: "+(t.message||"未知错误"),duration:3e3})}},async delPresetting(e){try{const t=await this.$api.delTaskReport(e);204===t.status&&((0,H.nk)({type:"success",message:"删除成功"}),this.getTableData(),this.loadStatistics())}catch(t){"cancel"!==t&&(0,H.nk)({type:"error",message:"删除失败: "+(t.message||"未知错误")})}},formatPercentage(e){if(!e&&0!==e)return"-";const t=Number(e);return t.toFixed(2)+"%"},formatNumber(e){return e||0===e?e.toLocaleString():"-"},getSuccessRateClass(e){const t=Number(e);return t>=95?"success-rate-high":t>=80?"success-rate-medium":"success-rate-low"},getSuccessRate(e){if(!e.totalRequests||0===e.totalRequests)return 0;const t=e.totalRequests-(e.failedRequests||0),a=t/e.totalRequests*100;return a.toFixed(1)},formatResponseTime(e){if(!e)return"-";const t=Number(e);return t<1e3?t.toFixed(0)+"ms":(t/1e3).toFixed(2)+"s"},formatDuration(e){if(!e)return"-";const t=Math.floor(e/1e3),a=Math.floor(t/60),s=Math.floor(a/60);return s>0?`${s}h ${a%60}m ${t%60}s`:a>0?`${a}m ${t%60}s`:`${t}s`},getStatusText(e){const t={0:"已完成",1:"执行中",99:"运行失败"};return t[e]||"未知状态"},getTaskTypeText(e){if(!e&&0!==e)return"-";const t={1:"普通任务",2:"定时任务",10:"普通任务",20:"定时任务"};return t[String(e)]||"-"},getRunPatternText(e){if(!e&&0!==e)return"-";const t={1:"并发模式",2:"阶梯模式",10:"并发模式",20:"阶梯模式"};return t[String(e)]||"-"},getStatusTagType(e){const t={0:"success",1:"primary",99:"danger"};return t[e]||"info"},getComparisonTableData(){if(!this.comparisonDialogData.comparisonData?.tasks)return[];const e=this.comparisonDialogData.comparisonData.tasks,t=["avgResponseTime","avgTps","successRate","avgCpu","avgMemory","totalRequests"],a={avgResponseTime:"平均响应时间(ms)",avgTps:"平均TPS",successRate:"成功率(%)",avgCpu:"平均CPU(%)",avgMemory:"平均内存(%)",totalRequests:"总请求数"};return t.map(t=>{const s=e.map(e=>e[t]||0),r=this.getBestMetricIndex(t,s);return{metric:a[t],values:s.map(e=>this.formatMetricValue(t,e)),bestIndex:r,bestValue:this.formatMetricValue(t,s[r])}})},getMetricPercentage(e,t){if(!this.comparisonDialogData.comparisonData?.tasks)return 0;const a=this.comparisonDialogData.comparisonData.tasks,s=a.map(t=>t[e]||0),r=Math.max(...s.filter(e=>e>0));return 0===r?0:Math.min(t/r*100,100)},getBestTask(){if(!this.comparisonDialogData.comparisonData?.tasks)return{};const e=this.comparisonDialogData.comparisonData.tasks;return this.findBestPerformanceTask(e)}},created(){}},O=a(71241);const B=(0,O.A)(Y,[["render",A],["__scopeId","data-v-39eb011f"]]);var Q=B}}]);
//# sourceMappingURL=838.b033fe3e.js.map