"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[619],{32619:function(e,t,a){a.r(t),a.d(t,{default:function(){return ve}});var l=a(56768);const s={class:"performance-alert-container"},r={key:0,class:"no-project-warning"};function i(e,t,a,i,o,n){const d=(0,l.g2)("el-alert"),c=(0,l.g2)("AlertManager");return(0,l.uX)(),(0,l.CE)("div",s,[e.pro&&e.pro.id?((0,l.uX)(),(0,l.Wv)(c,{key:1,"project-id":e.pro.id,onAlertTriggered:n.handleAlertTriggered,onAlertAcknowledged:n.handleAlertAcknowledged,onAlertRuleCreated:n.handle<PERSON>lertRuleCreated},null,8,["project-id","onAlertTriggered","onAlertAcknowledged","onAlertRuleCreated"])):((0,l.uX)(),(0,l.CE)("div",r,[(0,l.bF)(d,{title:"未选择项目",type:"warning",description:"请先选择一个项目，再使用告警功能","show-icon":"",closable:!1})]))])}var o=a(60782),n=a(24232);const d={class:"alert-manager"},c={class:"dashboard-header"},u={class:"header-left"},g={class:"header-title"},h={class:"header-actions"},m={class:"status-cards"},k={class:"status-card total-alerts"},b={class:"card-content"},p={class:"card-icon"},_={class:"card-data"},v={class:"card-value"},f={class:"status-card active-alerts"},y={class:"card-content"},F={class:"card-icon"},w={class:"card-data"},C={class:"card-value"},L={class:"status-card resolved-alerts"},A={class:"card-content"},S={class:"card-icon"},R={class:"card-data"},T={class:"card-value"},D={class:"status-card rules-count"},x={class:"card-content"},V={class:"card-icon"},M={class:"card-data"},W={class:"card-value"},$={class:"main-content"},E={class:"content-tabs"},I={key:0,class:"content-panel"},X={class:"panel-header"},U={class:"rule-name"},H={class:"rule-text"},j={class:"condition-text"},N={class:"notification-types"},P={class:"time-display"},q={class:"action-buttons"},K={key:1,class:"content-panel"},O={class:"panel-header"},z={class:"filter-group"},B={class:"alert-name"},J={class:"trigger-value"},Q={class:"time-display"},G={class:"duration"},Y={key:1,class:"text-muted"},Z={class:"condition-group"},ee={class:"severity-options"},te=["onClick"],ae={class:"notification-options"},le=["onClick"],se={key:0,class:"loading-text"},re={key:1,class:"empty-text"},ie={class:"toggle-switch"},oe={class:"dialog-footer"};function ne(e,t,a,s,r,i){const o=(0,l.g2)("AlarmClock"),ne=(0,l.g2)("el-icon"),de=(0,l.g2)("Bell"),ce=(0,l.g2)("el-button"),ue=(0,l.g2)("Plus"),ge=(0,l.g2)("Warning"),he=(0,l.g2)("CircleCloseFilled"),me=(0,l.g2)("CircleCheckFilled"),ke=(0,l.g2)("Setting"),be=(0,l.g2)("Document"),pe=(0,l.g2)("Histogram"),_e=(0,l.g2)("el-input"),ve=(0,l.g2)("el-table-column"),fe=(0,l.g2)("el-tag"),ye=(0,l.g2)("el-switch"),Fe=(0,l.g2)("Calendar"),we=(0,l.g2)("Edit"),Ce=(0,l.g2)("Delete"),Le=(0,l.g2)("el-table"),Ae=(0,l.g2)("el-option"),Se=(0,l.g2)("el-select"),Re=(0,l.g2)("el-date-picker"),Te=(0,l.g2)("Timer"),De=(0,l.g2)("Clock"),xe=(0,l.g2)("Check"),Ve=(0,l.g2)("el-form-item"),Me=(0,l.g2)("el-input-number"),We=(0,l.g2)("Message"),$e=(0,l.g2)("Connection"),Ee=(0,l.g2)("ChatDotRound"),Ie=(0,l.g2)("ChatRound"),Xe=(0,l.g2)("el-form"),Ue=(0,l.g2)("el-dialog"),He=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",d,[(0,l.Lk)("div",c,[(0,l.Lk)("div",u,[(0,l.Lk)("div",g,[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(o)]),_:1}),t[15]||(t[15]=(0,l.Lk)("h1",null,"告警中心",-1))]),t[16]||(t[16]=(0,l.Lk)("div",{class:"header-subtitle"},"实时监控系统状态，及时处理异常",-1))]),(0,l.Lk)("div",h,[(0,l.bF)(ce,{class:(0,n.C4)(["monitor-toggle-btn",r.alertMonitoringStatus?"is-active":""]),onClick:i.toggleAlertMonitoring},{default:(0,l.k6)(()=>[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(de)]),_:1}),(0,l.eW)(" "+(0,n.v_)(r.alertMonitoringStatus?"监控中":"开启监控"),1)]),_:1},8,["class","onClick"]),(0,l.bF)(ce,{class:"add-rule-btn",onClick:t[0]||(t[0]=e=>r.showCreateDialog=!0)},{default:(0,l.k6)(()=>[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(ue)]),_:1}),t[17]||(t[17]=(0,l.eW)(" 新建规则 "))]),_:1,__:[17]})])]),(0,l.Lk)("div",m,[(0,l.Lk)("div",k,[(0,l.Lk)("div",b,[(0,l.Lk)("div",p,[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(ge)]),_:1})]),(0,l.Lk)("div",_,[(0,l.Lk)("div",v,(0,n.v_)(r.alertStatistics.total_alerts),1),t[18]||(t[18]=(0,l.Lk)("div",{class:"card-label"},"总告警数",-1))])])]),(0,l.Lk)("div",f,[(0,l.Lk)("div",y,[(0,l.Lk)("div",F,[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(he)]),_:1})]),(0,l.Lk)("div",w,[(0,l.Lk)("div",C,(0,n.v_)(r.alertStatistics.active_alerts),1),t[19]||(t[19]=(0,l.Lk)("div",{class:"card-label"},"活跃告警",-1))])])]),(0,l.Lk)("div",L,[(0,l.Lk)("div",A,[(0,l.Lk)("div",S,[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(me)]),_:1})]),(0,l.Lk)("div",R,[(0,l.Lk)("div",T,(0,n.v_)(r.alertStatistics.resolved_alerts),1),t[20]||(t[20]=(0,l.Lk)("div",{class:"card-label"},"已解决",-1))])])]),(0,l.Lk)("div",D,[(0,l.Lk)("div",x,[(0,l.Lk)("div",V,[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(ke)]),_:1})]),(0,l.Lk)("div",M,[(0,l.Lk)("div",W,(0,n.v_)(r.alertRules.length),1),t[21]||(t[21]=(0,l.Lk)("div",{class:"card-label"},"告警规则",-1))])])])]),(0,l.Lk)("div",$,[(0,l.Lk)("div",E,[(0,l.Lk)("div",{class:(0,n.C4)(["tab-item","rules"===r.activeTab?"active":""]),onClick:t[1]||(t[1]=e=>r.activeTab="rules")},[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(be)]),_:1}),t[22]||(t[22]=(0,l.eW)(" 告警规则 "))],2),(0,l.Lk)("div",{class:(0,n.C4)(["tab-item","history"===r.activeTab?"active":""]),onClick:t[2]||(t[2]=e=>r.activeTab="history")},[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(pe)]),_:1}),t[23]||(t[23]=(0,l.eW)(" 告警历史 "))],2)]),"rules"===r.activeTab?((0,l.uX)(),(0,l.CE)("div",I,[(0,l.Lk)("div",X,[t[24]||(t[24]=(0,l.Lk)("h2",null,"规则列表",-1)),(0,l.bF)(_e,{modelValue:r.ruleSearchKeyword,"onUpdate:modelValue":t[3]||(t[3]=e=>r.ruleSearchKeyword=e),placeholder:"搜索规则名称","prefix-icon":"Search",clearable:"",class:"search-input"},null,8,["modelValue"])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(Le,{data:i.filteredRules,"empty-text":"暂无告警规则",class:"data-table","row-class-name":"table-row"},{default:(0,l.k6)(()=>[(0,l.bF)(ve,{label:"规则名称",prop:"name","min-width":"180px"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",U,[(0,l.Lk)("span",H,(0,n.v_)(e.row.name),1)])]),_:1}),(0,l.bF)(ve,{label:"监控指标",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(fe,{size:"small",effect:"dark",class:"metric-tag"},{default:(0,l.k6)(()=>[(0,l.eW)((0,n.v_)(i.getMetricTypeText(e.row.metric_type)),1)]),_:2},1024)]),_:1}),(0,l.bF)(ve,{label:"条件",width:"120"},{default:(0,l.k6)(e=>[(0,l.Lk)("span",j,(0,n.v_)(i.getConditionText(e.row.condition))+" "+(0,n.v_)(e.row.threshold)+(0,n.v_)(i.getUnitText(e.row.metric_type)),1)]),_:1}),(0,l.bF)(ve,{label:"严重程度",width:"90"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",{class:(0,n.C4)(["severity-badge","severity-"+e.row.severity])},(0,n.v_)(i.getSeverityText(e.row.severity)),3)]),_:1}),(0,l.bF)(ve,{label:"通知方式",width:"120"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",N,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.row.notification_types,e=>((0,l.uX)(),(0,l.Wv)(fe,{key:e,size:"small",class:"notification-tag",effect:"plain"},{default:(0,l.k6)(()=>[(0,l.eW)((0,n.v_)(i.getNotificationTypeText(e)),1)]),_:2},1024))),128))])]),_:1}),(0,l.bF)(ve,{label:"状态",width:"80"},{default:(0,l.k6)(e=>[(0,l.bF)(ye,{modelValue:e.row.is_enabled,"onUpdate:modelValue":t=>e.row.is_enabled=t,onChange:t=>i.toggleRuleStatus(e.row),class:"status-switch"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),(0,l.bF)(ve,{label:"创建时间",prop:"create_time",width:"160"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",P,[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(Fe)]),_:1}),(0,l.Lk)("span",null,(0,n.v_)(i.formatTime(e.row.create_time)),1)])]),_:1}),(0,l.bF)(ve,{label:"操作",width:"170",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",q,[(0,l.bF)(ce,{type:"primary",size:"small",onClick:t=>i.editRule(e.row),class:"action-btn edit-btn"},{default:(0,l.k6)(()=>[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(we)]),_:1}),t[25]||(t[25]=(0,l.eW)(" 编辑 "))]),_:2,__:[25]},1032,["onClick"]),(0,l.bF)(ce,{type:"danger",size:"small",onClick:t=>i.deleteRule(e.row),class:"action-btn delete-btn"},{default:(0,l.k6)(()=>[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(Ce)]),_:1}),t[26]||(t[26]=(0,l.eW)(" 删除 "))]),_:2,__:[26]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[He,r.tableLoading]])])):(0,l.Q3)("",!0),"history"===r.activeTab?((0,l.uX)(),(0,l.CE)("div",K,[(0,l.Lk)("div",O,[t[27]||(t[27]=(0,l.Lk)("h2",null,"告警历史",-1)),(0,l.Lk)("div",z,[(0,l.bF)(Se,{modelValue:r.historyStatusFilter,"onUpdate:modelValue":t[4]||(t[4]=e=>r.historyStatusFilter=e),placeholder:"状态筛选",class:"filter-select"},{default:(0,l.k6)(()=>[(0,l.bF)(Ae,{label:"全部",value:""}),(0,l.bF)(Ae,{label:"活跃",value:"active"}),(0,l.bF)(Ae,{label:"已确认",value:"acknowledged"}),(0,l.bF)(Ae,{label:"已解决",value:"resolved"})]),_:1},8,["modelValue"]),(0,l.bF)(Re,{modelValue:r.historyDateRange,"onUpdate:modelValue":t[5]||(t[5]=e=>r.historyDateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"date-range"},null,8,["modelValue"])])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(Le,{data:i.filteredHistory,"empty-text":"暂无告警历史",class:"data-table","row-class-name":"table-row"},{default:(0,l.k6)(()=>[(0,l.bF)(ve,{label:"告警名称",prop:"rule_name","min-width":"180"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",B,(0,n.v_)(e.row.rule_name),1)]),_:1}),(0,l.bF)(ve,{label:"严重程度",width:"90"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",{class:(0,n.C4)(["severity-badge","severity-"+e.row.severity])},(0,n.v_)(i.getSeverityText(e.row.severity)),3)]),_:1}),(0,l.bF)(ve,{label:"触发值",width:"120"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",J,(0,n.v_)(e.row.triggered_value)+(0,n.v_)(i.getUnitText(e.row.metric_type)),1)]),_:1}),(0,l.bF)(ve,{label:"状态",width:"100"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",{class:(0,n.C4)(["status-badge","status-"+e.row.status])},(0,n.v_)(i.getAlertStatusText(e.row.status)),3)]),_:1}),(0,l.bF)(ve,{label:"触发时间",prop:"triggered_at",width:"160"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",Q,[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(Te)]),_:1}),(0,l.Lk)("span",null,(0,n.v_)(i.formatTime(e.row.triggered_at)),1)])]),_:1}),(0,l.bF)(ve,{label:"持续时间",width:"120"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",G,[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(De)]),_:1}),(0,l.Lk)("span",null,(0,n.v_)(i.calculateDuration(e.row.triggered_at,e.row.resolved_at)),1)])]),_:1}),(0,l.bF)(ve,{label:"操作",width:"120"},{default:(0,l.k6)(e=>["active"===e.row.status?((0,l.uX)(),(0,l.Wv)(ce,{key:0,type:"success",size:"small",class:"action-btn confirm-btn",onClick:t=>i.acknowledgeAlert(e.row)},{default:(0,l.k6)(()=>[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(xe)]),_:1}),t[28]||(t[28]=(0,l.eW)(" 确认 "))]),_:2,__:[28]},1032,["onClick"])):((0,l.uX)(),(0,l.CE)("span",Y,"已处理"))]),_:1})]),_:1},8,["data"])),[[He,r.historyLoading]])])):(0,l.Q3)("",!0)]),(0,l.bF)(Ue,{modelValue:r.showCreateDialog,"onUpdate:modelValue":t[14]||(t[14]=e=>r.showCreateDialog=e),title:r.editingRule?"编辑告警规则":"创建告警规则",width:"600px","destroy-on-close":"",class:"custom-dialog",onClose:i.resetForm},{footer:(0,l.k6)(()=>[(0,l.Lk)("div",oe,[(0,l.bF)(ce,{onClick:t[13]||(t[13]=e=>r.showCreateDialog=!1),class:"cancel-btn"},{default:(0,l.k6)(()=>t[30]||(t[30]=[(0,l.eW)("取消")])),_:1,__:[30]}),(0,l.bF)(ce,{type:"primary",onClick:i.submitForm,loading:r.submitLoading,class:"submit-btn"},{default:(0,l.k6)(()=>[(0,l.eW)((0,n.v_)(r.editingRule?"保存更改":"创建规则"),1)]),_:1},8,["onClick","loading"])])]),default:(0,l.k6)(()=>[(0,l.bF)(Xe,{model:r.ruleForm,rules:r.formRules,ref:"ruleFormRef","label-width":"100px",class:"rule-form"},{default:(0,l.k6)(()=>[(0,l.bF)(Ve,{label:"规则名称",prop:"name"},{default:(0,l.k6)(()=>[(0,l.bF)(_e,{modelValue:r.ruleForm.name,"onUpdate:modelValue":t[6]||(t[6]=e=>r.ruleForm.name=e),placeholder:"请输入规则名称"},null,8,["modelValue"])]),_:1}),(0,l.bF)(Ve,{label:"监控指标",prop:"metric_type"},{default:(0,l.k6)(()=>[(0,l.bF)(Se,{modelValue:r.ruleForm.metric_type,"onUpdate:modelValue":t[7]||(t[7]=e=>r.ruleForm.metric_type=e),placeholder:"请选择监控指标",class:"full-width"},{default:(0,l.k6)(()=>[(0,l.bF)(Ae,{label:"平均响应时间",value:"avg_response_time"}),(0,l.bF)(Ae,{label:"错误率",value:"error_rate"}),(0,l.bF)(Ae,{label:"TPS",value:"tps"}),(0,l.bF)(Ae,{label:"CPU使用率",value:"cpu_usage"}),(0,l.bF)(Ae,{label:"内存使用率",value:"memory_usage"}),(0,l.bF)(Ae,{label:"并发用户数",value:"concurrent_users"})]),_:1},8,["modelValue"])]),_:1}),(0,l.bF)(Ve,{label:"告警条件",prop:"condition"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",Z,[(0,l.bF)(Se,{modelValue:r.ruleForm.condition,"onUpdate:modelValue":t[8]||(t[8]=e=>r.ruleForm.condition=e),placeholder:"条件",class:"condition-select"},{default:(0,l.k6)(()=>[(0,l.bF)(Ae,{label:"大于",value:"gt"}),(0,l.bF)(Ae,{label:"大于等于",value:"gte"}),(0,l.bF)(Ae,{label:"小于",value:"lt"}),(0,l.bF)(Ae,{label:"小于等于",value:"lte"}),(0,l.bF)(Ae,{label:"等于",value:"eq"})]),_:1},8,["modelValue"]),(0,l.bF)(Me,{modelValue:r.ruleForm.threshold,"onUpdate:modelValue":t[9]||(t[9]=e=>r.ruleForm.threshold=e),min:0,precision:2,"controls-position":"right",class:"threshold-input",placeholder:"阈值"},null,8,["modelValue"])])]),_:1}),(0,l.bF)(Ve,{label:"严重程度",prop:"severity"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",ee,[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)([{value:"low",text:"低",color:"#67C23A",bg:"#E1F3D8"},{value:"medium",text:"中",color:"#3b99e5",bg:"#ECF5FF"},{value:"high",text:"高",color:"#E6A23C",bg:"#FDF6EC"},{value:"critical",text:"紧急",color:"#F56C6C",bg:"#FEF0F0"}],e=>(0,l.Lk)("div",{key:e.value,class:(0,n.C4)(["severity-option-new",{selected:r.ruleForm.severity===e.value}]),style:(0,n.Tr)({"border-color":r.ruleForm.severity===e.value?e.color:"#DCDFE6",color:r.ruleForm.severity===e.value?e.color:"#606266","background-color":r.ruleForm.severity===e.value?e.bg:"transparent"}),onClick:t=>r.ruleForm.severity=e.value},(0,n.v_)(e.text),15,te)),64))])]),_:1}),(0,l.bF)(Ve,{label:"通知方式",prop:"notification_types"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",ae,[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)(["email","webhook","dingtalk","wechat"],e=>(0,l.Lk)("div",{key:e,class:(0,n.C4)(["notification-option",{selected:r.ruleForm.notification_types.includes(e)}]),onClick:t=>i.toggleNotificationType(e)},["email"===e?((0,l.uX)(),(0,l.Wv)(ne,{key:0},{default:(0,l.k6)(()=>[(0,l.bF)(We)]),_:1})):"webhook"===e?((0,l.uX)(),(0,l.Wv)(ne,{key:1},{default:(0,l.k6)(()=>[(0,l.bF)($e)]),_:1})):"dingtalk"===e?((0,l.uX)(),(0,l.Wv)(ne,{key:2},{default:(0,l.k6)(()=>[(0,l.bF)(Ee)]),_:1})):"wechat"===e?((0,l.uX)(),(0,l.Wv)(ne,{key:3},{default:(0,l.k6)(()=>[(0,l.bF)(Ie)]),_:1})):(0,l.Q3)("",!0),(0,l.eW)(" "+(0,n.v_)(i.getNotificationTypeText(e)),1)],10,le)),64))])]),_:1}),(0,l.bF)(Ve,{label:"关联任务",prop:"task_ids"},{default:(0,l.k6)(()=>[(0,l.bF)(Se,{modelValue:r.ruleForm.task_ids,"onUpdate:modelValue":t[10]||(t[10]=e=>r.ruleForm.task_ids=e),multiple:"",placeholder:"不选择则默认关联所有任务",class:"full-width",onChange:i.handleTaskSelectionChange,loading:r.tasksLoading},{empty:(0,l.k6)(()=>[r.tasksLoading?((0,l.uX)(),(0,l.CE)("div",se," 加载任务中... ")):0===r.performanceTasks.length?((0,l.uX)(),(0,l.CE)("div",re," 暂无任务数据 ")):(0,l.Q3)("",!0)]),default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(r.performanceTasks,e=>((0,l.uX)(),(0,l.Wv)(Ae,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange","loading"]),t[29]||(t[29]=(0,l.Lk)("div",{class:"form-help-text"}," 不选择任务则默认关联所有任务，编辑模式下可保持原有关联关系 ",-1))]),_:1,__:[29]}),(0,l.bF)(Ve,{label:"描述"},{default:(0,l.k6)(()=>[(0,l.bF)(_e,{modelValue:r.ruleForm.description,"onUpdate:modelValue":t[11]||(t[11]=e=>r.ruleForm.description=e),type:"textarea",rows:3,placeholder:"请输入规则描述"},null,8,["modelValue"])]),_:1}),(0,l.bF)(Ve,{label:"启用规则"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",ie,[(0,l.Lk)("span",{class:(0,n.C4)(["toggle-label",{active:!r.ruleForm.is_enabled}])},"禁用",2),(0,l.bF)(ye,{modelValue:r.ruleForm.is_enabled,"onUpdate:modelValue":t[12]||(t[12]=e=>r.ruleForm.is_enabled=e),class:"big-switch"},null,8,["modelValue"]),(0,l.Lk)("span",{class:(0,n.C4)(["toggle-label",{active:r.ruleForm.is_enabled}])},"启用",2)])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title","onClose"])])}a(44114),a(18111),a(22489),a(7588),a(61701),a(13579);var de=a(51219),ce=a(12933),ue=a(57477),ge={name:"AlertManager",components:{Plus:ue.Plus,Bell:ue.Bell,Warning:ue.Warning,CircleCloseFilled:ue.CircleCloseFilled,CircleCheckFilled:ue.CircleCheckFilled,Setting:ue.Setting,Edit:ue.Edit,Delete:ue.Delete,Check:ue.Check,Calendar:ue.Calendar,Timer:ue.Timer,Clock:ue.Clock,Message:ue.Message,Connection:ue.Connection,ChatDotRound:ue.ChatDotRound,ChatRound:ue.ChatRound,AlarmClock:ue.AlarmClock,Document:ue.Document,Histogram:ue.Histogram,Search:ue.Search},props:{projectId:{type:[String,Number],required:!0}},emits:["alert-triggered","alert-acknowledged","alert-rule-created"],data(){return{activeTab:"rules",alertRules:[],alertHistory:[],tableLoading:!1,historyLoading:!1,submitLoading:!1,showCreateDialog:!1,editingRule:null,alertMonitoringStatus:!1,ruleSearchKeyword:"",historyStatusFilter:"",historyDateRange:null,refreshTimer:null,performanceTasks:[],tasksLoading:!1,alertStatistics:{total_alerts:0,active_alerts:0,resolved_alerts:0},ruleForm:{name:"",metric_type:"",condition:"",threshold:0,severity:"",notification_types:[],description:"",is_enabled:!0,task_ids:[]},formRules:{name:[{required:!0,message:"请输入规则名称",trigger:"blur"}],metric_type:[{required:!0,message:"请选择监控指标",trigger:"change"}],condition:[{required:!0,message:"请选择告警条件",trigger:"change"}],threshold:[{required:!0,message:"请输入阈值",trigger:"blur"},{type:"number",message:"阈值必须是数字",trigger:"blur"}],severity:[{required:!0,message:"请选择严重程度",trigger:"change"}],notification_types:[{required:!0,message:"请选择通知方式",trigger:"change"},{type:"array",min:1,message:"请至少选择一种通知方式",trigger:"change"}],task_ids:[{type:"array",message:"请选择关联任务",trigger:"change"}]}}},mounted(){this.loadAlertRules(),this.loadAlertHistory(),this.loadAlertStatus(),this.loadPerformanceTasks(),this.refreshTimer=setInterval(()=>{this.alertMonitoringStatus&&(this.loadAlertHistory(),this.loadAlertStatus())},3e4)},beforeUnmount(){this.refreshTimer&&clearInterval(this.refreshTimer)},computed:{filteredRules(){if(!this.ruleSearchKeyword)return this.alertRules;const e=this.ruleSearchKeyword.toLowerCase();return this.alertRules.filter(t=>t.name.toLowerCase().includes(e))},filteredHistory(){let e=this.alertHistory;if(this.historyStatusFilter&&(e=e.filter(e=>e.status===this.historyStatusFilter)),this.historyDateRange&&2===this.historyDateRange.length){const t=new Date(this.historyDateRange[0]),a=new Date(this.historyDateRange[1]);a.setHours(23,59,59,999),e=e.filter(e=>{const l=new Date(e.triggered_at);return l>=t&&l<=a})}return e}},methods:{toggleNotificationType(e){const t=this.ruleForm.notification_types.indexOf(e);t>-1?this.ruleForm.notification_types.splice(t,1):this.ruleForm.notification_types.push(e)},handleTaskSelectionChange(e){this.ruleForm.task_ids=e,console.log("已选择任务:",e)},async loadAlertRules(){this.tableLoading=!0;try{console.log("正在加载告警规则，项目ID:",this.projectId);const e=await this.$api.getAlertRules({project_id:this.projectId});200===e.status&&(this.alertRules=e.data||[],console.log("成功加载告警规则:",this.alertRules.length,"条记录"))}catch(e){console.error("加载告警规则失败:",e),404===e.response?.status?de.nk.warning("告警功能暂未配置，请联系管理员"):de.nk.error("加载告警规则失败: "+(e.response?.data?.message||e.message||"网络错误")),this.alertRules=[]}finally{this.tableLoading=!1}},async loadAlertHistory(){this.historyLoading=!0;try{console.log("正在加载告警历史，项目ID:",this.projectId);const e=await this.$api.getAlertHistory({project_id:this.projectId});if(200===e.status){const t=e.data||[];console.log("成功加载告警历史:",t.length,"条记录"),this._checkForNewAlerts(this.alertHistory,t),this.alertHistory=t}}catch(e){console.error("加载告警历史失败:",e),404===e.response?.status?de.nk.warning("告警历史暂未配置，请联系管理员"):de.nk.error("加载告警历史失败: "+(e.response?.data?.message||e.message||"网络错误")),this.alertHistory=[]}finally{this.historyLoading=!1}},async loadAlertStatus(){try{const e=await this.$api.getAlertStatus({project_id:this.projectId});if(200===e.status){const t=e.data.statistics||{};this.alertStatistics={total_alerts:t.total||0,active_alerts:t.active||0,resolved_alerts:t.resolved||0},this.alertMonitoringStatus=e.data.monitoring_status||!1,console.log("成功加载告警状态:",this.alertStatistics,"监控状态:",this.alertMonitoringStatus)}}catch(e){console.error("加载告警状态失败:",e),this.alertStatistics={total_alerts:0,active_alerts:0,resolved_alerts:0},this.alertMonitoringStatus=!1}},async loadPerformanceTasks(){this.tasksLoading=!0;try{const e=await this.$api.getPerformanceTasks();if(200===e.status){let t=e.data;e.data&&e.data.results&&(t=e.data.results),this.performanceTasks=t.map(e=>({id:e.id,name:e.taskName||`任务 #${e.id}`})),console.log("成功加载性能任务:",this.performanceTasks.length,"条记录")}}catch(e){console.error("加载性能任务失败:",e),de.nk.error("加载性能任务失败: "+(e.response?.data?.message||e.message||"网络错误")),this.performanceTasks=[]}finally{this.tasksLoading=!1}},async toggleAlertMonitoring(){try{let e;this.alertMonitoringStatus?(console.log("正在停止告警监控..."),e=await this.$api.stopAlertMonitoring()):(console.log("正在启动告警监控..."),e=await this.$api.startAlertMonitoring()),200===e.status&&(this.alertMonitoringStatus=!this.alertMonitoringStatus,de.nk.success(this.alertMonitoringStatus?"告警监控已启动":"告警监控已停止"),this.loadAlertStatus())}catch(e){console.error("切换监控状态失败:",e),de.nk.error("切换监控状态失败: "+(e.response?.data?.message||e.message||"网络错误"))}},async toggleRuleStatus(e){try{console.log("切换规则状态:",e.id,e.is_enabled),de.nk.success(e.is_enabled?"规则已启用":"规则已禁用");e.is_enabled;try{const t=await this.$api.updateAlertRule(e.id,{is_enabled:e.is_enabled});if(200!==t.status&&201!==t.status)throw new Error("状态更新失败")}catch(t){throw e.is_enabled=!e.is_enabled,t}}catch(t){console.error("更新规则状态失败:",t),de.nk.error("更新规则状态失败: "+(t.response?.data?.message||t.message||"网络错误"))}},editRule(e){if(this.editingRule=e,this.ruleForm=JSON.parse(JSON.stringify(e)),"string"===typeof this.ruleForm.task_ids)try{this.ruleForm.task_ids=JSON.parse(this.ruleForm.task_ids)}catch(t){console.error("解析任务ID失败:",t),this.ruleForm.task_ids=[]}this.ruleForm.task_ids||(this.ruleForm.task_ids=[]),this.showCreateDialog=!0},async deleteRule(e){try{await ce.s.confirm(`确定要删除告警规则 "${e.name}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),console.log("正在删除规则:",e.id);const t=await this.$api.deleteAlertRule(e.id);204!==t.status&&200!==t.status||(de.nk.success("删除成功"),this.alertRules=this.alertRules.filter(t=>t.id!==e.id),this.alertStatistics.total_alerts>0&&(this.alertStatistics.total_alerts-=1))}catch(t){"cancel"!==t&&(console.error("删除规则失败:",t),de.nk.error("删除失败: "+(t.response?.data?.message||t.message||"网络错误")))}},async acknowledgeAlert(e){try{console.log("正在确认告警:",e.id);const t=await this.$api.acknowledgeAlert({alert_id:e.id,acknowledger:"current_user"});200===t.status&&(de.nk.success("告警已确认"),this.loadAlertHistory(),this.loadAlertStatus(),this.$emit("alert-acknowledged",e))}catch(t){console.error("确认告警失败:",t),de.nk.error("确认告警失败: "+(t.response?.data?.message||t.message||"网络错误"))}},async submitForm(){try{const e=await this.$refs.ruleFormRef.validate();if(!e)return;this.submitLoading=!0;const t={...this.ruleForm,project_id:this.projectId,notification_config:JSON.stringify({email:{recipients:[]},webhook:{url:""}})};let a;if(t.notification_types&&Array.isArray(t.notification_types)||(t.notification_types=[]),0===t.notification_types.length&&t.notification_types.push("email"),t.task_ids&&Array.isArray(t.task_ids)||(t.task_ids=[]),0===t.task_ids.length?(t.link_all_tasks=!0,console.log("未选择任务，将关联所有任务")):t.link_all_tasks=!1,console.log("提交告警规则数据:",t),a=this.editingRule?await this.$api.updateAlertRule(this.editingRule.id,t):await this.$api.addAlertRule(t),200===a.status||201===a.status){if(de.nk.success(this.editingRule?"更新成功":"创建成功"),this.showCreateDialog=!1,this.editingRule){const e=this.alertRules.findIndex(e=>e.id===this.editingRule.id);if(-1!==e){const l=a.data?.rule||{...t,id:this.editingRule.id,create_time:this.editingRule.create_time};this.alertRules[e]=l}}else{const e=a.data?.rule||{...t,id:Date.now(),create_time:(new Date).toISOString()};this.alertRules.unshift(e),this.alertStatistics.total_alerts+=1,this.$emit("alert-rule-created",e)}this.loadAlertRules()}}catch(e){console.error("提交失败:",e),de.nk.error("提交失败: "+(e.response?.data?.message||e.message||"网络错误"))}finally{this.submitLoading=!1}},resetForm(){this.editingRule=null,this.ruleForm={name:"",metric_type:"",condition:"",threshold:0,severity:"",notification_types:[],description:"",is_enabled:!0,task_ids:[]},this.$refs.ruleFormRef?.resetFields()},formatTime(e){return e?new Date(e).toLocaleString():"-"},calculateDuration(e,t){if(!e)return"-";const a=new Date(e),l=t?new Date(t):new Date,s=Math.floor((l-a)/1e3);return s<60?`${s}秒`:s<3600?`${Math.floor(s/60)}分钟`:`${Math.floor(s/3600)}小时`},getMetricTypeText(e){const t={avg_response_time:"响应时间",error_rate:"错误率",tps:"TPS",cpu_usage:"CPU",memory_usage:"内存",concurrent_users:"并发数"};return t[e]||e},getConditionText(e){const t={gt:">",gte:"≥",lt:"<",lte:"≤",eq:"="};return t[e]||e},getUnitText(e){const t={avg_response_time:"ms",error_rate:"%",tps:"",cpu_usage:"%",memory_usage:"%",concurrent_users:""};return t[e]||""},getSeverityType(e){const t={low:"info",medium:"warning",high:"danger",critical:"danger"};return t[e]||"info"},getSeverityText(e){const t={low:"低",medium:"中",high:"高",critical:"紧急"};return t[e]||e},getNotificationTypeText(e){const t={email:"邮件",webhook:"Webhook",dingtalk:"钉钉",wechat:"微信"};return t[e]||e},getAlertStatusType(e){const t={active:"danger",acknowledged:"warning",resolved:"success"};return t[e]||"info"},getAlertStatusText(e){const t={active:"活跃",acknowledged:"已确认",resolved:"已解决"};return t[e]||e},_checkForNewAlerts(e,t){if(!e||0===e.length)return;const a=t.filter(t=>"active"===t.status&&!e.some(e=>e.id===t.id));a.forEach(e=>{console.log("检测到新的告警:",e),this.$emit("alert-triggered",e)})}}},he=a(71241);const me=(0,he.A)(ge,[["render",ne],["__scopeId","data-v-5ef97f23"]]);var ke=me,be=a(93851),pe={name:"PerformanceAlert",components:{AlertManager:ke},computed:{...(0,o.aH)({pro:e=>e.pro})},created(){console.log("PerformanceAlert 组件已创建，项目ID:",this.pro?.id||"未选择项目")},mounted(){console.log("PerformanceAlert 组件已加载，项目ID:",this.pro?.id||"未选择项目"),this.pro&&this.pro.id||de.nk.warning("请先选择一个项目，再使用告警功能")},methods:{handleAlertTriggered(e){(0,be.df)({title:"告警触发",message:`告警 "${e.rule_name}" 已触发，请注意查看`,type:"warning",duration:8e3,position:"top-right"}),console.log("告警已触发:",e)},handleAlertAcknowledged(e){(0,de.nk)({type:"success",message:`告警 "${e.rule_name}" 已确认`,duration:3e3}),console.log("告警已确认:",e)},handleAlertRuleCreated(e){(0,de.nk)({type:"success",message:`新告警规则 "${e.name}" 已创建`,duration:3e3}),console.log("新告警规则已创建:",e)}}};const _e=(0,he.A)(pe,[["render",i],["__scopeId","data-v-eae3dc1a"]]);var ve=_e}}]);
//# sourceMappingURL=619.33852ff0.js.map