"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[491],{93491:function(e,t,l){l.d(t,{A:function(){return R}});var i=l(56768),a=l(24232),n=l(45130);const o={class:"search-area"},s={class:"action-buttons"},c={class:"button-group"},r={class:"env-info"},d={style:{"font-size":"14px",color:"#909399","margin-right":"10px","margin-bottom":"10px",display:"inline-block"}},u={slot:"footer",class:"dialog-footer"},h={class:"interface-title"},f={class:"interface-list"},k={class:"interface-checkbox"},g=["onClick"],m={class:"method-section"},p={class:"info-section"},C=["title"],_=["title"],b={style:{"margin-top":"10px"}},y={key:0},v={style:{color:"#409eff","margin-right":"8px"}},w={slot:"footer",class:"dialog-footer"};function F(e,t,l,F,x,T){const D=(0,i.g2)("treeNode"),E=(0,i.g2)("el-col"),I=(0,i.g2)("el-input"),V=(0,i.g2)("el-button"),W=(0,i.g2)("View"),L=(0,i.g2)("el-icon"),S=(0,i.g2)("Star"),R=(0,i.g2)("Close"),K=(0,i.g2)("Check"),X=(0,i.g2)("el-option"),A=(0,i.g2)("el-select"),$=(0,i.g2)("el-form-item"),N=(0,i.g2)("el-form"),U=(0,i.g2)("el-dialog"),O=(0,i.g2)("el-checkbox"),P=(0,i.g2)("el-tag"),Q=(0,i.g2)("el-scrollbar"),z=(0,i.g2)("el-row"),H=(0,i.g2)("newEditCase"),M=(0,i.g2)("el-drawer"),B=(0,i.g2)("el-card"),G=(0,i.g2)("el-timeline-item"),Y=(0,i.g2)("el-timeline");return(0,i.uX)(),(0,i.CE)(i.FK,null,[(0,i.bF)(U,{modelValue:x.addApiDlg,"onUpdate:modelValue":t[7]||(t[7]=e=>x.addApiDlg=e),title:"引用接口",width:"88%","before-close":T.clickClear,top:"0"},{default:(0,i.k6)(()=>[(0,i.bF)(z,{gutter:10,class:"main-content"},{default:(0,i.k6)(()=>[(0,i.bF)(E,{xs:24,sm:8,md:6,lg:6,xl:5,class:"left-panel"},{default:(0,i.k6)(()=>[(0,i.bF)(D,{onTreeClick:T.handleTreeClick,handleTreeClick:T.handleTreeClick},null,8,["onTreeClick","handleTreeClick"])]),_:1}),(0,i.bF)(E,{xs:24,sm:16,md:18,lg:18,xl:19,class:"right-content"},{default:(0,i.k6)(()=>[(0,i.Lk)("div",o,[(0,i.bF)(I,{style:{width:"100%","max-width":"300px","margin-right":"10px","margin-bottom":"10px"},modelValue:x.filterText,"onUpdate:modelValue":t[0]||(t[0]=e=>x.filterText=e),placeholder:"请输入接口名称进行搜索",clearable:""},null,8,["modelValue"]),(0,i.bF)(V,{type:"primary",onClick:T.handlenewInterfacesClick,style:{"margin-bottom":"10px"}},{default:(0,i.k6)(()=>t[12]||(t[12]=[(0,i.eW)("查询")])),_:1,__:[12]},8,["onClick"]),(0,i.bF)(V,{onClick:t[1]||(t[1]=e=>x.filterText=""),style:{"margin-bottom":"10px"}},{default:(0,i.k6)(()=>t[13]||(t[13]=[(0,i.eW)("重置")])),_:1,__:[13]})]),(0,i.Lk)("div",s,[(0,i.Lk)("div",c,[(0,i.bF)(V,{type:"primary",onClick:T.userInterface,style:{"margin-right":"10px","margin-bottom":"10px"}},{default:(0,i.k6)(()=>[(0,i.bF)(L,{style:{"margin-right":"6px"}},{default:(0,i.k6)(()=>[(0,i.bF)(W)]),_:1}),(0,i.eW)(" "+(0,a.v_)(T.buttonText),1)]),_:1},8,["onClick"]),(0,i.bF)(V,{type:"primary",onClick:t[2]||(t[2]=e=>x.dialogVisible=!0),style:{"margin-right":"10px","margin-bottom":"10px"}},{default:(0,i.k6)(()=>[(0,i.bF)(L,{style:{"margin-right":"6px"}},{default:(0,i.k6)(()=>[(0,i.bF)(S)]),_:1}),t[14]||(t[14]=(0,i.eW)(" 选择环境 "))]),_:1,__:[14]})]),(0,i.Lk)("div",r,[(0,i.Lk)("span",d,[t[15]||(t[15]=(0,i.eW)("当前环境： ")),(0,i.bF)(V,{type:"info",disabled:"",plain:""},{default:(0,i.k6)(()=>[(0,i.eW)((0,a.v_)(x.selectedEnvironmentName),1)]),_:1})]),(0,i.bF)(V,{type:"warning",style:{"margin-right":"10px","margin-bottom":"10px"},onClick:T.clickClear},{default:(0,i.k6)(()=>[(0,i.bF)(L,{style:{"margin-right":"6px"}},{default:(0,i.k6)(()=>[(0,i.bF)(R)]),_:1}),t[16]||(t[16]=(0,i.eW)(" 关闭窗口 "))]),_:1,__:[16]},8,["onClick"]),(0,i.bF)(V,{type:"primary",style:{"margin-bottom":"10px"},onClick:T.handleApiClick},{default:(0,i.k6)(()=>[(0,i.bF)(L,{style:{"margin-right":"6px"}},{default:(0,i.k6)(()=>[(0,i.bF)(K)]),_:1}),t[17]||(t[17]=(0,i.eW)(" 确认选择 "))]),_:1,__:[17]},8,["onClick"])])]),(0,i.bF)(U,{modelValue:x.dialogVisible,"onUpdate:modelValue":t[5]||(t[5]=e=>x.dialogVisible=e),width:"30%",title:"选择环境"},{footer:(0,i.k6)(()=>[(0,i.Lk)("span",u,[(0,i.bF)(V,{onClick:t[4]||(t[4]=e=>x.dialogVisible=!1)},{default:(0,i.k6)(()=>t[18]||(t[18]=[(0,i.eW)("取消")])),_:1,__:[18]}),(0,i.bF)(V,{type:"primary",onClick:T.confirmSelection},{default:(0,i.k6)(()=>t[19]||(t[19]=[(0,i.eW)("确定")])),_:1,__:[19]},8,["onClick"])])]),default:(0,i.k6)(()=>[(0,i.bF)(N,{rules:e.rulesinterface,ref:"interfaceRef"},{default:(0,i.k6)(()=>[(0,i.bF)($,{label:"测试环境",prop:"env"},{default:(0,i.k6)(()=>[(0,i.bF)(A,{modelValue:x.selectedEnvironment,"onUpdate:modelValue":t[3]||(t[3]=e=>x.selectedEnvironment=e),modelModifiers:{lazy:!0},placeholder:"请选择环境",style:{width:"70%"},"no-data-text":"暂无数据"},{default:(0,i.k6)(()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(e.testEnvs,e=>((0,i.uX)(),(0,i.Wv)(X,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["rules"])]),_:1},8,["modelValue"]),(0,i.Lk)("div",h,"全部接口共 ("+(0,a.v_)(x.interfaceCount)+") 个",1),(0,i.bF)(Q,{class:"interface-scrollbar"},{default:(0,i.k6)(()=>[(0,i.Lk)("div",f,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(x.tableData,e=>((0,i.uX)(),(0,i.CE)("div",{key:e.id,class:(0,a.C4)(["interface-item",[{"interface-item-selected":x.selectionConfig.selectedRowKeys.includes(e.id)},`method-${e.method.toLowerCase()}`]])},[(0,i.Lk)("div",k,[(0,i.bF)(O,{value:x.selectionConfig.selectedRowKeys.includes(e.id),onChange:t=>T.handleSingleSelect(e)},null,8,["value","onChange"])]),(0,i.Lk)("div",{class:"interface-content",onClick:t=>T.clickCopy(e.id)},[(0,i.Lk)("div",m,["POST"===e.method?((0,i.uX)(),(0,i.Wv)(P,{key:0,color:"#49cc90"},{default:(0,i.k6)(()=>[(0,i.eW)((0,a.v_)(e.method),1)]),_:2},1024)):(0,i.Q3)("",!0),"GET"===e.method?((0,i.uX)(),(0,i.Wv)(P,{key:1,color:"#61affe"},{default:(0,i.k6)(()=>[(0,i.eW)((0,a.v_)(e.method),1)]),_:2},1024)):(0,i.Q3)("",!0),"PUT"===e.method?((0,i.uX)(),(0,i.Wv)(P,{key:2,color:"#fca130"},{default:(0,i.k6)(()=>[(0,i.eW)((0,a.v_)(e.method),1)]),_:2},1024)):(0,i.Q3)("",!0),"PATCH"===e.method?((0,i.uX)(),(0,i.Wv)(P,{key:3,color:"#50e3c2"},{default:(0,i.k6)(()=>[(0,i.eW)((0,a.v_)(e.method),1)]),_:2},1024)):(0,i.Q3)("",!0),"DELETE"===e.method?((0,i.uX)(),(0,i.Wv)(P,{key:4,color:"#f93e3e"},{default:(0,i.k6)(()=>[(0,i.eW)((0,a.v_)(e.method),1)]),_:2},1024)):(0,i.Q3)("",!0),"DEAD"===e.method?((0,i.uX)(),(0,i.Wv)(P,{key:5,color:"rgb(201, 233, 104)"},{default:(0,i.k6)(()=>[(0,i.eW)((0,a.v_)(e.method),1)]),_:2},1024)):(0,i.Q3)("",!0)]),(0,i.Lk)("div",p,[(0,i.Lk)("div",{class:"interface-url",title:e.url},(0,a.v_)(e.url),9,C),(0,i.Lk)("div",{class:"interface-name",title:e.name},(0,a.v_)(e.name),9,_)]),(0,i.Lk)("div",{class:"action-section",onClick:t[6]||(t[6]=(0,n.D$)(()=>{},["stop"]))},[(0,i.bF)(V,{type:"text",onClick:t=>T.clickEditStep(e.id)},{default:(0,i.k6)(()=>t[20]||(t[20]=[(0,i.eW)("调试")])),_:2,__:[20]},1032,["onClick"]),(0,i.bF)(V,{type:"text",onClick:t=>T.clickCopy(e.id)},{default:(0,i.k6)(()=>t[21]||(t[21]=[(0,i.eW)("复制")])),_:2,__:[21]},1032,["onClick"]),(0,i.bF)(V,{type:"text",onClick:t=>T.clickDel(e.id)},{default:(0,i.k6)(()=>t[22]||(t[22]=[(0,i.eW)("删除")])),_:2,__:[22]},1032,["onClick"]),(0,i.bF)(V,{type:"text",onClick:T.clickLog},{default:(0,i.k6)(()=>t[23]||(t[23]=[(0,i.eW)("操作记录")])),_:1,__:[23]},8,["onClick"])])],8,g)],2))),128))])]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","before-close"]),(0,i.bF)(M,{modelValue:x.editCaseDlg,"onUpdate:modelValue":t[8]||(t[8]=e=>x.editCaseDlg=e),"destroy-on-close":!0,"with-header":!1,size:"50%",onClose:T.handleClose},{default:(0,i.k6)(()=>[(0,i.bF)(H,{ref:"childRef",onCloseDrawer:T.handleClose,Interface_id:x.Interface_id,copyDlg:x.copyDlg,style:{padding:"0 10px"}},null,8,["onCloseDrawer","Interface_id","copyDlg"])]),_:1},8,["modelValue","onClose"]),(0,i.bF)(M,{modelValue:x.logDlg,"onUpdate:modelValue":t[9]||(t[9]=e=>x.logDlg=e),"with-header":!1,size:"50%"},{default:(0,i.k6)(()=>[(0,i.bF)(B,null,{default:(0,i.k6)(()=>[t[24]||(t[24]=(0,i.Lk)("b",null,"接口操作记录",-1)),(0,i.Lk)("div",b,[(0,i.bF)(Y,null,{default:(0,i.k6)(()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(x.bugLogs,(t,l)=>((0,i.uX)(),(0,i.Wv)(G,{key:l,timestamp:e.$tools.rDate(t.create_time),placement:"top",color:"#0bbd87"},{default:(0,i.k6)(()=>[(0,i.bF)(B,null,{default:(0,i.k6)(()=>[(0,i.Lk)("h4",null,(0,a.v_)(t.handle),1),t.remark?((0,i.uX)(),(0,i.CE)("p",y,"变更记录："+(0,a.v_)(t.remark),1)):(0,i.Q3)("",!0),(0,i.Lk)("span",v,(0,a.v_)(t.update_user),1),(0,i.Lk)("span",null,"操作于 "+(0,a.v_)(e.$tools.rTime(t.create_time)),1)]),_:2},1024)]),_:2},1032,["timestamp"]))),128))]),_:1})])]),_:1,__:[24]})]),_:1},8,["modelValue"]),(0,i.bF)(U,{modelValue:x.importDlg,"onUpdate:modelValue":t[11]||(t[11]=e=>x.importDlg=e),width:"30%",title:"导入接口"},{footer:(0,i.k6)(()=>[(0,i.Lk)("span",w,[(0,i.bF)(V,{onClick:t[10]||(t[10]=e=>x.importDlg=!1)},{default:(0,i.k6)(()=>t[25]||(t[25]=[(0,i.eW)("取消")])),_:1,__:[25]}),(0,i.bF)(V,{type:"primary",onClick:e.confirmSelection1},{default:(0,i.k6)(()=>t[26]||(t[26]=[(0,i.eW)("导入")])),_:1,__:[26]},8,["onClick"])])]),default:(0,i.k6)(()=>[(0,i.bF)(N,{rules:e.rulesinterface,ref:"interfaceRef"},null,8,["rules"])]),_:1},8,["modelValue"])],64)}l(44114),l(18111),l(20116),l(61701);var x=l(31032),T=l(51219),D=l(12933),E=l(80225),I=l(60782),V=l(57477),W={props:{selectType:{type:String}},components:{treeNode:x.A,newEditCase:E.A,Check:V.Check,Close:V.Close,View:V.View,Star:V.Star},computed:{buttonText(){return this.showOnlySelf?"取消只看自己创建":"只看自己创建"},...(0,I.aH)(["pro","testEnvs","envId"]),username(){return window.sessionStorage.getItem("username")},env:{get(){return this.envId},set(e){this.selectEnv(e)}}},data(){return{addApiDlg:!0,treeId:"",filterText:"",tableData:[],editCaseDlg:!1,logDlg:!1,importDlg:!1,Interface_id:"",copyDlg:!1,showOnlySelf:!1,selectedOption:"",dialogVisible:!1,selectedEnvironment:"",selectedEnvironmentName:"暂未选择",selectionConfig:{selectedRowKeys:[],selectionChange:this.handleSelectionChange},bugLogs:[{create_time:"2024-02-18T10:30:00",handle:"修复了一个bug",remark:"这是修复bug的备注",update_user:"张三"},{create_time:"2024-02-17T14:20:00",handle:"重新测试了bug",remark:"接口名称登录变更为tms登录接口",update_user:"李四"},{create_time:"2024-02-16T09:45:00",handle:"提交了一个新的bug",update_user:"王五"}],interfaceCount:0}},methods:{...(0,I.PY)(["selectEnv"]),closeModal(){this.$emit("close-modal")},clickClear(){this.closeModal()},handleSingleSelect(e){this.selectType?this.selectionConfig.selectedRowKeys.push(e):this.selectionConfig.selectedRowKeys.push(e.id)},handleSelectionChange(e){this.selectType?this.selectionConfig.selectedRowKeys=e.map(e=>e):this.selectionConfig.selectedRowKeys=e.map(e=>e.id)},getRowClassName(e){switch(e){case"GET":return"--el-card-border-color:#61affe";case"POST":return"--el-card-border-color:#49cc90";case"PUT":return"--el-card-border-color:#fca130";case"DELETE":return"--el-card-border-color:#f93e3e";case"PATCH":return"--el-card-border-color:#50e3c2";default:return""}},async handleTreeClick(e,t,l){if(t){const l=await this.$api.getNewInterfaces(e,t);200===l.status&&(this.treeId=e,this.tableData=l.data,this.interfaceCount=l.data.length)}else if(l){const i=await this.$api.getNewInterfaces(e,t,l);200===i.status&&(this.treeId=e,this.tableData=i.data,this.interfaceCount=i.data.length)}else{const t=await this.$api.getNewInterfaces(e);200===t.status&&(this.treeId=e,this.tableData=t.data,this.interfaceCount=t.data.length)}},async delInterface(e){const t=await this.$api.delnewInterface(e);204===t.status&&((0,T.nk)({type:"success",message:"删除成功",duration:1e3}),this.handleTreeClick(this.treeId),this.filterText="",this.selectionConfig.selectedRowKeys=[])},handleApiClick(){if(0===this.selectionConfig.selectedRowKeys.length)return void(0,T.nk)({type:"warning",message:"请勾选数据后再操作！",duration:2e3});const e=[...this.selectionConfig.selectedRowKeys];this.$emit("childEvent",e),this.filterText="",this.selectionConfig.selectedRowKeys=[],this.clickClear()},handlenewInterfacesClick(){this.handleTreeClick(this.treeId,this.filterText)},clickDel(e){D.s.confirm("确定要删除该接口吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.delInterface(e)}).catch(()=>{(0,T.nk)({type:"info",message:"取消删除",duration:1e3})})},handleClose(){this.editCaseDlg=!1,this.copyDlg=!1,this.handleTreeClick(this.treeId)},clickEditStep(e){this.Interface_id=e,this.editCaseDlg=!0,this.$nextTick(()=>{this.$refs.childRef.getInterfaceInfo(this.Interface_id)})},clickCopy(e){this.copyDlg=!0,this.clickEditStep(e)},clickLog(){this.logDlg=!0},userInterface(){this.showOnlySelf=!this.showOnlySelf,this.showOnlySelf?this.handleTreeClick(this.treeId,"",this.username):this.handleTreeClick(this.treeId)},confirmSelection(){this.env=this.selectedEnvironment,this.selectedEnvironmentName=this.testEnvs.find(e=>e.id===this.selectedEnvironment).name,this.dialogVisible=!1}}},L=l(71241);const S=(0,L.A)(W,[["render",F],["__scopeId","data-v-0c3c4d2d"]]);var R=S}}]);
//# sourceMappingURL=491.c7b9ed3e.js.map