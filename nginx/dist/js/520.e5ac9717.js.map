{"version": 3, "file": "js/520.e5ac9717.js", "mappings": "uNACOA,MAAM,O,GAEFA,MAAM,e,GACJA,MAAM,a,GACJA,MAAM,mB,GAIJA,MAAM,c,GAgBRA,MAAM,gB,GACJA,MAAM,e,GAEHA,MAAM,gB,GAETA,MAAM,e,GAEHA,MAAM,gB,GAETA,MAAM,e,GAEHA,MAAM,gB,GAETA,MAAM,2B,GAEHA,MAAM,gB,GAIbA,MAAM,gB,GASNA,MAAM,kB,GACJA,MAAM,oB,GA4BGA,MAAM,a,GAITA,MAAM,gB,GACHA,MAAM,gB,GAehBA,MAAM,S,GACLC,MAAA,2E,GAEED,MAAM,wB,GACFA,MAAM,gB,GA2BDA,MAAM,iB,GAOXA,MAAM,qB,GAsDlBA,MAAM,iB,GAkBNA,MAAM,iB,GA0BFA,MAAM,oB,GAoBNA,MAAM,oB,GAUXA,MAAM,Y,q/BA3QZE,EAAAA,EAAAA,IAwLM,MAxLNC,EAwLM,EAvLJC,EAAAA,EAAAA,IAgDUC,EAAA,CAhDDL,MAAM,aAAW,C,iBACxB,IA8CM,EA9CNE,EAAAA,EAAAA,IA8CM,MA9CNI,EA8CM,EA7CJJ,EAAAA,EAAAA,IAuCM,MAvCNK,EAuCM,EAtCJL,EAAAA,EAAAA,IAmBM,MAnBNM,EAmBM,EAlBJJ,EAAAA,EAAAA,IAEYK,EAAA,CAFDT,MAAM,cAAcU,KAAK,OAAQC,QAAOC,EAAAC,M,kBACjD,IAAgC,EAAhCT,EAAAA,EAAAA,IAAgCU,EAAA,M,iBAAvB,IAAa,EAAbV,EAAAA,EAAAA,IAAaW,K,6BAAU,U,6BAElCb,EAAAA,EAAAA,IAcM,MAdNc,EAcM,C,eAbJd,EAAAA,EAAAA,IAAuC,QAAjCF,MAAM,eAAc,UAAM,KAChCI,EAAAA,EAAAA,IAQYK,EAAA,CAPVT,MAAM,mBACNU,KAAK,OACJC,QAAK,C,eAAEC,EAAAK,MAAM,c,qBACd,OAAW,a,kBAEX,IAAuB,E,iBAApBC,EAAAC,SAASC,UAAW,IACvB,IAAAhB,EAAAA,EAAAA,IAA6CU,EAAA,CAApCd,MAAM,aAAW,C,iBAAC,IAAQ,EAARI,EAAAA,EAAAA,IAAQiB,K,eAErCjB,EAAAA,EAAAA,IAEYK,EAAA,CAFDT,MAAM,gBAAiBU,KAAuC,OAAjCQ,EAAAC,SAASG,SAASC,WAAsB,UAAY,W,kBAC1F,IAAoE,E,iBAAjEC,EAAAC,YAAYP,EAAAC,SAASG,SAASC,aAAeL,EAAAC,SAASG,UAAQ,K,sBAIvEpB,EAAAA,EAAAA,IAiBM,MAjBNwB,EAiBM,EAhBJxB,EAAAA,EAAAA,IAGM,MAHNyB,EAGM,C,eAFJzB,EAAAA,EAAAA,IAAsC,QAAhCF,MAAM,gBAAe,QAAI,KAC/BE,EAAAA,EAAAA,IAAsD,OAAtD0B,GAAsDC,EAAAA,EAAAA,IAAzBX,EAAAC,SAASW,SAAO,MAE/C5B,EAAAA,EAAAA,IAGM,MAHN6B,EAGM,C,eAFJ7B,EAAAA,EAAAA,IAAuC,QAAjCF,MAAM,gBAAe,SAAK,KAChCE,EAAAA,EAAAA,IAA0E,OAA1E8B,GAA0EH,EAAAA,EAAAA,IAA5CX,EAAAe,OAAOC,MAAMhB,EAAAC,SAASgB,cAAW,MAEjEjC,EAAAA,EAAAA,IAGM,MAHNkC,EAGM,C,eAFJlC,EAAAA,EAAAA,IAAuC,QAAjCF,MAAM,gBAAe,SAAK,KAChCE,EAAAA,EAAAA,IAA0E,OAA1EmC,GAA0ER,EAAAA,EAAAA,IAA5CX,EAAAe,OAAOC,MAAMhB,EAAAC,SAASmB,cAAW,MAEjEpC,EAAAA,EAAAA,IAGM,MAHNqC,EAGM,C,eAFJrC,EAAAA,EAAAA,IAAuC,QAAjCF,MAAM,gBAAe,SAAK,KAChCE,EAAAA,EAAAA,IAA6D,OAA7DsC,GAA6DX,EAAAA,EAAAA,IAAhCX,EAAAC,SAASsB,MAAQ,QAAJ,UAIhDvC,EAAAA,EAAAA,IAIM,MAJNwC,EAIM,EAHJtC,EAAAA,EAAAA,IAEYK,EAAA,CAFAE,QAAOC,EAAA+B,SAAUjC,KAAK,UAAUV,MAAM,mB,kBAChD,IAAiC,EAAjCI,EAAAA,EAAAA,IAAiCU,EAAA,M,iBAAxB,IAAc,EAAdV,EAAAA,EAAAA,IAAcwC,K,6BAAU,Y,yCAKzCxC,EAAAA,EAAAA,IAqISyC,GAAA,CArIAC,OAAQ,EAAG7C,MAAA,gC,kBAClB,IAgDS,EAhDTG,EAAAA,EAAAA,IAgDS2C,EAAA,CAhDAC,KAAM,GAAC,C,iBACd,IA8CM,EA9CN9C,EAAAA,EAAAA,IA8CM,MA9CN+C,EA8CM,EA7CJ/C,EAAAA,EAAAA,IAMM,MANNgD,EAMM,EALJ9C,EAAAA,EAAAA,IAIW+C,EAAA,C,WAJQ3B,EAAA4B,W,qCAAA5B,EAAA4B,WAAUC,GAAEC,YAAY,YAAYC,UAAA,I,CAC1CC,QAAMC,EAAAA,EAAAA,IACf,IAA6D,EAA7DrD,EAAAA,EAAAA,IAA6DK,EAAA,CAAlDC,KAAK,UAAWC,QAAOC,EAAA8C,a,kBAAa,IAAEC,EAAA,MAAAA,EAAA,M,QAAF,S,wDAIrDvD,EAAAA,EAAAA,IAMYK,EAAA,CALVC,KAAK,UACLV,MAAM,mBACLW,QAAKgD,EAAA,KAAAA,EAAA,GAAAN,GAAEzC,EAAAK,MAAM,S,kBAEd,IAA2B,EAA3Bb,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQwD,K,6BAAU,Y,eAE7BxD,EAAAA,EAAAA,IA8BeyD,EAAA,CA9BDC,OAAO,uBAAqB,C,iBACxC,IA4BU,CA3BFtC,EAAAuC,W,WADRC,EAAAA,EAAAA,IA4BUC,EAAA,C,MA1BR,WAAS,KACR,mBAAkBzC,EAAAuC,SACnB/D,MAAM,cACLkE,KAAM1C,EAAA2C,UACNC,MAAOxD,EAAAyD,aACR,wBACC,wBAAsB,EACtBC,YAAY1D,EAAA2D,iB,CAEFC,SAAOf,EAAAA,EAAAA,IAChB,EADoBgB,OAAMP,UAAI,EAC9B9D,EAAAA,EAAAA,IAIeyD,EAAA,M,iBAHb,IAEO,EAFP3D,EAAAA,EAAAA,IAEO,OAFPwE,GAEO7C,EAAAA,EAAAA,IADF4C,EAAKE,OAAK,K,YAGjBzE,EAAAA,EAAAA,IASM,MATN0E,EASM,EARJ1E,EAAAA,EAAAA,IAOO,OAPP2E,EAOO,EANLzE,EAAAA,EAAAA,IAEYK,EAAA,CAFDC,KAAK,UAAUoE,KAAK,QAAQC,OAAA,GAAO/E,MAAM,2BAA4BW,QAAK0C,GAAEzC,EAAAK,MAAM,OAAOwD,EAAKP,O,kBACvG,IAA2B,EAA3B9D,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQiB,K,gCAEnBjB,EAAAA,EAAAA,IAEYK,EAAA,CAFDC,KAAK,SAASoE,KAAK,QAAQC,OAAA,GAAO/E,MAAM,6BAA8BW,QAAK0C,GAAEzC,EAAAoE,SAASP,EAAKP,KAAKe,K,kBACzG,IAA6B,EAA7B7E,EAAAA,EAAAA,IAA6BU,EAAA,M,iBAApB,IAAU,EAAVV,EAAAA,EAAAA,IAAU8E,K,mIASnC9E,EAAAA,EAAAA,IA+ES2C,EAAA,CA/EAC,KAAM,GAAI/C,MAAA,wB,kBACjB,IA4EM,EA5ENC,EAAAA,EAAAA,IA4EM,MA5ENiF,EA4EM,EA3EJjF,EAAAA,EAAAA,IA0EM,MA1ENkF,EA0EM,C,eAzEJlF,EAAAA,EAAAA,IAAkB,YAAX,QAAI,KACXA,EAAAA,EAAAA,IAuEQ,MAvERmF,EAuEQ,EAtEJnF,EAAAA,EAAAA,IAiCM,MAjCNoF,EAiCM,CAhCc9D,EAAA+D,WAAWC,M,WAA7BxB,EAAAA,EAAAA,IAIayB,EAAA,C,MAJqBzF,MAAM,WAAW0F,OAAO,OAAOC,QAAQ,SAASC,UAAU,O,kBAC1F,IAEY,EAFZxF,EAAAA,EAAAA,IAEYK,EAAA,CAFAT,MAAM,cAAeW,QAAOC,EAAAiF,c,kBACtC,IAA2B,EAA3BzF,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQ0F,K,sDAGrB1F,EAAAA,EAAAA,IAEY2F,GAAA,CAFDxC,UAAA,G,WAAmB/B,EAAA+D,WAAWC,I,qCAAXhE,EAAA+D,WAAWC,IAAGnC,GAAEC,YAAY,OAAOrD,MAAA,gBAAsB,eAAa,OAAO6E,KAAK,QAAQ9E,MAAM,c,kBACjH,IAAwB,G,aAAnCgG,EAAAA,EAAAA,IAAyFC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAA/DhF,EAAAiF,SAARC,K,WAAlBpC,EAAAA,EAAAA,IAAyFqC,GAAA,CAApDC,IAAKF,EAAKnB,GAAKN,MAAOyB,EAAKG,KAAOC,MAAOJ,EAAKnB,I,4DAErF7E,EAAAA,EAAAA,IAuBUqG,GAAA,C,WAvBUjF,EAAAkF,Q,qCAAAlF,EAAAkF,QAAOrD,GAAEsD,MAAM,OAAOC,IAAI,OAAOC,MAAM,O,CAiBhDC,QAAMrD,EAAAA,EAAAA,IACf,IAGO,EAHPvD,EAAAA,EAAAA,IAGO,OAHP6G,EAGO,EAFL3G,EAAAA,EAAAA,IAAwEK,EAAA,CAA5DE,QAAKgD,EAAA,KAAAA,EAAA,GAAAN,GAAEzC,EAAAoG,QAAQxF,EAAAyF,UAAUvG,KAAK,UAAUwG,MAAA,I,kBAAM,IAAEvD,EAAA,MAAAA,EAAA,M,QAAF,S,eAC1DvD,EAAAA,EAAAA,IAAkDK,EAAA,CAAtCE,QAAKgD,EAAA,KAAAA,EAAA,GAAAN,GAAE7B,EAAAkF,SAAU,I,kBAAO,IAAE/C,EAAA,MAAAA,EAAA,M,QAAF,S,mCAnBxC,IAekB,EAflBvD,EAAAA,EAAAA,IAekB+G,GAAA,CAfDC,OAAA,GAAQC,OAAQ,EAAG,kB,kBACC,IAAqD,G,aAAxFrB,EAAAA,EAAAA,IAMuBC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANmC1E,EAAAyF,QAAQK,sBAAqB,CAA5Cd,EAAOF,M,WAAlDtC,EAAAA,EAAAA,IAMuBuD,GAAA,CANA5C,MAAO2B,GAAG,CACpB3B,OAAKlB,EAAAA,EAAAA,IACd,IAAsC,EAAtCrD,EAAAA,EAAAA,IAAsCoH,GAAA,CAA9BC,MAAM,WAAS,C,iBAAC,IAAK9D,EAAA,MAAAA,EAAA,M,QAAL,Y,uBAAc,KACtC9B,EAAAA,EAAAA,IAAGyE,GAAG,K,iBACG,IACX,E,QADW,KACXzE,EAAAA,EAAAA,IAAG2E,GAAK,K,4CAEVR,EAAAA,EAAAA,IAMuBC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANmC1E,EAAAyF,QAAQS,gBAAe,CAAtClB,EAAOF,M,WAAlDtC,EAAAA,EAAAA,IAMuBuD,GAAA,CANA5C,MAAO2B,GAAG,CACpB3B,OAAKlB,EAAAA,EAAAA,IACd,IAAyC,EAAzCrD,EAAAA,EAAAA,IAAyCoH,GAAA,CAAjCC,MAAM,aAAW,C,iBAAC,IAAM9D,EAAA,MAAAA,EAAA,M,QAAN,a,uBAAe,KACzC9B,EAAAA,EAAAA,IAAGyE,GAAG,K,iBACG,IACX,E,QADW,KACXzE,EAAAA,EAAAA,IAAG2E,GAAK,K,iEAWdtG,EAAAA,EAAAA,IAmCM,MAnCNyH,EAmCM,EAlCJvH,EAAAA,EAAAA,IAqBcwH,GAAA,CArBDC,QAAQ,QAAQjC,UAAU,c,CAK1BkC,UAAQrE,EAAAA,EAAAA,IACjB,IAamB,EAbnBrD,EAAAA,EAAAA,IAamB2H,GAAA,M,iBAZjB,IAGmB,EAHnB3H,EAAAA,EAAAA,IAGmB4H,GAAA,CAHDC,QAAQ,OAAQtH,QAAKgD,EAAA,KAAAA,EAAA,GAAAN,GAAEzC,EAAAsH,SAAS,U,kBAChD,IAA6B,EAA7B9H,EAAAA,EAAAA,IAA6BU,EAAA,M,iBAApB,IAAU,EAAVV,EAAAA,EAAAA,IAAU+H,M,6BAAU,a,eAG/B/H,EAAAA,EAAAA,IAGmB4H,GAAA,CAHDC,QAAQ,OAAQtH,QAAKgD,EAAA,KAAAA,EAAA,GAAAN,GAAEzC,EAAAsH,SAAS,W,kBAChD,IAAoC,EAApC9H,EAAAA,EAAAA,IAAoCU,EAAA,M,iBAA3B,IAAiB,EAAjBV,EAAAA,EAAAA,IAAiBgI,M,6BAAU,a,eAGtChI,EAAAA,EAAAA,IAGmB4H,GAAA,CAHDC,QAAQ,OAAQtH,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAsH,SAAS,Y,kBAChD,IAAkC,EAAlC9H,EAAAA,EAAAA,IAAkCU,EAAA,M,iBAAzB,IAAe,EAAfV,EAAAA,EAAAA,IAAeiI,M,6BAAU,a,yCAfxC,IAGY,EAHZjI,EAAAA,EAAAA,IAGYK,EAAA,CAHDC,KAAK,OAAOoE,KAAK,QAAQ9E,MAAM,6BAA6BC,MAAA,yB,kBACrE,IAAiB,C,eAAjBC,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAAwDU,EAAA,CAA/Cd,MAAM,kBAAgB,C,iBAAC,IAAc,EAAdI,EAAAA,EAAAA,IAAckI,M,+BAmBlDlI,EAAAA,EAAAA,IAGYK,EAAA,CAHDC,KAAK,UAAUoE,KAAK,QAAQ9E,MAAM,gBAAiBW,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAsH,SAAS,U,kBAC5E,IAA8B,EAA9B9H,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,IAAWmI,M,qBACpBrI,EAAAA,EAAAA,IAAiB,YAAX,QAAI,M,eAEZE,EAAAA,EAAAA,IAGYK,EAAA,CAHDC,KAAK,UAAUoE,KAAK,QAAQ9E,MAAM,gBAAiBW,QAAOC,EAAA4H,iB,kBACnE,IAA+B,EAA/BpI,EAAAA,EAAAA,IAA+BU,EAAA,M,iBAAtB,IAAY,EAAZV,EAAAA,EAAAA,IAAYqI,M,qBACrBvI,EAAAA,EAAAA,IAAe,YAAT,MAAE,M,6BAEVE,EAAAA,EAAAA,IAGYK,EAAA,CAHDC,KAAK,UAAUoE,KAAK,QAAQ9E,MAAM,gBAAiBW,QAAOC,EAAA8H,a,kBACnE,IAAgC,EAAhCtI,EAAAA,EAAAA,IAAgCU,EAAA,M,iBAAvB,IAAa,EAAbV,EAAAA,EAAAA,IAAauI,M,qBACtBzI,EAAAA,EAAAA,IAAe,YAAT,MAAE,M,qCAMpBE,EAAAA,EAAAA,IAAmHwI,GAAA,CAAxG7E,SAAUvC,EAAAuC,SAAW8E,MAAOrH,EAAAqH,MAAQtD,WAAY/D,EAAA+D,WAAauD,aAAalI,EAAAmI,mB,kEAEvF3I,EAAAA,EAAAA,IAES2C,EAAA,CAFAC,KAAM,GAAC,C,iBACd,IAA+B,EAA/B5C,EAAAA,EAAAA,IAA+B4I,M,iBAKrC5I,EAAAA,EAAAA,IAaYqG,GAAA,CAbCE,MAAOnF,EAAAyH,Y,WAAsBzH,EAAA0H,c,uCAAA1H,EAAA0H,cAAa7F,GAAGwD,MAAM,MAAM,eAAa,eAAgBsC,UAAU,EAAMlJ,MAAA,sBAAyB,eAAcW,EAAAwI,iB,CAM7ItC,QAAMrD,EAAAA,EAAAA,IAClB,IAIO,EAJPvD,EAAAA,EAAAA,IAIO,OAJPmJ,EAIO,EAHNjJ,EAAAA,EAAAA,IAAmDK,EAAA,CAAvCE,QAAOC,EAAAwI,iBAAe,C,iBAAG,IAAEzF,EAAA,MAAAA,EAAA,M,QAAF,S,4BACN,QAAVnC,EAAA8H,a,WAAjBtF,EAAAA,EAAAA,IAAqFvD,EAAA,C,MAAhDC,KAAK,UAAWC,QAAOC,EAAA2I,U,kBAAW,IAAE5F,EAAA,MAAAA,EAAA,M,QAAF,S,0CAC3EK,EAAAA,EAAAA,IAAmEvD,EAAA,C,MAAjDC,KAAK,UAAWC,QAAOC,EAAA4I,W,kBAAY,IAAE7F,EAAA,MAAAA,EAAA,M,QAAF,S,kDATrD,IAIU,EAJVvD,EAAAA,EAAAA,IAIUqJ,GAAA,CAJAC,MAAOlI,EAAAmI,UAAYC,MAAO1I,EAAA2I,UAAWC,IAAI,W,kBACjD,IAEe,EAFf1J,EAAAA,EAAAA,IAEe2J,GAAA,CAFDpF,MAAM,OAAQqF,KAAK,Q,kBAC/B,IAA0E,EAA1E5J,EAAAA,EAAAA,IAA0E+C,EAAA,C,WAAvD3B,EAAAmI,UAAUpD,K,uCAAV/E,EAAAmI,UAAUpD,KAAIlD,GAAG4G,UAAU,KAAK3G,YAAY,W,4GAYrElD,EAAAA,EAAAA,IAeYqG,GAAA,CAfCE,MAAOnF,EAAAyH,Y,WAAsBzH,EAAA0I,e,uCAAA1I,EAAA0I,eAAc7G,GAAGwD,MAAM,MAAM,eAAa,eAAgBsC,UAAU,EAAMlJ,MAAA,sBAAyB,eAAcW,EAAAwI,iB,CAS9ItC,QAAMrD,EAAAA,EAAAA,IAClB,IAGO,EAHPvD,EAAAA,EAAAA,IAGO,OAHPiK,EAGO,EAFN/J,EAAAA,EAAAA,IAAmDK,EAAA,CAAvCE,QAAOC,EAAAwI,iBAAe,C,iBAAG,IAAEzF,EAAA,MAAAA,EAAA,M,QAAF,S,6BACrCvD,EAAAA,EAAAA,IAA4DK,EAAA,CAAhDC,KAAK,UAAWC,QAAOC,EAAAwJ,U,kBAAW,IAAEzG,EAAA,MAAAA,EAAA,M,QAAF,S,iDAX9C,IAOU,EAPVvD,EAAAA,EAAAA,IAOUqJ,GAAA,CAPAC,MAAOlI,EAAA6I,SAAWT,MAAO1I,EAAA2I,UAAWC,IAAI,W,kBAChD,IAEe,EAFf1J,EAAAA,EAAAA,IAEe2J,GAAA,CAFDpF,MAAM,OAAQqF,KAAK,Y,kBAC/B,IAA6E,EAA7E5J,EAAAA,EAAAA,IAA6E+C,EAAA,C,WAA1D3B,EAAA6I,SAASjJ,S,uCAATI,EAAA6I,SAASjJ,SAAQiC,GAAG4G,UAAU,KAAK3G,YAAY,W,gCAEpElD,EAAAA,EAAAA,IAEe2J,GAAA,CAFDpF,MAAM,OAAOqF,KAAK,Q,kBAC9B,IAAuE,EAAvE5J,EAAAA,EAAAA,IAAuE+C,EAAA,CAA7DzC,KAAK,W,WAAoBc,EAAA6I,SAAS5H,K,uCAATjB,EAAA6I,SAAS5H,KAAIY,GAAEC,YAAY,S,4GAWpElD,EAAAA,EAAAA,IAmEYkK,GAAA,C,WAnEQ9I,EAAA+I,O,uCAAA/I,EAAA+I,OAAMlH,GAAIsD,MAAOnF,EAAAgJ,SAAW,oBAAkB,EAAO,cAAY,EAAQC,QAAO7J,EAAA8J,YAAa5F,KAAK,O,CACzGN,SAAOf,EAAAA,EAAAA,IACnB,IA8CU,EA9CVrD,EAAAA,EAAAA,IA8CUuK,GAAA,CA9CDjK,KAAK,OAAOT,MAAA,wB,kBAChB,IAEM,EAFNC,EAAAA,EAAAA,IAEM,aADJE,EAAAA,EAAAA,IAAsLwK,GAAA,CAAzK9F,KAAK,OAAQ+F,cAAerJ,EAAAsJ,gB,WAA2BtJ,EAAAuJ,cAAcC,c,uCAAdxJ,EAAAuJ,cAAcC,cAAa3H,GAAG4H,SAAQrK,EAAAsK,qBAAsBjL,MAAA,sC,kBAAsC,IAAE0D,EAAA,MAAAA,EAAA,M,QAAF,S,6DAExKvD,EAAAA,EAAAA,IAyCeyD,EAAA,CAzCDC,OAAO,uBAAqB,C,iBAC7C,IAmBU,CAlBiB,SAAZtC,EAAA2J,U,WADfnH,EAAAA,EAAAA,IAmBUC,EAAA,C,MAjBD6F,IAAI,WACH5F,KAAM1C,EAAAqH,MACP,mBACCzE,MAAOxD,EAAAyD,aACP+G,cAAcxK,EAAAyK,kBACf,WAAS,KACR,sBAAoB,EACrB,uBACA,aAAW,Q,CACR7G,SAAOf,EAAAA,EAAAA,IACjB,EADqBgB,OAAMP,UAAI,EAC/BhE,EAAAA,EAAAA,IAKO,OALPoL,EAKO,EAJNpL,EAAAA,EAAAA,IAGM,aAFIE,EAAAA,EAAAA,IAAiGU,EAAA,CAAxFd,MAAM,YAAYC,MAAA,mB,kBAAuB,IAAqC,E,iBAAlCW,EAAA2K,aAAa9G,EAAK+G,OAAQ/G,IAAI,K,oBAAc,KACjG5C,EAAAA,EAAAA,IAAGqC,EAAKuH,SAASlF,MAAI,S,uDAK7BvC,EAAAA,EAAAA,IAmBKC,EAAA,C,MAjBD6F,IAAI,WACH5F,KAAM1C,EAAAqH,MACP,mBACCzE,MAAOxD,EAAAyD,aACP+G,cAAcxK,EAAAyK,kBACf,WAAS,KACR,sBAAoB,EACrB,uBACA,aAAW,Q,CACR7G,SAAOf,EAAAA,EAAAA,IACjB,EADqBgB,OAAMP,UAAI,EAC/BhE,EAAAA,EAAAA,IAKO,OALPwL,EAKO,EAJNxL,EAAAA,EAAAA,IAGM,aAFIE,EAAAA,EAAAA,IAAiGU,EAAA,CAAxFd,MAAM,YAAYC,MAAA,mB,kBAAuB,IAAqC,E,iBAAlCW,EAAA2K,aAAa9G,EAAK+G,OAAQ/G,IAAI,K,oBAAc,KAChG5C,EAAAA,EAAAA,IAAGqC,EAAKyB,SAASY,MAAQ,OAAQ,KAAE1E,EAAAA,EAAAA,IAAGqC,EAAKyB,SAASgG,KAAO,OAAJ,S,2DAOvEzL,EAAAA,EAAAA,IAgBM,MAhBN0L,EAgBM,CAfuB,UAAPpK,EAAA2J,U,WAAjBnH,EAAAA,EAAAA,IAEYvD,EAAA,C,MAFwBT,MAAM,+BAA+B8E,KAAK,UAAWnE,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiL,QAAQrK,EAAA2J,W,kBACtG,IAAoC,EAApC/K,EAAAA,EAAAA,IAAoCU,EAAA,M,iBAA3B,IAAiB,EAAjBV,EAAAA,EAAAA,IAAiBgI,M,6BAAU,Y,+BAEd,SAAP5G,EAAA2J,U,WAAjBnH,EAAAA,EAAAA,IAEYvD,EAAA,C,MAFuBT,MAAM,gCAAgC8E,KAAK,UAAWnE,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiL,QAAQrK,EAAA2J,W,kBACtG,IAA6B,EAA7B/K,EAAAA,EAAAA,IAA6BU,EAAA,M,iBAApB,IAAU,EAAVV,EAAAA,EAAAA,IAAU+H,M,6BAAU,Y,+BAEP,WAAP3G,EAAA2J,U,WAAjBnH,EAAAA,EAAAA,IAEYvD,EAAA,C,MAFyBT,MAAM,+BAA+B8E,KAAK,UAAWnE,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiL,QAAQrK,EAAA2J,W,kBACvG,IAAkC,EAAlC/K,EAAAA,EAAAA,IAAkCU,EAAA,M,iBAAzB,IAAe,EAAfV,EAAAA,EAAAA,IAAeiI,M,6BAAU,Y,+BAEZ,SAAP7G,EAAA2J,U,WAAjBnH,EAAAA,EAAAA,IAEYvD,EAAA,C,MAFuBT,MAAM,6BAA6B8E,KAAK,UAAWnE,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiL,QAAQrK,EAAA2J,W,kBACnG,IAA8B,EAA9B/K,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,IAAWmI,M,6BAAU,Y,gCAEhCnI,EAAAA,EAAAA,IAEYK,EAAA,CAFDT,MAAM,+BAA+B8E,KAAK,UAAWnE,QAAOC,EAAA8J,a,kBACrE,IAAkC,EAAlCtK,EAAAA,EAAAA,IAAkCU,EAAA,M,iBAAzB,IAAe,EAAfV,EAAAA,EAAAA,IAAeiI,M,6BAAU,Y,sKCzRrCrI,MAAM,kB,GACJA,MAAM,oB,GACJA,MAAM,kB,GACJA,MAAM,kB,GAcNA,MAAM,mB,GAyBF8L,KAAK,SAAS9L,MAAM,e,GAGdA,MAAM,wB,SAE8BA,MAAM,c,GACtCA,MAAM,a,GACHA,MAAM,a,GAENA,MAAM,c,aAELC,MAAA,mB,cAGAA,MAAA,mB,eAGAA,MAAA,mB,eAGAA,MAAA,mB,eAGAA,MAAA,mB,eAGAA,MAAA,8B,IAIJD,MAAM,e,IACNA,MAAM,Y,IACHA,MAAM,a,UAKsBA,MAAM,c,IACrCA,MAAM,a,IACHA,MAAM,a,IAGTA,MAAM,0B,IACJA,MAAM,uB,UAgBwBA,MAAM,c,IACtCA,MAAM,a,IACHA,MAAM,a,IAGTA,MAAM,e,IACJA,MAAM,wB,eAWNA,MAAM,Q,IACJA,MAAM,gB,IAINA,MAAM,gB,eAeRA,MAAM,Q,IACJA,MAAM,gB,IAKNA,MAAM,gB,eAeRA,MAAM,Q,IACJA,MAAM,sB,IAIJA,MAAM,gB,UAUyDA,MAAM,oB,IACnEA,MAAM,gB,IAINA,MAAM,gB,IAaNA,MAAM,gB,UAOyDA,MAAM,oB,IACrEA,MAAM,gB,IAUNA,MAAM,mB,IAQAC,MAAA,sB,UAYuDD,MAAM,oB,IACnEA,MAAM,gB,IAINA,MAAM,gB,IAQNA,MAAM,iB,IAmBRA,MAAM,uB,IAGJA,MAAM,gB,IAcNA,MAAM,gB,IAcNA,MAAM,gB,IAeNA,MAAM,gB,IAWNA,MAAM,oB,IAKJA,MAAM,gB,IASNA,MAAM,gB,UAemBA,MAAM,c,IACzCA,MAAM,a,IACHA,MAAM,a,IAGTA,MAAM,e,UAeyBA,MAAM,c,IACrCA,MAAM,a,IACHA,MAAM,a,IAGTA,MAAM,e,UAU2BA,MAAM,c,IACvCA,MAAM,a,IACHA,MAAM,a,IAGTA,MAAM,+B,IACJA,MAAM,gB,IAgBZA,MAAM,kB,0qBArZ3BgG,EAAAA,EAAAA,IA6bM,MA7bN7F,EA6bM,EA5bJD,EAAAA,EAAAA,IAwBM,MAxBNI,EAwBM,EAvBJJ,EAAAA,EAAAA,IAsBM,MAtBNK,EAsBM,EArBJL,EAAAA,EAAAA,IAaM,MAbNM,EAaM,EAZJJ,EAAAA,EAAAA,IAESoH,EAAA,CAFDC,MAAM,UAAUzH,MAAM,aAAcW,QAAOC,EAAAmL,a,kBACjD,IAA2B,EAA3B3L,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQwD,K,6BAAU,c,6BAE7BxD,EAAAA,EAAAA,IAESoH,EAAA,CAFDC,MAAM,UAAUzH,MAAM,aAAcW,QAAKgD,EAAA,KAAAA,EAAA,GAAAN,GAAEzC,EAAAoL,cAAc,GAAD,Q,kBAC9D,IAA2B,EAA3B5L,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQwD,K,6BAAU,a,eAE7BxD,EAAAA,EAAAA,IAESoH,EAAA,CAFDC,MAAM,YAAYzH,MAAM,aAAcW,QAAKgD,EAAA,KAAAA,EAAA,GAAAN,GAAEzC,EAAAoL,cAAc,GAAD,Y,kBAChE,IAA2B,EAA3B5L,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQwD,K,6BAAU,a,eAE7BxD,EAAAA,EAAAA,IAESoH,EAAA,CAFDC,MAAM,YAAYzH,MAAM,aAAcW,QAAKgD,EAAA,KAAAA,EAAA,GAAAN,GAAEzC,EAAAoL,cAAc,GAAD,Q,kBAChE,IAA2B,EAA3B5L,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQwD,K,6BAAU,c,iBAG/B1D,EAAAA,EAAAA,IAMM,MANNc,EAMM,EALJZ,EAAAA,EAAAA,IAEaqF,EAAA,CAFDE,QAAQ,gCAAiCsG,WAAW,EAAOrG,UAAU,O,kBAC/E,IAAgE,EAAhExF,EAAAA,EAAAA,IAAgEU,EAAA,CAAvDb,MAAA,yBAA0B,C,iBAAC,IAAkB,EAAlBG,EAAAA,EAAAA,IAAkB8L,K,eAExD9L,EAAAA,EAAAA,IACkB+L,EAAA,CADDrH,KAAK,Q,WAAiBsH,EAAA7G,WAAW8G,O,qCAAXD,EAAA7G,WAAW8G,OAAMhJ,GAAGiJ,IAAK,EAAIC,IAAK,GAAKtB,SAAQ/J,EAAAsL,c,yCAM5FpM,EAAAA,EAAAA,IA0ZeyD,EAAA,CA1ZDC,OAAO,uBAAqB,C,iBACxC,IAwZU,EAxZV1D,EAAAA,EAAAA,IAwZU6D,EAAA,CAvZPC,KAAMkI,EAAAvD,MACNzE,MAAOxD,EAAAyD,aACRoI,UAAA,GACC,qBAAoBjL,EAAAkL,SACpB,wBAAsB,EACtBpI,YAAY1D,EAAA+L,gBACZ,aAAY/L,EAAAgM,UACZC,WAAWjM,EAAAkM,gBACX,kBAAiBlM,EAAAmM,iBAClB/M,MAAM,e,kBAGJ,EADkByE,OAAKP,UAAI,CACZA,EAAKuH,W,WAApBzH,EAAAA,EAAAA,IAyYU3D,EAAA,C,MAzYqBL,OAAKgN,EAAAA,EAAAA,IAAA,0BAA6B9I,EAAKuH,SAAS/K,U,kBAC7E,IAuYM,EAvYNR,EAAAA,EAAAA,IAuYM,MAvYNwB,EAuYM,EAtYJtB,EAAAA,EAAAA,IAqYSyC,EAAA,CArYAC,OAAQ,GAAIpC,KAAK,OAAOuM,MAAM,SAASC,QAAQ,U,kBACtD,IAuWS,EAvWT9M,EAAAA,EAAAA,IAuWS2C,EAAA,CAvWAC,KAAM,GAAIhD,MAAM,qB,kBACvB,IAqWM,EArWNE,EAAAA,EAAAA,IAqWM,MArWNyB,EAqWM,CAnWyB,QAAlBuC,EAAKuH,SAAS/K,O,WAAzBsF,EAAAA,EAAAA,IA6BM,MA7BNpE,EA6BM,EA5BJ1B,EAAAA,EAAAA,IAuBM,MAvBN6B,EAuBM,EAtBJ7B,EAAAA,EAAAA,IAAoE,OAApE8B,GAAoEH,EAAAA,EAAAA,IAAzCjB,EAAA2K,aAAa9G,EAAK+G,OAAQ/G,IAAI,IACzDrE,EAAAA,EAAAA,IAAwDoH,EAAA,CAAhDC,MAAM,UAAUzH,MAAM,Y,kBAAW,IAAM2D,EAAA,MAAAA,EAAA,M,QAAN,a,eACzCzD,EAAAA,EAAAA,IAmBO,OAnBPkC,EAmBO,CAlBwC,SAAjC8B,EAAKuH,SAAS9F,QAAQwH,S,WAAlCnH,EAAAA,EAAAA,IAEO,OAAA3D,EAAA,EADLnC,EAAAA,EAAAA,IAAiE,IAAjEqC,GAAiEV,EAAAA,EAAAA,IAAnCqC,EAAKuH,SAAS9F,QAAQwH,QAAM,O,eAEf,QAAjCjJ,EAAKuH,SAAS9F,QAAQwH,S,WAAlCnH,EAAAA,EAAAA,IAEO,OAAAxD,EAAA,EADLtC,EAAAA,EAAAA,IAAiE,IAAjEwC,IAAiEb,EAAAA,EAAAA,IAAnCqC,EAAKuH,SAAS9F,QAAQwH,QAAM,O,eAEf,QAAjCjJ,EAAKuH,SAAS9F,QAAQwH,S,WAAlCnH,EAAAA,EAAAA,IAEO,OAAA/C,GAAA,EADL/C,EAAAA,EAAAA,IAAiE,IAAjEgD,IAAiErB,EAAAA,EAAAA,IAAnCqC,EAAKuH,SAAS9F,QAAQwH,QAAM,O,eAEf,UAAjCjJ,EAAKuH,SAAS9F,QAAQwH,S,WAAlCnH,EAAAA,EAAAA,IAEO,OAAAtB,GAAA,EADLxE,EAAAA,EAAAA,IAAiE,IAAjE0E,IAAiE/C,EAAAA,EAAAA,IAAnCqC,EAAKuH,SAAS9F,QAAQwH,QAAM,O,eAEf,WAAjCjJ,EAAKuH,SAAS9F,QAAQwH,S,WAAlCnH,EAAAA,EAAAA,IAEO,OAAAnB,GAAA,EADL3E,EAAAA,EAAAA,IAAiE,IAAjEiF,IAAiEtD,EAAAA,EAAAA,IAAnCqC,EAAKuH,SAAS9F,QAAQwH,QAAM,O,eAEf,SAAjCjJ,EAAKuH,SAAS9F,QAAQwH,S,WAAlCnH,EAAAA,EAAAA,IAEO,OAAAZ,GAAA,EADLlF,EAAAA,EAAAA,IAA4E,IAA5EmF,IAA4ExD,EAAAA,EAAAA,IAAnCqC,EAAKuH,SAAS9F,QAAQwH,QAAM,O,oBAI3EjN,EAAAA,EAAAA,IAGM,MAHNoF,GAGM,EAFJpF,EAAAA,EAAAA,IAAuD,IAAvD6G,IAAuDlF,EAAAA,EAAAA,IAAhCqC,EAAKuH,SAAS9F,QAAQgG,KAAG,IAChDzL,EAAAA,EAAAA,IAA8D,OAA9DyH,IAA8D9F,EAAAA,EAAAA,IAApCqC,EAAKuH,SAAS9F,QAAQY,MAAI,S,eAK3B,OAAlBrC,EAAKuH,SAAS/K,O,WAAzBsF,EAAAA,EAAAA,IAmBM,MAnBNqD,GAmBM,EAlBJnJ,EAAAA,EAAAA,IAGM,MAHNiK,GAGM,EAFJjK,EAAAA,EAAAA,IAAoE,OAApEoL,IAAoEzJ,EAAAA,EAAAA,IAAzCjB,EAAA2K,aAAa9G,EAAK+G,OAAQ/G,IAAI,IACzDrE,EAAAA,EAAAA,IAAiEoH,EAAA,CAAzDC,MAAM,oBAAoBzH,MAAM,Y,kBAAW,IAAK2D,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAErDzD,EAAAA,EAAAA,IAaM,MAbNwL,GAaM,EAZJxL,EAAAA,EAAAA,IAWM,MAXN0L,GAWM,EAVJxL,EAAAA,EAAAA,IAAkG+C,EAAA,CAAxFnD,MAAM,YAAYsD,YAAY,gB,WAAyBY,EAAKuH,SAAS9F,QAAQyH,S,yBAAtBlJ,EAAKuH,SAAS9F,QAAQyH,SAAQ/J,G,8CAC/FjD,EAAAA,EAAAA,IAOY2F,EAAA,C,WAPQ7B,EAAKuH,SAAS9F,QAAQ0H,a,yBAAtBnJ,EAAKuH,SAAS9F,QAAQ0H,aAAYhK,EAAEC,YAAY,MAAMtD,MAAM,mB,kBAE5E,IAAuB,G,aADzBgG,EAAAA,EAAAA,IAKEC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJe1E,EAAA8L,QAARlH,K,WADTpC,EAAAA,EAAAA,IAKEqC,EAAA,CAHCC,IAAKF,EAAKI,MACV7B,MAAOyB,EAAKzB,MACZ6B,MAAOJ,EAAKI,O,qFAGjBpG,EAAAA,EAAAA,IAAmF+C,EAAA,CAAzEnD,MAAM,YAAYsD,YAAY,I,WAAaY,EAAKuH,SAAS9F,QAAQa,M,yBAAtBtC,EAAKuH,SAAS9F,QAAQa,MAAKnD,G,oEAMzD,QAAlBa,EAAKuH,SAAS/K,O,WAAzBsF,EAAAA,EAAAA,IAcM,MAdNuH,GAcM,EAbJrN,EAAAA,EAAAA,IAGM,MAHNsN,GAGM,EAFJtN,EAAAA,EAAAA,IAAoE,OAApEuN,IAAoE5L,EAAAA,EAAAA,IAAzCjB,EAAA2K,aAAa9G,EAAK+G,OAAQ/G,IAAI,IACzDrE,EAAAA,EAAAA,IAAgEoH,EAAA,CAAxDC,MAAM,mBAAmBzH,MAAM,Y,kBAAW,IAAK2D,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAEpDzD,EAAAA,EAAAA,IAQM,MARNwN,GAQM,EAPJxN,EAAAA,EAAAA,IAMM,MANNyN,GAMM,EALJvN,EAAAA,EAAAA,IAIiBwN,EAAA,C,WAJQ1J,EAAKuH,SAAS9F,QAAQkI,O,yBAAtB3J,EAAKuH,SAAS9F,QAAQkI,OAAMxK,EAAG1C,QAAKgD,EAAA,KAAAA,EAAA,IAAAmK,EAAAA,EAAAA,IAAN,OAAW,WAAC9N,MAAM,e,kBACvE,IAAqD,EAArDI,EAAAA,EAAAA,IAAqD2N,EAAA,CAA3CpJ,MAAM,QAAQ6B,MAAM,S,kBAAQ,IAAI7C,EAAA,MAAAA,EAAA,M,QAAJ,W,eACtCvD,EAAAA,EAAAA,IAAkD2N,EAAA,CAAxCpJ,MAAM,MAAM6B,MAAM,O,kBAAM,IAAK7C,EAAA,MAAAA,EAAA,M,QAAL,Y,eAClCvD,EAAAA,EAAAA,IAAwD2N,EAAA,CAA9CpJ,MAAM,QAAQ6B,MAAM,S,kBAAQ,IAAO7C,EAAA,MAAAA,EAAA,M,QAAP,c,uFAKjB,QAAlBO,EAAKuH,SAAS/K,MAAgBwD,EAAKuH,SAASuC,M,WAAvDhI,EAAAA,EAAAA,IAqOM,O,MArOsDhG,MAAM,eAAgBW,QAAKgD,EAAA,KAAAA,EAAA,IAAAmK,EAAAA,EAAAA,IAAN,OAAW,Y,CACnD,UAA5B5J,EAAKuH,SAAS9F,QAAQkI,QAAgD,KAA5B3J,EAAKuH,SAAS9F,QAAQkI,S,WAA3E7H,EAAAA,EAAAA,IAmBM,MAAAiI,GAAA,EAlBJ/N,EAAAA,EAAAA,IAiBM,MAjBNgO,GAiBM,EAhBJhO,EAAAA,EAAAA,IAGM,MAHNiO,GAGM,C,eAFJjO,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAA+F+C,EAAA,C,WAA5Ee,EAAKuH,SAAS9F,QAAQyI,W,yBAAtBlK,EAAKuH,SAAS9F,QAAQyI,WAAU/K,EAAEpD,MAAA,gBAAqBqD,YAAY,Q,gDAExFpD,EAAAA,EAAAA,IAWM,MAXNmO,GAWM,C,eAVJnO,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAOE+L,EAAA,C,WANSjI,EAAKuH,SAAS9F,QAAQ2I,c,yBAAtBpK,EAAKuH,SAAS9F,QAAQ2I,cAAajL,EAC3CiJ,IAAK,EACLC,IAAK,IACNzH,KAAK,QACL,oBAAkB,QAClBxB,YAAY,K,4DAEdpD,EAAAA,EAAAA,IAAc,YAAR,KAAC,Y,eAI0B,QAA5BgE,EAAKuH,SAAS9F,QAAQkI,S,WAAjC7H,EAAAA,EAAAA,IAoBM,MAAAuI,GAAA,EAnBJrO,EAAAA,EAAAA,IAkBM,MAlBNsO,GAkBM,EAjBJtO,EAAAA,EAAAA,IAIM,MAJNuO,GAIM,EAHJrO,EAAAA,EAAAA,IAAkG+C,EAAA,CAAxFlD,MAAA,gBAAqBqD,YAAY,S,WAAkBY,EAAKuH,SAAS9F,QAAQ+I,a,yBAAtBxK,EAAKuH,SAAS9F,QAAQ+I,aAAYrL,G,4DAC/FnD,EAAAA,EAAAA,IAAsD,KAAnDD,MAAA,8CAA6C,MAAE,KAClDG,EAAAA,EAAAA,IAAqG+C,EAAA,CAA3FlD,MAAA,gBAAqBqD,YAAY,gB,WAAyBY,EAAKuH,SAAS9F,QAAQyH,S,yBAAtBlJ,EAAKuH,SAAS9F,QAAQyH,SAAQ/J,G,gDAEpGnD,EAAAA,EAAAA,IAWM,MAXNyO,GAWM,C,eAVJzO,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAOE+L,EAAA,C,WANSjI,EAAKuH,SAAS9F,QAAQ2I,c,yBAAtBpK,EAAKuH,SAAS9F,QAAQ2I,cAAajL,EAC3CiJ,IAAK,EACLC,IAAK,IACNzH,KAAK,QACL,oBAAkB,QAClBxB,YAAY,K,4DAEdpD,EAAAA,EAAAA,IAAc,YAAR,KAAC,Y,eAI0B,UAA5BgE,EAAKuH,SAAS9F,QAAQkI,S,WAAjC7H,EAAAA,EAAAA,IA0LM,MAAA4I,GAAA,EAzLJ1O,EAAAA,EAAAA,IAwLM,MAxLN2O,GAwLM,EAvLJ3O,EAAAA,EAAAA,IAsLM,MAtLN4O,GAsLM,C,eArLJ5O,EAAAA,EAAAA,IAA4E,MAAxED,MAAA,0DAA6D,UAAM,KAGvEC,EAAAA,EAAAA,IAOM,MAPN6O,GAOM,C,eANJ7O,EAAAA,EAAAA,IAA0C,QAApCD,MAAA,sBAAyB,QAAI,KACnCG,EAAAA,EAAAA,IAIY2F,EAAA,C,WAJQ7B,EAAKuH,SAAS9F,QAAQqJ,mB,yBAAtB9K,EAAKuH,SAAS9F,QAAQqJ,mBAAkB3L,EAAEC,YAAY,SAASrD,MAAA,iB,kBACjF,IAA2C,EAA3CG,EAAAA,EAAAA,IAA2CiG,EAAA,CAAhC1B,MAAM,OAAO6B,MAAM,cAC9BpG,EAAAA,EAAAA,IAA4CiG,EAAA,CAAjC1B,MAAM,MAAM6B,MAAM,gBAC7BpG,EAAAA,EAAAA,IAA2CiG,EAAA,CAAhC1B,MAAM,OAAO6B,MAAM,e,kDAKsB,aAA7CtC,EAAKuH,SAAS9F,QAAQqJ,qB,WAAjChJ,EAAAA,EAAAA,IAsBM,MAtBNiJ,GAsBM,EArBJ/O,EAAAA,EAAAA,IAGM,MAHNgP,GAGM,C,eAFJhP,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAAgH+C,EAAA,CAAtGlD,MAAA,gBAAqBqD,YAAY,mB,WAA4BY,EAAKuH,SAAS9F,QAAQwJ,iB,yBAAtBjL,EAAKuH,SAAS9F,QAAQwJ,iBAAgB9L,G,gDAE/GnD,EAAAA,EAAAA,IAYM,MAZNkP,GAYM,C,eAXJlP,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXE,EAAAA,EAAAA,IASY2F,EAAA,C,WATQ7B,EAAKuH,SAAS9F,QAAQ0J,c,yBAAtBnL,EAAKuH,SAAS9F,QAAQ0J,cAAahM,EAAEC,YAAY,QAAQrD,MAAA,iB,kBAC3E,IAAkC,EAAlCG,EAAAA,EAAAA,IAAkCiG,EAAA,CAAvB1B,MAAM,IAAI6B,MAAM,QAC3BpG,EAAAA,EAAAA,IAAoCiG,EAAA,CAAzB1B,MAAM,KAAK6B,MAAM,SAC5BpG,EAAAA,EAAAA,IAAkCiG,EAAA,CAAvB1B,MAAM,IAAI6B,MAAM,QAC3BpG,EAAAA,EAAAA,IAAoCiG,EAAA,CAAzB1B,MAAM,KAAK6B,MAAM,SAC5BpG,EAAAA,EAAAA,IAAmCiG,EAAA,CAAxB1B,MAAM,KAAK6B,MAAM,QAC5BpG,EAAAA,EAAAA,IAAmCiG,EAAA,CAAxB1B,MAAM,KAAK6B,MAAM,QAC5BpG,EAAAA,EAAAA,IAAyCiG,EAAA,CAA9B1B,MAAM,KAAK6B,MAAM,cAC5BpG,EAAAA,EAAAA,IAA8CiG,EAAA,CAAnC1B,MAAM,MAAM6B,MAAM,mB,mDAGjCtG,EAAAA,EAAAA,IAGM,MAHNoP,GAGM,C,eAFJpP,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAAkH+C,EAAA,CAAxGlD,MAAA,gBAAqBqD,YAAY,oB,WAA6BY,EAAKuH,SAAS9F,QAAQ4J,kB,yBAAtBrL,EAAKuH,SAAS9F,QAAQ4J,kBAAiBlM,G,kEAK3D,eAA7Ca,EAAKuH,SAAS9F,QAAQqJ,qB,WAAjChJ,EAAAA,EAAAA,IA4BM,MA5BNwJ,GA4BM,EA3BJtP,EAAAA,EAAAA,IASM,MATNuP,GASM,C,eARJvP,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXE,EAAAA,EAAAA,IAME+C,EAAA,CALAlD,MAAA,mCACAqD,YAAY,oD,WACHY,EAAKuH,SAAS9F,QAAQ+J,gB,yBAAtBxL,EAAKuH,SAAS9F,QAAQ+J,gBAAerM,EAC9C3C,KAAK,WACJiP,KAAM,G,gDAGXzP,EAAAA,EAAAA,IAgBM,MAhBN0P,GAgBM,EAfJxP,EAAAA,EAAAA,IAcWyP,EAAA,CAbTlJ,MAAM,QACNjG,KAAK,OACL,eACCoP,UAAU,EACX7P,MAAA,uB,CACWuE,SAAOf,EAAAA,EAAAA,IAChB,IAKM,EALNvD,EAAAA,EAAAA,IAKM,MALN6P,GAKM,EAJJ7P,EAAAA,EAAAA,IAAsC,WAAjC,cAAU2B,EAAAA,EAAAA,IAAEX,EAAA8O,eAAa,G,eAC9B9P,EAAAA,EAAAA,IAAyC,WAApC,kCAA8B,I,eACnCA,EAAAA,EAAAA,IAAiC,WAA5B,0BAAsB,KAC3BA,EAAAA,EAAAA,IAAwD,WAAnD,kBAAc2B,EAAAA,EAAAA,IAAEX,EAAA+O,UAAU,WAAOpO,EAAAA,EAAAA,IAAEX,EAAAgP,SAAS,IAAC,O,2BAQJ,aAA7ChM,EAAKuH,SAAS9F,QAAQqJ,qB,WAAjChJ,EAAAA,EAAAA,IA6BM,MA7BNmK,GA6BM,EA5BJjQ,EAAAA,EAAAA,IAGM,MAHNkQ,GAGM,C,eAFJlQ,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAAoH+C,EAAA,CAA1GlD,MAAA,gBAAqBqD,YAAY,sB,WAA+BY,EAAKuH,SAAS9F,QAAQ0K,kB,yBAAtBnM,EAAKuH,SAAS9F,QAAQ0K,kBAAiBhN,G,gDAEnHnD,EAAAA,EAAAA,IAOM,MAPNoQ,GAOM,C,eANJpQ,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAIE+C,EAAA,CAHAlD,MAAA,gBACAqD,YAAY,qC,WACHY,EAAKuH,SAAS9F,QAAQ4K,kB,yBAAtBrM,EAAKuH,SAAS9F,QAAQ4K,kBAAiBlN,G,gDAGpDnD,EAAAA,EAAAA,IAeM,MAfNsQ,GAeM,EAdJpQ,EAAAA,EAAAA,IAaWyP,EAAA,CAZTlJ,MAAM,SACNjG,KAAK,UACL,eACCoP,UAAU,EACX7P,MAAA,uB,CACWuE,SAAOf,EAAAA,EAAAA,IAChB,IAIME,EAAA,MAAAA,EAAA,MAJNzD,EAAAA,EAAAA,IAIM,OAJDD,MAAA,sBAAwB,EAC3BC,EAAAA,EAAAA,IAAmC,WAA9B,6BACLA,EAAAA,EAAAA,IAA0B,WAArB,oBACLA,EAAAA,EAAAA,IAAyB,WAApB,oB,kCAQfA,EAAAA,EAAAA,IA+EM,MA/ENuQ,GA+EM,C,eA9EJvQ,EAAAA,EAAAA,IAA+E,MAA3ED,MAAA,6DAAgE,UAAM,KAE1EC,EAAAA,EAAAA,IAYM,MAZNwQ,GAYM,C,eAXJxQ,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZE,EAAAA,EAAAA,IAQE+L,EAAA,C,WAPSjI,EAAKuH,SAAS9F,QAAQgL,mB,yBAAtBzM,EAAKuH,SAAS9F,QAAQgL,mBAAkBtN,EAChDiJ,IAAK,EACLC,IAAK,IACNzH,KAAK,QACL,oBAAkB,QAClBxB,YAAY,IACZrD,MAAA,iB,4DAEFC,EAAAA,EAAAA,IAA4E,QAAtED,MAAA,wDAAyD,UAAM,OAGvEC,EAAAA,EAAAA,IAYM,MAZN0Q,GAYM,C,eAXJ1Q,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAQE+L,EAAA,C,WAPSjI,EAAKuH,SAAS9F,QAAQ2I,c,yBAAtBpK,EAAKuH,SAAS9F,QAAQ2I,cAAajL,EAC3CiJ,IAAK,EACLC,IAAK,IACNzH,KAAK,QACL,oBAAkB,QAClBxB,YAAY,IACZrD,MAAA,iB,4DAEFC,EAAAA,EAAAA,IAAc,YAAR,KAAC,OAGTA,EAAAA,EAAAA,IAYM,MAZN2Q,GAYM,C,eAXJ3Q,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVE,EAAAA,EAAAA,IAQE+L,EAAA,C,WAPSjI,EAAKuH,SAAS9F,QAAQmL,a,yBAAtB5M,EAAKuH,SAAS9F,QAAQmL,aAAYzN,EAC1CiJ,IAAK,EACLC,IAAK,KACNzH,KAAK,QACL,oBAAkB,QAClBxB,YAAY,IACZrD,MAAA,iB,4DAEFC,EAAAA,EAAAA,IAAuB,YAAjB,cAAU,OAIlBA,EAAAA,EAAAA,IAQM,MARN6Q,GAQM,C,eAPJ7Q,EAAAA,EAAAA,IAAoB,YAAd,WAAO,KACbE,EAAAA,EAAAA,IAIE+C,EAAA,CAHAlD,MAAA,gBACAqD,YAAY,mB,WACHY,EAAKuH,SAAS9F,QAAQqL,gB,yBAAtB9M,EAAKuH,SAAS9F,QAAQqL,gBAAe3N,G,4DAEhDnD,EAAAA,EAAAA,IAAiF,QAA3ED,MAAA,wDAAyD,eAAW,OAI5EC,EAAAA,EAAAA,IAqBM,MArBN+Q,GAqBM,EApBJ7Q,EAAAA,EAAAA,IAEa8Q,EAAA,CAFD,mBAAiB,OAAOjR,MAAA,0B,kBAClC,IAA0D0D,EAAA,MAAAA,EAAA,MAA1DzD,EAAAA,EAAAA,IAA0D,QAApDD,MAAA,sCAAyC,QAAI,M,eAGrDC,EAAAA,EAAAA,IAOM,MAPNiR,GAOM,EANJ/Q,EAAAA,EAAAA,IAEcwK,EAAA,C,WAFQ1G,EAAKuH,SAAS9F,QAAQyL,kB,yBAAtBlN,EAAKuH,SAAS9F,QAAQyL,kBAAiB/N,EAAEpD,MAAA,yB,kBAA4B,IAE3F0D,EAAA,MAAAA,EAAA,M,QAF2F,kB,yDAG3FvD,EAAAA,EAAAA,IAEcwK,EAAA,C,WAFQ1G,EAAKuH,SAAS9F,QAAQ0L,mB,yBAAtBnN,EAAKuH,SAAS9F,QAAQ0L,mBAAkBhO,G,kBAAE,IAEhEM,EAAA,MAAAA,EAAA,M,QAFgE,iB,2DAKlEzD,EAAAA,EAAAA,IAMM,MANNoR,GAMM,C,eALJpR,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZE,EAAAA,EAAAA,IAGiBwN,EAAA,C,WAHQ1J,EAAKuH,SAAS9F,QAAQ4L,iB,yBAAtBrN,EAAKuH,SAAS9F,QAAQ4L,iBAAgBlO,EAAEyB,KAAK,S,kBACpE,IAAyC,EAAzC1E,EAAAA,EAAAA,IAAyC2N,EAAA,CAA/BpJ,MAAM,UAAQ,C,iBAAC,IAAKhB,EAAA,MAAAA,EAAA,M,QAAL,Y,eACzBvD,EAAAA,EAAAA,IAAwC2N,EAAA,CAA9BpJ,MAAM,SAAO,C,iBAAC,IAAKhB,EAAA,MAAAA,EAAA,M,QAAL,Y,gHAWX,WAAlBO,EAAKuH,SAAS/K,O,WAAzBsF,EAAAA,EAAAA,IAYM,MAZNwL,GAYM,EAXJtR,EAAAA,EAAAA,IAGM,MAHNuR,GAGM,EAFJvR,EAAAA,EAAAA,IAAoE,OAApEwR,IAAoE7P,EAAAA,EAAAA,IAAzCjB,EAAA2K,aAAa9G,EAAK+G,OAAQ/G,IAAI,IACzDrE,EAAAA,EAAAA,IAAgEoH,EAAA,CAAxDC,MAAM,mBAAmBzH,MAAM,Y,kBAAW,IAAK2D,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAEpDzD,EAAAA,EAAAA,IAMM,MANNyR,GAMM,CALwBzN,EAAKuH,SAASmG,W,WAA1C5N,EAAAA,EAAAA,IAAsLb,EAAA,C,MAA3KxC,QAAKgD,EAAA,KAAAA,EAAA,IAAAmK,EAAAA,EAAAA,IAAN,OAAW,W,WAAwC5J,EAAKuH,SAASlF,K,yBAAdrC,EAAKuH,SAASlF,KAAIlD,EAAGwO,OAAIxO,GAAEzC,EAAAkR,cAAc5N,EAAKuH,UAAW3B,IAAI,QAAQG,UAAU,KAAKjK,MAAM,qB,oEACvJgE,EAAAA,EAAAA,IAGYvD,EAAA,C,MAHMT,MAAM,gBAAgBkH,MAAA,GAAMxG,KAAK,OAAQC,QAAK,C,GAAEC,EAAAmR,aAAa7N,EAAKuH,U,qBAAW,OAAW,a,kBACxG,IAAsB,E,iBAApBvH,EAAKuH,SAASlF,MAAM,IACtB,IAAAnG,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQiB,K,qDAIM,WAAlB6C,EAAKuH,SAAS/K,MAAmBwD,EAAKuH,SAASuC,M,WAA1DhI,EAAAA,EAAAA,IAIM,O,MAJyDhG,MAAM,gBAAiBW,QAAKgD,EAAA,KAAAA,EAAA,IAAAmK,EAAAA,EAAAA,IAAN,OAAW,Y,EAC9F1N,EAAAA,EAAAA,IAESyC,EAAA,CAFAC,OAAQ,IAAE,C,iBACjB,IAAyG,EAAzG1C,EAAAA,EAAAA,IAAyG2C,EAAA,CAAhGC,KAAM,IAAE,C,iBAAE,IAA6E,EAA7E5C,EAAAA,EAAAA,IAA6E4R,EAAA,C,WAA5D9N,EAAKuH,SAASwG,O,yBAAd/N,EAAKuH,SAASwG,OAAM5O,EAAE6O,KAAK,SAASC,MAAM,U,0FAKrD,OAAlBjO,EAAKuH,SAAS/K,O,WAAzBsF,EAAAA,EAAAA,IAYM,MAZNoM,GAYM,EAXJlS,EAAAA,EAAAA,IAGM,MAHNmS,GAGM,EAFJnS,EAAAA,EAAAA,IAAoE,OAApEoS,IAAoEzQ,EAAAA,EAAAA,IAAzCjB,EAAA2K,aAAa9G,EAAK+G,OAAQ/G,IAAI,IACzDrE,EAAAA,EAAAA,IAAkEoH,EAAA,CAA1DC,MAAM,oBAAoBzH,MAAM,Y,kBAAW,IAAM2D,EAAA,MAAAA,EAAA,M,QAAN,a,iBAErDzD,EAAAA,EAAAA,IAMM,MANNqS,GAMM,CALwBrO,EAAKuH,SAASmG,W,WAA1C5N,EAAAA,EAAAA,IAAsLb,EAAA,C,MAA3KxC,QAAKgD,EAAA,KAAAA,EAAA,IAAAmK,EAAAA,EAAAA,IAAN,OAAW,W,WAAwC5J,EAAKuH,SAASlF,K,yBAAdrC,EAAKuH,SAASlF,KAAIlD,EAAGwO,OAAIxO,GAAEzC,EAAAkR,cAAc5N,EAAKuH,UAAW3B,IAAI,QAAQG,UAAU,KAAKjK,MAAM,qB,oEACvJgE,EAAAA,EAAAA,IAGYvD,EAAA,C,MAHMT,MAAM,gBAAgBkH,MAAA,GAAMxG,KAAK,OAAQC,QAAK,C,GAAEC,EAAAmR,aAAa7N,EAAKuH,U,uBAAW,OAAW,a,kBACxG,IAAsB,E,iBAApBvH,EAAKuH,SAASlF,MAAM,IACtB,IAAAnG,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQiB,K,qDAMM,SAAlB6C,EAAKuH,SAAS/K,O,WAAzBsF,EAAAA,EAAAA,IAkBM,MAlBNwM,GAkBM,EAjBJtS,EAAAA,EAAAA,IAGM,MAHNuS,GAGM,EAFJvS,EAAAA,EAAAA,IAAoE,OAApEwS,IAAoE7Q,EAAAA,EAAAA,IAAzCjB,EAAA2K,aAAa9G,EAAK+G,OAAQ/G,IAAI,IACzDrE,EAAAA,EAAAA,IAAiEoH,EAAA,CAAzDC,MAAM,oBAAoBzH,MAAM,Y,kBAAW,IAAK2D,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAErDzD,EAAAA,EAAAA,IAYM,MAZNyS,GAYM,EAXJzS,EAAAA,EAAAA,IAUM,MAVN0S,GAUM,EATJxS,EAAAA,EAAAA,IAOE+L,EAAA,C,WANSjI,EAAKuH,SAAS9F,QAAQkN,K,yBAAtB3O,EAAKuH,SAAS9F,QAAQkN,KAAIxP,EAClCiJ,IAAK,EACLC,IAAK,IACNzH,KAAK,QACL,oBAAkB,QAClBxB,YAAY,K,4DAEdpD,EAAAA,EAAAA,IAAc,YAAR,KAAC,Y,+BAMjBE,EAAAA,EAAAA,IA2BS2C,EAAA,CA3BAC,KAAM,EAAGhD,MAAM,gB,kBACtB,IAyBM,EAzBNE,EAAAA,EAAAA,IAyBM,MAzBN4S,GAyBM,CAxBgC,QAAlB5O,EAAKuH,SAAS/K,O,WAAhCsD,EAAAA,EAAAA,IAYayB,EAAA,C,MAZiCzF,MAAM,OAAO0F,OAAO,QAAQC,QAAQ,2BAA2BC,UAAU,O,kBACrH,IAUkB,EAVlBxF,EAAAA,EAAAA,IAUkB+L,EAAA,CATfxL,QAAKgD,EAAA,MAAAA,EAAA,KAAAmK,EAAAA,EAAAA,IAAN,OAAW,W,WACF5J,EAAKuH,SAASY,O,yBAAdnI,EAAKuH,SAASY,OAAMhJ,EAC5BiJ,IAAK,EACLC,IAAK,GACNzH,KAAK,UACL,oBAAkB,QAClBxB,YAAY,IACZrD,MAAA,sC,4EAIJG,EAAAA,EAAAA,IAOE2S,EAAA,CANCpS,QAAK,C,uBAAN,OAAW,W,GAIHO,EAAA8R,YAAY9O,I,WAHXA,EAAKuH,SAASwH,O,yBAAd/O,EAAKuH,SAASwH,OAAM5P,EAC7B,mBACAyB,KAAK,UAEL7E,MAAA,sE,wDAEFG,EAAAA,EAAAA,IAEYK,EAAA,CAFAE,QAAK,C,uBAAN,OAAW,W,GAA6CC,EAAAsS,QAAQhP,IAApDY,KAAK,UAAUC,OAAA,GAAOrE,KAAK,U,kBAChD,IAA6B,EAA7BN,EAAAA,EAAAA,IAA6BU,EAAA,M,iBAApB,IAAU,EAAVV,EAAAA,EAAAA,IAAU8E,K,uNAYtB1D,EAAA2R,Y,WAAfnP,EAAAA,EAAAA,IAAyHoP,EAAA,C,MAA9FC,WAAY7R,EAAA6R,WAAaC,aAAY1S,EAAAoL,cAAgBuH,aAAa3S,EAAA4S,kB,uEAE7FpT,EAAAA,EAAAA,IAEYkK,EAAA,C,WAFQ9I,EAAAiS,W,uCAAAjS,EAAAiS,WAAUpQ,GAAG,eAAa,EAAOyB,KAAK,O,kBACxD,IAAqH,EAArH1E,EAAAA,EAAAA,IAAqHsT,EAAA,CAA5G5J,IAAI,WAAY6J,cAAa/S,EAAA8J,YAAckJ,cAAepS,EAAAoS,cAAe3T,MAAA,oB,2GC1bjFA,MAAA,iB,gDAuHID,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IAULA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,kkBA9IhBgE,EAAAA,EAAAA,IAyJeH,EAAA,CAzJDC,OAAO,mCAAiC,C,iBACtD,IAuJM,EAvJN5D,EAAAA,EAAAA,IAuJM,MAvJNC,GAuJM,EAtJNC,EAAAA,EAAAA,IAAyF8Q,EAAA,CAA7E,mBAAiB,OAAOjR,MAAA,0B,kBAA4B,IAAY0D,EAAA,MAAAA,EAAA,MAAZzD,EAAAA,EAAAA,IAAY,SAAT,SAAK,M,eACxEE,EAAAA,EAAAA,IA8FUqJ,EAAA,CA9FAG,MAAOpI,EAAAqS,eAAgB/J,IAAI,eAAgBJ,MAAOlI,EAAAsS,U,kBAC1D,IA4BS,EA5BT1T,EAAAA,EAAAA,IA4BSyC,EAAA,CA5BCC,OAAQ,GAAI7C,MAAA,0B,kBACtB,IAea,EAfbG,EAAAA,EAAAA,IAea2C,EAAA,CAfJC,KAAM,IAAE,C,iBACf,IAaiB,EAbjB5C,EAAAA,EAAAA,IAaiB2J,EAAA,CAbHC,KAAK,OAAK,C,iBACpB,IAWW,EAXX5J,EAAAA,EAAAA,IAWW+C,EAAA,C,WAXQ3B,EAAAsS,SAASnI,I,qCAATnK,EAAAsS,SAASnI,IAAGtI,GAAEC,YAAY,W,CAChCyQ,SAAOtQ,EAAAA,EAAAA,IAChB,IAOY,EAPZrD,EAAAA,EAAAA,IAOY2F,EAAA,C,WAPQvE,EAAAsS,SAAS3G,O,qCAAT3L,EAAAsS,SAAS3G,OAAM9J,GAAEC,YAAY,OAAOwB,KAAK,QAAQ7E,MAAA,8B,kBACnE,IAAyE,EAAzEG,EAAAA,EAAAA,IAAyEiG,EAAA,CAA9D1B,MAAM,MAAM6B,MAAM,MAAMvG,MAAA,mCACnCG,EAAAA,EAAAA,IAA6DiG,EAAA,CAAlD1B,MAAM,OAAO6B,MAAM,OAAOvG,MAAA,qBACrCG,EAAAA,EAAAA,IAA2DiG,EAAA,CAAhD1B,MAAM,MAAM6B,MAAM,MAAMvG,MAAA,qBACnCG,EAAAA,EAAAA,IAA+DiG,EAAA,CAApD1B,MAAM,QAAQ6B,MAAM,QAAQvG,MAAA,qBACvCG,EAAAA,EAAAA,IAAiEiG,EAAA,CAAtD1B,MAAM,SAAS6B,MAAM,SAASvG,MAAA,qBACzCG,EAAAA,EAAAA,IAAwEiG,EAAA,CAA7D1B,MAAM,OAAO6B,MAAM,OAAOvG,MAAA,iC,iEAMjDG,EAAAA,EAAAA,IAUS2C,EAAA,CAVAC,KAAM,EAAG/C,MAAA,wB,kBAChB,IAEY,EAFZG,EAAAA,EAAAA,IAEYK,EAAA,CAFAE,QAAOC,EAAAoT,QAAStT,KAAK,W,kBAC/B,IAAgC,EAAhCN,EAAAA,EAAAA,IAAgCU,EAAA,M,iBAAvB,IAAa,EAAbV,EAAAA,EAAAA,IAAa6T,K,6BAAU,U,6BAElC7T,EAAAA,EAAAA,IAEYK,EAAA,CAFAE,QAAOC,EAAAsT,UAAWxT,KAAK,W,kBACjC,IAA8B,EAA9BN,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,IAAW+T,K,6BAAU,U,6BAEhC/T,EAAAA,EAAAA,IAEYK,EAAA,CAFAE,QAAOC,EAAAwT,gBAAiB1T,KAAK,W,kBACvC,IAA8B,EAA9BN,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,IAAWmI,K,6BAAU,U,6CAIlCnI,EAAAA,EAAAA,IA+DOyC,EAAA,CA/DEC,OAAQ,GAAI7C,MAAA,0B,kBACrB,IAIS,EAJTG,EAAAA,EAAAA,IAIS2C,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFf5C,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,OAAOqF,KAAK,Q,kBAC9B,IAAwF,EAAxF5J,EAAAA,EAAAA,IAAwF+C,EAAA,C,WAArE3B,EAAAsS,SAASvN,K,qCAAT/E,EAAAsS,SAASvN,KAAIlD,GAAEC,YAAY,UAAUC,UAAA,GAAUtD,MAAA,iB,wCAGtEG,EAAAA,EAAAA,IA2BO2C,EAAA,CA3BEC,KAAM,IAAE,C,iBACjB,IAyBe,EAzBf5C,EAAAA,EAAAA,IAyBeyD,EAAA,CAzBDC,OAAO,QAAM,C,iBACzB,IAuBa,EAvBb1D,EAAAA,EAAAA,IAuBa2J,EAAA,CAvBCpF,MAAM,QAAM,C,iBAExB,IAAqC,G,aADvCqB,EAAAA,EAAAA,IAUmBC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IATH1E,EAAAsS,SAASO,cAAhBC,K,WADTtQ,EAAAA,EAAAA,IAUmBwD,EAAA,CARhBlB,IAAKgO,EACNxP,KAAK,QACJpE,KAAME,EAAA2T,gBACPzE,SAAA,GACC,uBAAqB,EACtB7P,MAAA,uBACCwK,QAAKpH,GAAEzC,EAAA4T,UAAUF,GAClB5O,OAAO,S,kBACR,IAAS,E,iBAAN4O,GAAG,K,sCAEC9S,EAAAiT,MAAMC,U,WADd1Q,EAAAA,EAAAA,IASEb,EAAA,C,MAPA2G,IAAI,kB,WACKtI,EAAAiT,MAAME,S,qCAANnT,EAAAiT,MAAME,SAAQtR,GACvByB,KAAK,QACJ8P,SAAKC,EAAAA,EAAAA,IAAQjU,EAAAkU,OAAM,WACnBjD,OAAMjR,EAAAkU,OACP7U,MAAA,gBACAgK,UAAU,M,wDAEZjG,EAAAA,EAAAA,IAAyEvD,EAAA,C,MAAvDqE,KAAK,QAASnE,QAAOC,EAAAmU,a,kBAAa,IAASpR,EAAA,MAAAA,EAAA,M,QAAT,gB,sDAItDvD,EAAAA,EAAAA,IAIS2C,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFf5C,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,MAAI,C,iBACtB,IAAkF,EAAlFvE,EAAAA,EAAAA,IAAkF+C,EAAA,C,WAA/D3B,EAAAsS,SAASrR,K,qCAATjB,EAAAsS,SAASrR,KAAIY,GAAG3C,KAAK,WAAW6C,UAAA,GAAUtD,MAAA,gB,wCAGjEG,EAAAA,EAAAA,IAIS2C,EAAA,CAJAC,KAAM,GAAC,C,iBAChB,IAEe,EAFf5C,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,QAAS1E,MAAA,uB,kBAC3B,IAAgC,EAAhCC,EAAAA,EAAAA,IAAgC,UAAA2B,EAAAA,EAAAA,IAAA,KAAtBiS,SAAShS,SAAO,K,eAG5B1B,EAAAA,EAAAA,IAMS2C,EAAA,CANAC,KAAM,GAAC,C,iBAChB,IAIe,EAJf5C,EAAAA,EAAAA,IAIe2J,EAAA,CAJDpF,MAAM,QAAS1E,MAAA,uB,CAClBuE,SAAOf,EAAAA,EAAAA,IACoCuR,GAD7B,EACvB9U,EAAAA,EAAAA,IAAoD,UAAA2B,EAAAA,EAAAA,IAA9CX,EAAAe,OAAOC,MAAM+S,KAAKnB,SAAS3R,cAAW,K,eAI9C/B,EAAAA,EAAAA,IAIS2C,EAAA,CAJAC,KAAM,GAAC,C,iBAChB,IAEe,EAFf5C,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,QAAS1E,MAAA,uB,kBAC3B,IAAiC,EAAjCC,EAAAA,EAAAA,IAAiC,UAAA2B,EAAAA,EAAAA,IAAA,KAAvBiS,SAASoB,UAAQ,K,eAG7B9U,EAAAA,EAAAA,IAMS2C,EAAA,CANAC,KAAM,GAAC,C,iBAChB,IAIe,EAJf5C,EAAAA,EAAAA,IAIe2J,EAAA,CAJDpF,MAAM,QAAU1E,MAAA,uB,CACjBuE,SAAOf,EAAAA,EAAAA,IACmEuR,GAD5D,C,KACTlB,SAASxR,c,WAAvB0D,EAAAA,EAAAA,IAAmF,IAAA1F,IAAAuB,EAAAA,EAAAA,IAA7CX,EAAAe,OAAOC,MAAM+S,KAAKnB,SAASxR,cAAW,K,oEAMlFlC,EAAAA,EAAAA,IAAoF8Q,EAAA,CAAxE,mBAAiB,OAAOjR,MAAA,sB,kBAAwB,IAAW0D,EAAA,MAAAA,EAAA,MAAXzD,EAAAA,EAAAA,IAAW,SAAR,QAAI,M,eAEnEE,EAAAA,EAAAA,IA+CUuK,EAAA,CA/CDjK,KAAK,cAAcT,MAAA,wB,kBAC3B,IAAmF,EAAnFG,EAAAA,EAAAA,IAAmF+U,EAAA,CAAtExQ,MAAM,gBAAc,C,iBAAC,IAAmC,EAAnCvE,EAAAA,EAAAA,IAAmC4R,EAAA,C,WAAlBxQ,EAAA4T,Q,qCAAA5T,EAAA4T,QAAO/R,I,gCAC1DjD,EAAAA,EAAAA,IAAkF+U,EAAA,CAArExQ,MAAM,gBAAc,C,iBAAC,IAAkC,EAAlCvE,EAAAA,EAAAA,IAAkC4R,EAAA,C,WAAjBxQ,EAAA6T,O,qCAAA7T,EAAA6T,OAAMhS,I,gCACzDjD,EAAAA,EAAAA,IAWc+U,EAAA,CAXDxQ,MAAM,aAAW,C,iBAC7B,IAIiB,EAJjBvE,EAAAA,EAAAA,IAIiBwN,EAAA,C,WAJQpM,EAAA8T,U,qCAAA9T,EAAA8T,UAASjS,GAAEpD,MAAA,yB,kBACnC,IAAkD,EAAlDG,EAAAA,EAAAA,IAAkD2N,EAAA,CAAxCpJ,MAAM,QAAM,C,iBAAC,IAAgBhB,EAAA,MAAAA,EAAA,M,QAAhB,uB,eACvBvD,EAAAA,EAAAA,IAAuD2N,EAAA,CAA7CpJ,MAAM,QAAM,C,iBAAC,IAAqBhB,EAAA,MAAAA,EAAA,M,QAArB,4B,eACvBvD,EAAAA,EAAAA,IAA+C2N,EAAA,CAArCpJ,MAAM,YAAU,C,iBAAC,IAAShB,EAAA,MAAAA,EAAA,M,QAAT,gB,uCAEH,SAAdnC,EAAA8T,Y,WAAXtP,EAAAA,EAAAA,IAAuE,MAAAzF,GAAA,EAAtCH,EAAAA,EAAAA,IAAgC4R,EAAA,C,WAAfxQ,EAAA+T,K,qCAAA/T,EAAA+T,KAAIlS,I,0BACxB,SAAd7B,EAAA8T,Y,WAAhBtP,EAAAA,EAAAA,IAA4E,MAAAxF,GAAA,EAAtCJ,EAAAA,EAAAA,IAAgC4R,EAAA,C,WAAfxQ,EAAA0C,K,qCAAA1C,EAAA0C,KAAIb,I,0BAC7B,aAAd7B,EAAA8T,Y,WAAhBtP,EAAAA,EAAAA,IAEM,MAAAhF,GAAA,EADLZ,EAAAA,EAAAA,IAAoCoV,EAAA,C,WAAjBhU,EAAAiU,K,uCAAAjU,EAAAiU,KAAIpS,I,mDAGzBjD,EAAAA,EAAAA,IAWc+U,EAAA,CAXDxQ,MAAM,QAAM,C,iBACxB,IASS,EATTvE,EAAAA,EAAAA,IASSyC,EAAA,CATAC,OAAQ,IAAE,C,iBAClB,IAA2G,EAA3G1C,EAAAA,EAAAA,IAA2G2C,EAAA,CAAlGC,KAAM,IAAE,C,iBAAE,IAA+E,EAA/E5C,EAAAA,EAAAA,IAA+E4R,EAAA,C,WAA9DxQ,EAAAsS,SAAS4B,a,uCAATlU,EAAAsS,SAAS4B,aAAYrS,GAAE6O,KAAK,SAASC,MAAM,W,gCAC/E/R,EAAAA,EAAAA,IAMS2C,EAAA,CANAC,KAAM,GAAC,C,iBACf,IAAiD,EAAjD5C,EAAAA,EAAAA,IAAiD8Q,EAAA,CAArCjR,MAAA,iBAAmB,C,iBAAC,IAAI0D,EAAA,MAAAA,EAAA,M,QAAJ,W,eAChCzD,EAAAA,EAAAA,IAA4H,MAA5HwB,GAA4H,EAAtGtB,EAAAA,EAAAA,IAAgGK,EAAA,CAArFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAA+U,iBAAiB,S,kBAAQ,IAAMhS,EAAA,MAAAA,EAAA,M,QAAN,a,iBACpGzD,EAAAA,EAAAA,IAA4H,MAA5HyB,GAA4H,EAAtGvB,EAAAA,EAAAA,IAAgGK,EAAA,CAArFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAA+U,iBAAiB,S,kBAAQ,IAAMhS,EAAA,MAAAA,EAAA,M,QAAN,a,iBACpGzD,EAAAA,EAAAA,IAA6H,MAA7H0B,GAA6H,EAAvGxB,EAAAA,EAAAA,IAAiGK,EAAA,CAAtFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAA+U,iBAAiB,U,kBAAS,IAAMhS,EAAA,MAAAA,EAAA,M,QAAN,a,iBACrGzD,EAAAA,EAAAA,IAA6H,MAA7H6B,GAA6H,EAAvG3B,EAAAA,EAAAA,IAAiGK,EAAA,CAAtFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAA+U,iBAAiB,S,kBAAQ,IAAOhS,EAAA,MAAAA,EAAA,M,QAAP,c,yCAIvGvD,EAAAA,EAAAA,IAmBc+U,EAAA,CAnBDxQ,MAAM,QAAM,C,iBACxB,IAiBS,EAjBTvE,EAAAA,EAAAA,IAiBSyC,EAAA,CAjBAC,OAAQ,IAAE,C,iBAClB,IAA8G,EAA9G1C,EAAAA,EAAAA,IAA8G2C,EAAA,CAArGC,KAAM,IAAE,C,iBAAE,IAAkF,EAAlF5C,EAAAA,EAAAA,IAAkF4R,EAAA,C,WAAjExQ,EAAAsS,SAAS8B,gB,uCAATpU,EAAAsS,SAAS8B,gBAAevS,GAAE6O,KAAK,SAASC,MAAM,W,gCAClF/R,EAAAA,EAAAA,IAcS2C,EAAA,CAdAC,KAAM,GAAC,C,iBACf,IAAiD,EAAjD5C,EAAAA,EAAAA,IAAiD8Q,EAAA,CAArCjR,MAAA,iBAAmB,C,iBAAC,IAAI0D,EAAA,MAAAA,EAAA,M,QAAJ,W,eAChCvD,EAAAA,EAAAA,IAWeyD,EAAA,CAXDC,OAAO,SAAO,C,iBAC3B,IAAiI,EAAjI5D,EAAAA,EAAAA,IAAiI,MAAjI8B,GAAiI,EAA3G5B,EAAAA,EAAAA,IAAqGK,EAAA,CAA1FC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,a,kBAAY,IAAKlS,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAC1GzD,EAAAA,EAAAA,IAA0I,MAA1IkC,GAA0I,EAApHhC,EAAAA,EAAAA,IAA8GK,EAAA,CAAnGC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,e,kBAAc,IAAYlS,EAAA,MAAAA,EAAA,M,QAAZ,mB,iBAC5GzD,EAAAA,EAAAA,IAAoI,MAApImC,GAAoI,EAA9GjC,EAAAA,EAAAA,IAAwGK,EAAA,CAA7FC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,e,kBAAc,IAAMlS,EAAA,MAAAA,EAAA,M,QAAN,a,iBAC5GzD,EAAAA,EAAAA,IAA8H,MAA9HqC,GAA8H,EAAxGnC,EAAAA,EAAAA,IAAkGK,EAAA,CAAvFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,S,kBAAQ,IAAMlS,EAAA,MAAAA,EAAA,M,QAAN,a,iBACtGzD,EAAAA,EAAAA,IAA8H,MAA9HsC,GAA8H,EAAxGpC,EAAAA,EAAAA,IAAkGK,EAAA,CAAvFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,S,kBAAQ,IAAMlS,EAAA,MAAAA,EAAA,M,QAAN,a,iBACtGzD,EAAAA,EAAAA,IAA+H,MAA/HwC,GAA+H,EAAzGtC,EAAAA,EAAAA,IAAmGK,EAAA,CAAxFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,U,kBAAS,IAAMlS,EAAA,MAAAA,EAAA,M,QAAN,a,iBACvGzD,EAAAA,EAAAA,IAA+H,MAA/H+C,GAA+H,EAAzG7C,EAAAA,EAAAA,IAAmGK,EAAA,CAAxFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,S,kBAAQ,IAAOlS,EAAA,MAAAA,EAAA,M,QAAP,c,iBACtGzD,EAAAA,EAAAA,IAAkI,MAAlIgD,GAAkI,EAA5G9C,EAAAA,EAAAA,IAAsGK,EAAA,CAA3FC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,U,kBAAS,IAASlS,EAAA,MAAAA,EAAA,M,QAAT,gB,iBACvGzD,EAAAA,EAAAA,IAA2H,MAA3HwE,GAA2H,EAArGtE,EAAAA,EAAAA,IAA+FK,EAAA,CAApFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,Q,kBAAO,IAAIlS,EAAA,MAAAA,EAAA,M,QAAJ,W,iBACrGzD,EAAAA,EAAAA,IAAgI,MAAhI0E,GAAgI,EAA1GxE,EAAAA,EAAAA,IAAoGK,EAAA,CAAzFC,KAAK,UAAUoE,KAAK,QAAQoC,MAAA,GAAOvG,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAiV,mBAAmB,a,kBAAY,IAAIlS,EAAA,MAAAA,EAAA,M,QAAJ,W,wDAMpGnC,EAAAsU,Y,WAAX9P,EAAAA,EAAAA,IAGQ,MAAAnB,GAAA,EAFPzE,EAAAA,EAAAA,IAA4D8Q,EAAA,CAAhD,mBAAiB,QAAM,C,iBAAC,IAAWvN,EAAA,MAAAA,EAAA,MAAXzD,EAAAA,EAAAA,IAAW,SAAR,QAAI,M,eAC3CE,EAAAA,EAAAA,IAA6C2V,EAAA,CAAhCC,OAAQxU,EAAAsU,WAAS,uB,iEAajC,IACE1R,MAAO,CACLwP,cAAe,CACblT,KAAMuV,OACNzR,QAAS,CAAC,IAId0R,WAAY,CACVC,WAAU,KACVC,SAAQ,KACRC,OAAM,KACNC,UAAS,aACTC,QAAO,WACPC,QAAOA,GAAAA,SAETtS,IAAAA,GACE,MAAO,CACL2P,eAAgB,CACdtN,KAAM,CACJ,CACE4C,UAAU,EACVsN,QAAS,UACT5O,QAAS,SAGb8D,IAAK,CACH,CACExC,UAAU,EACVsN,QAAS,UACT5O,QAAS,UAIf4M,MAAO,CACLiC,KAAM,CACJtQ,KAAM,CACJ,CAAC1F,KAAM,IACP,CAACA,KAAM,WACP,CAACA,KAAM,QACP,CAACA,KAAM,UACP,CAACA,KAAM,aAGXgU,SAAS,EACTC,SAAU,IAEZb,SAAU,CACR3G,OAAQ,OACRkH,cAAe,GACfsC,YAAY,GACZhL,IAAK,GACLpF,KAAM,GACNqQ,SAAU3B,KAAK4B,OACf/U,QAAS,GACToT,SAAU,GACVzS,KAAM,GACN2S,QAAS,CAAC,EACV0B,QAAS,CAAC,KAAQ,CAAC,EAAG,KAAQ,KAAM,OAAU,CAAC,GAC/CrB,KAAM,GACNC,aAAc,gGAMdE,gBAAiB,2HAQnBN,UAAW,OACXC,KAAM,KACNrR,KAAM,KACNmR,OAAQ,KACRD,QAAS,KACT2B,gBAAiB,KACjBtB,KAAM,GACNpB,cAAe,GACfyB,UAAW,GAEf,EACAkB,SAAU,KACLC,EAAAA,EAAAA,IAAS,CAAC,MAAO,UACtBC,QAAAA,GACC,OAAOC,OAAOC,eAAeC,QAAQ,WACtC,GAGAC,QAAS,CAEPC,UAAAA,GACEtC,KAAKuC,UAAU,KACbvC,KAAKwC,MAAMC,gBAAgBC,SAE/B,EAEA7C,MAAAA,GACMG,KAAKR,MAAMC,SAAWO,KAAKR,MAAME,WAC9BM,KAAKnB,SAASO,gBAAeY,KAAKnB,SAASO,cAAgB,IAChEY,KAAKnB,SAASO,cAAcuD,KAAK3C,KAAKR,MAAME,UAC5CM,KAAKsC,cAEPtC,KAAKR,MAAMC,SAAU,EACrBO,KAAKR,MAAME,SAAW,EACxB,EAGAH,SAAAA,CAAUF,GACRW,KAAKnB,SAASO,cAAcwD,OAAO5C,KAAKnB,SAASO,cAAcyD,QAAQxD,GAAM,EAC/E,EAGAS,WAAAA,GACEE,KAAKR,MAAMC,SAAU,EACrBO,KAAKsC,YACP,EAEAhD,aAAAA,GACE,MAAMwD,EAAcC,KAAKC,MAAMD,KAAKE,SAAWjD,KAAKR,MAAMiC,KAAKtQ,KAAK+R,QACpE,OAAOlD,KAAKR,MAAMiC,KAAKtQ,KAAK2R,GAAarX,IAC3C,EAGAiV,gBAAAA,CAAiByC,GACf,OAAQA,GACN,IAAK,MACHnD,KAAKnB,SAAS4B,cAAgB,oDAC9B,MACF,IAAK,MACHT,KAAKnB,SAAS4B,cAAgB,kDAC9B,MACF,IAAK,OACHT,KAAKnB,SAAS4B,cAAgB,8EAC9B,MACF,IAAK,MACHT,KAAKnB,SAAS4B,cACV,gKACJ,MAEN,EAEAG,kBAAAA,CAAmBuC,GACjB,OAAQA,GACN,IAAK,UACHnD,KAAKnB,SAAS8B,iBAAmB,iDACjCX,KAAKnB,SAAS8B,iBAAmB,+CACjC,MACF,IAAK,YACHX,KAAKnB,SAAS8B,iBAAmB,0FACjC,MACF,IAAK,YACHX,KAAKnB,SAAS8B,iBAAmB,wEACjC,MACF,IAAK,MACHX,KAAKnB,SAAS8B,iBAAmB,oDACjC,MACF,IAAK,MACHX,KAAKnB,SAAS8B,iBAAmB,kDACjC,MACF,IAAK,OACHX,KAAKnB,SAAS8B,iBAAmB,8EACjC,MACF,IAAK,MACHX,KAAKnB,SAAS8B,iBACV,gKACJ,MACF,IAAK,OACHX,KAAKnB,SAAS8B,iBAAmB,0FACjC,MACF,IAAK,KACHX,KAAKnB,SAAS8B,iBAAmB,gDACjC,MACF,IAAK,UACHX,KAAKnB,SAAS8B,iBAAmB,mEACjC,MAEN,EAEAyC,aAAAA,GACEpD,KAAKa,UAAY,KACjBb,KAAKnB,SAAW,IAAImB,KAAKrB,cAAcjO,SACvCsP,KAAKM,KAAO+C,KAAKC,UAAUtD,KAAKnB,SAASgD,QAAQvB,MAAQ,CAAC,EAAG,KAAM,GACnEN,KAAK/Q,KAAOoU,KAAKC,UAAUtD,KAAKnB,SAASgD,QAAQ5S,MAAQ,CAAC,EAAG,KAAM,GACnE+Q,KAAKI,OAASiD,KAAKC,UAAUtD,KAAKnB,SAASgD,QAAQzB,QAAU,CAAC,EAAG,KAAM,GACvEJ,KAAKG,QAAUkD,KAAKC,UAAUtD,KAAKnB,SAASsB,SAAW,CAAC,EAAG,KAAM,GACjEH,KAAKnB,SAASO,cAAgBmE,MAAMC,KAAKxD,KAAKnB,SAASO,cAAcC,KACrEW,KAAKQ,KAAOR,KAAKnB,SAAS2B,IAC5B,EAGAiD,WAAAA,GACE,IAAIC,EAAW,IAAI1D,KAAKnB,iBACjB6E,EAAS1F,OAGhB0F,EAAStE,cAAgB,CAACC,IAAK,IAAIqE,EAAStE,gBAC5CsE,EAASzD,SAAWD,KAAKiC,SACzByB,EAASrW,YAAc2S,KAAKhT,OAAO2W,UACnC,IACED,EAASvD,QAAUkD,KAAKO,MAAM5D,KAAKG,QACrC,CAAE,MAAO0D,GAMP,OALA7D,KAAK8D,SAAS,CACZtC,QAAS,6BACT/V,KAAM,UACNsY,SAAU,MAEL,IACT,CAEA,GAAuB,SAAnB/D,KAAKK,UAAsB,CAC7B,MAAM2D,EAAQC,EAAQ,OACtB,IACEP,EAAS7B,QAAU,CAAEvB,KAAM0D,EAAMJ,MAAM5D,KAAKM,OAC5CoD,EAAS7B,QAAQ5S,KAAO,KACxByU,EAASlD,KAAO,EAElB,CAAE,MAAOqD,GAMP,OALA7D,KAAK8D,SAAS,CACZtC,QAAS,wCACT/V,KAAM,UACNsY,SAAU,MAEL,IACT,CACF,MACK,GAAuB,SAAnB/D,KAAKK,UACZ,IACEqD,EAAS7B,QAAU,CAAC5S,KAAMoU,KAAKO,MAAM5D,KAAK/Q,OAC1CyU,EAAS7B,QAAQvB,KAAO,KACxBoD,EAASlD,KAAO,EAClB,CAAE,MAAOqD,GAMP,OALA7D,KAAK8D,SAAS,CACZtC,QAAS,0CACT/V,KAAM,UACNsY,SAAU,MAEL,IACT,KAE0B,aAAnB/D,KAAKK,YACZqD,EAASlD,KAAOR,KAAKQ,KACrBkD,EAAS7B,QAAU,CAAC,GAEtB,IAGE,OAFA6B,EAAS7B,QAAQzB,OAASiD,KAAKO,MAAM5D,KAAKI,QAEnCsD,CACT,CAAE,MAAOG,GAMP,OALA7D,KAAK8D,SAAS,CACZtC,QAAS,2BACT/V,KAAM,UACNsY,SAAU,MAEL,IACT,CAEF,EAIA,eAAM9E,GACJe,KAAKwC,MAAM0B,aAAaC,SAASC,UAE/B,IAAKC,EAAO,OACZ,MAAMjE,EAAS,CAAC1P,QAASsP,KAAKyD,eACxBa,QAAiBtE,KAAKuE,KAAKC,iBAAiBxE,KAAKrB,cAAc3O,GAAIoQ,GACjD,MAApBkE,EAAStG,QACXgC,KAAK8D,SAAS,CACZrY,KAAM,UACN+V,QAAS,OACTuC,SAAU,OAIlB,EAGA,aAAMhF,GACJiB,KAAKwC,MAAM0B,aAAaC,SAASC,UAE/B,IAAKC,EAAO,OACZ,MAAMI,EAAUzE,KAAKyD,cACrBgB,EAAQC,UAAY,CAClBhO,IAAKsJ,KAAKnB,SAASnI,IACnBwB,OAAQ8H,KAAKnB,SAAS3G,QAExB,MAAMkI,EAAS,CACbnR,KAAMwV,EACNlU,IAAKyP,KAAK2E,OAENL,QAAiBtE,KAAKuE,KAAKK,WAAWxE,GACpB,MAApBkE,EAAStG,SACXgC,KAAKa,UAAYyD,EAASrV,MAC1B4V,EAAAA,EAAAA,IAAe,CACXd,SAAU,IACVrS,MAAO,OACPjG,KAAM,cAIhB,EAGA,qBAAM0T,GACJ,MAAMmF,QAAiBtE,KAAKuE,KAAKO,gBAAgB9E,KAAKrB,cAAcjO,QAAQV,IACpD,MAApBsU,EAAStG,SACXgC,KAAK8D,SAAS,CACjBrY,KAAM,UACN+V,QAAS,kBACTuC,SAAU,MAEP/D,KAAKnB,SAAW,IAAIyF,EAASrV,MAC7B+Q,KAAKa,UAAY,KACjBb,KAAKM,KAAO+C,KAAKC,UAAUtD,KAAKnB,SAASgD,QAAQvB,MAAQ,CAAC,EAAG,KAAM,GACnEN,KAAK/Q,KAAOoU,KAAKC,UAAUtD,KAAKnB,SAASgD,QAAQ5S,MAAQ,CAAC,EAAG,KAAM,GACnE+Q,KAAKI,OAASiD,KAAKC,UAAUtD,KAAKnB,SAASgD,QAAQzB,QAAU,CAAC,EAAG,KAAM,GACvEJ,KAAKG,QAAUkD,KAAKC,UAAUtD,KAAKnB,SAASsB,SAAW,CAAC,EAAG,KAAM,GACjEH,KAAKnB,SAASO,cAAgBmE,MAAMC,KAAKxD,KAAKnB,SAASO,cAAcC,KACrEW,KAAKQ,KAAOR,KAAKnB,SAAS2B,KAE9B,GAIFuE,MAAO,CACLpG,cAAe,CACbqG,MAAM,EACNC,OAAAA,CAAQC,EAAQC,GACdnF,KAAKoD,eACP,IAGJgC,OAAAA,GACEpF,KAAKoD,eACP,G,YC/eF,MAAMiC,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,UFicA,IACEpE,WAAW,CACTqE,QAAO,KACPlE,OAAM,KACNmE,QAAO,GACPC,KAAI,QACJC,KAAI,QACJC,OAAM,UACNC,eAAcA,GAAAA,gBAEhBxW,MAAO,CACLL,SAAU,CACRrD,KAAMma,OACNrW,QAAS,IAEXqE,MAAO,CACLnI,KAAM8X,OAERjT,WAAW,CACT7E,KAAMuV,SAGV/R,IAAAA,GACE,MAAO,CACLiP,WAAU,EACVM,YAAW,EACXJ,WAAW,OACXyH,QAAS,GACTpO,UAAU,EACVqO,eAAe,CACbC,OAAO,GACPzU,KAAM,GACN7F,KAAM,GACNiF,QAAS,CAAC,EACVlD,KAAK,GACLwP,OAAO,GACPnQ,QAAQ,GACRuK,OAAQ,IAEV4O,QAAS,GACTrH,cAAc,GACdtG,QAAS,CACL,CAAE9G,MAAO,QAAS7B,MAAO,MACzB,CAAE6B,MAAO,WAAY7B,MAAO,OAC5B,CAAE6B,MAAO,WAAY7B,MAAO,MAC5B,CAAE6B,MAAO,cAAe7B,MAAO,OAC/B,CAAE6B,MAAO,cAAe7B,MAAO,MAC/B,CAAE6B,MAAO,WAAY7B,MAAO,MAC5B,CAAE6B,MAAO,qBAAsB7B,MAAO,QACtC,CAAE6B,MAAO,kBAAmB7B,MAAO,QACnC,CAAE6B,MAAO,QAAS7B,MAAO,KACzB,CAAE6B,MAAO,WAAY7B,MAAO,OAIpC,EACA2S,QAAS,CAEP4D,aAAAA,CAAcxO,GACXuI,KAAK6F,SAAW,IAAIK,KACpBlG,KAAKvI,SAAWA,CAClB,EAEDC,eAAAA,CAAgBzI,GACW,QAArBA,EAAKuH,SAAS/K,MAChBuU,KAAKxB,YAAa,EAClBwB,KAAKrB,cAAgB1P,EAAKuH,UAEpB,CAAC,MAAM,UAAU2P,SAASlX,EAAKuH,SAAS/K,QAC9CwD,EAAKuH,SAASuC,KAAO9J,EAAKuH,SAASuC,IAEvC,EAEApB,SAAAA,CAAUyO,EAAcC,EAAS5a,GAE/B,MAAM6a,EAAqB,CAAC,MAAO,MACnC,QAAKA,EAAmBH,SAASE,EAASpX,KAAKuH,SAAS/K,QACtC,SAATA,GAA4B,SAATA,EAKhC,EAEE8a,UAAAA,CAAWzX,GACTkR,KAAKwG,MAAM,cAAe1X,EAC5B,EAEA,qBAAM+I,GACJ,MAAM4O,EAAeA,CAACjX,EAAMkX,EAAUC,KAEpCnX,EAAKoX,KAAOD,EAERnX,EAAKqX,UAAYrX,EAAKqX,SAAS3D,OAAS,GACxC1T,EAAKqX,SAASC,QAAQ,CAACC,EAAOC,KAE1BD,EAAMxQ,OAAS/G,EAAKQ,GAEpB+W,EAAMH,KAAOI,EAAa,EAE1BP,EAAaM,EAAOvX,EAAKQ,GAAI+W,EAAMH,SAK7C5G,KAAKpM,MAAMkT,QAAQ,CAACvQ,EAAQ0Q,KAExB1Q,EAAOqQ,KAAOK,EAAc,EAExB1Q,EAAOsQ,UAAYtQ,EAAOsQ,SAAS3D,OAAS,EAE5CuD,EAAalQ,EAAQA,EAAOvG,GAAIuG,EAAOqQ,MAEzCrQ,EAAOA,OAAS,MAG1B,EAEEuB,gBAAAA,GACEoP,SAASC,iBAAiB,YAAa,SAASC,GAChD,MAAMC,EAASD,EAAME,QACfC,EAAaL,SAASM,cAAc,YAAYC,wBAAwB9V,IAE1E0V,EAAS,KAAOE,EAAa,EAC/BrF,OAAOwF,SAAS,GAAI,IACXL,EAASnF,OAAOyF,YAAc,KACvCzF,OAAOwF,SAAS,EAAG,GAEvB,EACA,EAEApR,YAAAA,CAAaC,EAAQ/G,GACnB,MAAMoY,EAAQrR,EAAOsR,WAAWhF,QAAQrT,GACxC,OAAOoY,EAAQ,CACjB,EAEA,mBAAM7Q,CAAc9H,EAAMxD,GACxB,MAAM2U,EAAS,IAAIJ,KAAK8F,gBACxB1F,EAAOvT,QAAUmT,KAAKiC,SACtB7B,EAAO3U,KAAOA,EACd2U,EAAO2F,OAAS/F,KAAKlR,SACrB,MAAMgZ,EAAY,GAClB,IAAIC,EAAU/H,KAAKpM,MAAMsP,OAAS,EAAIlD,KAAKpM,MAAMsP,OAAS,EAAI,EAoC9D,GAlCW,OAARzX,GACD2U,EAAO9O,KAAO,QACd8O,EAAO1P,QAAU,CACfyH,SAAS,GACTC,aAAa,GACb7G,MAAM,WAED6O,EAAOhJ,QAEA,WAAR3L,GACN2U,EAAO9O,KAAO,eACP8O,EAAOhJ,QAGA,SAAR3L,GACN2U,EAAO9O,KAAO,QACd8O,EAAO1P,QAAU,CACfkN,KAAK,WAEAwC,EAAOhJ,SAGdgJ,EAAO9O,KAAO,SACd8O,EAAO3U,KAAO,MACd2U,EAAOhJ,OAAS,EAChBnI,EAAK6X,QAAQ3V,IACb,IAAI6W,EAAU,IACT5H,EACH1P,QAAQS,GAEV2W,EAAUnF,KAAKqF,MAIb,CAAC,KAAM,MAAO,OAAQ,UAAU7B,SAAS1a,GAAO,CAClD,MAAM6Y,QAAiBtE,KAAKuE,KAAK0D,gBAAgB7H,GACzB,MAApBkE,EAAStG,SACTgC,KAAKgG,QAAU1B,EAASrV,KAAKe,GAEnC,KACK,CACH,MAAMsU,QAAiBtE,KAAKuE,KAAK0D,gBAAgBH,GACzB,MAApBxD,EAAStG,SACTgC,KAAKgG,QAAU1B,EAASrV,KAAKiZ,IAAI/W,GAAQA,EAAKnB,IAEpD,CACA,MAAMsU,QAAiBtE,KAAKuE,KAAK4D,oBAAoB,CACnDC,KAAMpI,KAAK9T,SAAS8D,GACpB+V,OAAQ/F,KAAKlR,SACbuZ,KAAMrI,KAAKgG,QACXY,KAAMmB,EACNlb,QAASmT,KAAKiC,SACd1L,OAAQ,OAEgB,MAApB+N,EAAStG,SACX6G,EAAAA,EAAAA,IAAe,CACXd,SAAU,IACVrS,MAAO,OACPjG,KAAM,YAGduU,KAAKuG,WAAWvG,KAAKlR,SACvB,EAEAyP,gBAAAA,GACEyB,KAAK9B,WAAY,CACnB,EACApH,WAAAA,GACEkJ,KAAK9B,WAAY,CACnB,EAEArB,aAAAA,CAAc5N,GACZA,EAAK0N,UAAW,CAClB,EAEAG,YAAAA,CAAa7N,GACRA,EAAKxD,KAAiBwD,EAAK0N,UAAW,EACzCqD,KAAKuC,UAAU,KACbvC,KAAKwC,MAAM8F,MAAM5F,SAErB,EAGA,aAAMzE,CAAQhP,GACZmY,MAAMmB,kBACNC,QAAQC,IAAIxZ,GACZ,MAAMqV,QAAiBtE,KAAKuE,KAAKmE,oBAAoBzZ,EAAKe,GAAGgQ,KAAKlR,UACrE,GAAwB,MAApBwV,EAAStG,OAAgB,CAC3B,MAAM2K,QAAY3I,KAAKuE,KAAKqE,gBAAgB3Z,EAAKuH,SAASxG,IACvC,MAAf2Y,EAAI3K,UACH6G,EAAAA,EAAAA,IAAe,CACXd,SAAU,IACVrS,MAAO,OACPjG,KAAM,YAEVuU,KAAKuG,WAAWvG,KAAKlR,UAE5B,CACD,EACC2G,WAAAA,GACGuK,KAAKxB,YAAa,CACpB,GAGFuD,SAAU,KACLC,EAAAA,EAAAA,IAAS,CACV2C,MAAOnF,GAASA,EAAMmF,MACtBzT,SAAUsO,GAASA,EAAMtO,SACzB2X,IAAKrJ,GAASA,EAAMqJ,IACpB3c,SAAUsT,GAASA,EAAMtT,WAE3BkD,YAAAA,GACE,MAAO,CACLyX,SAAU,WACVnX,MAAO,OAEX,EACAuS,QAAAA,GACE,OAAOC,OAAOC,eAAeC,QAAQ,WACvC,IG1sBJ,MAAM,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,U,UCNWpX,MAAA,kG,IAEEA,MAAA,4B,0BAmIcA,MAAA,2F,ygBAvIzBG,EAAAA,EAAAA,IAgLUC,EAAA,CAhLD0d,OAAO,SAAO,C,iBACrB,IA8Ke,EA9Kf3d,EAAAA,EAAAA,IA8KeyD,EAAA,CA9KAC,OAAO,uBAAqB,C,iBACzC,IAMM,EANN5D,EAAAA,EAAAA,IAMM,MANNC,GAMM,C,eALFD,EAAAA,EAAAA,IAA0D,QAApDD,MAAA,0CAAyC,QAAI,KACrDC,EAAAA,EAAAA,IAGM,MAHNI,GAGM,EAFJF,EAAAA,EAAAA,IAA8DK,EAAA,CAAnDC,KAAK,UAAWC,QAAOC,EAAAod,c,kBAAc,IAAEra,EAAA,MAAAA,EAAA,M,QAAF,S,6BAChDvD,EAAAA,EAAAA,IAA+DK,EAAA,CAApDC,KAAK,OAAQC,QAAOC,EAAAqd,c,kBAAc,IAAMta,EAAA,MAAAA,EAAA,M,QAAN,a,iCAGjDvD,EAAAA,EAAAA,IAqKUqJ,EAAA,CArKAC,MAAOlI,EAAA0c,WAActU,MAAOpI,EAAA2c,YAAarU,IAAI,YAAY,cAAY,Q,kBAC7E,IAEe,EAFf1J,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,QAAQqF,KAAK,Y,kBAC/B,IAA4D,E,iBAAzDxI,EAAAC,YAAYD,EAAA0c,WAAW5c,WAAaE,EAAA0c,WAAW5c,UAAQ,K,OAE5DlB,EAAAA,EAAAA,IAEe2J,EAAA,CAFDC,KAAK,OAAOrF,MAAM,S,kBAC9B,IAAqE,EAArEvE,EAAAA,EAAAA,IAAqE+C,EAAA,C,WAAlD3B,EAAA0c,WAAW3X,K,qCAAX/E,EAAA0c,WAAW3X,KAAIlD,GAAEC,YAAY,W,+BAEX,OAAnB9B,EAAA0c,WAAW5c,W,WAA/B0C,EAAAA,EAAAA,IAqBe+F,EAAA,C,MArBiCpF,MAAM,QAAQqF,KAAK,Q,kBACjE,IAmBa,EAnBb5J,EAAAA,EAAAA,IAmBage,EAAA,CAlBHC,QAAS7c,EAAA8c,Y,kCAAA9c,EAAA8c,YAAWjb,GAC5BuC,UAAU,eACViB,MAAM,M,CACK0X,WAAS9a,EAAAA,EAAAA,IAClB,IAME,EANFrD,EAAAA,EAAAA,IAME+C,EAAA,C,WALS3B,EAAA0c,WAAWM,K,qCAAXhd,EAAA0c,WAAWM,KAAInb,GACxBE,UAAA,GACAkb,SAAA,GACAnb,YAAY,cACX3C,QAAOC,EAAA8d,S,oDAGZ,IAKkB,EALlBte,EAAAA,EAAAA,IAKkBue,EAAA,CAJfC,WAAYpd,EAAA0c,WAAWM,KACvBK,YAAWje,EAAAke,iBACXC,UAASne,EAAAoe,a,iGAKhB5e,EAAAA,EAAAA,IAOe2J,EAAA,CAPDC,KAAK,UAAUrF,MAAM,S,kBACjC,IAKY,EALZvE,EAAAA,EAAAA,IAKY2F,EAAA,C,WALSnF,EAAAqe,gB,qCAAAre,EAAAqe,gBAAe5b,GAAEC,YAAY,UAAUrD,MAAA,gB,kBAC1D,IAA0C,EAA1CG,EAAAA,EAAAA,IAA0CiG,EAAA,CAA/B1B,MAAM,KAAK6B,MAAM,OAC5BpG,EAAAA,EAAAA,IAAgDiG,EAAA,CAArC1B,MAAM,UAAU6B,MAAM,QACjCpG,EAAAA,EAAAA,IAAiDiG,EAAA,CAAtC1B,MAAM,WAAW6B,MAAM,QAClCpG,EAAAA,EAAAA,IAAiDiG,EAAA,CAAtC1B,MAAM,WAAW6B,MAAM,S,gCAGtCpG,EAAAA,EAAAA,IAKe2J,EAAA,CALDpF,MAAM,QAAQqF,KAAK,W,kBAC/B,IAGY,EAHZ5J,EAAAA,EAAAA,IAGY2F,EAAA,C,WAHQnF,EAAAse,kB,qCAAAte,EAAAse,kBAAiB7b,GAAEC,YAAY,UAAUrD,MAAA,gB,kBAC3D,IAA6C,EAA7CG,EAAAA,EAAAA,IAA6CiG,EAAA,CAAlC1B,MAAM,OAAO6B,MAAM,QAC9BpG,EAAAA,EAAAA,IAA6CiG,EAAA,CAAlC1B,MAAM,OAAO6B,MAAM,S,gCAGlCpG,EAAAA,EAAAA,IAKe2J,EAAA,CALDpF,MAAM,QAAQqF,KAAK,gB,kBAC/B,IAGY,EAHZ5J,EAAAA,EAAAA,IAGY2F,EAAA,C,WAHQnF,EAAAue,mB,qCAAAve,EAAAue,mBAAkB9b,GAAEC,YAAY,UAAUrD,MAAA,gB,kBAC5D,IAA+C,EAA/CG,EAAAA,EAAAA,IAA+CiG,EAAA,CAApC1B,MAAM,OAAO6B,MAAM,QAC9BpG,EAAAA,EAAAA,IAA+CiG,EAAA,CAApC1B,MAAM,OAAO6B,MAAM,S,gCAGlCpG,EAAAA,EAAAA,IAMe2J,EAAA,CANDpF,MAAM,QAAQqF,KAAK,gB,kBAC/B,IAIY,EAJZ5J,EAAAA,EAAAA,IAIY2F,EAAA,C,WAJQvE,EAAA0c,WAAWkB,S,qCAAX5d,EAAA0c,WAAWkB,SAAQ/b,GAAEC,YAAY,UAAUrD,MAAA,gB,kBAC7D,IAA2C,EAA3CG,EAAAA,EAAAA,IAA2CiG,EAAA,CAAhC1B,MAAM,IAAI6B,MAAM,OAC3BpG,EAAAA,EAAAA,IAA2CiG,EAAA,CAAhC1B,MAAM,IAAI6B,MAAM,OAC3BpG,EAAAA,EAAAA,IAA2CiG,EAAA,CAAhC1B,MAAM,IAAI6B,MAAM,Q,gCAG/BpG,EAAAA,EAAAA,IAqCuB2J,EAAA,CArCTpF,MAAM,QAAQqF,KAAK,a,kBACvB,IAGY,EAHZ5J,EAAAA,EAAAA,IAGY2F,EAAA,C,WAHQnF,EAAAye,e,qCAAAze,EAAAye,eAAchc,GAAEC,YAAY,UAAUrD,MAAA,uC,kBACxD,IAA6C,EAA7CG,EAAAA,EAAAA,IAA6CiG,EAAA,CAAlC1B,MAAM,KAAK6B,MAAM,QAC5BpG,EAAAA,EAAAA,IAA6CiG,EAAA,CAAlC1B,MAAM,KAAK6B,MAAM,S,uBAEW,OAA7BhF,EAAA0c,WAAWoB,gB,WAAvBtZ,EAAAA,EAAAA,IAoBO,OAAAzF,GAAA,EAnBLH,EAAAA,EAAAA,IAQE+L,EAAA,C,WAPS3K,EAAA0c,WAAWqB,UAAU,G,qCAArB/d,EAAA0c,WAAWqB,UAAU,GAADlc,GAC5BiJ,IAAK,EACLC,IAAK,IACNzH,KAAK,QACL,oBAAkB,QACjBmG,SAAQ/J,EAAAsL,aACTvM,MAAA,gB,iDAEFC,EAAAA,EAAAA,IAAyD,QAAnDD,MAAA,4CAA2C,KAAC,KAClDG,EAAAA,EAAAA,IAQE+L,EAAA,C,WAPS3K,EAAA0c,WAAWqB,UAAU,G,qCAArB/d,EAAA0c,WAAWqB,UAAU,GAADlc,GAC5BiJ,IAAK,EACLC,IAAK,IACNzH,KAAK,QACL,oBAAkB,QACjBmG,SAAQ/J,EAAAsL,aACTvM,MAAA,gB,kDAGJ+F,EAAAA,EAAAA,IAUO,OAAAxF,GAAA,EATLJ,EAAAA,EAAAA,IAQE+L,EAAA,C,WAPS3K,EAAA0c,WAAWqB,UAAU,G,uCAArB/d,EAAA0c,WAAWqB,UAAU,GAADlc,GAC5BiJ,IAAK,EACLC,IAAK,IACNzH,KAAK,QACL,oBAAkB,QACjBmG,SAAQ/J,EAAAsL,aACTvM,MAAA,gB,8CAIdG,EAAAA,EAAAA,IAce2J,EAAA,CAdD9J,MAAA,6CAA6C0E,MAAM,QAAQqF,KAAK,Y,kBAC5E,IAYiB,EAZjB5J,EAAAA,EAAAA,IAYiBwN,EAAA,C,WAZQpM,EAAA0c,WAAWsB,S,uCAAXhe,EAAA0c,WAAWsB,SAAQnc,I,kBAC1C,IAIW,EAJXjD,EAAAA,EAAAA,IAIW2N,EAAA,CAJApJ,MAAM,KAAMhE,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAA6e,cAAc,Q,kBAAO,IACjD,C,uBADiD,SACjDrf,EAAAA,EAAAA,IAEaqF,EAAA,CAFDE,QAAQ,gBAAiBsG,WAAW,EAAOrG,UAAU,O,kBAC/D,IAAyEjC,EAAA,MAAAA,EAAA,MAAzEzD,EAAAA,EAAAA,IAAyE,KAAtEF,MAAM,mBAAmBC,MAAA,sC,2CAGhCG,EAAAA,EAAAA,IAKW2N,EAAA,CALApJ,MAAM,KAAMhE,QAAKgD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAA6e,cAAc,Q,kBAAO,IAEjD,C,uBAFiD,WAEjDrf,EAAAA,EAAAA,IAEaqF,EAAA,CAFDE,QAAQ,eAAgBsG,WAAW,EAAOrG,UAAU,O,kBAC9D,IAAyEjC,EAAA,MAAAA,EAAA,MAAzEzD,EAAAA,EAAAA,IAAyE,KAAtEF,MAAM,mBAAmBC,MAAA,sC,2EAKE,OAAvBuB,EAAA0c,WAAWwB,e,WAA1B1b,EAAAA,EAAAA,IAaU3D,EAAA,C,MAbqCJ,MAAA,+BAAkCD,MAAM,OAAO+d,OAAO,U,kBACnG,IAWU,EAXV3d,EAAAA,EAAAA,IAWUqJ,EAAA,CAXD,cAAY,QAASC,MAAOlI,EAAAme,gBAAmB/V,MAAOpI,EAAAoe,qBAAsB9V,IAAI,W,kBACvF,IAEe,EAFf1J,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,SAASqF,KAAK,qB,kBAChC,IAAiE,EAAjE5J,EAAAA,EAAAA,IAAiE+C,EAAA,C,WAA9C3B,EAAAme,gBAAgBE,kB,uCAAhBre,EAAAme,gBAAgBE,kBAAiBxc,I,gCAEtDjD,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,SAASqF,KAAK,mB,kBAChC,IAA+D,EAA/D5J,EAAAA,EAAAA,IAA+D+C,EAAA,C,WAA5C3B,EAAAme,gBAAgBG,gB,uCAAhBte,EAAAme,gBAAgBG,gBAAezc,I,gCAEpDjD,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,QAAQqF,KAAK,Y,kBAC/B,IAAwD,EAAxD5J,EAAAA,EAAAA,IAAwD+C,EAAA,C,WAArC3B,EAAAme,gBAAgBI,S,uCAAhBve,EAAAme,gBAAgBI,SAAQ1c,I,oFAKX,OAAvB7B,EAAA0c,WAAWwB,e,WAA1B1b,EAAAA,EAAAA,IA4BkB3D,EAAA,C,MA5B6BJ,MAAA,wEAAqED,MAAM,OAAO+d,OAAO,U,kBAC9H,IAuBU,EAvBV3d,EAAAA,EAAAA,IAuBUqJ,EAAA,CAvBD,cAAY,QAASC,MAAOlI,EAAAwe,WAAapW,MAAOpI,EAAAye,gBAAiBnW,IAAI,W,kBACvE,IAA6C,G,aAAlD9D,EAAAA,EAAAA,IAqBMC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IArByB1E,EAAAwe,WAAWE,QAAO,CAApCC,EAAQtD,M,WAArB7W,EAAAA,EAAAA,IAqBM,OArB8CM,IAAKuW,GAAK,EAC5D3c,EAAAA,EAAAA,IAUM,MAVNc,GAUM,EATJd,EAAAA,EAAAA,IAA8B,YAAxB,MAAE2B,EAAAA,EAAAA,IAAGgb,EAAQ,GAAH,IAChBzc,EAAAA,EAAAA,IAOYK,EAAA,CANT2f,SAAUvD,EAAQ,EACnB/X,KAAK,OACLpE,KAAK,OACJC,QAAK0C,GAAEzC,EAAAyf,aAAaxD,I,kBACtB,IAEDlZ,EAAA,MAAAA,EAAA,M,QAFC,W,6CAIHvD,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,SAAUqF,KAAI,WAAe6S,EAAQ,sB,kBACvD,IAAwD,EAAxDzc,EAAAA,EAAAA,IAAwD+C,EAAA,C,WAArCgd,EAAON,kB,yBAAPM,EAAON,kBAAiBxc,G,oEAE7CjD,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,SAAUqF,KAAI,WAAe6S,EAAQ,oB,kBACvD,IAAsD,EAAtDzc,EAAAA,EAAAA,IAAsD+C,EAAA,C,WAAnCgd,EAAOL,gB,yBAAPK,EAAOL,gBAAezc,G,oEAE3CjD,EAAAA,EAAAA,IAEe2J,EAAA,CAFDpF,MAAM,UAAWqF,KAAI,WAAe6S,EAAQ,a,kBACxD,IAA+C,EAA/Czc,EAAAA,EAAAA,IAA+C+C,EAAA,C,WAA5Bgd,EAAOJ,S,yBAAPI,EAAOJ,SAAQ1c,G,0GAIxCjD,EAAAA,EAAAA,IAEYK,EAAA,CAFAR,MAAA,gFAAkFU,QAAOC,EAAA0f,W,kBAAY,IAEjH3c,EAAA,MAAAA,EAAA,M,QAFiH,iB,qDAK9F,OAAnBnC,EAAA0c,WAAWsB,W,WADrBxb,EAAAA,EAAAA,IAYSuc,EAAA,C,MAVLzc,OAAO,MACNI,KAAM1C,EAAAgf,WACPvgB,MAAA,0DACAmH,OAAO,OACNqZ,kBAAkB7f,EAAA8f,sBACnB5W,IAAI,e,kBAER,IAAiD,EAAjD1J,EAAAA,EAAAA,IAAiDugB,EAAA,CAAhCjgB,KAAK,YAAYmG,MAAM,UACxCzG,EAAAA,EAAAA,IAA4DugB,EAAA,CAA3C1T,MAAM,SAASjD,KAAK,OAAOrF,MAAM,UAClDvE,EAAAA,EAAAA,IAAyEugB,EAAA,CAAxD1T,MAAM,SAASjD,KAAK,UAAUrF,MAAM,KAAKkC,MAAM,Y,mGAMtEzG,EAAAA,EAAAA,IAEYqG,EAAA,CAFDE,MAAM,S,WAAkBnF,EAAAof,W,uCAAApf,EAAAof,WAAUvd,GAAE,sBAAkB,eAAczC,EAAA8J,YAAa7D,MAAM,MAAMD,IAAI,Q,kBAC1G,IAA6H,EAA7HxG,EAAAA,EAAAA,IAA6HygB,EAAA,CAAnHC,UAAWtf,EAAAsf,UAAYxf,SAAUE,EAAA0c,WAAW5c,SAAWyf,SAASngB,EAAA8J,YAAesW,UAAUpgB,EAAAqgB,e,iIAQvG,IACE/K,WAAY,CACVgL,QAAO,cACPC,cAAaA,GAAAA,GAEfjd,IAAAA,GACE,MAAO,CACLzC,YAAa,CAAC,GAAM,OAAQ,GAAM,QAClC6c,aAAa,EACbJ,WAAY,CACV3X,KAAM,GACNiY,KAAM,GACNld,SAAU,GACV8f,QAAS,IACT1B,aAAc,KACdN,SAAU,IACViC,QAAS,KACT7B,SAAU,KACV8B,eAAgB,CAAC,EACjBC,YAAa,GACbC,QAAS,GACT1f,QAAS,GACTwd,cAAc,KACdC,UAAU,CAAC,IAEbI,gBAAgB,CACZI,SAAS,GACTF,kBAAkB,GAClBC,gBAAgB,IAEpBE,WAAY,CACVE,QAAS,CACP,CAAEL,kBAAmB,GAAIC,gBAAiB,GAAIC,SAAU,MAG5D5B,YAAa,CACX5X,KAAM,CAAC,CAAE4C,UAAU,EAAMsN,QAAS,QAAS5O,QAAS,SACpD0X,UAAW,CAAC,CAAEpW,UAAU,EAAMsN,QAAS,UAAW5O,QAAS,UAG7D+X,qBAAsB,CACpBG,SAAU,CAAC,CAAE5W,UAAU,EAAMsN,QAAS,UAAW5O,QAAS,SAC1DgY,kBAAmB,CAAC,CAAE1W,UAAU,EAAMsN,QAAS,SAAU5O,QAAS,SAClEiY,gBAAiB,CAAC,CAAE3W,UAAU,EAAMsN,QAAS,QAAS5O,QAAS,UAEjE+Y,YAAY,EACZE,WAAW,EACXN,WAAY,GACZiB,iBAAkB,GAClBC,UAAU,GACVzB,gBAAiB,CAAC,EAEtB,EACAjJ,SAAU,KACLC,EAAAA,EAAAA,IAAS,KACPA,EAAAA,EAAAA,IAAS,CAAC,aACb0K,OAAQlN,GAASA,EAAMkN,OACvB7D,IAAKrJ,GAASA,EAAMqJ,MAEtB5G,QAAAA,GACE,OAAOC,OAAOC,eAAeC,QAAQ,WACvC,EACA4H,gBAAiB,CACf2C,GAAAA,GACE,OAAO3M,KAAKiJ,WAAWkD,QAAQ7f,UACjC,EACAsgB,GAAAA,CAAIrb,GACFyO,KAAKiJ,WAAWkD,QAAUvG,OAAOrU,EACnC,GAEF2Y,mBAAoB,CAClByC,GAAAA,GACE,OAAO3M,KAAKiJ,WAAWwB,aAAane,UACtC,EACAsgB,GAAAA,CAAIrb,GACFyO,KAAKiJ,WAAWwB,aAAelZ,CACjC,GAEF0Y,kBAAmB,CACjB0C,GAAAA,GACE,OAAO3M,KAAKiJ,WAAWmD,QAAQ9f,UACjC,EACAsgB,GAAAA,CAAIrb,GACFyO,KAAKiJ,WAAWmD,QAAUxG,OAAOrU,EACnC,GAEF6Y,eAAgB,CACduC,GAAAA,GACE,OAAO3M,KAAKiJ,WAAWoB,cAAc/d,UACvC,EACAsgB,GAAAA,CAAIrb,GACFyO,KAAKiJ,WAAWoB,cAAgB9Y,CAClC,IAIJsb,OAAAA,GACE7M,KAAKiJ,WAAW5c,SAAW2T,KAAK9T,SAASG,SACzC2T,KAAK8M,UACP,EACA/H,MAAO,CACL,2BAA2BgI,GAEvB/M,KAAKiJ,WAAWqB,UADF,OAAZyC,EAC0B,CAAC/M,KAAKiJ,WAAWqB,UAAU,GAAItK,KAAKiJ,WAAWqB,UAAU,IAEzD,CAACtK,KAAKiJ,WAAWqB,UAAU,GAE3D,GAEFjI,QAAS,CACP5M,WAAAA,CAAYuX,GACVhN,KAAK2L,WAAaqB,EAClBhN,KAAK2L,YAAa,CACpB,EAEAK,aAAAA,CAAc/c,GACZ+Q,KAAKiJ,WAAaha,EAClB,MAAMge,EAAcjN,KAAKiJ,WAAWqD,YACpCtM,KAAKyM,UAAYzM,KAAKuL,WAAW2B,OAAO/b,GAAQ8b,EAAY9G,SAAShV,EAAKnB,KACvC,OAA/BgQ,KAAKiJ,WAAWwB,aAClBzK,KAAK0K,gBAAkBzb,EAAKod,eAEU,OAA/BrM,KAAKiJ,WAAWwB,eACvBzK,KAAK+K,WAAa9b,EAAKod,gBAEzBrM,KAAKuC,UAAU,KACJvC,KAAKwC,MAAM2K,YACVnN,KAAKyM,UAAU3F,QAAQsG,IACnBpN,KAAKwC,MAAM2K,YAAYE,mBAAmBD,GAAK,KAGnD5E,QAAQ8E,MAAM,6BAG5B,EAEA,mBAAMC,GACL,MAAMjJ,QAAgBtE,KAAKuE,KAAKgJ,cAC5B,CACEC,WAAYxN,KAAK6I,IAAI7Y,GACrByd,WAAW,EACXrF,KAAMpI,KAAK9T,SAAS8D,KAEH,MAAnBsU,EAAStG,QACPsG,EAASrV,KAAK8R,OAAOmC,OAAO,GAC7BlD,KAAKgM,cAAc1H,EAASrV,KAAK8R,OAAO,GAG9C,EAEAyJ,aAAAA,CAAc/e,GACD,OAAPA,EACJiiB,WAAW,KACT1N,KAAKuC,UAAU,KACNvC,KAAKwC,MAAM2K,YACVnN,KAAKyM,UAAU3F,QAAQsG,IACnBpN,KAAKwC,MAAM2K,YAAYE,mBAAmBD,GAAK,KAGnD5E,QAAQ8E,MAAM,+BAGxB,KAEc,OAAP7hB,EACPuU,KAAKiJ,WAAWqD,YAActM,KAAKwM,iBAAiBtE,IAAIkF,GAAOA,EAAIpd,IAGnEgQ,KAAK8D,SAAS,CACZtC,QAAS,UACT/V,KAAM,WAGZ,EAEAud,YAAAA,GACEhJ,KAAK2L,YAAa,CACpB,EAEAlC,OAAAA,GACEzJ,KAAKqJ,aAAc,CACrB,EACAQ,gBAAAA,CAAiB8D,GACf3N,KAAKqJ,YAAcsE,CACrB,EACA5D,WAAAA,CAAY6D,GACV5N,KAAKiJ,WAAWM,KAAOqE,CACzB,EAEA,mBAAMC,GACJ,MAAMvJ,QAAiBtE,KAAKuE,KAAKuJ,WAAW9N,KAAK6I,IAAI7Y,GAAI,GACjC,MAApBsU,EAAStG,SACXgC,KAAKuL,WAAajH,EAASrV,KAAK8R,OAC9Bf,KAAKwM,iBAAmBxM,KAAKuL,WAAW2B,OAAO/b,IAA8B,IAAtBA,EAAK4c,cAC5D/N,KAAKyM,UAAYzM,KAAKwM,iBAAiBtE,IAAIkF,GAAOA,EAAIpd,IAG5D,EACAyb,qBAAAA,CAAsBuC,GAEpBhO,KAAKiJ,WAAWqD,YAAc0B,EAAa9F,IAAIkF,GAAOA,EAAIpd,GAE5D,EAEAie,UAAAA,GACE,MAAM7N,EAAS,IAAIJ,KAAKiJ,YASxB,GARA7I,EAAOgI,KAAOpI,KAAK9T,SAAS8D,GAC5BoQ,EAAO/S,YAAc2S,KAAKhT,OAAO2W,UACjCvD,EAAOH,SAAWD,KAAKiC,SACvB7B,EAAOvT,QAAUmT,KAAKiC,gBACf7B,EAAOlT,mBACPkT,EAAOpQ,GACU,OAApBoQ,EAAO/T,iBAA0B+T,EAAOmJ,KAEhB,OAAxBnJ,EAAOqK,aAAuB,CAChCrK,EAAOiM,eAAiBrM,KAAK0K,gBAC7B,MAAM,QAAEO,KAAYiD,GAAS9N,EAAOiM,eACpCjM,EAAOiM,eAAiB6B,CAC1B,MAAO,GAA4B,OAAxB9N,EAAOqK,aAAuB,CACvCrK,EAAOiM,eAAiBrM,KAAK+K,WAC7B,SAAWmD,GAAS9N,EAAOiM,eAC3BjM,EAAOiM,eAAiB6B,CAC1B,CAGA,OAFA9N,EAAOmM,QAAUvM,KAAK6I,IAAI7Y,GAEnBoQ,CACT,EAEA,kBAAM2I,GACJ,MAAM3I,EAASJ,KAAKiO,aACd3J,QAAiBtE,KAAKuE,KAAK4J,cAAc/N,GACvB,MAApBkE,EAAStG,QACXgC,KAAK8D,SAAS,CACZtC,QAAS,OACT/V,KAAM,WAGZ,EAEA4f,SAAAA,GACErL,KAAK+K,WAAWE,QAAQtI,KAAK,CAC3BiI,kBAAmB,GACnBC,gBAAiB,GACjBC,SAAU,KAEZ9K,KAAK8M,UACP,EAEAA,QAAAA,GAEE,MAAMsB,EAAc,CAAC,EAErBpO,KAAK+K,WAAWE,QAAQnE,QAAQ,CAACuH,EAAGzG,KAClCwG,EAAY,WAAWxG,uBAA6B,CAClD,CAAE1T,UAAU,EAAMsN,QAAS,YAAa5O,QAAS,SAEnDwb,EAAY,WAAWxG,qBAA2B,CAChD,CAAE1T,UAAU,EAAMsN,QAAS,YAAa5O,QAAS,SAEnDwb,EAAY,WAAWxG,cAAoB,CACzC,CAAE1T,UAAU,EAAMsN,QAAS,aAAc5O,QAAS,WAKtDoN,KAAKgL,gBAAkBoD,CACzB,EAEAhD,YAAAA,CAAaxD,GACP5H,KAAK+K,WAAWE,QAAQ/H,OAAS,IACnClD,KAAK+K,WAAWE,QAAQrI,OAAOgF,EAAO,GACtC5H,KAAK8M,WAET,GAIJ1H,OAAAA,GACEpF,KAAK6N,gBACL7N,KAAKuN,eACP,GC7cA,MAAM,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,UNgSA,IACEtM,WAAY,CACVqN,SAAQ,GACRC,cAAa,GACbC,UAAS,aACT/I,KAAI,QACJgJ,WAAU,cACVjJ,KAAI,QACJkJ,KAAI,QACJC,OAAM,UACNC,cAAa,iBACbC,YAAW,eACXtN,QAAO,WACPuN,SAAQ,YACRC,UAAS,aACTrJ,OAAM,UACNsJ,UAASA,GAAAA,WAEX/f,IAAAA,GACE,MAAO,CACLoF,WAAY,GACZL,YAAa,GACbC,eAAe,EACfgB,gBAAgB,EAChBrB,MAAM,GACN9E,SAAU,GACV4F,UAAW,CACTpD,KAAO,GACP8W,KAAM,IAER6G,cAAc,KACd7Z,SAAS,CACPjJ,SAAU,GACVqB,KAAM,GACNH,YAAa,GACb4S,SAAW,IAEbzT,YAAa,CAAC,GAAM,OAAQ,GAAM,QAClCmQ,UAAU,EACVxO,WAAW,GACXe,UAAW,GACXoB,WAAW,GACX0B,QAAQ,CAAC,EACTP,SAAS,EACT6D,QAAO,EACPC,SAAU,GACVW,QAAQ,GACRJ,cAAc,CACR,eAAgB,GAEtBD,iBAAgB,EAGpB,EACAwM,QAAS,KACJ6M,EAAAA,EAAAA,IAAa,CAAC,YAAa,cAAe,YAAa,kBAE1DtjB,IAAAA,GACEsW,OAAOiN,QAAQvjB,OACfoU,KAAKoP,WACP,EAEA,eAAMC,CAAUC,GACf,MAAMhL,QAAiBtE,KAAKuE,KAAKgL,cAAcvP,KAAK9T,SAAS8D,GAAGsf,GACzC,MAAnBhL,EAAStG,SACdgC,KAAK9Q,UAAYoV,EAASrV,KACtBqV,EAASrV,KAAKiU,OAAO,IACvBlD,KAAKlR,SAAWwV,EAASrV,KAAK,GAAGe,GAC7BgQ,KAAK1Q,gBAAgBgV,EAASrV,KAAK,KAGzC,EAEA,cAAMqF,GACJ,MAAM8L,EAASJ,KAAKtL,UACd4P,QAAiBtE,KAAKuE,KAAKiL,gBAAgBpP,GACzB,MAApBkE,EAAStG,SACXgC,KAAK8D,SAAS,CACZrY,KAAM,UACN+V,QAAS,OACTuC,SAAU,MAEZ/D,KAAK7L,kBACL6L,KAAKqP,YAET,EACA,eAAM9a,GACJ,MAAM6L,EAASJ,KAAKtL,UACd4P,QAAiBtE,KAAKuE,KAAKkL,gBAAgBrP,EAAOpQ,GAAGoQ,GACnC,MAApBkE,EAAStG,SACXgC,KAAK8D,SAAS,CACZrY,KAAM,UACN+V,QAAS,OACTuC,SAAU,MAEZ/D,KAAK7L,kBACL6L,KAAKqP,YAET,EACAtf,QAAAA,CAASC,GACTgQ,KAAK0P,SAAS,qBAAsB,KAAM,CACxCC,kBAAmB,KACnBC,iBAAkB,KAClBnkB,KAAM,YAELokB,KAAKzL,UACJ,MAAME,QAAiBtE,KAAKuE,KAAKuL,gBAAgB9f,GAC3B,MAAnBsU,EAAStG,SACVgC,KAAK8D,SAAS,CACZrY,KAAM,UACN+V,QAAS,UAGXxB,KAAKqP,eAGRU,MAAM,KACL/P,KAAK8D,SAAS,CACZrY,KAAM,OACN+V,QAAS,WAGjB,EACE,cAAMvO,CAASxH,GAEb,OADAuU,KAAK1K,OAAS7J,EACNA,GACN,IAAK,OACHuU,KAAKzK,SAAW,WAChByK,KAAK9J,QAAUzK,EACf,MAAM6Y,QAAiBtE,KAAKuE,KAAKyL,aAAahQ,KAAKlR,UAC7B,MAAnBwV,EAAStG,SACVgC,KAAKpM,MAAQ0Q,EAASrV,MAExB,MAEF,IAAK,SACH+Q,KAAKzK,SAAW,OAChByK,KAAK9J,QAAUzK,EACf,MAEF,IAAK,QACHuU,KAAKzK,SAAW,OAChByK,KAAK9J,QAAUzK,EACf,MAEF,IAAK,OACHuU,KAAKzK,SAAW,OAChByK,KAAK9J,QAAUzK,EACf,MAGN,EAEAgK,WAAAA,GACEuK,KAAK1K,QAAS,EACd0K,KAAKlK,cAAcC,eAAgB,EACnCiK,KAAKlM,kBAAkBkM,KAAKlR,SAC9B,EAEA9C,KAAAA,CAAMP,EAAKwD,GAGT,OAFA+Q,KAAK3L,WAAa5I,EAEVA,GACN,IAAK,MACHuU,KAAK/L,eAAgB,EACrB+L,KAAKhM,YAAc,OACnBgM,KAAKtL,UAAU0T,KAAOpI,KAAK9T,SAAS8D,GACpC,MAEF,IAAK,OACHgQ,KAAK/L,eAAgB,EACrB+L,KAAKhM,YAAc,OACnBgM,KAAKtL,UAAY,IAAIzF,GACrB+Q,KAAKtL,UAAU1E,GAAKf,EAAKe,GACzB,MAEF,IAAK,YACHgQ,KAAK/K,gBAAiB,EACtB+K,KAAKhM,YAAc,OACnBgM,KAAK5K,SAASjJ,SAAW6T,KAAK9T,SAASC,SACvC6T,KAAK5K,SAAS5H,KAAOwS,KAAK9T,SAASsB,KACnC,MAEF,QACEwS,KAAKhM,YAAc,GACnB,MAEN,EAEAG,eAAAA,GACE6L,KAAK/L,eAAgB,EACrB+L,KAAK/K,gBAAiB,EACtB+K,KAAKtL,UAAY,CACfpD,KAAO,GACP8W,KAAM,IAERpI,KAAK5K,SAAW,CACdjJ,SAAU,GACVqB,KAAM,GACNH,YAAa,GACb4S,SAAW,GAEf,EAEA3Q,eAAAA,CAAgBL,GACd+Q,KAAKlR,SAAWG,EAAKe,GACrBgQ,KAAK1P,WAAarB,EAClB+Q,KAAKlM,kBAAkBkM,KAAKlR,SAC9B,EAEAL,WAAAA,GACEuR,KAAKqP,UAAUrP,KAAK7R,WACtB,EAEA2O,YAAAA,GACEkD,KAAKrD,UAAW,EAChBqD,KAAKuC,UAAU,KACbvC,KAAKwC,MAAM8F,MAAM5F,SAErB,EACA7F,aAAAA,GACAmD,KAAKrD,UAAW,CAClB,EACE,kBAAM/L,GAEP,MAAM0T,QAAiBtE,KAAKuE,KAAK0L,WAAWjQ,KAAK1P,WAAWC,IAAIyP,KAAK6I,IAAI7Y,IACjD,MAApBsU,EAAStG,SACZgC,KAAKhO,QAAUsS,EAASrV,MAEzB+Q,KAAKvO,SAAU,CAChB,EAEAM,OAAAA,CAAQC,GACPgO,KAAKvO,SAAU,EACfuO,KAAKkQ,cAAcle,GACnBgO,KAAKmQ,QAAQxN,KAAK,CAAErR,KAAM,WAC3B,EACE,cAAM6D,GACJ,MAAMiL,EAASJ,KAAK5K,SACpBgL,EAAOH,SAAWD,KAAKiC,SACvB7B,EAAO/S,YAAc2S,KAAKhT,OAAO2W,UACjC,MAAMW,QAAiBtE,KAAKuE,KAAK6L,sBAAsBpQ,KAAK9T,SAAS8D,GAAIoQ,GACjD,MAApBkE,EAAStG,SACXgC,KAAK8D,SAAS,CACZrY,KAAM,UACN+V,QAAS,OACTuC,SAAU,MAEZ/D,KAAKqQ,YAAY/L,EAASrV,MAC1B+Q,KAAK7L,kBAET,EAEA,uBAAML,CAAkBhF,GACtB,MAAMwV,QAAiBtE,KAAKuE,KAAK+L,iBAAiBxhB,GAC1B,MAApBwV,EAAStG,SACXgC,KAAKpM,MAAQ0Q,EAASrV,KAE1B,EACF,qBAAMsE,GACJ,MAAMgd,EAAQ,IAAIvQ,KAAK1P,YACpBigB,EAAMhgB,IACPggB,EAAMhgB,IAAM,CAACggB,EAAMhgB,YAEZggB,EAAMhgB,UAETyP,KAAKuE,KAAKkL,gBAAgBzP,KAAKlR,SAASyhB,GAC9C,MAAOjM,QAAiBtE,KAAKuE,KAAKiM,qBAAqBxQ,KAAKpM,OACpC,MAApB0Q,EAAStG,SACT6G,EAAAA,EAAAA,IAAe,CACXd,SAAU,IACVrS,MAAO,OACPjG,KAAM,WAGhB,EACA6K,YAAAA,CAAaC,EAAQ/G,GACnB,MAAMoY,EAAQrR,EAAOsR,WAAWhF,QAAQrT,GACxC,OAAOoY,EAAQ,CACjB,EAEAxR,iBAAAA,CAAkBqa,EAAMC,EAAMC,GACpB,IAAIC,EAAgB,EAChBC,EAAiB,EACjBC,GAAqB,EAEzB,IAAI,IAAIC,EAAE,EAAEA,EAAE/Q,KAAKpM,MAAMsP,OAAO6N,IAC4B,GAArD/Q,KAAKwC,MAAMwO,SAASC,QAAQjR,KAAKpM,MAAMmd,IAAI5F,WAC1C0F,GAAkB,GAEiC,GAApD7Q,KAAKwC,MAAMwO,SAASC,QAAQjR,KAAKpM,MAAMmd,IAAIG,UAC1CN,GAAiB,GAEwC,GAA1D5Q,KAAKwC,MAAMwO,SAASC,QAAQjR,KAAKpM,MAAMmd,IAAInb,gBAC1Ckb,GAAqB,GAIX,GAAfF,GACC5Q,KAAKnK,iBAAkB,EACvBmK,KAAKlK,cAAcC,eAAgB,EAEZ,GAApB+a,IACC9Q,KAAKnK,iBAAkB,EACvBmK,KAAKlK,cAAcC,eAAgB,IAIlC6a,EAAcC,GAAiB7Q,KAAKpM,MAAMsP,QAC/ClD,KAAKnK,iBAAkB,EACvBmK,KAAKlK,cAAcC,eAAgB,IAInCiK,KAAKnK,iBAAkB,EACvBmK,KAAKlK,cAAcC,eAAgB,EAK3C,EAENE,oBAAAA,CAAqBkb,GAEnB,GADAnR,KAAKnK,iBAAkB,EACnBsb,EACA,IAAK,IAAIJ,EAAI,EAAGA,EAAI/Q,KAAKpM,MAAMsP,OAAQ6N,IAC9B/Q,KAAKwC,MAAMwO,SAASC,QAAQjR,KAAKpM,MAAMmd,IAAI5F,UAC5CnL,KAAKwC,MAAMwO,SAASI,WAAWpR,KAAKpM,MAAMmd,GAAG/gB,IAAI,GAAM,QAI/DgQ,KAAKwC,MAAMwO,SAASK,eAAe,GAEzC,EAEA,aAAMza,CAAQnL,GACZ,MAAM2U,EAAS,CAAC3U,KAAKA,EAAKwD,KAAK+Q,KAAKwC,MAAMwO,SAASM,mBACnD,GAAW,SAAP7lB,EAAc,CAChB,MAAM6Y,QAAiBtE,KAAKuE,KAAKgN,mBAAmBnR,GAC1B,MAApBkE,EAAStG,UACX6G,EAAAA,EAAAA,IAAe,CACXd,SAAU,IACVrS,MAAO,SACPjG,KAAM,YAEVuU,KAAKvK,cACN,KACA,CACH,MAAM6O,QAAiBtE,KAAKuE,KAAKiN,iBAAiBxR,KAAKwC,MAAMwO,SAASM,mBAC9C,MAApBhN,EAAStG,UACX6G,EAAAA,EAAAA,IAAe,CACXd,SAAU,IACVrS,MAAO,SACPjG,KAAM,YAEVuU,KAAKvK,cAGT,CACF,EAEA,iBAAMhC,GACJ,IAAKuM,KAAKlR,SAMR,YALAkR,KAAK8D,SAAS,CACZrY,KAAM,UACN+V,QAAS,SACTuC,SAAU,MAKd,IAAK/D,KAAKpM,OAA+B,IAAtBoM,KAAKpM,MAAMsP,OAM5B,YALAlD,KAAK8D,SAAS,CACZrY,KAAM,UACN+V,QAAS,gBACTuC,SAAU,MAMd,MAAM0N,EAAmBzR,KAAK0R,mBACzBD,EAAiBE,QAmBpB3R,KAAK4R,iBAlBL5R,KAAK0P,SACH,cAAc+B,EAAiBI,OAAOC,KAAK,mBAC3C,OACA,CACEnC,kBAAmB,OACnBC,iBAAkB,KAClBnkB,KAAM,UACNsmB,YAAa,yBAEflC,KAAK,KACL7P,KAAK4R,mBACJ7B,MAAM,KACP/P,KAAK8D,SAAS,CACZrY,KAAM,OACN+V,QAAS,WAMjB,EAEAkQ,gBAAAA,GACE,MAAMG,EAAS,GACf,IAAIF,GAAU,EAuDd,OApDK3R,KAAK1P,WAAWC,MACnBshB,EAAOlP,KAAK,aACZgP,GAAU,GAIZ3R,KAAKpM,MAAMkT,QAAQ,CAACuB,EAAMT,KACxB,MAAMpR,EAAW6R,EAAK7R,SACtB,GAAKA,EAEL,OAAQA,EAAS/K,MACf,IAAK,MACE+K,EAAS9F,SAASgG,MACrBmb,EAAOlP,KAAK,OAAOiF,EAAQ,kBAC3B+J,GAAU,GAEPnb,EAAS9F,SAASwH,SACrB2Z,EAAOlP,KAAK,OAAOiF,EAAQ,mBAC3B+J,GAAU,GAEZ,MACF,IAAK,KACEnb,EAAS9F,SAASyH,UAAa3B,EAAS9F,SAAS0H,cAAiB5B,EAAS9F,SAASa,QACvFsgB,EAAOlP,KAAK,OAAOiF,EAAQ,iBAC3B+J,GAAU,GAEZ,MACF,IAAK,MAC8B,UAA7Bnb,EAAS9F,SAASkI,QAAuBpC,EAAS9F,SAASyI,aAC7D0Y,EAAOlP,KAAK,OAAOiF,EAAQ,kBAC3B+J,GAAU,GAEqB,QAA7Bnb,EAAS9F,SAASkI,QAAsBpC,EAAS9F,SAASyH,UAAa3B,EAAS9F,SAAS+I,eAC3FoY,EAAOlP,KAAK,OAAOiF,EAAQ,oBAC3B+J,GAAU,GAEZ,MACF,IAAK,SACEnb,EAASwG,QAAqC,KAA3BxG,EAASwG,OAAOgV,SACtCH,EAAOlP,KAAK,OAAOiF,EAAQ,gBAC3B+J,GAAU,GAEZ,MACF,IAAK,OACEnb,EAAS9F,SAASkN,OACrBiU,EAAOlP,KAAK,OAAOiF,EAAQ,kBAC3B+J,GAAU,GAEZ,SAIC,CAAEA,UAASE,SACpB,EAEAD,cAAAA,GAEE,MAAMK,EAAYjS,KAAKkS,iBAGvBlS,KAAKmS,OAAOF,EAAW,SAAU,CAC/BtC,kBAAmB,SACnBC,iBAAkB,KAClBwC,kBAAkB,EAClB3mB,KAAM,OACN4mB,0BAA0B,EAC1BN,YAAa,eACbngB,MAAO,QACNie,KAAK,KACN7P,KAAKsS,iBACJvC,MAAM,KACP/P,KAAK8D,SAAS,CACZrY,KAAM,OACN+V,QAAS,aAGf,EAEA0Q,cAAAA,GACE,MAAM3hB,EAAMyP,KAAK9O,SAASqhB,KAAK1O,GAAKA,EAAE7T,KAAOgQ,KAAK1P,WAAWC,KACvDiiB,EAAUjiB,EAAMA,EAAIe,KAAO,OAEjC,MAAO,gWAM+B0O,KAAK1P,WAAWgB,MAAQ,0DACxBkhB,oDACAxS,KAAK1P,WAAW8G,QAAU,oDAC1B4I,KAAKpM,MAAMsP,yDACXlD,KAAKpM,MAAMsZ,OAAOuF,GAAKA,EAAEjc,UAAUwH,QAAQkF,wSAQvElD,KAAKpM,MAAMsU,IAAI,CAACG,EAAMT,IAAU5H,KAAK0S,mBAAmBrK,EAAMT,IAAQkK,KAAK,iFAK/E9R,KAAK2S,4SAMD3S,KAAK4S,kFAKjB,EAEAF,kBAAAA,CAAmBrK,EAAMT,GACvB,MAAMpR,EAAW6R,EAAK7R,SACtB,IAAKA,EAAU,MAAO,GAEtB,MAAMqc,EAAarc,EAASwH,OAAS,IAAM,IACrC8U,EAAalL,EAAQ,EAE3B,IAAImL,EAAc,GAClB,OAAQvc,EAAS/K,MACf,IAAK,MACHsnB,EAAc,0GAEOvc,EAAS9F,SAASwH,QAAU,mDAC3B1B,EAAS9F,SAASgG,KAAO,4CAChCF,EAASY,QAAU,sCAGlC,MACF,IAAK,KACH2b,EAAc,0GAEOvc,EAAS9F,SAASyH,UAAY,MAAM3B,EAAS9F,SAAS0H,cAAgB,MAAM5B,EAAS9F,SAASa,OAAS,8CAG5H,MACF,IAAK,MACHwhB,EAAc,sGAEgC,UAA7Bvc,EAAS9F,SAASkI,OAAqB,OAAS,8BAC9B,UAA7BpC,EAAS9F,SAASkI,OAClB,cAAcpC,EAAS9F,SAASyI,YAAc,cAC9C,cAAc3C,EAAS9F,SAAS+I,cAAgB,SAASjD,EAAS9F,SAASyH,UAAY,sCAE5E3B,EAAS9F,SAAS2I,eAAiB,uCAGpD,MACF,IAAK,SACH0Z,EAAc,sGAEGvc,EAASwG,OAASxG,EAASwG,OAAOgW,MAAM,MAAM9P,OAAS,sCAGxE,MACF,IAAK,OACH6P,EAAc,sGAEGvc,EAAS9F,SAASkN,MAAQ,uCAG3C,MAGJ,MAAO,0GAC4FpH,EAASwH,OAAS,uBAAyB,gFAEtI6U,OAAgBC,MAAe9S,KAAKiT,gBAAgBzc,EAAS/K,WAAW+K,EAASlF,MAAQkF,EAAS9F,SAASY,MAAQ,kCAErHyhB,uBAGR,EAEAJ,oBAAAA,GACE,OAAK3S,KAAK1P,WAAWC,IAWd,sRAK0ByP,KAAK9O,SAASqhB,KAAK1O,GAAKA,EAAE7T,KAAOgQ,KAAK1P,WAAWC,MAAMe,MAAQ,uNAfvF,6UAwBX,EAEAshB,wBAAAA,GACE,MAAMM,EAAc,GAGdC,EAAgBnT,KAAKpM,MAAMsZ,OAAOuF,IAAMA,EAAEjc,UAAUwH,QAAQkF,OAC9DiQ,EAAgB,GAClBD,EAAYvQ,KAAK,KAAKwQ,uBAIxB,MAAMC,EAAWpT,KAAKpM,MAAMsZ,OAAOuF,GAA0B,QAArBA,EAAEjc,UAAU/K,MAAgByX,OAChEkQ,EAAW,GACbF,EAAYvQ,KAAK,MAAMyQ,2BAIzB,MAAMC,EAAYrT,KAAKpM,MAAMsZ,OAAOuF,GAA0B,QAArBA,EAAEjc,UAAU/K,MAAgByX,OACjEmQ,EAAY,GACdH,EAAYvQ,KAAK,MAAM0Q,2BAIzB,MAAMC,EAActT,KAAKpM,MAAMsZ,OAAOuF,GAA0B,WAArBA,EAAEjc,UAAU/K,MAAmByX,OAS1E,OARIoQ,EAAc,GAChBJ,EAAYvQ,KAAK,MAAM2Q,qBAGE,IAAvBJ,EAAYhQ,QACdgQ,EAAYvQ,KAAK,iBAGZuQ,EAAYhL,IAAIuK,GAAK,KAAKA,KAAKX,KAAK,OAC7C,EAEAmB,eAAAA,CAAgBxnB,GACd,MAAM8nB,EAAY,CAChB,IAAO,SACP,GAAM,QACN,IAAO,QACP,OAAU,QACV,GAAM,SACN,KAAQ,SAEV,OAAOA,EAAU9nB,IAAS,MAC5B,EAEA,kBAAM6mB,GACJ,IACEtS,KAAK8D,SAAS,CACZrY,KAAM,OACN+V,QAAS,cACTuC,SAAU,MAKZ,MAAM3D,EAAS,CACboT,SAAUxT,KAAKlR,SACf2kB,OAAQzT,KAAK1P,WAAWC,IACxBmjB,YAAY,GAGRpP,QAAiBtE,KAAKuE,KAAKoP,cAAcvT,GAEvB,MAApBkE,EAAStG,QAEXgC,KAAK4T,iBAAiBtP,EAASrV,KAEnC,CAAE,MAAOqe,GACP9E,QAAQ8E,MAAM,UAAWA,GACzBtN,KAAK8D,SAAS,CACZrY,KAAM,QACN+V,QAAS,YAAc8L,EAAMhJ,UAAUrV,MAAMuS,SAAW8L,EAAM9L,SAAW,QACzEuC,SAAU,KAEd,CACF,EAEA6P,gBAAAA,CAAiBC,GAEf,MAAMC,EAAU,CAEdC,YAAaF,EAAaE,aAAeF,EAAaG,YAAc,EACpEC,iBAAkBJ,EAAaI,kBAAoBJ,EAAaK,iBAAmB,EACnFC,aAAcN,EAAaM,cAAgBN,EAAaO,aAAe,EACvEC,eAAgBR,EAAaQ,gBAAkBR,EAAaS,eAAiB,EAC7EC,aAAcV,EAAaU,cAAgBV,EAAaW,aAAe,EAEvEC,aAAcZ,EAAaa,eAAeD,cAAgB,GAC1DE,cAAed,EAAaa,eAAeC,eAAiB,GAC5DC,eAAgBf,EAAae,gBAAkBf,EAAagB,eAAiB,WAGzEC,EAAc9U,KAAK+U,kBAAkBjB,GAE3C9T,KAAKmS,OAAO2C,EAAa,SAAU,CACjCnF,kBAAmB,KACnBlkB,KAAM,OACN4mB,0BAA0B,EAC1BN,YAAa,uBACbngB,MAAO,OAEX,EAEAmjB,iBAAAA,CAAkBjB,GAChB,OAAKA,EAIE,sdAMsEA,EAAQC,aAAeD,EAAQE,YAAc,0RAI7CF,EAAQG,kBAAoBH,EAAQI,iBAAmBJ,EAAQkB,eAAiB,yRAIhFlB,EAAQK,cAAgBL,EAAQM,aAAe,0RAI+B,KAA5EN,EAAQO,gBAAkBP,EAAQQ,eAAiBR,EAAQ/P,UAAY,IAAWkR,QAAQ,kJAMnKnB,EAAQW,aAAezU,KAAKkV,iBAAiBpB,EAAQW,cAAgB,iBAErEX,EAAQqB,KAAOnV,KAAKoV,eAAetB,EAAQqB,MAAQ,yBA7BhD,4DAgCX,EAEAD,gBAAAA,CAAiBG,GACf,MAAO,iNAICA,EAAYnN,IAAI,CAACnH,EAAQ6G,IAAU,+HAC+F,YAAlB7G,EAAO/C,OAAuB,UAAY,oGAElI,YAAlB+C,EAAO/C,OAAuB,IAAwB,WAAlB+C,EAAO/C,OAAsB,IAAM,UAAU4J,EAAQ,MAAM7G,EAAOuU,WAAa,uHAGrD,KAAjDvU,EAAOsT,gBAAkBtT,EAAOgD,UAAY,IAAWkR,QAAQ,wCACnElU,EAAO/C,QAAU,+BAC1B+C,EAAOS,QAAU,YAAYT,EAAOS,gBAAkB,uBACtDT,EAAOwU,SAAWxU,EAAOwU,QAAQC,YAAc,aAAazU,EAAOwU,QAAQC,oBAAsB,uBAC/E,WAAlBzU,EAAO/C,QAAuB+C,EAAOS,QAAU,oCAAoCT,EAAOS,gBAAkB,4DAGjHsQ,KAAK,yCAIhB,EAEAsD,cAAAA,CAAeD,GACb,MAAO,4TAICA,EAAKjN,IAAIO,GAAO,qCAAqCA,EAAIgN,cAAchN,EAAIiN,UAAUjN,EAAIjH,iBAAiBsQ,KAAK,yCAIzH,EAEA,cAAMpkB,GACJ,IAAKsS,KAAK2E,MAMR,YALA3E,KAAK8D,SAAS,CACZrY,KAAM,UACN+V,QAAS,aACTuC,SAAU,MAId,MAAM3D,EAAS,CAAEuV,OAAQ3V,KAAK9T,SAAS8D,GAAIO,IAAKyP,KAAK2E,OAC/CL,QAAiBtE,KAAKuE,KAAKqR,QAAQ5V,KAAK9T,SAAS8D,GAAIoQ,GAEnC,MAApBkE,EAAStG,SACX6G,EAAAA,EAAAA,IAAe,CACbnT,MAAO,QACP8P,QAAS,cACT/V,KAAM,UACNsY,SAAU,IACV8R,WAAW,EACXC,SAAU,aAGhB,GAIA/T,SAAU,KACLC,EAAAA,EAAAA,IAAS,CAAC,MAAM,WAAW,QAAQ,aACtC5S,YAAAA,GACE,MAAO,CACLyX,SAAU,WACVnX,MAAO,OAEX,EACAqmB,eAAgB,CACdpJ,GAAAA,GACE,OAAO3M,KAAKyB,KAAKpV,SAASC,UAC5B,EACAsgB,GAAAA,CAAIrb,GACFyO,KAAKyB,KAAKpV,SAAWkF,CACvB,GAEF0Q,QAAAA,GACD,OAAOC,OAAOC,eAAeC,QAAQ,WACtC,GAGAyK,OAAAA,GAEA,EAEA9H,MAAO,CAEP,EACDK,OAAAA,GACGpF,KAAKqP,YACL3B,WAAW,KACP1N,KAAKlM,kBAAkBkM,KAAKlR,WAC7B,IACL,GOvnCF,MAAM,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASknB,GAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/maskMgrDetail.vue", "webpack://frontend-web/./src/views/PerformanceTest/perfStep.vue", "webpack://frontend-web/./src/views/PerformanceTest/editApiDlg.vue", "webpack://frontend-web/./src/views/PerformanceTest/editApiDlg.vue?26ea", "webpack://frontend-web/./src/views/PerformanceTest/perfStep.vue?5668", "webpack://frontend-web/./src/views/PerformanceTest/maskMgrDetail_set.vue", "webpack://frontend-web/./src/views/PerformanceTest/maskMgrDetail_set.vue?45ff", "webpack://frontend-web/./src/views/PerformanceTest/maskMgrDetail.vue?3778"], "sourcesContent": ["<template>\r\n  <div class=\"box\">\r\n    <el-card class=\"task-card\">\r\n      <div class=\"task-header\">\r\n        <div class=\"task-info\">\r\n          <div class=\"task-navigation\">\r\n            <el-button class=\"back-button\" type=\"text\" @click=\"back\">\r\n              <el-icon><CaretLeft /></el-icon>返回\r\n            </el-button>\r\n            <div class=\"task-title\">\r\n              <span class=\"title-label\">任务管理 /</span>\r\n              <el-button\r\n                class=\"task-name-button\"\r\n                type=\"text\"\r\n                @click=\"popup('task-edit')\"\r\n                @click.stop\r\n              >\r\n                {{ perfTask.taskName }}\r\n                <el-icon class=\"edit-icon\"><Edit /></el-icon>\r\n              </el-button>\r\n              <el-button class=\"task-type-tag\" :type=\"perfTask.taskType.toString() === '20' ? 'warning' : 'primary'\">\r\n                {{ taskTypeMap[perfTask.taskType.toString()] || perfTask.taskType }}\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"task-details\">\r\n            <div class=\"detail-item\">\r\n              <span class=\"detail-label\">创建人:</span>\r\n              <span class=\"detail-value\">{{perfTask.creator}}</span>\r\n            </div>\r\n            <div class=\"detail-item\">\r\n              <span class=\"detail-label\">创建时间:</span>\r\n              <span class=\"detail-value\">{{ $tools.rTime(perfTask.create_time) }}</span>\r\n            </div>\r\n            <div class=\"detail-item\">\r\n              <span class=\"detail-label\">最后修改:</span>\r\n              <span class=\"detail-value\">{{ $tools.rTime(perfTask.update_time) }}</span>\r\n            </div>\r\n            <div class=\"detail-item description\">\r\n              <span class=\"detail-label\">任务描述:</span>\r\n              <span class=\"detail-value\">{{perfTask.desc || '暂无描述'}}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"task-actions\">\r\n          <el-button @click=\"clickRun\" type=\"success\" class=\"run-task-button\">\r\n            <el-icon><CaretRight /></el-icon>执行任务\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n    <el-row :gutter=\"2\" style=\" justify-content: flex-end;\">\r\n      <el-col :span=\"4\">\r\n        <div class=\"tree-component\">\r\n          <div class=\"search-container\">\r\n            <el-input v-model=\"filterText\" placeholder=\"请输入场景名称搜索\" clearable>\r\n              <template #append>\r\n                <el-button type=\"primary\" @click=\"searchClick\">查询</el-button>\r\n              </template>\r\n            </el-input>\r\n          </div>\r\n          <el-button\r\n            type=\"primary\"\r\n            class=\"add-scene-button\"\r\n            @click=\"popup('add')\"\r\n          >\r\n            <el-icon><Plus /></el-icon>添加场景\r\n          </el-button>\r\n          <el-scrollbar height=\"calc(100vh - 265px)\">\r\n            <el-tree\r\n              v-if=\"scenceId\"\r\n              node-key=\"id\"\r\n              :current-node-key=\"scenceId\"\r\n              class=\"filter-tree\"\r\n              :data=\"sceneList\"\r\n              :props=\"defaultProps\"\r\n              default-expand-all\r\n              :expand-on-click-node=\"false\"\r\n              @node-click=\"handleNodeClick\"\r\n            >\r\n              <template #default=\"{ node, data }\">\r\n                <el-scrollbar>\r\n                  <span class=\"bold-node\">\r\n                    {{ node.label }}\r\n                  </span>\r\n                </el-scrollbar>\r\n                <div class=\"node-content\">\r\n                  <span class=\"tree-actions\">\r\n                    <el-button type=\"primary\" size=\"small\" circle class=\"tree-action-btn edit-btn\" @click=\"popup('edit',node.data)\">\r\n                      <el-icon><Edit /></el-icon>\r\n                    </el-button>\r\n                    <el-button type=\"danger\" size=\"small\" circle class=\"tree-action-btn delete-btn\" @click=\"delScene(node.data.id)\">\r\n                      <el-icon><Delete /></el-icon>\r\n                    </el-button>\r\n                  </span>\r\n                </div>\r\n              </template>\r\n            </el-tree>\r\n          </el-scrollbar>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"14\" style=\"background:#ffffff;\">\r\n        <div  class=\"title\">\r\n          <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n            <span >场景步骤</span>\r\n            <div class=\"action-buttons-group\">\r\n                <div class=\"env-selector\">\r\n                  <el-tooltip v-if=\"scenceData.env\" class=\"box-item\" effect=\"dark\" content=\"查看环境信息\" placement=\"top\">\r\n                    <el-button  class=\"icon-button\" @click=\"clickShowEnv\">\r\n                      <el-icon><View /></el-icon>\r\n                    </el-button>\r\n                  </el-tooltip>\r\n                  <el-select clearable v-model=\"scenceData.env\" placeholder=\"选择环境\" style=\"width: 180px;\" no-data-text=\"暂无数据\" size=\"small\" class=\"env-select\">\r\n                    <el-option v-for=\"item in testEnvs\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n                  </el-select>\r\n                  <el-dialog v-model=\"showEnv\" title=\"环境变量\" top=\"10px\" width=\"70%\">\r\n                  <el-descriptions border :column=\"1\" label-align>\r\n                    <el-descriptions-item :label=\"key\" v-for=\"(value, key) in envInfo.debug_global_variable\">\r\n                      <template #label>\r\n                        <el-tag color=\"#E6A23C\">debug</el-tag>\r\n                        {{ key }}\r\n                      </template>\r\n                      {{ value }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item :label=\"key\" v-for=\"(value, key) in envInfo.global_variable\">\r\n                      <template #label>\r\n                        <el-tag color=\"#67C23AFF\">global</el-tag>\r\n                        {{ key }}\r\n                      </template>\r\n                      {{ value }}\r\n                    </el-descriptions-item>\r\n                  </el-descriptions>\r\n                  <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                      <el-button @click=\"editEnv(envInfo)\" type=\"success\" plain>编辑</el-button>\r\n                      <el-button @click=\"showEnv = false\">关闭</el-button>\r\n                    </span>\r\n                  </template>\r\n                </el-dialog>\r\n                </div>\r\n                <div class=\"operation-buttons\">\r\n                  <el-dropdown trigger=\"click\" placement=\"bottom-end\">\r\n                    <el-button type=\"info\" size=\"small\" class=\"action-button batch-button\" style=\"margin-right: 12px\">\r\n                      <span>批量操作</span>\r\n                      <el-icon class=\"el-icon--right\"><arrow-down /></el-icon>\r\n                    </el-button>\r\n                    <template #dropdown>\r\n                      <el-dropdown-menu>\r\n                        <el-dropdown-item command=\"批量禁用\" @click=\"clickOts('stop')\">\r\n                          <el-icon><Remove /></el-icon>\r\n                          批量禁用\r\n                        </el-dropdown-item>\r\n                        <el-dropdown-item command=\"批量启用\" @click=\"clickOts('start')\">\r\n                          <el-icon><SuccessFilled /></el-icon>\r\n                          批量启用\r\n                        </el-dropdown-item>\r\n                        <el-dropdown-item command=\"批量删除\" @click=\"clickOts('delete')\">\r\n                          <el-icon><CircleClose /></el-icon>\r\n                          批量删除\r\n                        </el-dropdown-item>\r\n                      </el-dropdown-menu>\r\n                    </template>\r\n                  </el-dropdown>\r\n                  <el-button type=\"warning\" size=\"small\" class=\"action-button\" @click=\"clickOts('sync')\">\r\n                    <el-icon><Refresh /></el-icon>\r\n                    <span>同步接口</span>\r\n                  </el-button>\r\n                  <el-button type=\"primary\" size=\"small\" class=\"action-button\" @click=\"clickScenceStep\">\r\n                    <el-icon><Document /></el-icon>\r\n                    <span>保存</span>\r\n                  </el-button>\r\n                  <el-button type=\"success\" size=\"small\" class=\"action-button\" @click=\"debugScence\">\r\n                    <el-icon><VideoPlay /></el-icon>\r\n                    <span>调试</span>\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n          </div>\r\n        </div>\r\n        <perfStep :scenceId=\"scenceId\" :steps=\"steps\" :scenceData=\"scenceData\" @fetch-steps=\"getTaskScenceStep\"></perfStep>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <configuration></configuration>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n  <!--  新增/修改场景弹窗-->\r\n  <el-dialog  :title=\"dialogTitle\" v-model=\"dialogVisible\"  width=\"30%\" custom-class=\"class_dialog\" :required=\"true\" style=\"text-align:left\" :before-close=\"clearValidation\">\r\n    <el-form :model=\"sceneForm\" :rules=\"rulesPerf\" ref=\"perfRef\">\r\n      <el-form-item label=\"场景名称\"  prop=\"name\" >\r\n        <el-input v-model=\"sceneForm.name\"  maxlength=\"50\" placeholder=\"请输入场景名称\"/>\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n\t\t\t<span class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"clearValidation\" >取消</el-button>\r\n        <el-button v-if=\"dialogType==='add'\" type=\"primary\" @click=\"addScene\" >确定</el-button>\r\n\t\t\t\t<el-button v-else type=\"primary\" @click=\"editScene\" >确定</el-button>\r\n\t\t\t</span>\r\n\t\t</template>\r\n  </el-dialog>\r\n  <!--  修改任务名称弹窗-->\r\n  <el-dialog  :title=\"dialogTitle\" v-model=\"dialogVisible1\"  width=\"30%\" custom-class=\"class_dialog\" :required=\"true\" style=\"text-align:left\" :before-close=\"clearValidation\">\r\n    <el-form :model=\"taskForm\" :rules=\"rulesPerf\" ref=\"perfRef\">\r\n      <el-form-item label=\"任务名称\"  prop=\"taskName\" >\r\n        <el-input v-model=\"taskForm.taskName\"  maxlength=\"50\" placeholder=\"请输入任务名称\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"任务描述\" prop=\"desc\">\r\n        <el-input type=\"textarea\" v-model=\"taskForm.desc\" placeholder=\"请输入备注\"/>\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n\t\t\t<span class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"clearValidation\" >取消</el-button>\r\n\t\t\t\t<el-button  type=\"primary\" @click=\"editTask\" >确定</el-button>\r\n\t\t\t</span>\r\n\t\t</template>\r\n  </el-dialog>\r\n  <!--  批量同步、批量禁用/启用、批量删除弹窗-->\r\n  <el-drawer v-model=\"otsDlg\"  :title=\"titleOts\" :destroy-on-close=\"true\" :show-close=\"false\" @close=\"handleClose\" size=\"23%\">\r\n    <template #default>\r\n\t\t\t<el-tabs type=\"card\" style=\"margin-left: 10px\">\r\n        <div>\r\n          <el-checkbox size=\"mini\" :indeterminate=\"isIndeterminate\"  v-model=\"new_task_form.case_checkAll\" @change=\"handleCheckAllChange\" style=\"padding:0px;margin-right:5px;\">全选</el-checkbox>\r\n        </div>\r\n        <el-scrollbar height=\"calc(100vh - 160px)\">\r\n\t\t\t\t\t<el-tree\r\n              v-if=\"typeOts !== 'sync'\"\r\n              ref=\"casetree\"\r\n              :data=\"steps\"\r\n              show-checkbox\r\n              :props=\"defaultProps\"\r\n              @check-change=\"case_check_change\"\r\n              node-key=\"id\"\r\n              :default-expand-all=\"false\"\r\n              highlight-current\r\n              empty-text=\"暂无数据\">\r\n\t\t\t\t\t\t<template #default=\"{ node, data }\">\r\n\t\t\t\t\t\t\t<span class=\"custom-tree-node\">\r\n\t\t\t\t\t\t\t\t<div>\r\n                  <el-icon class=\"step-icon\" style=\"color: #909399\">{{ getCardIndex(node.parent, node) }}</el-icon>\r\n                  {{ data.stepInfo.name }}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</el-tree>\r\n          <el-tree\r\n              v-else\r\n              ref=\"casetree\"\r\n              :data=\"steps\"\r\n              show-checkbox\r\n              :props=\"defaultProps\"\r\n              @check-change=\"case_check_change\"\r\n              node-key=\"id\"\r\n              :default-expand-all=\"false\"\r\n              highlight-current\r\n              empty-text=\"暂无数据\">\r\n\t\t\t\t\t\t<template #default=\"{ node, data }\">\r\n\t\t\t\t\t\t\t<span class=\"custom-tree-node\">\r\n\t\t\t\t\t\t\t\t<div>\r\n                  <el-icon class=\"step-icon\" style=\"color: #909399\">{{ getCardIndex(node.parent, node) }}</el-icon>\r\n                   {{ data.content?.name || '未定义' }}  {{ data.content?.url || '未定义' }}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</el-tree>\r\n        </el-scrollbar>\r\n\t\t\t</el-tabs>\r\n\t\t\t<div class=\"add-btns\">\r\n        <el-button v-if=\"typeOts==='start'\" class=\"drawer-action-btn enable-btn\" size=\"default\" @click=\"makeOts(typeOts)\">\r\n          <el-icon><SuccessFilled /></el-icon>确认启用\r\n        </el-button>\r\n        <el-button v-if=\"typeOts==='stop'\" class=\"drawer-action-btn disable-btn\" size=\"default\" @click=\"makeOts(typeOts)\">\r\n          <el-icon><Remove /></el-icon>确认禁用\r\n        </el-button>\r\n        <el-button v-if=\"typeOts==='delete'\" class=\"drawer-action-btn delete-btn\" size=\"default\" @click=\"makeOts(typeOts)\">\r\n          <el-icon><CircleClose /></el-icon>确认删除\r\n        </el-button>\r\n        <el-button v-if=\"typeOts==='sync'\" class=\"drawer-action-btn sync-btn\" size=\"default\" @click=\"makeOts(typeOts)\">\r\n          <el-icon><Refresh /></el-icon>确认同步\r\n        </el-button>\r\n        <el-button class=\"drawer-action-btn cancel-btn\" size=\"default\" @click=\"handleClose\">\r\n          <el-icon><CircleClose /></el-icon>关闭窗口\r\n        </el-button>\r\n\t\t\t</div>\r\n\t\t</template>\r\n  </el-drawer>\r\n</template>\r\n\r\n<script >\r\nimport {mapMutations, mapState} from \"vuex\";\r\nimport {ElNotification} from \"element-plus\";\r\nimport perfStep from './perfStep.vue'\r\nimport configuration from './maskMgrDetail_set.vue'\r\nimport {ElMessage, ElMessageBox} from \"element-plus\";\r\nimport { CaretLeft, Edit, CaretRight, Plus, View, Remove, SuccessFilled,\r\n  CircleClose, Refresh, Document, VideoPlay, Delete, ArrowDown } from '@element-plus/icons-vue';\r\nexport default {\r\n  components: {\r\n    perfStep,\r\n    configuration,\r\n    CaretLeft,\r\n    Edit,\r\n    CaretRight,\r\n    Plus,\r\n    View,\r\n    Remove,\r\n    SuccessFilled,\r\n    CircleClose,\r\n    Refresh,\r\n    Document,\r\n    VideoPlay,\r\n    Delete,\r\n    ArrowDown\r\n  },\r\n  data() {\r\n    return {\r\n      dialogType: '',\r\n      dialogTitle: '',\r\n      dialogVisible: false,\r\n      dialogVisible1: false,\r\n      steps:[],\r\n      scenceId: '',\r\n      sceneForm: {\r\n        name : '',\r\n        task :'',\r\n      },\r\n      importSetData:null,\r\n      taskForm:{\r\n        taskName: '',\r\n        desc: '',\r\n        update_time: '',\r\n        modifier : '',\r\n      },\r\n      taskTypeMap: {'10': '普通任务', '20': '定时任务'},\r\n      inputDlg: false,\r\n      filterText:'',\r\n      sceneList: [],\r\n      scenceData:'',\r\n      envInfo:{},\r\n      showEnv: false,\r\n      otsDlg:false,\r\n      titleOts: '',\r\n      typeOts:'',\r\n      new_task_form:{\r\n            \"case_checkAll\":false\r\n        },\r\n      isIndeterminate:false,\r\n\r\n    };\r\n  },\r\n \tmethods: {\r\n    ...mapMutations(['clearTask', 'checkedTask', 'selectEnv', 'selectEnvInfo']),\r\n\r\n    back() {\r\n      window.history.back();\r\n      this.clearTask()\r\n    },\r\n\r\n    async getScenes(query) {\r\n     const response = await this.$api.getTaskScenes(this.perfTask.id,query)\r\n     if (response.status ===200){\r\n\t\t\t\tthis.sceneList = response.data;\r\n\t\t\t\tif (response.data.length>0){\r\n\t\t\t\t  this.scenceId = response.data[0].id\r\n          this.handleNodeClick(response.data[0])\r\n        }\r\n\t\t\t}\r\n    },\r\n\r\n    async addScene() {\r\n      const params = this.sceneForm\r\n      const response = await this.$api.createTaskScene(params);\r\n      if (response.status === 201) {\r\n        this.$message({\r\n          type: 'success',\r\n          message: '添加成功',\r\n          duration: 1000\r\n        });\r\n        this.clearValidation();\r\n        this.getScenes()\r\n      }\r\n    },\r\n    async editScene() {\r\n      const params = this.sceneForm\r\n      const response = await this.$api.updateTaskScene(params.id,params);\r\n      if (response.status === 200) {\r\n        this.$message({\r\n          type: 'success',\r\n          message: '编辑成功',\r\n          duration: 1000\r\n        });\r\n        this.clearValidation();\r\n        this.getScenes()\r\n      }\r\n    },\r\n    delScene(id) {\r\n    this.$confirm('此操作将永久删除该场景, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning',\r\n    })\r\n      .then(async () => {\r\n        const response = await this.$api.deleteTaskScene(id)\r\n        if(response.status ===204){\r\n          this.$message({\r\n            type: 'success',\r\n            message: '删除成功!'\r\n          });\r\n          // 刷新页面\r\n          this.getScenes();\r\n        }\r\n      })\r\n      .catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        });\r\n      });\r\n  },\r\n    async clickOts(type) {\r\n      this.otsDlg = type;\r\n      switch (type){\r\n        case 'sync':\r\n          this.titleOts = '批量同步接口数据';\r\n          this.typeOts = type;\r\n          const response = await this.$api.getSceneStep(this.scenceId)\r\n          if(response.status ===200){\r\n            this.steps = response.data;\r\n          }\r\n          break;\r\n\r\n        case 'delete':\r\n          this.titleOts = '批量删除';\r\n          this.typeOts = type;\r\n          break;\r\n\r\n        case 'start':\r\n          this.titleOts = '批量启用';\r\n          this.typeOts = type;\r\n          break;\r\n\r\n        case 'stop':\r\n          this.titleOts = '批量禁用';\r\n          this.typeOts = type;\r\n          break;\r\n\r\n      };\r\n    },\r\n\r\n    handleClose(){\r\n      this.otsDlg = false;\r\n      this.new_task_form.case_checkAll = false;\r\n      this.getTaskScenceStep(this.scenceId);\r\n    },\r\n\r\n    popup(type,data) {\r\n      this.dialogType = type;\r\n      // 根据不同的对话框类型设置标题\r\n      switch (type) {\r\n        case 'add':\r\n          this.dialogVisible = true;\r\n          this.dialogTitle = '新增场景';\r\n          this.sceneForm.task = this.perfTask.id;\r\n          break;\r\n\r\n        case 'edit':\r\n          this.dialogVisible = true;\r\n          this.dialogTitle = '编辑场景';\r\n          this.sceneForm = {...data};\r\n          this.sceneForm.id = data.id\r\n          break;\r\n\r\n        case 'task-edit':\r\n          this.dialogVisible1 = true;\r\n          this.dialogTitle = '编辑任务';\r\n          this.taskForm.taskName = this.perfTask.taskName;\r\n          this.taskForm.desc = this.perfTask.desc;\r\n          break;\r\n\r\n        default:\r\n          this.dialogTitle = '';\r\n          break;\r\n      }\r\n    },\r\n\r\n    clearValidation() {\r\n      this.dialogVisible = false;\r\n      this.dialogVisible1 = false;\r\n      this.sceneForm = {\r\n        name : '',\r\n        task :'',\r\n      };\r\n      this.taskForm = {\r\n        taskName: '',\r\n        desc: '',\r\n        update_time: '',\r\n        modifier : ''\r\n      }\r\n    },\r\n\r\n    handleNodeClick(data) {\r\n      this.scenceId = data.id;\r\n      this.scenceData = data\r\n      this.getTaskScenceStep(this.scenceId)\r\n    },\r\n\r\n    searchClick() {\r\n      this.getScenes(this.filterText)\r\n    },\r\n\r\n    startEditing() {\r\n      this.inputDlg = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.input.focus();\r\n      })\r\n    },\r\n    cancelEditing() {\r\n    this.inputDlg = false;\r\n  },\r\n    async clickShowEnv() {\r\n\t\t\t// 获取单个环境信息\r\n\t\t\tconst response = await this.$api.getEnvInfo(this.scenceData.env,this.pro.id);\r\n\t\t\tif (response.status === 200) {\r\n\t\t\t\tthis.envInfo = response.data;\r\n\t\t\t}\r\n\t\t\tthis.showEnv = true;\r\n\t\t},\r\n\r\n\t\teditEnv(envInfo) {\r\n\t\t\tthis.showEnv = false;\r\n\t\t\tthis.selectEnvInfo(envInfo);\r\n\t\t\tthis.$router.push({ name: 'testenv' });\r\n\t\t},\r\n    async editTask() {\r\n      const params = this.taskForm\r\n      params.modifier = this.username;\r\n      params.update_time = this.$tools.newTime()\r\n      const response = await this.$api.updatePerformanceTask(this.perfTask.id, params);\r\n      if (response.status === 200) {\r\n        this.$message({\r\n          type: 'success',\r\n          message: '编辑成功',\r\n          duration: 1000\r\n        });\r\n        this.checkedTask(response.data)\r\n        this.clearValidation();\r\n      }\r\n    },\r\n\r\n    async getTaskScenceStep(scenceId) {\r\n      const response = await this.$api.getTaskSceneStep(scenceId);\r\n      if (response.status === 200) {\r\n        this.steps = response.data;\r\n      }\r\n    },\r\n  async clickScenceStep(){\r\n    const prams = {...this.scenceData}\r\n    if(prams.env){\r\n      prams.env = [prams.env]\r\n    }else {\r\n      delete prams.env\r\n    }\r\n    await this.$api.updateTaskScene(this.scenceId,prams)\r\n    const  response = await this.$api.batchUpdateSceneStep(this.steps)\r\n    if (response.status === 200) {\r\n        ElNotification({\r\n            duration: 500,\r\n            title: '保存成功',\r\n            type: 'success',\r\n          });\r\n    }\r\n  },\r\n  getCardIndex(parent, node) {\r\n    const index = parent.childNodes.indexOf(node);\r\n    return index + 1;\r\n  },\r\n\r\n  case_check_change(node1,node2,node3){//树节点check事件\r\n            let checked_count = 0;//被勾选上的一级节点个数\r\n            let disabled_count = 0;//置灰的一级节点个数\r\n            let indeterminate_flag = false;//有没有一级节点处于半选状态\r\n            //遍历所有一级节点\r\n            for(let i=0;i<this.steps.length;i++){\r\n                if(this.$refs.casetree.getNode(this.steps[i]).disabled==true){\r\n                    disabled_count += 1;//如果有置灰的节点，置灰变量加1\r\n                }\r\n                if(this.$refs.casetree.getNode(this.steps[i]).checked==true){\r\n                    checked_count += 1;//如果有勾选的节点，勾选变量加1\r\n                }\r\n                if(this.$refs.casetree.getNode(this.steps[i]).indeterminate==true){\r\n                    indeterminate_flag = true;//如果有半选的节点，半选变量设为true\r\n                }\r\n            }\r\n\r\n            if(checked_count==0){\r\n                this.isIndeterminate = false;\r\n                this.new_task_form.case_checkAll = false;//如果勾选的一级节点数为0，则设置全选按钮样式不为半选样式，全选的值为false\r\n\r\n                if(indeterminate_flag==true){//如果下面有半选的，设置全选按钮的样式为半选样式\r\n                    this.isIndeterminate = true;\r\n                    this.new_task_form.case_checkAll = false;\r\n                }\r\n\r\n            }\r\n            else if((checked_count+disabled_count)==this.steps.length){//如果树上勾上的和置灰的加起来等于tree上data的长度，设置全选按钮样式不为半选样式，全选值为true\r\n                this.isIndeterminate = false;\r\n                this.new_task_form.case_checkAll = true;\r\n\r\n            }\r\n            else{//上面条件不满足，则说明没有全部勾上，设置样式为半选，全选值为false\r\n                this.isIndeterminate = true;\r\n                this.new_task_form.case_checkAll = false;\r\n\r\n            }\r\n            return;\r\n\r\n        },\r\n\r\n  handleCheckAllChange(val) {\r\n    this.isIndeterminate = false; // 设置全选按钮样式不为半选\r\n    if (val) { // 当前值是全选\r\n        for (let i = 0; i < this.steps.length; i++) {\r\n            if (!this.$refs.casetree.getNode(this.steps[i]).disabled) {\r\n                this.$refs.casetree.setChecked(this.steps[i].id, true, true);\r\n            }\r\n        }\r\n    } else { // 当前值不是全选\r\n        this.$refs.casetree.setCheckedKeys([]); // 清空勾选状态\r\n    }\r\n  },\r\n\r\n  async makeOts(type){\r\n    const params = {type:type,data:this.$refs.casetree.getCheckedNodes()}\r\n    if (type!=='sync'){\r\n      const response = await this.$api.batchTaskSceneStep(params);\r\n        if (response.status === 200) {\r\n          ElNotification({\r\n              duration: 500,\r\n              title: '批量变更成功',\r\n              type: 'success',\r\n            });\r\n          this.handleClose();\r\n        }}\r\n    else {\r\n      const response = await this.$api.batchSaveApiStep(this.$refs.casetree.getCheckedNodes());\r\n      if (response.status === 200) {\r\n        ElNotification({\r\n            duration: 500,\r\n            title: '批量同步成功',\r\n            type: 'success',\r\n          });\r\n        this.handleClose();\r\n      }\r\n\r\n    }\r\n  },\r\n\r\n  async debugScence(){\r\n    if (!this.scenceId) {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '请先选择场景',\r\n        duration: 2000\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (!this.steps || this.steps.length === 0) {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '当前场景没有步骤，无法调试',\r\n        duration: 2000\r\n      });\r\n      return;\r\n    }\r\n\r\n    // 验证场景配置\r\n    const validationResult = this.validateScenario();\r\n    if (!validationResult.isValid) {\r\n      this.$confirm(\r\n        `场景配置存在问题：\\n${validationResult.errors.join('\\n')}\\n\\n是否继续调试？`,\r\n        '场景验证',\r\n        {\r\n          confirmButtonText: '继续调试',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          customClass: 'debug-confirm-dialog'\r\n        }\r\n      ).then(() => {\r\n        this.startDebugMode();\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消调试'\r\n        });\r\n      });\r\n    } else {\r\n      this.startDebugMode();\r\n    }\r\n  },\r\n\r\n  validateScenario() {\r\n    const errors = [];\r\n    let isValid = true;\r\n\r\n    // 检查环境配置\r\n    if (!this.scenceData.env) {\r\n      errors.push('• 未选择测试环境');\r\n      isValid = false;\r\n    }\r\n\r\n    // 检查步骤配置\r\n    this.steps.forEach((step, index) => {\r\n      const stepInfo = step.stepInfo;\r\n      if (!stepInfo) return;\r\n\r\n      switch (stepInfo.type) {\r\n        case 'api':\r\n          if (!stepInfo.content?.url) {\r\n            errors.push(`• 步骤${index + 1}: HTTP请求缺少URL`);\r\n            isValid = false;\r\n          }\r\n          if (!stepInfo.content?.method) {\r\n            errors.push(`• 步骤${index + 1}: HTTP请求缺少请求方法`);\r\n            isValid = false;\r\n          }\r\n          break;\r\n        case 'if':\r\n          if (!stepInfo.content?.variable || !stepInfo.content?.JudgmentMode || !stepInfo.content?.value) {\r\n            errors.push(`• 步骤${index + 1}: 条件控制器配置不完整`);\r\n            isValid = false;\r\n          }\r\n          break;\r\n        case 'for':\r\n          if (stepInfo.content?.select === 'count' && !stepInfo.content?.cycleIndex) {\r\n            errors.push(`• 步骤${index + 1}: 循环控制器缺少循环次数`);\r\n            isValid = false;\r\n          }\r\n          if (stepInfo.content?.select === 'for' && (!stepInfo.content?.variable || !stepInfo.content?.variableName)) {\r\n            errors.push(`• 步骤${index + 1}: for循环控制器配置不完整`);\r\n            isValid = false;\r\n          }\r\n          break;\r\n        case 'script':\r\n          if (!stepInfo.script || stepInfo.script.trim() === '') {\r\n            errors.push(`• 步骤${index + 1}: 自定义脚本内容为空`);\r\n            isValid = false;\r\n          }\r\n          break;\r\n        case 'time':\r\n          if (!stepInfo.content?.time) {\r\n            errors.push(`• 步骤${index + 1}: 等待控制器缺少等待时间`);\r\n            isValid = false;\r\n          }\r\n          break;\r\n      }\r\n    });\r\n\r\n    return { isValid, errors };\r\n  },\r\n\r\n  startDebugMode() {\r\n    // 构建调试信息\r\n    const debugInfo = this.buildDebugInfo();\r\n\r\n    // 显示调试对话框\r\n    this.$alert(debugInfo, '场景调试信息', {\r\n      confirmButtonText: '开始执行调试',\r\n      cancelButtonText: '关闭',\r\n      showCancelButton: true,\r\n      type: 'info',\r\n      dangerouslyUseHTMLString: true,\r\n      customClass: 'debug-dialog',\r\n      width: '80%'\r\n    }).then(() => {\r\n      this.executeDebug();\r\n    }).catch(() => {\r\n      this.$message({\r\n        type: 'info',\r\n        message: '已关闭调试窗口'\r\n      });\r\n    });\r\n  },\r\n\r\n  buildDebugInfo() {\r\n    const env = this.testEnvs.find(e => e.id === this.scenceData.env);\r\n    const envName = env ? env.name : '未知环境';\r\n\r\n    return `\r\n      <div style=\"max-height: 500px; overflow-y: auto; text-align: left;\">\r\n        <!-- 场景基本信息 -->\r\n        <div style=\"margin-bottom: 20px;\">\r\n          <h3 style=\"color: #409eff; margin-bottom: 10px;\">🎯 场景基本信息</h3>\r\n          <div style=\"background: #f8f9fa; padding: 15px; border-radius: 6px;\">\r\n            <div><strong>场景名称:</strong> ${this.scenceData.name || '未命名场景'}</div>\r\n            <div><strong>测试环境:</strong> ${envName}</div>\r\n            <div><strong>场景权重:</strong> ${this.scenceData.weight || 1}</div>\r\n            <div><strong>步骤数量:</strong> ${this.steps.length}</div>\r\n            <div><strong>启用步骤:</strong> ${this.steps.filter(s => s.stepInfo?.status).length}</div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 执行步骤预览 -->\r\n        <div style=\"margin-bottom: 20px;\">\r\n          <h3 style=\"color: #409eff; margin-bottom: 10px;\">📋 执行步骤预览</h3>\r\n          <div style=\"background: #f8f9fa; padding: 15px; border-radius: 6px;\">\r\n            ${this.steps.map((step, index) => this.buildStepDebugInfo(step, index)).join('')}\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 环境变量 -->\r\n        ${this.buildEnvironmentInfo()}\r\n\r\n        <!-- 调试建议 -->\r\n        <div style=\"margin-bottom: 20px;\">\r\n          <h3 style=\"color: #409eff; margin-bottom: 10px;\">💡 调试建议</h3>\r\n          <div style=\"background: #fff3cd; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;\">\r\n            ${this.generateDebugSuggestions()}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    `;\r\n  },\r\n\r\n  buildStepDebugInfo(step, index) {\r\n    const stepInfo = step.stepInfo;\r\n    if (!stepInfo) return '';\r\n\r\n    const statusIcon = stepInfo.status ? '✅' : '❌';\r\n    const stepNumber = index + 1;\r\n\r\n    let stepDetails = '';\r\n    switch (stepInfo.type) {\r\n      case 'api':\r\n        stepDetails = `\r\n          <div style=\"margin-left: 20px; font-size: 12px; color: #666;\">\r\n            <div>方法: <code>${stepInfo.content?.method || 'GET'}</code></div>\r\n            <div>URL: <code>${stepInfo.content?.url || '未设置'}</code></div>\r\n            <div>权重: ${stepInfo.weight || 1}</div>\r\n          </div>\r\n        `;\r\n        break;\r\n      case 'if':\r\n        stepDetails = `\r\n          <div style=\"margin-left: 20px; font-size: 12px; color: #666;\">\r\n            <div>条件: <code>${stepInfo.content?.variable || ''} ${stepInfo.content?.JudgmentMode || ''} ${stepInfo.content?.value || ''}</code></div>\r\n          </div>\r\n        `;\r\n        break;\r\n      case 'for':\r\n        stepDetails = `\r\n          <div style=\"margin-left: 20px; font-size: 12px; color: #666;\">\r\n            <div>循环类型: ${stepInfo.content?.select === 'count' ? '次数循环' : 'for循环'}</div>\r\n            ${stepInfo.content?.select === 'count' ?\r\n              `<div>循环次数: ${stepInfo.content?.cycleIndex || '未设置'}</div>` :\r\n              `<div>循环变量: ${stepInfo.content?.variableName || ''} in ${stepInfo.content?.variable || ''}</div>`\r\n            }\r\n            <div>循环间隔: ${stepInfo.content?.cycleInterval || 0}秒</div>\r\n          </div>\r\n        `;\r\n        break;\r\n      case 'script':\r\n        stepDetails = `\r\n          <div style=\"margin-left: 20px; font-size: 12px; color: #666;\">\r\n            <div>脚本行数: ${stepInfo.script ? stepInfo.script.split('\\n').length : 0}</div>\r\n          </div>\r\n        `;\r\n        break;\r\n      case 'time':\r\n        stepDetails = `\r\n          <div style=\"margin-left: 20px; font-size: 12px; color: #666;\">\r\n            <div>等待时间: ${stepInfo.content?.time || 0}秒</div>\r\n          </div>\r\n        `;\r\n        break;\r\n    }\r\n\r\n    return `\r\n      <div style=\"margin-bottom: 10px; padding: 10px; border: 1px solid #e4e7ed; border-radius: 4px; ${stepInfo.status ? 'background: #f0f9ff;' : 'background: #fff2f0;'}\">\r\n        <div style=\"font-weight: 500;\">\r\n          ${statusIcon} 步骤${stepNumber}: ${this.getStepTypeName(stepInfo.type)} - ${stepInfo.name || stepInfo.content?.name || '未命名'}\r\n        </div>\r\n        ${stepDetails}\r\n      </div>\r\n    `;\r\n  },\r\n\r\n  buildEnvironmentInfo() {\r\n    if (!this.scenceData.env) {\r\n      return `\r\n        <div style=\"margin-bottom: 20px;\">\r\n          <h3 style=\"color: #409eff; margin-bottom: 10px;\">🌍 环境变量</h3>\r\n          <div style=\"background: #fff2f0; padding: 15px; border-radius: 6px; border-left: 4px solid #f56c6c;\">\r\n            <div style=\"color: #f56c6c;\">⚠️ 未选择测试环境</div>\r\n          </div>\r\n        </div>\r\n      `;\r\n    }\r\n\r\n    return `\r\n      <div style=\"margin-bottom: 20px;\">\r\n        <h3 style=\"color: #409eff; margin-bottom: 10px;\">🌍 环境变量</h3>\r\n        <div style=\"background: #f8f9fa; padding: 15px; border-radius: 6px;\">\r\n          <div style=\"margin-bottom: 10px;\">\r\n            <strong>当前环境:</strong> ${this.testEnvs.find(e => e.id === this.scenceData.env)?.name || '未知'}\r\n          </div>\r\n          <div style=\"font-size: 12px; color: #666;\">\r\n            <div>调试将使用当前环境的配置变量</div>\r\n            <div>点击环境信息按钮可查看详细变量配置</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    `;\r\n  },\r\n\r\n  generateDebugSuggestions() {\r\n    const suggestions = [];\r\n\r\n    // 检查禁用的步骤\r\n    const disabledSteps = this.steps.filter(s => !s.stepInfo?.status).length;\r\n    if (disabledSteps > 0) {\r\n      suggestions.push(`有 ${disabledSteps} 个步骤被禁用，调试时将跳过这些步骤`);\r\n    }\r\n\r\n    // 检查API步骤\r\n    const apiSteps = this.steps.filter(s => s.stepInfo?.type === 'api').length;\r\n    if (apiSteps > 0) {\r\n      suggestions.push(`包含 ${apiSteps} 个HTTP请求，建议先测试单个接口的连通性`);\r\n    }\r\n\r\n    // 检查循环步骤\r\n    const loopSteps = this.steps.filter(s => s.stepInfo?.type === 'for').length;\r\n    if (loopSteps > 0) {\r\n      suggestions.push(`包含 ${loopSteps} 个循环控制器，注意循环次数设置避免过度执行`);\r\n    }\r\n\r\n    // 检查脚本步骤\r\n    const scriptSteps = this.steps.filter(s => s.stepInfo?.type === 'script').length;\r\n    if (scriptSteps > 0) {\r\n      suggestions.push(`包含 ${scriptSteps} 个自定义脚本，确保脚本语法正确`);\r\n    }\r\n\r\n    if (suggestions.length === 0) {\r\n      suggestions.push('场景配置良好，可以开始调试');\r\n    }\r\n\r\n    return suggestions.map(s => `• ${s}`).join('<br>');\r\n  },\r\n\r\n  getStepTypeName(type) {\r\n    const typeNames = {\r\n      'api': 'HTTP请求',\r\n      'if': '条件控制器',\r\n      'for': '循环控制器',\r\n      'script': '自定义脚本',\r\n      'py': '导入PY脚本',\r\n      'time': '等待控制器'\r\n    };\r\n    return typeNames[type] || '未知类型';\r\n  },\r\n\r\n  async executeDebug() {\r\n    try {\r\n      this.$message({\r\n        type: 'info',\r\n        message: '开始执行场景调试...',\r\n        duration: 2000\r\n      });\r\n\r\n\r\n      // 调用后端调试API\r\n      const params = {\r\n        scene_id: this.scenceId,\r\n        env_id: this.scenceData.env,\r\n        debug_mode: true\r\n      };\r\n\r\n      const response = await this.$api.debugScenario(params);\r\n\r\n      if (response.status === 200) {\r\n        // 显示调试结果\r\n        this.showDebugResults(response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('调试执行错误:', error);\r\n      this.$message({\r\n        type: 'error',\r\n        message: '调试执行失败: ' + (error.response?.data?.message || error.message || '未知错误'),\r\n        duration: 3000\r\n      });\r\n    }\r\n  },\r\n\r\n  showDebugResults(responseData) {\r\n    // 构建完整的结果对象，包含顶层统计数据\r\n    const results = {\r\n      // 从顶层获取统计数据（后端提供的兼容字段）\r\n      total_steps: responseData.total_steps || responseData.totalSteps || 0,\r\n      successful_steps: responseData.successful_steps || responseData.successfulSteps || 0,\r\n      failed_steps: responseData.failed_steps || responseData.failedSteps || 0,\r\n      execution_time: responseData.execution_time || responseData.executionTime || 0,\r\n      success_rate: responseData.success_rate || responseData.successRate || 0,\r\n      // 从debug_results中获取详细数据\r\n      step_results: responseData.debug_results?.step_results || [],\r\n      error_summary: responseData.debug_results?.error_summary || [],\r\n      overall_result: responseData.overall_result || responseData.overallResult || 'unknown'\r\n    };\r\n    \r\n    const resultsHtml = this.buildDebugResults(results);\r\n\r\n    this.$alert(resultsHtml, '调试执行结果', {\r\n      confirmButtonText: '关闭',\r\n      type: 'info',\r\n      dangerouslyUseHTMLString: true,\r\n      customClass: 'debug-results-dialog',\r\n      width: '85%'\r\n    });\r\n  },\r\n\r\n  buildDebugResults(results) {\r\n    if (!results) {\r\n      return '<div style=\"text-align: center; color: #999;\">暂无调试结果</div>';\r\n    }\r\n\r\n    return `\r\n      <div style=\"max-height: 600px; overflow-y: auto; text-align: left;\">\r\n        <div style=\"margin-bottom: 20px;\">\r\n          <h3 style=\"color: #409eff; margin-bottom: 10px;\">📊 执行概览</h3>\r\n          <div style=\"display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px;\">\r\n            <div style=\"background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;\">\r\n              <div style=\"font-size: 18px; font-weight: bold; color: #409eff;\">${results.total_steps || results.totalSteps || 0}</div>\r\n              <div style=\"font-size: 12px; color: #666;\">总步骤</div>\r\n            </div>\r\n            <div style=\"background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;\">\r\n              <div style=\"font-size: 18px; font-weight: bold; color: #67c23a;\">${results.successful_steps || results.successfulSteps || results.success_steps || 0}</div>\r\n              <div style=\"font-size: 12px; color: #666;\">成功</div>\r\n            </div>\r\n            <div style=\"background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;\">\r\n              <div style=\"font-size: 18px; font-weight: bold; color: #f56c6c;\">${results.failed_steps || results.failedSteps || 0}</div>\r\n              <div style=\"font-size: 12px; color: #666;\">失败</div>\r\n            </div>\r\n            <div style=\"background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;\">\r\n              <div style=\"font-size: 18px; font-weight: bold; color: #e6a23c;\">${((results.execution_time || results.executionTime || results.duration || 0) * 1000).toFixed(0)}ms</div>\r\n              <div style=\"font-size: 12px; color: #666;\">耗时</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        ${results.step_results ? this.buildStepResults(results.step_results) : ''}\r\n\r\n        ${results.logs ? this.buildDebugLogs(results.logs) : ''}\r\n      </div>\r\n    `;\r\n  },\r\n\r\n  buildStepResults(stepResults) {\r\n    return `\r\n      <div style=\"margin-bottom: 20px;\">\r\n        <h3 style=\"color: #409eff; margin-bottom: 10px;\">🎯 步骤执行结果</h3>\r\n        <div style=\"background: #f8f9fa; padding: 15px; border-radius: 6px;\">\r\n          ${stepResults.map((result, index) => `\r\n            <div style=\"margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid ${result.status === 'success' ? '#67c23a' : '#f56c6c'};\">\r\n              <div style=\"font-weight: 500; margin-bottom: 5px;\">\r\n                ${result.status === 'success' ? '✅' : result.status === 'failed' ? '❌' : '⏭️'} 步骤${index + 1}: ${result.step_name || '未命名'}\r\n              </div>\r\n              <div style=\"font-size: 12px; color: #666;\">\r\n                <div>执行时间: ${((result.execution_time || result.duration || 0) * 1000).toFixed(0)}ms</div>\r\n                <div>状态: ${result.status || '未知'}</div>\r\n                ${result.message ? `<div>信息: ${result.message}</div>` : ''}\r\n                ${result.details && result.details.status_code ? `<div>响应码: ${result.details.status_code}</div>` : ''}\r\n                ${result.status === 'failed' && result.message ? `<div style=\"color: #f56c6c;\">错误: ${result.message}</div>` : ''}\r\n              </div>\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      </div>\r\n    `;\r\n  },\r\n\r\n  buildDebugLogs(logs) {\r\n    return `\r\n      <div style=\"margin-bottom: 20px;\">\r\n        <h3 style=\"color: #409eff; margin-bottom: 10px;\">📝 执行日志</h3>\r\n        <div style=\"background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 12px; max-height: 300px; overflow-y: auto;\">\r\n          ${logs.map(log => `<div style=\"margin-bottom: 2px;\">[${log.timestamp}] ${log.level}: ${log.message}</div>`).join('')}\r\n        </div>\r\n      </div>\r\n    `;\r\n  },\r\n\r\n  async clickRun() {\r\n    if (!this.envId) {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '当前未选中执行环境!',\r\n        duration: 1000\r\n      });\r\n      return\r\n    }\r\n    const params = { taskId: this.perfTask.id, env: this.envId };\r\n    const response = await this.$api.runTask(this.perfTask.id, params);\r\n\r\n    if (response.status === 200) {\r\n      ElNotification({\r\n        title: '任务已启动',\r\n        message: '请前往报告列表查看结果',\r\n        type: 'success',\r\n        duration: 3000,\r\n        showClose: true,\r\n        position: 'top-right',\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n\t},\r\n  computed: {\r\n    ...mapState(['pro','perfTask','envId','testEnvs']),\r\n    defaultProps() {\r\n      return {\r\n        children: 'children',\r\n        label: 'name',\r\n      }\r\n    },\r\n    selectTaskType: {\r\n      get() {\r\n        return this.form.taskType.toString();\r\n      },\r\n      set(value) {\r\n        this.form.taskType = value;\r\n      }\r\n    },\r\n    username() {\r\n\t\t\treturn window.sessionStorage.getItem('username');\r\n\t\t},\r\n\t},\r\n\r\n  mounted() {\r\n\r\n  },\r\n\r\n  watch: {\r\n\r\n  },\r\n\tcreated() {\r\n    this.getScenes();\r\n    setTimeout(() => {\r\n        this.getTaskScenceStep(this.scenceId);\r\n    }, 500);\r\n  },\r\n\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.box{\r\n  padding:5px 5px 5px 5px;\r\n  background:#f5f7f9;\r\n}\r\n.el-icon-caret-right:before {\r\n    padding-right: 3px;\r\n}\r\n\r\n.projectInfo{\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n  display: flex;\r\n  height: 85px;\r\n}\r\n\r\n.task-button{\r\n  color:black;\r\n  border: none;\r\n  outline: none;\r\n  font-size: 17px;\r\n  padding-left: 5px;\r\n}\r\n.right-info {\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.tree-component {\r\n  padding: 10px;\r\n  box-shadow: 5px 0 5px rgba(0, 0, 0, 0.06); /* 添加此样式来设置阴影 */\r\n  background:#ffffff;\r\n}\r\n.filter-tree {\r\n  margin-top: 10px;\r\n  padding-right:0px;\r\n}\r\n.tree-component[data-v-1b4274da] {\r\n    width: 300px;\r\n    padding-right: 0px;\r\n    box-shadow: 5px 0 5px rgba(0, 0, 0, 0.06);\r\n}\r\n.node-content {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  font-size: 16px;\r\n\r\n}\r\n.el-icon {\r\n  margin-left: 10px;\r\n  transition: transform 0.3s ease;\r\n}\r\n.el-icon:hover {\r\n  transform: scale(1.2);\r\n}\r\n.bold-node {\r\n  font-weight: bold;\r\n}\r\n.el-tag {\r\n  color: #ffffff;\r\n  width: 80px;\r\n  height: 30px;\r\n  text-align: center;\r\n  font-size: 13px;\r\n  line-height: 30px;\r\n\r\n}\r\n\r\n.title {\r\n    padding: 9px;\r\n    border: 1px solid var(--el-card-border-color);\r\n    background-color: #fff;\r\n    overflow: hidden;\r\n}\r\n\r\n.el-dropdown-menu__item {\r\n  color: #606266;\r\n  &:hover {\r\n    background-color: #ebf5ff;\r\n  }\r\n}\r\n.add-btns {\r\n\ttext-align: center;\r\n}\r\n\r\n.step-icon {\r\n  display: inline-flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  margin-right: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 调试对话框样式 */\r\n:deep(.debug-dialog) {\r\n  .el-message-box {\r\n    width: 80% !important;\r\n    max-width: 1000px;\r\n  }\r\n\r\n  .el-message-box__content {\r\n    max-height: 600px;\r\n    overflow-y: auto;\r\n    padding: 20px !important;\r\n  }\r\n\r\n  .el-message-box__message {\r\n    margin: 0 !important;\r\n  }\r\n}\r\n\r\n:deep(.debug-confirm-dialog) {\r\n  .el-message-box {\r\n    width: 500px !important;\r\n  }\r\n\r\n  .el-message-box__message {\r\n    white-space: pre-line !important;\r\n    line-height: 1.6 !important;\r\n  }\r\n}\r\n\r\n:deep(.debug-results-dialog) {\r\n  .el-message-box {\r\n    width: 85% !important;\r\n    max-width: 1200px;\r\n  }\r\n\r\n  .el-message-box__content {\r\n    max-height: 700px;\r\n    overflow-y: auto;\r\n    padding: 20px !important;\r\n  }\r\n\r\n  .el-message-box__message {\r\n    margin: 0 !important;\r\n  }\r\n}\r\n\r\n/* 调试结果HTML内容样式 */\r\n:deep(.debug-dialog) h3,\r\n:deep(.debug-results-dialog) h3 {\r\n  margin: 0 0 10px 0 !important;\r\n  font-size: 16px !important;\r\n  font-weight: 600 !important;\r\n}\r\n\r\n:deep(.debug-dialog) code,\r\n:deep(.debug-results-dialog) code {\r\n  background: #f5f5f5 !important;\r\n  padding: 2px 4px !important;\r\n  border-radius: 3px !important;\r\n  font-family: 'Courier New', monospace !important;\r\n  font-size: 11px !important;\r\n}\r\n\r\n/* 操作按钮区域样式 */\r\n.action-buttons-group {\r\n  display: flex;\r\n  gap: 15px;\r\n  align-items: center;\r\n}\r\n\r\n.env-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 5px;\r\n}\r\n\r\n.icon-button {\r\n  margin-right: 5px;\r\n}\r\n\r\n\r\n.env-select :deep(.el-input__wrapper) {\r\n  box-shadow: 0 0 0 1px #dcdfe6 inset;\r\n  border-radius: 4px;\r\n}\r\n\r\n.env-select :deep(.el-input__wrapper:hover) {\r\n  box-shadow: 0 0 0 1px #c0c4cc inset;\r\n}\r\n\r\n.operation-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.action-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  border-radius: 4px;\r\n  padding: 8px 15px;\r\n  transition: all 0.3s;\r\n  border: none;\r\n  font-weight: 500;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.batch-button {\r\n  background: linear-gradient(to right, #909399, #a6a9ad);\r\n  color: white;\r\n}\r\n\r\n.action-button:nth-child(2) {\r\n  background: linear-gradient(to right, #e6a23c, #f0c78a);\r\n  color: white;\r\n}\r\n\r\n.action-button:nth-child(3) {\r\n  background: linear-gradient(to right, #409eff, #53a8ff);\r\n  color: white;\r\n}\r\n\r\n.action-button:nth-child(4) {\r\n  background: linear-gradient(to right, #67c23a, #85ce61);\r\n  color: white;\r\n}\r\n\r\n.action-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  filter: brightness(1.05);\r\n}\r\n\r\n.action-button:active {\r\n  transform: translateY(1px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.el-dropdown-menu__item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 10px 15px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.el-dropdown-menu__item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n/* 场景树操作按钮样式 */\r\n.tree-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.tree-action-btn {\r\n  padding: 4px;\r\n  transform: scale(0.8);\r\n  transition: all 0.3s;\r\n  opacity: 0.8;\r\n  border: none;\r\n}\r\n\r\n.edit-btn {\r\n  background: linear-gradient(to right, #409eff, #53a8ff);\r\n  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n.delete-btn {\r\n  background: linear-gradient(to right, #f56c6c, #f78989);\r\n  box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);\r\n}\r\n\r\n.tree-action-btn:hover {\r\n  transform: scale(0.9);\r\n  opacity: 1;\r\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.tree-action-btn .el-icon {\r\n  margin-left: 0;\r\n}\r\n\r\n/* 优化场景树样式 */\r\n.filter-tree {\r\n  margin-top: 10px;\r\n  padding-right: 0px;\r\n  font-size: 14px;\r\n}\r\n\r\n.filter-tree :deep(.el-tree-node__content) {\r\n  height: 36px;\r\n  border-radius: 4px;\r\n  margin-bottom: 3px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.filter-tree :deep(.el-tree-node__content:hover) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.filter-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.bold-node {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 150px;\r\n}\r\n\r\n/* 执行任务按钮样式 */\r\n.run-task-button {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  padding: 10px 20px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  background: linear-gradient(to right, #67c23a, #4CCB7E);\r\n  border: none;\r\n  box-shadow: 0 4px 12px rgba(76, 203, 126, 0.3);\r\n}\r\n\r\n.run-task-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 15px rgba(76, 203, 126, 0.4);\r\n}\r\n\r\n.run-task-button:active {\r\n  transform: translateY(1px);\r\n  box-shadow: 0 2px 8px rgba(76, 203, 126, 0.3);\r\n}\r\n\r\n/* 添加场景按钮样式 */\r\n.add-scene-button {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n  font-weight: 500;\r\n  background: linear-gradient(to right, #409eff, #53a8ff);\r\n  border: none;\r\n  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);\r\n  margin-bottom: 15px;\r\n  padding: 10px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.add-scene-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.3);\r\n  background: linear-gradient(to right, #53a8ff, #66b1ff);\r\n}\r\n\r\n.add-scene-button:active {\r\n  transform: translateY(1px);\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n.search-container {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.search-input :deep(.el-input__inner) {\r\n  border-radius: 4px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 8px 12px;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.search-input :deep(.el-input__inner:focus) {\r\n  border-color: #409eff;\r\n  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n.search-btn {\r\n  border-radius: 4px;\r\n  background: linear-gradient(to right, #409eff, #53a8ff);\r\n  border: none;\r\n  padding: 8px 12px;\r\n  font-size: 14px;\r\n  color: white;\r\n  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);\r\n  transition: all 0.3s;\r\n}\r\n\r\n.search-btn:hover {\r\n  background: linear-gradient(to right, #53a8ff, #66b1ff);\r\n  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n.search-btn:active {\r\n  transform: translateY(1px);\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n/* 批量操作弹窗按钮样式 */\r\n.add-btns {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 15px;\r\n  margin-top: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.drawer-action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n  transition: all 0.3s;\r\n  font-weight: 500;\r\n  border: none;\r\n  color: white;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.drawer-action-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  filter: brightness(1.05);\r\n}\r\n\r\n.drawer-action-btn:active {\r\n  transform: translateY(1px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.enable-btn {\r\n  background: linear-gradient(to right, #67c23a, #85ce61);\r\n}\r\n\r\n.disable-btn {\r\n  background: linear-gradient(to right, #909399, #a6a9ad);\r\n}\r\n\r\n.delete-btn {\r\n  background: linear-gradient(to right, #f56c6c, #f78989);\r\n}\r\n\r\n.sync-btn {\r\n  background: linear-gradient(to right, #e6a23c, #f0c78a);\r\n}\r\n\r\n.cancel-btn {\r\n  background: linear-gradient(to right, #909399, #a6a9ad);\r\n}\r\n\r\n/* 新增优化的任务卡片样式 */\r\n.task-card {\r\n  margin-bottom: 15px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.task-card:hover {\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.task-card :deep(.el-card__body) {\r\n  padding: 0;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 16px 20px;\r\n  background: linear-gradient(to right, #ffffff, #f9fafc);\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-info {\r\n  flex: 1;\r\n}\r\n\r\n.task-navigation {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.back-button {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 15px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  padding: 0;\r\n  margin-right: 10px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.back-button:hover {\r\n  color: #409EFF;\r\n  transform: translateX(-2px);\r\n}\r\n\r\n.back-button .el-icon {\r\n  margin-right: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.task-title {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.title-label {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-right: 8px;\r\n}\r\n\r\n.task-name-button {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  padding: 0;\r\n  margin-right: 10px;\r\n  transition: all 0.2s;\r\n  max-width: 300px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  text-align: left;\r\n}\r\n\r\n.task-name-button:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n.edit-icon {\r\n  margin-left: 6px;\r\n  font-size: 14px;\r\n  color: #409EFF;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.task-name-button:hover .edit-icon {\r\n  transform: scale(1.2);\r\n}\r\n\r\n.task-type-tag {\r\n  font-size: 12px;\r\n  padding: 0 10px;\r\n  height: 24px;\r\n  line-height: 24px;\r\n  border-radius: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.task-details {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-top: 6px;\r\n  padding-left: 2px;\r\n}\r\n\r\n.detail-item {\r\n  margin-right: 24px;\r\n  margin-bottom: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-label {\r\n  font-size: 13px;\r\n  color: #909399;\r\n  margin-right: 6px;\r\n}\r\n\r\n.detail-value {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.description {\r\n  flex-basis: 100%;\r\n  margin-top: 4px;\r\n}\r\n\r\n.description .detail-value {\r\n  color: #606266;\r\n  max-width: 500px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.task-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n</style>\r\n", "<template>\r\n  <div class=\"page-container\">\r\n    <div class=\"page-header-card\">\r\n      <div class=\"header-content\">\r\n        <div class=\"action-toolbar\">\r\n          <el-tag color=\"#61649f\" class=\"action-tag\" @click=\"clickApiDlg\">\r\n            <el-icon><Plus /></el-icon>HTTP请求\r\n          </el-tag>\r\n          <el-tag color=\"#E6A23C\" class=\"action-tag\" @click=\"addController([],'if')\">\r\n            <el-icon><Plus /></el-icon>条件控制器\r\n          </el-tag>\r\n          <el-tag color=\"#7B4D12FF\" class=\"action-tag\" @click=\"addController([],'script')\">\r\n            <el-icon><Plus /></el-icon>自定义脚本\r\n          </el-tag>\r\n          <el-tag color=\"#67C23AFF\" class=\"action-tag\" @click=\"addController([],'py')\">\r\n            <el-icon><Plus /></el-icon>导入PY脚本\r\n          </el-tag>\r\n        </div>\r\n        <div class=\"weight-settings\">\r\n          <el-tooltip content=\"场景运行权重设置，默认1设置后会影响运行权重，请谨慎设置！\" :enterable=\"false\" placement=\"top\">\r\n            <el-icon style=\"margin-right: 10px\"><QuestionFilled /></el-icon>\r\n          </el-tooltip>\r\n          <el-input-number size=\"small\" v-model=\"scenceData.weight\" :min=\"1\" :max=\"10\" @change=\"handleChange\">\r\n          </el-input-number>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-scrollbar height=\"calc(100vh - 220px)\">\r\n      <el-tree\r\n        :data=\"steps\"\r\n        :props=\"defaultProps\"\r\n        draggable\r\n        :default-expand-all=\"isExpand\"\r\n        :expand-on-click-node=\"false\"\r\n        @node-click=\"handleStepClick\"\r\n        :allow-drop=\"allowDrop\"\r\n        @node-drop=\"updateStepOrder\"\r\n        :node-drag-start=\"handleDragScroll\"\r\n        class=\"custom-tree\"\r\n      >\r\n        <template v-slot=\"{ node,data }\">\r\n          <el-card v-if=\"data.stepInfo\" :class=\"['step-card', `step-card-${data.stepInfo.type}`]\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <el-row :gutter=\"10\" type=\"flex\" align=\"middle\" justify=\"center\">\r\n                <el-col :span=\"20\" class=\"card-main-content\">\r\n                  <div class=\"card-content-wrapper\">\r\n                    <!--HTTP接口展示-->\r\n                    <div v-if=\"data.stepInfo.type==='api'\" class=\"card-inner\">\r\n                      <div class=\"card-left\">\r\n                        <span class=\"step-icon\">{{ getCardIndex(node.parent, node) }}</span>\r\n                        <el-tag color=\"#61649f\" class=\"step-tag\">HTTP请求</el-tag>\r\n                        <span class=\"method-tag\">\r\n                          <span v-if=\"data.stepInfo.content.method === 'POST'\">\r\n                            <b style=\"color: #49cc90;\">{{ data.stepInfo.content.method }}</b>\r\n                          </span>\r\n                          <span v-if=\"data.stepInfo.content.method === 'GET'\">\r\n                            <b style=\"color: #61affe;\">{{ data.stepInfo.content.method }}</b>\r\n                          </span>\r\n                          <span v-if=\"data.stepInfo.content.method === 'PUT'\">\r\n                            <b style=\"color: #fca130;\">{{ data.stepInfo.content.method }}</b>\r\n                          </span>\r\n                          <span v-if=\"data.stepInfo.content.method === 'PATCH'\">\r\n                            <b style=\"color: #50e3c2;\">{{ data.stepInfo.content.method }}</b>\r\n                          </span>\r\n                          <span v-if=\"data.stepInfo.content.method === 'DELETE'\">\r\n                            <b style=\"color: #f93e3e;\">{{ data.stepInfo.content.method }}</b>\r\n                          </span>\r\n                          <span v-if=\"data.stepInfo.content.method === 'DEAD'\">\r\n                            <b style=\"color: rgb(201, 233, 104);\">{{ data.stepInfo.content.method }}</b>\r\n                          </span>\r\n                        </span>\r\n                      </div>\r\n                      <div class=\"card-center\">\r\n                        <b class=\"card-url\">{{ data.stepInfo.content.url }}</b>\r\n                        <span class=\"card-name\">{{data.stepInfo.content.name }}</span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!--if控制器展示-->\r\n                    <div v-if=\"data.stepInfo.type==='if'\" class=\"card-inner\">\r\n                      <div class=\"card-left\">\r\n                        <span class=\"step-icon\">{{ getCardIndex(node.parent, node) }}</span>\r\n                        <el-tag color=\"rgb(230, 162, 60)\" class=\"step-tag\">条件控制器</el-tag>\r\n                      </div>\r\n                      <div class=\"card-center if-content\">\r\n                        <div class=\"if-controls-wrapper\">\r\n                          <el-input class=\"input-def\" placeholder=\"变量，例如{{name}}\" v-model=\"data.stepInfo.content.variable\"/>\r\n                          <el-select v-model=\"data.stepInfo.content.JudgmentMode\" placeholder=\"请选择\" class=\"judgment-select\">\r\n                            <el-option\r\n                              v-for=\"item in options\"\r\n                              :key=\"item.value\"\r\n                              :label=\"item.label\"\r\n                              :value=\"item.value\"\r\n                            />\r\n                          </el-select>\r\n                          <el-input class=\"input-def\" placeholder=\"值\" v-model=\"data.stepInfo.content.value\"/>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!--循环控制器展示-->\r\n                    <div v-if=\"data.stepInfo.type==='for'\" class=\"card-inner\">\r\n                      <div class=\"card-left\">\r\n                        <span class=\"step-icon\">{{ getCardIndex(node.parent, node) }}</span>\r\n                        <el-tag color=\"rgb(2, 167, 240)\" class=\"step-tag\">循环控制器</el-tag>\r\n                      </div>\r\n                      <div class=\"card-center\">\r\n                        <div class=\"for-controls-wrapper\">\r\n                          <el-radio-group v-model=\"data.stepInfo.content.select\" @click.stop class=\"radio-group\">\r\n                            <el-radio label=\"count\" value=\"count\">次数循环</el-radio>\r\n                            <el-radio label=\"for\" value=\"for\">for循环</el-radio>\r\n                            <el-radio label=\"while\" value=\"while\">while循环</el-radio>\r\n                          </el-radio-group>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div v-if=\"data.stepInfo.type==='for' && data.stepInfo.dlg\" class=\"loop-details\" @click.stop>\r\n                      <div v-if=\"data.stepInfo.content.select==='count' || data.stepInfo.content.select===''\">\r\n                        <div class=\"loop\">\r\n                          <div class=\"loop-control\">\r\n                            <span>循环次数</span>\r\n                            <el-input v-model=\"data.stepInfo.content.cycleIndex\" style=\"width: 200px\" placeholder=\"循环次数\" />\r\n                          </div>\r\n                          <div class=\"loop-control\">\r\n                            <span>循环间隔</span>\r\n                            <el-input-number\r\n                              v-model=\"data.stepInfo.content.cycleInterval\"\r\n                              :min=\"0\"\r\n                              :max=\"999\"\r\n                              size=\"small\"\r\n                              controls-position=\"right\"\r\n                              placeholder=\"秒\"\r\n                            />\r\n                            <span>秒</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div v-if=\"data.stepInfo.content.select==='for'\">\r\n                        <div class=\"loop\">\r\n                          <div class=\"loop-control\">\r\n                            <el-input style=\"width: 200px\" placeholder=\"定义变量名称\" v-model=\"data.stepInfo.content.variableName\"/>\r\n                            <b style=\"margin-left: 10px;margin-right: 10px\">in</b>\r\n                            <el-input style=\"width: 200px\" placeholder=\"变量，例如{{name}}\" v-model=\"data.stepInfo.content.variable\"/>\r\n                          </div>\r\n                          <div class=\"loop-control\">\r\n                            <span>循环间隔</span>\r\n                            <el-input-number\r\n                              v-model=\"data.stepInfo.content.cycleInterval\"\r\n                              :min=\"0\"\r\n                              :max=\"999\"\r\n                              size=\"small\"\r\n                              controls-position=\"right\"\r\n                              placeholder=\"秒\"\r\n                            />\r\n                            <span>秒</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div v-if=\"data.stepInfo.content.select==='while'\">\r\n                        <div class=\"loop\">\r\n                          <div class=\"while-loop-section\">\r\n                            <h4 style=\"margin: 0 0 10px 0; color: #409eff; font-size: 14px;\">循环条件设置</h4>\r\n\r\n                            <!-- 条件类型选择 -->\r\n                            <div class=\"loop-control\">\r\n                              <span style=\"min-width: 80px;\">条件类型</span>\r\n                              <el-select v-model=\"data.stepInfo.content.whileConditionType\" placeholder=\"选择条件类型\" style=\"width: 200px;\">\r\n                                <el-option label=\"变量比较\" value=\"variable\" />\r\n                                <el-option label=\"表达式\" value=\"expression\" />\r\n                                <el-option label=\"脚本函数\" value=\"function\" />\r\n                              </el-select>\r\n                            </div>\r\n\r\n                            <!-- 变量比较模式 -->\r\n                            <div v-if=\"data.stepInfo.content.whileConditionType === 'variable'\" class=\"condition-config\">\r\n                              <div class=\"loop-control\">\r\n                                <span>左操作数</span>\r\n                                <el-input style=\"width: 180px\" placeholder=\"变量，例如{{counter}}\" v-model=\"data.stepInfo.content.whileLeftOperand\"/>\r\n                              </div>\r\n                              <div class=\"loop-control\">\r\n                                <span>比较操作符</span>\r\n                                <el-select v-model=\"data.stepInfo.content.whileOperator\" placeholder=\"选择操作符\" style=\"width: 120px;\">\r\n                                  <el-option label=\"<\" value=\"lt\" />\r\n                                  <el-option label=\"<=\" value=\"lte\" />\r\n                                  <el-option label=\">\" value=\"gt\" />\r\n                                  <el-option label=\">=\" value=\"gte\" />\r\n                                  <el-option label=\"==\" value=\"eq\" />\r\n                                  <el-option label=\"!=\" value=\"ne\" />\r\n                                  <el-option label=\"包含\" value=\"contains\" />\r\n                                  <el-option label=\"不包含\" value=\"not_contains\" />\r\n                                </el-select>\r\n                              </div>\r\n                              <div class=\"loop-control\">\r\n                                <span>右操作数</span>\r\n                                <el-input style=\"width: 180px\" placeholder=\"值或变量，例如10或{{max}}\" v-model=\"data.stepInfo.content.whileRightOperand\"/>\r\n                              </div>\r\n                            </div>\r\n\r\n                            <!-- 表达式模式 -->\r\n                            <div v-if=\"data.stepInfo.content.whileConditionType === 'expression'\" class=\"condition-config\">\r\n                              <div class=\"loop-control\">\r\n                                <span>条件表达式</span>\r\n                                <el-input\r\n                                  style=\"width: 100%; max-width: 400px;\"\r\n                                  placeholder=\"例如: {{counter}} < 100 and {{status}} == 'running'\"\r\n                                  v-model=\"data.stepInfo.content.whileExpression\"\r\n                                  type=\"textarea\"\r\n                                  :rows=\"2\"\r\n                                />\r\n                              </div>\r\n                              <div class=\"expression-help\">\r\n                                <el-alert\r\n                                  title=\"表达式说明\"\r\n                                  type=\"info\"\r\n                                  show-icon\r\n                                  :closable=\"false\"\r\n                                  style=\"margin-top: 10px;\">\r\n                                  <template #default>\r\n                                    <div style=\"font-size: 12px;\">\r\n                                      <div>• 支持变量引用: {{variable_name}}</div>\r\n                                      <div>• 支持比较操作: <, <=, >, >=, ==, !=</div>\r\n                                      <div>• 支持逻辑操作: and, or, not</div>\r\n                                      <div>• 支持函数调用: len({{list_var}}), int({{str_var}})</div>\r\n                                    </div>\r\n                                  </template>\r\n                                </el-alert>\r\n                              </div>\r\n                            </div>\r\n\r\n                            <!-- 脚本函数模式 -->\r\n                            <div v-if=\"data.stepInfo.content.whileConditionType === 'function'\" class=\"condition-config\">\r\n                              <div class=\"loop-control\">\r\n                                <span>函数名称</span>\r\n                                <el-input style=\"width: 200px\" placeholder=\"例如: check_condition\" v-model=\"data.stepInfo.content.whileFunctionName\"/>\r\n                              </div>\r\n                              <div class=\"loop-control\">\r\n                                <span>函数参数</span>\r\n                                <el-input\r\n                                  style=\"width: 300px;\"\r\n                                  placeholder=\"例如: {{var1}}, {{var2}}, 'constant'\"\r\n                                  v-model=\"data.stepInfo.content.whileFunctionArgs\"\r\n                                />\r\n                              </div>\r\n                              <div class=\"function-help\">\r\n                                <el-alert\r\n                                  title=\"函数使用说明\"\r\n                                  type=\"warning\"\r\n                                  show-icon\r\n                                  :closable=\"false\"\r\n                                  style=\"margin-top: 10px;\">\r\n                                  <template #default>\r\n                                    <div style=\"font-size: 12px;\">\r\n                                      <div>• 函数必须返回布尔值 (True/False)</div>\r\n                                      <div>• 函数需要在全局作用域中定义</div>\r\n                                      <div>• 参数支持变量引用和常量值</div>\r\n                                    </div>\r\n                                  </template>\r\n                                </el-alert>\r\n                              </div>\r\n                            </div>\r\n\r\n                            <!-- 通用配置 -->\r\n                            <div class=\"while-common-config\">\r\n                              <h4 style=\"margin: 15px 0 10px 0; color: #409eff; font-size: 14px;\">循环控制设置</h4>\r\n\r\n                              <div class=\"loop-control\">\r\n                                <span>最大循环次数</span>\r\n                                <el-input-number\r\n                                  v-model=\"data.stepInfo.content.whileMaxIterations\"\r\n                                  :min=\"1\"\r\n                                  :max=\"10000\"\r\n                                  size=\"small\"\r\n                                  controls-position=\"right\"\r\n                                  placeholder=\"次\"\r\n                                  style=\"width: 150px;\"\r\n                                />\r\n                                <span style=\"margin-left: 10px; font-size: 12px; color: #666;\">防止无限循环</span>\r\n                              </div>\r\n\r\n                              <div class=\"loop-control\">\r\n                                <span>循环间隔</span>\r\n                                <el-input-number\r\n                                  v-model=\"data.stepInfo.content.cycleInterval\"\r\n                                  :min=\"0\"\r\n                                  :max=\"999\"\r\n                                  size=\"small\"\r\n                                  controls-position=\"right\"\r\n                                  placeholder=\"秒\"\r\n                                  style=\"width: 120px;\"\r\n                                />\r\n                                <span>秒</span>\r\n                              </div>\r\n\r\n                              <div class=\"loop-control\">\r\n                                <span>超时时间</span>\r\n                                <el-input-number\r\n                                  v-model=\"data.stepInfo.content.whileTimeout\"\r\n                                  :min=\"0\"\r\n                                  :max=\"3600\"\r\n                                  size=\"small\"\r\n                                  controls-position=\"right\"\r\n                                  placeholder=\"秒\"\r\n                                  style=\"width: 120px;\"\r\n                                />\r\n                                <span>秒 (0表示无超时)</span>\r\n                              </div>\r\n\r\n                              <!-- 循环变量配置 -->\r\n                              <div class=\"loop-control\">\r\n                                <span>循环计数器变量</span>\r\n                                <el-input\r\n                                  style=\"width: 200px\"\r\n                                  placeholder=\"例如: loop_counter\"\r\n                                  v-model=\"data.stepInfo.content.whileCounterVar\"\r\n                                />\r\n                                <span style=\"margin-left: 10px; font-size: 12px; color: #666;\">可在条件和子步骤中使用</span>\r\n                              </div>\r\n\r\n                              <!-- 高级选项 -->\r\n                              <div class=\"advanced-options\">\r\n                                <el-divider content-position=\"left\" style=\"margin: 15px 0 10px 0;\">\r\n                                  <span style=\"color: #909399; font-size: 12px;\">高级选项</span>\r\n                                </el-divider>\r\n\r\n                                <div class=\"loop-control\">\r\n                                  <el-checkbox v-model=\"data.stepInfo.content.whileBreakOnError\" style=\"margin-right: 20px;\">\r\n                                    遇到错误时终止循环\r\n                                  </el-checkbox>\r\n                                  <el-checkbox v-model=\"data.stepInfo.content.whileLogIterations\">\r\n                                    记录每次迭代日志\r\n                                  </el-checkbox>\r\n                                </div>\r\n\r\n                                <div class=\"loop-control\">\r\n                                  <span>条件检查时机</span>\r\n                                  <el-radio-group v-model=\"data.stepInfo.content.whileCheckTiming\" size=\"small\">\r\n                                    <el-radio label=\"before\">执行前检查</el-radio>\r\n                                    <el-radio label=\"after\">执行后检查</el-radio>\r\n                                  </el-radio-group>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!--自定义脚本展示-->\r\n                    <div v-if=\"data.stepInfo.type==='script'\" class=\"card-inner\">\r\n                      <div class=\"card-left\">\r\n                        <span class=\"step-icon\">{{ getCardIndex(node.parent, node) }}</span>\r\n                        <el-tag color=\"rgb(123, 77, 18)\" class=\"step-tag\">自定义脚本</el-tag>\r\n                      </div>\r\n                      <div class=\"card-center\">\r\n                        <el-input @click.stop v-if=\"data.stepInfo.inputDlg\" v-model=\"data.stepInfo.name\" @blur=\"cancelEditing(data.stepInfo)\" ref=\"input\" maxlength=\"50\" class=\"script-name-input\"></el-input>\r\n                        <el-button v-else class=\"script-button\" plain type=\"text\" @click=\"startEditing(data.stepInfo)\" @click.stop>\r\n                          {{data.stepInfo.name}}\r\n                          <el-icon><Edit /></el-icon>\r\n                        </el-button>\r\n                      </div>\r\n                    </div>\r\n                    <div v-if=\"data.stepInfo.type==='script' && data.stepInfo.dlg\" class=\"script-editor\" @click.stop>\r\n                      <el-row :gutter=\"10\">\r\n                        <el-col :span=\"24\"><Editor v-model=\"data.stepInfo.script\" lang=\"python\" theme=\"chrome\"></Editor></el-col>\r\n                      </el-row>\r\n                    </div>\r\n\r\n                    <!--py脚本展示-->\r\n                    <div v-if=\"data.stepInfo.type==='py'\" class=\"card-inner\">\r\n                      <div class=\"card-left\">\r\n                        <span class=\"step-icon\">{{ getCardIndex(node.parent, node) }}</span>\r\n                        <el-tag color=\"rgb(103, 194, 58)\" class=\"step-tag\">导入PY脚本</el-tag>\r\n                      </div>\r\n                      <div class=\"card-center\">\r\n                        <el-input @click.stop v-if=\"data.stepInfo.inputDlg\" v-model=\"data.stepInfo.name\" @blur=\"cancelEditing(data.stepInfo)\" ref=\"input\" maxlength=\"50\" class=\"script-name-input\"></el-input>\r\n                        <el-button v-else class=\"script-button\" plain type=\"text\" @click=\"startEditing(data.stepInfo)\" @click.stop>\r\n                          {{data.stepInfo.name}}\r\n                          <el-icon><Edit /></el-icon>\r\n                        </el-button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!--time控制器展示-->\r\n                    <div v-if=\"data.stepInfo.type==='time'\" class=\"card-inner\">\r\n                      <div class=\"card-left\">\r\n                        <span class=\"step-icon\">{{ getCardIndex(node.parent, node) }}</span>\r\n                        <el-tag color=\"rgb(103, 194, 58)\" class=\"step-tag\">等待控制器</el-tag>\r\n                      </div>\r\n                      <div class=\"card-center time-controller\">\r\n                        <div class=\"time-control\">\r\n                          <el-input-number\r\n                            v-model=\"data.stepInfo.content.time\"\r\n                            :min=\"0\"\r\n                            :max=\"999\"\r\n                            size=\"small\"\r\n                            controls-position=\"right\"\r\n                            placeholder=\"秒\"\r\n                          />\r\n                          <span>秒</span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"4\" class=\"card-actions\">\r\n                  <div class=\"action-buttons\">\r\n                    <el-tooltip v-if=\"data.stepInfo.type==='api'\" class=\"item\" effect=\"light\" content=\"接口任务运行权重默认为1,设置后将影响运行结果！\" placement=\"top\">\r\n                      <el-input-number\r\n                        @click.stop\r\n                        v-model=\"data.stepInfo.weight\"\r\n                        :min=\"1\"\r\n                        :max=\"10\"\r\n                        size=\"default\"\r\n                        controls-position=\"right\"\r\n                        placeholder=1\r\n                        style=\"width: 80px;margin-right: 10px\"\r\n                      >\r\n                      </el-input-number>\r\n                    </el-tooltip>\r\n                    <el-switch\r\n                      @click.stop\r\n                      v-model=\"data.stepInfo.status\"\r\n                      inline-prompt\r\n                      size=\"default\"\r\n                      @click=\"switchClick(data)\"\r\n                      style=\"--el-switch-on-color: #53a8ff; --el-switch-off-color: #dcdfe6\"\r\n                    />\r\n                    <el-button @click.stop size=\"default\" circle type=\"danger\" @click=\"delTree(data)\">\r\n                      <el-icon><Delete /></el-icon>\r\n                    </el-button>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n          </el-card>\r\n        </template>\r\n      </el-tree>\r\n    </el-scrollbar>\r\n\r\n    <!--  选择接口弹窗-->\r\n    <apiCite v-if=\"addApiDlg\" :selectType=\"selectType\" @childEvent=\"addController\" @close-modal=\"handleCloseModal\"></apiCite>\r\n    <!--  编辑接口弹窗-->\r\n    <el-drawer v-model=\"editApiDlg\" :with-header=\"false\" size=\"50%\">\r\n      <editApi ref=\"childRef\" @closeDrawer=\"handleClose\" :interfaceData=\"interfaceData\" style=\"padding: 0 10px;\"></editApi>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {mapMutations, mapState} from \"vuex\";\r\nimport {ElNotification, ElMessageBox, ElMessage} from \"element-plus\";\r\nimport apiCite from '../../views/TestCase/apiCiteDlg.vue';\r\nimport Editor from '@/components/common/Editor.vue';\r\nimport editApi from '../../views/PerformanceTest/editApiDlg.vue';\r\nimport { Plus, Edit, Delete, QuestionFilled } from '@element-plus/icons-vue';\r\nimport * as uuid from 'uuid';\r\n\r\nexport default {\r\n  components:{\r\n    apiCite,\r\n    Editor,\r\n    editApi,\r\n    Plus,\r\n    Edit,\r\n    Delete,\r\n    QuestionFilled\r\n  },\r\n  props: {\r\n    scenceId: {\r\n      type: Number,\r\n      default: []\r\n    },\r\n    steps: {\r\n      type: Array,\r\n    },\r\n    scenceData:{\r\n      type: String,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      addApiDlg:false,\r\n      editApiDlg:false,\r\n      selectType:'perf',\r\n      treeKey: '',\r\n      isExpand: false,\r\n      ControllerData:{\r\n        scence:'',\r\n        name: '',\r\n        type: '',\r\n        content: {},\r\n        desc:\"\",\r\n        script:\"\",\r\n        creator:'',\r\n        weight: '',\r\n      },\r\n      step_id: '',\r\n      interfaceData:'',\r\n      options: [\r\n          { value: 'equal', label: '等于' },\r\n          { value: 'notEqual', label: '不等于' },\r\n          { value: 'contains', label: '包含' },\r\n          { value: 'notContains', label: '不包含' },\r\n          { value: 'greaterThan', label: '大于' },\r\n          { value: 'lessThan', label: '小于' },\r\n          { value: 'greaterThanOrEqual', label: '大于等于' },\r\n          { value: 'lessThanOrEqual', label: '小于等于' },\r\n          { value: 'empty', label: '空' },\r\n          { value: 'notEmpty', label: '非空' }\r\n        ],\r\n\r\n    }\r\n  },\r\n  methods: {\r\n\r\n    rowOpenORFold(isExpand) {\r\n\t      this.treeKey = +new Date()\r\n\t      this.isExpand = isExpand\r\n\t    },\r\n\r\n    handleStepClick(data) {\r\n      if (data.stepInfo.type==='api'){\r\n        this.editApiDlg = true;\r\n        this.interfaceData = data.stepInfo;\r\n      }\r\n      else if(['for','script'].includes(data.stepInfo.type)) {\r\n        data.stepInfo.dlg = !data.stepInfo.dlg;\r\n      }\r\n    },\r\n\r\n    allowDrop(draggingNode, dropNode,type) {\r\n      // 只有 type 为 api, for, if 的节点可以作为父级节点\r\n      const allowedParentTypes = ['for', 'if'];\r\n      if (!allowedParentTypes.includes(dropNode.data.stepInfo.type)) {\r\n        return type === \"prev\" || type === \"next\";\r\n\r\n      }else {\r\n        return true\r\n      };\r\n  },\r\n\r\n    fetchSteps(scenceId) {\r\n      this.$emit('fetch-steps', scenceId);\r\n    },\r\n\r\n    async updateStepOrder() {\r\n      const setParentIds = (node, parentId, parentSort) => {\r\n        // 设置父节点的排序字段\r\n        node.sort = parentSort;\r\n        // 如果节点有子节点，则递归设置子节点的 parent 和排序字段\r\n        if (node.children && node.children.length > 0) {\r\n            node.children.forEach((child, childIndex) => {\r\n                // 设置子节点的 parent 为当前节点的 id\r\n                child.parent = node.id;\r\n                // 设置子节点的排序字段\r\n                child.sort = childIndex + 1;\r\n                // 递归调用，处理子节点的子节点\r\n                setParentIds(child, node.id, child.sort);\r\n              });\r\n            }\r\n        };\r\n      // 遍历步骤数组，设置父节点的排序字段和子节点的 parent 和排序字段\r\n      this.steps.forEach((parent, parentIndex) => {\r\n          // 设置父节点的排序字段\r\n          parent.sort = parentIndex + 1;\r\n          // 如果父节点有子节点，则设置子节点的 parent 和排序字段\r\n          if (parent.children && parent.children.length > 0) {\r\n              // 调用函数设置父节点和子节点的属性\r\n              setParentIds(parent, parent.id, parent.sort);\r\n          }else {\r\n            parent.parent = null;\r\n          }\r\n      })\r\n\t\t},\r\n\r\n    handleDragScroll() {\r\n      document.addEventListener('mousemove', function(event) {\r\n      const mouseY = event.clientY;\r\n      const elementTop = document.querySelector('.el-tree').getBoundingClientRect().top;\r\n\r\n      if (mouseY < 100 && elementTop > 0) {\r\n        window.scrollBy(0, -10);\r\n      } else if (mouseY > window.innerHeight - 100) {\r\n        window.scrollBy(0, 10);\r\n      }\r\n    });\r\n    },\r\n\r\n    getCardIndex(parent, node) {\r\n      const index = parent.childNodes.indexOf(node);\r\n      return index + 1;\r\n    },\r\n\r\n    async addController(data, type) {\r\n      const params = {...this.ControllerData};\r\n      params.creator = this.username;\r\n      params.type = type;\r\n      params.scence = this.scenceId;\r\n      const DataArray = [];\r\n      let order_s = this.steps.length > 0 ? this.steps.length + 1 : 1;\r\n\r\n      if(type ==='if'){\r\n        params.name = \"条件控制器\";\r\n        params.content = {\r\n          variable:\"\",\r\n          JudgmentMode:\"\",\r\n          value:\"\",\r\n        };\r\n        delete params.weight;\r\n      }\r\n      else if(type ==='script'){\r\n        params.name = \"自定义脚本\";\r\n        delete params.weight;\r\n\r\n      }\r\n      else if(type ==='time'){\r\n        params.name = \"定时控制器\";\r\n        params.content = {\r\n          time:\"\"\r\n        };\r\n        delete params.weight;\r\n      }\r\n      else {\r\n        params.name = 'HTTP接口';\r\n        params.type = 'api';\r\n        params.weight = 1;\r\n        data.forEach(item => {\r\n        let newItem = {\r\n          ...params,\r\n          content:item\r\n        };\r\n        DataArray.push(newItem);\r\n      })\r\n      }\r\n\r\n      if (['if', 'for', 'time', 'script'].includes(type)) {\r\n        const response = await this.$api.createSceneStep(params)\r\n        if (response.status === 201) {\r\n            this.step_id = response.data.id\r\n        }\r\n      }\r\n      else {\r\n        const response = await this.$api.createSceneStep(DataArray)\r\n        if (response.status === 201) {\r\n            this.step_id = response.data.map(item => item.id);\r\n        }\r\n      }\r\n      const response = await this.$api.createTaskSceneStep({\r\n        task: this.perfTask.id,\r\n        scence: this.scenceId,\r\n        step: this.step_id,\r\n        sort: order_s,\r\n        creator: this.username,\r\n        parent: null,\r\n      })\r\n        if (response.status === 201) {\r\n          ElNotification({\r\n              duration: 500,\r\n              title: '添加成功',\r\n              type: 'success',\r\n            })\r\n        }\r\n      this.fetchSteps(this.scenceId)\r\n    },\r\n\r\n    handleCloseModal() {\r\n      this.addApiDlg = false;\r\n    },\r\n    clickApiDlg() {\r\n      this.addApiDlg = true;\r\n    },\r\n\r\n    cancelEditing(data) {\r\n      data.inputDlg = false;\r\n    },\r\n\r\n    startEditing(data) {\r\n      if(data.type==='script'){data.inputDlg = true}else {data.inputDlg = true}\r\n      this.$nextTick(() => {\r\n        this.$refs.input.focus();\r\n      });\r\n    },\r\n\r\n\r\n    async delTree(data) {\r\n      event.stopPropagation();\r\n      console.log(data)\r\n      const response = await this.$api.deleteTaskSceneStep(data.id,this.scenceId);\r\n\t\t\tif (response.status === 204) {\r\n\t\t\t  const res = await this.$api.deleteSceneStep(data.stepInfo.id);\r\n\t\t\t  if (res.status === 204){\r\n          ElNotification({\r\n              duration: 500,\r\n              title: '删除成功',\r\n              type: 'success',\r\n            });\r\n          this.fetchSteps(this.scenceId)\r\n          }\r\n\t\t\t}\r\n  },\r\n   handleClose() {\r\n      this.editApiDlg = false;\r\n    },\r\n\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      envId: state => state.envId,\r\n      testEnvs: state => state.testEnvs,\r\n      pro: state => state.pro,\r\n      perfTask: state => state.perfTask,\r\n    }),\r\n    defaultProps() {\r\n      return {\r\n        children: 'children',\r\n        label: 'name',\r\n      }\r\n    },\r\n    username() {\r\n      return window.sessionStorage.getItem('username');\r\n    },\r\n  },\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.page-header-card {\r\n  background-color: #fff;\r\n  padding: 10px 20px;\r\n  margin-bottom: 10px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.action-toolbar {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.action-tag {\r\n  color: #ffffff;\r\n  width: 100px;\r\n  height: 32px;\r\n  text-align: center;\r\n  font-size: 13px;\r\n  line-height: 32px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.action-tag:hover {\r\n  opacity: 0.9;\r\n}\r\n\r\n.weight-settings {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #606266;\r\n}\r\n\r\n.custom-tree {\r\n  padding: 10px 20px 10px 10px;\r\n  width: 100%;\r\n}\r\n\r\n/* 卡片样式 */\r\n.step-card {\r\n  margin-bottom: 8px;\r\n  border-radius: 10px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s;\r\n  width: 100%;\r\n  position: relative;\r\n  z-index: 5;\r\n  overflow: visible !important;\r\n}\r\n\r\n.step-card:hover {\r\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 不同类型卡片的边框颜色 */\r\n.step-card-api {\r\n  border-left: 4px solid #61649f;\r\n}\r\n.step-card-if {\r\n  border-left: 4px solid rgb(230, 162, 60);\r\n}\r\n.step-card-for {\r\n  border-left: 4px solid rgb(2, 167, 240);\r\n}\r\n.step-card-script {\r\n  border-left: 4px solid rgb(123, 77, 18);\r\n}\r\n.step-card-py {\r\n  border-left: 4px solid rgb(103, 194, 58);\r\n}\r\n.step-card-time {\r\n  border-left: 4px solid rgb(103, 194, 58);\r\n}\r\n\r\n.card-header {\r\n  padding: 5px;\r\n  width: 100%;\r\n}\r\n\r\n.card-main-content {\r\n  flex-grow: 1;\r\n  padding: 2px 0;\r\n}\r\n\r\n.card-content-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  overflow: visible;\r\n  max-width: 100%;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n  padding: 2px 0;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding-right: 15px;\r\n}\r\n\r\n/* 卡片内部布局样式 */\r\n.card-inner {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  position: relative;\r\n  width: 100%;\r\n  min-height: 32px;\r\n  overflow: visible;\r\n}\r\n\r\n.card-left {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 170px;\r\n  min-width: 170px;\r\n  flex-shrink: 0;\r\n  justify-content: flex-start;\r\n  margin-right: 10px;\r\n  gap: 6px;\r\n}\r\n\r\n.card-center {\r\n  margin-left: 5px;\r\n  flex-grow: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.step-tag {\r\n  color: #ffffff;\r\n  height: 22px;\r\n  text-align: center;\r\n  font-size: 11px;\r\n  line-height: 22px;\r\n  padding: 0 4px;\r\n  width: 80px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.method-tag {\r\n  min-width: 50px;\r\n  display: inline-block;\r\n  font-size: 12px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.card-name {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 2px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.card-url {\r\n  font-size: 13px;\r\n  margin-right: 8px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 60%;\r\n}\r\n\r\n.step-icon {\r\n  display: inline-flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 22px;\r\n  height: 22px;\r\n  border-radius: 50%;\r\n  font-weight: bold;\r\n  background-color: white;\r\n  border: 2px solid currentColor;\r\n  text-align: center;\r\n  line-height: 22px;\r\n  font-size: 12px;\r\n  box-sizing: border-box;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 为不同类型步骤的序号设置不同颜色 */\r\n.step-card-api .step-icon {\r\n  color: rgb(97, 100, 159) !important;\r\n  border-color: rgb(97, 100, 159) !important;\r\n}\r\n\r\n.step-card-if .step-icon {\r\n  color: rgb(230, 162, 60) !important;\r\n  border-color: rgb(230, 162, 60) !important;\r\n}\r\n\r\n.step-card-for .step-icon {\r\n  color: rgb(2, 167, 240) !important;\r\n  border-color: rgb(2, 167, 240) !important;\r\n}\r\n\r\n.step-card-script .step-icon {\r\n  color: rgb(123, 77, 18) !important;\r\n  border-color: rgb(123, 77, 18) !important;\r\n}\r\n\r\n.step-card-py .step-icon {\r\n  color: rgb(103, 194, 58) !important;\r\n  border-color: rgb(103, 194, 58) !important;\r\n}\r\n\r\n.step-card-time .step-icon {\r\n  color: rgb(103, 194, 58) !important;\r\n  border-color: rgb(103, 194, 58) !important;\r\n}\r\n\r\n/* 条件控制器样式 */\r\n.if-content {\r\n  width: 100%;\r\n  flex-wrap: nowrap;\r\n  gap: 8px;\r\n  justify-content: flex-start;\r\n  padding: 8px 0;\r\n  overflow: visible;\r\n  position: relative;\r\n}\r\n\r\n.if-controls-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: visible !important;\r\n  min-width: min-content;\r\n  z-index: 20;\r\n}\r\n\r\n.judgment-select {\r\n  width: 90px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.input-def {\r\n  width: 160px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 脚本样式 */\r\n.script-name-input {\r\n  height: 28px;\r\n  max-width: 380px;\r\n}\r\n\r\n.script-button {\r\n  color: black;\r\n  border: none;\r\n  outline: none;\r\n  font-size: 13px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n  width: auto;\r\n  margin: 0;\r\n  padding: 0 8px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.script-button .el-icon {\r\n  margin-left: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.script-editor {\r\n  padding: 15px 0;\r\n  border-top: 1px dashed #eee;\r\n  margin-top: 8px;\r\n  width: 100%;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* 循环控制器样式 */\r\n.for-controls-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: visible;\r\n}\r\n\r\n.radio-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.loop-details {\r\n  background-color: #f9f9f9;\r\n  border-radius: 0 0 8px 8px;\r\n  padding: 10px 15px;\r\n  margin-top: 5px;\r\n  width: 100%;\r\n  overflow: visible;\r\n  position: relative;\r\n}\r\n\r\n.loop {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  overflow: visible;\r\n  position: relative;\r\n}\r\n\r\n.loop-control {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  white-space: nowrap;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 等待控制器样式 */\r\n.time-controller {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n  width: 100%;\r\n  flex-wrap: nowrap;\r\n}\r\n\r\n.time-control {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  white-space: nowrap;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 树节点样式 */\r\n:deep(.el-tree-node__content) {\r\n  padding: 2px 3px;\r\n  height: auto;\r\n  width: 100%;\r\n}\r\n\r\n:deep(.el-tree-node__expand-icon) {\r\n  padding: 4px;\r\n}\r\n\r\n:deep(.el-tree-node.is-drop-inner>.el-tree-node__content .el-tree-node__label) {\r\n  background-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n:deep(.el-tree-node) {\r\n  width: 100%;\r\n}\r\n\r\n:deep(.el-tree) {\r\n  width: 100%;\r\n}\r\n\r\n:deep(.el-card) {\r\n  width: 100%;\r\n  margin-right: 20px;\r\n  --el-card-padding: 5px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n:deep(.el-tree-node__children) {\r\n  width: 100%;\r\n}\r\n\r\n/* 设置Input和其他表单元素的尺寸 */\r\n:deep(.el-input__inner) {\r\n  height: 28px;\r\n  line-height: 28px;\r\n  font-size: 12px;\r\n}\r\n\r\n:deep(.el-input-number) {\r\n  line-height: normal;\r\n}\r\n\r\n:deep(.el-switch) {\r\n  --el-switch-size: 18px;\r\n  margin-right: 5px;\r\n}\r\n\r\n:deep(.el-button.is-circle) {\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 6px;\r\n  margin-left: 5px;\r\n}\r\n\r\n/* while循环样式 */\r\n.while-loop-section {\r\n  width: 100%;\r\n  max-width: 800px;\r\n}\r\n\r\n.condition-config {\r\n  background: #fafbfc;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.while-common-config {\r\n  background: #f8f9fa;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  margin: 15px 0;\r\n}\r\n\r\n.expression-help,\r\n.function-help {\r\n  margin-top: 10px;\r\n}\r\n\r\n.advanced-options {\r\n  margin-top: 15px;\r\n}\r\n\r\n.advanced-options .loop-control {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.advanced-options .el-checkbox {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.advanced-options .el-radio-group {\r\n  margin-left: 10px;\r\n}\r\n\r\n/* 响应式样式 */\r\n@media (max-width: 1200px) {\r\n  .card-inner {\r\n    flex-wrap: wrap;\r\n    padding: 10px 20px;\r\n  }\r\n\r\n  .card-left {\r\n    width: auto;\r\n    min-width: 150px;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .if-controls-wrapper {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .card-center {\r\n    width: 100%;\r\n    margin-top: 10px;\r\n    margin-left: 0;\r\n  }\r\n\r\n  .card-url {\r\n    max-width: 100%;\r\n    margin: 5px 0;\r\n  }\r\n\r\n  .input-def {\r\n    width: 100%;\r\n    margin-bottom: 8px;\r\n  }\r\n  \r\n  /* while循环条件配置响应式布局 */\r\n  .condition-config .loop-control,\r\n  .while-common-config .loop-control {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .condition-config .loop-control span,\r\n  .while-common-config .loop-control span {\r\n    min-width: auto;\r\n    margin-bottom: 5px;\r\n  }\r\n}\r\n</style>", "<template>\r\n  <el-scrollbar height=\"calc(100vh);padding-right:10px;\">\r\n  <div style=\"margin: 10px\" >\r\n  <el-divider content-position=\"left\" style=\"margin-bottom: 30px\"><b>Api信息</b></el-divider>\r\n  <el-form :rules=\"rulesinterface\" ref=\"interfaceRef\" :model=\"caseInfo\">\r\n    <el-row  :gutter=\"10\" style=\"margin-bottom: 20px\">\r\n    <el-col :span=\"16\">\r\n      <el-form-item prop=\"url\">\r\n          <el-input v-model=\"caseInfo.url\" placeholder=\"请输入接口地址\">\r\n            <template #prepend >\r\n              <el-select v-model=\"caseInfo.method\" placeholder=\"请求类型\" size=\"small\" style=\"width: 96px;color: black\">\r\n                <el-option label=\"GET\" value=\"GET\" style=\"color: rgba(204,73,145,0.87)\"/>\r\n                <el-option label=\"POST\" value=\"POST\" style=\"color: #61affe\"/>\r\n                <el-option label=\"PUT\" value=\"PUT\" style=\"color: #fca130\"/>\r\n                <el-option label=\"PATCH\" value=\"PATCH\" style=\"color: #50e3c2\"/>\r\n                <el-option label=\"DELETE\" value=\"DELETE\" style=\"color: #f93e3e\"/>\r\n                <el-option label=\"HEAD\" value=\"HEAD\" style=\"color: rgb(201, 233, 104)\"/>\r\n              </el-select>\r\n            </template>\r\n          </el-input>\r\n        </el-form-item>\r\n        </el-col>\r\n    <el-col :span=\"8\" style=\"text-align: right;\">\r\n      <el-button @click=\"runCase\" type=\"success\">\r\n        <el-icon><Promotion /></el-icon>调试\r\n      </el-button>\r\n      <el-button @click=\"editClick\" type=\"primary\">\r\n        <el-icon><EditPen /></el-icon>保存\r\n      </el-button>\r\n      <el-button @click=\"getNewInterface\" type=\"warning\">\r\n        <el-icon><Refresh /></el-icon>同步\r\n      </el-button>\r\n    </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"24\" style=\"margin-bottom: 20px\">\r\n    <el-col :span=\"12\">\r\n      <el-form-item label=\"接口名称\" prop=\"name\" >\r\n        <el-input v-model=\"caseInfo.name\" placeholder=\"请输入接口名称\" clearable style=\"width: 300px\"/>\r\n      </el-form-item>\r\n    </el-col>\r\n    <el-col :span=\"12\">\r\n    <el-scrollbar height=\"60px\">\r\n      <el-form-item label=\"接口标签\">\r\n      <el-tag\r\n        v-for=\"tag in caseInfo.interface_tag\"\r\n        :key=\"tag\"\r\n        size=\"small\"\r\n        :type=\"getRandomType()\"\r\n        closable\r\n        :disable-transitions=\"false\"\r\n        style=\"margin-right: 5px\"\r\n        @close=\"removeTag(tag)\"\r\n        effect=\"light\"\r\n      >{{ tag }}</el-tag>\r\n      <el-input\r\n        v-if=\"state.editTag\"\r\n        ref=\"caseTagInputRef\"\r\n        v-model=\"state.tagValue\"\r\n        size=\"small\"\r\n        @keyup.enter=\"addTag\"\r\n        @blur=\"addTag\"\r\n        style=\"width: 100px\"\r\n        maxlength=\"30\"\r\n      />\r\n      <el-button v-else size=\"small\" @click=\"showEditTag\">+ New Tag</el-button>\r\n    </el-form-item>\r\n    </el-scrollbar>\r\n  </el-col>\r\n    <el-col :span=\"24\">\r\n      <el-form-item label=\"描述\">\r\n        <el-input v-model=\"caseInfo.desc\"  type=\"textarea\" clearable style=\"width: 100%\"/>\r\n      </el-form-item>\r\n    </el-col>\r\n    <el-col :span=\"5\">\r\n    <el-form-item label=\"创建用户：\"  style=\"margin-top: 10px;\">\r\n      <a>{{this.caseInfo.creator}}</a>\r\n    </el-form-item>\r\n    </el-col>\r\n    <el-col :span=\"7\">\r\n    <el-form-item label=\"创建时间：\"  style=\"margin-top: 10px;\">\r\n    <template #default=\"scope\">\r\n      <a>{{ $tools.rTime(this.caseInfo.create_time) }}</a>\r\n    </template>\r\n    </el-form-item>\r\n    </el-col>\r\n    <el-col :span=\"5\">\r\n    <el-form-item label=\"修改用户：\"  style=\"margin-top: 10px;\">\r\n      <a>{{this.caseInfo.modifier}}</a>\r\n    </el-form-item>\r\n    </el-col>\r\n    <el-col :span=\"7\">\r\n    <el-form-item label=\"修改时间：\"   style=\"margin-top: 10px;\">\r\n      <template #default=\"scope\">\r\n        <a v-if=\"this.caseInfo.update_time\">{{$tools.rTime(this.caseInfo.update_time)}}</a>\r\n      </template>\r\n    </el-form-item>\r\n    </el-col>\r\n  </el-row>\r\n  </el-form>\r\n  <el-divider content-position=\"left\" style=\"margin-top: 0px\"><b>请求信息</b></el-divider>\r\n    <!-- ace编辑器 -->\r\n\t\t<el-tabs type=\"border-card\" style=\"min-height: 370px;\" >\r\n\t\t\t<el-tab-pane label=\"请求头(headers)\"><Editor v-model=\"headers\"></Editor></el-tab-pane>\r\n\t\t\t<el-tab-pane label=\"查询参数(Params)\"><Editor v-model=\"params\"></Editor></el-tab-pane>\r\n\t\t\t<el-tab-pane label=\"请求体(Body)\">\r\n\t\t\t\t<el-radio-group v-model=\"paramType\" style=\"margin-bottom: 5px;\">\r\n\t\t\t\t\t<el-radio label=\"json\">application/json</el-radio>\r\n\t\t\t\t\t<el-radio label=\"data\">x-www-form-urlencoded</el-radio>\r\n\t\t\t\t\t<el-radio label=\"formData\">form-data</el-radio>\r\n\t\t\t\t</el-radio-group>\r\n\t\t\t\t<div v-if=\"paramType === 'json'\"><Editor v-model=\"json\"></Editor></div>\r\n\t\t\t\t<div v-else-if=\"paramType === 'data'\"><Editor v-model=\"data\"></Editor></div>\r\n\t\t\t\t<div v-else-if=\"paramType === 'formData'\">\r\n\t\t\t\t\t<FromData v-model=\"file\"></FromData>\r\n\t\t\t\t</div>\r\n\t\t\t</el-tab-pane>\r\n\t\t\t<el-tab-pane label=\"前置脚本\">\r\n\t\t\t\t<el-row :gutter=\"10\">\r\n\t\t\t\t\t<el-col :span=\"18\"><Editor v-model=\"caseInfo.setup_script\" lang=\"python\" theme=\"monokai\"></Editor></el-col>\r\n\t\t\t\t\t<el-col :span=\"6\">\r\n\t\t\t\t\t\t<el-divider style=\"width:195px\">脚本模板</el-divider>\r\n\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addSetUptCodeMod('ENV')\">预设全局变量</el-button></div>\r\n\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addSetUptCodeMod('env')\">预设局部变量</el-button></div>\r\n\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addSetUptCodeMod('func')\">调用全局函数</el-button></div>\r\n\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addSetUptCodeMod('sql')\">执行sql查询</el-button></div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t</el-tab-pane>\r\n\t\t\t<el-tab-pane label=\"后置脚本\">\r\n\t\t\t\t<el-row :gutter=\"10\">\r\n\t\t\t\t\t<el-col :span=\"18\"><Editor v-model=\"caseInfo.teardown_script\" lang=\"python\" theme=\"monokai\"></Editor></el-col>\r\n\t\t\t\t\t<el-col :span=\"6\">\r\n\t\t\t\t\t\t<el-divider style=\"width:195px\">脚本模板</el-divider>\r\n\t\t\t\t\t\t<el-scrollbar height=\"250px\">\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('getBody')\">获取响应体</el-button></div>\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('JSextract')\">jsonpath提取数据</el-button></div>\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('REextract')\">正则提取数据</el-button></div>\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('ENV')\">设置全局变量</el-button></div>\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('env')\">设置局部变量</el-button></div>\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('func')\">调用全局函数</el-button></div>\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('sql')\">执行sql查询</el-button></div>\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('http')\">断言HTTP状态码</el-button></div>\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('eq')\">断言相对</el-button></div>\r\n\t\t\t\t\t\t\t<div class=\"code_mod\"><el-button type=\"success\" size=\"small\" plain @click=\"addTearDownCodeMod('contain')\">断言包含</el-button></div>\r\n\t\t\t\t\t\t</el-scrollbar>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t</el-tab-pane>\r\n\t\t</el-tabs>\r\n\t\t<div v-if=\"runResult\">\r\n\t\t\t<el-divider content-position=\"left\"><b>执行结果</b></el-divider>\r\n\t\t\t<caseResult :result=\"runResult\"></caseResult>\r\n    </div>\r\n  </div>\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script>\r\nimport caseResult from '@/components/common/caseResult.vue';\r\nimport FromData from '@/components/common/FormData.vue'\r\nimport Editor from \"@/components/common/Editor\";\r\nimport {mapState} from \"vuex\";\r\nimport {ElMessage,ElNotification} from \"element-plus\";\r\nimport { Promotion, EditPen, Refresh } from '@element-plus/icons-vue';\r\nexport default {\r\n  props: {\r\n    interfaceData: {\r\n      type: String,\r\n      default: {}\r\n    }\r\n  },\r\n\r\n  components: {\r\n    caseResult,\r\n    FromData,\r\n    Editor,\r\n    Promotion,\r\n    EditPen,\r\n    Refresh\r\n  },\r\n  data() {\r\n    return {\r\n      rulesinterface: {\r\n        name: [\r\n          {\r\n            required: true,\r\n            message: '请输入接口名称',\r\n            trigger: 'blur'\r\n          }\r\n        ],\r\n        url: [\r\n          {\r\n            required: true,\r\n            message: '请输入接口信息',\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      state: {\r\n        form: {\r\n          item: [\r\n            {type: ''},\r\n            {type: 'success'},\r\n            {type: 'info'},\r\n            {type: 'danger'},\r\n            {type: 'warning'}\r\n          ]\r\n        },\r\n        editTag: false, // 标记是否处于编辑状态\r\n        tagValue: '', // 输入框中的值\r\n      },\r\n      caseInfo: {\r\n        method: 'POST',\r\n        interface_tag: [],\r\n        YApi_status:'',\r\n        url: '',\r\n        name: '',\r\n        treenode: this.treeId,\r\n        creator: '',\r\n        modifier: '',\r\n        desc: '',\r\n        headers: {},\r\n        request: {\"json\": {}, \"data\": null, \"params\": {}},\r\n        file: [],\r\n        setup_script: '# 前置脚本(python):\\n' +\r\n            '# global_tools:全局工具函数\\n' +\r\n            '# data:用例数据 \\n' +\r\n            '# env: 局部环境\\n' +\r\n            '# ENV: 全局环境\\n' +\r\n            '# db: 数据库操作对象',\r\n        teardown_script: '# 后置脚本(python):\\n' +\r\n            '# global_tools:全局工具函数\\n' +\r\n            '# data:用例数据 \\n' +\r\n            '# response:响应对象response \\n' +\r\n            '# env: 局部环境\\n' +\r\n            '# ENV: 全局环境\\n' +\r\n            '# db: 数据库操作对象'\r\n      },\r\n      paramType: 'json',\r\n      json: '{}',\r\n      data: '{}',\r\n      params: '{}',\r\n      headers: '{}',\r\n      interfaceparams: '{}',\r\n      file: [],\r\n      interface_tag: [],\r\n      runResult: \"\",\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['pro', 'envId']),\r\n  username() {\r\n\t\t\treturn window.sessionStorage.getItem('username');\r\n\t\t},\r\n\r\n  },\r\n  methods: {\r\n    // 标签功能点击自动聚焦\r\n    focusInput() {\r\n      this.$nextTick(() => {\r\n        this.$refs.caseTagInputRef.focus();\r\n      });\r\n    },\r\n    // 新增标签\r\n    addTag() {\r\n      if (this.state.editTag && this.state.tagValue) {\r\n        if (!this.caseInfo.interface_tag) this.caseInfo.interface_tag = [];\r\n        this.caseInfo.interface_tag.push(this.state.tagValue);\r\n        this.focusInput();\r\n      }\r\n      this.state.editTag = false;\r\n      this.state.tagValue = '';\r\n    },\r\n\r\n    // 删除标签\r\n    removeTag(tag) {\r\n      this.caseInfo.interface_tag.splice(this.caseInfo.interface_tag.indexOf(tag), 1);\r\n    },\r\n\r\n    // 确定保存标签\r\n    showEditTag() {\r\n      this.state.editTag = true;\r\n      this.focusInput();\r\n    },\r\n    // 随机创建不一样type的标签\r\n    getRandomType() {\r\n      const randomIndex = Math.floor(Math.random() * this.state.form.item.length);\r\n      return this.state.form.item[randomIndex].type;\r\n    },\r\n\r\n    // 生成前置脚本的方法\r\n    addSetUptCodeMod(tp) {\r\n      switch (tp) {\r\n        case 'ENV':\r\n          this.caseInfo.setup_script += '\\n# 设置全局变量 \\ntest.save_global_variable(\"变量名\",变量值)';\r\n          break;\r\n        case 'env':\r\n          this.caseInfo.setup_script += '\\n# 设置局部变量  \\ntest.save_env_variable(\"变量名\",变量值)';\r\n          break;\r\n        case 'func':\r\n          this.caseInfo.setup_script += '\\n# 调用全局工具函数random_mobile随机生成一个手机号码  \\nmobile = global_func.random_mobile()';\r\n          break;\r\n        case 'sql':\r\n          this.caseInfo.setup_script +=\r\n              '\\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\\n# db.连接名.execute_all(sql语句) \\nsql = \"SELECT count(*) as count FROM futureloan.member\"\\nres = db.aliyun.execute_all(sql)';\r\n          break;\r\n      }\r\n    },\r\n    // 生成后置脚本的方法\r\n    addTearDownCodeMod(tp) {\r\n      switch (tp) {\r\n        case 'getBody':\r\n          this.caseInfo.teardown_script += '\\n# Demo:获取响应体(json)  \\nbody = response.json()';\r\n          this.caseInfo.teardown_script += '\\n# Demo2:获取响应体(字符串)  \\nbody = response.text';\r\n          break;\r\n        case 'JSextract':\r\n          this.caseInfo.teardown_script += '\\n# Demo:jsonpath提取response中的msg字段  \\nmsg = test.json_extract(response.json(),\"$..msg\")';\r\n          break;\r\n        case 'REextract':\r\n          this.caseInfo.teardown_script += '\\n# Demo:正则提取响应体中的数据  \\nres = test.re_extract(response.text,\"正则表达式\",)';\r\n          break;\r\n        case 'ENV':\r\n          this.caseInfo.teardown_script += '\\n# 设置全局变量 \\ntest.save_global_variable(\"变量名\",变量值)';\r\n          break;\r\n        case 'env':\r\n          this.caseInfo.teardown_script += '\\n# 设置局部变量  \\ntest.save_env_variable(\"变量名\",变量值)';\r\n          break;\r\n        case 'func':\r\n          this.caseInfo.teardown_script += '\\n# 调用全局工具函数random_mobile随机生成一个手机号码  \\nmobile = global_func.random_mobile()';\r\n          break;\r\n        case 'sql':\r\n          this.caseInfo.teardown_script +=\r\n              '\\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\\n# db.连接名.execute_all(sql语句) \\nsql = \"SELECT count(*) as count FROM futureloan.member\"\\nres = db.aliyun.execute_all(sql)';\r\n          break;\r\n        case 'http':\r\n          this.caseInfo.teardown_script += '\\n# 断言http状态码 \\n# Demo:断言http状态码是否为200  \\ntest.assertion(\"相等\",200,response.status_code)';\r\n          break;\r\n        case 'eq':\r\n          this.caseInfo.teardown_script += '\\n# 断言相等 \\ntest.assertion(\"相等\",\"预期结果\",\"实际结果\")';\r\n          break;\r\n        case 'contain':\r\n          this.caseInfo.teardown_script += '\\n# 断言包含:预期结果中的内容在实际结果中是否存在 \\ntest.assertion(\"包含\",\"预期结果\",\"实际结果\")';\r\n          break;\r\n      }\r\n    },\r\n\r\n    interfaceInfo() {\r\n      this.runResult = null;\r\n      this.caseInfo = {...this.interfaceData.content};\r\n      this.json = JSON.stringify(this.caseInfo.request.json || {}, null, 4);\r\n      this.data = JSON.stringify(this.caseInfo.request.data || {}, null, 4);\r\n      this.params = JSON.stringify(this.caseInfo.request.params || {}, null, 4);\r\n      this.headers = JSON.stringify(this.caseInfo.headers || {}, null, 4);\r\n      this.caseInfo.interface_tag = Array.from(this.caseInfo.interface_tag.tag);\r\n      this.file = this.caseInfo.file;\r\n    },\r\n\r\n    //  组装接口的数据\r\n    getEditData() {\r\n      let caseData = {...this.caseInfo};\r\n      delete caseData.status\r\n\r\n      // tag标签改成interface_tag:{tag:[值1,值2]}\r\n      caseData.interface_tag = {tag: [...caseData.interface_tag]};\r\n      caseData.modifier = this.username;\r\n      caseData.update_time = this.$tools.newTime()\r\n      try {\r\n        caseData.headers = JSON.parse(this.headers);\r\n      } catch (e) {\r\n        this.$message({\r\n          message: '提交的headers数据 json格式错误，请检查！',\r\n          type: 'warning',\r\n          duration: 1000\r\n        });\r\n        return null;\r\n      }\r\n      // 请求体格式的选择\r\n      if (this.paramType === 'json') {\r\n        const json5 = require('json5');\r\n        try {\r\n          caseData.request = { json: json5.parse(this.json) };\r\n          caseData.request.data = null;\r\n          caseData.file = [];\r\n\r\n        } catch (e) {\r\n          this.$message({\r\n            message: \"提交的app-``lication/json数据json格式错误，请检查！\",\r\n            type: 'warning',\r\n            duration: 1000\r\n          });\r\n          return null;\r\n        }\r\n      }\r\n      else if (this.paramType === 'data') {\r\n        try {\r\n          caseData.request = {data: JSON.parse(this.data)};\r\n          caseData.request.json = null\r\n          caseData.file = []\r\n        } catch (e) {\r\n          this.$message({\r\n            message: \"提交的x-www-form-urlencoded数据json格式错误，请检查！\",\r\n            type: 'warning',\r\n            duration: 1000\r\n          });\r\n          return null;\r\n        }\r\n      }\r\n      else if (this.paramType === 'formData') {\r\n        caseData.file = this.file;\r\n        caseData.request = {}\r\n      }\r\n      try {\r\n        caseData.request.params = JSON.parse(this.params);\r\n        // caseData.interface = this.caseInfo.interface.id;\r\n        return caseData;\r\n      } catch (e) {\r\n        this.$message({\r\n          message: \"提交的Params数据json格式错误，请检查！\",\r\n          type: 'warning',\r\n          duration: 1000\r\n        });\r\n        return null;\r\n      }\r\n\r\n    },\r\n\r\n\r\n    // 修改接口\r\n    async editClick() {\r\n      this.$refs.interfaceRef.validate(async vaild => {\r\n        // 判断是否验证通过，不通过则直接return\r\n        if (!vaild) return;\r\n        const params = {content :this.getEditData()};\r\n        const response = await this.$api.updateScenceStep(this.interfaceData.id, params);\r\n        if (response.status === 200) {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '保存成功',\r\n            duration: 1000\r\n          });\r\n        }\r\n      })\r\n    },\r\n\r\n    // 运行用例\r\n    async runCase() {\r\n      this.$refs.interfaceRef.validate(async vaild => {\r\n        // 判断是否验证通过，不通过则直接return\r\n        if (!vaild) return;\r\n        const runData = this.getEditData();\r\n        runData.interface = {\r\n          url: this.caseInfo.url,\r\n          method: this.caseInfo.method\r\n        };\r\n        const params = {\r\n          data: runData,\r\n          env: this.envId\r\n        };\r\n        const response = await this.$api.runNewCase(params);\r\n        if (response.status === 200) {\r\n          this.runResult = response.data;\r\n          ElNotification({\r\n              duration: 500,\r\n              title: '删除成功',\r\n              type: 'success',\r\n            });\r\n        }\r\n      })\r\n    },\r\n\r\n    // 获取最新的接口数据\r\n    async getNewInterface(){\r\n      const response = await this.$api.getnewInterface(this.interfaceData.content.id);\r\n      if (response.status === 200) {\r\n        this.$message({\r\n\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\tmessage: '获取成功，若需要更新数据请保存',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n        this.caseInfo = {...response.data};\r\n        this.runResult = null;\r\n        this.json = JSON.stringify(this.caseInfo.request.json || {}, null, 4);\r\n        this.data = JSON.stringify(this.caseInfo.request.data || {}, null, 4);\r\n        this.params = JSON.stringify(this.caseInfo.request.params || {}, null, 4);\r\n        this.headers = JSON.stringify(this.caseInfo.headers || {}, null, 4);\r\n        this.caseInfo.interface_tag = Array.from(this.caseInfo.interface_tag.tag);\r\n        this.file = this.caseInfo.file;\r\n      }\r\n    }\r\n\r\n  },\r\n\r\n  watch: {\r\n    interfaceData: {\r\n      deep: true, // 深度监听\r\n      handler(newVal, oldVal) {\r\n        this.interfaceInfo();\r\n      },\r\n    },\r\n  },\r\n  created() {\r\n    this.interfaceInfo()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.code_mod {\r\n\tmargin-bottom: 5px;\r\n}\r\n</style>", "import { render } from \"./editApiDlg.vue?vue&type=template&id=12ddac76&scoped=true\"\nimport script from \"./editApiDlg.vue?vue&type=script&lang=js\"\nexport * from \"./editApiDlg.vue?vue&type=script&lang=js\"\n\nimport \"./editApiDlg.vue?vue&type=style&index=0&id=12ddac76&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-12ddac76\"]])\n\nexport default __exports__", "import { render } from \"./perfStep.vue?vue&type=template&id=21d2f755&scoped=true\"\nimport script from \"./perfStep.vue?vue&type=script&lang=js\"\nexport * from \"./perfStep.vue?vue&type=script&lang=js\"\n\nimport \"./perfStep.vue?vue&type=style&index=0&id=21d2f755&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-21d2f755\"]])\n\nexport default __exports__", "<template>\r\n  <el-card shadow=\"never\">\r\n    <el-scrollbar  height=\"calc(100vh - 200px)\">\r\n      <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px\">\r\n          <span style=\"font-size: 18px;margin-left: 7px\">任务配置</span>\r\n        <div style=\"display: flex; gap: 5px\">\r\n          <el-button type=\"primary\" @click=\"clickSetEdit\">保存</el-button>\r\n          <el-button type=\"info\" @click=\"clickSetting\">导入预设配置</el-button>\r\n        </div>\r\n      </div>\r\n      <el-form :model=\"configForm\"  :rules=\"rulesConfig\" ref=\"ConfigRef\" label-width=\"95px\" >\r\n        <el-form-item label=\"任务类型：\" prop=\"taskType\">\r\n          {{ taskTypeMap[configForm.taskType] || configForm.taskType}}\r\n        </el-form-item>\r\n        <el-form-item prop=\"name\" label=\"配置名称：\" >\r\n          <el-input v-model=\"configForm.name\" placeholder=\"请输入配置名称\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"configForm.taskType==='20'\" label=\"时间配置：\" prop=\"rule\">\r\n          <el-popover\r\n            v-model:visible=\"cronVisible\"\r\n            placement=\"bottom-start\"\r\n            width=\"30\">\r\n            <template #reference>\r\n              <el-input\r\n                v-model=\"configForm.rule\"\r\n                clearable\r\n                readonly\r\n                placeholder=\"请选择定时任务时间配置\"\r\n                @click=\"cronFun\"\r\n              />\r\n            </template>\r\n            <timerTaskCron\r\n              :runTimeStr=\"configForm.rule\"\r\n              @closeTime=\"closeRunTimeCron\"\r\n              @runTime=\"runTimeCron\"\r\n            >\r\n              </timerTaskCron>\r\n          </el-popover>\r\n        </el-form-item>\r\n        <el-form-item prop=\"logMode\" label=\"日志模式：\" >\r\n          <el-select  v-model=\"selectedLogMode\" placeholder=\"请选择日志模式\" style=\"width: 100%\">\r\n            <el-option label=\"关闭\" value=0></el-option>\r\n            <el-option label=\"开启-全部日志\" value=10></el-option>\r\n            <el-option label=\"开启-仅成功日志\" value=20></el-option>\r\n            <el-option label=\"开启-仅失败日志\" value=30></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"控制模式：\" prop=\"control\">\r\n          <el-select v-model=\"selectControlMode\" placeholder=\"请选择控制模式\" style=\"width: 100%\">\r\n            <el-option label=\"集合模式\" value=10></el-option>\r\n            <el-option label=\"单独模式\" value=20></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"压测模式：\" prop=\"pressureMode\">\r\n          <el-select v-model=\"selectPressureMode\" placeholder=\"请选择压测模式\" style=\"width: 100%\">\r\n            <el-option label=\"并发模式\" value='10'></el-option>\r\n            <el-option label=\"阶梯模式\" value='20'></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"时长单位：\" prop=\"pressureMode\">\r\n          <el-select v-model=\"configForm.timeUnit\" placeholder=\"请选择时长单位\" style=\"width: 100%\">\r\n            <el-option label=\"s\" value=\"s\"></el-option>\r\n            <el-option label=\"m\" value=\"m\"></el-option>\r\n            <el-option label=\"h\" value=\"h\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"思考时间：\" prop=\"thinkTime\">\r\n                  <el-select v-model=\"selectTimeType\" placeholder=\"请选择时间类型\" style=\"width: 100%;margin-bottom: 10px\">\r\n                    <el-option label=\"固定\" value='10'></el-option>\r\n                    <el-option label=\"随机\" value='20'></el-option>\r\n                  </el-select>\r\n                  <span v-if=\"configForm.thinkTimeType === '20'\">\r\n                    <el-input-number\r\n                      v-model=\"configForm.thinkTime[0]\"\r\n                      :min=\"0\"\r\n                      :max=\"999\"\r\n                      size=\"small\"\r\n                      controls-position=\"right\"\r\n                      @change=\"handleChange\"\r\n                      style=\"width: 90px\"\r\n                    />\r\n                    <span style=\"margin-right: 5px;margin-left: 5px\">-</span>\r\n                    <el-input-number\r\n                      v-model=\"configForm.thinkTime[1]\"\r\n                      :min=\"0\"\r\n                      :max=\"999\"\r\n                      size=\"small\"\r\n                      controls-position=\"right\"\r\n                      @change=\"handleChange\"\r\n                      style=\"width: 90px\"\r\n                    />\r\n                  </span>\r\n                  <span v-else>\r\n                    <el-input-number\r\n                      v-model=\"configForm.thinkTime[0]\"\r\n                      :min=\"0\"\r\n                      :max=\"999\"\r\n                      size=\"small\"\r\n                      controls-position=\"right\"\r\n                      @change=\"handleChange\"\r\n                      style=\"width: 90px\"\r\n                    />\r\n                  </span>\r\n                </el-form-item>\r\n        <el-form-item style=\"margin-top: 15px;margin-bottom: 15px\" label=\"运行机器：\" prop=\"resource\">\r\n          <el-radio-group v-model=\"configForm.resource\">\r\n            <el-radio  label=\"10\" @click=\"clickResource('10')\">默认\r\n              <el-tooltip content=\"使用机器管理中默认机器运行\" :enterable=\"false\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"color: #909399; font-size: 16px;\"></i>\r\n              </el-tooltip>\r\n            </el-radio>\r\n            <el-radio  label=\"20\" @click=\"clickResource('20')\">\r\n              自定义\r\n              <el-tooltip content=\"支持选择多机器分布式运行\" :enterable=\"false\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"color: #909399; font-size: 16px;\"></i>\r\n              </el-tooltip>\r\n            </el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-card v-if=\"configForm.pressureMode==='10'\" style=\"background-color: #f5f7f9\" class=\"card\" shadow=\"always\">\r\n          <el-form label-width=\"120px\" :model=\"FormConcurrency\"  :rules=\"rulesConcurrencyMode\" ref=\"CaseRef\">\r\n            <el-form-item label=\"并发用户数：\" prop=\"concurrencyNumber\">\r\n              <el-input v-model=\"FormConcurrency.concurrencyNumber\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"并发数步长：\" prop=\"concurrencyStep\">\r\n              <el-input v-model=\"FormConcurrency.concurrencyStep\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"持续时长：\" prop=\"lastLong\">\r\n              <el-input v-model=\"FormConcurrency.lastLong\"></el-input>\r\n            </el-form-item>\r\n\r\n          </el-form>\r\n        </el-card>\r\n        <el-card v-if=\"configForm.pressureMode==='20'\" style=\"margin-left: 7px;margin-right: 4px;background-color: #f5f7f9\" class=\"card\" shadow=\"always\">\r\n                  <el-form label-width=\"125px\" :model=\"FormLadder\" :rules=\"rulesLadderMode\" ref=\"CaseRef\">\r\n                    <div v-for=\"(ladder, index) in FormLadder.ladders\" :key=\"index\">\r\n                      <div style=\"color: #606266; display: flex; align-items: center; justify-content: space-between;\">\r\n                        <span>阶梯{{ index + 1 }}</span>\r\n                        <el-button\r\n                          :disabled=\"index < 1\"\r\n                          size=\"mini\"\r\n                          type=\"text\"\r\n                          @click=\"removeLadder(index)\"\r\n                        >\r\n                          删除\r\n                        </el-button>\r\n                      </div>\r\n                      <el-form-item label=\"并发用户数：\" :prop=\"'ladders.' + index + '.concurrencyNumber'\">\r\n                        <el-input v-model=\"ladder.concurrencyNumber\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"并发数步长：\" :prop=\"'ladders.' + index + '.concurrencyStep'\">\r\n                        <el-input v-model=\"ladder.concurrencyStep\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"阶梯持续时长：\" :prop=\"'ladders.' + index + '.lastLong'\">\r\n                        <el-input v-model=\"ladder.lastLong\"></el-input>\r\n                      </el-form-item>\r\n                    </div>\r\n                  </el-form>\r\n                  <el-button  style=\"width: 100%;margin-top: 20px; background-color: #ecf5ff; color: #409eff;\" @click=\"addLadder\" >\r\n                    add Data\r\n                  </el-button>\r\n                </el-card>\r\n        <el-table\r\n            v-if=\"configForm.resource==='20'\"\r\n            height=\"200\"\r\n            :data=\"serverData\"\r\n            style=\"width: 100%; margin-top:15px; margin-bottom: 35px\"\r\n            border=\"true\"\r\n            @selection-change=\"handleSelectionChange\"\r\n            ref=\"serverTable\"\r\n        >\r\n        <el-table-column type=\"selection\" width=\"40px\" />\r\n        <el-table-column align=\"center\" prop=\"name\" label=\"机器名称\"  />\r\n        <el-table-column align=\"center\" prop=\"host_ip\" label=\"IP\" width=\"130px\"/>\r\n      </el-table>\r\n      </el-form>\r\n    </el-scrollbar>\r\n  </el-card>\r\n  <!--导入预设配置弹窗-->\r\n  <el-dialog title=\"导入预设配置\" v-model=\"SettingDlg\" destroy-on-close :before-close=\"handleClose\" width=\"80%\" top=\"10px\">\r\n    <makeSet :setButton=\"setButton\" :taskType=\"configForm.taskType\" @set-dlg=\"handleClose\"  @set-data=\"handleSetData\" ></makeSet>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport {mapMutations, mapState} from \"vuex\";\r\nimport makeSet from './makeSet.vue'\r\nimport timerTaskCron from \"@/components/common/timerTaskCron\";\r\nexport default {\r\n  components: {\r\n    makeSet,\r\n    timerTaskCron\r\n  },\r\n  data() {\r\n    return {\r\n      taskTypeMap: {'10': '普通任务', '20': '定时任务'},\r\n      cronVisible: false,\r\n      configForm: {\r\n        name: '',\r\n        rule: '',\r\n        taskType: '',\r\n        logMode: '0',\r\n        pressureMode: '10',\r\n        timeUnit: 's',\r\n        control: '20',\r\n        resource: '10',\r\n        pressureConfig: {},\r\n        serverArray: [],\r\n        project: '',\r\n        creator: '',\r\n        thinkTimeType:'10',\r\n        thinkTime:[0],\r\n      },\r\n      FormConcurrency:{\r\n          lastLong:'',\r\n          concurrencyNumber:'',\r\n          concurrencyStep:''\r\n        },\r\n      FormLadder: {\r\n        ladders: [\r\n          { concurrencyNumber: '', concurrencyStep: '', lastLong: '' }\r\n        ]\r\n      },\r\n      rulesConfig: {\r\n        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],\r\n        thinkTime: [{ required: true, message: '请输入思考时间', trigger: 'blur' }]\r\n\r\n      },\r\n      rulesConcurrencyMode: {\r\n        lastLong: [{ required: true, message: '请输入持续时长', trigger: 'blur' }],\r\n        concurrencyNumber: [{ required: true, message: '请输入并发数', trigger: 'blur' }],\r\n        concurrencyStep: [{ required: true, message: '请输入步长', trigger: 'blur' }]\r\n      },\r\n      SettingDlg: false,\r\n      setButton: true,\r\n      serverData: [],\r\n      defaultSelection: [],\r\n      Selection:[],\r\n      rulesLadderMode: {},\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      ...mapState(['perfTask']),\r\n      server: state => state.server,\r\n      pro: state => state.pro\r\n    }),\r\n    username() {\r\n      return window.sessionStorage.getItem('username');\r\n    },\r\n    selectedLogMode: {\r\n      get() {\r\n        return this.configForm.logMode.toString();\r\n      },\r\n      set(value) {\r\n        this.configForm.logMode = Number(value);\r\n      }\r\n    },\r\n    selectPressureMode: {\r\n      get() {\r\n        return this.configForm.pressureMode.toString();\r\n      },\r\n      set(value) {\r\n        this.configForm.pressureMode = value;\r\n      }\r\n    },\r\n    selectControlMode: {\r\n      get() {\r\n        return this.configForm.control.toString();\r\n      },\r\n      set(value) {\r\n        this.configForm.control = Number(value);\r\n      }\r\n    },\r\n    selectTimeType: {\r\n      get() {\r\n        return this.configForm.thinkTimeType.toString();\r\n      },\r\n      set(value) {\r\n        this.configForm.thinkTimeType = value;\r\n      }\r\n     },\r\n\r\n  },\r\n  mounted() {\r\n    this.configForm.taskType = this.perfTask.taskType;\r\n    this.setRules();\r\n  },\r\n  watch: {\r\n    'configForm.thinkTimeType'(newType) {\r\n      if (newType === '20') {\r\n        this.configForm.thinkTime = [this.configForm.thinkTime[0], this.configForm.thinkTime[1]];\r\n      } else {\r\n        this.configForm.thinkTime = [this.configForm.thinkTime[0]];\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleClose(done) {\r\n      this.SettingDlg = done;\r\n      this.SettingDlg = false\r\n    },\r\n\r\n    handleSetData(data) {\r\n      this.configForm = data;\r\n      const selectedIds = this.configForm.serverArray;\r\n      this.Selection = this.serverData.filter(item => selectedIds.includes(item.id));\r\n      if (this.configForm.pressureMode==='10') {\r\n        this.FormConcurrency = data.pressureConfig\r\n      }\r\n      else if (this.configForm.pressureMode==='20') {\r\n        this.FormLadder = data.pressureConfig\r\n      }\r\n      this.$nextTick(()=>{\r\n             if (this.$refs.serverTable) {\r\n                  this.Selection.forEach(row => {\r\n                      this.$refs.serverTable.toggleRowSelection(row, true);\r\n                  });\r\n              } else {\r\n                  console.error('serverTable is undefined');\r\n              }\r\n          })\r\n    },\r\n\r\n    async getPresetting() {\r\n     const response =await this.$api.getPresetting(\r\n         {\r\n           project_id: this.pro.id,\r\n           isSetting: true,\r\n           task: this.perfTask.id\r\n         })\r\n     if (response.status ===200){\r\n       if (response.data.result.length>0){\r\n          this.handleSetData(response.data.result[0]);\r\n       }\r\n\t\t\t}\r\n    },\r\n\r\n    clickResource(type) {\r\n      if (type==='20') {\r\n      setTimeout(() => {\r\n        this.$nextTick(()=>{\r\n             if (this.$refs.serverTable) {\r\n                  this.Selection.forEach(row => {\r\n                      this.$refs.serverTable.toggleRowSelection(row, true);\r\n                  });\r\n              } else {\r\n                  console.error('serverTable is undefined');\r\n              }\r\n          })\r\n      },2000)\r\n      }\r\n      else if (type==='10') {\r\n        this.configForm.serverArray = this.defaultSelection.map(row => row.id);\r\n      }\r\n      else {\r\n        this.$message({\r\n          message: '暂不支持该类型',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    clickSetting() {\r\n      this.SettingDlg = true;\r\n    },\r\n\r\n    cronFun() {\r\n      this.cronVisible = true;\r\n    },\r\n    closeRunTimeCron(isClose) {\r\n      this.cronVisible = isClose;\r\n    },\r\n    runTimeCron(cron) {\r\n      this.configForm.rule = cron;\r\n    },\r\n\r\n    async getServerData() {\r\n      const response = await this.$api.getServers(this.pro.id, 1)\r\n      if (response.status === 200) {\r\n        this.serverData = response.data.result;\r\n          this.defaultSelection = this.serverData.filter(item => item.default_code === true);\r\n          this.Selection = this.defaultSelection.map(row => row.id)\r\n\r\n      }\r\n    },\r\n    handleSelectionChange(selectedRows) {\r\n      // 选择的行可能包含多个对象\r\n      this.configForm.serverArray = selectedRows.map(row => row.id);\r\n\r\n    },\r\n\r\n    dataSubmit() {\r\n      const params = {...this.configForm}\r\n      params.task = this.perfTask.id;\r\n      params.update_time = this.$tools.newTime();\r\n      params.modifier = this.username;\r\n      params.creator = this.username;\r\n      delete params.create_time;\r\n      delete params.id;\r\n      if (params.taskType === '10') delete params.rule;\r\n\r\n      if (params.pressureMode === '10') {\r\n        params.pressureConfig = this.FormConcurrency;\r\n        const { ladders, ...rest } = params.pressureConfig;\r\n        params.pressureConfig = rest;\r\n      } else if (params.pressureMode === '20') {\r\n        params.pressureConfig = this.FormLadder;\r\n        const { ...rest } = params.pressureConfig;\r\n        params.pressureConfig = rest;\r\n      }\r\n      params.project = this.pro.id;\r\n\r\n      return params;\r\n    },\r\n\r\n    async clickSetEdit() {\r\n      const params = this.dataSubmit()\r\n      const response = await this.$api.setPresetting(params)\r\n      if (response.status === 200) {\r\n        this.$message({\r\n          message: '保存成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    addLadder() {\r\n      this.FormLadder.ladders.push({\r\n        concurrencyNumber: '',\r\n        concurrencyStep: '',\r\n        lastLong: ''\r\n      });\r\n      this.setRules()\r\n    },\r\n\r\n    setRules() {\r\n      // 动态生成验证规则\r\n      const ladderRules = {};\r\n      // 遍历 FormLadder.ladders 数组，为每个阶梯项动态设置规则\r\n      this.FormLadder.ladders.forEach((_, index) => {\r\n        ladderRules[`ladders.${index}.concurrencyNumber`] = [\r\n          { required: true, message: '并发用户数不能为空', trigger: 'blur' },\r\n        ];\r\n        ladderRules[`ladders.${index}.concurrencyStep`] = [\r\n          { required: true, message: '并发数步长不能为空', trigger: 'blur' },\r\n        ];\r\n        ladderRules[`ladders.${index}.lastLong`] = [\r\n          { required: true, message: '阶梯持续时长不能为空', trigger: 'blur' },\r\n        ];\r\n      });\r\n\r\n      // 设置 rulesLadderMode 的值\r\n      this.rulesLadderMode = ladderRules;\r\n    },\r\n\r\n    removeLadder(index) {\r\n      if (this.FormLadder.ladders.length > 1) {\r\n        this.FormLadder.ladders.splice(index, 1);\r\n        this.setRules();\r\n      }\r\n    },\r\n\r\n\r\n  },\r\ncreated() {\r\n  this.getServerData();\r\n  this.getPresetting();\r\n}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n:deep(.el-scrollbar__bar) {\r\n  display: none;\r\n}\r\n\r\n</style>", "import { render } from \"./maskMgrDetail_set.vue?vue&type=template&id=2b80dd13&scoped=true\"\nimport script from \"./maskMgrDetail_set.vue?vue&type=script&lang=js\"\nexport * from \"./maskMgrDetail_set.vue?vue&type=script&lang=js\"\n\nimport \"./maskMgrDetail_set.vue?vue&type=style&index=0&id=2b80dd13&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-2b80dd13\"]])\n\nexport default __exports__", "import { render } from \"./maskMgrDetail.vue?vue&type=template&id=491b2273&scoped=true\"\nimport script from \"./maskMgrDetail.vue?vue&type=script&lang=js\"\nexport * from \"./maskMgrDetail.vue?vue&type=script&lang=js\"\n\nimport \"./maskMgrDetail.vue?vue&type=style&index=0&id=491b2273&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-491b2273\"]])\n\nexport default __exports__"], "names": ["class", "style", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_el_card", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_component_el_button", "type", "onClick", "$options", "back", "_component_el_icon", "_component_CaretLeft", "_hoisted_5", "popup", "_ctx", "perfTask", "taskName", "_component_Edit", "taskType", "toString", "$data", "taskTypeMap", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "creator", "_hoisted_9", "_hoisted_10", "$tools", "rTime", "create_time", "_hoisted_11", "_hoisted_12", "update_time", "_hoisted_13", "_hoisted_14", "desc", "_hoisted_15", "clickRun", "_component_CaretRight", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_16", "_hoisted_17", "_component_el_input", "filterText", "$event", "placeholder", "clearable", "append", "_withCtx", "searchClick", "_cache", "_component_Plus", "_component_el_scrollbar", "height", "scenceId", "_createBlock", "_component_el_tree", "data", "sceneList", "props", "defaultProps", "onNodeClick", "handleNodeClick", "default", "node", "_hoisted_18", "label", "_hoisted_19", "_hoisted_20", "size", "circle", "delScene", "id", "_component_Delete", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "scenceData", "env", "_component_el_tooltip", "effect", "content", "placement", "clickShowEnv", "_component_View", "_component_el_select", "_createElementBlock", "_Fragment", "_renderList", "testEnvs", "item", "_component_el_option", "key", "name", "value", "_component_el_dialog", "showEnv", "title", "top", "width", "footer", "_hoisted_25", "editEnv", "envInfo", "plain", "_component_el_descriptions", "border", "column", "debug_global_variable", "_component_el_descriptions_item", "_component_el_tag", "color", "global_variable", "_hoisted_26", "_component_el_dropdown", "trigger", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "clickOts", "_component_Remove", "_component_SuccessFilled", "_component_CircleClose", "_component_arrow_down", "_component_Refresh", "clickScenceStep", "_component_Document", "debugScence", "_component_VideoPlay", "_component_perfStep", "steps", "onFetchSteps", "getTaskScenceStep", "_component_configuration", "dialogTitle", "dialogVisible", "required", "clearValidation", "_hoisted_27", "dialogType", "addScene", "editScene", "_component_el_form", "model", "sceneForm", "rules", "rulesPerf", "ref", "_component_el_form_item", "prop", "maxlength", "dialogVisible1", "_hoisted_28", "editTask", "taskForm", "_component_el_drawer", "otsDlg", "titleOts", "onClose", "handleClose", "_component_el_tabs", "_component_el_checkbox", "indeterminate", "isIndeterminate", "new_task_form", "case_checkAll", "onChange", "handleCheckAllChange", "typeOts", "onCheckChange", "case_check_change", "_hoisted_29", "getCardIndex", "parent", "stepInfo", "_hoisted_30", "url", "_hoisted_31", "makeOts", "slot", "clickApiDlg", "addController", "enterable", "_component_QuestionFilled", "_component_el_input_number", "$props", "weight", "min", "max", "handleChange", "draggable", "isExpand", "handleStepClick", "allowDrop", "onNodeDrop", "updateStepOrder", "handleDragScroll", "_normalizeClass", "align", "justify", "method", "variable", "JudgmentMode", "options", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_component_el_radio_group", "select", "_withModifiers", "_component_el_radio", "dlg", "_hoisted_37", "_hoisted_38", "_hoisted_39", "cycleIndex", "_hoisted_40", "cycleInterval", "_hoisted_41", "_hoisted_42", "_hoisted_43", "variableName", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "whileConditionType", "_hoisted_49", "_hoisted_50", "whileLeftOperand", "_hoisted_51", "whileOperator", "_hoisted_52", "whileRightOperand", "_hoisted_53", "_hoisted_54", "whileExpression", "rows", "_hoisted_55", "_component_el_alert", "closable", "_hoisted_56", "variable_name", "list_var", "str_var", "_hoisted_57", "_hoisted_58", "whileFunctionName", "_hoisted_59", "whileFunctionArgs", "_hoisted_60", "_hoisted_61", "_hoisted_62", "whileMaxIterations", "_hoisted_63", "_hoisted_64", "whileTimeout", "_hoisted_65", "whileCounterVar", "_hoisted_66", "_component_el_divider", "_hoisted_67", "whileBreakOnError", "whileLogIterations", "_hoisted_68", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_hoisted_72", "inputDlg", "onBlur", "cancelEditing", "startEditing", "_component_Editor", "script", "lang", "theme", "_hoisted_73", "_hoisted_74", "_hoisted_75", "_hoisted_76", "_hoisted_77", "_hoisted_78", "_hoisted_79", "_hoisted_80", "_hoisted_81", "time", "_hoisted_82", "_component_el_switch", "switchClick", "status", "delTree", "addApiDlg", "_component_apiCite", "selectType", "onChildEvent", "onCloseModal", "handleCloseModal", "editApiDlg", "_component_editApi", "onCloseDrawer", "interfaceData", "rulesinterface", "caseInfo", "prepend", "runCase", "_component_Promotion", "editClick", "_component_EditPen", "getNewInterface", "interface_tag", "tag", "getRandomType", "removeTag", "state", "editTag", "tagValue", "onKeyup", "_with<PERSON><PERSON><PERSON>", "addTag", "showEditTag", "scope", "this", "modifier", "_component_el_tab_pane", "headers", "params", "paramType", "json", "_component_FromData", "file", "setup_script", "addSetUptCodeMod", "teardown_script", "addTearDownCodeMod", "runResult", "_component_caseResult", "result", "String", "components", "caseResult", "FromData", "Editor", "Promotion", "EditPen", "Refresh", "message", "form", "YApi_status", "treenode", "treeId", "request", "interfaceparams", "computed", "mapState", "username", "window", "sessionStorage", "getItem", "methods", "focusInput", "$nextTick", "$refs", "caseTagInputRef", "focus", "push", "splice", "indexOf", "randomIndex", "Math", "floor", "random", "length", "tp", "interfaceInfo", "JSON", "stringify", "Array", "from", "getEditData", "caseData", "newTime", "parse", "e", "$message", "duration", "json5", "require", "interfaceRef", "validate", "async", "vaild", "response", "$api", "updateScenceStep", "runData", "interface", "envId", "runNewCase", "ElNotification", "getnewInterface", "watch", "deep", "handler", "newVal", "oldVal", "created", "__exports__", "apiCite", "editApi", "Plus", "Edit", "Delete", "QuestionFilled", "Number", "<PERSON><PERSON><PERSON>", "ControllerData", "scence", "step_id", "rowOpenORFold", "Date", "includes", "draggingNode", "dropNode", "allowedParentTypes", "fetchSteps", "$emit", "setParentIds", "parentId", "parentSort", "sort", "children", "for<PERSON>ach", "child", "childIndex", "parentIndex", "document", "addEventListener", "event", "mouseY", "clientY", "elementTop", "querySelector", "getBoundingClientRect", "scrollBy", "innerHeight", "index", "childNodes", "DataArray", "order_s", "newItem", "createSceneStep", "map", "createTaskSceneStep", "task", "step", "input", "stopPropagation", "console", "log", "deleteTaskSceneStep", "res", "deleteSceneStep", "pro", "shadow", "clickSetEdit", "clickSetting", "configForm", "rulesConfig", "_component_el_popover", "visible", "cronVisible", "reference", "rule", "readonly", "cron<PERSON><PERSON>", "_component_timerTaskCron", "runTimeStr", "onCloseTime", "closeRunTimeCron", "onRunTime", "runTimeCron", "selectedLogMode", "selectControlMode", "selectPressureMode", "timeUnit", "selectTimeType", "thinkTimeType", "thinkTime", "resource", "clickResource", "pressureMode", "FormConcurrency", "rulesConcurrencyMode", "concurrencyNumber", "concurrencyStep", "lastLong", "FormLadder", "rulesLadderMode", "ladders", "ladder", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_component_el_table", "serverData", "onSelectionChange", "handleSelectionChange", "_component_el_table_column", "SettingDlg", "_component_makeSet", "setButton", "onSetDlg", "onSetData", "handleSetData", "makeSet", "timerTaskCron", "logMode", "control", "pressureConfig", "serverArray", "project", "defaultSelection", "Selection", "server", "get", "set", "mounted", "setRules", "newType", "done", "selectedIds", "filter", "serverTable", "row", "toggleRowSelection", "error", "getPresetting", "project_id", "isSetting", "setTimeout", "isClose", "cron", "getServerData", "getServers", "default_code", "selectedRows", "dataSubmit", "rest", "setPresetting", "ladderRules", "_", "perfStep", "configuration", "CaretLeft", "CaretRight", "View", "Remove", "SuccessFilled", "CircleClose", "Document", "VideoPlay", "ArrowDown", "importSetData", "mapMutations", "history", "clearTask", "getScenes", "query", "getTaskScenes", "createTaskScene", "updateTaskScene", "$confirm", "confirmButtonText", "cancelButtonText", "then", "deleteTaskScene", "catch", "getSceneStep", "getEnvInfo", "selectEnvInfo", "$router", "updatePerformanceTask", "checkedTask", "getTaskSceneStep", "prams", "batchUpdateSceneStep", "node1", "node2", "node3", "checked_count", "disabled_count", "indeterminate_flag", "i", "casetree", "getNode", "checked", "val", "setChecked", "set<PERSON><PERSON><PERSON><PERSON>eys", "getCheckedNodes", "batchTaskSceneStep", "batchSaveApiStep", "validationResult", "validateScenario", "<PERSON><PERSON><PERSON><PERSON>", "startDebugMode", "errors", "join", "customClass", "trim", "debugInfo", "buildDebugInfo", "$alert", "showCancelButton", "dangerouslyUseHTMLString", "executeDebug", "find", "envName", "s", "buildStepDebugInfo", "buildEnvironmentInfo", "generateDebugSuggestions", "statusIcon", "<PERSON><PERSON><PERSON><PERSON>", "stepDetails", "split", "getStepTypeName", "suggestions", "disabledSteps", "apiSteps", "loopSteps", "scriptSteps", "typeNames", "scene_id", "env_id", "debug_mode", "debugScenario", "showDebugResults", "responseData", "results", "total_steps", "totalSteps", "successful_steps", "successfulSteps", "failed_steps", "failedSteps", "execution_time", "executionTime", "success_rate", "successRate", "step_results", "debug_results", "error_summary", "overall_result", "overallResult", "resultsHtml", "buildDebugResults", "success_steps", "toFixed", "buildStepResults", "logs", "buildDebugLogs", "stepR<PERSON><PERSON>s", "step_name", "details", "status_code", "timestamp", "level", "taskId", "runTask", "showClose", "position", "selectTaskType", "render"], "sourceRoot": ""}