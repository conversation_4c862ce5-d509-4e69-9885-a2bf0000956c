"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[519],{63519:function(e,t,s){s.r(t),s.d(t,{default:function(){return G}});var a=s(56768),l=s(24232);const r={class:"execution-container"},i={class:"panel-header"},n={class:"metrics-grid"},o={class:"metric-content"},d={class:"metric-icon tps-icon"},u={class:"metric-info"},c={class:"metric-value"},h={class:"metric-content"},m={class:"metric-icon response-icon"},p={class:"metric-info"},g={class:"metric-value"},b={class:"metric-content"},_={class:"metric-icon users-icon"},k={class:"metric-info"},f={class:"metric-value"},v={class:"metric-content"},F={class:"metric-info"},C={class:"metric-value"},w={class:"chart-header"},y={ref:"chartContainer",style:{height:"300px"}},T={class:"status-item"},V={class:"status-item"},S={class:"status-item"},L={class:"status-item"},R={class:"progress-section"},x={class:"progress-label"},W={class:"progress-percent"},D={class:"quick-actions"},M={class:"resource-item"},U={class:"resource-value"},$={class:"resource-item"},P={class:"resource-value"},I={class:"resource-item"},E={class:"network-io"},X={class:"dialog-footer"};function z(e,t,s,z,B,O){const A=(0,a.g2)("VideoPlay"),K=(0,a.g2)("VideoPause"),N=(0,a.g2)("el-icon"),G=(0,a.g2)("el-button"),J=(0,a.g2)("Setting"),j=(0,a.g2)("el-button-group"),q=(0,a.g2)("el-option"),H=(0,a.g2)("el-select"),Q=(0,a.g2)("el-form-item"),Y=(0,a.g2)("el-col"),Z=(0,a.g2)("el-row"),ee=(0,a.g2)("el-input-number"),te=(0,a.g2)("el-input"),se=(0,a.g2)("el-form"),ae=(0,a.g2)("el-card"),le=(0,a.g2)("TrendCharts"),re=(0,a.g2)("Timer"),ie=(0,a.g2)("User"),ne=(0,a.g2)("WarningFilled"),oe=(0,a.g2)("el-radio-button"),de=(0,a.g2)("el-radio-group"),ue=(0,a.g2)("el-tag"),ce=(0,a.g2)("el-divider"),he=(0,a.g2)("el-progress"),me=(0,a.g2)("el-tab-pane"),pe=(0,a.g2)("el-tabs"),ge=(0,a.g2)("el-dialog");return(0,a.uX)(),(0,a.CE)("div",r,[(0,a.bF)(ae,{class:"config-panel",shadow:"hover"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",i,[t[22]||(t[22]=(0,a.Lk)("h3",null,"性能测试执行",-1)),(0,a.bF)(j,null,{default:(0,a.k6)(()=>[(0,a.bF)(G,{type:B.isRunning?"danger":"primary",loading:B.isStarting,onClick:O.toggleTest,disabled:!B.selectedTask},{default:(0,a.k6)(()=>[(0,a.bF)(N,null,{default:(0,a.k6)(()=>[B.isRunning?((0,a.uX)(),(0,a.Wv)(K,{key:1})):((0,a.uX)(),(0,a.Wv)(A,{key:0}))]),_:1}),(0,a.eW)(" "+(0,l.v_)(B.isRunning?"停止测试":"开始测试"),1)]),_:1},8,["type","loading","onClick","disabled"]),(0,a.bF)(G,{onClick:t[0]||(t[0]=e=>B.showConfigDialog=!0),disabled:B.isRunning},{default:(0,a.k6)(()=>[(0,a.bF)(N,null,{default:(0,a.k6)(()=>[(0,a.bF)(J)]),_:1}),t[21]||(t[21]=(0,a.eW)(" 配置 "))]),_:1,__:[21]},8,["disabled"])]),_:1})])]),default:(0,a.k6)(()=>[(0,a.bF)(se,{model:B.testConfig,"label-width":"120px",size:"default"},{default:(0,a.k6)(()=>[(0,a.bF)(Z,{gutter:20},{default:(0,a.k6)(()=>[(0,a.bF)(Y,{span:8},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"选择任务"},{default:(0,a.k6)(()=>[(0,a.bF)(H,{modelValue:B.selectedTask,"onUpdate:modelValue":t[1]||(t[1]=e=>B.selectedTask=e),placeholder:"请选择性能任务",disabled:B.isRunning,style:{width:"100%"}},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(B.tasks,e=>((0,a.uX)(),(0,a.Wv)(q,{key:e.id,label:e.taskName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),(0,a.bF)(Y,{span:8},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"测试环境"},{default:(0,a.k6)(()=>[(0,a.bF)(H,{modelValue:B.testConfig.env_id,"onUpdate:modelValue":t[2]||(t[2]=e=>B.testConfig.env_id=e),placeholder:"请选择测试环境",disabled:B.isRunning,style:{width:"100%"}},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(B.environments,e=>((0,a.uX)(),(0,a.Wv)(q,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),(0,a.bF)(Y,{span:8},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"执行模式"},{default:(0,a.k6)(()=>[(0,a.bF)(H,{modelValue:B.testConfig.mode,"onUpdate:modelValue":t[3]||(t[3]=e=>B.testConfig.mode=e),disabled:B.isRunning,style:{width:"100%"}},{default:(0,a.k6)(()=>[(0,a.bF)(q,{label:"单机模式",value:"single"}),(0,a.bF)(q,{label:"分布式模式",value:"distributed"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),(0,a.bF)(Z,{gutter:20},{default:(0,a.k6)(()=>[(0,a.bF)(Y,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"并发用户数"},{default:(0,a.k6)(()=>[(0,a.bF)(ee,{modelValue:B.testConfig.users,"onUpdate:modelValue":t[4]||(t[4]=e=>B.testConfig.users=e),min:1,max:1e4,disabled:B.isRunning,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),(0,a.bF)(Y,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"启动速率"},{default:(0,a.k6)(()=>[(0,a.bF)(ee,{modelValue:B.testConfig.spawn_rate,"onUpdate:modelValue":t[5]||(t[5]=e=>B.testConfig.spawn_rate=e),min:1,max:100,disabled:B.isRunning,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),(0,a.bF)(Y,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"持续时间(分钟)"},{default:(0,a.k6)(()=>[(0,a.bF)(ee,{modelValue:B.testConfig.duration,"onUpdate:modelValue":t[6]||(t[6]=e=>B.testConfig.duration=e),min:1,max:1440,disabled:B.isRunning,style:{width:"100%"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),(0,a.bF)(Y,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"报告名称"},{default:(0,a.k6)(()=>[(0,a.bF)(te,{modelValue:B.testConfig.report_name,"onUpdate:modelValue":t[7]||(t[7]=e=>B.testConfig.report_name=e),placeholder:"自动生成",disabled:B.isRunning},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),(0,a.bF)(Z,{gutter:20,style:{"margin-top":"20px"}},{default:(0,a.k6)(()=>[(0,a.bF)(Y,{span:18},{default:(0,a.k6)(()=>[(0,a.Lk)("div",n,[(0,a.bF)(ae,{class:"metric-card",shadow:"hover"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",o,[(0,a.Lk)("div",d,[(0,a.bF)(N,null,{default:(0,a.k6)(()=>[(0,a.bF)(le)]),_:1})]),(0,a.Lk)("div",u,[(0,a.Lk)("div",c,(0,l.v_)(B.currentMetrics.tps||0),1),t[23]||(t[23]=(0,a.Lk)("div",{class:"metric-label"},"TPS",-1))])])]),_:1}),(0,a.bF)(ae,{class:"metric-card",shadow:"hover"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",h,[(0,a.Lk)("div",m,[(0,a.bF)(N,null,{default:(0,a.k6)(()=>[(0,a.bF)(re)]),_:1})]),(0,a.Lk)("div",p,[(0,a.Lk)("div",g,(0,l.v_)(B.currentMetrics.avg_response_time||0)+"ms",1),t[24]||(t[24]=(0,a.Lk)("div",{class:"metric-label"},"平均响应时间",-1))])])]),_:1}),(0,a.bF)(ae,{class:"metric-card",shadow:"hover"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",b,[(0,a.Lk)("div",_,[(0,a.bF)(N,null,{default:(0,a.k6)(()=>[(0,a.bF)(ie)]),_:1})]),(0,a.Lk)("div",k,[(0,a.Lk)("div",f,(0,l.v_)(B.currentMetrics.users||0),1),t[25]||(t[25]=(0,a.Lk)("div",{class:"metric-label"},"活跃用户",-1))])])]),_:1}),(0,a.bF)(ae,{class:"metric-card",shadow:"hover"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",v,[(0,a.Lk)("div",{class:(0,l.C4)(["metric-icon error-icon",O.getErrorRateClass(B.currentMetrics.error_rate)])},[(0,a.bF)(N,null,{default:(0,a.k6)(()=>[(0,a.bF)(ne)]),_:1})],2),(0,a.Lk)("div",F,[(0,a.Lk)("div",C,(0,l.v_)((B.currentMetrics.error_rate||0).toFixed(2))+"%",1),t[26]||(t[26]=(0,a.Lk)("div",{class:"metric-label"},"错误率",-1))])])]),_:1})]),(0,a.bF)(ae,{style:{"margin-top":"20px"},shadow:"hover"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",w,[t[31]||(t[31]=(0,a.Lk)("h4",null,"实时性能趋势",-1)),(0,a.bF)(de,{modelValue:B.chartType,"onUpdate:modelValue":t[8]||(t[8]=e=>B.chartType=e),size:"small"},{default:(0,a.k6)(()=>[(0,a.bF)(oe,{label:"tps"},{default:(0,a.k6)(()=>t[27]||(t[27]=[(0,a.eW)("TPS")])),_:1,__:[27]}),(0,a.bF)(oe,{label:"response_time"},{default:(0,a.k6)(()=>t[28]||(t[28]=[(0,a.eW)("响应时间")])),_:1,__:[28]}),(0,a.bF)(oe,{label:"users"},{default:(0,a.k6)(()=>t[29]||(t[29]=[(0,a.eW)("用户数")])),_:1,__:[29]}),(0,a.bF)(oe,{label:"error_rate"},{default:(0,a.k6)(()=>t[30]||(t[30]=[(0,a.eW)("错误率")])),_:1,__:[30]})]),_:1},8,["modelValue"])])]),default:(0,a.k6)(()=>[(0,a.Lk)("div",y,null,512)]),_:1})]),_:1}),(0,a.bF)(Y,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(ae,{class:"status-panel",shadow:"hover"},{header:(0,a.k6)(()=>t[32]||(t[32]=[(0,a.Lk)("h4",null,"测试状态",-1)])),default:(0,a.k6)(()=>[(0,a.Lk)("div",T,[t[33]||(t[33]=(0,a.Lk)("span",{class:"status-label"},"测试状态:",-1)),(0,a.bF)(ue,{type:O.getStatusType(B.testStatus)},{default:(0,a.k6)(()=>[(0,a.eW)((0,l.v_)(O.getStatusText(B.testStatus)),1)]),_:1},8,["type"])]),(0,a.Lk)("div",V,[t[34]||(t[34]=(0,a.Lk)("span",{class:"status-label"},"运行时长:",-1)),(0,a.Lk)("span",null,(0,l.v_)(O.formatDuration(B.testDuration)),1)]),(0,a.Lk)("div",S,[t[35]||(t[35]=(0,a.Lk)("span",{class:"status-label"},"开始时间:",-1)),(0,a.Lk)("span",null,(0,l.v_)(O.formatTime(B.testStartTime)),1)]),(0,a.Lk)("div",L,[t[36]||(t[36]=(0,a.Lk)("span",{class:"status-label"},"预计结束:",-1)),(0,a.Lk)("span",null,(0,l.v_)(O.formatTime(O.estimatedEndTime)),1)]),(0,a.bF)(ce),(0,a.Lk)("div",R,[(0,a.Lk)("div",x,[t[37]||(t[37]=(0,a.eW)(" 测试进度 ")),(0,a.Lk)("span",W,(0,l.v_)(B.testProgress)+"%",1)]),(0,a.bF)(he,{percentage:B.testProgress,status:100===B.testProgress?"success":"primary","stroke-width":8},null,8,["percentage","status"])]),(0,a.bF)(ce),(0,a.Lk)("div",D,[(0,a.bF)(G,{size:"small",type:"primary",onClick:O.viewReport,disabled:!B.currentReportId},{default:(0,a.k6)(()=>t[38]||(t[38]=[(0,a.eW)(" 查看报告 ")])),_:1,__:[38]},8,["onClick","disabled"]),(0,a.bF)(G,{size:"small",type:"info",onClick:O.exportResults,disabled:!B.currentReportId},{default:(0,a.k6)(()=>t[39]||(t[39]=[(0,a.eW)(" 导出结果 ")])),_:1,__:[39]},8,["onClick","disabled"])])]),_:1}),(0,a.bF)(ae,{style:{"margin-top":"20px"},shadow:"hover"},{header:(0,a.k6)(()=>t[40]||(t[40]=[(0,a.Lk)("h4",null,"系统资源",-1)])),default:(0,a.k6)(()=>[(0,a.Lk)("div",M,[t[41]||(t[41]=(0,a.Lk)("div",{class:"resource-label"},"CPU使用率",-1)),(0,a.bF)(he,{percentage:B.systemResources.cpu,status:O.getCpuStatus(B.systemResources.cpu),"show-text":!1,"stroke-width":6},null,8,["percentage","status"]),(0,a.Lk)("span",U,(0,l.v_)(B.systemResources.cpu)+"%",1)]),(0,a.Lk)("div",$,[t[42]||(t[42]=(0,a.Lk)("div",{class:"resource-label"},"内存使用率",-1)),(0,a.bF)(he,{percentage:B.systemResources.memory,status:O.getMemoryStatus(B.systemResources.memory),"show-text":!1,"stroke-width":6},null,8,["percentage","status"]),(0,a.Lk)("span",P,(0,l.v_)(B.systemResources.memory)+"%",1)]),(0,a.Lk)("div",I,[t[43]||(t[43]=(0,a.Lk)("div",{class:"resource-label"},"网络IO",-1)),(0,a.Lk)("div",E,[(0,a.Lk)("span",null,"↑ "+(0,l.v_)(O.formatBytes(B.systemResources.network_sent)),1),(0,a.Lk)("span",null,"↓ "+(0,l.v_)(O.formatBytes(B.systemResources.network_recv)),1)])])]),_:1})]),_:1})]),_:1}),(0,a.bF)(ge,{modelValue:B.showConfigDialog,"onUpdate:modelValue":t[20]||(t[20]=e=>B.showConfigDialog=e),title:"高级配置",width:"60%"},{footer:(0,a.k6)(()=>[(0,a.Lk)("span",X,[(0,a.bF)(G,{onClick:t[19]||(t[19]=e=>B.showConfigDialog=!1)},{default:(0,a.k6)(()=>t[47]||(t[47]=[(0,a.eW)("取消")])),_:1,__:[47]}),(0,a.bF)(G,{type:"primary",onClick:O.saveAdvancedConfig},{default:(0,a.k6)(()=>t[48]||(t[48]=[(0,a.eW)("保存配置")])),_:1,__:[48]},8,["onClick"])])]),default:(0,a.k6)(()=>[(0,a.bF)(pe,{modelValue:B.configTab,"onUpdate:modelValue":t[18]||(t[18]=e=>B.configTab=e)},{default:(0,a.k6)(()=>[(0,a.bF)(me,{label:"基础配置",name:"basic"},{default:(0,a.k6)(()=>[(0,a.bF)(se,{model:B.advancedConfig,"label-width":"120px"},{default:(0,a.k6)(()=>[(0,a.bF)(Z,{gutter:20},{default:(0,a.k6)(()=>[(0,a.bF)(Y,{span:12},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"思考时间(秒)"},{default:(0,a.k6)(()=>[(0,a.bF)(ee,{modelValue:B.advancedConfig.think_time,"onUpdate:modelValue":t[9]||(t[9]=e=>B.advancedConfig.think_time=e),min:0,max:60},null,8,["modelValue"])]),_:1})]),_:1}),(0,a.bF)(Y,{span:12},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"超时时间(秒)"},{default:(0,a.k6)(()=>[(0,a.bF)(ee,{modelValue:B.advancedConfig.timeout,"onUpdate:modelValue":t[10]||(t[10]=e=>B.advancedConfig.timeout=e),min:1,max:300},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),(0,a.bF)(Z,{gutter:20},{default:(0,a.k6)(()=>[(0,a.bF)(Y,{span:12},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"重试次数"},{default:(0,a.k6)(()=>[(0,a.bF)(ee,{modelValue:B.advancedConfig.retry_count,"onUpdate:modelValue":t[11]||(t[11]=e=>B.advancedConfig.retry_count=e),min:0,max:10},null,8,["modelValue"])]),_:1})]),_:1}),(0,a.bF)(Y,{span:12},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"日志级别"},{default:(0,a.k6)(()=>[(0,a.bF)(H,{modelValue:B.advancedConfig.log_level,"onUpdate:modelValue":t[12]||(t[12]=e=>B.advancedConfig.log_level=e),style:{width:"100%"}},{default:(0,a.k6)(()=>[(0,a.bF)(q,{label:"关闭",value:"off"}),(0,a.bF)(q,{label:"错误",value:"error"}),(0,a.bF)(q,{label:"警告",value:"warning"}),(0,a.bF)(q,{label:"信息",value:"info"}),(0,a.bF)(q,{label:"调试",value:"debug"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),(0,a.bF)(me,{label:"分布式配置",name:"distributed"},{default:(0,a.k6)(()=>[(0,a.bF)(se,{model:B.advancedConfig,"label-width":"120px"},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"主节点"},{default:(0,a.k6)(()=>[(0,a.bF)(H,{modelValue:B.advancedConfig.master_server,"onUpdate:modelValue":t[13]||(t[13]=e=>B.advancedConfig.master_server=e),style:{width:"100%"}},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(B.servers,e=>((0,a.uX)(),(0,a.Wv)(q,{key:e.id,label:`${e.name} (${e.host_ip})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(Q,{label:"工作节点"},{default:(0,a.k6)(()=>[(0,a.bF)(H,{modelValue:B.advancedConfig.worker_servers,"onUpdate:modelValue":t[14]||(t[14]=e=>B.advancedConfig.worker_servers=e),multiple:"",style:{width:"100%"}},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(B.servers,e=>((0,a.uX)(),(0,a.Wv)(q,{key:e.id,label:`${e.name} (${e.host_ip})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),(0,a.bF)(me,{label:"告警设置",name:"alerts"},{default:(0,a.k6)(()=>[(0,a.bF)(se,{model:B.advancedConfig,"label-width":"120px"},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{label:"响应时间告警"},{default:(0,a.k6)(()=>[(0,a.bF)(ee,{modelValue:B.advancedConfig.response_time_threshold,"onUpdate:modelValue":t[15]||(t[15]=e=>B.advancedConfig.response_time_threshold=e),min:0},{append:(0,a.k6)(()=>t[44]||(t[44]=[(0,a.eW)("ms")])),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(Q,{label:"错误率告警"},{default:(0,a.k6)(()=>[(0,a.bF)(ee,{modelValue:B.advancedConfig.error_rate_threshold,"onUpdate:modelValue":t[16]||(t[16]=e=>B.advancedConfig.error_rate_threshold=e),min:0,max:100},{append:(0,a.k6)(()=>t[45]||(t[45]=[(0,a.eW)("%")])),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(Q,{label:"TPS告警"},{default:(0,a.k6)(()=>[(0,a.bF)(ee,{modelValue:B.advancedConfig.tps_threshold,"onUpdate:modelValue":t[17]||(t[17]=e=>B.advancedConfig.tps_threshold=e),min:0},{append:(0,a.k6)(()=>t[46]||(t[46]=[(0,a.eW)("TPS")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])])}s(44114),s(18111),s(7588);var B=s(57477),O=s(91006),A={name:"PerformanceExecutionOptimized",components:{VideoPlay:B.VideoPlay,VideoPause:B.VideoPause,Setting:B.Setting,TrendCharts:B.TrendCharts,Timer:B.Timer,User:B.User,WarningFilled:B.WarningFilled},data(){return{selectedTask:null,testConfig:{env_id:null,mode:"single",users:10,spawn_rate:2,duration:5,report_name:""},advancedConfig:{think_time:1,timeout:30,retry_count:0,log_level:"info",master_server:null,worker_servers:[],response_time_threshold:1e3,error_rate_threshold:5,tps_threshold:100},isRunning:!1,isStarting:!1,testStatus:"idle",testStartTime:null,testDuration:0,testProgress:0,currentReportId:null,currentMetrics:{tps:0,avg_response_time:0,users:0,error_rate:0},systemResources:{cpu:0,memory:0,network_sent:0,network_recv:0},chart:null,chartType:"tps",chartData:{times:[],tps:[],response_time:[],users:[],error_rate:[]},tasks:[],environments:[],servers:[],showConfigDialog:!1,configTab:"basic",wsConnection:null,updateTimer:null,guiUrl:"",webPort:8089}},computed:{estimatedEndTime(){if(!this.testStartTime||!this.testConfig.duration)return null;const e=new Date(this.testStartTime);return new Date(e.getTime()+60*this.testConfig.duration*1e3)}},async mounted(){await this.loadInitialData(),this.initChart(),this.startUpdateTimer()},beforeUnmount(){this.cleanup()},methods:{async loadInitialData(){try{const e=await this.$api.getPerformanceTasksForExecution();this.tasks=e.data.results||[];const t=await this.$api.getTestEnvironments();this.environments=t.data.results||[];const s=await this.$api.getServersForExecution();this.servers=s.data.results||[]}catch(e){console.error("加载初始数据失败:",e),this.$message.error("加载数据失败")}},async toggleTest(){this.isRunning?await this.stopTest():await this.startTest()},async startTest(){if(this.selectedTask){this.isStarting=!0;try{const e=await this.$api.runPerformanceTestOptimized(this.selectedTask,{...this.testConfig,...this.advancedConfig});this.isRunning=!0,this.testStatus="running",this.testStartTime=new Date,this.currentReportId=e.data.report_id,e.data.gui_url&&(this.guiUrl=e.data.gui_url,this.webPort=e.data.web_port,this.$message.success(`测试启动成功！GUI地址: ${e.data.gui_url}`)),this.connectWebSocket(),this.$message.success("性能测试已开始")}catch(e){console.error("启动测试失败:",e),this.$message.error("启动测试失败: "+(e.response?.data?.message||e.message))}finally{this.isStarting=!1}}else this.$message.warning("请选择要执行的任务")},async stopTest(){try{this.selectedTask&&await this.$api.stopPerformanceTestOptimized(this.selectedTask),this.isRunning=!1,this.testStatus="stopped",this.disconnectWebSocket(),this.$message.success("性能测试已停止")}catch(e){console.error("停止测试失败:",e),this.$message.error("停止测试失败")}},connectWebSocket(){this.wsConnection&&this.wsConnection.close();const e="https:"===window.location.protocol?"wss:":"ws:",t=`${e}//${window.location.host}/ws/performance/monitor/${this.selectedTask}/`;this.wsConnection=new WebSocket(t),this.wsConnection.onopen=()=>{console.log("WebSocket连接已建立"),this.wsConnection.send(JSON.stringify({type:"start_monitoring"}))},this.wsConnection.onmessage=e=>{const t=JSON.parse(e.data);this.handleWebSocketMessage(t)},this.wsConnection.onerror=e=>{console.error("WebSocket连接错误:",e)},this.wsConnection.onclose=()=>{console.log("WebSocket连接已关闭"),this.isRunning&&setTimeout(()=>{this.connectWebSocket()},5e3)}},disconnectWebSocket(){this.wsConnection&&(this.wsConnection.close(),this.wsConnection=null)},handleWebSocketMessage(e){switch(e.type){case"performance_update":this.updateMetrics(e.data);break;case"test_completed":this.handleTestCompleted(e.data);break;case"test_failed":this.handleTestFailed(e.error);break;case"current_status":this.updateCurrentStatus(e.data);break}},updateMetrics(e){this.currentMetrics={tps:e.avg_tps||0,avg_response_time:e.avg_response_time||0,users:e.current_users||0,error_rate:e.error_rate||0};const t=(new Date).toLocaleTimeString();this.chartData.times.push(t),this.chartData.tps.push(e.avg_tps||0),this.chartData.response_time.push(e.avg_response_time||0),this.chartData.users.push(e.current_users||0),this.chartData.error_rate.push(e.error_rate||0),Object.keys(this.chartData).forEach(e=>{this.chartData[e].length>50&&this.chartData[e].shift()}),this.updateChart()},updateCurrentStatus(e){e&&(this.currentMetrics={tps:e.avg_tps||0,avg_response_time:e.avg_response_time||0,users:e.current_users||0,error_rate:e.error_rate||0})},handleTestCompleted(e){this.isRunning=!1,this.testStatus="completed",this.testProgress=100,this.$message.success("性能测试已完成"),this.disconnectWebSocket()},handleTestFailed(e){this.isRunning=!1,this.testStatus="failed",this.$message.error("性能测试失败: "+e),this.disconnectWebSocket()},initChart(){this.$nextTick(()=>{this.$refs.chartContainer&&(this.chart=O.Ts(this.$refs.chartContainer),this.updateChart())})},updateChart(){if(!this.chart)return;const e={title:{text:this.getChartTitle(),left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"axis"},xAxis:{type:"category",data:this.chartData.times,axisLabel:{interval:"auto",rotate:45}},yAxis:{type:"value",name:this.getChartUnit()},series:[{data:this.chartData[this.chartType],type:"line",smooth:!0,areaStyle:{opacity:.3},itemStyle:{color:this.getChartColor()}}],grid:{left:"3%",right:"4%",bottom:"15%",containLabel:!0}};this.chart.setOption(e)},getChartTitle(){const e={tps:"TPS趋势",response_time:"响应时间趋势",users:"用户数趋势",error_rate:"错误率趋势"};return e[this.chartType]||""},getChartUnit(){const e={tps:"TPS",response_time:"毫秒(ms)",users:"用户数",error_rate:"百分比(%)"};return e[this.chartType]||""},getChartColor(){const e={tps:"#409eff",response_time:"#67c23a",users:"#e6a23c",error_rate:"#f56c6c"};return e[this.chartType]||"#409eff"},startUpdateTimer(){this.updateTimer=setInterval(()=>{if(this.isRunning&&this.testStartTime){if(this.testDuration=Math.floor((Date.now()-new Date(this.testStartTime).getTime())/1e3),this.testConfig.duration){const e=60*this.testConfig.duration;this.testProgress=Math.min(100,this.testDuration/e*100)}this.testProgress>=100&&this.isRunning&&this.stopTest()}this.updateSystemResources()},1e3)},updateSystemResources(){this.systemResources={cpu:Math.max(0,Math.min(100,this.systemResources.cpu+10*(Math.random()-.5))),memory:Math.max(0,Math.min(100,this.systemResources.memory+5*(Math.random()-.5))),network_sent:this.systemResources.network_sent+1e6*Math.random(),network_recv:this.systemResources.network_recv+1e6*Math.random()}},cleanup(){this.disconnectWebSocket(),this.updateTimer&&clearInterval(this.updateTimer),this.chart&&this.chart.dispose()},saveAdvancedConfig(){this.showConfigDialog=!1,this.$message.success("配置已保存")},viewReport(){this.currentReportId&&this.$router.push({name:"PerformanceResult-Detail",params:{id:this.currentReportId}})},exportResults(){this.currentReportId&&this.$api.exportTaskReport(this.currentReportId)},getStatusType(e){const t={idle:"info",running:"primary",completed:"success",failed:"danger",stopped:"warning"};return t[e]||"info"},getStatusText(e){const t={idle:"待运行",running:"运行中",completed:"已完成",failed:"失败",stopped:"已停止"};return t[e]||"未知"},getErrorRateClass(e){return e>5?"error-high":e>1?"error-medium":"error-low"},getCpuStatus(e){return e>80?"exception":e>60?"warning":"success"},getMemoryStatus(e){return e>85?"exception":e>70?"warning":"success"},formatDuration(e){if(!e)return"0s";const t=Math.floor(e/3600),s=Math.floor(e%3600/60),a=e%60;return`${t}h ${s}m ${a}s`},formatTime(e){return e?new Date(e).toLocaleString():"--"},formatBytes(e){if(!e)return"0B";const t=1024,s=["B","KB","MB","GB"],a=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,a)).toFixed(2))+s[a]}},watch:{chartType(){this.updateChart()}}},K=s(71241);const N=(0,K.A)(A,[["render",z],["__scopeId","data-v-1cb4d7ec"]]);var G=N}}]);
//# sourceMappingURL=519.540f0f79.js.map