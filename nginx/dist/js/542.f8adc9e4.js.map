{"version": 3, "file": "js/542.f8adc9e4.js", "mappings": "0NACMA,MAAM,oB,SACSA,MAAM,uB,GAEnBA,MAAM,c,GACLA,MAAM,e,GAILA,MAAM,iB,GACLA,MAAM,gB,GACNA,MAAM,e,GAGRA,MAAM,gB,GAGHA,MAAM,e,GAMLA,MAAM,qB,GAEJA,MAAM,kB,GACJA,MAAM,mB,GAEJA,MAAM,a,GACJA,MAAM,a,GACNA,MAAM,W,GAKVA,MAAM,c,GACJA,MAAM,mB,GACJA,MAAM,a,GACNA,MAAM,a,GACJA,MAAM,c,GAKVA,MAAM,qB,GACJA,MAAM,a,GACNA,MAAM,a,GACJA,MAAM,c,GAKVA,MAAM,kB,GACJA,MAAM,a,GACNA,MAAM,a,GACJA,MAAM,c,GAKVA,MAAM,mB,GACJA,MAAM,a,GACNA,MAAM,a,GACJA,MAAM,c,GAQZA,MAAM,iB,GACJA,MAAM,c,GASJA,MAAM,gBAAgBC,IAAI,U,GAE5BD,MAAM,c,GAIJA,MAAM,gBAAgBC,IAAI,U,GAMhCD,MAAM,gB,GACJA,MAAM,iB,GAEJA,MAAM,e,iBAUDA,MAAM,a,iBAcLA,MAAM,gB,GAGNA,MAAM,c,GACJA,MAAM,c,GAKRA,MAAM,gB,GAKRA,MAAM,gB,IAQEA,MAAM,e,IAONA,MAAM,a,kWApJAE,GAAAC,S,WAApCC,EAAAA,EAAAA,IAoMM,MApMNC,EAoMM,CAnMMH,GAAAI,S,WAAXF,EAAAA,EAAAA,IAkMM,MAlMNG,EAkMM,EAhMLC,EAAAA,EAAAA,IAgBM,MAhBNC,EAgBM,EAfLD,EAAAA,EAAAA,IAQM,MARNE,EAQM,EAPLC,EAAAA,EAAAA,IAEYC,GAAA,CAFDC,KAAK,OAAOb,MAAM,WAAYc,QAAOC,GAAAC,Q,kBAC/C,IAA2B,EAA3BL,EAAAA,EAAAA,IAA2BM,GAAA,M,iBAAlB,IAAQ,EAARN,EAAAA,EAAAA,IAAQO,M,6BAElBV,EAAAA,EAAAA,IAGM,MAHNW,EAGM,EAFLX,EAAAA,EAAAA,IAA4D,MAA5DY,EAA0B,UAAMC,EAAAA,EAAAA,IAAGC,EAAAC,OAAOC,OAAOC,IAAE,IACnDjB,EAAAA,EAAAA,IAAqE,MAArEkB,GAAqEL,EAAAA,EAAAA,IAAzCC,EAAAK,OAAOC,MAAM1B,GAAAC,OAAO0B,cAAW,QAG7DrB,EAAAA,EAAAA,IAKM,MALNsB,EAKM,EAJLtB,EAAAA,EAAAA,IAGM,OAHDR,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,eAAuBhB,GAAAiB,oB,cACjCxB,EAAAA,EAAAA,IAAoC,QAA9BR,MAAM,eAAc,OAAG,KAC7BQ,EAAAA,EAAAA,IAA6D,OAA7DyB,GAA6DZ,EAAAA,EAAAA,IAAhCnB,GAAAC,OAAO+B,WAAa,GAAI,IAAC,I,QAItDvB,EAAAA,EAAAA,IA8KewB,GAAA,CA9KAC,MAAO,CAAAC,OAAA,qBAAAC,UAAA,UAAkD,C,iBAEtE,IA2KM,EA3KN9B,EAAAA,EAAAA,IA2KM,MA3KN+B,EA2KM,EAzKJ/B,EAAAA,EAAAA,IAiEM,MAjENgC,EAiEM,EAhEJhC,EAAAA,EAAAA,IAMM,MANNiC,EAMM,C,aALJjC,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAGM,MAHNkC,EAGM,EAFJlC,EAAAA,EAAAA,IAA8D,MAA9DmC,GAA8DtB,EAAAA,EAAAA,IAApCnB,GAAAC,OAAOyC,WAAa,SAAJ,IAC1CpC,EAAAA,EAAAA,IAA0D,MAA1DqC,GAA0DxB,EAAAA,EAAAA,IAAlCnB,GAAAC,OAAO2C,UAAY,QAAJ,QAK3CtC,EAAAA,EAAAA,IAiCM,MAjCNuC,EAiCM,EAhCJvC,EAAAA,EAAAA,IAOM,MAPNwC,EAOM,EANJxC,EAAAA,EAAAA,IAAgE,MAAhEyC,EAAgE,EAAzCtC,EAAAA,EAAAA,IAAmCM,GAAA,M,iBAA1B,IAAgB,EAAhBN,EAAAA,EAAAA,IAAgBuC,M,SAChD1C,EAAAA,EAAAA,IAGM,MAHN2C,EAGM,EAFJ3C,EAAAA,EAAAA,IAAgF,MAAhF4C,GAAgF/B,EAAAA,EAAAA,IAArDnB,GAAAI,OAAO+C,SAAWnD,GAAAI,OAAO+C,QAAQC,QAAU,GAAJ,G,aAClE9C,EAAAA,EAAAA,IAAkC,OAA7BR,MAAM,cAAa,QAAI,M,aAE9BQ,EAAAA,EAAAA,IAA6B,OAAxBR,MAAM,aAAW,aAExBQ,EAAAA,EAAAA,IAOM,MAPN+C,EAOM,EANJ/C,EAAAA,EAAAA,IAAiE,MAAjEgD,EAAiE,EAA1C7C,EAAAA,EAAAA,IAAoCM,GAAA,M,iBAA3B,IAAiB,EAAjBN,EAAAA,EAAAA,IAAiB8C,M,SACjDjD,EAAAA,EAAAA,IAGM,MAHNkD,EAGM,EAFJlD,EAAAA,EAAAA,IAAuD,MAAvDmD,GAAuDtC,EAAAA,EAAAA,IAA5BN,GAAA6C,aAAaN,QAAM,G,aAC9C9C,EAAAA,EAAAA,IAAkC,OAA7BR,MAAM,cAAa,QAAI,M,aAE9BQ,EAAAA,EAAAA,IAA6B,OAAxBR,MAAM,aAAW,aAExBQ,EAAAA,EAAAA,IAOM,MAPNqD,EAOM,EANJrD,EAAAA,EAAAA,IAA2D,MAA3DsD,EAA2D,EAApCnD,EAAAA,EAAAA,IAA8BM,GAAA,M,iBAArB,IAAW,EAAXN,EAAAA,EAAAA,IAAWoD,M,SAC3CvD,EAAAA,EAAAA,IAGM,MAHNwD,EAGM,EAFJxD,EAAAA,EAAAA,IAAoD,MAApDyD,GAAoD5C,EAAAA,EAAAA,IAAzBN,GAAAmD,UAAUZ,QAAM,G,aAC3C9C,EAAAA,EAAAA,IAAkC,OAA7BR,MAAM,cAAa,QAAI,M,aAE9BQ,EAAAA,EAAAA,IAA6B,OAAxBR,MAAM,aAAW,aAExBQ,EAAAA,EAAAA,IAOM,MAPN2D,EAOM,EANJ3D,EAAAA,EAAAA,IAAqE,MAArE4D,EAAqE,EAA9CzD,EAAAA,EAAAA,IAAwCM,GAAA,M,iBAA/B,IAAqB,EAArBN,EAAAA,EAAAA,IAAqB0D,M,SACrD7D,EAAAA,EAAAA,IAGM,MAHN8D,EAGM,EAFJ9D,EAAAA,EAAAA,IAAqD,MAArD+D,GAAqDlD,EAAAA,EAAAA,IAA1BN,GAAAyD,WAAWlB,QAAM,G,aAC5C9C,EAAAA,EAAAA,IAAkC,OAA7BR,MAAM,cAAa,QAAI,M,aAE9BQ,EAAAA,EAAAA,IAA6B,OAAxBR,MAAM,aAAW,eAK1BQ,EAAAA,EAAAA,IAkBM,MAlBNiE,EAkBM,EAjBJjE,EAAAA,EAAAA,IAUM,MAVNkE,EAUM,C,eATJlE,EAAAA,EAAAA,IAOM,OAPDR,MAAM,gBAAc,EACvBQ,EAAAA,EAAAA,IAAa,UAAT,SACJA,EAAAA,EAAAA,IAIM,OAJDR,MAAM,gBAAc,EACvBQ,EAAAA,EAAAA,IAA2C,QAArCR,MAAM,uBAAsB,OAClCQ,EAAAA,EAAAA,IAAwC,QAAlCR,MAAM,oBAAmB,OAC/BQ,EAAAA,EAAAA,IAAyC,QAAnCR,MAAM,qBAAoB,U,KAGpCQ,EAAAA,EAAAA,IAA8C,MAA9CmE,EAA8C,aAEhDnE,EAAAA,EAAAA,IAKM,MALNoE,EAKM,C,eAJJpE,EAAAA,EAAAA,IAEM,OAFDR,MAAM,gBAAc,EACvBQ,EAAAA,EAAAA,IAAa,UAAT,U,KAENA,EAAAA,EAAAA,IAA8C,MAA9CqE,EAA8C,iBAMpDrE,EAAAA,EAAAA,IAoGM,MApGNsE,EAoGM,EAnGJtE,EAAAA,EAAAA,IAeM,MAfNuE,EAeM,C,eAdJvE,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAYM,MAZNwE,EAYM,G,aAXJ5E,EAAAA,EAAAA,IAUM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IATUhF,GAAAiF,WAAPC,K,WADThF,EAAAA,EAAAA,IAUM,OARHiF,IAAKD,EAAIE,MACVtF,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,aAAY,CAAAwD,OACArF,GAAAsF,gBAAkBJ,EAAIE,MAAK,CAAGF,EAAIE,QAAQ,KAC3DxE,QAAK2E,GAAE1E,GAAA2E,mBAAmBN,EAAIE,Q,EAE/B3E,EAAAA,EAAAA,IAAgEM,GAAA,CAAvDjB,MAAM,YAAU,C,iBAAC,IAA4B,G,WAA5B2F,EAAAA,EAAAA,KAA4BC,EAAAA,EAAAA,IAAZR,EAAIS,U,YAC9CrF,EAAAA,EAAAA,IAA4B,aAAAa,EAAAA,EAAAA,IAAnB+D,EAAIU,OAAK,IAClBtF,EAAAA,EAAAA,IAA2D,OAA3DuF,GAA2D1E,EAAAA,EAAAA,IAAhCN,GAAAiF,YAAYZ,EAAIE,QAAK,I,mBAKtD3E,EAAAA,EAAAA,IAiFewB,GAAA,CAjFDnC,MAAM,cAAY,C,iBAC9B,IA+EmB,EA/EnBW,EAAAA,EAAAA,IA+EmBsF,EAAAA,EAAA,CA/EDC,KAAK,cAAY,C,iBAE/B,IAAwC,G,aAD1C9F,EAAAA,EAAAA,IA6EM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA5EqBhF,GAAAiG,eAAc,CAA/BC,EAAOC,M,WADjBjG,EAAAA,EAAAA,IA6EM,OA3EHiF,IAAKe,EAAMF,KAAOG,EACnBrG,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,aAAY,CAAAuE,SACEpG,GAAAqG,eAAeC,SAASH,O,EAE5C7F,EAAAA,EAAAA,IAaM,OAbDR,MAAM,eAAgBc,QAAK2E,GAAE1E,GAAA0F,YAAYJ,I,EAC5C7F,EAAAA,EAAAA,IAEM,MAFNkG,EAEM,EADJ/F,EAAAA,EAAAA,IAAgEM,GAAA,M,iBAAvD,IAA6C,G,WAA7C0E,EAAAA,EAAAA,KAA6CC,EAAAA,EAAAA,IAA7B7E,GAAA4F,aAAaP,EAAMQ,Y,cAE9CpG,EAAAA,EAAAA,IAKM,MALNqG,EAKM,EAJJrG,EAAAA,EAAAA,IAAyD,MAAzDsG,GAAyDzF,EAAAA,EAAAA,IAA9B+E,EAAMF,MAAQ,SAAJ,IACrC1F,EAAAA,EAAAA,IAEM,OAFDR,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,cAAsBhB,GAAAgG,mBAAmBX,EAAMQ,W,QACrD7F,GAAAiG,kBAAkBZ,EAAMQ,QAAK,MAGpCpG,EAAAA,EAAAA,IAEM,MAFNyG,EAEM,EADJtG,EAAAA,EAAAA,IAAqFM,GAAA,CAA3EjB,OAAK+B,EAAAA,EAAAA,IAAA,CAAAmF,OAAYhH,GAAAqG,eAAeC,SAASH,M,kBAAU,IAAc,EAAd1F,EAAAA,EAAAA,IAAcwG,M,uCAI/E3G,EAAAA,EAAAA,IAuDM,MAvDN4G,EAuDM,EAtDJzG,EAAAA,EAAAA,IAqDW0G,GAAA,CApDRC,KAAMlB,EAAMmB,OAAS,GACtBvH,MAAM,aACNwH,KAAK,S,kBAEL,IAMkB,EANlB7G,EAAAA,EAAAA,IAMkB8G,GAAA,CAND5G,KAAK,UAAQ,CACjB6G,SAAOC,EAAAA,EAAAA,IAGVC,GAHiB,EACvBpH,EAAAA,EAAAA,IAEM,MAFNqH,GAEM,EADJlH,EAAAA,EAAAA,IAAuCmH,GAAA,CAA7BC,OAAQH,EAAMI,K,8BAI9BrH,EAAAA,EAAAA,IAOkB8G,GAAA,CAPD3B,MAAM,OAAO,YAAU,O,CAC3B4B,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpH,EAAAA,EAAAA,IAGM,MAHNyH,GAGM,EAFJtH,EAAAA,EAAAA,IAAiEM,GAAA,M,iBAAxD,IAA8C,G,WAA9C0E,EAAAA,EAAAA,KAA8CC,EAAAA,EAAAA,IAA9B7E,GAAAmH,gBAAgBN,EAAMI,U,YAC/CxH,EAAAA,EAAAA,IAA4C,aAAAa,EAAAA,EAAAA,IAAnCuG,EAAMI,IAAI9B,MAAQ,SAAJ,O,OAI7BvF,EAAAA,EAAAA,IAUkB8G,GAAA,CAVD3B,MAAM,OAAOqC,MAAM,MAAMC,MAAM,U,CACnCV,SAAOC,EAAAA,EAAAA,IAOVC,GAPiB,CAEI,QAAnBA,EAAMI,IAAInH,MAAkB+G,EAAMI,IAAIK,S,WAD9CjI,EAAAA,EAAAA,IAMM,O,MAJJJ,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,eACE6F,EAAMI,IAAIK,OAAOC,kB,QAEtBV,EAAMI,IAAIK,QAAM,K,wBAIzB1H,EAAAA,EAAAA,IAUkB8G,GAAA,CAVD3B,MAAM,MAAMqC,MAAM,MAAMC,MAAM,U,CAClCV,SAAOC,EAAAA,EAAAA,IAOVC,GAPiB,CAEI,QAAnBA,EAAMI,IAAInH,WAA4C0H,IAA1BX,EAAMI,IAAIQ,c,WAD9CpI,EAAAA,EAAAA,IAMM,O,MAJJJ,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,cACEhB,GAAA0H,mBAAmBb,EAAMI,IAAIQ,iB,QAElCZ,EAAMI,IAAIQ,aAAW,K,wBAI9B7H,EAAAA,EAAAA,IAUkB8G,GAAA,CAVD3B,MAAM,KAAKqC,MAAM,MAAMC,MAAM,U,CACjCV,SAAOC,EAAAA,EAAAA,IAOVC,GAPiB,CAEfA,EAAMI,IAAIpB,Q,WADlBxG,EAAAA,EAAAA,IAMM,O,MAJJJ,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,eACEhB,GAAA2H,mBAAmBd,EAAMI,IAAIpB,W,QAElCgB,EAAMI,IAAIpB,OAAK,K,0DAlDM1G,GAAAqG,eAAeC,SAASH,O,4JAiF5E,IACCH,KAAM,aACNyC,WAAY,CACTC,QAAO,KACPC,KAAI,QACJC,aAAY,gBACZC,cAAa,iBACbC,QAAO,WACPC,kBAAiB,qBACjBC,WAAU,cACVC,KAAI,QACJC,WAAU,cACVC,IAAG,OACHC,YAAWA,GAAAA,IAEdhC,IAAAA,GACC,MAAO,CACNnH,OAAQ,KACRG,OAAQ,KACR6F,eAAgB,GAChBX,cAAe,MACfe,eAAgB,GAChBpB,WAAY,CACX,CAAEW,MAAO,OAAQR,MAAO,MAAOO,KAAM,QACrC,CAAEC,MAAO,OAAQR,MAAO,UAAWO,KAAM,iBACzC,CAAEC,MAAO,OAAQR,MAAO,OAAQO,KAAM,WACtC,CAAEC,MAAO,OAAQR,MAAO,QAASO,KAAM,sBAG1C,EACA0D,SAAU,CACT3F,YAAAA,GACC,OAAK4F,KAAKlJ,QAAWkJ,KAAKlJ,OAAO+C,QAC1BmG,KAAKlJ,OAAO+C,QAAQoG,OAAOC,GAAqB,YAAdA,EAAI9C,OADI,EAElD,EACA1C,SAAAA,GACC,OAAKsF,KAAKlJ,QAAWkJ,KAAKlJ,OAAO+C,QAC1BmG,KAAKlJ,OAAO+C,QAAQoG,OAAOC,GAAqB,SAAdA,EAAI9C,OADI,EAElD,EACApC,UAAAA,GACC,OAAKgF,KAAKlJ,QAAWkJ,KAAKlJ,OAAO+C,QAC1BmG,KAAKlJ,OAAO+C,QAAQoG,OAAOC,GAAqB,UAAdA,EAAI9C,OADI,EAElD,EACA5E,gBAAAA,GACC,IAAKwH,KAAKrJ,SAAWqJ,KAAKrJ,OAAO+B,UAAW,MAAO,OACnD,MAAMyH,EAAOC,WAAWJ,KAAKrJ,OAAO+B,WACpC,OAAIyH,GAAQ,GAAW,YACnBA,GAAQ,GAAW,OACnBA,GAAQ,GAAW,OAChB,MACR,GAEDE,QAAS,CACR7I,MAAAA,GACC8I,OAAOC,QAAQC,MAChB,EACA,mBAAMC,CAAcxI,GACnB,IACCyI,QAAQC,IAAI,cAAe1I,GAC3B,MAAM2I,QAAiBZ,KAAKa,KAAKC,cAAc7I,GAC/CyI,QAAQC,IAAI,WAAYC,GAEA,MAApBA,EAASG,SAERH,EAAS9C,MAAQ8C,EAAS9C,KAAKjE,QAClCmG,KAAKlJ,OAAS8J,EAAS9C,KAGf8C,EAAS9C,MAAQ8C,EAAS9C,KAAKkD,OACvChB,KAAKlJ,OAAS8J,EAAS9C,KAAKkD,MAG7BN,QAAQC,IAAI,YAAaX,KAAKlJ,QAE1BkJ,KAAKlJ,QAAUkJ,KAAKlJ,OAAO+C,SAC9BmG,KAAK9D,mBAAmB,OACxBwE,QAAQC,IAAI,YAAaX,KAAKrD,kBAE9B+D,QAAQO,MAAM,aACdC,GAAAA,GAAUD,MAAM,cAGnB,CAAE,MAAOA,GACRP,QAAQO,MAAM,YAAaA,GAC3BC,GAAAA,GAAUD,MAAM,WACjB,CACD,EACA,mBAAME,CAAclJ,GACnB,IACCyI,QAAQC,IAAI,cAAe1I,GAC3B,MAAM2I,QAAiBZ,KAAKa,KAAKM,cAAclJ,GAC/CyI,QAAQC,IAAI,WAAYC,GAEA,MAApBA,EAASG,QAAkBH,EAAS9C,OACvCkC,KAAKrJ,OAASiK,EAAS9C,KACvB4C,QAAQC,IAAI,YAAaX,KAAKrJ,QAEhC,CAAE,MAAOsK,GACRP,QAAQO,MAAM,YAAaA,GAC3BC,GAAAA,GAAUD,MAAM,WACjB,CACD,EACAG,MAAAA,GACC,IAAKpB,KAAKlJ,SAAWkJ,KAAKqB,MAAMD,OAE/B,YADAV,QAAQC,IAAI,uBAIb,MAAM7E,EAAQ,CACbkE,KAAKlJ,OAAOwK,KAAO,EACnBtB,KAAKlJ,OAAOyK,SAAW,EACvBvB,KAAKlJ,OAAO0K,MAAQ,EACpBxB,KAAKlJ,OAAOmK,OAAS,GAEhB3E,EAAQ,CAAC,OAAQ,OAAQ,OAAQ,QACvCoE,QAAQC,IAAI,SAAU,CAAC7E,QAAOQ,UAC9B0D,KAAKyB,OAAOL,OAAOpB,KAAKqB,MAAMD,OAAQtF,EAAOQ,EAC9C,EACAoF,MAAAA,GACC,IAAK1B,KAAKlJ,SAAWkJ,KAAKqB,MAAMK,OAE/B,YADAhB,QAAQC,IAAI,uBAIb,MAAMgB,EAAQ,CACb,CAAE7F,MAAOkE,KAAKlJ,OAAOyK,SAAW,EAAG7E,KAAM,MACzC,CAAEZ,MAAOkE,KAAKlJ,OAAO0K,MAAQ,EAAG9E,KAAM,MACtC,CAAEZ,MAAOkE,KAAKlJ,OAAOmK,OAAS,EAAGvE,KAAM,OAExCgE,QAAQC,IAAI,SAAUgB,GACtB3B,KAAKyB,OAAOC,OAAO1B,KAAKqB,MAAMK,OAAQC,EACvC,EACAzF,kBAAAA,CAAmB7E,GAClB,IAAK2I,KAAKlJ,SAAWkJ,KAAKlJ,OAAO+C,UAAY+H,MAAMC,QAAQ7B,KAAKlJ,OAAO+C,SAGtE,OAFA6G,QAAQO,MAAM,uBACdjB,KAAKrD,eAAiB,IAIvBqD,KAAKhE,cAAgB3E,EACrBqJ,QAAQC,IAAI,UAAWtJ,GAEV,QAATA,EACH2I,KAAKrD,eAAiB,IAAIqD,KAAKlJ,OAAO+C,SACnB,YAATxC,EACV2I,KAAKrD,eAAiB,IAAIqD,KAAK5F,cACZ,SAAT/C,EACV2I,KAAKrD,eAAiB,IAAIqD,KAAKtF,WACZ,UAATrD,IACV2I,KAAKrD,eAAiB,IAAIqD,KAAKhF,aAGhC0F,QAAQC,IAAI,WAAYX,KAAKrD,eAAe7C,QAC5CkG,KAAKjD,eAAiB,EACvB,EACAE,WAAAA,CAAYJ,GACX,MAAMiF,EAAW9B,KAAKjD,eAAegF,QAAQlF,IAC3B,IAAdiF,EACH9B,KAAKjD,eAAeiF,KAAKnF,GAEzBmD,KAAKjD,eAAekF,OAAOH,EAAU,EAEvC,EACAtF,WAAAA,CAAYnF,GACX,OAAK2I,KAAKlJ,QAAWkJ,KAAKlJ,OAAO+C,QACpB,QAATxC,EAAuB2I,KAAKlJ,OAAO+C,QAAQC,OAClC,YAATzC,EAA2B2I,KAAK5F,aAAaN,OACpC,SAATzC,EAAwB2I,KAAKtF,UAAUZ,OACpCkG,KAAKhF,WAAWlB,OAJ0B,CAKlD,EACAqD,YAAAA,CAAaC,GACZ,MAAc,YAAVA,EAA4B,gBAClB,SAAVA,EAAyB,UACtB,mBACR,EACAG,kBAAAA,CAAmBH,GAClB,MAAO,SAASA,GACjB,EACAI,iBAAAA,CAAkBJ,GACjB,MAAM8E,EAAM,CACXX,QAAS,KACTC,KAAM,KACNP,MAAO,MAER,OAAOiB,EAAI9E,IAAUA,CACtB,EACAsB,eAAAA,CAAgBF,GACf,OAAKA,GAAQA,EAAInH,MACG,QAAbmH,EAAInH,KAAiB,aADE,KAE/B,EACA4H,kBAAAA,CAAmBkD,GAElB,OADAA,EAAOC,SAASD,GAAQ,GACpBA,GAAQ,KAAOA,EAAO,IAAY,UAClCA,GAAQ,KAAOA,EAAO,IAAY,WAClCA,GAAQ,KAAOA,EAAO,IAAY,eAC/B,cACR,EACAjD,kBAAAA,CAAmB9B,GAClB,OAAKA,EACS,OAAVA,EAAuB,UACb,OAAVA,EAAuB,OACpB,QAHY,OAIpB,GAED,aAAMiF,GACL,IACC3B,QAAQC,IAAI,QACZ,MAAM1I,EAAK+H,KAAKjI,OAAOC,OAAOC,GAC1BA,GACHyI,QAAQC,IAAI,QAAS1I,SACf+H,KAAKS,cAAcxI,SACnB+H,KAAKmB,cAAclJ,KAEzByI,QAAQO,MAAM,YACdC,GAAAA,GAAUD,MAAM,YAElB,CAAE,MAAOA,GACRP,QAAQO,MAAM,WAAYA,GAC1BC,GAAAA,GAAUD,MAAM,UACjB,CACD,EACAqB,OAAAA,GACKtC,KAAKqB,MAAMD,QAAQpB,KAAKoB,SACxBpB,KAAKqB,MAAMK,QAAQ1B,KAAK0B,QAC7B,EACAa,OAAAA,GACKvC,KAAKqB,MAAMD,QAAQpB,KAAKoB,SACxBpB,KAAKqB,MAAMK,QAAQ1B,KAAK0B,QAC7B,G,YCpbD,MAAMc,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASC,IAAQ,CAAC,YAAY,qBAEzF,S,0ICQUjM,MAAM,e,GAGPoC,MAAA,mB,aAsCApC,MAAM,e,SAM6BoC,MAAA,mB,SAOLA,MAAA,mB,SACKA,MAAA,mB,SAC3BA,MAAA,mB,SAK0BA,MAAA,mB,SAC1BA,MAAA,mB,SASRA,MAAA,0D,GAcIpC,MAAM,iB,0ZArGhBW,EAAAA,EAAAA,IAsFQuL,EAAA,CAtFC,cAAY,KAAK9J,MAAA,uBAA2BvB,KAAK,cAAcyE,MAAM,KAAKkC,KAAK,Q,kBACzF,IAYgB,CAZkB,OAAf2E,EAAApE,OAAOlH,O,WAA1B8E,EAAAA,EAAAA,IAYgByG,EAAA,C,MAZyBtG,MAAM,MAAMI,KAAK,M,kBACtD,IAUM,CAVKiG,EAAApE,OAAOsE,kB,WAAlBjM,EAAAA,EAAAA,IAUM,MAAAC,EAAA,CATO8L,EAAApE,OAAOsE,gBAAgB,gBAAgB7F,SAAS,sB,WAA3DpG,EAAAA,EAAAA,IAGM,MAAAG,EAAA,EADJI,EAAAA,EAAAA,IAA4F2L,EAAA,CAAnFC,UAAU,E,WAAeJ,EAAApE,OAAOyE,c,qCAAPL,EAAApE,OAAOyE,cAAa/G,GAAEgH,KAAK,OAAOC,MAAM,U,uCAE5EtM,EAAAA,EAAAA,IAIM,MAAAK,EAAA,EAHJE,EAAAA,EAAAA,IAEewB,EAAA,CAFDE,OAAO,QAAUsK,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAA0G,EAA1GlM,EAAAA,EAAAA,IAA0G2L,EAAA,CAAjGC,UAAU,EAAMO,UAAQX,EAAApE,OAAOyE,cAAeC,KAAK,OAAOC,MAAM,SAASrK,OAAO,S,6EAKjE,OAAf8J,EAAApE,OAAOlH,O,WAA1B8E,EAAAA,EAAAA,IAWcyG,EAAA,C,MAX2BtG,MAAM,MAAMI,KAAK,M,kBACtD,IASe,EATfvF,EAAAA,EAAAA,IASewB,EAAA,CATDE,OAAO,QAASsK,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAOI,CAP2BV,EAAApE,OAAOsE,kB,WAAtCjM,EAAAA,EAAAA,IAOI,MAPJM,EAOI,G,aANLN,EAAAA,EAAAA,IAKM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IALsBiH,EAAApE,OAAOsE,gBAAe,CAArC/G,EAAOD,M,WAApBjF,EAAAA,EAAAA,IAKM,aAJLO,EAAAA,EAAAA,IAGSoM,EAAA,CAHD3K,MAAA,qBAAyBvB,KAAK,Q,kBACrC,IAAgD,EAAhDL,EAAAA,EAAAA,IAAgD,IAAhDW,GAAgDE,EAAAA,EAAAA,IAAlBgE,EAAM,OAAH,IACjC7E,EAAAA,EAAAA,IAAwB,aAAAa,EAAAA,EAAAA,IAAfiE,GAAK,K,yEAMgB,OAAf6G,EAAApE,OAAOlH,O,WAA1B8E,EAAAA,EAAAA,IA4BcyG,EAAA,C,MA5B2BtG,MAAM,OAAOI,KAAK,M,kBACvD,IA0Be,EA1BfvF,EAAAA,EAAAA,IA0BewB,EAAA,CA1BDE,OAAO,QAASsK,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAwBI,CAxBOV,EAAApE,OAAOiF,gB,WAAlB5M,EAAAA,EAAAA,IAwBI,MAAAgB,EAAA,EAvBLT,EAAAA,EAAAA,IAsBcsM,EAAA,C,WAtBQ/M,EAAAgN,Y,qCAAAhN,EAAAgN,YAAWzH,GAAEzF,MAAM,e,kBACxC,IAMmB,EANnBW,EAAAA,EAAAA,IAMmBwM,EAAA,CANDjH,KAAK,KAAG,CACdkH,OAAKzF,EAAAA,EAAAA,IACf,IAAciF,EAAA,KAAAA,EAAA,KAAdpM,EAAAA,EAAAA,IAAc,SAAX,WAAO,M,iBAEX,IAA+C,EAA/CA,EAAAA,EAAAA,IAA+C,WAA1C,qBAAiBa,EAAAA,EAAAA,IAAG8K,EAAApE,OAAOM,QAAM,IACtC7H,EAAAA,EAAAA,IAAyC,WAApC,kBAAca,EAAAA,EAAAA,IAAG8K,EAAApE,OAAOsF,KAAG,K,OAEjC1M,EAAAA,EAAAA,IAOmBwM,EAAA,CAPDjH,KAAK,KAAG,CACdkH,OAAKzF,EAAAA,EAAAA,IACf,IAAsBiF,EAAA,MAAAA,EAAA,MAAtBpM,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEd,IAA8C,G,aAAnDJ,EAAAA,EAAAA,IAEM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFsBiH,EAAApE,OAAOuF,gBAAe,CAArChI,EAAOD,M,WAApBjF,EAAAA,EAAAA,IAEM,aADLI,EAAAA,EAAAA,IAAsC,aAAAa,EAAAA,EAAAA,IAA7BgE,EAAM,MAAQC,GAAK,O,eAG9B3E,EAAAA,EAAAA,IAKmBwM,EAAA,CALDjH,KAAK,KAAG,CACdkH,OAAKzF,EAAAA,EAAAA,IACf,IAAsBiF,EAAA,MAAAA,EAAA,MAAtBpM,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEnB,IAAuC,EAAvCA,EAAAA,EAAAA,IAAuC,aAAAa,EAAAA,EAAAA,IAA9B8K,EAAApE,OAAOiF,eAAa,K,oFAMjCrM,EAAAA,EAAAA,IAYcyL,EAAA,CAZDtG,MAAM,MAAI,C,iBACtB,IAUe,EAVfnF,EAAAA,EAAAA,IAUewB,EAAA,CAVDE,OAAO,QAASsK,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAQM,EARNrM,EAAAA,EAAAA,IAQM,MARNkB,EAQM,G,aAPLtB,EAAAA,EAAAA,IAMM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANuBiH,EAAApE,OAAOwF,SAAQ,CAA/BC,EAAMnH,M,WAAnBjG,EAAAA,EAAAA,IAMM,YAL8C,UAAZoN,EAAK,K,WAA5C7H,EAAAA,EAAAA,IAAmFoH,EAAA,C,MAA3E3K,MAAA,sB,kBAAqD,IAAa,E,iBAAVoL,EAAK,IAAD,K,YACZ,YAAZA,EAAK,K,WAAjD7H,EAAAA,EAAAA,IAAwGoH,EAAA,C,MAAhG3K,MAAA,qBAA2DvB,KAAK,W,kBAAU,IAAa,E,iBAAV2M,EAAK,IAAD,K,YACjC,UAAZA,EAAK,K,WAAjD7H,EAAAA,EAAAA,IAAqGoH,EAAA,C,MAA7F3K,MAAA,qBAAyDvB,KAAK,U,kBAAS,IAAa,E,iBAAV2M,EAAK,IAAD,K,YAC9B,SAAZA,EAAK,K,WAAjD7H,EAAAA,EAAAA,IAAqGoH,EAAA,C,MAA7F3K,MAAA,qBAAwDvB,KAAK,W,kBAAU,IAAa,E,iBAAV2M,EAAK,IAAD,K,YAC1D,WAAZA,EAAK,K,WAArBpN,EAAAA,EAAAA,IAAiF,MAAjF0B,GAAiFT,EAAAA,EAAAA,IAAhBmM,EAAK,IAAD,K,4CAKzE7M,EAAAA,EAAAA,IAMcyL,EAAA,CANDqB,SAAA,IAAQ,CACT3H,OAAK6B,EAAAA,EAAAA,IACf,IAAkG,CAArE,OAAjBwE,EAAApE,OAAOnB,Q,WAAnBxG,EAAAA,EAAAA,IAAkG,OAAlG6B,GAAkGZ,EAAAA,EAAAA,IAAA,YAAtB8K,EAAApE,OAAOnB,OAAK,IACtD,OAAjBuF,EAAApE,OAAOnB,Q,WAAxBxG,EAAAA,EAAAA,IAAuG,OAAvGmC,GAAuGlB,EAAAA,EAAAA,IAAA,YAAtB8K,EAAApE,OAAOnB,OAAK,M,WAC7FxG,EAAAA,EAAAA,IAA8D,OAA9DoC,GAA8DnB,EAAAA,EAAAA,IAAtB8K,EAAApE,OAAOnB,OAAK,M,MAGpB,OAAfuF,EAAApE,OAAOlH,O,WAA1B8E,EAAAA,EAAAA,IAKcyG,EAAA,C,MAL2BqB,SAAA,I,CAC7B3H,OAAK6B,EAAAA,EAAAA,IACf,IAA4G,CAAhGwE,EAAApE,OAAOS,aAAe,M,WAAlCpI,EAAAA,EAAAA,IAA4G,OAA5GqC,GAA4GpB,EAAAA,EAAAA,IAAA,YAA5B8K,EAAApE,OAAOS,aAAW,M,WAClGpI,EAAAA,EAAAA,IAAkF,OAAlFsC,GAAkFrB,EAAAA,EAAAA,IAAA,YAA5B8K,EAAApE,OAAOS,aAAW,M,wBAG1E7H,EAAAA,EAAAA,IAIcyL,EAAA,CAJDqB,SAAA,IAAQ,CACT3H,OAAK6B,EAAAA,EAAAA,IACf,IAAiC,E,2BAAlBwE,EAAApE,OAAO2F,UAAQ,K,cAIuD,OAAjBvB,EAAApE,OAAOnB,OAAkBuF,EAAAwB,U,WAA7FvN,EAAAA,EAAAA,IAEM,MAFNuC,EAEM,EADJhC,EAAAA,EAAAA,IAAqFC,EAAA,CAAxEE,QAAOC,EAAA6M,cAAe/M,KAAK,UAAUgN,MAAA,GAAMrG,KAAK,Q,kBAAO,IAAKoF,EAAA,MAAAA,EAAA,M,QAAL,Y,gDAGtEjM,EAAAA,EAAAA,IAeYmN,EAAA,CAfDV,MAAM,Q,WAAiBlN,EAAA6N,U,qCAAA7N,EAAA6N,UAAStI,GAAE0C,MAAM,MAAO,eAAcpH,EAAAiN,mB,CAS3DC,QAAMtG,EAAAA,EAAAA,IACf,IAGM,EAHNnH,EAAAA,EAAAA,IAGM,MAHNqC,EAGM,EAFJlC,EAAAA,EAAAA,IAAqDC,EAAA,CAAzCE,QAAOC,EAAAiN,mBAAiB,C,iBAAE,IAAGpB,EAAA,MAAAA,EAAA,M,QAAH,U,6BACtCjM,EAAAA,EAAAA,IAA0DC,EAAA,CAA/CC,KAAK,UAAWC,QAAOC,EAAAmN,S,kBAAS,IAAGtB,EAAA,MAAAA,EAAA,M,QAAH,U,iDAX/C,IAOU,EAPVjM,EAAAA,EAAAA,IAOUwN,EAAA,CAPAC,MAAOlO,EAAAmO,SAAO,C,iBACtB,IAIe,EAJf1N,EAAAA,EAAAA,IAIe2N,EAAA,CAJDxI,MAAM,QAAM,C,iBACxB,IAEY,EAFZnF,EAAAA,EAAAA,IAEY4N,EAAA,CAFD/G,KAAK,Q,WAAiBtH,EAAAmO,QAAQG,U,qCAARtO,EAAAmO,QAAQG,UAAS/I,GAAEgJ,YAAY,WAAWrM,MAAA,gB,kBACT,IAA0B,G,aAA1FhC,EAAAA,EAAAA,IAAsH6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAAvChF,EAAAwO,WAARC,K,WAAvEhJ,EAAAA,EAAAA,IAAsHiJ,EAAA,CAA1G9I,MAAO6I,EAAKzI,KAAO,IAAMyI,EAAKtB,IAAM/H,MAAOqJ,EAAKlN,GAAgC4D,IAAKsJ,EAAKlN,I,oEAG1Gd,EAAAA,EAAAA,IAAiK2N,EAAA,CAAnJxI,MAAM,SAAO,C,iBAAC,IAAsH,EAAtHnF,EAAAA,EAAAA,IAAsHkO,EAAA,CAA3GC,SAAU,CAAAC,QAAA,EAAAC,QAAA,G,WAAqC9O,EAAAmO,QAAQY,K,qCAAR/O,EAAAmO,QAAQY,KAAIxJ,GAAE5E,KAAK,WAAWqO,aAAa,O,0HAczI,GACCC,MAAO,CACNpH,OAAQ,CACPL,QAAS,CAAC,GAEXiG,QAAS,CACRjG,SAAS,IAGX6B,SAAU,KACN6F,EAAAA,EAAAA,IAAS,CAAC,SAEdzG,WAAY,CACX0G,OAAMA,EAAAA,GAEP/H,IAAAA,GACC,MAAO,CACN4F,YAAa,CAAC,IAAK,IAAK,KAExBa,WAAW,EAEXM,QAAS,CACRG,UAAW,KACXS,KAAM,GACNzE,KAAM,GACND,OAAQ,OAENmE,WAAW,GAEhB,EACA7E,QAAS,CACR,aAAMqE,GACL1E,KAAK6E,QAAQiB,QAAU9F,KAAK+F,IAAI9N,GAChC+H,KAAK6E,QAAQ7D,KAAOhB,KAAKzB,OACzB,MAAMqC,QAAiBZ,KAAKa,KAAKmF,WAAWhG,KAAK6E,SACzB,MAApBjE,EAASG,SACZf,KAAKiG,SAAS,CACb5O,KAAM,UACN6O,QAAS,UACTC,SAAU,MAEXnG,KAAKuE,WAAY,EACjBvE,KAAK6E,QAAU,CACdG,UAAW,KACXS,KAAM,GACNzE,KAAM,GACND,OAAQ,OAGX,EAEEyD,iBAAAA,GACExE,KAAKuE,WAAY,EACjBvE,KAAK6E,QAAU,CAChBG,UAAW,KACXS,KAAM,GACNzE,KAAM,GACND,OAAQ,MAEP,EAGF,mBAAMqD,GACJ,MAAMxD,QAAiBZ,KAAKa,KAAKuF,mBACT,MAApBxF,EAASG,SACXf,KAAKkF,WAAatE,EAAS9C,KAC3BkC,KAAKuE,WAAY,EAErB,I,WChLJ,MAAM/B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,O", "sources": ["webpack://frontend-web/./src/views/Reports/Report.vue", "webpack://frontend-web/./src/views/Reports/Report.vue?4281", "webpack://frontend-web/./src/components/common/caseResult.vue", "webpack://frontend-web/./src/components/common/caseResult.vue?0f1a"], "sourcesContent": ["  <template>\n\t<div class=\"report-dashboard\" v-if=\"record\">\n\t\t<div v-if=\"report\" class=\"dashboard-container\">\n\t\t\t<!-- 顶部状态栏 -->\n\t\t\t<div class=\"status-bar\">\n\t\t\t\t<div class=\"status-left\">\n\t\t\t\t\t<el-button type=\"text\" class=\"back-btn\" @click=\"goBack\">\n\t\t\t\t\t\t<el-icon><Back /></el-icon>\n\t\t\t\t\t</el-button>\n\t\t\t\t\t<div class=\"report-status\">\n\t\t\t\t\t\t<div class=\"status-title\">测试报告 #{{ $route.params.id }}</div>\n\t\t\t\t\t\t<div class=\"status-time\">{{ $tools.rTime(record.create_time) }}</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"status-right\">\n\t\t\t\t\t<div class=\"status-badge\" :class=\"getPassRateClass\">\n\t\t\t\t\t\t<span class=\"badge-label\">通过率</span>\n\t\t\t\t\t\t<span class=\"badge-value\">{{ record.pass_rate || 0 }}%</span>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n      <el-scrollbar :style=\"{ height: 'calc(100vh - 21vh)',minHeight: '700px'}\">\n        <!-- 主要内容区 -->\n        <div class=\"dashboard-content\">\n          <!-- 左侧数据概览 -->\n          <div class=\"overview-panel\">\n            <div class=\"overview-header\">\n              <h2>测试概览</h2>\n              <div class=\"task-info\">\n                <div class=\"task-name\">{{ record.plan_name || '未命名计划' }}</div>\n                <div class=\"env-tag\">{{ record.env_name || '未知环境' }}</div>\n              </div>\n            </div>\n\n            <!-- 统计卡片 -->\n            <div class=\"stat-cards\">\n              <div class=\"stat-card total\">\n                <div class=\"stat-icon\"><el-icon><DataAnalysis /></el-icon></div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-value\">{{ report.results && report.results.length || 0 }}</div>\n                  <div class=\"stat-label\">场景总数</div>\n                </div>\n                <div class=\"stat-wave\"></div>\n              </div>\n              <div class=\"stat-card success\">\n                <div class=\"stat-icon\"><el-icon><SuccessFilled /></el-icon></div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-value\">{{ successscent.length }}</div>\n                  <div class=\"stat-label\">通过场景</div>\n                </div>\n                <div class=\"stat-wave\"></div>\n              </div>\n              <div class=\"stat-card fail\">\n                <div class=\"stat-icon\"><el-icon><Warning /></el-icon></div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-value\">{{ failscent.length }}</div>\n                  <div class=\"stat-label\">失败场景</div>\n                </div>\n                <div class=\"stat-wave\"></div>\n              </div>\n              <div class=\"stat-card error\">\n                <div class=\"stat-icon\"><el-icon><CircleCloseFilled /></el-icon></div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-value\">{{ errorscent.length }}</div>\n                  <div class=\"stat-label\">错误场景</div>\n                </div>\n                <div class=\"stat-wave\"></div>\n              </div>\n            </div>\n\n            <!-- 图表区域 -->\n            <div class=\"chart-section\">\n              <div class=\"chart-card\">\n                <div class=\"chart-header\">\n                  <h3>执行统计</h3>\n                  <div class=\"chart-legend\">\n                    <span class=\"legend-item success\">通过</span>\n                    <span class=\"legend-item fail\">失败</span>\n                    <span class=\"legend-item error\">错误</span>\n                  </div>\n                </div>\n                <div class=\"chart-content\" ref=\"chart1\"></div>\n              </div>\n              <div class=\"chart-card\">\n                <div class=\"chart-header\">\n                  <h3>结果分布</h3>\n                </div>\n                <div class=\"chart-content\" ref=\"chart2\"></div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 右侧场景详情 -->\n          <div class=\"detail-panel\">\n            <div class=\"detail-header\">\n              <h2>测试场景</h2>\n              <div class=\"filter-tabs\">\n                <div\n                  v-for=\"tab in filterTabs\"\n                  :key=\"tab.value\"\n                  class=\"filter-tab\"\n                  :class=\"{ active: currentFilter === tab.value, [tab.value]: true }\"\n                  @click=\"handleFilterChange(tab.value)\"\n                >\n                  <el-icon class=\"tab-icon\"><component :is=\"tab.icon\" /></el-icon>\n                  <span>{{ tab.label }}</span>\n                  <span class=\"tab-count\">{{ getTabCount(tab.value) }}</span>\n                </div>\n              </div>\n            </div>\n\n            <el-scrollbar class=\"scene-list\">\n              <transition-group name=\"scene-fade\">\n                <div\n                  v-for=\"(scent, index) in showScentDatas\"\n                  :key=\"scent.name + index\"\n                  class=\"scene-card\"\n                  :class=\"{ expanded: expandedScenes.includes(index) }\"\n                >\n                  <div class=\"scene-header\" @click=\"toggleScene(index)\">\n                    <div class=\"scene-status\">\n                      <el-icon><component :is=\"getSceneIcon(scent.state)\" /></el-icon>\n                    </div>\n                    <div class=\"scene-info\">\n                      <div class=\"scene-name\">{{ scent.name || '未命名场景' }}</div>\n                      <div class=\"scene-state\" :class=\"getSceneStateClass(scent.state)\">\n                        {{ getSceneStateText(scent.state) }}\n                      </div>\n                    </div>\n                    <div class=\"scene-action\">\n                      <el-icon :class=\"{ rotate: expandedScenes.includes(index) }\"><ArrowRight /></el-icon>\n                    </div>\n                  </div>\n\n                  <div class=\"scene-detail\" v-show=\"expandedScenes.includes(index)\">\n                    <el-table\n                      :data=\"scent.cases || []\"\n                      class=\"case-table\"\n                      size=\"small\"\n                    >\n                      <el-table-column type=\"expand\">\n                        <template #default=\"scope\">\n                          <div class=\"case-detail\">\n                            <caseRes :result=\"scope.row\"></caseRes>\n                          </div>\n                        </template>\n                      </el-table-column>\n                      <el-table-column label=\"用例名称\" min-width=\"200\">\n                        <template #default=\"scope\">\n                          <div class=\"case-name\">\n                            <el-icon><component :is=\"getCaseTypeIcon(scope.row)\" /></el-icon>\n                            <span>{{ scope.row.name || '未命名用例' }}</span>\n                          </div>\n                        </template>\n                      </el-table-column>\n                      <el-table-column label=\"请求方法\" width=\"100\" align=\"center\">\n                        <template #default=\"scope\">\n                          <div\n                            v-if=\"scope.row.type === 'api' && scope.row.method\"\n                            class=\"method-badge\"\n                            :class=\"scope.row.method.toLowerCase()\"\n                          >\n                            {{ scope.row.method }}\n                          </div>\n                        </template>\n                      </el-table-column>\n                      <el-table-column label=\"状态码\" width=\"100\" align=\"center\">\n                        <template #default=\"scope\">\n                          <div\n                            v-if=\"scope.row.type === 'api' && scope.row.status_cede !== undefined\"\n                            class=\"status-code\"\n                            :class=\"getStatusCodeClass(scope.row.status_cede)\"\n                          >\n                            {{ scope.row.status_cede }}\n                          </div>\n                        </template>\n                      </el-table-column>\n                      <el-table-column label=\"结果\" width=\"100\" align=\"center\">\n                        <template #default=\"scope\">\n                          <div\n                            v-if=\"scope.row.state\"\n                            class=\"result-badge\"\n                            :class=\"getCaseResultClass(scope.row.state)\"\n                          >\n                            {{ scope.row.state }}\n                          </div>\n                        </template>\n                      </el-table-column>\n                    </el-table>\n                  </div>\n                </div>\n              </transition-group>\n            </el-scrollbar>\n          </div>\n        </div>\n      </el-scrollbar>\n\t\t</div>\n\t</div>\n</template>\n\n<script>\nimport caseRes from '../../components/common/caseResult.vue';\nimport { \n  Back, \n  DataAnalysis, \n  SuccessFilled, \n  Warning, \n  CircleCloseFilled, \n  ArrowRight, \n  Grid, \n  Connection, \n  Cpu\n} from '@element-plus/icons-vue';\nimport { ElMessage, ElScrollbar } from 'element-plus';\n\nexport default {\n\tname: 'TestReport',\n\tcomponents: { \n    caseRes,\n    Back,\n    DataAnalysis,\n    SuccessFilled,\n    Warning,\n    CircleCloseFilled,\n    ArrowRight,\n    Grid,\n    Connection,\n    Cpu,\n    ElScrollbar\n  },\n\tdata() {\n\t\treturn {\n\t\t\trecord: null,\n\t\t\treport: null,\n\t\t\tshowScentDatas: [],\n\t\t\tcurrentFilter: 'all',\n\t\t\texpandedScenes: [],\n\t\t\tfilterTabs: [\n\t\t\t\t{ label: '全部场景', value: 'all', icon: 'Grid' },\n\t\t\t\t{ label: '通过场景', value: 'success', icon: 'SuccessFilled' },\n\t\t\t\t{ label: '失败场景', value: 'fail', icon: 'Warning' },\n\t\t\t\t{ label: '错误场景', value: 'error', icon: 'CircleCloseFilled' }\n\t\t\t]\n\t\t};\n\t},\n\tcomputed: {\n\t\tsuccessscent() {\n\t\t\tif (!this.report || !this.report.results) return [];\n\t\t\treturn this.report.results.filter(val => val.state === 'success');\n\t\t},\n\t\tfailscent() {\n\t\t\tif (!this.report || !this.report.results) return [];\n\t\t\treturn this.report.results.filter(val => val.state === 'fail');\n\t\t},\n\t\terrorscent() {\n\t\t\tif (!this.report || !this.report.results) return [];\n\t\t\treturn this.report.results.filter(val => val.state === 'error');\n\t\t},\n\t\tgetPassRateClass() {\n\t\t\tif (!this.record || !this.record.pass_rate) return 'poor';\n\t\t\tconst rate = parseFloat(this.record.pass_rate);\n\t\t\tif (rate >= 90) return 'excellent';\n\t\t\tif (rate >= 70) return 'good';\n\t\t\tif (rate >= 50) return 'fair';\n\t\t\treturn 'poor';\n\t\t}\n\t},\n\tmethods: {\n\t\tgoBack() {\n\t\t\twindow.history.back();\n\t\t},\n\t\tasync getReportInfo(id) {\n\t\t\ttry {\n\t\t\t\tconsole.log('获取报告信息, ID:', id);\n\t\t\t\tconst response = await this.$api.getTestReport(id);\n\t\t\t\tconsole.log('报告API响应:', response);\n\t\t\t\t\n\t\t\t\tif (response.status === 200) {\n\t\t\t\t\t// 如果数据是直接在response.data，而不是response.data.info\n\t\t\t\t\tif (response.data && response.data.results) {\n\t\t\t\t\t\tthis.report = response.data;\n\t\t\t\t\t} \n\t\t\t\t\t// 或者数据在response.data.info\n\t\t\t\t\telse if (response.data && response.data.info) {\n\t\t\t\t\t\tthis.report = response.data.info;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('处理后的报告数据:', this.report);\n\t\t\t\t\t\n\t\t\t\t\tif (this.report && this.report.results) {\n\t\t\t\t\t\tthis.handleFilterChange('all');\n\t\t\t\t\t\tconsole.log('过滤后的场景数据:', this.showScentDatas);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('报告数据结构不正确');\n\t\t\t\t\t\tElMessage.error('报告数据结构不正确');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取报告信息失败:', error);\n\t\t\t\tElMessage.error('获取报告信息失败');\n\t\t\t}\n\t\t},\n\t\tasync getRecordInfo(id) {\n\t\t\ttry {\n\t\t\t\tconsole.log('获取记录信息, ID:', id);\n\t\t\t\tconst response = await this.$api.getRecordInfo(id);\n\t\t\t\tconsole.log('记录API响应:', response);\n\t\t\t\t\n\t\t\t\tif (response.status === 200 && response.data) {\n\t\t\t\t\tthis.record = response.data;\n\t\t\t\t\tconsole.log('处理后的记录数据:', this.record);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取记录信息失败:', error);\n\t\t\t\tElMessage.error('获取记录信息失败');\n\t\t\t}\n\t\t},\n\t\tchart1() {\n\t\t\tif (!this.report || !this.$refs.chart1) {\n\t\t\t\tconsole.log('无法渲染图表1 - 数据或DOM不存在');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tconst value = [\n\t\t\t\tthis.report.all || 0, \n\t\t\t\tthis.report.success || 0, \n\t\t\t\tthis.report.fail || 0, \n\t\t\t\tthis.report.error || 0\n\t\t\t];\n\t\t\tconst label = ['用例总数', '通过用例', '失败用例', '错误用例'];\n\t\t\tconsole.log('图表1数据:', {value, label});\n\t\t\tthis.$chart.chart1(this.$refs.chart1, value, label);\n\t\t},\n\t\tchart2() {\n\t\t\tif (!this.report || !this.$refs.chart2) {\n\t\t\t\tconsole.log('无法渲染图表2 - 数据或DOM不存在');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tconst datas = [\n\t\t\t\t{ value: this.report.success || 0, name: '通过' },\n\t\t\t\t{ value: this.report.fail || 0, name: '失败' },\n\t\t\t\t{ value: this.report.error || 0, name: '错误' }\n\t\t\t];\n\t\t\tconsole.log('图表2数据:', datas);\n\t\t\tthis.$chart.chart2(this.$refs.chart2, datas);\n\t\t},\n\t\thandleFilterChange(type) {\n\t\t\tif (!this.report || !this.report.results || !Array.isArray(this.report.results)) {\n\t\t\t\tconsole.error('没有场景数据或数据格式不正确');\n\t\t\t\tthis.showScentDatas = [];\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.currentFilter = type;\n\t\t\tconsole.log('切换场景过滤:', type);\n\t\t\t\n\t\t\tif (type === 'all') {\n\t\t\t\tthis.showScentDatas = [...this.report.results];\n\t\t\t} else if (type === 'success') {\n\t\t\t\tthis.showScentDatas = [...this.successscent];\n\t\t\t} else if (type === 'fail') {\n\t\t\t\tthis.showScentDatas = [...this.failscent];\n\t\t\t} else if (type === 'error') {\n\t\t\t\tthis.showScentDatas = [...this.errorscent];\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('过滤后场景数量:', this.showScentDatas.length);\n\t\t\tthis.expandedScenes = [];\n\t\t},\n\t\ttoggleScene(index) {\n\t\t\tconst position = this.expandedScenes.indexOf(index);\n\t\t\tif (position === -1) {\n\t\t\t\tthis.expandedScenes.push(index);\n\t\t\t} else {\n\t\t\t\tthis.expandedScenes.splice(position, 1);\n\t\t\t}\n\t\t},\n\t\tgetTabCount(type) {\n\t\t\tif (!this.report || !this.report.results) return 0;\n\t\t\tif (type === 'all') return this.report.results.length;\n\t\t\tif (type === 'success') return this.successscent.length;\n\t\t\tif (type === 'fail') return this.failscent.length;\n\t\t\treturn this.errorscent.length;\n\t\t},\n\t\tgetSceneIcon(state) {\n\t\t\tif (state === 'success') return 'SuccessFilled';\n\t\t\tif (state === 'fail') return 'Warning';\n\t\t\treturn 'CircleCloseFilled';\n\t\t},\n\t\tgetSceneStateClass(state) {\n\t\t\treturn `state-${state}`;\n\t\t},\n\t\tgetSceneStateText(state) {\n\t\t\tconst map = {\n\t\t\t\tsuccess: '通过',\n\t\t\t\tfail: '失败',\n\t\t\t\terror: '错误'\n\t\t\t};\n\t\t\treturn map[state] || state;\n\t\t},\n\t\tgetCaseTypeIcon(row) {\n\t\t\tif (!row || !row.type) return 'Cpu';\n\t\t\treturn row.type === 'api' ? 'Connection' : 'Cpu';\n\t\t},\n\t\tgetStatusCodeClass(code) {\n\t\t\tcode = parseInt(code || 0);\n\t\t\tif (code >= 200 && code < 300) return 'success';\n\t\t\tif (code >= 300 && code < 400) return 'redirect';\n\t\t\tif (code >= 400 && code < 500) return 'client-error';\n\t\t\treturn 'server-error';\n\t\t},\n\t\tgetCaseResultClass(state) {\n\t\t\tif (!state) return 'error';\n\t\t\tif (state === '成功') return 'success';\n\t\t\tif (state === '失败') return 'fail';\n\t\t\treturn 'error';\n\t\t}\n\t},\n\tasync created() {\n\t\ttry {\n\t\t\tconsole.log('组件创建');\n\t\t\tconst id = this.$route.params.id;\n\t\t\tif (id) {\n\t\t\t\tconsole.log('报告ID:', id);\n\t\t\t\tawait this.getReportInfo(id);\n\t\t\t\tawait this.getRecordInfo(id);\n\t\t\t} else {\n\t\t\t\tconsole.error('没有找到报告ID');\n\t\t\t\tElMessage.error('没有找到报告ID');\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('初始化报告失败:', error);\n\t\t\tElMessage.error('初始化报告失败');\n\t\t}\n\t},\n\tmounted() {\n\t\tif (this.$refs.chart1) this.chart1();\n\t\tif (this.$refs.chart2) this.chart2();\n\t},\n\tupdated() {\n\t\tif (this.$refs.chart1) this.chart1();\n\t\tif (this.$refs.chart2) this.chart2();\n\t}\n};\n</script>\n\n<style scoped>\n.report-dashboard {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);\n\tpadding: 20px;\n}\n\n.dashboard-container {\n\tmax-width: 1800px;\n\tmargin: 0 auto;\n}\n\n/* 顶部状态栏 */\n.status-bar {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tbackground: rgba(255, 255, 255, 0.9);\n\tpadding: 16px 24px;\n\tborder-radius: 12px;\n\tmargin-bottom: 24px;\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n\tbackdrop-filter: blur(10px);\n\tborder: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.status-left {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20px;\n}\n\n.back-btn {\n\tcolor: #606266;\n\tfont-size: 20px;\n\ttransition: all 0.3s;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 8px;\n}\n\n.back-btn:hover {\n\tcolor: #409eff;\n\ttransform: translateX(-2px);\n}\n\n.status-title {\n\tfont-size: 20px;\n\tfont-weight: 600;\n\tbackground: linear-gradient(45deg, #409eff, #36cfc9);\n\t-webkit-background-clip: text;\n\t-webkit-text-fill-color: transparent;\n}\n\n.status-time {\n\tfont-size: 14px;\n\tcolor: #909399;\n\tmargin-top: 4px;\n}\n\n.status-badge {\n\tpadding: 8px 16px;\n\tborder-radius: 20px;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n\ttransition: all 0.3s;\n}\n\n.status-badge:hover {\n\ttransform: translateY(-2px);\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.status-badge.excellent { background: linear-gradient(45deg, #67c23a, #95d475); color: #fff; }\n.status-badge.good { background: linear-gradient(45deg, #409eff, #79bbff); color: #fff; }\n.status-badge.fair { background: linear-gradient(45deg, #e6a23c, #ebb563); color: #fff; }\n.status-badge.poor { background: linear-gradient(45deg, #f56c6c, #f78989); color: #fff; }\n\n.badge-label {\n\tfont-size: 12px;\n\topacity: 0.9;\n}\n\n.badge-value {\n\tfont-size: 20px;\n\tfont-weight: 600;\n\tmargin-top: 2px;\n}\n\n/* 主内容区布局 */\n.dashboard-content {\n\tdisplay: grid;\n\tgrid-template-columns: 400px 1fr;\n\tgap: 24px;\n}\n\n/* 左侧概览面板 */\n.overview-panel {\n\tbackground: rgba(255, 255, 255, 0.9);\n\tborder-radius: 12px;\n\tpadding: 16px;\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n\tbackdrop-filter: blur(10px);\n\tborder: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.overview-header {\n\tmargin-bottom: 16px;\n}\n\n.overview-header h2 {\n\tmargin: 0 0 12px 0;\n\tfont-size: 18px;\n\tcolor: #303133;\n}\n\n.task-info {\n\tbackground: rgba(255, 255, 255, 0.8);\n\tpadding: 8px 12px;\n\tborder-radius: 8px;\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n}\n\n.task-name {\n\tfont-size: 14px;\n\tfont-weight: 500;\n\tmargin-bottom: 6px;\n\tcolor: #303133;\n}\n\n.env-tag {\n\tdisplay: inline-block;\n\tpadding: 4px 8px;\n\tbackground: linear-gradient(45deg, #409eff, #36cfc9);\n\tcolor: #fff;\n\tborder-radius: 4px;\n\tfont-size: 12px;\n}\n\n/* 统计卡片 */\n.stat-cards {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(2, 1fr);\n\tgap: 12px;\n\tmargin-bottom: 16px;\n}\n\n.stat-card {\n\tposition: relative;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tborder-radius: 10px;\n\tpadding: 12px;\n\toverflow: hidden;\n\ttransition: all 0.3s;\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n}\n\n.stat-card:hover {\n\ttransform: translateY(-2px);\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.stat-icon {\n\tfont-size: 20px;\n\tmargin-bottom: 8px;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.stat-icon .el-icon {\n\tfont-size: 20px;\n}\n\n.stat-value {\n\tfont-size: 24px;\n\tfont-weight: 600;\n\tmargin-bottom: 2px;\n}\n\n.stat-label {\n\tfont-size: 12px;\n\tcolor: #909399;\n}\n\n.stat-wave {\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 4px;\n\tbackground: linear-gradient(90deg, transparent, currentColor, transparent);\n\tanimation: wave 2s infinite;\n}\n\n@keyframes wave {\n\t0% { transform: translateX(-100%); }\n\t100% { transform: translateX(100%); }\n}\n\n.stat-card.total { color: #409eff; }\n.stat-card.success { color: #67c23a; }\n.stat-card.fail { color: #e6a23c; }\n.stat-card.error { color: #f56c6c; }\n\n/* 图表区域 */\n.chart-card {\n\tbackground: rgba(255, 255, 255, 0.8);\n\tborder-radius: 10px;\n\tpadding: 12px;\n\tmargin-bottom: 12px;\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n\ttransition: all 0.3s;\n}\n\n.chart-card:hover {\n\ttransform: translateY(-2px);\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.chart-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 12px;\n}\n\n.chart-header h3 {\n\tmargin: 0;\n\tfont-size: 14px;\n\tcolor: #303133;\n\tfont-weight: 600;\n}\n\n.chart-legend {\n\tdisplay: flex;\n\tgap: 8px;\n}\n\n.legend-item {\n\tfont-size: 11px;\n\tdisplay: flex;\n\talign-items: center;\n\tcolor: #606266;\n}\n\n.legend-item::before {\n\tcontent: '';\n\tdisplay: inline-block;\n\twidth: 6px;\n\theight: 6px;\n\tborder-radius: 50%;\n\tmargin-right: 3px;\n}\n\n.legend-item.success::before { background: #67c23a; }\n.legend-item.fail::before { background: #e6a23c; }\n.legend-item.error::before { background: #f56c6c; }\n\n.chart-content {\n\theight: 200px;\n\twidth: 100%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n}\n\n/* 右侧详情面板 */\n.detail-panel {\n\tbackground: rgba(255, 255, 255, 0.9);\n\tborder-radius: 12px;\n\tpadding: 20px;\n\tdisplay: flex;\n\tflex-direction: column;\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n\tbackdrop-filter: blur(10px);\n\tborder: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.detail-header {\n\tmargin-bottom: 24px;\n}\n\n.detail-header h2 {\n\tmargin: 0 0 16px 0;\n\tfont-size: 18px;\n\tcolor: #303133;\n}\n\n/* 过滤标签 */\n.filter-tabs {\n\tdisplay: flex;\n\tgap: 12px;\n\tmargin-bottom: 20px;\n}\n\n.filter-tab {\n\tflex: 1;\n\tpadding: 12px;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tborder-radius: 8px;\n\tcursor: pointer;\n\ttransition: all 0.3s;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8px;\n\tcolor: #606266;\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.tab-icon {\n\tfont-size: 16px;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.filter-tab::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 4px;\n\theight: 100%;\n\ttransition: all 0.3s;\n\topacity: 0.7;\n}\n\n.filter-tab.all::before {\n\tbackground: linear-gradient(to bottom, #409eff, #36cfc9);\n}\n\n.filter-tab.success::before {\n\tbackground: linear-gradient(to bottom, #67c23a, #95d475);\n}\n\n.filter-tab.fail::before {\n\tbackground: linear-gradient(to bottom, #e6a23c, #ebb563);\n}\n\n.filter-tab.error::before {\n\tbackground: linear-gradient(to bottom, #f56c6c, #f78989);\n}\n\n.filter-tab:hover {\n\tbackground: rgba(255, 255, 255, 0.9);\n\ttransform: translateY(-2px);\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.filter-tab:hover::before {\n\twidth: 6px;\n}\n\n.filter-tab.active {\n\tbackground: #fff;\n\tpadding-left: 16px;\n}\n\n.filter-tab.active::before {\n\twidth: 8px;\n}\n\n.filter-tab.active.all {\n\tcolor: #409eff;\n\tborder: 1px solid rgba(64, 158, 255, 0.2);\n}\n\n.filter-tab.active.success {\n\tcolor: #67c23a;\n\tborder: 1px solid rgba(103, 194, 58, 0.2);\n}\n\n.filter-tab.active.fail {\n\tcolor: #e6a23c;\n\tborder: 1px solid rgba(230, 162, 60, 0.2);\n}\n\n.filter-tab.active.error {\n\tcolor: #f56c6c;\n\tborder: 1px solid rgba(245, 108, 108, 0.2);\n}\n\n.filter-tab i {\n\tfont-size: 16px;\n\ttransition: all 0.3s;\n}\n\n.filter-tab:hover i {\n\ttransform: scale(1.1);\n}\n\n.filter-tab.active i {\n\ttransform: scale(1.2);\n}\n\n.tab-count {\n\tmargin-left: auto;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tpadding: 4px 10px;\n\tborder-radius: 12px;\n\tfont-size: 12px;\n\tfont-weight: bold;\n\ttransition: all 0.3s;\n\tmin-width: 24px;\n\ttext-align: center;\n}\n\n.filter-tab.active .tab-count {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.filter-tab.active.all .tab-count {\n\tbackground: rgba(64, 158, 255, 0.1);\n\tcolor: #409eff;\n}\n\n.filter-tab.active.success .tab-count {\n\tbackground: rgba(103, 194, 58, 0.1);\n\tcolor: #67c23a;\n}\n\n.filter-tab.active.fail .tab-count {\n\tbackground: rgba(230, 162, 60, 0.1);\n\tcolor: #e6a23c;\n}\n\n.filter-tab.active.error .tab-count {\n\tbackground: rgba(245, 108, 108, 0.1);\n\tcolor: #f56c6c;\n}\n\n/* 场景列表 */\n.scene-list {\n\theight: calc(100vh - 26vh);\n\tmin-height: 400px;\n\tpadding-right: 8px;\n}\n\n.scene-card {\n\tbackground: rgba(255, 255, 255, 0.8);\n\tborder-radius: 8px;\n\tmargin-bottom: 12px;\n\ttransition: all 0.3s, max-height 0.5s ease;\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n\toverflow: hidden;\n}\n\n.scene-card:hover {\n\ttransform: translateX(4px);\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.scene-header {\n\tpadding: 16px;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16px;\n\tcursor: pointer;\n}\n\n.scene-status {\n\tfont-size: 20px;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.scene-status .el-icon {\n\tfont-size: 20px;\n}\n\n.scene-info {\n\tflex: 1;\n}\n\n.scene-name {\n\tfont-weight: 500;\n\tmargin-bottom: 4px;\n\tcolor: #303133;\n}\n\n.scene-state {\n\tfont-size: 12px;\n\tpadding: 2px 8px;\n\tborder-radius: 4px;\n\tdisplay: inline-flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.state-success { background: rgba(103, 194, 58, 0.1); color: #67c23a; }\n.state-fail { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }\n.state-error { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }\n\n.scene-action {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.scene-action .el-icon {\n\ttransition: transform 0.3s;\n\tcolor: #909399;\n}\n\n.scene-action .el-icon.rotate {\n\ttransform: rotate(90deg);\n}\n\n.scene-detail {\n\tpadding: 0 16px 16px;\n\toverflow: hidden;\n\ttransition: all 0.3s;\n}\n\n/* 用例表格样式 */\n.case-table {\n\tbackground: transparent !important;\n}\n\n.case-table :deep(.el-table__expanded-cell) {\n\tpadding: 10px 20px;\n\tbackground: rgba(250, 250, 250, 0.5);\n}\n\n.case-name {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8px;\n\tcolor: #303133;\n}\n\n.case-name .el-icon {\n\tfont-size: 16px;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.method-badge {\n\tpadding: 4px 8px;\n\tborder-radius: 4px;\n\tfont-size: 12px;\n\tfont-weight: 500;\n\tdisplay: inline-flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.method-badge.get { background: rgba(103, 194, 58, 0.1); color: #67c23a; }\n.method-badge.post { background: rgba(64, 158, 255, 0.1); color: #409eff; }\n.method-badge.put { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }\n.method-badge.delete { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }\n\n.status-code {\n\tpadding: 4px 8px;\n\tborder-radius: 4px;\n\tfont-size: 12px;\n\tdisplay: inline-flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.status-code.success { background: rgba(103, 194, 58, 0.1); color: #67c23a; }\n.status-code.redirect { background: rgba(64, 158, 255, 0.1); color: #409eff; }\n.status-code.client-error { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }\n.status-code.server-error { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }\n\n.result-badge {\n\tpadding: 4px 8px;\n\tborder-radius: 4px;\n\tfont-size: 12px;\n\tdisplay: inline-flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.result-badge.success { background: rgba(103, 194, 58, 0.1); color: #67c23a; }\n.result-badge.fail { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }\n.result-badge.error { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }\n\n/* 动画效果 */\n.scene-fade-enter-active, .scene-fade-leave-active {\n\ttransition: all 0.3s ease;\n}\n\n.scene-fade-enter-from, .scene-fade-leave-to {\n\topacity: 0;\n\ttransform: translateY(10px);\n}\n\n/* 展开/收起效果 */\n.scene-detail {\n  overflow: hidden;\n  transition: all 0.3s;\n}\n\n.scene-card {\n  transition: all 0.3s, max-height 0.5s ease;\n  overflow: hidden;\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n\twidth: 6px;\n}\n\n::-webkit-scrollbar-track {\n\tbackground: rgba(255, 255, 255, 0.1);\n\tborder-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n\tbackground: rgba(144, 147, 153, 0.3);\n\tborder-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n\tbackground: rgba(144, 147, 153, 0.5);\n}\n\n/* 自定义 Element Plus 表格样式 */\n:deep(.el-table) {\n  --el-table-header-bg-color: rgba(255, 255, 255, 0.6);\n  --el-table-row-hover-bg-color: rgba(255, 255, 255, 0.8);\n  background: transparent;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n:deep(.el-table__header-wrapper),\n:deep(.el-table__body-wrapper) {\n  background: transparent;\n}\n\n:deep(.el-table th) {\n  background-color: var(--el-table-header-bg-color);\n  font-weight: 600;\n  color: #303133;\n  padding: 10px 0;\n}\n\n:deep(.el-table td) {\n  padding: 10px 0;\n}\n\n:deep(.el-table .el-table__cell) {\n  vertical-align: middle;\n}\n</style>\n", "import { render } from \"./Report.vue?vue&type=template&id=cf72507a&scoped=true\"\nimport script from \"./Report.vue?vue&type=script&lang=js\"\nexport * from \"./Report.vue?vue&type=script&lang=js\"\n\nimport \"./Report.vue?vue&type=style&index=0&id=cf72507a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-cf72507a\"]])\n\nexport default __exports__", "<template>\n\t  <el-tabs model-value=\"rb\" style=\"min-height: 300px;\" type=\"border-card\" value=\"rb\" size=\"mini\">\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应体\" name=\"rb\">\n      <div v-if=\"result.response_header\">\n        <div v-if=\"result.response_header['Content-Type'].includes('application/json')\">\n          <!-- 如果 Content-Type 是 application/json，渲染 JSON 格式的 Editor -->\n          <Editor :readOnly=\"true\" v-model=\"result.response_body\" lang=\"json\" theme=\"chrome\"></Editor>\n        </div>\n        <div v-else>\n          <el-scrollbar height=\"400px\"  @wheel.stop>\n            <Editor :readOnly=\"true\" v-html=\"result.response_body\" lang=\"html\" theme=\"chrome\" height=\"400px\"></Editor>\n          </el-scrollbar>\n        </div>\n      </div>\n    </el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应头\" name=\"rh\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div class=\"tab-box-sli\" v-if=\"result.response_header\">\n\t\t\t\t<div v-for=\"(value, key) in result.response_header\">\n\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" type=\"info\">\n\t\t\t\t\t\t<b style=\"color: #747474;\">{{ key + ' : ' }}</b>\n\t\t\t\t\t\t<span>{{ value }}</span>\n\t\t\t\t\t</el-tag>\n\t\t\t\t</div>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"请求信息\" name=\"rq\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div v-if=\"result.requests_body\">\n\t\t\t\t<el-collapse v-model=\"activeNames\" class=\"tab-box-sli\">\n\t\t\t\t\t<el-collapse-item name=\"1\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>General</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div>Request Method : {{ result.method }}</div>\n\t\t\t\t\t\t<div>Request URL : {{ result.url }}</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"2\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Headers</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div v-for=\"(value, key) in result.requests_header\">\n\t\t\t\t\t\t\t<span>{{ key + ' : ' + value }}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"3\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Payload</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<span>{{ result.requests_body }}</span>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t</el-collapse>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane label=\"日志\">\n\t\t\t<el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t\t<div class=\"tab-box-sli\">\n\t\t\t\t\t<div v-for=\"(item, index) in result.log_data\">\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-if=\"item[0] === 'DEBUG'\" >{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'WARNING'\" type=\"warning\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'ERROR'\" type=\"danger\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'INFO'\" type=\"success\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<pre v-else-if=\"item[0] === 'EXCEPT'\" style=\"color: #d60000;\">{{ item[1] }}</pre>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.state === '成功'\" style=\"color: #00AA7F;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else-if=\"result.state === '失败'\" style=\"color: #d18d17;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else style=\"color: #ff0000;\">{{ result.state }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.status_cede <= 300\" style=\"color: #00AA7F;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t\t<span v-else style=\"color: #ff5500;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t{{ 'Time : ' + result.run_time }}\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t</el-tabs>\n    <div style=\"margin-top: 10px;width: 100%;text-align: center;\" v-if=\"result.state === '失败' && showbtn\">\n      <el-button  @click=\"getInterfaces\" type=\"success\" plain size=\"mini\">提交bug</el-button>\n    </div>\n    <!-- 添加bug的弹框 -->\n    <el-dialog title=\"提交bug\" v-model=\"addBugDlg\" width=\"40%\" :before-close=\"closeDialogResult\">\n      <el-form :model=\"bugForm\">\n        <el-form-item label=\"所属接口\">\n          <el-select size=\"small\" v-model=\"bugForm.interface\" placeholder=\"bug对应的接口\" style=\"width: 100%;\">\n            <el-option :label=\"iter.name + ' ' + iter.url\" :value=\"iter.id\" v-for=\"iter in interfaces\" :key=\"iter.id\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"bug描述\"><el-input :autosize=\"{ minRows: 3, maxRows: 4 }\" v-model=\"bugForm.desc\" type=\"textarea\" autocomplete=\"off\"></el-input></el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"closeDialogResult\">取 消</el-button>\n          <el-button type=\"success\" @click=\"saveBug\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n</template>\n\n<script>\nimport Editor from './Editor.vue';\nimport { mapState } from 'vuex';\nexport default {\n\tprops: {\n\t\tresult: {\n\t\t\tdefault: {}\n\t\t},\n\t\tshowbtn: {\n\t\t\tdefault: true\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState(['pro'])\n\t},\n\tcomponents: {\n\t\tEditor\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveNames: ['1', '2', '3'],\n\t\t\t// 提交bug的显示窗口\n\t\t\taddBugDlg: false,\n\t\t\t// 添加bug的表单\n\t\t\tbugForm: {\n\t\t\t\tinterface: null,\n\t\t\t\tdesc: '',\n\t\t\t\tinfo: '',\n\t\t\t\tstatus: '待处理'\n\t\t\t},\n      interfaces:[]\n\t\t};\n\t},\n\tmethods: {\n\t\tasync saveBug() {\n\t\t\tthis.bugForm.project = this.pro.id;\n\t\t\tthis.bugForm.info = this.result;\n\t\t\tconst response = await this.$api.createBugs(this.bugForm);\n\t\t\tif (response.status === 201) {\n\t\t\t\tthis.$message({\n\t\t\t\t\ttype: 'success',\n\t\t\t\t\tmessage: 'bug提交成功',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\tthis.addBugDlg = false;\n\t\t\t\tthis.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n\t\t\t}\n\t\t},\n    // 取消按钮时重置输入信息\n    closeDialogResult() {\n      this.addBugDlg = false;\n      this.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n      },\n\n    // 获取接口列表\n    async getInterfaces() {\n      const response = await this.$api.getNewInterfaces();\n      if (response.status === 200) {\n        this.interfaces = response.data\n        this.addBugDlg = true\n      }\n    }\n\t}\n};\n</script>\n\n<style></style>\n", "import { render } from \"./caseResult.vue?vue&type=template&id=3a14eb2a\"\nimport script from \"./caseResult.vue?vue&type=script&lang=js\"\nexport * from \"./caseResult.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__"], "names": ["class", "ref", "$data", "record", "_createElementBlock", "_hoisted_1", "report", "_hoisted_2", "_createElementVNode", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_button", "type", "onClick", "$options", "goBack", "_component_el_icon", "_component_Back", "_hoisted_5", "_hoisted_6", "_toDisplayString", "_ctx", "$route", "params", "id", "_hoisted_7", "$tools", "rTime", "create_time", "_hoisted_8", "_normalizeClass", "getPassRateClass", "_hoisted_9", "pass_rate", "_component_el_scrollbar", "style", "height", "minHeight", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "plan_name", "_hoisted_15", "env_name", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_component_DataAnalysis", "_hoisted_19", "_hoisted_20", "results", "length", "_hoisted_21", "_hoisted_22", "_component_SuccessFilled", "_hoisted_23", "_hoisted_24", "successscent", "_hoisted_25", "_hoisted_26", "_component_Warning", "_hoisted_27", "_hoisted_28", "failscent", "_hoisted_29", "_hoisted_30", "_component_CircleCloseFilled", "_hoisted_31", "_hoisted_32", "errorscent", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_Fragment", "_renderList", "filterTabs", "tab", "key", "value", "active", "currentFilter", "$event", "handleFilterChange", "_createBlock", "_resolveDynamicComponent", "icon", "label", "_hoisted_42", "getTabCount", "_TransitionGroup", "name", "showScentDatas", "scent", "index", "expanded", "expandedScenes", "includes", "toggleScene", "_hoisted_44", "getSceneIcon", "state", "_hoisted_45", "_hoisted_46", "getSceneStateClass", "getSceneStateText", "_hoisted_47", "rotate", "_component_ArrowRight", "_hoisted_48", "_component_el_table", "data", "cases", "size", "_component_el_table_column", "default", "_withCtx", "scope", "_hoisted_49", "_component_caseRes", "result", "row", "_hoisted_50", "getCaseTypeIcon", "width", "align", "method", "toLowerCase", "undefined", "status_cede", "getStatusCodeClass", "getCaseResultClass", "components", "caseRes", "Back", "DataAnalysis", "SuccessFilled", "Warning", "CircleCloseFilled", "ArrowRight", "Grid", "Connection", "Cpu", "ElScrollbar", "computed", "this", "filter", "val", "rate", "parseFloat", "methods", "window", "history", "back", "getReportInfo", "console", "log", "response", "$api", "getTestReport", "status", "info", "error", "ElMessage", "getRecordInfo", "chart1", "$refs", "all", "success", "fail", "$chart", "chart2", "datas", "Array", "isArray", "position", "indexOf", "push", "splice", "map", "code", "parseInt", "created", "mounted", "updated", "__exports__", "render", "_component_el_tabs", "$props", "_component_el_tab_pane", "response_header", "_component_Editor", "readOnly", "response_body", "lang", "theme", "onWheel", "_cache", "_withModifiers", "innerHTML", "_component_el_tag", "requests_body", "_component_el_collapse", "activeNames", "_component_el_collapse_item", "title", "url", "requests_header", "log_data", "item", "disabled", "run_time", "showbtn", "getInterfaces", "plain", "_component_el_dialog", "addBugDlg", "closeDialogResult", "footer", "saveBug", "_component_el_form", "model", "bugForm", "_component_el_form_item", "_component_el_select", "interface", "placeholder", "interfaces", "iter", "_component_el_option", "_component_el_input", "autosize", "minRows", "maxRows", "desc", "autocomplete", "props", "mapState", "Editor", "project", "pro", "createBugs", "$message", "message", "duration", "getNewInterfaces"], "sourceRoot": ""}