{"version": 3, "file": "js/233.76b1a057.js", "mappings": "yMACOA,MAAM,mB,GAEJA,MAAM,e,GAKJA,MAAM,kB,GAKJA,MAAM,kB,GAWNA,MAAM,e,GAORA,MAAM,kB,GAuCNA,MAAM,a,iBAQEA,MAAM,e,GACJA,MAAM,a,GAENA,MAAM,a,GAENA,MAAM,e,GASRA,MAAM,gB,GACJA,MAAM,e,GAEHA,MAAM,gB,GAETA,MAAM,e,GAEHA,MAAM,gB,GAETA,MAAM,e,GAEHA,MAAM,gB,GAGXA,MAAM,e,GACHA,MAAM,Y,GACNA,MAAM,Q,SAETA,MAAM,uB,GASdA,MAAM,kB,SAYgBA,MAAM,qB,GAE5BA,MAAM,kB,GAQFA,MAAM,e,SAU4BA,MAAM,kB,GAClCA,MAAM,a,GACHA,MAAM,c,GAGTA,MAAM,a,GACHA,MAAM,c,GAGTA,MAAM,a,GACHA,MAAM,c,SAIJA,MAAM,mB,GAWjBA,MAAM,e,GAWJA,MAAM,mB,GAEJA,MAAM,gB,GAMDA,MAAM,Q,GACNA,MAAM,Q,GACNA,MAAM,S,SAEyCA,MAAM,mB,GAO5DA,MAAM,mB,GAEJA,MAAM,gB,GAMDA,MAAM,Q,GACNA,MAAM,Q,GACNA,MAAM,S,SAEmDA,MAAM,mB,GAOtEA,MAAM,mB,IAEJA,MAAM,gB,IAMDA,MAAM,Q,IACNA,MAAM,Q,IACNA,MAAM,S,UAEgDA,MAAM,mB,IAOnEA,MAAM,mB,IAEJA,MAAM,gB,IAMDA,MAAM,Q,IACNA,MAAM,Q,IACNA,MAAM,S,UAE+CA,MAAM,mB,IAYpEA,MAAM,e,IA4BNA,MAAM,e,IAOJC,IAAI,WAAWD,MAAM,mB,IAGrBC,IAAI,oBAAoBD,MAAM,mB,IAQhCA,MAAM,e,IAKRA,MAAM,mB,IAOFA,MAAM,mB,UAsCCA,MAAM,mB,4lBA3W5BE,EAAAA,EAAAA,IA+WM,MA/WNC,EA+WM,EA7WJC,EAAAA,EAAAA,IAgBM,MAhBNC,EAgBM,EAfJC,EAAAA,EAAAA,IAGgBC,GAAA,CAHDC,UAAU,KAAG,C,iBAC1B,IAAmF,EAAnFF,EAAAA,EAAAA,IAAmFG,GAAA,CAA9DC,GAAI,CAAAC,KAAA,wBAA+B,C,iBAAE,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,cAC1DN,EAAAA,EAAAA,IAA6CG,GAAA,M,iBAAzB,IAAIG,EAAA,KAAAA,EAAA,K,QAAJ,W,sBAEtBR,EAAAA,EAAAA,IAUM,MAVNS,EAUM,C,aATJT,EAAAA,EAAAA,IAGM,OAHDJ,MAAM,gBAAc,EACvBI,EAAAA,EAAAA,IAAiB,UAAb,aACJA,EAAAA,EAAAA,IAAyB,SAAtB,wB,KAELA,EAAAA,EAAAA,IAIM,MAJNU,EAIM,EAHJR,EAAAA,EAAAA,IAEYS,GAAA,CAFDC,KAAK,UAAWC,QAAOC,GAAAC,mBAAoBC,MAAA,I,kBACpD,IAA2B,EAA3Bd,EAAAA,EAAAA,IAA2Be,GAAA,M,iBAAlB,IAAQ,EAARf,EAAAA,EAAAA,IAAQgB,M,2BAAU,c,iCAOnBC,GAAAC,kB,4BAAhBC,EAAAA,EAAAA,IAyGUC,GAAA,C,MAzGwB1B,MAAM,iBAAiB2B,OAAO,S,CACnDC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNzB,EAAAA,EAAAA,IAGM,MAHN0B,EAGM,EAFJxB,EAAAA,EAAAA,IAAmCe,GAAA,M,iBAA1B,IAAgB,EAAhBf,EAAAA,EAAAA,IAAgByB,M,mBACzB3B,EAAAA,EAAAA,IAAmB,YAAb,UAAM,Q,iBAKhB,IAoCM,EApCNA,EAAAA,EAAAA,IAoCM,MApCN4B,EAoCM,EAnCJ1B,EAAAA,EAAAA,IAkCS2B,GAAA,CAlCAC,OAAQ,IAAE,C,iBACjB,IAQS,EART5B,EAAAA,EAAAA,IAQS6B,GAAA,CARAC,KAAM,GAAC,C,iBACd,IAME,EANF9B,EAAAA,EAAAA,IAME+B,GAAA,C,WALSd,GAAAe,c,qCAAAf,GAAAe,cAAaC,GACtBC,YAAY,SACZ,cAAY,SACZC,UAAA,GACCC,QAAOxB,GAAAyB,c,0CAGZrC,EAAAA,EAAAA,IAOS6B,GAAA,CAPAC,KAAM,GAAC,C,iBACd,IAKY,EALZ9B,EAAAA,EAAAA,IAKYsC,GAAA,C,WALQrB,GAAAsB,a,qCAAAtB,GAAAsB,aAAYN,GAAEC,YAAY,OAAOC,UAAA,GAAWK,SAAQ5B,GAAAyB,c,kBACtE,IAAiC,EAAjCrC,EAAAA,EAAAA,IAAiCyC,GAAA,CAAtBC,MAAM,KAAKC,MAAM,MAC5B3C,EAAAA,EAAAA,IAAmCyC,GAAA,CAAxBC,MAAM,MAAMC,MAAM,OAC7B3C,EAAAA,EAAAA,IAAmCyC,GAAA,CAAxBC,MAAM,MAAMC,MAAM,OAC7B3C,EAAAA,EAAAA,IAAqCyC,GAAA,CAA1BC,MAAM,OAAOC,MAAM,S,2CAGlC3C,EAAAA,EAAAA,IAUS6B,GAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQE,EARF9B,EAAAA,EAAAA,IAQE4C,GAAA,C,WAPS3B,GAAA4B,U,qCAAA5B,GAAA4B,UAASZ,GAClBvB,KAAK,gBACL,oBAAkB,OAClB,kBAAgB,OAChBoC,OAAO,sBACP,eAAa,sBACZN,SAAQ5B,GAAAyB,c,2CAGbrC,EAAAA,EAAAA,IAIS6B,GAAA,CAJAC,KAAM,GAAC,C,iBACd,IAEY,EAFZ9B,EAAAA,EAAAA,IAEYS,GAAA,CAFDC,KAAK,UAAWC,QAAOC,GAAAmC,kBAAoBC,SAAU/B,GAAAgC,cAAcC,OAAS,G,kBAAG,IAClF,E,QADkF,WAClFC,EAAAA,EAAAA,IAAGlC,GAAAgC,cAAcC,QAAS,KAClC,K,kDAMNpD,EAAAA,EAAAA,IA8CM,MA9CNsD,EA8CM,EA7CJpD,EAAAA,EAAAA,IA4CS2B,GAAA,CA5CAC,OAAQ,IAAE,C,iBACC,IAAiC,G,aAAnDhC,EAAAA,EAAAA,IA0CSyD,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA1C0B1C,GAAA2C,gBAAVC,K,WAAzBrC,EAAAA,EAAAA,IA0CSU,GAAA,CA1CAC,KAAM,EAAsC2B,IAAKD,EAAOE,I,kBAC/D,IAwCM,EAxCN5D,EAAAA,EAAAA,IAwCM,OAvCJJ,OAAKiE,EAAAA,EAAAA,IAAA,CAAC,YAAW,UACK/C,GAAAgD,WAAWJ,GAAM,UAAgB5C,GAAAiD,UAAUL,MAChE7C,QAAKsB,GAAErB,GAAAkD,gBAAgBN,I,EAExB1D,EAAAA,EAAAA,IAaM,MAbNiE,EAaM,EAZJjE,EAAAA,EAAAA,IAGM,MAHNkE,EAGM,EAFJlE,EAAAA,EAAAA,IAAgC,WAAAqD,EAAAA,EAAAA,IAAzBK,EAAOS,YAAU,IACxBnE,EAAAA,EAAAA,IAA8D,IAA9DoE,GAA8Df,EAAAA,EAAAA,IAAtCK,EAAOW,MAAMC,UAAY,QAAJ,MAE/CtE,EAAAA,EAAAA,IAOM,MAPNuE,EAOM,EANJrE,EAAAA,EAAAA,IAKSsE,GAAA,CAJN5D,KAAME,GAAA2D,cAAcf,EAAOgB,cAC5BC,KAAK,S,kBAEL,IAAwC,E,iBAArC7D,GAAA8D,cAAclB,EAAOgB,eAAY,K,yBAI1C1E,EAAAA,EAAAA,IAaM,MAbN6E,EAaM,EAZJ7E,EAAAA,EAAAA,IAGM,MAHN8E,EAGM,C,aAFJ9E,EAAAA,EAAAA,IAAqC,QAA/BJ,MAAM,gBAAe,OAAG,KAC9BI,EAAAA,EAAAA,IAA2E,OAA3E+E,GAA2E1B,EAAAA,EAAAA,IAA7CvC,GAAAkE,aAAatB,EAAOuB,QAAU,EAAG,IAAP,MAE1DjF,EAAAA,EAAAA,IAGM,MAHNkF,EAGM,C,aAFJlF,EAAAA,EAAAA,IAAsC,QAAhCJ,MAAM,gBAAe,QAAI,KAC/BI,EAAAA,EAAAA,IAAsF,OAAtFmF,GAAsF9B,EAAAA,EAAAA,IAAxDvC,GAAAkE,aAAatB,EAAO0B,iBAAmB,EAAG,IAAK,KAAE,MAEjFpF,EAAAA,EAAAA,IAGM,MAHNqF,EAGM,C,eAFJrF,EAAAA,EAAAA,IAAqC,QAA/BJ,MAAM,gBAAe,OAAG,KAC9BI,EAAAA,EAAAA,IAA+D,OAA/DsF,GAA+DjC,EAAAA,EAAAA,IAAjCvC,GAAAyE,eAAe7B,IAAU,IAAC,QAG5D1D,EAAAA,EAAAA,IAGM,MAHNwF,EAGM,EAFJxF,EAAAA,EAAAA,IAA2D,OAA3DyF,GAA2DpC,EAAAA,EAAAA,IAAjCK,EAAOgC,UAAY,MAAJ,IACzC1F,EAAAA,EAAAA,IAA8D,OAA9D2F,GAA8DtC,EAAAA,EAAAA,IAAxCvC,GAAA8E,WAAWlC,EAAOmC,cAAW,KAEd/E,GAAAgD,WAAWJ,K,WAAlD5D,EAAAA,EAAAA,IAEM,MAFNgG,EAEM,EADJ5F,EAAAA,EAAAA,IAA4Be,GAAA,M,iBAAnB,IAAS,EAATf,EAAAA,EAAAA,IAAS6F,M,gEAQ5B/F,EAAAA,EAAAA,IAOM,MAPNgG,EAOM,EANJ9F,EAAAA,EAAAA,IAKE+F,GAAA,CAJCC,MAAK,OAAS/E,GAAAgC,cAAcC,aAAajC,GAAAgC,cAAcC,OAAS,EAAI,kBAAoBjC,GAAAgC,cAAcC,OAAS,EAAI,cAAgB,KACnIxC,KAAMO,GAAAgC,cAAcC,OAAS,EAAI,UAAYjC,GAAAgC,cAAcC,OAAS,EAAI,QAAU,UAClF+C,UAAU,EACX,gB,oCAOKhF,GAAAC,mB,WAAXtB,EAAAA,EAAAA,IAsOM,MAtONsG,EAsOM,EApOJpG,EAAAA,EAAAA,IAGM,MAHNqG,EAGM,EAFJnG,EAAAA,EAAAA,IAAqES,GAAA,CAAzDE,QAAOC,GAAAwF,gBAAiBC,KAAK,a,kBAAY,IAAI/F,EAAA,MAAAA,EAAA,M,QAAJ,W,6BACrDN,EAAAA,EAAAA,IAAgFS,GAAA,CAArEC,KAAK,UAAWC,QAAOC,GAAA0F,aAAcD,KAAK,Y,kBAAW,IAAI/F,EAAA,MAAAA,EAAA,M,QAAJ,W,+BAIlEN,EAAAA,EAAAA,IAgCUoB,GAAA,CAhCD1B,MAAM,gBAAgB2B,OAAO,S,CACzBC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNzB,EAAAA,EAAAA,IAGM,MAHNyG,EAGM,EAFJvG,EAAAA,EAAAA,IAA+Be,GAAA,M,iBAAtB,IAAY,EAAZf,EAAAA,EAAAA,IAAYwG,M,qBACrB1G,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGd,IAwBS,EAxBTE,EAAAA,EAAAA,IAwBS2B,GAAA,CAxBAC,OAAQ,IAAE,C,iBAEV,IAAwD,G,aAD/DhC,EAAAA,EAAAA,IAsBSyD,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IArB2BrC,GAAAC,iBAAiBuF,UAAS,CAA/CC,EAAUC,M,WADzBxF,EAAAA,EAAAA,IAsBSU,GAAA,CAtBA+E,GAAI,GAAKC,GAAI,GAAKC,GAAE,GAAOC,KAAKC,IAAIC,OAAOC,KAAKjG,GAAAC,iBAAiBuF,WAAWvD,OAAQ,GAC5BO,IAAKkD,G,kBACpE,IAmBM,EAnBN7G,EAAAA,EAAAA,IAmBM,OAnBDJ,OAAKiE,EAAAA,EAAAA,IAAA,CAAC,gBAAe,YAAsB/C,GAAAuG,eAAeR,O,EAC7D7G,EAAAA,EAAAA,IAAiC,WAAAqD,EAAAA,EAAAA,IAA1BuD,EAASU,WAAS,GACdxG,GAAAuG,eAAeR,K,WAA1B/G,EAAAA,EAAAA,IAaM,MAbNyH,EAaM,EAZJvH,EAAAA,EAAAA,IAGM,MAHNwH,EAGM,EAFJxH,EAAAA,EAAAA,IAAiH,OAAjHyH,GAAiHpE,EAAAA,EAAAA,IAArFvC,GAAAkE,aAAa7D,GAAAC,iBAAiBsG,mBAAmBb,IAASc,UAAUC,IAAK,IAAF,G,eACnG5H,EAAAA,EAAAA,IAAqC,QAA/BJ,MAAM,cAAa,SAAK,OAEhCI,EAAAA,EAAAA,IAGM,MAHN6H,EAGM,EAFJ7H,EAAAA,EAAAA,IAA6H,OAA7H8H,GAA6HzE,EAAAA,EAAAA,IAAjGvC,GAAAkE,aAAa7D,GAAAC,iBAAiBsG,mBAAmBb,IAASc,UAAUI,cAAe,IAAK,KAAE,G,eACtH/H,EAAAA,EAAAA,IAAoC,QAA9BJ,MAAM,cAAa,QAAI,OAE/BI,EAAAA,EAAAA,IAGM,MAHNgI,EAGM,EAFJhI,EAAAA,EAAAA,IAAyH,OAAzHiI,GAAyH5E,EAAAA,EAAAA,IAA7FvC,GAAAkE,aAAa7D,GAAAC,iBAAiBsG,mBAAmBb,IAASc,UAAUO,WAAY,IAAK,IAAC,G,eAClHlI,EAAAA,EAAAA,IAAmC,QAA7BJ,MAAM,cAAa,OAAG,W,WAGhCE,EAAAA,EAAAA,IAEM,MAFNqI,EAEM,EADJjI,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAckI,M,6BAAU,gB,gDAQ3ClI,EAAAA,EAAAA,IAyFUoB,GAAA,CAzFD1B,MAAM,eAAe2B,OAAO,S,CACxBC,QAAMC,EAAAA,EAAAA,IACf,IAOM,EAPNzB,EAAAA,EAAAA,IAOM,MAPNqI,EAOM,EANJnI,EAAAA,EAAAA,IAAkCe,GAAA,M,iBAAzB,IAAe,EAAff,EAAAA,EAAAA,IAAeoI,M,qBACxBtI,EAAAA,EAAAA,IAAiB,YAAX,QAAI,IACOmH,OAAOC,KAAKjG,GAAAC,iBAAiBsG,oBAAoBtE,OAAS+D,OAAOC,KAAKjG,GAAAC,iBAAiBuF,WAAWvD,S,WAAnH/B,EAAAA,EAAAA,IAGYV,GAAA,C,MAFHC,KAAK,UAAU+D,KAAK,QAAQ3D,MAAA,GAAMuH,MAAA,wB,kBAA0B,IAErE/H,EAAA,MAAAA,EAAA,M,QAFqE,kB,oDAKzE,IA6ES,EA7ETN,EAAAA,EAAAA,IA6ES2B,GAAA,CA7EAC,OAAQ,IAAE,C,iBACjB,IAkBS,EAlBT5B,EAAAA,EAAAA,IAkBS6B,GAAA,CAlBAC,KAAM,GAAC,C,iBACd,IAgBM,EAhBNhC,EAAAA,EAAAA,IAgBM,MAhBNwI,EAgBM,C,eAfJxI,EAAAA,EAAAA,IAAc,UAAV,SAAK,KACTA,EAAAA,EAAAA,IAaM,MAbNyI,EAaM,G,aAZJ3I,EAAAA,EAAAA,IAQMyD,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAPoBrC,GAAAC,iBAAiBsH,SAASd,IAAG,CAA7Ce,EAAMC,M,WADhB9I,EAAAA,EAAAA,IAQM,OANH6D,IAAKgF,EAAKE,QACXjJ,MAAM,gB,EAENI,EAAAA,EAAAA,IAAyC,OAAzC8I,GAAyCzF,EAAAA,EAAAA,IAAnBuF,EAAQ,GAAH,IAC3B5I,EAAAA,EAAAA,IAA8C,OAA9C+I,GAA8C1F,EAAAA,EAAAA,IAAxBsF,EAAKrB,WAAS,IACpCtH,EAAAA,EAAAA,IAA4D,OAA5DgJ,GAA4D3F,EAAAA,EAAAA,IAArCvC,GAAAkE,aAAa2D,EAAK9F,MAAO,IAAF,O,MAEI,IAAzC1B,GAAAC,iBAAiBsH,SAASd,IAAIxE,S,WAAzCtD,EAAAA,EAAAA,IAEM,MAFNmJ,EAEM,EADJ/I,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAckI,M,6BAAU,gB,4BAKzClI,EAAAA,EAAAA,IAkBS6B,GAAA,CAlBAC,KAAM,GAAC,C,iBACd,IAgBM,EAhBNhC,EAAAA,EAAAA,IAgBM,MAhBNkJ,EAgBM,C,eAfJlJ,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAaM,MAbNmJ,EAaM,G,aAZJrJ,EAAAA,EAAAA,IAQMyD,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAPoBrC,GAAAC,iBAAiBsH,SAASX,cAAa,CAAvDY,EAAMC,M,WADhB9I,EAAAA,EAAAA,IAQM,OANH6D,IAAKgF,EAAKE,QACXjJ,MAAM,gB,EAENI,EAAAA,EAAAA,IAAyC,OAAzCoJ,GAAyC/F,EAAAA,EAAAA,IAAnBuF,EAAQ,GAAH,IAC3B5I,EAAAA,EAAAA,IAA8C,OAA9CqJ,GAA8ChG,EAAAA,EAAAA,IAAxBsF,EAAKrB,WAAS,IACpCtH,EAAAA,EAAAA,IAA8D,OAA9DsJ,GAA8DjG,EAAAA,EAAAA,IAAvCvC,GAAAkE,aAAa2D,EAAK9F,MAAO,IAAK,KAAE,O,MAEK,IAAnD1B,GAAAC,iBAAiBsH,SAASX,cAAc3E,S,WAAnDtD,EAAAA,EAAAA,IAEM,MAFNyJ,EAEM,EADJrJ,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAckI,M,6BAAU,gB,4BAKzClI,EAAAA,EAAAA,IAkBS6B,GAAA,CAlBAC,KAAM,GAAC,C,iBACd,IAgBM,EAhBNhC,EAAAA,EAAAA,IAgBM,MAhBNwJ,EAgBM,C,eAfJxJ,EAAAA,EAAAA,IAAc,UAAV,SAAK,KACTA,EAAAA,EAAAA,IAaM,MAbNyJ,GAaM,G,aAZJ3J,EAAAA,EAAAA,IAQMyD,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAPoBrC,GAAAC,iBAAiBsH,SAASR,WAAU,CAApDS,EAAMC,M,WADhB9I,EAAAA,EAAAA,IAQM,OANH6D,IAAKgF,EAAKE,QACXjJ,MAAM,gB,EAENI,EAAAA,EAAAA,IAAyC,OAAzC0J,IAAyCrG,EAAAA,EAAAA,IAAnBuF,EAAQ,GAAH,IAC3B5I,EAAAA,EAAAA,IAA8C,OAA9C2J,IAA8CtG,EAAAA,EAAAA,IAAxBsF,EAAKrB,WAAS,IACpCtH,EAAAA,EAAAA,IAA6D,OAA7D4J,IAA6DvG,EAAAA,EAAAA,IAAtCvC,GAAAkE,aAAa2D,EAAK9F,MAAO,IAAK,IAAC,O,MAEG,IAAhD1B,GAAAC,iBAAiBsH,SAASR,WAAW9E,S,WAAhDtD,EAAAA,EAAAA,IAEM,MAFN+J,GAEM,EADJ3J,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAckI,M,6BAAU,gB,4BAKzClI,EAAAA,EAAAA,IAkBS6B,GAAA,CAlBAC,KAAM,GAAC,C,iBACd,IAgBM,EAhBNhC,EAAAA,EAAAA,IAgBM,MAhBN8J,GAgBM,C,eAfJ9J,EAAAA,EAAAA,IAAc,UAAV,SAAK,KACTA,EAAAA,EAAAA,IAaM,MAbN+J,GAaM,G,aAZJjK,EAAAA,EAAAA,IAQMyD,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAPoBrC,GAAAC,iBAAiBsH,SAASsB,UAAS,CAAnDrB,EAAMC,M,WADhB9I,EAAAA,EAAAA,IAQM,OANH6D,IAAKgF,EAAKE,QACXjJ,MAAM,gB,EAENI,EAAAA,EAAAA,IAAyC,OAAzCiK,IAAyC5G,EAAAA,EAAAA,IAAnBuF,EAAQ,GAAH,IAC3B5I,EAAAA,EAAAA,IAA8C,OAA9CkK,IAA8C7G,EAAAA,EAAAA,IAAxBsF,EAAKrB,WAAS,IACpCtH,EAAAA,EAAAA,IAAmE,OAAnEmK,IAAmE9G,EAAAA,EAAAA,IAA5CvC,GAAAkE,aAA0B,IAAb2D,EAAK9F,MAAa,IAAK,IAAC,O,MAEJ,IAA/C1B,GAAAC,iBAAiBsH,SAASsB,UAAU5G,S,WAA/CtD,EAAAA,EAAAA,IAEM,MAFNsK,GAEM,EADJlK,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAckI,M,6BAAU,gB,4CAS7ClI,EAAAA,EAAAA,IAyBUoB,GAAA,CAzBD1B,MAAM,eAAe2B,OAAO,S,CACxBC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNzB,EAAAA,EAAAA,IAGM,MAHNqK,GAGM,EAFJnK,EAAAA,EAAAA,IAAmCe,GAAA,M,iBAA1B,IAAgB,EAAhBf,EAAAA,EAAAA,IAAgByB,M,qBACzB3B,EAAAA,EAAAA,IAAmB,YAAb,UAAM,Q,iBAGhB,IAiBW,EAjBXE,EAAAA,EAAAA,IAiBWoK,GAAA,CAjBAC,KAAMzJ,GAAA0J,iBAAkBC,OAAA,GAAOC,OAAO,MAAMC,OAAA,I,kBACrD,IAAqE,EAArEzK,EAAAA,EAAAA,IAAqE0K,GAAA,CAApDC,KAAK,SAASjI,MAAM,KAAKkI,MAAM,MAAMC,MAAM,W,aAC5DjL,EAAAA,EAAAA,IAckByD,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAbarC,GAAAC,iBAAiBuF,UAAS,CAA/CC,EAAUC,M,WADpBxF,EAAAA,EAAAA,IAckBuJ,GAAA,CAZfjH,IAAKkD,EACLjE,MAAOgE,EAASU,UACjB0D,MAAM,U,CAEKC,SAAOxJ,EAAAA,EAAAA,IAChB,EADoByJ,SAAG,EACvBlL,EAAAA,EAAAA,IAKO,QAJJJ,OAAKiE,EAAAA,EAAAA,IAAA,eAAkBqH,EAAIC,YAActE,GACpC,kB,QAEHqE,EAAIE,OAAOvE,IAAM,K,yDAQ9B3G,EAAAA,EAAAA,IAeUoB,GAAA,CAfD1B,MAAM,cAAc2B,OAAO,S,CACvBC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNzB,EAAAA,EAAAA,IAGM,MAHNqL,GAGM,EAFJnL,EAAAA,EAAAA,IAAkCe,GAAA,M,iBAAzB,IAAe,EAAff,EAAAA,EAAAA,IAAeoI,M,qBACxBtI,EAAAA,EAAAA,IAAmB,YAAb,UAAM,Q,iBAGhB,IAOS,EAPTE,EAAAA,EAAAA,IAOS2B,GAAA,CAPAC,OAAQ,IAAE,C,iBACjB,IAES,EAFT5B,EAAAA,EAAAA,IAES6B,GAAA,CAFAC,KAAM,IAAE,C,iBACf,IAAkD,EAAlDhC,EAAAA,EAAAA,IAAkD,MAAlDsL,GAAkD,Y,OAEpDpL,EAAAA,EAAAA,IAES6B,GAAA,CAFAC,KAAM,IAAE,C,iBACf,IAA2D,EAA3DhC,EAAAA,EAAAA,IAA2D,MAA3DuL,GAA2D,Y,uBAMjErL,EAAAA,EAAAA,IAgDUoB,GAAA,CAhDD1B,MAAM,eAAe2B,OAAO,S,CACxBC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNzB,EAAAA,EAAAA,IAGM,MAHNwL,GAGM,EAFJtL,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAckI,M,qBACvBpI,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,Q,iBAGf,IAwCM,EAxCNA,EAAAA,EAAAA,IAwCM,MAxCNyL,GAwCM,CAtCItK,GAAAC,iBAAiBsK,SAASC,wB,WADlCtK,EAAAA,EAAAA,IAmBW4E,GAAA,C,MAjBRC,MAAO/E,GAAAC,iBAAiBsK,SAASC,uBAAuBrE,UAAY,QACrE1G,KAAK,UACJuF,UAAU,G,kBAEX,IAYM,EAZNnG,EAAAA,EAAAA,IAYM,MAZN4L,GAYM,C,eAXJ5L,EAAAA,EAAAA,IAA+B,WAA5BA,EAAAA,EAAAA,IAAwB,cAAhB,a,KACXA,EAAAA,EAAAA,IAGK,YAFHA,EAAAA,EAAAA,IAA6F,UAAzF,WAAOqD,EAAAA,EAAAA,IAAGvC,GAAAkE,aAAa7D,GAAAC,iBAAiBsK,QAAQC,sBAAsBE,QAAS,IAAF,IACjF7L,EAAAA,EAAAA,IAA0G,UAAtG,YAAQqD,EAAAA,EAAAA,IAAGvC,GAAAkE,aAAa7D,GAAAC,iBAAiBsK,QAAQC,sBAAsBG,kBAAmB,IAAK,KAAE,K,eAEvG9L,EAAAA,EAAAA,IAA6B,WAA1BA,EAAAA,EAAAA,IAAsB,cAAd,W,KACXA,EAAAA,EAAAA,IAIK,a,aAHHF,EAAAA,EAAAA,IAEKyD,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFoBrC,GAAAC,iBAAiBsK,QAAQK,gBAAvCC,K,WAAXlM,EAAAA,EAAAA,IAEK,MAF+D6D,IAAKqI,IAAU3I,EAAAA,EAAAA,IAC9E2I,GAAU,K,4CAKrB3K,EAAAA,EAAAA,IAkBW4E,GAAA,C,MAhBTC,MAAM,aACNtF,KAAK,OACJuF,UAAU,G,kBAEX,IAWM3F,EAAA,MAAAA,EAAA,MAXNR,EAAAA,EAAAA,IAWM,OAXDJ,MAAM,mBAAiB,EAC1BI,EAAAA,EAAAA,IAAa,SAAV,WACHA,EAAAA,EAAAA,IAGK,YAFHA,EAAAA,EAAAA,IAAsB,UAAlB,kBACJA,EAAAA,EAAAA,IAAkB,UAAd,gBAENA,EAAAA,EAAAA,IAAU,SAAP,QACHA,EAAAA,EAAAA,IAGK,YAFHA,EAAAA,EAAAA,IAAyB,UAArB,qBACJA,EAAAA,EAAAA,IAA2B,UAAvB,0B,kDASLmB,GAAA8K,U,WAAXnM,EAAAA,EAAAA,IAGM,MAHNoM,GAGM,EAFJhM,EAAAA,EAAAA,IAAiDe,GAAA,CAAxCrB,MAAM,cAAY,C,iBAAC,IAAW,EAAXM,EAAAA,EAAAA,IAAWiM,M,qBACvCnM,EAAAA,EAAAA,IAAkB,SAAf,eAAW,Q,kMAepB,IACEoM,KAAM,iBACNC,WAAY,CACVC,OAAM,UAAEC,aAAY,gBAAEC,KAAI,QAAEC,SAAQ,YAAEC,OAAM,UAC5CC,aAAY,gBAAEC,QAAO,WAAEC,SAAQ,YAAEC,MAAK,SAAEC,QAAO,WAC/CC,YAAW,eAAEC,SAAQ,YAAEC,WAAU,cAAEC,KAAIA,GAAAA,MAEzC5C,IAAAA,GACE,MAAO,CACL0B,SAAS,EACT/J,cAAe,GACfO,aAAc,GACdM,UAAW,GACXqK,WAAY,GACZjK,cAAe,GACf/B,iBAAkB,KAGlBiM,YAAa,EACbC,SAAU,GAEd,EACAC,SAAU,KACLC,EAAAA,GAAAA,IAAS,CACVC,IAAKC,GAASA,EAAMD,MAGtBhK,eAAAA,GACE,IAAIkK,EAAWC,KAAKR,WAapB,OAXIQ,KAAK1L,gBACPyL,EAAWA,EAASE,OAAOnK,GACzBA,EAAOS,YAAY2J,cAAcC,SAASH,KAAK1L,cAAc4L,gBAC7DpK,EAAOW,MAAMC,UAAUwJ,cAAcC,SAASH,KAAK1L,cAAc4L,iBAIjEF,KAAKnL,eACPkL,EAAWA,EAASE,OAAOnK,GAAUA,EAAOgB,eAAiBkJ,KAAKnL,eAG7DkL,CACT,EAEAnD,gBAAAA,GACE,IAAKoD,KAAKxM,kBAAkBsG,mBAAoB,MAAO,GAEvD,MAAMsG,EAAU,CACd,CAAErK,IAAK,eAAgBf,MAAO,QAASI,OAASiL,GAAMA,GACtD,CAAEtK,IAAK,yBAA0Bf,MAAO,aAAcI,OAASiL,GAAMA,EAAI,MACzE,CAAEtK,IAAK,sBAAuBf,MAAO,SAAUI,OAASiL,GAAMA,EAAI,KAClE,CAAEtK,IAAK,mBAAoBf,MAAO,QAASI,OAASiL,GAAMA,GAC1D,CAAEtK,IAAK,mBAAoBf,MAAO,QAASI,OAASiL,GAAMA,GAC1D,CAAEtK,IAAK,wBAAyBf,MAAO,OAAQI,OAASiL,GAAMA,GAC9D,CAAEtK,IAAK,0BAA2Bf,MAAO,SAAUI,OAASiL,IAAW,IAAJA,GAASC,QAAQ,GAAK,KACzF,CAAEvK,IAAK,oCAAqCf,MAAO,UAAWI,OAASiL,IAAW,IAAJA,GAASC,QAAQ,GAAK,MAGtG,OAAOF,EAAQG,IAAIC,IACjB,MAAMhD,EAAS,CAAC,EAChB,IAAIiD,EAAY,KACZlD,EAAa,KAGjB,MAAMmD,EAAanH,OAAOC,KAAKwG,KAAKxM,iBAAiBuF,WA8BrD,OA3BA2H,EAAWC,QAAQ1H,IAEjB,IAAK+G,KAAKxM,iBAAiBsG,mBAAmBb,GAE5C,YADAuE,EAAOvE,GAAU,KAInB,MAAMhE,EAAQ+K,KAAKY,eAAeZ,KAAKxM,iBAAiBsG,mBAAmBb,GAASuH,EAAOzK,KAI3F,GAHAyH,EAAOvE,GAAUuH,EAAOpL,OAAOH,GAAS,GAG1B,OAAVA,QAA4B4L,IAAV5L,EACpB,GAAkB,OAAdwL,EACFA,EAAYxL,EACZsI,EAAatE,MACR,CACL,MAAM6H,EAAWN,EAAOzK,IAAIoK,SAAS,kBAAoBK,EAAOzK,IAAIoK,SAAS,cACzElL,EAAQwL,EACRxL,EAAQwL,EACRK,IACFL,EAAYxL,EACZsI,EAAatE,EAEjB,IAIG,CACLuH,OAAQA,EAAOxL,MACfwI,SACAD,eAGN,GAGF,aAAMwD,SACEf,KAAKgB,aACb,EAEAC,aAAAA,GAEEC,OAAOC,oBAAoB,SAAUnB,KAAKoB,cAGtCpB,KAAKqB,MAAMC,UACbC,GAAAA,GAAyBvB,KAAKqB,MAAMC,WAAWE,UAE7CxB,KAAKqB,MAAMI,mBACbF,GAAAA,GAAyBvB,KAAKqB,MAAMI,oBAAoBD,SAE5D,EAEAE,QAAS,CACP,iBAAMV,GACJhB,KAAK3B,SAAU,EACf,IACE,MAAMsD,EAAS,CACbC,WAAY5B,KAAKH,IAAI7J,GACrB6L,UAAW,IAGPC,QAAiB9B,KAAK+B,KAAKC,eAAeL,GAChD3B,KAAKR,WAAasC,EAASnF,KAAKsF,QAAUH,EAASnF,KAAKA,MAAQ,EAClE,CAAE,MAAOuF,GACPC,GAAAA,GAAUD,MAAM,cAAgBA,EAAME,SAAW,QACnD,CAAE,QACApC,KAAK3B,SAAU,CACjB,CACF,EAEA1J,YAAAA,GACE,EAGFuB,UAAAA,CAAWJ,GACT,OAAOkK,KAAKzK,cAAc8M,KAAK5L,GAAQA,EAAKT,KAAOF,EAAOE,GAC5D,EAEAG,SAAAA,CAAUL,GACR,GAAIkK,KAAKzK,cAAcC,QAAU,EAAG,OAAO,EAG3C,MAAMyD,EAAS+G,KAAKsC,UAAUxM,GAC9B,QAAKmD,IAEG+G,KAAKzK,cAAc8M,KAAK5L,GAAQuJ,KAAKsC,UAAU7L,KAAUwC,EACnE,EAEAqJ,SAAAA,CAAUxM,GACR,OAAOA,EAAOW,MAAMT,IAAMF,EAAOmD,QAAUnD,EAAOmF,OACpD,EAEA7E,eAAAA,CAAgBN,IACTkK,KAAK7J,UAAUL,IAAYkK,KAAK9J,WAAWJ,MAE5CkK,KAAK9J,WAAWJ,GAClBkK,KAAKzK,cAAgByK,KAAKzK,cAAc0K,OAAOxJ,GAAQA,EAAKT,KAAOF,EAAOE,IAE1EgK,KAAKzK,cAAcgN,KAAKzM,GAE5B,EAEA,uBAAMT,GACJ,GAAI2K,KAAKzK,cAAcC,OAAS,EAC9B2M,GAAAA,GAAUK,QAAQ,qBADpB,CAKAxC,KAAK3B,SAAU,EACf,IAEE,MAAMoE,EAAUzC,KAAKzK,cAAcgL,IAAI9J,GAAQuJ,KAAKsC,UAAU7L,IACxDkL,EAAS,CACbe,SAAU,IAAI,IAAIC,IAAIF,IACtBG,iBAAkB,IAGpBC,QAAQC,IAAI,QAASnB,GAErB,MAAMG,QAAiB9B,KAAK+B,KAAKgB,uBAAuBpB,GACxD3B,KAAKxM,iBAAmBsO,EAASnF,KAAKqG,kBAEtCH,QAAQC,IAAI,YAAaG,KAAKC,UAAUlD,KAAKxM,mBAC7CqP,QAAQC,IAAI,QAAS9C,KAAKxM,iBAAiBuF,WAC3C8J,QAAQC,IAAI,QAAS9C,KAAKxM,iBAAiBsG,oBAC3C+I,QAAQC,IAAI,QAAS9C,KAAKxM,iBAAiBsH,UAC3C+H,QAAQC,IAAI,SAAU9C,KAAKxM,iBAAiB2P,eAG5C,MAAMC,EAAc,GACpB7J,OAAOC,KAAKwG,KAAKxM,iBAAiBuF,WAAW4H,QAAQ1H,IAC9C+G,KAAKxM,iBAAiBsG,mBAAmBb,IAC5CmK,EAAYb,KAAKvC,KAAKxM,iBAAiBuF,UAAUE,GAAQS,aAIzD0J,EAAY5N,OAAS,GACvB2M,GAAAA,GAAUK,QAAQ,kBAAkBY,EAAYC,KAAK,SAIvDrD,KAAKsD,UAAU,KACbtD,KAAKuD,iBAGPpB,GAAAA,GAAUqB,QAAQ,SACpB,CAAE,MAAOtB,GACPC,GAAAA,GAAUD,MAAM,YAAcA,EAAME,SAAW,SAC/CS,QAAQX,MAAM,UAAWA,EAC3B,CAAE,QACAlC,KAAK3B,SAAU,CACjB,CA7CA,CA8CF,EAEAkF,YAAAA,GACEvD,KAAKyD,iBACLzD,KAAK0D,0BAGLxC,OAAOyC,iBAAiB,SAAU3D,KAAKoB,aACzC,EAGAA,YAAAA,GACMpB,KAAKqB,MAAMC,UAAYtB,KAAKqB,MAAMI,oBACpCF,GAAAA,GAAyBvB,KAAKqB,MAAMC,WAAWsC,SAC/CrC,GAAAA,GAAyBvB,KAAKqB,MAAMI,oBAAoBmC,SAE5D,EAEAH,cAAAA,GACE,IAAKzD,KAAKqB,MAAMC,SAEd,YADAuB,QAAQgB,KAAK,iBAIf,MAAMC,EAAQvC,GAAAA,GAAavB,KAAKqB,MAAMC,UAChCyC,EAAS,CACbzL,MAAO,CAAE0L,KAAM,QAASC,KAAM,UAC9BC,QAAS,CAAEC,QAAS,QACpBC,OAAQ,CAAEC,OAAQ,GAClBC,MAAO,CAAEtR,KAAM,WAAY2J,KAAM,IACjC4H,MAAO,CAAEvR,KAAM,QAASwL,KAAM,OAC9BgG,OAAQ,GACRC,KAAM,CACJR,KAAM,KACNS,MAAO,KACPL,OAAQ,MACRM,IAAK,MACLC,cAAc,IAIlB,IAAIC,GAAU,EACVC,EAAmB,GAGvB,GAAI9E,KAAKxM,kBAAkB2P,cAAe,CACxCN,QAAQC,IAAI,iBAAkB9C,KAAKxM,iBAAiB2P,eAGpD,MAAMzC,EAAanH,OAAOC,KAAKwG,KAAKxM,iBAAiBuF,WACrD8J,QAAQC,IAAI,UAAWpC,GAGvBA,EAAWC,QAAQ1H,IACjB,IAAK+G,KAAKxM,iBAAiB2P,cAAclK,GAEvC,YADA4J,QAAQC,IAAI,MAAM7J,aAIpB,MAAM8L,EAAW/E,KAAKxM,iBAAiB2P,cAAclK,GACjD8L,GAAYA,EAASvP,OAAS,IAChCqN,QAAQC,IAAI,MAAM7J,OAAY8L,EAASvP,eACP,IAA5BsP,EAAiBtP,SACnBsP,EAAmBC,EAASxE,IAAIyE,GAASA,EAAMC,UAMjDH,EAAiBtP,OAAS,IAC5BuO,EAAOO,MAAM3H,KAAOmI,EAAiBvE,IAAI0E,GAAQ,IAAIC,KAAKD,GAAME,uBAIlEzE,EAAWC,QAAQ1H,IACjB,MAAMvC,EAAWsJ,KAAKxM,iBAAiBuF,UAAUE,IAASS,WAAa,KAAKT,IAE5E,IAAK+G,KAAKxM,iBAAiB2P,cAAclK,GAYvC,YAVA8K,EAAOS,OAAOjC,KAAK,CACjB/D,KAAM9H,EACN1D,KAAM,OACN2J,KAAM,GACNyI,QAAQ,EACRC,YAAY,EACZC,UAAW,CACTpI,MAAO,KAMb,MAAM6H,EAAW/E,KAAKxM,iBAAiB2P,cAAclK,GAEjD8L,GAAYA,EAASvP,OAAS,GAChCqP,GAAU,EAEVd,EAAOS,OAAOjC,KAAK,CACjB/D,KAAM9H,EACN1D,KAAM,OACN2J,KAAMoI,EAASxE,IAAIyE,GAASA,EAAMhL,KAClCoL,QAAQ,EACRC,YAAY,EACZC,UAAW,CACTpI,MAAO,MAKX6G,EAAOS,OAAOjC,KAAK,CACjB/D,KAAM9H,EACN1D,KAAM,OACN2J,KAAM,GACNyI,QAAQ,EACRC,YAAY,EACZC,UAAW,CACTpI,MAAO,MAKjB,CAGK2H,IACHd,EAAOzL,MAAM0L,KAAO,eAGtBnB,QAAQC,IAAI,WAAYiB,GACxBD,EAAMyB,UAAUxB,EAClB,EAEAL,uBAAAA,GACE,IAAK1D,KAAKqB,MAAMI,kBAEd,YADAoB,QAAQgB,KAAK,kBAIf,MAAMC,EAAQvC,GAAAA,GAAavB,KAAKqB,MAAMI,mBAChCsC,EAAS,CACbzL,MAAO,CAAE0L,KAAM,SAAUC,KAAM,UAC/BC,QAAS,CAAEC,QAAS,QACpBC,OAAQ,CAAEC,OAAQ,GAClBC,MAAO,CAAEtR,KAAM,WAAY2J,KAAM,IACjC4H,MAAO,CAAEvR,KAAM,QAASwL,KAAM,YAC9BgG,OAAQ,GACRC,KAAM,CACJR,KAAM,KACNS,MAAO,KACPL,OAAQ,MACRM,IAAK,MACLC,cAAc,IAIlB,IAAIC,GAAU,EACVC,EAAmB,GAEvB,GAAI9E,KAAKxM,kBAAkB2P,cAAe,CACxCN,QAAQC,IAAI,kBAAmB9C,KAAKxM,iBAAiB2P,eAGrD,MAAMzC,EAAanH,OAAOC,KAAKwG,KAAKxM,iBAAiBuF,WAGrD2H,EAAWC,QAAQ1H,IACjB,IAAK+G,KAAKxM,iBAAiB2P,cAAclK,GAEvC,YADA4J,QAAQC,IAAI,MAAM7J,aAIpB,MAAM8L,EAAW/E,KAAKxM,iBAAiB2P,cAAclK,GACjD8L,GAAYA,EAASvP,OAAS,IAChCqN,QAAQC,IAAI,MAAM7J,OAAY8L,EAASvP,eACP,IAA5BsP,EAAiBtP,SACnBsP,EAAmBC,EAASxE,IAAIyE,GAASA,EAAMC,UAMjDH,EAAiBtP,OAAS,IAC5BuO,EAAOO,MAAM3H,KAAOmI,EAAiBvE,IAAI0E,GAAQ,IAAIC,KAAKD,GAAME,uBAIlEzE,EAAWC,QAAQ1H,IACjB,MAAMvC,EAAWsJ,KAAKxM,iBAAiBuF,UAAUE,IAASS,WAAa,KAAKT,IAE5E,IAAK+G,KAAKxM,iBAAiB2P,cAAclK,GAYvC,YAVA8K,EAAOS,OAAOjC,KAAK,CACjB/D,KAAM9H,EACN1D,KAAM,OACN2J,KAAM,GACNyI,QAAQ,EACRC,YAAY,EACZC,UAAW,CACTpI,MAAO,KAMb,MAAM6H,EAAW/E,KAAKxM,iBAAiB2P,cAAclK,GAEjD8L,GAAYA,EAASvP,OAAS,GAChCqP,GAAU,EAEVd,EAAOS,OAAOjC,KAAK,CACjB/D,KAAM9H,EACN1D,KAAM,OACN2J,KAAMoI,EAASxE,IAAIyE,GAASA,EAAM7K,eAClCiL,QAAQ,EACRC,YAAY,EACZC,UAAW,CACTpI,MAAO,MAKX6G,EAAOS,OAAOjC,KAAK,CACjB/D,KAAM9H,EACN1D,KAAM,OACN2J,KAAM,GACNyI,QAAQ,EACRC,YAAY,EACZC,UAAW,CACTpI,MAAO,MAKjB,CAGK2H,IACHd,EAAOzL,MAAM0L,KAAO,gBAGtBnB,QAAQC,IAAI,YAAaiB,GACzBD,EAAMyB,UAAUxB,EAClB,EAEArL,eAAAA,GACEsH,KAAKxM,iBAAmB,KACxBwM,KAAKzK,cAAgB,EACvB,EAEAqD,YAAAA,GAEEuJ,GAAAA,GAAUqD,KAAK,aACjB,EAEArS,kBAAAA,GACE+N,OAAOuE,QAAQC,MACjB,EAEA7O,aAAAA,CAAc8O,GACZ,MAAMC,EAAY,CAChB,EAAK,UACL,EAAK,UACL,GAAM,UAER,OAAOA,EAAUD,IAAW,MAC9B,EAEA3O,aAAAA,CAAc2O,GACZ,MAAMC,EAAY,CAChB,EAAK,MACL,EAAK,MACL,GAAM,QAER,OAAOA,EAAUD,IAAW,IAC9B,EAEAhO,cAAAA,CAAe7B,GACb,OAAKA,EAAO+P,eAA0C,IAAzB/P,EAAO+P,gBAC3B/P,EAAOgQ,iBAAmB,GAAKhQ,EAAO+P,cAAgB,KAAKvF,QAAQ,GADZ,CAElE,EAEAtI,UAAAA,CAAW+N,GACT,OAAKA,EACE,IAAIb,KAAKa,GAASC,iBADJ,GAEvB,EAEA5O,YAAAA,CAAanC,EAAOgR,EAAY,GAC9B,OAAc,OAAVhR,QAA4B4L,IAAV5L,EAA4B,IAC3CA,EAAMqL,QAAQ2F,EACvB,EAEArF,cAAAA,CAAesF,EAAKvT,GAClB,OAAOA,EAAKwT,MAAM,KAAKC,OAAO,CAACC,EAAStQ,IAAQsQ,IAAUtQ,GAAMmQ,EAClE,EAEAzM,cAAAA,CAAeR,GACb,YAAyE4H,IAAlEb,KAAKxM,kBAAkBsG,qBAAqBb,IAASc,QAC9D,I,YC33BJ,MAAMuM,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASC,IAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/TaskComparison.vue", "webpack://frontend-web/./src/views/PerformanceTest/TaskComparison.vue?715b"], "sourcesContent": ["<template>\n  <div class=\"task-comparison\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <el-breadcrumb separator=\"/\">\n        <el-breadcrumb-item :to=\"{ path: '/performance/result' }\">性能测试</el-breadcrumb-item>\n        <el-breadcrumb-item>任务对比</el-breadcrumb-item>\n      </el-breadcrumb>\n      <div class=\"header-content\">\n        <div class=\"header-title\">\n          <h1>任务性能对比分析</h1>\n          <p>对比不同任务的性能指标，发现优化机会</p>\n        </div>\n        <div class=\"header-actions\">\n          <el-button type=\"primary\" @click=\"returnToReportList\" plain>\n            <el-icon><Back /></el-icon>返回报告列表\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 任务选择区域 -->\n    <el-card v-if=\"!comparisonResult\" class=\"selection-card\" shadow=\"hover\">\n      <template #header>\n        <div class=\"card-header\">\n          <el-icon><DataAnalysis /></el-icon>\n          <span>选择对比任务</span>\n        </div>\n      </template>\n\n      <!-- 搜索过滤 -->\n      <div class=\"filter-section\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <el-input\n              v-model=\"searchKeyword\"\n              placeholder=\"搜索任务名称\"\n              prefix-icon=\"Search\"\n              clearable\n              @input=\"handleSearch\"\n            />\n          </el-col>\n          <el-col :span=\"6\">\n            <el-select v-model=\"statusFilter\" placeholder=\"报告状态\" clearable @change=\"handleSearch\">\n              <el-option label=\"全部\" value=\"\" />\n              <el-option label=\"已完成\" value=\"0\" />\n              <el-option label=\"执行中\" value=\"1\" />\n              <el-option label=\"运行失败\" value=\"99\" />\n            </el-select>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-date-picker\n              v-model=\"timeRange\"\n              type=\"datetimerange\"\n              start-placeholder=\"开始时间\"\n              end-placeholder=\"结束时间\"\n              format=\"YYYY-MM-DD HH:mm:ss\"\n              value-format=\"YYYY-MM-DD HH:mm:ss\"\n              @change=\"handleSearch\"\n            />\n          </el-col>\n          <el-col :span=\"4\">\n            <el-button type=\"primary\" @click=\"performComparison\" :disabled=\"selectedTasks.length < 2\">\n              开始对比 ({{ selectedTasks.length }})\n            </el-button>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 任务列表 -->\n      <div class=\"task-list\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\" v-for=\"report in filteredReports\" :key=\"report.id\">\n            <div\n              class=\"task-card\"\n              :class=\"{ 'selected': isSelected(report), 'disabled': !canSelect(report) }\"\n              @click=\"toggleSelection(report)\"\n            >\n              <div class=\"task-header\">\n                <div class=\"task-info\">\n                  <h4>{{ report.reportName }}</h4>\n                  <p class=\"task-name\">{{ report.task?.taskName || '未知任务' }}</p>\n                </div>\n                <div class=\"task-status\">\n                  <el-tag\n                    :type=\"getStatusType(report.reportStatus)\"\n                    size=\"small\"\n                  >\n                    {{ getStatusText(report.reportStatus) }}\n                  </el-tag>\n                </div>\n              </div>\n              <div class=\"task-metrics\">\n                <div class=\"metric-item\">\n                  <span class=\"metric-label\">TPS</span>\n                  <span class=\"metric-value\">{{ formatNumber(report.avgTps || 0, 2) }}</span>\n                </div>\n                <div class=\"metric-item\">\n                  <span class=\"metric-label\">响应时间</span>\n                  <span class=\"metric-value\">{{ formatNumber(report.avgResponseTime || 0, 2) }}ms</span>\n                </div>\n                <div class=\"metric-item\">\n                  <span class=\"metric-label\">成功率</span>\n                  <span class=\"metric-value\">{{ getSuccessRate(report) }}%</span>\n                </div>\n              </div>\n              <div class=\"task-footer\">\n                <span class=\"executor\">{{ report.executor || '未知' }}</span>\n                <span class=\"time\">{{ formatDate(report.create_time) }}</span>\n              </div>\n              <div class=\"selection-indicator\" v-if=\"isSelected(report)\">\n                <el-icon><Check /></el-icon>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 选择提示 -->\n      <div class=\"selection-hint\">\n        <el-alert\n          :title=\"`已选择 ${selectedTasks.length} 个任务${selectedTasks.length < 2 ? '，至少需要选择2个任务进行对比' : selectedTasks.length > 5 ? '，最多支持5个任务对比' : ''}`\"\n          :type=\"selectedTasks.length < 2 ? 'warning' : selectedTasks.length > 5 ? 'error' : 'success'\"\n          :closable=\"false\"\n          show-icon\n        />\n      </div>\n    </el-card>\n\n    <!-- 对比结果 -->\n\n    <div v-if=\"comparisonResult\" class=\"comparison-result\">\n      <!-- 操作栏 -->\n      <div class=\"result-actions\">\n        <el-button @click=\"resetComparison\" icon=\"ArrowLeft\">重新选择</el-button>\n        <el-button type=\"success\" @click=\"exportReport\" icon=\"Download\">导出报告</el-button>\n      </div>\n\n      <!-- 对比概览 -->\n      <el-card class=\"overview-card\" shadow=\"hover\">\n        <template #header>\n          <div class=\"card-header\">\n            <el-icon><PieChart /></el-icon>\n            <span>对比概览</span>\n          </div>\n        </template>\n        <el-row :gutter=\"20\">\n          <el-col :xs=\"24\" :sm=\"24\" :md=\"24 / Math.min(Object.keys(comparisonResult.task_info).length, 4)\" \n                 v-for=\"(taskInfo, taskId) in comparisonResult.task_info\" :key=\"taskId\">\n            <div class=\"overview-item\" :class=\"{'no-data': !hasMetricsData(taskId)}\">\n              <h3>{{ taskInfo.task_name }}</h3>\n              <div v-if=\"hasMetricsData(taskId)\" class=\"overview-stats\">\n                <div class=\"stat-item\">\n                  <span class=\"stat-value\">{{ formatNumber(comparisonResult.comparison_metrics[taskId]?.averages?.tps, 2) }}</span>\n                  <span class=\"stat-label\">平均TPS</span>\n                </div>\n                <div class=\"stat-item\">\n                  <span class=\"stat-value\">{{ formatNumber(comparisonResult.comparison_metrics[taskId]?.averages?.response_time, 2) }}ms</span>\n                  <span class=\"stat-label\">响应时间</span>\n                </div>\n                <div class=\"stat-item\">\n                  <span class=\"stat-value\">{{ formatNumber(comparisonResult.comparison_metrics[taskId]?.averages?.error_rate, 2) }}%</span>\n                  <span class=\"stat-label\">错误率</span>\n                </div>\n              </div>\n              <div v-else class=\"no-data-message\">\n                <el-icon><InfoFilled /></el-icon> 无可用数据\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </el-card>\n\n      <!-- 性能排名 -->\n      <el-card class=\"ranking-card\" shadow=\"hover\">\n        <template #header>\n          <div class=\"card-header\">\n            <el-icon><TrendCharts /></el-icon>\n            <span>性能排名</span>\n            <el-button v-if=\"Object.keys(comparisonResult.comparison_metrics).length < Object.keys(comparisonResult.task_info).length\" \n                     type=\"warning\" size=\"small\" plain style=\"margin-left: 10px\">\n              部分任务数据不完整\n            </el-button>\n          </div>\n        </template>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <div class=\"ranking-section\">\n              <h4>TPS排名</h4>\n              <div class=\"ranking-list\">\n                <div\n                  v-for=\"(item, index) in comparisonResult.rankings.tps\"\n                  :key=\"item.task_id\"\n                  class=\"ranking-item\"\n                >\n                  <span class=\"rank\">{{ index + 1 }}</span>\n                  <span class=\"name\">{{ item.task_name }}</span>\n                  <span class=\"value\">{{ formatNumber(item.value, 2) }}</span>\n                </div>\n                <div v-if=\"comparisonResult.rankings.tps.length === 0\" class=\"no-ranking-data\">\n                  <el-icon><InfoFilled /></el-icon> 无排名数据\n                </div>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"ranking-section\">\n              <h4>响应时间排名</h4>\n              <div class=\"ranking-list\">\n                <div\n                  v-for=\"(item, index) in comparisonResult.rankings.response_time\"\n                  :key=\"item.task_id\"\n                  class=\"ranking-item\"\n                >\n                  <span class=\"rank\">{{ index + 1 }}</span>\n                  <span class=\"name\">{{ item.task_name }}</span>\n                  <span class=\"value\">{{ formatNumber(item.value, 2) }}ms</span>\n                </div>\n                <div v-if=\"comparisonResult.rankings.response_time.length === 0\" class=\"no-ranking-data\">\n                  <el-icon><InfoFilled /></el-icon> 无排名数据\n                </div>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"ranking-section\">\n              <h4>错误率排名</h4>\n              <div class=\"ranking-list\">\n                <div\n                  v-for=\"(item, index) in comparisonResult.rankings.error_rate\"\n                  :key=\"item.task_id\"\n                  class=\"ranking-item\"\n                >\n                  <span class=\"rank\">{{ index + 1 }}</span>\n                  <span class=\"name\">{{ item.task_name }}</span>\n                  <span class=\"value\">{{ formatNumber(item.value, 2) }}%</span>\n                </div>\n                <div v-if=\"comparisonResult.rankings.error_rate.length === 0\" class=\"no-ranking-data\">\n                  <el-icon><InfoFilled /></el-icon> 无排名数据\n                </div>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"ranking-section\">\n              <h4>稳定性排名</h4>\n              <div class=\"ranking-list\">\n                <div\n                  v-for=\"(item, index) in comparisonResult.rankings.stability\"\n                  :key=\"item.task_id\"\n                  class=\"ranking-item\"\n                >\n                  <span class=\"rank\">{{ index + 1 }}</span>\n                  <span class=\"name\">{{ item.task_name }}</span>\n                  <span class=\"value\">{{ formatNumber(item.value * 100, 2) }}%</span>\n                </div>\n                <div v-if=\"comparisonResult.rankings.stability.length === 0\" class=\"no-ranking-data\">\n                  <el-icon><InfoFilled /></el-icon> 无排名数据\n                </div>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </el-card>\n\n      <!-- 详细指标对比 -->\n      <el-card class=\"metrics-card\" shadow=\"hover\">\n        <template #header>\n          <div class=\"card-header\">\n            <el-icon><DataAnalysis /></el-icon>\n            <span>详细指标对比</span>\n          </div>\n        </template>\n        <el-table :data=\"metricsTableData\" stripe height=\"350\" border>\n          <el-table-column prop=\"metric\" label=\"指标\" width=\"150\" fixed=\"left\" />\n          <el-table-column\n            v-for=\"(taskInfo, taskId) in comparisonResult.task_info\"\n            :key=\"taskId\"\n            :label=\"taskInfo.task_name\"\n            align=\"center\"\n          >\n            <template #default=\"{ row }\">\n              <span\n                :class=\"{ 'best-value': row.bestTaskId == taskId }\"\n                class=\"metric-cell\"\n              >\n                {{ row.values[taskId] }}\n              </span>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-card>\n\n      <!-- 性能趋势图表 -->\n      <el-card class=\"charts-card\" shadow=\"hover\">\n        <template #header>\n          <div class=\"card-header\">\n            <el-icon><TrendCharts /></el-icon>\n            <span>性能趋势对比</span>\n          </div>\n        </template>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div ref=\"tpsChart\" class=\"chart-container\"></div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div ref=\"responseTimeChart\" class=\"chart-container\"></div>\n          </el-col>\n        </el-row>\n      </el-card>\n\n      <!-- 总结与建议 -->\n      <el-card class=\"summary-card\" shadow=\"hover\">\n        <template #header>\n          <div class=\"card-header\">\n            <el-icon><InfoFilled /></el-icon>\n            <span>总结与建议</span>\n          </div>\n        </template>\n        <div class=\"summary-content\">\n          <el-alert\n            v-if=\"comparisonResult.summary?.best_performance_task\"\n            :title=\"comparisonResult.summary?.best_performance_task?.task_name + ' 表现最佳'\"\n            type=\"success\"\n            :closable=\"false\"\n          >\n            <div class=\"summary-details\">\n              <p><strong>最佳性能指标:</strong></p>\n              <ul>\n                <li>平均TPS: {{ formatNumber(comparisonResult.summary.best_performance_task.avg_tps, 2) }}</li>\n                <li>平均响应时间: {{ formatNumber(comparisonResult.summary.best_performance_task.avg_response_time, 2) }}ms</li>\n              </ul>\n              <p><strong>优化建议:</strong></p>\n              <ul>\n                <li v-for=\"suggestion in comparisonResult.summary.recommendations\" :key=\"suggestion\">\n                  {{ suggestion }}\n                </li>\n              </ul>\n            </div>\n          </el-alert>\n          <el-alert\n            v-else\n            title=\"无法确定最佳表现任务\"\n            type=\"info\"\n            :closable=\"false\"\n          >\n            <div class=\"summary-details\">\n              <p>可能的原因:</p>\n              <ul>\n                <li>部分任务没有足够的性能数据</li>\n                <li>任务性能指标不完整</li>\n              </ul>\n              <p>建议:</p>\n              <ul>\n                <li>选择有完整性能数据的任务进行对比</li>\n                <li>确保任务已经执行完成并生成了性能报告</li>\n              </ul>\n            </div>\n          </el-alert>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 加载遮罩 -->\n    <div v-if=\"loading\" class=\"loading-overlay\">\n      <el-icon class=\"is-loading\"><Loading /></el-icon>\n      <p>正在分析对比数据...</p>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ElMessage } from 'element-plus'\nimport { mapState } from 'vuex'\nimport * as echarts from 'echarts'\nimport { \n  Search, DataAnalysis, View, Download, Delete,\n  RefreshRight, Setting, Calendar, Check, Loading,\n  TrendCharts, PieChart, InfoFilled, Back\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'TaskComparison',\n  components: {\n    Search, DataAnalysis, View, Download, Delete,\n    RefreshRight, Setting, Calendar, Check, Loading,\n    TrendCharts, PieChart, InfoFilled, Back\n  },\n  data() {\n    return {\n      loading: false,\n      searchKeyword: '',\n      statusFilter: '',\n      timeRange: [],\n      reportList: [],\n      selectedTasks: [],\n      comparisonResult: null,\n      \n      // 分页\n      currentPage: 1,\n      pageSize: 12\n    }\n  },\n  computed: {\n    ...mapState({\n      pro: state => state.pro\n    }),\n    \n    filteredReports() {\n      let filtered = this.reportList\n      \n      if (this.searchKeyword) {\n        filtered = filtered.filter(report => \n          report.reportName?.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||\n          report.task?.taskName?.toLowerCase().includes(this.searchKeyword.toLowerCase())\n        )\n      }\n      \n      if (this.statusFilter) {\n        filtered = filtered.filter(report => report.reportStatus === this.statusFilter)\n      }\n      \n      return filtered\n    },\n    \n    metricsTableData() {\n      if (!this.comparisonResult?.comparison_metrics) return []\n      \n      const metrics = [\n        { key: 'averages.tps', label: '平均TPS', format: (v) => v },\n        { key: 'averages.response_time', label: '平均响应时间(ms)', format: (v) => v + 'ms' },\n        { key: 'averages.error_rate', label: '错误率(%)', format: (v) => v + '%' },\n        { key: 'extremes.max_tps', label: '最大TPS', format: (v) => v },\n        { key: 'extremes.min_tps', label: '最小TPS', format: (v) => v },\n        { key: 'totals.total_requests', label: '总请求数', format: (v) => v },\n        { key: 'stability.tps_stability', label: 'TPS稳定性', format: (v) => (v * 100).toFixed(1) + '%' },\n        { key: 'stability.response_time_stability', label: '响应时间稳定性', format: (v) => (v * 100).toFixed(1) + '%' }\n      ]\n      \n      return metrics.map(metric => {\n        const values = {}\n        let bestValue = null\n        let bestTaskId = null\n        \n        // 获取所有任务ID，包括可能没有指标数据的任务\n        const allTaskIds = Object.keys(this.comparisonResult.task_info)\n        \n        // 先遍历所有任务ID，确保每个任务都有一个值（即使是'-'）\n        allTaskIds.forEach(taskId => {\n          // 如果任务没有指标数据，显示'-'\n          if (!this.comparisonResult.comparison_metrics[taskId]) {\n            values[taskId] = '-'\n            return\n          }\n          \n          const value = this.getNestedValue(this.comparisonResult.comparison_metrics[taskId], metric.key)\n          values[taskId] = metric.format(value || 0)\n          \n          // 只有有效数据的任务才参与最佳值比较\n          if (value !== null && value !== undefined) {\n            if (bestValue === null) {\n              bestValue = value\n              bestTaskId = taskId\n            } else {\n              const isBetter = metric.key.includes('response_time') || metric.key.includes('error_rate') \n                ? value < bestValue \n                : value > bestValue\n              if (isBetter) {\n                bestValue = value\n                bestTaskId = taskId\n              }\n            }\n          }\n        })\n        \n        return {\n          metric: metric.label,\n          values,\n          bestTaskId\n        }\n      })\n    }\n  },\n  \n  async mounted() {\n    await this.loadReports()\n  },\n  \n  beforeUnmount() {\n    // 移除窗口大小变化的事件监听器\n    window.removeEventListener('resize', this.handleResize)\n    \n    // 销毁图表实例\n    if (this.$refs.tpsChart) {\n      echarts.getInstanceByDom(this.$refs.tpsChart)?.dispose()\n    }\n    if (this.$refs.responseTimeChart) {\n      echarts.getInstanceByDom(this.$refs.responseTimeChart)?.dispose()\n    }\n  },\n  \n  methods: {\n    async loadReports() {\n      this.loading = true\n      try {\n        const params = {\n          project_id: this.pro.id,\n          page_size: 50  // 加载更多数据用于选择\n        }\n        \n        const response = await this.$api.getTaskReports(params)\n        this.reportList = response.data.result || response.data.data || []\n      } catch (error) {\n        ElMessage.error('加载报告列表失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    handleSearch() {\n      // 搜索逻辑已在computed中实现\n    },\n    \n    isSelected(report) {\n      return this.selectedTasks.some(task => task.id === report.id)\n    },\n    \n    canSelect(report) {\n      if (this.selectedTasks.length >= 5) return false\n      \n      // 检查是否已经选择了同一任务的其他报告\n      const taskId = this.getTaskId(report)\n      if (!taskId) return false\n      \n      return !this.selectedTasks.some(task => this.getTaskId(task) === taskId)\n    },\n    \n    getTaskId(report) {\n      return report.task?.id || report.taskId || report.task_id\n    },\n    \n    toggleSelection(report) {\n      if (!this.canSelect(report) && !this.isSelected(report)) return\n      \n      if (this.isSelected(report)) {\n        this.selectedTasks = this.selectedTasks.filter(task => task.id !== report.id)\n      } else {\n        this.selectedTasks.push(report)\n      }\n    },\n    \n    async performComparison() {\n      if (this.selectedTasks.length < 2) {\n        ElMessage.warning('请至少选择2个任务进行对比')\n        return\n      }\n      \n      this.loading = true\n      try {\n        // 获取任务ID，使用任务ID而不是报告ID\n        const taskIds = this.selectedTasks.map(task => this.getTaskId(task))\n        const params = {\n          task_ids: [...new Set(taskIds)], // 去重\n          time_range_hours: 72 // 增加时间范围，确保有足够的数据\n        }\n        \n        console.log('对比参数:', params)\n        \n        const response = await this.$api.compareTaskPerformance(params)\n        this.comparisonResult = response.data.comparison_report\n        \n        console.log('对比结果完整数据:', JSON.stringify(this.comparisonResult))\n        console.log('任务信息:', this.comparisonResult.task_info)\n        console.log('对比指标:', this.comparisonResult.comparison_metrics)\n        console.log('排名数据:', this.comparisonResult.rankings)\n        console.log('时间线数据:', this.comparisonResult.timeline_data)\n        \n        // 检查是否有任务没有数据\n        const noDataTasks = []\n        Object.keys(this.comparisonResult.task_info).forEach(taskId => {\n          if (!this.comparisonResult.comparison_metrics[taskId]) {\n            noDataTasks.push(this.comparisonResult.task_info[taskId].task_name)\n          }\n        })\n        \n        if (noDataTasks.length > 0) {\n          ElMessage.warning(`以下任务没有足够的性能数据: ${noDataTasks.join(', ')}`)\n        }\n        \n        // 渲染图表\n        this.$nextTick(() => {\n          this.renderCharts()\n        })\n        \n        ElMessage.success('对比分析完成')\n      } catch (error) {\n        ElMessage.error('对比分析失败: ' + (error.message || '未知错误'))\n        console.error('对比分析错误:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    renderCharts() {\n      this.renderTpsChart()\n      this.renderResponseTimeChart()\n      \n      // 添加窗口大小变化时重新渲染图表\n      window.addEventListener('resize', this.handleResize)\n    },\n    \n    // 添加处理窗口大小变化的方法\n    handleResize() {\n      if (this.$refs.tpsChart && this.$refs.responseTimeChart) {\n        echarts.getInstanceByDom(this.$refs.tpsChart)?.resize()\n        echarts.getInstanceByDom(this.$refs.responseTimeChart)?.resize()\n      }\n    },\n    \n    renderTpsChart() {\n      if (!this.$refs.tpsChart) {\n        console.warn('TPS图表DOM元素不存在')\n        return\n      }\n      \n      const chart = echarts.init(this.$refs.tpsChart)\n      const option = {\n        title: { text: 'TPS对比', left: 'center' },\n        tooltip: { trigger: 'axis' },\n        legend: { bottom: 0 },\n        xAxis: { type: 'category', data: [] },\n        yAxis: { type: 'value', name: 'TPS' },\n        series: [],\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '15%',\n          top: '15%',\n          containLabel: true\n        }\n      }\n      \n      let hasData = false\n      let commonTimePoints = []\n      \n      // 确保所有任务都有数据点\n      if (this.comparisonResult?.timeline_data) {\n        console.log('渲染TPS图表，时间线数据:', this.comparisonResult.timeline_data)\n        \n        // 获取所有任务ID，包括可能没有指标数据的任务\n        const allTaskIds = Object.keys(this.comparisonResult.task_info)\n        console.log('所有任务ID:', allTaskIds)\n        \n        // 首先收集所有有效的时间点\n        allTaskIds.forEach(taskId => {\n          if (!this.comparisonResult.timeline_data[taskId]) {\n            console.log(`任务 ${taskId} 没有时间线数据`)\n            return\n          }\n          \n          const timeline = this.comparisonResult.timeline_data[taskId]\n          if (timeline && timeline.length > 0) {\n            console.log(`任务 ${taskId} 有 ${timeline.length} 个时间点`)\n            if (commonTimePoints.length === 0) {\n              commonTimePoints = timeline.map(point => point.time)\n            }\n          }\n        })\n        \n        // 如果有时间点，设置X轴数据\n        if (commonTimePoints.length > 0) {\n          option.xAxis.data = commonTimePoints.map(time => new Date(time).toLocaleTimeString())\n        }\n        \n        // 然后为每个任务添加数据系列\n        allTaskIds.forEach(taskId => {\n          const taskName = this.comparisonResult.task_info[taskId]?.task_name || `任务${taskId}`\n          \n          if (!this.comparisonResult.timeline_data[taskId]) {\n            // 如果没有时间线数据，添加空系列\n            option.series.push({\n              name: taskName,\n              type: 'line',\n              data: [],\n              smooth: true,\n              showSymbol: false,\n              lineStyle: {\n                width: 3\n              }\n            })\n            return\n          }\n          \n          const timeline = this.comparisonResult.timeline_data[taskId]\n          \n          if (timeline && timeline.length > 0) {\n            hasData = true\n            \n            option.series.push({\n              name: taskName,\n              type: 'line',\n              data: timeline.map(point => point.tps),\n              smooth: true,\n              showSymbol: false,\n              lineStyle: {\n                width: 3\n              }\n            })\n          } else {\n            // 即使没有数据，也添加一个空系列，保持图例的一致性\n            option.series.push({\n              name: taskName,\n              type: 'line',\n              data: [],\n              smooth: true,\n              showSymbol: false,\n              lineStyle: {\n                width: 3\n              }\n            })\n          }\n        })\n      }\n      \n      // 如果没有数据，显示无数据提示\n      if (!hasData) {\n        option.title.text = 'TPS对比 (无数据)'\n      }\n      \n      console.log('TPS图表配置:', option)\n      chart.setOption(option)\n    },\n    \n    renderResponseTimeChart() {\n      if (!this.$refs.responseTimeChart) {\n        console.warn('响应时间图表DOM元素不存在')\n        return\n      }\n      \n      const chart = echarts.init(this.$refs.responseTimeChart)\n      const option = {\n        title: { text: '响应时间对比', left: 'center' },\n        tooltip: { trigger: 'axis' },\n        legend: { bottom: 0 },\n        xAxis: { type: 'category', data: [] },\n        yAxis: { type: 'value', name: '响应时间(ms)' },\n        series: [],\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '15%',\n          top: '15%',\n          containLabel: true\n        }\n      }\n      \n      let hasData = false\n      let commonTimePoints = []\n      \n      if (this.comparisonResult?.timeline_data) {\n        console.log('渲染响应时间图表，时间线数据:', this.comparisonResult.timeline_data)\n        \n        // 获取所有任务ID，包括可能没有指标数据的任务\n        const allTaskIds = Object.keys(this.comparisonResult.task_info)\n        \n        // 首先收集所有有效的时间点\n        allTaskIds.forEach(taskId => {\n          if (!this.comparisonResult.timeline_data[taskId]) {\n            console.log(`任务 ${taskId} 没有时间线数据`)\n            return\n          }\n          \n          const timeline = this.comparisonResult.timeline_data[taskId]\n          if (timeline && timeline.length > 0) {\n            console.log(`任务 ${taskId} 有 ${timeline.length} 个时间点`)\n            if (commonTimePoints.length === 0) {\n              commonTimePoints = timeline.map(point => point.time)\n            }\n          }\n        })\n        \n        // 如果有时间点，设置X轴数据\n        if (commonTimePoints.length > 0) {\n          option.xAxis.data = commonTimePoints.map(time => new Date(time).toLocaleTimeString())\n        }\n        \n        // 然后为每个任务添加数据系列\n        allTaskIds.forEach(taskId => {\n          const taskName = this.comparisonResult.task_info[taskId]?.task_name || `任务${taskId}`\n          \n          if (!this.comparisonResult.timeline_data[taskId]) {\n            // 如果没有时间线数据，添加空系列\n            option.series.push({\n              name: taskName,\n              type: 'line',\n              data: [],\n              smooth: true,\n              showSymbol: false,\n              lineStyle: {\n                width: 3\n              }\n            })\n            return\n          }\n          \n          const timeline = this.comparisonResult.timeline_data[taskId]\n          \n          if (timeline && timeline.length > 0) {\n            hasData = true\n            \n            option.series.push({\n              name: taskName,\n              type: 'line',\n              data: timeline.map(point => point.response_time),\n              smooth: true,\n              showSymbol: false,\n              lineStyle: {\n                width: 3\n              }\n            })\n          } else {\n            // 即使没有数据，也添加一个空系列，保持图例的一致性\n            option.series.push({\n              name: taskName,\n              type: 'line',\n              data: [],\n              smooth: true,\n              showSymbol: false,\n              lineStyle: {\n                width: 3\n              }\n            })\n          }\n        })\n      }\n      \n      // 如果没有数据，显示无数据提示\n      if (!hasData) {\n        option.title.text = '响应时间对比 (无数据)'\n      }\n      \n      console.log('响应时间图表配置:', option)\n      chart.setOption(option)\n    },\n    \n    resetComparison() {\n      this.comparisonResult = null\n      this.selectedTasks = []\n    },\n    \n    exportReport() {\n      // 导出功能实现\n      ElMessage.info('导出功能开发中...')\n    },\n    \n    returnToReportList() {\n      window.history.back();\n    },\n    \n    getStatusType(status) {\n      const statusMap = {\n        '0': 'success',\n        '1': 'warning', \n        '99': 'danger'\n      }\n      return statusMap[status] || 'info'\n    },\n    \n    getStatusText(status) {\n      const statusMap = {\n        '0': '已完成',\n        '1': '执行中',\n        '99': '运行失败'\n      }\n      return statusMap[status] || '未知'\n    },\n    \n    getSuccessRate(report) {\n      if (!report.totalRequests || report.totalRequests === 0) return 0\n      return ((report.successRequests || 0) / report.totalRequests * 100).toFixed(1)\n    },\n    \n    formatDate(dateStr) {\n      if (!dateStr) return '-'\n      return new Date(dateStr).toLocaleString()\n    },\n    \n    formatNumber(value, precision = 2) {\n      if (value === null || value === undefined) return '-'\n      return value.toFixed(precision)\n    },\n    \n    getNestedValue(obj, path) {\n      return path.split('.').reduce((current, key) => current?.[key], obj)\n    },\n\n    hasMetricsData(taskId) {\n      return this.comparisonResult?.comparison_metrics?.[taskId]?.averages !== undefined;\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.task-comparison {\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: 100vh;\n  \n  // 确保内容不会超出容器\n  overflow-x: hidden;\n  padding-bottom: 100px; // 增加底部间距到100px\n}\n\n.page-header {\n  margin-bottom: 24px;\n  \n  h1 {\n    margin: 10px 0;\n    color: #303133;\n  }\n  \n  p {\n    color: #909399;\n    margin: 0;\n  }\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10px;\n}\n\n.header-title {\n  flex: 1;\n}\n\n.header-actions {\n  margin-left: 20px;\n}\n\n.selection-card {\n  margin-bottom: 24px;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n}\n\n.filter-section {\n  margin-bottom: 20px;\n}\n\n.task-list {\n  margin-bottom: 20px;\n}\n\n.task-card {\n  border: 2px solid #e4e7ed;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 16px;\n  cursor: pointer;\n  transition: all 0.3s;\n  position: relative;\n  background: white;\n  \n  &:hover {\n    border-color: #409eff;\n    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);\n  }\n  \n  &.selected {\n    border-color: #409eff;\n    background: #f0f9ff;\n  }\n  \n  &.disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n}\n\n.task-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12px;\n  \n  h4 {\n    margin: 0;\n    color: #303133;\n    font-size: 16px;\n  }\n  \n  .task-name {\n    margin: 4px 0 0 0;\n    color: #909399;\n    font-size: 12px;\n  }\n}\n\n.task-metrics {\n  display: flex;\n  gap: 16px;\n  margin-bottom: 12px;\n}\n\n.metric-item {\n  display: flex;\n  flex-direction: column;\n  \n  .metric-label {\n    font-size: 12px;\n    color: #909399;\n  }\n  \n  .metric-value {\n    font-size: 14px;\n    font-weight: 500;\n    color: #303133;\n  }\n}\n\n.task-footer {\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n  color: #909399;\n}\n\n.selection-indicator {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  width: 24px;\n  height: 24px;\n  background: #409eff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n.selection-hint {\n  margin-top: 16px;\n}\n\n.comparison-result {\n  .result-actions {\n    margin-bottom: 20px;\n    display: flex;\n    gap: 12px;\n  }\n   \n  // 添加滚动区域样式\n  max-height: calc(100vh - 200px);\n  overflow-y: auto;\n  padding-right: 10px; // 添加右侧padding，避免滚动条贴边\n  padding-bottom: 40px; // 添加底部padding，确保内容完全显示\n}\n\n.overview-card,\n.ranking-card,\n.metrics-card,\n.charts-card,\n.summary-card {\n  margin-bottom: 24px;\n}\n\n.overview-item {\n  text-align: center;\n  padding: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  background: white;\n  \n  h3 {\n    margin: 0 0 16px 0;\n    color: #303133;\n  }\n  \n  &.no-data {\n    background: #f8f9fa;\n    border: 1px dashed #e4e7ed;\n  }\n}\n\n.overview-stats {\n  display: flex;\n  justify-content: space-around;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  \n  .stat-value {\n    font-size: 20px;\n    font-weight: 600;\n    color: #409eff;\n  }\n  \n  .stat-label {\n    font-size: 12px;\n    color: #909399;\n    margin-top: 4px;\n  }\n}\n\n.ranking-section {\n  h4 {\n    margin: 0 0 12px 0;\n    color: #303133;\n  }\n}\n\n.ranking-list {\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 12px;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 0;\n  border-bottom: 1px solid #e4e7ed;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n  \n  .rank {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #409eff;\n    color: white;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 12px;\n    font-weight: 500;\n  }\n  \n  .name {\n    flex: 1;\n    font-size: 14px;\n  }\n  \n  .value {\n    font-weight: 500;\n    color: #409eff;\n  }\n}\n\n.metric-cell {\n  &.best-value {\n    color: #67c23a;\n    font-weight: 600;\n  }\n}\n\n.chart-container {\n  height: 300px;\n  margin-bottom: 20px; // 添加底部间距\n}\n\n.summary-details {\n  ul {\n    margin: 8px 0;\n    padding-left: 20px;\n  }\n  \n  li {\n    margin: 4px 0;\n  }\n}\n\n.summary-content {\n  max-height: 300px; /* 设置最大高度 */\n  overflow-y: auto; /* 添加滚动条 */\n  padding-right: 5px; /* 添加右侧内边距，避免滚动条贴边 */\n}\n\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.9);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  \n  .el-icon {\n    font-size: 32px;\n    color: #409eff;\n    margin-bottom: 12px;\n  }\n  \n  p {\n    color: #409eff;\n    font-size: 14px;\n  }\n}\n\n.no-data-message {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 10px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.no-ranking-data {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 10px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n// 添加响应式布局样式\n@media screen and (max-width: 768px) {\n  .ranking-card .el-row {\n    display: flex;\n    flex-direction: column;\n    \n    .el-col {\n      width: 100%;\n      margin-bottom: 16px;\n    }\n  }\n  \n  .charts-card .el-row {\n    display: flex;\n    flex-direction: column;\n    \n    .el-col {\n      width: 100%;\n      margin-bottom: 16px;\n    }\n  }\n  \n  .chart-container {\n    height: 250px; // 在小屏幕上减小图表高度\n  }\n}\n</style>", "import { render } from \"./TaskComparison.vue?vue&type=template&id=265f2092&scoped=true\"\nimport script from \"./TaskComparison.vue?vue&type=script&lang=js\"\nexport * from \"./TaskComparison.vue?vue&type=script&lang=js\"\n\nimport \"./TaskComparison.vue?vue&type=style&index=0&id=265f2092&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-265f2092\"]])\n\nexport default __exports__"], "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_breadcrumb", "separator", "_component_el_breadcrumb_item", "to", "path", "_cache", "_hoisted_3", "_hoisted_4", "_component_el_button", "type", "onClick", "$options", "returnToReportList", "plain", "_component_el_icon", "_component_Back", "$data", "comparisonResult", "_createBlock", "_component_el_card", "shadow", "header", "_withCtx", "_hoisted_5", "_component_DataAnalysis", "_hoisted_6", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_input", "searchKeyword", "$event", "placeholder", "clearable", "onInput", "handleSearch", "_component_el_select", "statusFilter", "onChange", "_component_el_option", "label", "value", "_component_el_date_picker", "timeRange", "format", "performComparison", "disabled", "selectedTasks", "length", "_toDisplayString", "_hoisted_7", "_Fragment", "_renderList", "filteredReports", "report", "key", "id", "_normalizeClass", "isSelected", "canSelect", "toggleSelection", "_hoisted_9", "_hoisted_10", "reportName", "_hoisted_11", "task", "taskName", "_hoisted_12", "_component_el_tag", "getStatusType", "reportStatus", "size", "getStatusText", "_hoisted_13", "_hoisted_14", "_hoisted_15", "formatNumber", "avgTps", "_hoisted_16", "_hoisted_17", "avgResponseTime", "_hoisted_18", "_hoisted_19", "getSuccessRate", "_hoisted_20", "_hoisted_21", "executor", "_hoisted_22", "formatDate", "create_time", "_hoisted_23", "_component_Check", "_hoisted_24", "_component_el_alert", "title", "closable", "_hoisted_25", "_hoisted_26", "resetComparison", "icon", "exportReport", "_hoisted_27", "_component_<PERSON><PERSON><PERSON>", "task_info", "taskInfo", "taskId", "xs", "sm", "md", "Math", "min", "Object", "keys", "hasMetricsData", "task_name", "_hoisted_28", "_hoisted_29", "_hoisted_30", "comparison_metrics", "averages", "tps", "_hoisted_31", "_hoisted_32", "response_time", "_hoisted_33", "_hoisted_34", "error_rate", "_hoisted_35", "_component_InfoFilled", "_hoisted_36", "_component_Trend<PERSON><PERSON>s", "style", "_hoisted_37", "_hoisted_38", "rankings", "item", "index", "task_id", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "stability", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_component_el_table", "data", "metricsTableData", "stripe", "height", "border", "_component_el_table_column", "prop", "width", "fixed", "align", "default", "row", "bestTaskId", "values", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "summary", "best_performance_task", "_hoisted_67", "avg_tps", "avg_response_time", "recommendations", "suggestion", "loading", "_hoisted_68", "_component_Loading", "name", "components", "Search", "DataAnalysis", "View", "Download", "Delete", "RefreshRight", "Setting", "Calendar", "Check", "Loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "InfoFilled", "Back", "reportList", "currentPage", "pageSize", "computed", "mapState", "pro", "state", "filtered", "this", "filter", "toLowerCase", "includes", "metrics", "v", "toFixed", "map", "metric", "bestValue", "allTaskIds", "for<PERSON>ach", "getNestedValue", "undefined", "isBetter", "mounted", "loadReports", "beforeUnmount", "window", "removeEventListener", "handleResize", "$refs", "tpsChart", "echarts", "dispose", "responseTimeChart", "methods", "params", "project_id", "page_size", "response", "$api", "getTaskReports", "result", "error", "ElMessage", "message", "some", "getTaskId", "push", "warning", "taskIds", "task_ids", "Set", "time_range_hours", "console", "log", "compareTaskPerformance", "comparison_report", "JSON", "stringify", "timeline_data", "noDataTasks", "join", "$nextTick", "<PERSON><PERSON><PERSON><PERSON>", "success", "renderTpsChart", "renderResponseTimeChart", "addEventListener", "resize", "warn", "chart", "option", "text", "left", "tooltip", "trigger", "legend", "bottom", "xAxis", "yAxis", "series", "grid", "right", "top", "containLabel", "hasData", "commonTimePoints", "timeline", "point", "time", "Date", "toLocaleTimeString", "smooth", "showSymbol", "lineStyle", "setOption", "info", "history", "back", "status", "statusMap", "totalRequests", "successRequests", "dateStr", "toLocaleString", "precision", "obj", "split", "reduce", "current", "__exports__", "render"], "sourceRoot": ""}