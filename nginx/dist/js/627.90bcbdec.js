"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[627],{4627:function(t,e,a){a.r(e),a.d(e,{default:function(){return v}});var o=a(56768),r=a(68264),d=a.n(r),l=a(90144);const n={class:"ve_404"},c={class:"error"},s={class:"astronaut"};var i={__name:"404",setup(t){const e=(0,l.KR)(null);let a=null;(0,o.sV)(()=>{r()});const r=()=>{e.value.width=e.value.parentNode.clientWidth,e.value.height=e.value.parentNode.clientHeight;let t="#131e38",o="#fe9642",r="#FFF8E7",l="#7f3f98",n="#563795",c="#fbc715",s=new(d().Illustration)({element:e.value,dragRotate:!0,zoom:.65}),i=new(d().RoundedRect)({addTo:s,width:200,height:220,color:"white",fill:!0,cornerRadius:16,stroke:60});new(d().RoundedRect)({addTo:i,width:180,height:310,color:o,fill:!0,cornerRadius:24,stroke:120,translate:{z:-85,y:-60}});let h=new(d().RoundedRect)({addTo:i,height:30,width:28,stroke:60,fill:!0,color:n,translate:{x:-140,y:-64},cornerRadius:.05});new(d().RoundedRect)({addTo:h,height:120,width:12,stroke:60,fill:!0,color:r,translate:{y:120},cornerRadius:.05});let u=new(d().Shape)({addTo:h,path:[{x:-20},{x:20}],stroke:32,color:l,translate:{y:210}});u.copy({color:n,translate:{y:230}}),new(d().RoundedRect)({addTo:u,height:32,width:22,translate:{x:-8,y:60},fill:!0,color:c,stroke:30}),new(d().RoundedRect)({addTo:u,height:24,width:0,translate:{x:24,y:50},fill:!0,color:o,stroke:20}),h.copyGraph({translate:{x:140,y:-64},rotate:{y:d().TAU/2}});let v=new(d().RoundedRect)({addTo:i,height:160,width:28,stroke:60,fill:!0,color:r,translate:{x:-56,y:230},cornerRadius:.05}),y=new(d().Shape)({addTo:v,path:[{x:-28},{x:28}],stroke:32,color:l,translate:{y:100}});y.copy({color:n,translate:{y:124}}),new(d().RoundedRect)({addTo:v,width:96,height:24,stroke:40,fill:!0,color:c,translate:{x:-24,y:170},cornerRadius:24}),v.copyGraph({translate:{x:56,y:230},rotate:{y:d().TAU/2}});let f=new(d().RoundedRect)({addTo:i,width:216,height:180,depth:40,cornerRadius:80,stroke:60,color:r,fill:!0,translate:{y:-300}}),w=new(d().RoundedRect)({addTo:f,width:210,height:165,cornerRadius:64,color:t,fill:!0,backface:!1,translate:{z:20}});new(d().Rect)({addTo:w,width:48,height:2,stroke:10,translate:{x:24,y:-24,z:10},color:"white",backface:!1});let R=new(d().RoundedRect)({addTo:f,width:36,height:72,cornerRadius:80,stroke:20,color:o,fill:!0,translate:{x:-140}});R.copy({translate:{x:140}});let p=new(d().Shape)({addTo:f,path:[{x:-110},{x:110}],translate:{y:120},stroke:40,color:l});p.copy({translate:{y:160},color:n});let _=new(d().Shape)({addTo:i,path:[{x:-20},{x:20}],stroke:10,translate:{x:200,z:200},color:c});function k(){s.rotate.y+=.005,s.rotate.x+=.005,s.rotate.z+=.005,s.updateRenderGraph(),a=requestAnimationFrame(k)}_.copy({translate:{x:320,y:200,z:-400},color:c}),_.copy({translate:{x:-220,y:300,z:-400},color:"white"}),_.copy({translate:{x:-100,y:400,z:-280},color:l}),_.copy({translate:{x:50,y:-60,z:150},color:o}),_.copy({translate:{x:-250,y:80,z:300},color:l}),_.copy({translate:{x:-350,y:-280,z:175},color:n}),_.copy({translate:{x:250,y:-380,z:-175},color:"white"}),s.updateRenderGraph(),k()};return(0,o.hi)(()=>{cancelAnimationFrame(a),a=null}),(t,a)=>{const d=(0,o.g2)("router-link"),l=(0,o.gN)("resize");return(0,o.uX)(),(0,o.CE)("div",n,[a[4]||(a[4]=(0,o.Fv)('<div class="moon" data-v-7c1fa82d></div><div class="moon__crater moon__crater1" data-v-7c1fa82d></div><div class="moon__crater moon__crater2" data-v-7c1fa82d></div><div class="moon__crater moon__crater3" data-v-7c1fa82d></div><div class="star star1" data-v-7c1fa82d>⭐</div><div class="star star2" data-v-7c1fa82d>⭐</div><div class="star star3" data-v-7c1fa82d>⭐</div><div class="star star4" data-v-7c1fa82d>⭐</div><div class="star star5" data-v-7c1fa82d>⭐</div>',9)),(0,o.RG)(t.$slots,"default",{},()=>[(0,o.Lk)("div",c,[a[1]||(a[1]=(0,o.Lk)("div",{class:"error__title"},"404",-1)),a[2]||(a[2]=(0,o.Lk)("div",{class:"error__subtitle"},"🐱🐱🐱(⓿_⓿)🐱🐱🐱",-1)),a[3]||(a[3]=(0,o.Lk)("div",{class:"error__description"},"看来你是迷路了......",-1)),(0,o.bF)(d,{to:"/"},{default:(0,o.k6)(()=>a[0]||(a[0]=[(0,o.Lk)("button",{class:"error__button error__button--active"}," 回到首页 ",-1)])),_:1,__:[0]})])]),(0,o.bo)(((0,o.uX)(),(0,o.CE)("div",s,[(0,o.Lk)("canvas",{ref_key:"cav",ref:e},null,512)])),[[l,{resize:r}]])])}}},h=a(71241);const u=(0,h.A)(i,[["__scopeId","data-v-7c1fa82d"]]);var v=u}}]);
//# sourceMappingURL=627.90bcbdec.js.map