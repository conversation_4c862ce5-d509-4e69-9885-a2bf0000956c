"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[315],{34315:function(e,t,a){a.r(t),a.d(t,{default:function(){return Le}});var s=a(56768),r=a(24232);const l={class:"performance-container"},i={class:"dashboard-header"},o={class:"header-title"},d={class:"header-actions"},n={class:"search-wrapper"},c={class:"task-stats"},u={class:"stat-card"},k={class:"stat-value"},m={class:"stat-icon running-icon1"},p={class:"stat-card"},f={class:"stat-value"},h={class:"stat-icon running-icon"},g={class:"stat-card"},b={class:"stat-value"},_={class:"stat-icon completed-icon"},v={class:"stat-card"},y={class:"stat-value"},F={class:"stat-icon error-icon"},w={class:"task-table-card"},L={class:"task-info"},T={class:"task-avatar-container"},C={class:"task-detail"},V={class:"task-name"},x={class:"task-meta"},W={class:"meta-info"},S={class:"status-cell"},M={class:"status-text"},E={class:"user-info"},P={class:"user-avatar"},$={class:"user-name"},z={key:0,class:"update-info"},U={class:"update-by"},I={class:"update-time"},D={key:1,class:"update-info"},N={class:"update-by"},X={class:"update-time"},H={class:"desc-text"},R={key:1,class:"no-data"},A={class:"action-buttons"},B={class:"empty-data"},O={class:"pagination-container"},j={class:"dialog-content"},q={class:"form-row"},K={class:"form-row"},G={class:"radio-content"},Q={class:"radio-content"},Y={key:0,class:"tip-box warning"},J={class:"radio-content"},Z={class:"radio-content"},ee={key:0,class:"tip-box info"},te={key:0,class:"distributed-config"},ae={class:"form-row"},se={class:"server-option"},re={class:"server-name"},le={class:"server-address"},ie={class:"port-option"},oe={class:"port-number"},de={class:"form-row"},ne={class:"server-option"},ce={class:"server-name"},ue={class:"server-address"},ke={class:"form-row"},me={class:"worker-info"},pe={class:"dialog-footer"};function fe(e,t,a,fe,he,ge){const be=(0,s.g2)("el-tag"),_e=(0,s.g2)("Search"),ve=(0,s.g2)("el-icon"),ye=(0,s.g2)("el-input"),Fe=(0,s.g2)("el-button"),we=(0,s.g2)("Plus"),Le=(0,s.g2)("List"),Te=(0,s.g2)("Loading"),Ce=(0,s.g2)("SuccessFilled"),Ve=(0,s.g2)("WarningFilled"),xe=(0,s.g2)("el-table-column"),We=(0,s.g2)("el-avatar"),Se=(0,s.g2)("router-link"),Me=(0,s.g2)("el-tooltip"),Ee=(0,s.g2)("Promotion"),Pe=(0,s.g2)("Menu"),$e=(0,s.g2)("More"),ze=(0,s.g2)("CopyDocument"),Ue=(0,s.g2)("el-dropdown-item"),Ie=(0,s.g2)("Delete"),De=(0,s.g2)("el-dropdown-menu"),Ne=(0,s.g2)("el-dropdown"),Xe=(0,s.g2)("el-empty"),He=(0,s.g2)("el-table"),Re=(0,s.g2)("el-pagination"),Ae=(0,s.g2)("el-scrollbar"),Be=(0,s.g2)("el-form-item"),Oe=(0,s.g2)("Tickets"),je=(0,s.g2)("el-radio"),qe=(0,s.g2)("Timer"),Ke=(0,s.g2)("el-radio-group"),Ge=(0,s.g2)("InfoFilled"),Qe=(0,s.g2)("Monitor"),Ye=(0,s.g2)("Connection"),Je=(0,s.g2)("el-option"),Ze=(0,s.g2)("el-select"),et=(0,s.g2)("el-input-number"),tt=(0,s.g2)("el-form"),at=(0,s.g2)("el-dialog"),st=(0,s.gN)("loading");return(0,s.uX)(),(0,s.CE)(s.FK,null,[(0,s.Lk)("div",l,[(0,s.Lk)("div",i,[(0,s.Lk)("div",o,[t[15]||(t[15]=(0,s.Lk)("h2",null,"性能测试任务",-1)),(0,s.bF)(be,{type:"info",effect:"plain",class:"header-tag"},{default:(0,s.k6)(()=>t[14]||(t[14]=[(0,s.eW)("任务列表")])),_:1,__:[14]})]),(0,s.Lk)("div",d,[(0,s.Lk)("div",n,[(0,s.bF)(ye,{modelValue:he.filterText,"onUpdate:modelValue":t[0]||(t[0]=e=>he.filterText=e),placeholder:"搜索任务名称",class:"search-input",clearable:""},{prefix:(0,s.k6)(()=>[(0,s.bF)(ve,{class:"search-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(_e)]),_:1})]),_:1},8,["modelValue"]),(0,s.bF)(Fe,{type:"primary",onClick:ge.searchClick,class:"search-btn"},{default:(0,s.k6)(()=>t[16]||(t[16]=[(0,s.eW)("搜索")])),_:1,__:[16]},8,["onClick"])]),(0,s.bF)(Fe,{type:"primary",onClick:t[1]||(t[1]=e=>ge.popup("add")),class:"create-btn"},{default:(0,s.k6)(()=>[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(we)]),_:1}),t[17]||(t[17]=(0,s.eW)("新建任务 "))]),_:1,__:[17]})])]),(0,s.Lk)("div",c,[(0,s.Lk)("div",u,[(0,s.Lk)("div",k,(0,r.v_)(he.taskList.length),1),t[18]||(t[18]=(0,s.Lk)("div",{class:"stat-label"},"当前任务",-1)),(0,s.Lk)("div",m,[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(Le)]),_:1})])]),(0,s.Lk)("div",p,[(0,s.Lk)("div",f,(0,r.v_)(ge.getRunningCount),1),t[19]||(t[19]=(0,s.Lk)("div",{class:"stat-label"},"运行中",-1)),(0,s.Lk)("div",h,[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(Te)]),_:1})])]),(0,s.Lk)("div",g,[(0,s.Lk)("div",b,(0,r.v_)(ge.getCompletedCount),1),t[20]||(t[20]=(0,s.Lk)("div",{class:"stat-label"},"已完成",-1)),(0,s.Lk)("div",_,[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ce)]),_:1})])]),(0,s.Lk)("div",v,[(0,s.Lk)("div",y,(0,r.v_)(ge.getErrorCount),1),t[21]||(t[21]=(0,s.Lk)("div",{class:"stat-label"},"异常",-1)),(0,s.Lk)("div",F,[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ve)]),_:1})])])]),(0,s.bF)(Ae,{class:"table-scrollbar",height:"calc(100vh - 280px)"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",w,[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(He,{data:he.taskList,style:{width:"100%"},"row-key":"id",border:!1,stripe:"",height:he.tableHeight,"highlight-current-row":"","header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold"}},{empty:(0,s.k6)(()=>[(0,s.Lk)("div",B,[(0,s.bF)(Xe,{"image-size":120,description:"暂无任务数据"},{description:(0,s.k6)(()=>t[27]||(t[27]=[(0,s.Lk)("p",null,"暂无任务数据",-1)])),default:(0,s.k6)(()=>[(0,s.bF)(Fe,{type:"primary",onClick:t[2]||(t[2]=e=>ge.popup("add"))},{default:(0,s.k6)(()=>t[28]||(t[28]=[(0,s.eW)("新建任务")])),_:1,__:[28]})]),_:1})])]),default:(0,s.k6)(()=>[(0,s.bF)(xe,{type:"selection",width:"55",align:"center"}),(0,s.bF)(xe,{label:"任务信息","min-width":"300"},{default:(0,s.k6)(({row:e})=>[(0,s.Lk)("div",L,[(0,s.Lk)("div",T,[(0,s.bF)(We,{size:40,class:"task-avatar",style:(0,r.Tr)({backgroundColor:ge.getRandomColor(e.taskName)})},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(e.taskName.substr(0,1).toUpperCase()),1)]),_:2},1032,["style"])]),(0,s.Lk)("div",C,[(0,s.Lk)("div",V,[(0,s.bF)(Se,{to:"/maskMgrDetail/",onClick:t=>ge.clickTaskManage(e),class:"task-name-link"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(e.taskName),1)]),_:2},1032,["onClick"])]),(0,s.Lk)("div",x,[(0,s.bF)(be,{type:"10"===e.taskType?"primary":"success",size:"small",effect:"plain",class:"meta-tag"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(he.taskTypeMap[e.taskType.toString()]||e.taskType),1)]),_:2},1032,["type"]),(0,s.Lk)("div",W,"创建于 "+(0,r.v_)(ge.formatTimeAgo(e.create_time)),1)])])])]),_:1}),(0,s.bF)(xe,{label:"运行状态",align:"center",width:"120"},{default:(0,s.k6)(({row:e})=>[(0,s.Lk)("div",S,[(0,s.Lk)("div",{class:(0,r.C4)(["status-indicator",ge.getStatusClass(e.status)])},null,2),(0,s.Lk)("span",M,(0,r.v_)(e.status_display||ge.getStatusText(e.status)||"未设置"),1)])]),_:1}),(0,s.bF)(xe,{label:"运行模式",align:"center",width:"120"},{default:(0,s.k6)(({row:e})=>[(0,s.bF)(be,{effect:"plain",size:"small",class:"mode-tag",type:ge.getRunPatternType(e.runPattern)},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(e.taskType_display),1)]),_:2},1032,["type"])]),_:1}),(0,s.bF)(xe,{label:"分布式模式",align:"center",width:"120"},{default:(0,s.k6)(({row:e})=>[(0,s.bF)(be,{effect:"plain",size:"small",type:ge.getDistributedModeType(e.distributed_mode)},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(ge.getDistributedModeText(e.distributed_mode)),1)]),_:2},1032,["type"])]),_:1}),(0,s.bF)(xe,{label:"任务创建者",align:"center",width:"160"},{default:(0,s.k6)(({row:e})=>[(0,s.Lk)("div",E,[(0,s.Lk)("div",P,[(0,s.bF)(We,{size:28,style:(0,r.Tr)({backgroundColor:ge.getRandomColor(e.creator)})},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(e.creator?e.creator.substr(0,1).toUpperCase():"U"),1)]),_:2},1032,["style"])]),(0,s.Lk)("span",$,(0,r.v_)(e.creator),1)])]),_:1}),(0,s.bF)(xe,{label:"最后更新",align:"center",width:"160"},{default:(0,s.k6)(({row:e})=>[e.modifier&&e.update_time?((0,s.uX)(),(0,s.CE)("div",z,[(0,s.Lk)("div",U,(0,r.v_)(e.modifier),1),(0,s.Lk)("div",I,(0,r.v_)(ge.formatTimeAgo(e.update_time)),1)])):e.create_time?((0,s.uX)(),(0,s.CE)("div",D,[(0,s.Lk)("div",N,(0,r.v_)(e.creator||"系统"),1),(0,s.Lk)("div",X,(0,r.v_)(ge.formatTimeAgo(e.create_time)),1)])):((0,s.uX)(),(0,s.Wv)(be,{key:2,size:"small",effect:"plain",type:"info"},{default:(0,s.k6)(()=>t[22]||(t[22]=[(0,s.eW)("未更新")])),_:1,__:[22]}))]),_:1}),(0,s.bF)(xe,{label:"备注",align:"center","min-width":"200"},{default:(0,s.k6)(({row:e})=>[e.desc?((0,s.uX)(),(0,s.Wv)(Me,{key:0,class:"box-item",effect:"dark",content:e.desc,placement:"top-start"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",H,(0,r.v_)(e.desc),1)]),_:2},1032,["content"])):((0,s.uX)(),(0,s.CE)("span",R,"暂无备注"))]),_:1}),(0,s.bF)(xe,{label:"操作",fixed:"right",width:"280",align:"center"},{default:(0,s.k6)(({row:e})=>[(0,s.Lk)("div",A,[(0,s.bF)(Fe,{onClick:t=>ge.runTask(e.id),type:"primary",plain:"",size:"small",class:"action-btn run-btn"},{default:(0,s.k6)(()=>[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ee)]),_:1}),t[23]||(t[23]=(0,s.eW)(" 运行 "))]),_:2,__:[23]},1032,["onClick"]),(0,s.bF)(Fe,{onClick:t=>ge.clickTaskManage(e),type:"success",plain:"",size:"small",class:"action-btn manage-btn"},{default:(0,s.k6)(()=>[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(Pe)]),_:1}),t[24]||(t[24]=(0,s.eW)(" 管理 "))]),_:2,__:[24]},1032,["onClick"]),(0,s.bF)(Ne,{trigger:"click",onCommand:t=>ge.handleCommand(t,e),class:"action-dropdown"},{dropdown:(0,s.k6)(()=>[(0,s.bF)(De,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ue,{command:"copy"},{default:(0,s.k6)(()=>[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(ze)]),_:1}),t[25]||(t[25]=(0,s.eW)(" 复制任务 "))]),_:1,__:[25]}),(0,s.bF)(Ue,{divided:"",command:"delete",class:"danger-item"},{default:(0,s.k6)(()=>[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ie)]),_:1}),t[26]||(t[26]=(0,s.eW)(" 删除任务 "))]),_:1,__:[26]})]),_:1})]),default:(0,s.k6)(()=>[(0,s.bF)(Fe,{size:"small",class:"more-btn"},{default:(0,s.k6)(()=>[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)($e)]),_:1})]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data","height"])),[[st,he.tableLoading]]),(0,s.Lk)("div",O,[(0,s.bF)(Re,{"current-page":he.pages.current,"onUpdate:currentPage":t[3]||(t[3]=e=>he.pages.current=e),background:"",layout:"total, prev, pager, next, jumper","page-size":100,total:he.pages.count,onCurrentChange:ge.currentPages},null,8,["current-page","total","onCurrentChange"])])])]),_:1})]),(0,s.bF)(at,{modelValue:he.dialogVisible,"onUpdate:modelValue":t[13]||(t[13]=e=>he.dialogVisible=e),title:he.dialogTitle,width:"800px","destroy-on-close":"","before-close":ge.clearValidation,class:"task-dialog"},{footer:(0,s.k6)(()=>[(0,s.Lk)("div",pe,[(0,s.bF)(Fe,{onClick:ge.clearValidation,plain:""},{default:(0,s.k6)(()=>t[35]||(t[35]=[(0,s.eW)("取消")])),_:1,__:[35]},8,["onClick"]),(0,s.bF)(Fe,{type:"primary",onClick:ge.addTask},{default:(0,s.k6)(()=>t[36]||(t[36]=[(0,s.eW)("确定")])),_:1,__:[36]},8,["onClick"])])]),default:(0,s.k6)(()=>[(0,s.Lk)("div",j,[(0,s.bF)(tt,{model:he.form,rules:he.rulesPerf,ref:"perfRef","label-position":"top"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",q,[(0,s.bF)(Be,{label:"任务名称",prop:"taskName",class:"form-item-half"},{default:(0,s.k6)(()=>[(0,s.bF)(ye,{modelValue:he.form.taskName,"onUpdate:modelValue":t[4]||(t[4]=e=>he.form.taskName=e),maxlength:"50",placeholder:"请输入任务名称"},null,8,["modelValue"])]),_:1}),(0,s.bF)(Be,{label:"所属项目",prop:"project",class:"form-item-half"},{default:(0,s.k6)(()=>[(0,s.bF)(ye,{modelValue:he.form.proName,"onUpdate:modelValue":t[5]||(t[5]=e=>he.form.proName=e),disabled:""},null,8,["modelValue"])]),_:1})]),(0,s.Lk)("div",K,[(0,s.bF)(Be,{label:"任务类型",prop:"taskType",class:"form-item-half"},{default:(0,s.k6)(()=>[(0,s.bF)(Ke,{modelValue:ge.selectTaskType,"onUpdate:modelValue":t[6]||(t[6]=e=>ge.selectTaskType=e),class:"task-type-radio"},{default:(0,s.k6)(()=>[(0,s.bF)(je,{label:"10"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",G,[(0,s.bF)(ve,{class:"radio-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(Oe)]),_:1}),t[29]||(t[29]=(0,s.Lk)("span",null,"普通任务",-1))])]),_:1}),(0,s.bF)(je,{label:"20"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Q,[(0,s.bF)(ve,{class:"radio-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(qe)]),_:1}),t[30]||(t[30]=(0,s.Lk)("span",null,"定时任务",-1))])]),_:1})]),_:1},8,["modelValue"]),"20"===he.form.taskType?((0,s.uX)(),(0,s.CE)("div",Y,[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ge)]),_:1}),t[31]||(t[31]=(0,s.Lk)("span",null,"定时任务下只允许创建一个场景！",-1))])):(0,s.Q3)("",!0)]),_:1}),(0,s.bF)(Be,{label:"运行模式",prop:"distributed_mode",class:"form-item-half"},{default:(0,s.k6)(()=>[(0,s.bF)(Ke,{modelValue:he.form.distributed_mode,"onUpdate:modelValue":t[7]||(t[7]=e=>he.form.distributed_mode=e),class:"run-mode-radio"},{default:(0,s.k6)(()=>[(0,s.bF)(je,{label:"single"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",J,[(0,s.bF)(ve,{class:"radio-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(Qe)]),_:1}),t[32]||(t[32]=(0,s.Lk)("span",null,"单机模式",-1))])]),_:1}),(0,s.bF)(je,{label:"distributed"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Z,[(0,s.bF)(ve,{class:"radio-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(Ye)]),_:1}),t[33]||(t[33]=(0,s.Lk)("span",null,"分布式模式",-1))])]),_:1})]),_:1},8,["modelValue"]),"distributed"===he.form.distributed_mode?((0,s.uX)(),(0,s.CE)("div",ee,[(0,s.bF)(ve,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ge)]),_:1}),t[34]||(t[34]=(0,s.Lk)("span",null,"分布式模式需要配置主服务器和工作服务器",-1))])):(0,s.Q3)("",!0)]),_:1})]),"distributed"===he.form.distributed_mode?((0,s.uX)(),(0,s.CE)("div",te,[(0,s.Lk)("div",ae,[(0,s.bF)(Be,{label:"主服务器",prop:"master_server",class:"form-item-half"},{default:(0,s.k6)(()=>[(0,s.bF)(Ze,{modelValue:he.form.master_server,"onUpdate:modelValue":t[8]||(t[8]=e=>he.form.master_server=e),placeholder:"请选择主服务器",style:{width:"100%"},onChange:ge.handleMasterServerChange},{default:(0,s.k6)(()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(he.availableServers,e=>((0,s.uX)(),(0,s.Wv)(Je,{key:e.id,label:`${e.name} (${e.host_ip}:${e.host_port})`,value:e.id},{default:(0,s.k6)(()=>[(0,s.Lk)("div",se,[(0,s.Lk)("span",re,(0,r.v_)(e.name),1),(0,s.bF)(be,{type:"active"===e.status?"success":"danger",size:"small"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)("active"===e.status?"可用":"不可用"),1)]),_:2},1032,["type"])]),(0,s.Lk)("div",le,(0,r.v_)(e.host_ip)+":"+(0,r.v_)(e.host_port),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),(0,s.bF)(Be,{label:"GUI端口",prop:"gui_port",class:"form-item-half"},{default:(0,s.k6)(()=>[(0,s.bF)(Ze,{modelValue:he.form.gui_port,"onUpdate:modelValue":t[9]||(t[9]=e=>he.form.gui_port=e),placeholder:"请选择GUI端口",style:{width:"100%"}},{default:(0,s.k6)(()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(he.guiPorts,e=>((0,s.uX)(),(0,s.Wv)(Je,{key:e.port,label:`端口 ${e.port}`,value:e.port,disabled:e.occupied},{default:(0,s.k6)(()=>[(0,s.Lk)("div",ie,[(0,s.Lk)("span",oe,(0,r.v_)(e.port),1),(0,s.bF)(be,{type:e.occupied?"danger":"success",size:"small"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(e.occupied?"已占用":"可用"),1)]),_:2},1032,["type"])])]),_:2},1032,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),(0,s.Lk)("div",de,[(0,s.bF)(Be,{label:"工作服务器",prop:"worker_servers",class:"form-item-full"},{default:(0,s.k6)(()=>[(0,s.bF)(Ze,{modelValue:he.form.worker_servers,"onUpdate:modelValue":t[10]||(t[10]=e=>he.form.worker_servers=e),multiple:"",placeholder:"请选择工作服务器",style:{width:"100%"}},{default:(0,s.k6)(()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ge.availableWorkerServers,e=>((0,s.uX)(),(0,s.Wv)(Je,{key:e.id,label:`${e.name} (${e.host_ip}:${e.host_port})`,value:e.id},{default:(0,s.k6)(()=>[(0,s.Lk)("div",ne,[(0,s.Lk)("span",ce,(0,r.v_)(e.name),1),(0,s.bF)(be,{type:"active"===e.status?"success":"danger",size:"small"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)("active"===e.status?"可用":"不可用"),1)]),_:2},1032,["type"])]),(0,s.Lk)("div",ue,(0,r.v_)(e.host_ip)+":"+(0,r.v_)(e.host_port),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),(0,s.Lk)("div",ke,[(0,s.bF)(Be,{label:"工作进程数",prop:"total_workers",class:"form-item-half"},{default:(0,s.k6)(()=>[(0,s.bF)(et,{modelValue:he.form.total_workers,"onUpdate:modelValue":t[11]||(t[11]=e=>he.form.total_workers=e),min:1,max:ge.maxWorkers,placeholder:"总工作进程数",style:{width:"100%"}},null,8,["modelValue","max"]),(0,s.Lk)("div",me,[(0,s.Lk)("span",null,"建议进程数: "+(0,r.v_)(ge.recommendedWorkers),1),(0,s.Lk)("span",null,"最大可用: "+(0,r.v_)(ge.maxWorkers),1)])]),_:1})])])):(0,s.Q3)("",!0),(0,s.bF)(Be,{label:"任务描述",prop:"desc"},{default:(0,s.k6)(()=>[(0,s.bF)(ye,{type:"textarea",modelValue:he.form.desc,"onUpdate:modelValue":t[12]||(t[12]=e=>he.form.desc=e),placeholder:"请输入任务描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue","title","before-close"])],64)}a(44114),a(18111),a(22489),a(7588),a(18237);var he=a(60782),ge=a(51219),be=a(12933),_e=a(93851),ve=a(57477),ye={components:{Plus:ve.Plus,Promotion:ve.Promotion,CopyDocument:ve.CopyDocument,Menu:ve.Menu,Delete:ve.Delete,InfoFilled:ve.InfoFilled,Search:ve.Search,List:ve.List,Loading:ve.Loading,SuccessFilled:ve.SuccessFilled,WarningFilled:ve.WarningFilled,More:ve.More,User:ve.User,Edit:ve.Edit,Tickets:ve.Tickets,Timer:ve.Timer,Monitor:ve.Monitor,Connection:ve.Connection},data(){return{taskTypeMap:{10:"普通任务",20:"定时任务"},filterText:"",taskList:[],pages:{count:0,current:1},addDlg:!1,form:{taskName:"",desc:"",project:"",taskType:"10",creator:"",modifier:"",distributed_mode:"single",master_server:null,worker_servers:[],total_workers:1,gui_port:8089},dialogVisible:!1,dialogTitle:"",tableLoading:!1,rulesPerf:{taskName:[{required:!0,message:"请输入任务名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 个字符",trigger:"blur"}],distributed_mode:[{required:!0,message:"请选择运行模式",trigger:"change"}],master_server:[{validator:(e,t,a)=>{"distributed"!==this.form.distributed_mode||t?a():a(new Error("分布式模式下必须选择主服务器"))},trigger:"change"}],worker_servers:[{validator:(e,t,a)=>{"distributed"!==this.form.distributed_mode||t&&0!==t.length?a():a(new Error("分布式模式下必须选择至少一个工作服务器"))},trigger:"change"}],gui_port:[{validator:(e,t,a)=>{"distributed"!==this.form.distributed_mode||t?a():a(new Error("分布式模式下必须选择GUI端口"))},trigger:"change"}],total_workers:[{validator:(e,t,a)=>{if("distributed"===this.form.distributed_mode){const e=this.maxWorkers;!t||t<1?a(new Error("工作进程数不能小于1")):t>e?a(new Error(`工作进程数不能超过${e}`)):a()}else a()},trigger:"blur"}]},statusMap:{0:{text:"执行完成",type:"success",class:"status-completed"},1:{text:"执行中",type:"warning",class:"status-running"},99:{text:"执行失败",type:"danger",class:"status-error"}},colorCache:{},tableHeight:"calc(100vh - 330px)",resizeObserver:null,availableServers:[],guiPorts:[{port:8089,occupied:!1},{port:8090,occupied:!1},{port:8091,occupied:!1},{port:8092,occupied:!1},{port:8093,occupied:!1},{port:8094,occupied:!1},{port:8095,occupied:!1},{port:8096,occupied:!1}],portCheckTimer:null}},mounted(){this.calculateTableHeight(),this.loadServers(),this.startPortCheck(),this.resizeObserver=new ResizeObserver(()=>{this.calculateTableHeight()});const e=document.querySelector(".table-scrollbar");e&&this.resizeObserver.observe(e),window.addEventListener("resize",this.calculateTableHeight)},beforeUnmount(){window.removeEventListener("resize",this.calculateTableHeight),this.resizeObserver&&this.resizeObserver.disconnect(),this.portCheckTimer&&clearInterval(this.portCheckTimer)},computed:{...(0,he.aH)({pro:e=>e.pro,envId:e=>e.envId}),username(){return window.sessionStorage.getItem("username")},selectTaskType:{get(){return this.form.taskType.toString()},set(e){this.form.taskType=e}},getRunningCount(){return this.taskList.filter(e=>"1"===e.status).length},getCompletedCount(){return this.taskList.filter(e=>"0"===e.status).length},getErrorCount(){return this.taskList.filter(e=>"99"===e.status).length},minTableHeight(){return this.taskList.length>0?void 0:"400px"},availableWorkerServers(){return this.availableServers.filter(e=>e.id!==this.form.master_server)},maxWorkers(){if(!this.form.worker_servers||0===this.form.worker_servers.length)return 1;const e=this.availableServers.filter(e=>this.form.worker_servers.includes(e.id));return e.reduce((e,t)=>e+(t.max_workers||4),0)},recommendedWorkers(){return Math.ceil(.7*this.maxWorkers)}},methods:{...(0,he.PY)(["checkedTask"]),calculateTableHeight(){this.$nextTick(()=>{const e=document.querySelector(".dashboard-header")?.offsetHeight||0,t=document.querySelector(".task-stats")?.offsetHeight||0,a=document.querySelector(".pagination-container")?.offsetHeight||0,s=window.innerHeight-e-t-a-120;this.tableHeight=Math.max(s,300)+"px"})},async allTask(e,t){this.tableLoading=!0;try{const a=await this.$api.getPerformanceTask(this.pro.id,e,t);200===a.status&&(this.taskList=a.data.result,this.pages.count=a.data.count,this.pages.current=a.data.current,this.$nextTick(()=>{this.calculateTableHeight()}))}catch(a){(0,ge.nk)({type:"error",message:"获取任务列表失败",duration:3e3})}finally{this.tableLoading=!1}},currentPages(e){this.allTask(e,this.filterText)},searchClick(){this.allTask(1,this.filterText)},clickTaskManage(e){this.$router.push({name:"maskMgrDetail"}),this.checkedTask(e)},popup(e){switch(this.dialogVisible=!0,e){case"add":this.dialogTitle="创建新任务",this.form.creator=this.username,this.form.project=this.pro.id,this.form.proName=this.pro.name,this.loadServers(),delete this.form.modifier;break;default:this.dialogTitle="";break}},async loadServers(){try{const e=await this.$api.getServers(this.pro.id,1);200===e.status&&(this.availableServers=e.data.result||[])}catch(e){console.error("加载服务器列表失败:",e),this.$message.error("加载服务器列表失败")}},async checkPortStatus(){if(this.form.master_server)try{const e=await this.$api.checkServerPorts(this.form.master_server);if(200===e.status){const t=e.data.occupied_ports||[];this.guiPorts.forEach(e=>{e.occupied=t.includes(e.port)})}}catch(e){console.error("检查端口状态失败:",e)}},startPortCheck(){this.portCheckTimer=setInterval(()=>{"distributed"===this.form.distributed_mode&&this.form.master_server&&this.checkPortStatus()},3e4)},handleMasterServerChange(){this.form.gui_port=8089,this.checkPortStatus(),this.form.worker_servers.includes(this.form.master_server)&&(this.form.worker_servers=this.form.worker_servers.filter(e=>e!==this.form.master_server))},clearValidation(){this.dialogVisible=!1,this.$refs.perfRef&&this.$refs.perfRef.clearValidate(),this.form={taskName:"",desc:"",project:"",taskType:"10",creator:"",modifier:"",distributed_mode:"single",master_server:null,worker_servers:[],total_workers:1,gui_port:8089}},async addTask(){this.$refs.perfRef.validate(async e=>{if(!e)return;const t=this.form;try{const e=await this.$api.createPerformanceTask(t);201===e.status&&(this.form={taskName:"",desc:"",project:"",taskType:"10",creator:"",modifier:"",distributed_mode:"single",master_server:null,worker_servers:[],total_workers:1,gui_port:8089},this.dialogVisible=!1,(0,ge.nk)({type:"success",message:"任务创建成功",duration:1e3}),this.allTask(1))}catch(a){(0,ge.nk)({type:"error",message:"添加失败: "+(a.message||"未知错误"),duration:3e3})}})},delTask(e){be.s.confirm("此操作将永久删除该任务, 是否继续?","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",closeOnClickModal:!1}).then(async()=>{try{const t=await this.$api.delPerformanceTask(e);204===t.status&&((0,ge.nk)({type:"success",message:"删除成功!"}),this.allTask(this.pages.current))}catch(t){(0,ge.nk)({type:"error",message:"删除失败: "+(t.message||"未知错误"),duration:3e3})}}).catch(()=>{(0,ge.nk)({type:"info",message:"已取消删除"})})},async clickCopy(e){const t={...e};t.taskName=t.taskName+"_副本";try{const e=await this.$api.createPerformanceTask(t);201===e.status&&((0,ge.nk)({type:"success",message:"复制成功",duration:1e3}),this.allTask(this.pages.current))}catch(a){(0,ge.nk)({type:"error",message:"复制失败: "+(a.message||"未知错误"),duration:3e3})}},async runTask(e){if(!this.envId)return void this.$message({type:"warning",message:"当前未选中执行环境!",duration:1e3});const t={taskId:e,env:this.envId};try{const a=await this.$api.runTask(e,t);200===a.status&&((0,_e.df)({title:"任务已启动",message:"请前往报告列表查看结果",type:"success",duration:3e3,showClose:!0,position:"top-right"}),this.refreshTaskList())}catch(a){(0,ge.nk)({type:"error",message:"任务启动失败: "+(a.message||"未知错误"),duration:3e3}),this.refreshTaskList()}},refreshTaskList(){setTimeout(()=>{this.allTask(this.pages.current)},1e3)},getStatusText(e){return this.statusMap[e]?.text||e},getStatusClass(e){return this.statusMap[e]?.class||"status-unknown"},getRunPatternType(e){const t={loadtest:"primary",stresstest:"warning",spiketest:"danger",volumetest:"success",endurancetest:"info",baselinetest:""};return t[e]||"info"},getDistributedModeText(e){const t={single:"单机模式",distributed:"分布式模式"};return t[e]||e||"未设置"},getDistributedModeType(e){const t={single:"info",distributed:"warning"};return t[e]||"info"},formatTimeAgo(e){if(!e)return"";const t=new Date(e),a=new Date,s=Math.floor((a-t)/1e3);if(s<60)return"刚刚";const r=Math.floor(s/60);if(r<60)return`${r}分钟前`;const l=Math.floor(r/60);if(l<24)return`${l}小时前`;const i=Math.floor(l/24);return i<7?`${i}天前`:this.$tools.rTime(e).split(" ")[0]},getRandomColor(e){if(!e)return"#909399";if(this.colorCache[e])return this.colorCache[e];let t=0;for(let r=0;r<e.length;r++)t=e.charCodeAt(r)+((t<<5)-t);const a=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#3963BC","#4EC9B0","#FF9F43","#9C27B0","#00BCD4","#795548","#607D8B","#FF5722","#9E9E9E","#4CAF50"],s=Math.abs(t)%a.length;return this.colorCache[e]=a[s],this.colorCache[e]},handleCommand(e,t){switch(e){case"copy":this.clickCopy(t);break;case"delete":this.delTask(t.id);break}}},created(){this.allTask(1)}},Fe=a(71241);const we=(0,Fe.A)(ye,[["render",fe],["__scopeId","data-v-0ebe0c80"]]);var Le=we}}]);
//# sourceMappingURL=315.dc71d75f.js.map