{"version": 3, "file": "js/619.945114e0.js", "mappings": "8LACOA,MAAM,+B,SACmBA,MAAM,sB,kGADpCC,EAAAA,EAAAA,IAiBM,MAjBNC,EAiBM,CAhBQC,EAAAC,KAAQD,EAAAC,IAAIC,K,WASxBC,EAAAA,EAAAA,IAMEC,EAAA,C,MAJC,aAAYJ,EAAAC,IAAIC,GAChBG,iBAAiBC,EAAAC,qBACjBC,oBAAoBF,EAAAG,wBACpBC,mBAAoBJ,EAAAK,wB,mGAdvBb,EAAAA,EAAAA,IAQM,MARNc,EAQM,EAPJC,EAAAA,EAAAA,IAMEC,EAAA,CALAC,MAAM,QACNC,KAAK,UACLC,YAAY,mBACZ,eACCC,UAAU,Q,oCCPZrB,MAAM,iB,GACJA,MAAM,oB,GACJA,MAAM,e,GACJA,MAAM,gB,GAMRA,MAAM,kB,GAeRA,MAAM,gB,GACJA,MAAM,4B,GACJA,MAAM,gB,GACJA,MAAM,a,GAGNA,MAAM,a,GACJA,MAAM,c,GAKZA,MAAM,6B,GACJA,MAAM,gB,GACJA,MAAM,a,GAGNA,MAAM,a,GACJA,MAAM,c,GAMZA,MAAM,+B,GACJA,MAAM,gB,GACJA,MAAM,a,GAGNA,MAAM,a,GACJA,MAAM,c,GAMZA,MAAM,2B,GACJA,MAAM,gB,GACJA,MAAM,a,GAGNA,MAAM,a,GACJA,MAAM,c,GAQdA,MAAM,gB,GAEJA,MAAM,gB,SAgBuBA,MAAM,iB,GACjCA,MAAM,gB,GAoBAA,MAAM,a,GACHA,MAAM,a,GAeRA,MAAM,kB,GAgBPA,MAAM,sB,GAyBNA,MAAM,gB,GASNA,MAAM,kB,SAgBiBA,MAAM,iB,GACnCA,MAAM,gB,GAEJA,MAAM,gB,GA2BFA,MAAM,c,GAcNA,MAAM,iB,GAgBNA,MAAM,gB,GASNA,MAAM,Y,SAkBEA,MAAM,c,GAiClBA,MAAM,mB,IAoBNA,MAAM,oB,mBAsBNA,MAAM,wB,yBA8BkBA,MAAM,gB,UAGgBA,MAAM,c,IAmBpDA,MAAM,iB,IASRA,MAAM,iB,03BAlajBC,EAAAA,EAAAA,IA0aM,MA1aNC,EA0aM,EAzaJoB,EAAAA,EAAAA,IAoBM,MApBNP,EAoBM,EAnBJO,EAAAA,EAAAA,IAMM,MANNC,EAMM,EALJD,EAAAA,EAAAA,IAGM,MAHNE,EAGM,EAFJR,EAAAA,EAAAA,IAAiCS,GAAA,M,iBAAxB,IAAc,EAAdT,EAAAA,EAAAA,IAAcU,K,qBACvBJ,EAAAA,EAAAA,IAAa,UAAT,QAAI,M,eAEVA,EAAAA,EAAAA,IAAkD,OAA7CtB,MAAM,mBAAkB,mBAAe,OAE9CsB,EAAAA,EAAAA,IAWM,MAXNK,EAWM,EAVJX,EAAAA,EAAAA,IAKYY,GAAA,CAJT5B,OAAK6B,EAAAA,EAAAA,IAAA,sBAAyBC,EAAAC,sBAAwB,YAAc,KACpEC,QAAOvB,EAAAwB,uB,kBACR,IAA2B,EAA3BjB,EAAAA,EAAAA,IAA2BS,GAAA,M,iBAAlB,IAAQ,EAART,EAAAA,EAAAA,IAAQkB,M,eAAU,KAC3BC,EAAAA,EAAAA,IAAGL,EAAAC,sBAAwB,MAAQ,QAAX,K,6BAE1Bf,EAAAA,EAAAA,IAGYY,GAAA,CAHD5B,MAAM,eAAgBgC,QAAKI,EAAA,KAAAA,EAAA,GAAAC,GAAEP,EAAAQ,kBAAmB,I,kBACzD,IAA2B,EAA3BtB,EAAAA,EAAAA,IAA2BS,GAAA,M,iBAAlB,IAAQ,EAART,EAAAA,EAAAA,IAAQuB,M,6BAAU,a,mBAOjCjB,EAAAA,EAAAA,IA+CM,MA/CNkB,EA+CM,EA9CJlB,EAAAA,EAAAA,IAUM,MAVNmB,EAUM,EATJnB,EAAAA,EAAAA,IAQM,MARNoB,EAQM,EAPJpB,EAAAA,EAAAA,IAEM,MAFNqB,EAEM,EADJ3B,EAAAA,EAAAA,IAA8BS,GAAA,M,iBAArB,IAAW,EAAXT,EAAAA,EAAAA,IAAW4B,M,SAEtBtB,EAAAA,EAAAA,IAGM,MAHNuB,EAGM,EAFJvB,EAAAA,EAAAA,IAAgE,MAAhEwB,GAAgEX,EAAAA,EAAAA,IAArCL,EAAAiB,gBAAgBC,cAAY,G,eACvD1B,EAAAA,EAAAA,IAAkC,OAA7BtB,MAAM,cAAa,QAAI,WAIlCsB,EAAAA,EAAAA,IAUM,MAVN2B,EAUM,EATJ3B,EAAAA,EAAAA,IAQM,MARN4B,EAQM,EAPJ5B,EAAAA,EAAAA,IAEM,MAFN6B,EAEM,EADJnC,EAAAA,EAAAA,IAAwCS,GAAA,M,iBAA/B,IAAqB,EAArBT,EAAAA,EAAAA,IAAqBoC,M,SAEhC9B,EAAAA,EAAAA,IAGM,MAHN+B,EAGM,EAFJ/B,EAAAA,EAAAA,IAAiE,MAAjEgC,GAAiEnB,EAAAA,EAAAA,IAAtCL,EAAAiB,gBAAgBQ,eAAa,G,eACxDjC,EAAAA,EAAAA,IAAkC,OAA7BtB,MAAM,cAAa,QAAI,WAKlCsB,EAAAA,EAAAA,IAUM,MAVNkC,EAUM,EATJlC,EAAAA,EAAAA,IAQM,MARNmC,EAQM,EAPJnC,EAAAA,EAAAA,IAEM,MAFNoC,EAEM,EADJ1C,EAAAA,EAAAA,IAAwCS,GAAA,M,iBAA/B,IAAqB,EAArBT,EAAAA,EAAAA,IAAqB2C,M,SAEhCrC,EAAAA,EAAAA,IAGM,MAHNsC,EAGM,EAFJtC,EAAAA,EAAAA,IAAmE,MAAnEuC,GAAmE1B,EAAAA,EAAAA,IAAxCL,EAAAiB,gBAAgBe,iBAAe,G,eAC1DxC,EAAAA,EAAAA,IAAiC,OAA5BtB,MAAM,cAAa,OAAG,WAKjCsB,EAAAA,EAAAA,IAUM,MAVNyC,EAUM,EATJzC,EAAAA,EAAAA,IAQM,MARN0C,EAQM,EAPJ1C,EAAAA,EAAAA,IAEM,MAFN2C,EAEM,EADJjD,EAAAA,EAAAA,IAA8BS,GAAA,M,iBAArB,IAAW,EAAXT,EAAAA,EAAAA,IAAWkD,M,SAEtB5C,EAAAA,EAAAA,IAGM,MAHN6C,EAGM,EAFJ7C,EAAAA,EAAAA,IAAqD,MAArD8C,GAAqDjC,EAAAA,EAAAA,IAA1BL,EAAAuC,WAAWC,QAAM,G,eAC5ChD,EAAAA,EAAAA,IAAkC,OAA7BtB,MAAM,cAAa,QAAI,aAOpCsB,EAAAA,EAAAA,IAqNM,MArNNiD,EAqNM,EAnNJjD,EAAAA,EAAAA,IAaM,MAbNkD,EAaM,EAZJlD,EAAAA,EAAAA,IAKM,OAJHtB,OAAK6B,EAAAA,EAAAA,IAAA,YAA6B,UAAdC,EAAA2C,UAAwB,SAAW,KACvDzC,QAAKI,EAAA,KAAAA,EAAA,GAAAC,GAAEP,EAAA2C,UAAY,U,EACpBzD,EAAAA,EAAAA,IAA+BS,GAAA,M,iBAAtB,IAAY,EAAZT,EAAAA,EAAAA,IAAY0D,M,6BAAU,Y,IAGjCpD,EAAAA,EAAAA,IAKM,OAJHtB,OAAK6B,EAAAA,EAAAA,IAAA,YAA6B,YAAdC,EAAA2C,UAA0B,SAAW,KACzDzC,QAAKI,EAAA,KAAAA,EAAA,GAAAC,GAAEP,EAAA2C,UAAY,Y,EACpBzD,EAAAA,EAAAA,IAAgCS,GAAA,M,iBAAvB,IAAa,EAAbT,EAAAA,EAAAA,IAAa2D,M,6BAAU,Y,KAMX,UAAd7C,EAAA2C,Y,WAAXxE,EAAAA,EAAAA,IAoGM,MApGN2E,EAoGM,EAnGJtD,EAAAA,EAAAA,IASM,MATNuD,EASM,C,eARJvD,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRN,EAAAA,EAAAA,IAME8D,GAAA,C,WALShD,EAAAiD,kB,qCAAAjD,EAAAiD,kBAAiB1C,GAC1B2C,YAAY,SACZ,cAAY,SACZC,UAAA,GACAjF,MAAM,gB,+CAIVM,EAAAA,EAAAA,IAuFW4E,GAAA,CAtFRC,KAAM1E,EAAA2E,cAEP,aAAW,SACXpF,MAAM,aACN,iBAAe,a,kBAEf,IAMkB,EANlBgB,EAAAA,EAAAA,IAMkBqE,GAAA,CANDC,MAAM,OAAOC,KAAK,OAAO,YAAU,S,CACvCC,SAAOC,EAAAA,EAAAA,IAGVC,GAHiB,EACvBpE,EAAAA,EAAAA,IAEM,MAFNqE,EAEM,EADJrE,EAAAA,EAAAA,IAAmD,OAAnDsE,GAAmDzD,EAAAA,EAAAA,IAAxBuD,EAAMG,IAAIC,MAAI,O,OAK/C9E,EAAAA,EAAAA,IAMkBqE,GAAA,CANDC,MAAM,OAAOS,MAAM,O,CACvBP,SAAOC,EAAAA,EAAAA,IAGPC,GAHc,EACvB1E,EAAAA,EAAAA,IAESgF,GAAA,CAFDC,KAAK,QAAQC,OAAO,OAAOlG,MAAM,c,kBACvC,IAA8C,E,iBAA3CS,EAAA0F,kBAAkBT,EAAMG,IAAIO,cAAW,K,oBAKhDpF,EAAAA,EAAAA,IAMkBqE,GAAA,CANDC,MAAM,KAAKS,MAAM,O,CACrBP,SAAOC,EAAAA,EAAAA,IAGTC,GAHgB,EACvBpE,EAAAA,EAAAA,IAEO,OAFP+E,GAEOlE,EAAAA,EAAAA,IADF1B,EAAA6F,iBAAiBZ,EAAMG,IAAIU,YAAa,KAACpE,EAAAA,EAAAA,IAAGuD,EAAMG,IAAIW,YAASrE,EAAAA,EAAAA,IAAM1B,EAAAgG,YAAYf,EAAMG,IAAIO,cAAW,K,OAK/GpF,EAAAA,EAAAA,IAMkBqE,GAAA,CANDC,MAAM,OAAOS,MAAM,M,CACvBP,SAAOC,EAAAA,EAAAA,IAGVC,GAHiB,EACvBpE,EAAAA,EAAAA,IAEM,OAFDtB,OAAK6B,EAAAA,EAAAA,IAAA,CAAC,iBAAgB,YAAuB6D,EAAMG,IAAIa,a,QACvDjG,EAAAkG,gBAAgBjB,EAAMG,IAAIa,WAAQ,K,OAK3C1F,EAAAA,EAAAA,IAakBqE,GAAA,CAbDC,MAAM,OAAOS,MAAM,O,CACvBP,SAAOC,EAAAA,EAAAA,IAUVC,GAViB,EACvBpE,EAAAA,EAAAA,IASM,MATNsF,EASM,G,aARJ3G,EAAAA,EAAAA,IAOS4G,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANQpB,EAAMG,IAAIkB,mBAAlB5F,K,WADTb,EAAAA,EAAAA,IAOS0F,GAAA,CALNgB,IAAK7F,EACN8E,KAAK,QACLjG,MAAM,mBACNkG,OAAO,S,kBACP,IAAmC,E,iBAAhCzF,EAAAwG,wBAAwB9F,IAAI,K,8BAMvCH,EAAAA,EAAAA,IAQkBqE,GAAA,CARDC,MAAM,KAAKS,MAAM,M,CACrBP,SAAOC,EAAAA,EAAAA,IAKJC,GALW,EACvB1E,EAAAA,EAAAA,IAIYkG,GAAA,C,WAHDxB,EAAMG,IAAIsB,W,yBAAVzB,EAAMG,IAAIsB,WAAU9E,EAC5B+E,SAAM/E,GAAE5B,EAAA4G,iBAAiB3B,EAAMG,KAChC7F,MAAM,iB,iEAKZgB,EAAAA,EAAAA,IAOkBqE,GAAA,CAPDC,MAAM,OAAOC,KAAK,cAAcQ,MAAM,O,CAC1CP,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpE,EAAAA,EAAAA,IAGM,MAHNgG,EAGM,EAFJtG,EAAAA,EAAAA,IAA+BS,GAAA,M,iBAAtB,IAAY,EAAZT,EAAAA,EAAAA,IAAYuG,M,OACrBjG,EAAAA,EAAAA,IAAoD,aAAAa,EAAAA,EAAAA,IAA3C1B,EAAA+G,WAAW9B,EAAMG,IAAI4B,cAAW,O,OAK/CzG,EAAAA,EAAAA,IAakBqE,GAAA,CAbDC,MAAM,KAAKS,MAAM,MAAM2B,MAAM,S,CACjClC,SAAOC,EAAAA,EAAAA,IAUVC,GAViB,EACvBpE,EAAAA,EAAAA,IASM,MATNqG,EASM,EARJ3G,EAAAA,EAAAA,IAGYY,GAAA,CAHDT,KAAK,UAAU8E,KAAK,QAASjE,QAAKK,GAAE5B,EAAAmH,SAASlC,EAAMG,KAAM7F,MAAM,uB,kBACxE,IAA2B,EAA3BgB,EAAAA,EAAAA,IAA2BS,GAAA,M,iBAAlB,IAAQ,EAART,EAAAA,EAAAA,IAAQ6G,M,6BAAU,W,gCAG7B7G,EAAAA,EAAAA,IAGYY,GAAA,CAHDT,KAAK,SAAS8E,KAAK,QAASjE,QAAKK,GAAE5B,EAAAqH,WAAWpC,EAAMG,KAAM7F,MAAM,yB,kBACzE,IAA6B,EAA7BgB,EAAAA,EAAAA,IAA6BS,GAAA,M,iBAApB,IAAU,EAAVT,EAAAA,EAAAA,IAAU+G,M,6BAAU,W,kEA/E1BjG,EAAAkG,oB,eAyFU,YAAdlG,EAAA2C,Y,WAAXxE,EAAAA,EAAAA,IA2FM,MA3FNgI,EA2FM,EA1FJ3G,EAAAA,EAAAA,IAkBM,MAlBN4G,EAkBM,C,eAjBJ5G,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAeM,MAfN6G,EAeM,EAdJnH,EAAAA,EAAAA,IAKYoH,GAAA,C,WALQtG,EAAAuG,oB,qCAAAvG,EAAAuG,oBAAmBhG,GAAE2C,YAAY,OAAOhF,MAAM,iB,kBAChE,IAAiC,EAAjCgB,EAAAA,EAAAA,IAAiCsH,GAAA,CAAtBhD,MAAM,KAAKiD,MAAM,MAC5BvH,EAAAA,EAAAA,IAAuCsH,GAAA,CAA5BhD,MAAM,KAAKiD,MAAM,YAC5BvH,EAAAA,EAAAA,IAA8CsH,GAAA,CAAnChD,MAAM,MAAMiD,MAAM,kBAC7BvH,EAAAA,EAAAA,IAA0CsH,GAAA,CAA/BhD,MAAM,MAAMiD,MAAM,e,wBAE/BvH,EAAAA,EAAAA,IAOEwH,GAAA,C,WANS1G,EAAA2G,iB,qCAAA3G,EAAA2G,iBAAgBpG,GACzBlB,KAAK,YACL,kBAAgB,IAChB,oBAAkB,OAClB,kBAAgB,OAChBnB,MAAM,c,iDAKZM,EAAAA,EAAAA,IAqEW4E,GAAA,CApERC,KAAM1E,EAAAiI,gBAEP,aAAW,SACX1I,MAAM,aACN,iBAAe,a,kBAEf,IAIkB,EAJlBgB,EAAAA,EAAAA,IAIkBqE,GAAA,CAJDC,MAAM,OAAOC,KAAK,YAAY,YAAU,O,CAC5CC,SAAOC,EAAAA,EAAAA,IACuCC,GADhC,EACvBpE,EAAAA,EAAAA,IAAuD,MAAvDqH,GAAuDxG,EAAAA,EAAAA,IAA5BuD,EAAMG,IAAI+C,WAAS,K,OAIlD5H,EAAAA,EAAAA,IAMkBqE,GAAA,CANDC,MAAM,OAAOS,MAAM,M,CACvBP,SAAOC,EAAAA,EAAAA,IAGVC,GAHiB,EACvBpE,EAAAA,EAAAA,IAEM,OAFDtB,OAAK6B,EAAAA,EAAAA,IAAA,CAAC,iBAAgB,YAAuB6D,EAAMG,IAAIa,a,QACvDjG,EAAAkG,gBAAgBjB,EAAMG,IAAIa,WAAQ,K,OAK3C1F,EAAAA,EAAAA,IAMkBqE,GAAA,CANDC,MAAM,MAAMS,MAAM,O,CACtBP,SAAOC,EAAAA,EAAAA,IAGVC,GAHiB,EACvBpE,EAAAA,EAAAA,IAEM,MAFNuH,GAEM1G,EAAAA,EAAAA,IADDuD,EAAMG,IAAIiD,kBAAe3G,EAAAA,EAAAA,IAAM1B,EAAAgG,YAAYf,EAAMG,IAAIO,cAAW,K,OAKzEpF,EAAAA,EAAAA,IAMkBqE,GAAA,CANDC,MAAM,KAAKS,MAAM,O,CACrBP,SAAOC,EAAAA,EAAAA,IAGVC,GAHiB,EACvBpE,EAAAA,EAAAA,IAEM,OAFDtB,OAAK6B,EAAAA,EAAAA,IAAA,CAAC,eAAc,UAAqB6D,EAAMG,IAAIkD,W,QACnDtI,EAAAuI,mBAAmBtD,EAAMG,IAAIkD,SAAM,K,OAK5C/H,EAAAA,EAAAA,IAOkBqE,GAAA,CAPDC,MAAM,OAAOC,KAAK,eAAeQ,MAAM,O,CAC3CP,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpE,EAAAA,EAAAA,IAGM,MAHN2H,EAGM,EAFJjI,EAAAA,EAAAA,IAA4BS,GAAA,M,iBAAnB,IAAS,EAATT,EAAAA,EAAAA,IAASkI,M,OAClB5H,EAAAA,EAAAA,IAAqD,aAAAa,EAAAA,EAAAA,IAA5C1B,EAAA+G,WAAW9B,EAAMG,IAAIsD,eAAY,O,OAKhDnI,EAAAA,EAAAA,IAOkBqE,GAAA,CAPDC,MAAM,OAAOS,MAAM,O,CACvBP,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpE,EAAAA,EAAAA,IAGM,MAHN8H,EAGM,EAFJpI,EAAAA,EAAAA,IAA4BS,GAAA,M,iBAAnB,IAAS,EAATT,EAAAA,EAAAA,IAASqI,M,OAClB/H,EAAAA,EAAAA,IAAmF,aAAAa,EAAAA,EAAAA,IAA1E1B,EAAA6I,kBAAkB5D,EAAMG,IAAIsD,aAAczD,EAAMG,IAAI0D,cAAW,O,OAK9EvI,EAAAA,EAAAA,IAakBqE,GAAA,CAbDC,MAAM,KAAKS,MAAM,O,CACrBP,SAAOC,EAAAA,EAAAA,IASJC,GATW,CAEM,WAArBA,EAAMG,IAAIkD,S,WADlBzI,EAAAA,EAAAA,IAQYsB,GAAA,C,MANVT,KAAK,UACL8E,KAAK,QACLjG,MAAM,yBACLgC,QAAKK,GAAE5B,EAAA+I,iBAAiB9D,EAAMG,M,kBAC/B,IAA4B,EAA5B7E,EAAAA,EAAAA,IAA4BS,GAAA,M,iBAAnB,IAAS,EAATT,EAAAA,EAAAA,IAASyI,M,6BAAU,W,6CAG9BxJ,EAAAA,EAAAA,IAA0C,OAA1CyJ,EAAgC,U,+BAhEzB5H,EAAA6H,sB,kBAwEjB3I,EAAAA,EAAAA,IAuIY4I,GAAA,C,WAtID9H,EAAAQ,iB,uCAAAR,EAAAQ,iBAAgBD,GACxBnB,MAAOY,EAAA+H,YAAc,SAAW,SACjC9D,MAAM,QACN,sBACA/F,MAAM,gBACL8J,QAAOrJ,EAAAsJ,W,CAyHGC,QAAMvE,EAAAA,EAAAA,IACf,IAKM,EALNnE,EAAAA,EAAAA,IAKM,MALN2I,GAKM,EAJJjJ,EAAAA,EAAAA,IAA8EY,GAAA,CAAlEI,QAAKI,EAAA,MAAAA,EAAA,IAAAC,GAAEP,EAAAQ,kBAAmB,GAAOtC,MAAM,c,kBAAa,IAAEoC,EAAA,MAAAA,EAAA,M,QAAF,S,eAChEpB,EAAAA,EAAAA,IAEYY,GAAA,CAFDT,KAAK,UAAWa,QAAOvB,EAAAyJ,WAAaC,QAASrI,EAAAsI,cAAepK,MAAM,c,kBAC3E,IAAmC,E,iBAAhC8B,EAAA+H,YAAc,OAAS,QAAZ,K,mDA3HpB,IAqHU,EArHV7I,EAAAA,EAAAA,IAqHUqJ,GAAA,CArHAC,MAAOxI,EAAAyI,SAAWC,MAAO1I,EAAA2I,UAAWC,IAAI,cAAc,cAAY,QAAQ1K,MAAM,a,kBACxF,IAEe,EAFfgB,EAAAA,EAAAA,IAEe2J,GAAA,CAFDrF,MAAM,OAAOC,KAAK,Q,kBAC9B,IAA0D,EAA1DvE,EAAAA,EAAAA,IAA0D8D,GAAA,C,WAAvChD,EAAAyI,SAASzE,K,qCAAThE,EAAAyI,SAASzE,KAAIzD,GAAE2C,YAAY,W,gCAGhDhE,EAAAA,EAAAA,IASe2J,GAAA,CATDrF,MAAM,OAAOC,KAAK,e,kBAC9B,IAOY,EAPZvE,EAAAA,EAAAA,IAOYoH,GAAA,C,WAPQtG,EAAAyI,SAASnE,Y,qCAATtE,EAAAyI,SAASnE,YAAW/D,GAAE2C,YAAY,UAAUhF,MAAM,c,kBACpE,IAAsD,EAAtDgB,EAAAA,EAAAA,IAAsDsH,GAAA,CAA3ChD,MAAM,SAASiD,MAAM,uBAChCvH,EAAAA,EAAAA,IAA4CsH,GAAA,CAAjChD,MAAM,MAAMiD,MAAM,gBAC7BvH,EAAAA,EAAAA,IAAqCsH,GAAA,CAA1BhD,MAAM,MAAMiD,MAAM,SAC7BvH,EAAAA,EAAAA,IAA8CsH,GAAA,CAAnChD,MAAM,SAASiD,MAAM,eAChCvH,EAAAA,EAAAA,IAAgDsH,GAAA,CAArChD,MAAM,QAAQiD,MAAM,kBAC/BvH,EAAAA,EAAAA,IAAoDsH,GAAA,CAAzChD,MAAM,QAAQiD,MAAM,uB,gCAInCvH,EAAAA,EAAAA,IAkBe2J,GAAA,CAlBDrF,MAAM,OAAOC,KAAK,a,kBAC9B,IAgBM,EAhBNjE,EAAAA,EAAAA,IAgBM,MAhBNsJ,EAgBM,EAfJ5J,EAAAA,EAAAA,IAMYoH,GAAA,C,WANQtG,EAAAyI,SAAShE,U,qCAATzE,EAAAyI,SAAShE,UAASlE,GAAE2C,YAAY,KAAKhF,MAAM,oB,kBAC7D,IAAmC,EAAnCgB,EAAAA,EAAAA,IAAmCsH,GAAA,CAAxBhD,MAAM,KAAKiD,MAAM,QAC5BvH,EAAAA,EAAAA,IAAsCsH,GAAA,CAA3BhD,MAAM,OAAOiD,MAAM,SAC9BvH,EAAAA,EAAAA,IAAmCsH,GAAA,CAAxBhD,MAAM,KAAKiD,MAAM,QAC5BvH,EAAAA,EAAAA,IAAsCsH,GAAA,CAA3BhD,MAAM,OAAOiD,MAAM,SAC9BvH,EAAAA,EAAAA,IAAmCsH,GAAA,CAAxBhD,MAAM,KAAKiD,MAAM,S,wBAE9BvH,EAAAA,EAAAA,IAOkB6J,GAAA,C,WANP/I,EAAAyI,SAAS/D,U,qCAAT1E,EAAAyI,SAAS/D,UAASnE,GAC1ByI,IAAK,EACLC,UAAW,EACZ,oBAAkB,QAClB/K,MAAM,kBACNgF,YAAY,M,kCAKlBhE,EAAAA,EAAAA,IAoBe2J,GAAA,CApBDrF,MAAM,OAAOC,KAAK,Y,kBAC9B,IAkBM,EAlBNjE,EAAAA,EAAAA,IAkBM,MAlBN0J,GAkBM,G,WAjBJ/K,EAAAA,EAAAA,IAgBM4G,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAfY,C,2NAATmE,IADT3J,EAAAA,EAAAA,IAgBM,OATH0F,IAAKiE,EAAM1C,MACXvI,OAAK6B,EAAAA,EAAAA,IAAA,iCAAuCC,EAAAyI,SAAS7D,WAAauE,EAAM1C,SACxE2C,OAAKC,EAAAA,EAAAA,IAAA,C,eAAoCrJ,EAAAyI,SAAS7D,WAAauE,EAAM1C,MAAQ0C,EAAMG,MAAK,U,MAAuCtJ,EAAAyI,SAAS7D,WAAauE,EAAM1C,MAAQ0C,EAAMG,MAAK,U,mBAAkDtJ,EAAAyI,SAAS7D,WAAauE,EAAM1C,MAAQ0C,EAAMI,GAAE,gBAK5QrJ,QAAKK,GAAEP,EAAAyI,SAAS7D,SAAWuE,EAAM1C,Q,QAC/B0C,EAAMK,MAAI,GAAAC,K,gBAKnBvK,EAAAA,EAAAA,IAce2J,GAAA,CAdDrF,MAAM,OAAOC,KAAK,sB,kBAC9B,IAYM,EAZNjE,EAAAA,EAAAA,IAYM,MAZNkK,GAYM,G,WAXJvL,EAAAA,EAAAA,IAUM4G,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IATW,CAAC,QAAS,UAAW,WAAY,UAAzC3F,IADTG,EAAAA,EAAAA,IAUM,OARH0F,IAAK7F,EACLnB,OAAK6B,EAAAA,EAAAA,IAAA,iCAAuCC,EAAAyI,SAASxD,mBAAmB0E,SAAStK,MACjFa,QAAKK,GAAE5B,EAAAiL,uBAAuBvK,I,CACP,UAATA,I,WAAfb,EAAAA,EAAAA,IAAsDmB,GAAA,CAAAuF,IAAA,I,iBAArB,IAAW,EAAXhG,EAAAA,EAAAA,IAAW2K,M,OACf,YAATxK,I,WAApBb,EAAAA,EAAAA,IAAgEmB,GAAA,CAAAuF,IAAA,I,iBAAxB,IAAc,EAAdhG,EAAAA,EAAAA,IAAc4K,M,OACzB,aAATzK,I,WAApBb,EAAAA,EAAAA,IAAmEmB,GAAA,CAAAuF,IAAA,I,iBAA1B,IAAgB,EAAhBhG,EAAAA,EAAAA,IAAgB6K,M,OAC5B,WAAT1K,I,WAApBb,EAAAA,EAAAA,IAA8DmB,GAAA,CAAAuF,IAAA,I,iBAAvB,IAAa,EAAbhG,EAAAA,EAAAA,IAAa8K,M,gCAAU,KAC9D3J,EAAAA,EAAAA,IAAG1B,EAAAwG,wBAAwB9F,IAAI,I,wBAKrCH,EAAAA,EAAAA,IA0Be2J,GAAA,CA1BDrF,MAAM,OAAOC,KAAK,Y,kBAC9B,IAqBY,EArBZvE,EAAAA,EAAAA,IAqBYoH,GAAA,C,WApBDtG,EAAAyI,SAASwB,S,uCAATjK,EAAAyI,SAASwB,SAAQ1J,GAC1B2J,SAAA,GACAhH,YAAY,eACZhF,MAAM,aACLoH,SAAQ3G,EAAAwL,0BACR9B,QAASrI,EAAAoK,c,CAOCC,OAAK1G,EAAAA,EAAAA,IACd,IAEM,CAFK3D,EAAAoK,e,WAAXjM,EAAAA,EAAAA,IAEM,MAFNmM,GAA8C,eAGF,IAA5BtK,EAAAuK,iBAAiB/H,S,WAAjCrE,EAAAA,EAAAA,IAEM,MAFNqM,GAAkE,c,kCATlE,IAAgC,G,aADlCrM,EAAAA,EAAAA,IAKY4G,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJKhF,EAAAuK,iBAARE,K,WADTjM,EAAAA,EAAAA,IAKYgI,GAAA,CAHTtB,IAAKuF,EAAKlM,GACViF,MAAOiH,EAAKzG,KACZyC,MAAOgE,EAAKlM,I,+FAWjBiB,EAAAA,EAAAA,IAEM,OAFDtB,MAAM,kBAAiB,mCAE5B,M,eAGFgB,EAAAA,EAAAA,IAMe2J,GAAA,CANDrF,MAAM,MAAI,C,iBACtB,IAI0B,EAJ1BtE,EAAAA,EAAAA,IAI0B8D,GAAA,C,WAHfhD,EAAAyI,SAASnJ,Y,uCAATU,EAAAyI,SAASnJ,YAAWiB,GAC7BlB,KAAK,WACJqL,KAAM,EACPxH,YAAY,W,gCAGhBhE,EAAAA,EAAAA,IAMe2J,GAAA,CANDrF,MAAM,QAAM,C,iBACxB,IAIM,EAJNhE,EAAAA,EAAAA,IAIM,MAJNmL,GAIM,EAHJnL,EAAAA,EAAAA,IAA2E,QAApEtB,OAAK6B,EAAAA,EAAAA,IAAA,yBAA+BC,EAAAyI,SAASpD,eAAc,KAAE,IACpEnG,EAAAA,EAAAA,IAA8DkG,GAAA,C,WAA1CpF,EAAAyI,SAASpD,W,uCAATrF,EAAAyI,SAASpD,WAAU9E,GAAErC,MAAM,c,wBAC/CsB,EAAAA,EAAAA,IAA0E,QAAnEtB,OAAK6B,EAAAA,EAAAA,IAAA,wBAA8BC,EAAAyI,SAASpD,eAAc,KAAE,O,4KAyB/E,IACErB,KAAM,eACN4G,WAAY,CACVC,KAAI,QAAEC,KAAI,QAAEC,QAAO,WAAEC,kBAAiB,qBAAEC,kBAAiB,qBAAEC,QAAO,WAClEC,KAAI,QAAEC,OAAM,UAAEC,MAAK,SAAEC,SAAQ,YAAEC,MAAK,SAAEC,MAAK,SAAEC,QAAO,WAAEC,WAAU,cAChEC,aAAY,gBAAEC,UAAS,aAAEC,WAAU,cAAEC,SAAQ,YAAEC,UAAS,aAAEC,OAAMA,GAAAA,QAElEC,MAAO,CACLC,UAAW,CACT7M,KAAM,CAAC8M,OAAQC,QACfC,UAAU,IAGdC,MAAO,CAAC,kBAAmB,qBAAsB,sBACjDjJ,IAAAA,GACE,MAAO,CACLV,UAAW,QACXJ,WAAY,GACZgK,aAAc,GACdrG,cAAc,EACd2B,gBAAgB,EAChBS,eAAe,EACf9H,kBAAkB,EAClBuH,YAAa,KACb9H,uBAAuB,EACvBgD,kBAAmB,GACnBsD,oBAAqB,GACrBI,iBAAkB,KAClB6F,aAAc,KACdjC,iBAAkB,GAClBH,cAAc,EAEdnJ,gBAAiB,CACfC,aAAc,EACdO,cAAe,EACfO,gBAAiB,GAGnByG,SAAU,CACRzE,KAAM,GACNM,YAAa,GACbG,UAAW,GACXC,UAAW,EACXE,SAAU,GACVK,mBAAoB,GACpB3F,YAAa,GACb+F,YAAY,EACZ4E,SAAU,IAGZtB,UAAW,CACT3E,KAAM,CACJ,CAAEqI,UAAU,EAAMI,QAAS,UAAWC,QAAS,SAEjDpI,YAAa,CACX,CAAE+H,UAAU,EAAMI,QAAS,UAAWC,QAAS,WAEjDjI,UAAW,CACT,CAAE4H,UAAU,EAAMI,QAAS,UAAWC,QAAS,WAEjDhI,UAAW,CACT,CAAE2H,UAAU,EAAMI,QAAS,QAASC,QAAS,QAC7C,CAAErN,KAAM,SAAUoN,QAAS,UAAWC,QAAS,SAEjD9H,SAAU,CACR,CAAEyH,UAAU,EAAMI,QAAS,UAAWC,QAAS,WAEjDzH,mBAAoB,CAClB,CAAEoH,UAAU,EAAMI,QAAS,UAAWC,QAAS,UAC/C,CAAErN,KAAM,QAAS2J,IAAK,EAAGyD,QAAS,cAAeC,QAAS,WAE5DzC,SAAU,CAER,CAAE5K,KAAM,QAASoN,QAAS,UAAWC,QAAS,YAItD,EACAC,OAAAA,GACEC,KAAKC,iBACLD,KAAKE,mBACLF,KAAKG,kBACLH,KAAKI,uBAGLJ,KAAKJ,aAAeS,YAAY,KAC1BL,KAAK3M,wBACP2M,KAAKE,mBACLF,KAAKG,oBAEN,IACL,EACAG,aAAAA,GAEMN,KAAKJ,cACPW,cAAcP,KAAKJ,aAEvB,EACAY,SAAU,CAER9J,aAAAA,GACE,IAAKsJ,KAAK3J,kBACR,OAAO2J,KAAKrK,WAEd,MAAM8K,EAAUT,KAAK3J,kBAAkBqK,cACvC,OAAOV,KAAKrK,WAAWgL,OAAOC,GAC5BA,EAAKxJ,KAAKsJ,cAAc3D,SAAS0D,GAErC,EAGAzG,eAAAA,GACE,IAAI6G,EAASb,KAAKL,aAQlB,GALIK,KAAKrG,sBACPkH,EAASA,EAAOF,OAAOG,GAAQA,EAAKzG,SAAW2F,KAAKrG,sBAIlDqG,KAAKjG,kBAAqD,IAAjCiG,KAAKjG,iBAAiBnE,OAAc,CAC/D,MAAMmL,EAAY,IAAIC,KAAKhB,KAAKjG,iBAAiB,IAC3CkH,EAAU,IAAID,KAAKhB,KAAKjG,iBAAiB,IAC/CkH,EAAQC,SAAS,GAAI,GAAI,GAAI,KAE7BL,EAASA,EAAOF,OAAOG,IACrB,MAAMK,EAAc,IAAIH,KAAKF,EAAKrG,cAClC,OAAO0G,GAAeJ,GAAaI,GAAeF,GAEtD,CAEA,OAAOJ,CACT,GAEFO,QAAS,CAEPpE,sBAAAA,CAAuBvK,GACrB,MAAM4O,EAAQrB,KAAKnE,SAASxD,mBAAmBiJ,QAAQ7O,GACnD4O,GAAS,EACXrB,KAAKnE,SAASxD,mBAAmBkJ,OAAOF,EAAO,GAE/CrB,KAAKnE,SAASxD,mBAAmBmJ,KAAK/O,EAE1C,EAGA8K,yBAAAA,CAA0B1D,GACxBmG,KAAKnE,SAASwB,SAAWxD,EACzB4H,QAAQC,IAAI,SAAU7H,EACxB,EAGA,oBAAMoG,GACJD,KAAK1G,cAAe,EACpB,IACEmI,QAAQC,IAAI,iBAAkB1B,KAAKV,WACnC,MAAMqC,QAAiB3B,KAAK4B,KAAKC,cAAc,CAAEC,WAAY9B,KAAKV,YAE1C,MAApBqC,EAAStH,SACX2F,KAAKrK,WAAagM,EAASlL,MAAQ,GACnCgL,QAAQC,IAAI,YAAa1B,KAAKrK,WAAWC,OAAQ,OAErD,CAAE,MAAOmM,GACPN,QAAQM,MAAM,YAAaA,GACI,MAA3BA,EAAMJ,UAAUtH,OAClB2H,GAAAA,GAAUC,QAAQ,mBAElBD,GAAAA,GAAUD,MAAM,cAAgBA,EAAMJ,UAAUlL,MAAMoJ,SAAWkC,EAAMlC,SAAW,SAEpFG,KAAKrK,WAAa,EACpB,CAAE,QACAqK,KAAK1G,cAAe,CACtB,CACF,EAGA,sBAAM4G,GACJF,KAAK/E,gBAAiB,EACtB,IACEwG,QAAQC,IAAI,iBAAkB1B,KAAKV,WACnC,MAAMqC,QAAiB3B,KAAK4B,KAAKM,gBAAgB,CAAEJ,WAAY9B,KAAKV,YAEpE,GAAwB,MAApBqC,EAAStH,OAAgB,CAC3B,MAAM8H,EAAkBR,EAASlL,MAAQ,GACzCgL,QAAQC,IAAI,YAAaS,EAAgBvM,OAAQ,OAGjDoK,KAAKoC,mBAAmBpC,KAAKL,aAAcwC,GAG3CnC,KAAKL,aAAewC,CACtB,CACF,CAAE,MAAOJ,GACPN,QAAQM,MAAM,YAAaA,GACI,MAA3BA,EAAMJ,UAAUtH,OAClB2H,GAAAA,GAAUC,QAAQ,mBAElBD,GAAAA,GAAUD,MAAM,cAAgBA,EAAMJ,UAAUlL,MAAMoJ,SAAWkC,EAAMlC,SAAW,SAEpFG,KAAKL,aAAe,EACtB,CAAE,QACAK,KAAK/E,gBAAiB,CACxB,CACF,EAGA,qBAAMkF,GACJ,IACE,MAAMwB,QAAiB3B,KAAK4B,KAAKS,eAAe,CAAEP,WAAY9B,KAAKV,YAEnE,GAAwB,MAApBqC,EAAStH,OAAgB,CAC3B,MAAMiI,EAAaX,EAASlL,KAAK6L,YAAc,CAAC,EAChDtC,KAAK3L,gBAAkB,CACrBC,aAAcgO,EAAWC,OAAS,EAClC1N,cAAeyN,EAAWE,QAAU,EACpCpN,gBAAiBkN,EAAWG,UAAY,GAE1CzC,KAAK3M,sBAAwBsO,EAASlL,KAAKiM,oBAAqB,EAChEjB,QAAQC,IAAI,YAAa1B,KAAK3L,gBAAiB,QAAS2L,KAAK3M,sBAC/D,CACF,CAAE,MAAO0O,GACPN,QAAQM,MAAM,YAAaA,GAE3B/B,KAAK3L,gBAAkB,CACrBC,aAAc,EACdO,cAAe,EACfO,gBAAiB,GAEnB4K,KAAK3M,uBAAwB,CAC/B,CACF,EAGA,0BAAM+M,GACJJ,KAAKxC,cAAe,EACpB,IAEE,MAAMmE,QAAiB3B,KAAK4B,KAAKe,sBACjC,GAAwB,MAApBhB,EAAStH,OAAgB,CAE3B,IAAIuI,EAAQjB,EAASlL,KACjBkL,EAASlL,MAAQkL,EAASlL,KAAKoM,UACjCD,EAAQjB,EAASlL,KAAKoM,SAIxB7C,KAAKrC,iBAAmBiF,EAAME,IAAIjF,IAAG,CACnClM,GAAIkM,EAAKlM,GACTyF,KAAMyG,EAAKkF,UAAY,OAAOlF,EAAKlM,QAGrC8P,QAAQC,IAAI,YAAa1B,KAAKrC,iBAAiB/H,OAAQ,MACzD,CACF,CAAE,MAAOmM,GACPN,QAAQM,MAAM,YAAaA,GAC3BC,GAAAA,GAAUD,MAAM,cAAgBA,EAAMJ,UAAUlL,MAAMoJ,SAAWkC,EAAMlC,SAAW,SAClFG,KAAKrC,iBAAmB,EAC1B,CAAE,QACAqC,KAAKxC,cAAe,CACtB,CACF,EAGA,2BAAMjK,GACJ,IACE,IAAIoO,EACA3B,KAAK3M,uBACPoO,QAAQC,IAAI,eACZC,QAAiB3B,KAAK4B,KAAKoB,wBAE3BvB,QAAQC,IAAI,eACZC,QAAiB3B,KAAK4B,KAAKqB,wBAGL,MAApBtB,EAAStH,SACX2F,KAAK3M,uBAAyB2M,KAAK3M,sBACnC2O,GAAAA,GAAUkB,QAAQlD,KAAK3M,sBAAwB,UAAY,WAG3D2M,KAAKG,kBAET,CAAE,MAAO4B,GACPN,QAAQM,MAAM,YAAaA,GAC3BC,GAAAA,GAAUD,MAAM,cAAgBA,EAAMJ,UAAUlL,MAAMoJ,SAAWkC,EAAMlC,SAAW,QACpF,CACF,EAGA,sBAAMlH,CAAiBiI,GACrB,IACEa,QAAQC,IAAI,UAAWd,EAAKjP,GAAIiP,EAAKnI,YAGrCuJ,GAAAA,GAAUkB,QAAQtC,EAAKnI,WAAa,QAAU,SAGvBmI,EAAKnI,WAC5B,IACE,MAAMkJ,QAAiB3B,KAAK4B,KAAKuB,gBAAgBvC,EAAKjP,GAAI,CAAE8G,WAAYmI,EAAKnI,aAC7E,GAAwB,MAApBkJ,EAAStH,QAAsC,MAApBsH,EAAStH,OACtC,MAAM,IAAI+I,MAAM,SAEpB,CAAE,MAAOrB,GAGP,MADAnB,EAAKnI,YAAcmI,EAAKnI,WAClBsJ,CACR,CACF,CAAE,MAAOA,GACPN,QAAQM,MAAM,YAAaA,GAC3BC,GAAAA,GAAUD,MAAM,cAAgBA,EAAMJ,UAAUlL,MAAMoJ,SAAWkC,EAAMlC,SAAW,QACpF,CACF,EAGA3G,QAAAA,CAAS0H,GAMP,GALAZ,KAAK7E,YAAcyF,EAEnBZ,KAAKnE,SAAWwH,KAAKC,MAAMD,KAAKE,UAAU3C,IAGJ,kBAA3BZ,KAAKnE,SAASwB,SACvB,IACE2C,KAAKnE,SAASwB,SAAWgG,KAAKC,MAAMtD,KAAKnE,SAASwB,SACpD,CAAE,MAAOmG,GACP/B,QAAQM,MAAM,YAAayB,GAC3BxD,KAAKnE,SAASwB,SAAW,EAC3B,CAIG2C,KAAKnE,SAASwB,WACjB2C,KAAKnE,SAASwB,SAAW,IAI3B2C,KAAKpM,kBAAmB,CAC1B,EAGA,gBAAMwF,CAAWwH,GACf,UACQ6C,GAAAA,EAAaC,QACjB,cAAc9C,EAAKxJ,WACnB,OACA,CACEuM,kBAAmB,KACnBC,iBAAkB,KAClBnR,KAAM,YAIVgP,QAAQC,IAAI,UAAWd,EAAKjP,IAC5B,MAAMgQ,QAAiB3B,KAAK4B,KAAKiC,gBAAgBjD,EAAKjP,IAE9B,MAApBgQ,EAAStH,QAAsC,MAApBsH,EAAStH,SACtC2H,GAAAA,GAAUkB,QAAQ,QAGlBlD,KAAKrK,WAAaqK,KAAKrK,WAAWgL,OAAOmD,GAAKA,EAAEnS,KAAOiP,EAAKjP,IAGxDqO,KAAK3L,gBAAgBC,aAAe,IACtC0L,KAAK3L,gBAAgBC,cAAgB,GAG3C,CAAE,MAAOyN,GACO,WAAVA,IACFN,QAAQM,MAAM,UAAWA,GACzBC,GAAAA,GAAUD,MAAM,UAAYA,EAAMJ,UAAUlL,MAAMoJ,SAAWkC,EAAMlC,SAAW,SAElF,CACF,EAGA,sBAAM/E,CAAiBiJ,GACrB,IACEtC,QAAQC,IAAI,UAAWqC,EAAMpS,IAC7B,MAAMgQ,QAAiB3B,KAAK4B,KAAK9G,iBAAiB,CAChDkJ,SAAUD,EAAMpS,GAChBsS,aAAc,iBAGQ,MAApBtC,EAAStH,SACX2H,GAAAA,GAAUkB,QAAQ,SAElBlD,KAAKE,mBACLF,KAAKG,kBAGLH,KAAKkE,MAAM,qBAAsBH,GAErC,CAAE,MAAOhC,GACPN,QAAQM,MAAM,UAAWA,GACzBC,GAAAA,GAAUD,MAAM,YAAcA,EAAMJ,UAAUlL,MAAMoJ,SAAWkC,EAAMlC,SAAW,QAClF,CACF,EAGA,gBAAMrE,GACJ,IACE,MAAM2I,QAAcnE,KAAKoE,MAAMC,YAAYC,WAC3C,IAAKH,EAAO,OAEZnE,KAAKtE,eAAgB,EAGrB,MAAM6I,EAAS,IACVvE,KAAKnE,SACRiG,WAAY9B,KAAKV,UAEjBkF,oBAAqBnB,KAAKE,UAAU,CAClCkB,MAAO,CACLC,WAAY,IAEdC,QAAS,CACPC,IAAK,OAgCX,IAAIjD,EAOJ,GAjCK4C,EAAOlM,oBAAuBwM,MAAMC,QAAQP,EAAOlM,sBACtDkM,EAAOlM,mBAAqB,IAIW,IAArCkM,EAAOlM,mBAAmBzC,QAC5B2O,EAAOlM,mBAAmBmJ,KAAK,SAI5B+C,EAAOlH,UAAawH,MAAMC,QAAQP,EAAOlH,YAC5CkH,EAAOlH,SAAW,IAIW,IAA3BkH,EAAOlH,SAASzH,QAElB2O,EAAOQ,gBAAiB,EACxBtD,QAAQC,IAAI,kBAEZ6C,EAAOQ,gBAAiB,EAI1BtD,QAAQC,IAAI,YAAa6C,GAIvB5C,EADE3B,KAAK7E,kBACU6E,KAAK4B,KAAKuB,gBAAgBnD,KAAK7E,YAAYxJ,GAAI4S,SAE/CvE,KAAK4B,KAAKoD,aAAaT,GAGlB,MAApB5C,EAAStH,QAAsC,MAApBsH,EAAStH,OAAgB,CAKtD,GAJA2H,GAAAA,GAAUkB,QAAQlD,KAAK7E,YAAc,OAAS,QAC9C6E,KAAKpM,kBAAmB,EAGpBoM,KAAK7E,YAAa,CAEpB,MAAMkG,EAAQrB,KAAKrK,WAAWsP,UAAUnB,GAAKA,EAAEnS,KAAOqO,KAAK7E,YAAYxJ,IACvE,IAAe,IAAX0P,EAAc,CAEhB,MAAM6D,EAAcvD,EAASlL,MAAMmK,MAAQ,IACtC2D,EACH5S,GAAIqO,KAAK7E,YAAYxJ,GACrBoH,YAAaiH,KAAK7E,YAAYpC,aAGhCiH,KAAKrK,WAAW0L,GAAS6D,CAC3B,CACF,KAAO,CAEL,MAAMC,EAAUxD,EAASlL,MAAMmK,MAAQ,IAClC2D,EACH5S,GAAIqP,KAAKoE,MACTrM,aAAa,IAAIiI,MAAOqE,eAE1BrF,KAAKrK,WAAW2P,QAAQH,GAGxBnF,KAAK3L,gBAAgBC,cAAgB,EAGrC0L,KAAKkE,MAAM,qBAAsBiB,EACnC,CAGAnF,KAAKC,gBACP,CACF,CAAE,MAAO8B,GACPN,QAAQM,MAAM,QAASA,GACvBC,GAAAA,GAAUD,MAAM,UAAYA,EAAMJ,UAAUlL,MAAMoJ,SAAWkC,EAAMlC,SAAW,QAChF,CAAE,QACAG,KAAKtE,eAAgB,CACvB,CACF,EAGAL,SAAAA,GACE2E,KAAK7E,YAAc,KACnB6E,KAAKnE,SAAW,CACdzE,KAAM,GACNM,YAAa,GACbG,UAAW,GACXC,UAAW,EACXE,SAAU,GACVK,mBAAoB,GACpB3F,YAAa,GACb+F,YAAY,EACZ4E,SAAU,IAEZ2C,KAAKoE,MAAMC,aAAakB,aAC1B,EAGAzM,UAAAA,CAAW0M,GACT,OAAKA,EACE,IAAIxE,KAAKwE,GAAMC,iBADJ,GAEpB,EAGA7K,iBAAAA,CAAkB8K,EAAWC,GAC3B,IAAKD,EAAW,MAAO,IACvB,MAAME,EAAQ,IAAI5E,KAAK0E,GACjBG,EAAMF,EAAU,IAAI3E,KAAK2E,GAAW,IAAI3E,KACxC8E,EAAOC,KAAKC,OAAOH,EAAMD,GAAS,KAExC,OAAIE,EAAO,GAAW,GAAGA,KACrBA,EAAO,KAAa,GAAGC,KAAKC,MAAMF,EAAO,QACtC,GAAGC,KAAKC,MAAMF,EAAO,SAC9B,EAGArO,iBAAAA,CAAkBhF,GAChB,MAAMwT,EAAQ,CACZC,kBAAmB,OACnBC,WAAY,MACZC,IAAK,MACLC,UAAW,MACXC,aAAc,KACdC,iBAAkB,OAEpB,OAAON,EAAMxT,IAASA,CACxB,EAGAmF,gBAAAA,CAAiBC,GACf,MAAM2O,EAAa,CACjBC,GAAI,IACJC,IAAK,IACLC,GAAI,IACJC,IAAK,IACLC,GAAI,KAEN,OAAOL,EAAW3O,IAAcA,CAClC,EAGAE,WAAAA,CAAY+O,GACV,MAAMC,EAAQ,CACZb,kBAAmB,KACnBC,WAAY,IACZC,IAAK,GACLC,UAAW,IACXC,aAAc,IACdC,iBAAkB,IAEpB,OAAOQ,EAAMD,IAAe,EAC9B,EAGAE,eAAAA,CAAgBhP,GACd,MAAMiO,EAAQ,CACZgB,IAAK,OACLC,OAAQ,UACRC,KAAM,SACNC,SAAU,UAEZ,OAAOnB,EAAMjO,IAAa,MAC5B,EAGAC,eAAAA,CAAgBD,GACd,MAAMqP,EAAQ,CACZJ,IAAK,IACLC,OAAQ,IACRC,KAAM,IACNC,SAAU,MAEZ,OAAOC,EAAMrP,IAAaA,CAC5B,EAGAO,uBAAAA,CAAwB9F,GACtB,MAAMwT,EAAQ,CACZxB,MAAO,KACPE,QAAS,UACT2C,SAAU,KACVC,OAAQ,MAEV,OAAOtB,EAAMxT,IAASA,CACxB,EAGA+U,kBAAAA,CAAmBnN,GACjB,MAAM4L,EAAQ,CACZzD,OAAQ,SACRiF,aAAc,UACdhF,SAAU,WAEZ,OAAOwD,EAAM5L,IAAW,MAC1B,EAGAC,kBAAAA,CAAmBD,GACjB,MAAMgN,EAAQ,CACZ7E,OAAQ,KACRiF,aAAc,MACdhF,SAAU,OAEZ,OAAO4E,EAAMhN,IAAWA,CAC1B,EAGA+H,kBAAAA,CAAmBsF,EAAWC,GAC5B,IAAKD,GAAkC,IAArBA,EAAU9R,OAAc,OAG1C,MAAMgS,EAAkBD,EAAUhH,OAAOoD,GACtB,WAAjBA,EAAM1J,SACLqN,EAAUG,KAAKC,GAAYA,EAASnW,KAAOoS,EAAMpS,KAIpDiW,EAAgBG,QAAQhE,IACtBtC,QAAQC,IAAI,WAAYqC,GACxB/D,KAAKkE,MAAM,kBAAmBH,IAElC,I,YC/iCJ,MAAMiE,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,U,YFiBA,IACE5Q,KAAM,mBACN4G,WAAY,CACViK,aAAYA,IAEdzH,SAAU,KACL0H,EAAAA,EAAAA,IAAS,CACVxW,IAAKyW,GAASA,EAAMzW,OAGxB0W,OAAAA,GACE3G,QAAQC,IAAI,+BAAgC1B,KAAKtO,KAAKC,IAAM,QAC9D,EACAoO,OAAAA,GACE0B,QAAQC,IAAI,+BAAgC1B,KAAKtO,KAAKC,IAAM,SACvDqO,KAAKtO,KAAQsO,KAAKtO,IAAIC,IACzBqQ,GAAAA,GAAUC,QAAQ,mBAEtB,EACAb,QAAS,CACPpP,oBAAAA,CAAqBqW,IAEnBC,EAAAA,GAAAA,IAAe,CACb9V,MAAO,OACPqN,QAAS,OAAOwI,EAAUnO,uBAC1BzH,KAAM,UACN8V,SAAU,IACVC,SAAU,cAEZ/G,QAAQC,IAAI,SAAU2G,EACxB,EAEAnW,uBAAAA,CAAwBmW,IAEtBrG,EAAAA,GAAAA,IAAU,CACRvP,KAAM,UACNoN,QAAS,OAAOwI,EAAUnO,iBAC1BqO,SAAU,MAEZ9G,QAAQC,IAAI,SAAU2G,EACxB,EAEAjW,sBAAAA,CAAuBqW,IAErBzG,EAAAA,GAAAA,IAAU,CACRvP,KAAM,UACNoN,QAAS,UAAU4I,EAASrR,YAC5BmR,SAAU,MAEZ9G,QAAQC,IAAI,YAAa+G,EAC3B,IGrEJ,MAAM,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceAlert.vue", "webpack://frontend-web/./src/views/PerformanceTest/AlertManager.vue", "webpack://frontend-web/./src/views/PerformanceTest/AlertManager.vue?0175", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceAlert.vue?d856"], "sourcesContent": ["<template>\n  <div class=\"performance-alert-container\">\n    <div v-if=\"!pro || !pro.id\" class=\"no-project-warning\">\n      <el-alert\n        title=\"未选择项目\"\n        type=\"warning\"\n        description=\"请先选择一个项目，再使用告警功能\"\n        show-icon\n        :closable=\"false\"\n      />\n    </div>\n    <AlertManager \n      v-else\n      :project-id=\"pro.id\"\n      @alert-triggered=\"handleAlertTriggered\"\n      @alert-acknowledged=\"handleAlertAcknowledged\"\n      @alert-rule-created=\"handleAlertRuleCreated\"\n    />\n  </div>\n</template>\n\n<script>\nimport { mapState } from 'vuex'\nimport AlertManager from '@/views/PerformanceTest/AlertManager.vue'\nimport { ElMessage, ElNotification } from 'element-plus'\n\nexport default {\n  name: 'PerformanceAlert',\n  components: {\n    AlertManager\n  },\n  computed: {\n    ...mapState({\n      pro: state => state.pro\n    })\n  },\n  created() {\n    console.log('PerformanceAlert 组件已创建，项目ID:', this.pro?.id || '未选择项目')\n  },\n  mounted() {\n    console.log('PerformanceAlert 组件已加载，项目ID:', this.pro?.id || '未选择项目')\n    if (!this.pro || !this.pro.id) {\n      ElMessage.warning('请先选择一个项目，再使用告警功能')\n    }\n  },\n  methods: {\n    handleAlertTriggered(alertData) {\n      // 处理告警触发事件\n      ElNotification({\n        title: '告警触发',\n        message: `告警 \"${alertData.rule_name}\" 已触发，请注意查看`,\n        type: 'warning',\n        duration: 8000,\n        position: 'top-right'\n      })\n      console.log('告警已触发:', alertData)\n    },\n    \n    handleAlertAcknowledged(alertData) {\n      // 处理告警确认事件  \n      ElMessage({\n        type: 'success',\n        message: `告警 \"${alertData.rule_name}\" 已确认`,\n        duration: 3000\n      })\n      console.log('告警已确认:', alertData)\n    },\n    \n    handleAlertRuleCreated(ruleData) {\n      // 处理告警规则创建事件\n      ElMessage({\n        type: 'success',\n        message: `新告警规则 \"${ruleData.name}\" 已创建`,\n        duration: 3000\n      })\n      console.log('新告警规则已创建:', ruleData)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.performance-alert-container {\n  padding: 20px;\n  height: 100%;\n}\n\n.no-project-warning {\n  max-width: 600px;\n  margin: 100px auto;\n}\n</style>", "<template>\n  <div class=\"alert-manager\">\n    <div class=\"dashboard-header\">\n      <div class=\"header-left\">\n        <div class=\"header-title\">\n          <el-icon><AlarmClock /></el-icon>\n          <h1>告警中心</h1>\n        </div>\n        <div class=\"header-subtitle\">实时监控系统状态，及时处理异常</div>\n      </div>\n      <div class=\"header-actions\">\n        <el-button \n          :class=\"['monitor-toggle-btn', alertMonitoringStatus ? 'is-active' : '']\"\n          @click=\"toggleAlertMonitoring\">\n          <el-icon><Bell /></el-icon>\n          {{ alertMonitoringStatus ? '监控中' : '开启监控' }}\n        </el-button>\n        <el-button class=\"add-rule-btn\" @click=\"showCreateDialog = true\">\n          <el-icon><Plus /></el-icon>\n          新建规则\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 监控状态卡片 -->\n    <div class=\"status-cards\">\n      <div class=\"status-card total-alerts\">\n        <div class=\"card-content\">\n          <div class=\"card-icon\">\n            <el-icon><Warning /></el-icon>\n          </div>\n          <div class=\"card-data\">\n            <div class=\"card-value\">{{ alertStatistics.total_alerts }}</div>\n            <div class=\"card-label\">总告警数</div>\n          </div>\n        </div>\n      </div>\n      <div class=\"status-card active-alerts\">\n        <div class=\"card-content\">\n          <div class=\"card-icon\">\n            <el-icon><CircleCloseFilled /></el-icon>\n          </div>\n          <div class=\"card-data\">\n            <div class=\"card-value\">{{ alertStatistics.active_alerts }}</div>\n            <div class=\"card-label\">活跃告警</div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"status-card resolved-alerts\">\n        <div class=\"card-content\">\n          <div class=\"card-icon\">\n            <el-icon><CircleCheckFilled /></el-icon>\n          </div>\n          <div class=\"card-data\">\n            <div class=\"card-value\">{{ alertStatistics.resolved_alerts }}</div>\n            <div class=\"card-label\">已解决</div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"status-card rules-count\">\n        <div class=\"card-content\">\n          <div class=\"card-icon\">\n            <el-icon><Setting /></el-icon>\n          </div>\n          <div class=\"card-data\">\n            <div class=\"card-value\">{{ alertRules.length }}</div>\n            <div class=\"card-label\">告警规则</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 主内容区域 -->\n    <div class=\"main-content\">\n      <!-- 标签切换 -->\n      <div class=\"content-tabs\">\n        <div \n          :class=\"['tab-item', activeTab === 'rules' ? 'active' : '']\" \n          @click=\"activeTab = 'rules'\">\n          <el-icon><Document /></el-icon>\n          告警规则\n        </div>\n        <div \n          :class=\"['tab-item', activeTab === 'history' ? 'active' : '']\" \n          @click=\"activeTab = 'history'\">\n          <el-icon><Histogram /></el-icon>\n          告警历史\n        </div>\n      </div>\n      \n      <!-- 规则列表 -->\n      <div v-if=\"activeTab === 'rules'\" class=\"content-panel\">\n        <div class=\"panel-header\">\n          <h2>规则列表</h2>\n          <el-input\n            v-model=\"ruleSearchKeyword\"\n            placeholder=\"搜索规则名称\"\n            prefix-icon=\"Search\"\n            clearable\n            class=\"search-input\"\n          />\n        </div>\n        \n        <el-table\n          :data=\"filteredRules\"\n          v-loading=\"tableLoading\"\n          empty-text=\"暂无告警规则\"\n          class=\"data-table\"\n          row-class-name=\"table-row\">\n          \n          <el-table-column label=\"规则名称\" prop=\"name\" min-width=\"180px\">\n            <template #default=\"scope\">\n              <div class=\"rule-name\">\n                <span class=\"rule-text\">{{ scope.row.name }}</span>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"监控指标\" width=\"100\">\n            <template #default=\"scope\">\n              <el-tag size=\"small\" effect=\"dark\" class=\"metric-tag\">\n                {{ getMetricTypeText(scope.row.metric_type) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"条件\" width=\"120\">\n            <template #default=\"scope\">\n              <span class=\"condition-text\">\n                {{ getConditionText(scope.row.condition) }} {{ scope.row.threshold }}{{ getUnitText(scope.row.metric_type) }}\n              </span>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"严重程度\" width=\"90\">\n            <template #default=\"scope\">\n              <div class=\"severity-badge\" :class=\"'severity-' + scope.row.severity\">\n                {{ getSeverityText(scope.row.severity) }}\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"通知方式\" width=\"120\">\n            <template #default=\"scope\">\n              <div class=\"notification-types\">\n                <el-tag \n                  v-for=\"type in scope.row.notification_types\" \n                  :key=\"type\" \n                  size=\"small\"\n                  class=\"notification-tag\"\n                  effect=\"plain\">\n                  {{ getNotificationTypeText(type) }}\n                </el-tag>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"状态\" width=\"80\">\n            <template #default=\"scope\">\n              <el-switch \n                v-model=\"scope.row.is_enabled\"\n                @change=\"toggleRuleStatus(scope.row)\"\n                class=\"status-switch\">\n              </el-switch>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"创建时间\" prop=\"create_time\" width=\"160\">\n            <template #default=\"scope\">\n              <div class=\"time-display\">\n                <el-icon><Calendar /></el-icon>\n                <span>{{ formatTime(scope.row.create_time) }}</span>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"操作\" width=\"170\" fixed=\"right\">\n            <template #default=\"scope\">\n              <div class=\"action-buttons\">\n                <el-button type=\"primary\" size=\"small\" @click=\"editRule(scope.row)\" class=\"action-btn edit-btn\">\n                  <el-icon><Edit /></el-icon>\n                  编辑\n                </el-button>\n                <el-button type=\"danger\" size=\"small\" @click=\"deleteRule(scope.row)\" class=\"action-btn delete-btn\">\n                  <el-icon><Delete /></el-icon>\n                  删除\n                </el-button>\n              </div>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n      \n      <!-- 告警历史面板 -->\n      <div v-if=\"activeTab === 'history'\" class=\"content-panel\">\n        <div class=\"panel-header\">\n          <h2>告警历史</h2>\n          <div class=\"filter-group\">\n            <el-select v-model=\"historyStatusFilter\" placeholder=\"状态筛选\" class=\"filter-select\">\n              <el-option label=\"全部\" value=\"\" />\n              <el-option label=\"活跃\" value=\"active\" />\n              <el-option label=\"已确认\" value=\"acknowledged\" />\n              <el-option label=\"已解决\" value=\"resolved\" />\n            </el-select>\n            <el-date-picker\n              v-model=\"historyDateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              class=\"date-range\"\n            />\n          </div>\n        </div>\n        \n        <el-table\n          :data=\"filteredHistory\"\n          v-loading=\"historyLoading\"\n          empty-text=\"暂无告警历史\"\n          class=\"data-table\"\n          row-class-name=\"table-row\">\n          \n          <el-table-column label=\"告警名称\" prop=\"rule_name\" min-width=\"180\">\n            <template #default=\"scope\">\n              <div class=\"alert-name\">{{ scope.row.rule_name }}</div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"严重程度\" width=\"90\">\n            <template #default=\"scope\">\n              <div class=\"severity-badge\" :class=\"'severity-' + scope.row.severity\">\n                {{ getSeverityText(scope.row.severity) }}\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"触发值\" width=\"120\">\n            <template #default=\"scope\">\n              <div class=\"trigger-value\">\n                {{ scope.row.triggered_value }}{{ getUnitText(scope.row.metric_type) }}\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"状态\" width=\"100\">\n            <template #default=\"scope\">\n              <div class=\"status-badge\" :class=\"'status-' + scope.row.status\">\n                {{ getAlertStatusText(scope.row.status) }}\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"触发时间\" prop=\"triggered_at\" width=\"160\">\n            <template #default=\"scope\">\n              <div class=\"time-display\">\n                <el-icon><Timer /></el-icon>\n                <span>{{ formatTime(scope.row.triggered_at) }}</span>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"持续时间\" width=\"120\">\n            <template #default=\"scope\">\n              <div class=\"duration\">\n                <el-icon><Clock /></el-icon>\n                <span>{{ calculateDuration(scope.row.triggered_at, scope.row.resolved_at) }}</span>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"操作\" width=\"120\">\n            <template #default=\"scope\">\n              <el-button \n                v-if=\"scope.row.status === 'active'\"\n                type=\"success\" \n                size=\"small\" \n                class=\"action-btn confirm-btn\"\n                @click=\"acknowledgeAlert(scope.row)\">\n                <el-icon><Check /></el-icon>\n                确认\n              </el-button>\n              <span v-else class=\"text-muted\">已处理</span>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n\n    <!-- 创建/编辑告警规则对话框 -->\n    <el-dialog\n      v-model=\"showCreateDialog\"\n      :title=\"editingRule ? '编辑告警规则' : '创建告警规则'\"\n      width=\"600px\"\n      destroy-on-close\n      class=\"custom-dialog\"\n      @close=\"resetForm\">\n      \n      <el-form :model=\"ruleForm\" :rules=\"formRules\" ref=\"ruleFormRef\" label-width=\"100px\" class=\"rule-form\">\n        <el-form-item label=\"规则名称\" prop=\"name\">\n          <el-input v-model=\"ruleForm.name\" placeholder=\"请输入规则名称\" />\n        </el-form-item>\n        \n        <el-form-item label=\"监控指标\" prop=\"metric_type\">\n          <el-select v-model=\"ruleForm.metric_type\" placeholder=\"请选择监控指标\" class=\"full-width\">\n            <el-option label=\"平均响应时间\" value=\"avg_response_time\" />\n            <el-option label=\"错误率\" value=\"error_rate\" />\n            <el-option label=\"TPS\" value=\"tps\" />\n            <el-option label=\"CPU使用率\" value=\"cpu_usage\" />\n            <el-option label=\"内存使用率\" value=\"memory_usage\" />\n            <el-option label=\"并发用户数\" value=\"concurrent_users\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"告警条件\" prop=\"condition\">\n          <div class=\"condition-group\">\n            <el-select v-model=\"ruleForm.condition\" placeholder=\"条件\" class=\"condition-select\">\n              <el-option label=\"大于\" value=\"gt\" />\n              <el-option label=\"大于等于\" value=\"gte\" />\n              <el-option label=\"小于\" value=\"lt\" />\n              <el-option label=\"小于等于\" value=\"lte\" />\n              <el-option label=\"等于\" value=\"eq\" />\n            </el-select>\n            <el-input-number \n              v-model=\"ruleForm.threshold\" \n              :min=\"0\"\n              :precision=\"2\"\n              controls-position=\"right\"\n              class=\"threshold-input\"\n              placeholder=\"阈值\">\n            </el-input-number>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"严重程度\" prop=\"severity\">\n          <div class=\"severity-options\">\n            <div \n              v-for=\"level in [\n                {value: 'low', text: '低', color: '#67C23A', bg: '#E1F3D8'},\n                {value: 'medium', text: '中', color: '#3b99e5', bg: '#ECF5FF'},\n                {value: 'high', text: '高', color: '#E6A23C', bg: '#FDF6EC'},\n                {value: 'critical', text: '紧急', color: '#F56C6C', bg: '#FEF0F0'}\n              ]\" \n              :key=\"level.value\"\n              :class=\"['severity-option-new', {'selected': ruleForm.severity === level.value}]\"\n              :style=\"{\n                'border-color': ruleForm.severity === level.value ? level.color : '#DCDFE6',\n                'color': ruleForm.severity === level.value ? level.color : '#606266',\n                'background-color': ruleForm.severity === level.value ? level.bg : 'transparent'\n              }\"\n              @click=\"ruleForm.severity = level.value\">\n              {{ level.text }}\n            </div>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"通知方式\" prop=\"notification_types\">\n          <div class=\"notification-options\">\n            <div \n              v-for=\"type in ['email', 'webhook', 'dingtalk', 'wechat']\" \n              :key=\"type\"\n              :class=\"['notification-option', {'selected': ruleForm.notification_types.includes(type)}]\"\n              @click=\"toggleNotificationType(type)\">\n              <el-icon v-if=\"type === 'email'\"><Message /></el-icon>\n              <el-icon v-else-if=\"type === 'webhook'\"><Connection /></el-icon>\n              <el-icon v-else-if=\"type === 'dingtalk'\"><ChatDotRound /></el-icon>\n              <el-icon v-else-if=\"type === 'wechat'\"><ChatRound /></el-icon>\n              {{ getNotificationTypeText(type) }}\n            </div>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"关联任务\" prop=\"task_ids\">\n          <el-select\n            v-model=\"ruleForm.task_ids\"\n            multiple\n            placeholder=\"不选择则默认关联所有任务\"\n            class=\"full-width\"\n            @change=\"handleTaskSelectionChange\"\n            :loading=\"tasksLoading\">\n            <el-option\n              v-for=\"task in performanceTasks\"\n              :key=\"task.id\"\n              :label=\"task.name\"\n              :value=\"task.id\">\n            </el-option>\n            <template #empty>\n              <div v-if=\"tasksLoading\" class=\"loading-text\">\n                加载任务中...\n              </div>\n              <div v-else-if=\"performanceTasks.length === 0\" class=\"empty-text\">\n                暂无任务数据\n              </div>\n            </template>\n          </el-select>\n          <div class=\"form-help-text\">\n            不选择任务则默认关联所有任务，编辑模式下可保持原有关联关系\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"描述\">\n          <el-input \n            v-model=\"ruleForm.description\" \n            type=\"textarea\" \n            :rows=\"3\"\n            placeholder=\"请输入规则描述\" />\n        </el-form-item>\n        \n        <el-form-item label=\"启用规则\">\n          <div class=\"toggle-switch\">\n            <span :class=\"['toggle-label', {'active': !ruleForm.is_enabled}]\">禁用</span>\n            <el-switch v-model=\"ruleForm.is_enabled\" class=\"big-switch\" />\n            <span :class=\"['toggle-label', {'active': ruleForm.is_enabled}]\">启用</span>\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"showCreateDialog = false\" class=\"cancel-btn\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitLoading\" class=\"submit-btn\">\n            {{ editingRule ? '保存更改' : '创建规则' }}\n          </el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { \n  Plus, Bell, Warning, CircleCloseFilled, CircleCheckFilled, Setting,\n  Edit, Delete, Check, Calendar, Timer, Clock, Message, Connection,\n  ChatDotRound, ChatRound, AlarmClock, Document, Histogram, Search\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'AlertManager',\n  components: {\n    Plus, Bell, Warning, CircleCloseFilled, CircleCheckFilled, Setting,\n    Edit, Delete, Check, Calendar, Timer, Clock, Message, Connection,\n    ChatDotRound, ChatRound, AlarmClock, Document, Histogram, Search\n  },\n  props: {\n    projectId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  emits: ['alert-triggered', 'alert-acknowledged', 'alert-rule-created'],\n  data() {\n    return {\n      activeTab: 'rules',\n      alertRules: [],\n      alertHistory: [],\n      tableLoading: false,\n      historyLoading: false,\n      submitLoading: false,\n      showCreateDialog: false,\n      editingRule: null,\n      alertMonitoringStatus: false,\n      ruleSearchKeyword: '',\n      historyStatusFilter: '',\n      historyDateRange: null,\n      refreshTimer: null,\n      performanceTasks: [], // 性能任务列表\n      tasksLoading: false, // 加载任务状态\n      \n      alertStatistics: {\n        total_alerts: 0,\n        active_alerts: 0,\n        resolved_alerts: 0\n      },\n      \n      ruleForm: {\n        name: '',\n        metric_type: '',\n        condition: '',\n        threshold: 0,\n        severity: '',\n        notification_types: [],\n        description: '',\n        is_enabled: true,\n        task_ids: [] // 关联的任务ID列表\n      },\n      \n      formRules: {\n        name: [\n          { required: true, message: '请输入规则名称', trigger: 'blur' }\n        ],\n        metric_type: [\n          { required: true, message: '请选择监控指标', trigger: 'change' }\n        ],\n        condition: [\n          { required: true, message: '请选择告警条件', trigger: 'change' }\n        ],\n        threshold: [\n          { required: true, message: '请输入阈值', trigger: 'blur' },\n          { type: 'number', message: '阈值必须是数字', trigger: 'blur' }\n        ],\n        severity: [\n          { required: true, message: '请选择严重程度', trigger: 'change' }\n        ],\n        notification_types: [\n          { required: true, message: '请选择通知方式', trigger: 'change' },\n          { type: 'array', min: 1, message: '请至少选择一种通知方式', trigger: 'change' }\n        ],\n        task_ids: [\n          // 删除必填验证，改为非必填\n          { type: 'array', message: '请选择关联任务', trigger: 'change' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.loadAlertRules()\n    this.loadAlertHistory()\n    this.loadAlertStatus()\n    this.loadPerformanceTasks() // 加载性能任务\n    \n    // 设置定时刷新\n    this.refreshTimer = setInterval(() => {\n      if (this.alertMonitoringStatus) {\n        this.loadAlertHistory()\n        this.loadAlertStatus()\n      }\n    }, 30000) // 每30秒刷新一次\n  },\n  beforeUnmount() {\n    // 清除定时器\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n    }\n  },\n  computed: {\n    // 过滤后的规则列表\n    filteredRules() {\n      if (!this.ruleSearchKeyword) {\n        return this.alertRules;\n      }\n      const keyword = this.ruleSearchKeyword.toLowerCase();\n      return this.alertRules.filter(rule => \n        rule.name.toLowerCase().includes(keyword)\n      );\n    },\n    \n    // 过滤后的历史列表\n    filteredHistory() {\n      let result = this.alertHistory;\n      \n      // 状态过滤\n      if (this.historyStatusFilter) {\n        result = result.filter(item => item.status === this.historyStatusFilter);\n      }\n      \n      // 日期范围过滤\n      if (this.historyDateRange && this.historyDateRange.length === 2) {\n        const startDate = new Date(this.historyDateRange[0]);\n        const endDate = new Date(this.historyDateRange[1]);\n        endDate.setHours(23, 59, 59, 999); // 设置为当天结束\n        \n        result = result.filter(item => {\n          const triggerDate = new Date(item.triggered_at);\n          return triggerDate >= startDate && triggerDate <= endDate;\n        });\n      }\n      \n      return result;\n    }\n  },\n  methods: {\n    // 切换通知类型\n    toggleNotificationType(type) {\n      const index = this.ruleForm.notification_types.indexOf(type);\n      if (index > -1) {\n        this.ruleForm.notification_types.splice(index, 1);\n      } else {\n        this.ruleForm.notification_types.push(type);\n      }\n    },\n\n    // 处理任务选择变化\n    handleTaskSelectionChange(value) {\n      this.ruleForm.task_ids = value;\n      console.log('已选择任务:', value);\n    },\n    \n    // 加载告警规则\n    async loadAlertRules() {\n      this.tableLoading = true\n      try {\n        console.log('正在加载告警规则，项目ID:', this.projectId)\n        const response = await this.$api.getAlertRules({ project_id: this.projectId })\n        \n        if (response.status === 200) {\n          this.alertRules = response.data || []\n          console.log('成功加载告警规则:', this.alertRules.length, '条记录')\n        }\n      } catch (error) {\n        console.error('加载告警规则失败:', error)\n        if (error.response?.status === 404) {\n          ElMessage.warning('告警功能暂未配置，请联系管理员')\n        } else {\n          ElMessage.error('加载告警规则失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n        }\n        this.alertRules = []\n      } finally {\n        this.tableLoading = false\n      }\n    },\n    \n    // 加载告警历史\n    async loadAlertHistory() {\n      this.historyLoading = true\n      try {\n        console.log('正在加载告警历史，项目ID:', this.projectId)\n        const response = await this.$api.getAlertHistory({ project_id: this.projectId })\n        \n        if (response.status === 200) {\n          const newAlertHistory = response.data || []\n          console.log('成功加载告警历史:', newAlertHistory.length, '条记录')\n          \n          // 检查是否有新的告警\n          this._checkForNewAlerts(this.alertHistory, newAlertHistory)\n          \n          // 更新告警历史\n          this.alertHistory = newAlertHistory\n        }\n      } catch (error) {\n        console.error('加载告警历史失败:', error)\n        if (error.response?.status === 404) {\n          ElMessage.warning('告警历史暂未配置，请联系管理员')\n        } else {\n          ElMessage.error('加载告警历史失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n        }\n        this.alertHistory = []\n      } finally {\n        this.historyLoading = false\n      }\n    },\n    \n    // 加载告警状态\n    async loadAlertStatus() {\n      try {\n        const response = await this.$api.getAlertStatus({ project_id: this.projectId })\n        \n        if (response.status === 200) {\n          const statistics = response.data.statistics || {}\n          this.alertStatistics = {\n            total_alerts: statistics.total || 0,\n            active_alerts: statistics.active || 0,\n            resolved_alerts: statistics.resolved || 0\n          }\n          this.alertMonitoringStatus = response.data.monitoring_status || false\n          console.log('成功加载告警状态:', this.alertStatistics, '监控状态:', this.alertMonitoringStatus)\n        }\n      } catch (error) {\n        console.error('加载告警状态失败:', error)\n        // 设置默认值\n        this.alertStatistics = {\n          total_alerts: 0,\n          active_alerts: 0,\n          resolved_alerts: 0\n        }\n        this.alertMonitoringStatus = false\n      }\n    },\n\n    // 加载性能任务\n    async loadPerformanceTasks() {\n      this.tasksLoading = true;\n      try {\n        // 获取所有任务，不限于当前项目\n        const response = await this.$api.getPerformanceTasks();\n        if (response.status === 200) {\n          // 处理分页和非分页的数据结构\n          let tasks = response.data;\n          if (response.data && response.data.results) {\n            tasks = response.data.results; // 分页结构\n          }\n          \n          // 将任务数据转换为选项格式，确保显示任务名称\n          this.performanceTasks = tasks.map(task => ({\n            id: task.id,\n            name: task.taskName || `任务 #${task.id}`\n          }));\n          \n          console.log('成功加载性能任务:', this.performanceTasks.length, '条记录');\n        }\n      } catch (error) {\n        console.error('加载性能任务失败:', error);\n        ElMessage.error('加载性能任务失败: ' + (error.response?.data?.message || error.message || '网络错误'));\n        this.performanceTasks = [];\n      } finally {\n        this.tasksLoading = false;\n      }\n    },\n    \n    // 切换告警监控状态\n    async toggleAlertMonitoring() {\n      try {\n        let response\n        if (this.alertMonitoringStatus) {\n          console.log('正在停止告警监控...')\n          response = await this.$api.stopAlertMonitoring()\n        } else {\n          console.log('正在启动告警监控...')\n          response = await this.$api.startAlertMonitoring()\n        }\n        \n        if (response.status === 200) {\n          this.alertMonitoringStatus = !this.alertMonitoringStatus\n          ElMessage.success(this.alertMonitoringStatus ? '告警监控已启动' : '告警监控已停止')\n          \n          // 刷新告警状态\n          this.loadAlertStatus()\n        }\n      } catch (error) {\n        console.error('切换监控状态失败:', error)\n        ElMessage.error('切换监控状态失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n      }\n    },\n    \n    // 切换规则状态\n    async toggleRuleStatus(rule) {\n      try {\n        console.log('切换规则状态:', rule.id, rule.is_enabled)\n        \n        // 由于后端没有完整实现updateAlertRule，我们只做前端状态更新\n        ElMessage.success(rule.is_enabled ? '规则已启用' : '规则已禁用')\n        \n        // 模拟API调用\n        const originalStatus = rule.is_enabled\n        try {\n          const response = await this.$api.updateAlertRule(rule.id, { is_enabled: rule.is_enabled })\n          if (response.status !== 200 && response.status !== 201) {\n            throw new Error('状态更新失败')\n          }\n        } catch (error) {\n          // 如果API调用失败，恢复原始状态\n          rule.is_enabled = !rule.is_enabled\n          throw error\n        }\n      } catch (error) {\n        console.error('更新规则状态失败:', error)\n        ElMessage.error('更新规则状态失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n      }\n    },\n    \n    // 编辑规则\n    editRule(rule) {\n      this.editingRule = rule\n      // 深拷贝规则数据，避免直接修改原始数据\n      this.ruleForm = JSON.parse(JSON.stringify(rule))\n      \n      // 确保任务IDs是数组格式\n      if (typeof this.ruleForm.task_ids === 'string') {\n        try {\n          this.ruleForm.task_ids = JSON.parse(this.ruleForm.task_ids);\n        } catch (e) {\n          console.error('解析任务ID失败:', e);\n          this.ruleForm.task_ids = [];\n        }\n      }\n      \n      // 如果任务ID为null，设置为空数组\n      if (!this.ruleForm.task_ids) {\n        this.ruleForm.task_ids = [];\n      }\n      \n      // 显示编辑对话框\n      this.showCreateDialog = true\n    },\n    \n    // 删除规则\n    async deleteRule(rule) {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除告警规则 \"${rule.name}\" 吗？`,\n          '删除确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n        \n        console.log('正在删除规则:', rule.id)\n        const response = await this.$api.deleteAlertRule(rule.id)\n        \n        if (response.status === 204 || response.status === 200) {\n          ElMessage.success('删除成功')\n          \n          // 手动从列表中移除该规则\n          this.alertRules = this.alertRules.filter(r => r.id !== rule.id)\n          \n          // 更新统计数据\n          if (this.alertStatistics.total_alerts > 0) {\n            this.alertStatistics.total_alerts -= 1\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除规则失败:', error)\n          ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n        }\n      }\n    },\n    \n    // 确认告警\n    async acknowledgeAlert(alert) {\n      try {\n        console.log('正在确认告警:', alert.id)\n        const response = await this.$api.acknowledgeAlert({\n          alert_id: alert.id,\n          acknowledger: 'current_user'\n        })\n        \n        if (response.status === 200) {\n          ElMessage.success('告警已确认')\n          // 刷新告警列表和状态\n          this.loadAlertHistory()\n          this.loadAlertStatus()\n          \n          // 触发事件，通知父组件告警已确认\n          this.$emit('alert-acknowledged', alert)\n        }\n      } catch (error) {\n        console.error('确认告警失败:', error)\n        ElMessage.error('确认告警失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n      }\n    },\n    \n    // 提交表单\n    async submitForm() {\n      try {\n        const valid = await this.$refs.ruleFormRef.validate()\n        if (!valid) return\n        \n        this.submitLoading = true\n        \n        // 构建参数对象\n        const params = {\n          ...this.ruleForm,\n          project_id: this.projectId,\n          // 添加默认的通知配置，避免后端报错\n          notification_config: JSON.stringify({\n            email: {\n              recipients: []\n            },\n            webhook: {\n              url: \"\"\n            }\n          })\n        }\n        \n        // 确保通知类型始终是数组\n        if (!params.notification_types || !Array.isArray(params.notification_types)) {\n          params.notification_types = []\n        }\n        \n        // 确保通知类型至少有一项\n        if (params.notification_types.length === 0) {\n          params.notification_types.push('email')\n        }\n\n        // 确保任务ID始终是数组\n        if (!params.task_ids || !Array.isArray(params.task_ids)) {\n          params.task_ids = [];\n        }\n\n        // 处理任务ID，空数组代表关联所有任务\n        if (params.task_ids.length === 0) {\n          // 添加一个特殊标记，表示关联所有任务\n          params.link_all_tasks = true;\n          console.log('未选择任务，将关联所有任务');\n        } else {\n          params.link_all_tasks = false;\n        }\n        \n        // 调试日志：查看发送的数据\n        console.log('提交告警规则数据:', params)\n        \n        let response\n        if (this.editingRule) {\n          response = await this.$api.updateAlertRule(this.editingRule.id, params)\n        } else {\n          response = await this.$api.addAlertRule(params)\n        }\n        \n        if (response.status === 200 || response.status === 201) {\n          ElMessage.success(this.editingRule ? '更新成功' : '创建成功')\n          this.showCreateDialog = false\n          \n          // 手动更新规则列表\n          if (this.editingRule) {\n            // 更新现有规则\n            const index = this.alertRules.findIndex(r => r.id === this.editingRule.id)\n            if (index !== -1) {\n              // 使用新规则替换旧规则\n              const updatedRule = response.data?.rule || {\n                ...params,\n                id: this.editingRule.id,\n                create_time: this.editingRule.create_time\n              }\n              // Vue 3中直接赋值即可\n              this.alertRules[index] = updatedRule\n            }\n          } else {\n            // 添加新规则\n            const newRule = response.data?.rule || {\n              ...params,\n              id: Date.now(), // 生成临时ID\n              create_time: new Date().toISOString()\n            }\n            this.alertRules.unshift(newRule)\n            \n            // 更新统计数据\n            this.alertStatistics.total_alerts += 1\n            \n            // 如果是新建规则并创建成功，触发事件通知父组件\n            this.$emit('alert-rule-created', newRule)\n          }\n          \n          // 刷新规则列表\n          this.loadAlertRules()\n        }\n      } catch (error) {\n        console.error('提交失败:', error)\n        ElMessage.error('提交失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n      } finally {\n        this.submitLoading = false\n      }\n    },\n    \n    // 重置表单\n    resetForm() {\n      this.editingRule = null\n      this.ruleForm = {\n        name: '',\n        metric_type: '',\n        condition: '',\n        threshold: 0,\n        severity: '',\n        notification_types: [],\n        description: '',\n        is_enabled: true,\n        task_ids: []\n      }\n      this.$refs.ruleFormRef?.resetFields()\n    },\n    \n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '-'\n      return new Date(time).toLocaleString()\n    },\n    \n    // 计算持续时间\n    calculateDuration(startTime, endTime) {\n      if (!startTime) return '-'\n      const start = new Date(startTime)\n      const end = endTime ? new Date(endTime) : new Date()\n      const diff = Math.floor((end - start) / 1000)\n      \n      if (diff < 60) return `${diff}秒`\n      if (diff < 3600) return `${Math.floor(diff / 60)}分钟`\n      return `${Math.floor(diff / 3600)}小时`\n    },\n    \n    // 获取指标类型文本\n    getMetricTypeText(type) {\n      const types = {\n        avg_response_time: '响应时间',\n        error_rate: '错误率',\n        tps: 'TPS',\n        cpu_usage: 'CPU',\n        memory_usage: '内存',\n        concurrent_users: '并发数'\n      }\n      return types[type] || type\n    },\n    \n    // 获取条件文本\n    getConditionText(condition) {\n      const conditions = {\n        gt: '>',\n        gte: '≥',\n        lt: '<',\n        lte: '≤',\n        eq: '='\n      }\n      return conditions[condition] || condition\n    },\n    \n    // 获取单位文本\n    getUnitText(metricType) {\n      const units = {\n        avg_response_time: 'ms',\n        error_rate: '%',\n        tps: '',\n        cpu_usage: '%',\n        memory_usage: '%',\n        concurrent_users: ''\n      }\n      return units[metricType] || ''\n    },\n    \n    // 获取严重程度类型\n    getSeverityType(severity) {\n      const types = {\n        low: 'info',\n        medium: 'warning',\n        high: 'danger',\n        critical: 'danger'\n      }\n      return types[severity] || 'info'\n    },\n    \n    // 获取严重程度文本\n    getSeverityText(severity) {\n      const texts = {\n        low: '低',\n        medium: '中',\n        high: '高',\n        critical: '紧急'\n      }\n      return texts[severity] || severity\n    },\n    \n    // 获取通知类型文本\n    getNotificationTypeText(type) {\n      const types = {\n        email: '邮件',\n        webhook: 'Webhook',\n        dingtalk: '钉钉',\n        wechat: '微信'\n      }\n      return types[type] || type\n    },\n    \n    // 获取告警状态类型\n    getAlertStatusType(status) {\n      const types = {\n        active: 'danger',\n        acknowledged: 'warning',\n        resolved: 'success'\n      }\n      return types[status] || 'info'\n    },\n    \n    // 获取告警状态文本\n    getAlertStatusText(status) {\n      const texts = {\n        active: '活跃',\n        acknowledged: '已确认',\n        resolved: '已解决'\n      }\n      return texts[status] || status\n    },\n    \n    // 检查新的告警\n    _checkForNewAlerts(oldAlerts, newAlerts) {\n      if (!oldAlerts || oldAlerts.length === 0) return\n      \n      // 查找新添加的active告警\n      const newActiveAlerts = newAlerts.filter(alert => \n        alert.status === 'active' && \n        !oldAlerts.some(oldAlert => oldAlert.id === alert.id)\n      )\n      \n      // 触发告警事件\n      newActiveAlerts.forEach(alert => {\n        console.log('检测到新的告警:', alert)\n        this.$emit('alert-triggered', alert)\n      })\n    },\n    \n  }\n}\n</script>\n\n<style scoped>\n/* 全局样式 */\n.alert-manager {\n  padding: 20px;\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\n  color: #333;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 40px);\n}\n\n/* 头部样式 */\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  background: #fff;\n  padding: 20px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n}\n\n.header-left {\n  display: flex;\n  flex-direction: column;\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 4px;\n}\n\n.header-title h1 {\n  font-size: 22px;\n  font-weight: 600;\n  margin: 0 0 0 10px;\n  color: #303133;\n}\n\n.header-title .el-icon {\n  font-size: 24px;\n  color: #409EFF;\n}\n\n.header-subtitle {\n  font-size: 14px;\n  color: #909399;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n}\n\n.monitor-toggle-btn {\n  background: #f0f2f5;\n  border: none;\n  color: #606266;\n  padding: 10px 16px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.monitor-toggle-btn.is-active {\n  background: #ecf5ff;\n  color: #409EFF;\n}\n\n.monitor-toggle-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.add-rule-btn {\n  background: #409EFF;\n  color: white;\n  border: none;\n  padding: 10px 16px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.add-rule-btn:hover {\n  background: #66b1ff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n}\n\n/* 状态卡片样式 */\n.status-cards {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20px;\n  margin-bottom: 24px;\n}\n\n.status-card {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  cursor: pointer;\n}\n\n.status-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\n}\n\n.status-card.total-alerts {\n  border-top: 4px solid #E6A23C;\n}\n\n.status-card.active-alerts {\n  border-top: 4px solid #F56C6C;\n}\n\n.status-card.resolved-alerts {\n  border-top: 4px solid #67C23A;\n}\n\n.status-card.rules-count {\n  border-top: 4px solid #409EFF;\n}\n\n.card-content {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n}\n\n.card-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  margin-right: 16px;\n  transition: all 0.3s ease;\n}\n\n.total-alerts .card-icon {\n  background-color: rgba(230, 162, 60, 0.1);\n  color: #E6A23C;\n}\n\n.active-alerts .card-icon {\n  background-color: rgba(245, 108, 108, 0.1);\n  color: #F56C6C;\n}\n\n.resolved-alerts .card-icon {\n  background-color: rgba(103, 194, 58, 0.1);\n  color: #67C23A;\n}\n\n.rules-count .card-icon {\n  background-color: rgba(64, 158, 255, 0.1);\n  color: #409EFF;\n}\n\n.status-card:hover .card-icon {\n  transform: scale(1.1) rotate(5deg);\n}\n\n.card-data {\n  flex: 1;\n}\n\n.card-value {\n  font-size: 28px;\n  font-weight: 700;\n  line-height: 1.2;\n  color: #303133;\n  margin-bottom: 6px;\n}\n\n.card-label {\n  font-size: 14px;\n  color: #909399;\n  font-weight: 500;\n}\n\n/* 主内容区域 */\n.main-content {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n}\n\n/* 标签切换 */\n.content-tabs {\n  display: flex;\n  border-bottom: 1px solid #ebeef5;\n  background: #fff;\n}\n\n.tab-item {\n  padding: 16px 24px;\n  font-size: 16px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  transition: all 0.3s ease;\n  color: #606266;\n  border-bottom: 2px solid transparent;\n}\n\n.tab-item:hover {\n  color: #409EFF;\n}\n\n.tab-item.active {\n  color: #409EFF;\n  border-bottom: 2px solid #409EFF;\n  font-weight: 500;\n}\n\n/* 内容面板 */\n.content-panel {\n  padding: 20px;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.panel-header h2 {\n  font-size: 18px;\n  font-weight: 600;\n  margin: 0;\n  color: #303133;\n}\n\n.search-input {\n  width: 240px;\n}\n\n.filter-group {\n  display: flex;\n  gap: 12px;\n}\n\n.filter-select {\n  width: 120px;\n}\n\n.date-range {\n  width: 320px;\n}\n\n/* 表格样式 */\n.data-table {\n  width: 100%;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 20px;\n}\n\n.table-row {\n  transition: all 0.2s ease;\n}\n\n.table-row:hover {\n  background-color: #f5f7fa;\n}\n\n/* 表格内容样式 */\n.rule-name {\n  font-weight: 500;\n  color: #303133;\n}\n\n.metric-tag {\n  font-weight: 500;\n}\n\n.condition-text {\n  font-family: 'Courier New', monospace;\n  font-weight: 600;\n  color: #606266;\n  background: #f5f7fa;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n\n.severity-badge {\n  display: inline-block;\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 600;\n  text-align: center;\n}\n\n.severity-low {\n  background-color: #e1f3d8;\n  color: #67C23A;\n}\n\n.severity-medium {\n  background-color: #ecf5ff;\n  color: #3b99e5;\n}\n\n.severity-high {\n  background-color: #fdf6ec;\n  color: #E6A23C;\n}\n\n.severity-critical {\n  background-color: #fef0f0;\n  color: #F56C6C;\n}\n\n.status-badge {\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 500;\n  text-align: center;\n}\n\n.status-active {\n  background-color: #fef0f0;\n  color: #F56C6C;\n}\n\n.status-acknowledged {\n  background-color: #fdf6ec;\n  color: #E6A23C;\n}\n\n.status-resolved {\n  background-color: #e1f3d8;\n  color: #67C23A;\n}\n\n.notification-types {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 5px;\n}\n\n.notification-tag {\n  background: #f0f2f5;\n  border: none;\n  color: #606266;\n}\n\n.time-display, .duration {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  color: #606266;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.action-btn {\n  border: none;\n  border-radius: 4px;\n  transition: all 0.2s ease;\n}\n\n.edit-btn {\n  background: #ecf5ff;\n  color: #409EFF;\n}\n\n.delete-btn {\n  background: #fef0f0;\n  color: #F56C6C;\n}\n\n.confirm-btn {\n  background: #f0f9eb;\n  color: #67C23A;\n}\n\n.action-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.text-muted {\n  color: #909399;\n  font-style: italic;\n}\n\n/* 对话框样式 */\n.custom-dialog {\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.rule-form {\n  padding: 20px 0;\n}\n\n.full-width {\n  width: 100%;\n}\n\n.condition-group {\n  display: flex;\n  gap: 12px;\n}\n\n.condition-select {\n  width: 120px;\n}\n\n.threshold-input {\n  flex: 1;\n}\n\n.severity-options {\n  display: flex;\n  gap: 16px;\n  margin-top: 8px;\n}\n\n.severity-option-new {\n  flex: 1;\n  padding: 16px 0;\n  border-radius: 8px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid;\n  font-weight: 600;\n  font-size: 16px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: hidden;\n}\n\n.severity-option-new:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);\n}\n\n.severity-option-new::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 6px;\n  background-color: currentColor;\n  opacity: 0.6;\n}\n\n.severity-option-new.selected {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);\n  border-width: 3px;\n}\n\n.notification-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.notification-option {\n  padding: 8px 12px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border: 1px solid #dcdfe6;\n  color: #606266;\n}\n\n.notification-option.selected {\n  background-color: #ecf5ff;\n  border-color: #409EFF;\n  color: #409EFF;\n}\n\n.toggle-switch {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.toggle-label {\n  color: #909399;\n  transition: all 0.2s ease;\n}\n\n.toggle-label.active {\n  color: #303133;\n  font-weight: 500;\n}\n\n.big-switch {\n  transform: scale(1.2);\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding-top: 20px;\n}\n\n.cancel-btn {\n  border: none;\n  background: #f0f2f5;\n}\n\n.submit-btn {\n  border: none;\n  background: #409EFF;\n  padding: 10px 24px;\n  transition: all 0.3s ease;\n}\n\n.submit-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .status-cards {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n  .dashboard-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 16px;\n  }\n  \n  .header-actions {\n    width: 100%;\n  }\n  \n  .status-cards {\n    grid-template-columns: 1fr;\n  }\n  \n  .panel-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12px;\n  }\n  \n  .search-input,\n  .filter-select,\n  .date-range {\n    width: 100%;\n  }\n  \n  .filter-group {\n    width: 100%;\n    flex-direction: column;\n  }\n  \n  .severity-options,\n  .notification-options {\n    flex-direction: column;\n  }\n}\n\n.form-help-text {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 5px;\n  font-style: italic;\n}\n\n.loading-text, .empty-text {\n  text-align: center;\n  padding: 12px 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.loading-text {\n  color: #409EFF;\n}\n</style>", "import { render } from \"./AlertManager.vue?vue&type=template&id=5ef97f23&scoped=true\"\nimport script from \"./AlertManager.vue?vue&type=script&lang=js\"\nexport * from \"./AlertManager.vue?vue&type=script&lang=js\"\n\nimport \"./AlertManager.vue?vue&type=style&index=0&id=5ef97f23&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-5ef97f23\"]])\n\nexport default __exports__", "import { render } from \"./PerformanceAlert.vue?vue&type=template&id=eae3dc1a&scoped=true\"\nimport script from \"./PerformanceAlert.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceAlert.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceAlert.vue?vue&type=style&index=0&id=eae3dc1a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-eae3dc1a\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_ctx", "pro", "id", "_createBlock", "_component_AlertManager", "onAlertTriggered", "$options", "handleAlertTriggered", "onAlertAcknowledged", "handleAlertAcknowledged", "onAlertRuleCreated", "handleAlertRuleCreated", "_hoisted_2", "_createVNode", "_component_el_alert", "title", "type", "description", "closable", "_createElementVNode", "_hoisted_3", "_hoisted_4", "_component_el_icon", "_component_AlarmClock", "_hoisted_5", "_component_el_button", "_normalizeClass", "$data", "alertMonitoringStatus", "onClick", "toggleAlertMonitoring", "_component_Bell", "_toDisplayString", "_cache", "$event", "showCreateDialog", "_component_Plus", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_component_Warning", "_hoisted_10", "_hoisted_11", "alertStatistics", "total_alerts", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_component_CircleCloseFilled", "_hoisted_15", "_hoisted_16", "active_alerts", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_component_CircleCheckFilled", "_hoisted_20", "_hoisted_21", "resolved_alerts", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_component_Setting", "_hoisted_25", "_hoisted_26", "alertRules", "length", "_hoisted_27", "_hoisted_28", "activeTab", "_component_Document", "_component_Histogram", "_hoisted_29", "_hoisted_30", "_component_el_input", "ruleSearchKeyword", "placeholder", "clearable", "_component_el_table", "data", "filteredRules", "_component_el_table_column", "label", "prop", "default", "_withCtx", "scope", "_hoisted_31", "_hoisted_32", "row", "name", "width", "_component_el_tag", "size", "effect", "getMetricTypeText", "metric_type", "_hoisted_33", "getConditionText", "condition", "threshold", "getUnitText", "severity", "getSeverityText", "_hoisted_34", "_Fragment", "_renderList", "notification_types", "key", "getNotificationTypeText", "_component_el_switch", "is_enabled", "onChange", "toggleRuleStatus", "_hoisted_35", "_component_Calendar", "formatTime", "create_time", "fixed", "_hoisted_36", "editRule", "_component_Edit", "deleteRule", "_component_Delete", "tableLoading", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_component_el_select", "historyStatus<PERSON><PERSON>er", "_component_el_option", "value", "_component_el_date_picker", "historyDateRange", "filteredHistory", "_hoisted_40", "rule_name", "_hoisted_41", "triggered_value", "status", "getAlertStatusText", "_hoisted_42", "_component_Timer", "triggered_at", "_hoisted_43", "_component_Clock", "calculateDuration", "resolved_at", "<PERSON><PERSON><PERSON><PERSON>", "_component_Check", "_hoisted_44", "historyLoading", "_component_el_dialog", "editingRule", "onClose", "resetForm", "footer", "_hoisted_53", "submitForm", "loading", "submitLoading", "_component_el_form", "model", "ruleForm", "rules", "formRules", "ref", "_component_el_form_item", "_hoisted_45", "_component_el_input_number", "min", "precision", "_hoisted_46", "level", "style", "_normalizeStyle", "color", "bg", "text", "_hoisted_47", "_hoisted_48", "includes", "toggleNotificationType", "_component_Message", "_component_Connection", "_component_ChatDotRound", "_component_ChatRound", "task_ids", "multiple", "handleTaskSelectionChange", "tasksLoading", "empty", "_hoisted_50", "performanceTasks", "_hoisted_51", "task", "rows", "_hoisted_52", "components", "Plus", "Bell", "Warning", "CircleCloseFilled", "CircleCheckFilled", "Setting", "Edit", "Delete", "Check", "Calendar", "Timer", "Clock", "Message", "Connection", "ChatDotRound", "ChatRound", "AlarmClock", "Document", "Histogram", "Search", "props", "projectId", "String", "Number", "required", "emits", "alertHistory", "refreshTimer", "message", "trigger", "mounted", "this", "loadAlertRules", "loadAlertHistory", "loadAlertStatus", "loadPerformanceTasks", "setInterval", "beforeUnmount", "clearInterval", "computed", "keyword", "toLowerCase", "filter", "rule", "result", "item", "startDate", "Date", "endDate", "setHours", "triggerDate", "methods", "index", "indexOf", "splice", "push", "console", "log", "response", "$api", "getAlertRules", "project_id", "error", "ElMessage", "warning", "getAlertHistory", "newAlertHistory", "_checkForNewAlerts", "getAlertStatus", "statistics", "total", "active", "resolved", "monitoring_status", "getPerformanceTasks", "tasks", "results", "map", "taskName", "stopAlertMonitoring", "startAlertMonitoring", "success", "updateAlertRule", "Error", "JSON", "parse", "stringify", "e", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "deleteAlertRule", "r", "alert", "alert_id", "acknowledger", "$emit", "valid", "$refs", "ruleFormRef", "validate", "params", "notification_config", "email", "recipients", "webhook", "url", "Array", "isArray", "link_all_tasks", "addAlertRule", "findIndex", "updatedRule", "newRule", "now", "toISOString", "unshift", "resetFields", "time", "toLocaleString", "startTime", "endTime", "start", "end", "diff", "Math", "floor", "types", "avg_response_time", "error_rate", "tps", "cpu_usage", "memory_usage", "concurrent_users", "conditions", "gt", "gte", "lt", "lte", "eq", "metricType", "units", "getSeverityType", "low", "medium", "high", "critical", "texts", "<PERSON><PERSON><PERSON>", "wechat", "getAlertStatusType", "acknowledged", "old<PERSON><PERSON><PERSON>", "new<PERSON><PERSON><PERSON>", "newActiveAlerts", "some", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "__exports__", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapState", "state", "created", "alertData", "ElNotification", "duration", "position", "ruleData", "render"], "sourceRoot": ""}