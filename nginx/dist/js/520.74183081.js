"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[57,520],{68774:function(e,t,l){l.r(t),l.d(t,{default:function(){return il}});var a=l(56768),s=l(45130),n=l(24232);const o={class:"box"},i={class:"task-header"},d={class:"task-info"},r={class:"task-navigation"},c={class:"task-title"},u={class:"task-details"},p={class:"detail-item"},f={class:"detail-value"},m={class:"detail-item"},h={class:"detail-value"},g={class:"detail-item"},k={class:"detail-value"},b={class:"detail-item description"},_={class:"detail-value"},v={class:"task-actions"},y={class:"tree-component"},F={class:"search-container"},C={class:"bold-node"},x={class:"node-content"},V={class:"tree-actions"},w={class:"title"},I={style:{display:"flex","justify-content":"space-between","align-items":"center"}},T={class:"action-buttons-group"},L={class:"env-selector"},S={class:"dialog-footer"},$={class:"operation-buttons"},D={class:"dialog-footer"},E={class:"dialog-footer"},U={class:"custom-tree-node"},W={class:"custom-tree-node"},z={class:"add-btns"};function M(e,t,l,M,N,R){const X=(0,a.g2)("CaretLeft"),O=(0,a.g2)("el-icon"),A=(0,a.g2)("el-button"),P=(0,a.g2)("Edit"),q=(0,a.g2)("CaretRight"),j=(0,a.g2)("el-card"),Q=(0,a.g2)("el-input"),B=(0,a.g2)("Plus"),H=(0,a.g2)("el-scrollbar"),J=(0,a.g2)("Delete"),K=(0,a.g2)("el-tree"),Y=(0,a.g2)("el-col"),G=(0,a.g2)("View"),Z=(0,a.g2)("el-tooltip"),ee=(0,a.g2)("el-option"),te=(0,a.g2)("el-select"),le=(0,a.g2)("el-tag"),ae=(0,a.g2)("el-descriptions-item"),se=(0,a.g2)("el-descriptions"),ne=(0,a.g2)("el-dialog"),oe=(0,a.g2)("arrow-down"),ie=(0,a.g2)("Remove"),de=(0,a.g2)("el-dropdown-item"),re=(0,a.g2)("SuccessFilled"),ce=(0,a.g2)("CircleClose"),ue=(0,a.g2)("el-dropdown-menu"),pe=(0,a.g2)("el-dropdown"),fe=(0,a.g2)("Refresh"),me=(0,a.g2)("Document"),he=(0,a.g2)("VideoPlay"),ge=(0,a.g2)("perfStep"),ke=(0,a.g2)("configuration"),be=(0,a.g2)("el-row"),_e=(0,a.g2)("el-form-item"),ve=(0,a.g2)("el-form"),ye=(0,a.g2)("el-checkbox"),Fe=(0,a.g2)("el-tabs"),Ce=(0,a.g2)("el-drawer");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.Lk)("div",o,[(0,a.bF)(j,{class:"task-card"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",i,[(0,a.Lk)("div",d,[(0,a.Lk)("div",r,[(0,a.bF)(A,{class:"back-button",type:"text",onClick:R.back},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(X)]),_:1}),t[23]||(t[23]=(0,a.eW)("返回 "))]),_:1,__:[23]},8,["onClick"]),(0,a.Lk)("div",c,[t[24]||(t[24]=(0,a.Lk)("span",{class:"title-label"},"任务管理 /",-1)),(0,a.bF)(A,{class:"task-name-button",type:"text",onClick:[t[0]||(t[0]=e=>R.popup("task-edit")),t[1]||(t[1]=(0,s.D$)(()=>{},["stop"]))]},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(e.perfTask.taskName)+" ",1),(0,a.bF)(O,{class:"edit-icon"},{default:(0,a.k6)(()=>[(0,a.bF)(P)]),_:1})]),_:1}),(0,a.bF)(A,{class:"task-type-tag",type:"20"===e.perfTask.taskType.toString()?"warning":"primary"},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(N.taskTypeMap[e.perfTask.taskType.toString()]||e.perfTask.taskType),1)]),_:1},8,["type"])])]),(0,a.Lk)("div",u,[(0,a.Lk)("div",p,[t[25]||(t[25]=(0,a.Lk)("span",{class:"detail-label"},"创建人:",-1)),(0,a.Lk)("span",f,(0,n.v_)(e.perfTask.creator),1)]),(0,a.Lk)("div",m,[t[26]||(t[26]=(0,a.Lk)("span",{class:"detail-label"},"创建时间:",-1)),(0,a.Lk)("span",h,(0,n.v_)(e.$tools.rTime(e.perfTask.create_time)),1)]),(0,a.Lk)("div",g,[t[27]||(t[27]=(0,a.Lk)("span",{class:"detail-label"},"最后修改:",-1)),(0,a.Lk)("span",k,(0,n.v_)(e.$tools.rTime(e.perfTask.update_time)),1)]),(0,a.Lk)("div",b,[t[28]||(t[28]=(0,a.Lk)("span",{class:"detail-label"},"任务描述:",-1)),(0,a.Lk)("span",_,(0,n.v_)(e.perfTask.desc||"暂无描述"),1)])])]),(0,a.Lk)("div",v,[(0,a.bF)(A,{onClick:R.clickRun,type:"success",class:"run-task-button"},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(q)]),_:1}),t[29]||(t[29]=(0,a.eW)("执行任务 "))]),_:1,__:[29]},8,["onClick"])])])]),_:1}),(0,a.bF)(be,{gutter:2,style:{"justify-content":"flex-end"}},{default:(0,a.k6)(()=>[(0,a.bF)(Y,{span:4},{default:(0,a.k6)(()=>[(0,a.Lk)("div",y,[(0,a.Lk)("div",F,[(0,a.bF)(Q,{modelValue:N.filterText,"onUpdate:modelValue":t[2]||(t[2]=e=>N.filterText=e),placeholder:"请输入场景名称搜索",clearable:""},{append:(0,a.k6)(()=>[(0,a.bF)(A,{type:"primary",onClick:R.searchClick},{default:(0,a.k6)(()=>t[30]||(t[30]=[(0,a.eW)("查询")])),_:1,__:[30]},8,["onClick"])]),_:1},8,["modelValue"])]),(0,a.bF)(A,{type:"primary",class:"add-scene-button",onClick:t[3]||(t[3]=e=>R.popup("add"))},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(B)]),_:1}),t[31]||(t[31]=(0,a.eW)("添加场景 "))]),_:1,__:[31]}),(0,a.bF)(H,{height:"calc(100vh - 265px)"},{default:(0,a.k6)(()=>[N.scenceId?((0,a.uX)(),(0,a.Wv)(K,{key:0,"node-key":"id","current-node-key":N.scenceId,class:"filter-tree",data:N.sceneList,props:R.defaultProps,"default-expand-all":"","expand-on-click-node":!1,onNodeClick:R.handleNodeClick},{default:(0,a.k6)(({node:e,data:t})=>[(0,a.bF)(H,null,{default:(0,a.k6)(()=>[(0,a.Lk)("span",C,(0,n.v_)(e.label),1)]),_:2},1024),(0,a.Lk)("div",x,[(0,a.Lk)("span",V,[(0,a.bF)(A,{type:"primary",size:"small",circle:"",class:"tree-action-btn edit-btn",onClick:t=>R.popup("edit",e.data)},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(P)]),_:1})]),_:2},1032,["onClick"]),(0,a.bF)(A,{type:"danger",size:"small",circle:"",class:"tree-action-btn delete-btn",onClick:t=>R.delScene(e.data.id)},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(J)]),_:1})]),_:2},1032,["onClick"])])])]),_:1},8,["current-node-key","data","props","onNodeClick"])):(0,a.Q3)("",!0)]),_:1})])]),_:1}),(0,a.bF)(Y,{span:14,style:{background:"#ffffff"}},{default:(0,a.k6)(()=>[(0,a.Lk)("div",w,[(0,a.Lk)("div",I,[t[43]||(t[43]=(0,a.Lk)("span",null,"场景步骤",-1)),(0,a.Lk)("div",T,[(0,a.Lk)("div",L,[N.scenceData.env?((0,a.uX)(),(0,a.Wv)(Z,{key:0,class:"box-item",effect:"dark",content:"查看环境信息",placement:"top"},{default:(0,a.k6)(()=>[(0,a.bF)(A,{class:"icon-button",onClick:R.clickShowEnv},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(G)]),_:1})]),_:1},8,["onClick"])]),_:1})):(0,a.Q3)("",!0),(0,a.bF)(te,{clearable:"",modelValue:N.scenceData.env,"onUpdate:modelValue":t[4]||(t[4]=e=>N.scenceData.env=e),placeholder:"选择环境",style:{width:"180px"},"no-data-text":"暂无数据",size:"small",class:"env-select"},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(e.testEnvs,e=>((0,a.uX)(),(0,a.Wv)(ee,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),(0,a.bF)(ne,{modelValue:N.showEnv,"onUpdate:modelValue":t[7]||(t[7]=e=>N.showEnv=e),title:"环境变量",top:"10px",width:"70%"},{footer:(0,a.k6)(()=>[(0,a.Lk)("span",S,[(0,a.bF)(A,{onClick:t[5]||(t[5]=e=>R.editEnv(N.envInfo)),type:"success",plain:""},{default:(0,a.k6)(()=>t[34]||(t[34]=[(0,a.eW)("编辑")])),_:1,__:[34]}),(0,a.bF)(A,{onClick:t[6]||(t[6]=e=>N.showEnv=!1)},{default:(0,a.k6)(()=>t[35]||(t[35]=[(0,a.eW)("关闭")])),_:1,__:[35]})])]),default:(0,a.k6)(()=>[(0,a.bF)(se,{border:"",column:1,"label-align":""},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(N.envInfo.debug_global_variable,(e,l)=>((0,a.uX)(),(0,a.Wv)(ae,{label:l},{label:(0,a.k6)(()=>[(0,a.bF)(le,{color:"#E6A23C"},{default:(0,a.k6)(()=>t[32]||(t[32]=[(0,a.eW)("debug")])),_:1,__:[32]}),(0,a.eW)(" "+(0,n.v_)(l),1)]),default:(0,a.k6)(()=>[(0,a.eW)(" "+(0,n.v_)(e),1)]),_:2},1032,["label"]))),256)),((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(N.envInfo.global_variable,(e,l)=>((0,a.uX)(),(0,a.Wv)(ae,{label:l},{label:(0,a.k6)(()=>[(0,a.bF)(le,{color:"#67C23AFF"},{default:(0,a.k6)(()=>t[33]||(t[33]=[(0,a.eW)("global")])),_:1,__:[33]}),(0,a.eW)(" "+(0,n.v_)(l),1)]),default:(0,a.k6)(()=>[(0,a.eW)(" "+(0,n.v_)(e),1)]),_:2},1032,["label"]))),256))]),_:1})]),_:1},8,["modelValue"])]),(0,a.Lk)("div",$,[(0,a.bF)(pe,{trigger:"click",placement:"bottom-end"},{dropdown:(0,a.k6)(()=>[(0,a.bF)(ue,null,{default:(0,a.k6)(()=>[(0,a.bF)(de,{command:"批量禁用",onClick:t[8]||(t[8]=e=>R.clickOts("stop"))},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(ie)]),_:1}),t[37]||(t[37]=(0,a.eW)(" 批量禁用 "))]),_:1,__:[37]}),(0,a.bF)(de,{command:"批量启用",onClick:t[9]||(t[9]=e=>R.clickOts("start"))},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(re)]),_:1}),t[38]||(t[38]=(0,a.eW)(" 批量启用 "))]),_:1,__:[38]}),(0,a.bF)(de,{command:"批量删除",onClick:t[10]||(t[10]=e=>R.clickOts("delete"))},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(ce)]),_:1}),t[39]||(t[39]=(0,a.eW)(" 批量删除 "))]),_:1,__:[39]})]),_:1})]),default:(0,a.k6)(()=>[(0,a.bF)(A,{type:"info",size:"small",class:"action-button batch-button",style:{"margin-right":"12px"}},{default:(0,a.k6)(()=>[t[36]||(t[36]=(0,a.Lk)("span",null,"批量操作",-1)),(0,a.bF)(O,{class:"el-icon--right"},{default:(0,a.k6)(()=>[(0,a.bF)(oe)]),_:1})]),_:1,__:[36]})]),_:1}),(0,a.bF)(A,{type:"warning",size:"small",class:"action-button",onClick:t[11]||(t[11]=e=>R.clickOts("sync"))},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(fe)]),_:1}),t[40]||(t[40]=(0,a.Lk)("span",null,"同步接口",-1))]),_:1,__:[40]}),(0,a.bF)(A,{type:"primary",size:"small",class:"action-button",onClick:R.clickScenceStep},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(me)]),_:1}),t[41]||(t[41]=(0,a.Lk)("span",null,"保存",-1))]),_:1,__:[41]},8,["onClick"]),(0,a.bF)(A,{type:"success",size:"small",class:"action-button",onClick:R.debugScence},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(he)]),_:1}),t[42]||(t[42]=(0,a.Lk)("span",null,"调试",-1))]),_:1,__:[42]},8,["onClick"])])])])]),(0,a.bF)(ge,{scenceId:N.scenceId,steps:N.steps,scenceData:N.scenceData,onFetchSteps:R.getTaskScenceStep},null,8,["scenceId","steps","scenceData","onFetchSteps"])]),_:1}),(0,a.bF)(Y,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(ke)]),_:1})]),_:1})]),(0,a.bF)(ne,{title:N.dialogTitle,modelValue:N.dialogVisible,"onUpdate:modelValue":t[13]||(t[13]=e=>N.dialogVisible=e),width:"30%","custom-class":"class_dialog",required:!0,style:{"text-align":"left"},"before-close":R.clearValidation},{footer:(0,a.k6)(()=>[(0,a.Lk)("span",D,[(0,a.bF)(A,{onClick:R.clearValidation},{default:(0,a.k6)(()=>t[44]||(t[44]=[(0,a.eW)("取消")])),_:1,__:[44]},8,["onClick"]),"add"===N.dialogType?((0,a.uX)(),(0,a.Wv)(A,{key:0,type:"primary",onClick:R.addScene},{default:(0,a.k6)(()=>t[45]||(t[45]=[(0,a.eW)("确定")])),_:1,__:[45]},8,["onClick"])):((0,a.uX)(),(0,a.Wv)(A,{key:1,type:"primary",onClick:R.editScene},{default:(0,a.k6)(()=>t[46]||(t[46]=[(0,a.eW)("确定")])),_:1,__:[46]},8,["onClick"]))])]),default:(0,a.k6)(()=>[(0,a.bF)(ve,{model:N.sceneForm,rules:e.rulesPerf,ref:"perfRef"},{default:(0,a.k6)(()=>[(0,a.bF)(_e,{label:"场景名称",prop:"name"},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{modelValue:N.sceneForm.name,"onUpdate:modelValue":t[12]||(t[12]=e=>N.sceneForm.name=e),maxlength:"50",placeholder:"请输入场景名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","before-close"]),(0,a.bF)(ne,{title:N.dialogTitle,modelValue:N.dialogVisible1,"onUpdate:modelValue":t[16]||(t[16]=e=>N.dialogVisible1=e),width:"30%","custom-class":"class_dialog",required:!0,style:{"text-align":"left"},"before-close":R.clearValidation},{footer:(0,a.k6)(()=>[(0,a.Lk)("span",E,[(0,a.bF)(A,{onClick:R.clearValidation},{default:(0,a.k6)(()=>t[47]||(t[47]=[(0,a.eW)("取消")])),_:1,__:[47]},8,["onClick"]),(0,a.bF)(A,{type:"primary",onClick:R.editTask},{default:(0,a.k6)(()=>t[48]||(t[48]=[(0,a.eW)("确定")])),_:1,__:[48]},8,["onClick"])])]),default:(0,a.k6)(()=>[(0,a.bF)(ve,{model:N.taskForm,rules:e.rulesPerf,ref:"perfRef"},{default:(0,a.k6)(()=>[(0,a.bF)(_e,{label:"任务名称",prop:"taskName"},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{modelValue:N.taskForm.taskName,"onUpdate:modelValue":t[14]||(t[14]=e=>N.taskForm.taskName=e),maxlength:"50",placeholder:"请输入任务名称"},null,8,["modelValue"])]),_:1}),(0,a.bF)(_e,{label:"任务描述",prop:"desc"},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{type:"textarea",modelValue:N.taskForm.desc,"onUpdate:modelValue":t[15]||(t[15]=e=>N.taskForm.desc=e),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","before-close"]),(0,a.bF)(Ce,{modelValue:N.otsDlg,"onUpdate:modelValue":t[22]||(t[22]=e=>N.otsDlg=e),title:N.titleOts,"destroy-on-close":!0,"show-close":!1,onClose:R.handleClose,size:"23%"},{default:(0,a.k6)(()=>[(0,a.bF)(Fe,{type:"card",style:{"margin-left":"10px"}},{default:(0,a.k6)(()=>[(0,a.Lk)("div",null,[(0,a.bF)(ye,{size:"mini",indeterminate:N.isIndeterminate,modelValue:N.new_task_form.case_checkAll,"onUpdate:modelValue":t[17]||(t[17]=e=>N.new_task_form.case_checkAll=e),onChange:R.handleCheckAllChange,style:{padding:"0px","margin-right":"5px"}},{default:(0,a.k6)(()=>t[49]||(t[49]=[(0,a.eW)("全选")])),_:1,__:[49]},8,["indeterminate","modelValue","onChange"])]),(0,a.bF)(H,{height:"calc(100vh - 160px)"},{default:(0,a.k6)(()=>["sync"!==N.typeOts?((0,a.uX)(),(0,a.Wv)(K,{key:0,ref:"casetree",data:N.steps,"show-checkbox":"",props:R.defaultProps,onCheckChange:R.case_check_change,"node-key":"id","default-expand-all":!1,"highlight-current":"","empty-text":"暂无数据"},{default:(0,a.k6)(({node:e,data:t})=>[(0,a.Lk)("span",U,[(0,a.Lk)("div",null,[(0,a.bF)(O,{class:"step-icon",style:{color:"#909399"}},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(R.getCardIndex(e.parent,e)),1)]),_:2},1024),(0,a.eW)(" "+(0,n.v_)(t.stepInfo.name),1)])])]),_:1},8,["data","props","onCheckChange"])):((0,a.uX)(),(0,a.Wv)(K,{key:1,ref:"casetree",data:N.steps,"show-checkbox":"",props:R.defaultProps,onCheckChange:R.case_check_change,"node-key":"id","default-expand-all":!1,"highlight-current":"","empty-text":"暂无数据"},{default:(0,a.k6)(({node:e,data:t})=>[(0,a.Lk)("span",W,[(0,a.Lk)("div",null,[(0,a.bF)(O,{class:"step-icon",style:{color:"#909399"}},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(R.getCardIndex(e.parent,e)),1)]),_:2},1024),(0,a.eW)(" "+(0,n.v_)(t.content?.name||"未定义")+" "+(0,n.v_)(t.content?.url||"未定义"),1)])])]),_:1},8,["data","props","onCheckChange"]))]),_:1})]),_:1}),(0,a.Lk)("div",z,["start"===N.typeOts?((0,a.uX)(),(0,a.Wv)(A,{key:0,class:"drawer-action-btn enable-btn",size:"default",onClick:t[18]||(t[18]=e=>R.makeOts(N.typeOts))},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(re)]),_:1}),t[50]||(t[50]=(0,a.eW)("确认启用 "))]),_:1,__:[50]})):(0,a.Q3)("",!0),"stop"===N.typeOts?((0,a.uX)(),(0,a.Wv)(A,{key:1,class:"drawer-action-btn disable-btn",size:"default",onClick:t[19]||(t[19]=e=>R.makeOts(N.typeOts))},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(ie)]),_:1}),t[51]||(t[51]=(0,a.eW)("确认禁用 "))]),_:1,__:[51]})):(0,a.Q3)("",!0),"delete"===N.typeOts?((0,a.uX)(),(0,a.Wv)(A,{key:2,class:"drawer-action-btn delete-btn",size:"default",onClick:t[20]||(t[20]=e=>R.makeOts(N.typeOts))},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(ce)]),_:1}),t[52]||(t[52]=(0,a.eW)("确认删除 "))]),_:1,__:[52]})):(0,a.Q3)("",!0),"sync"===N.typeOts?((0,a.uX)(),(0,a.Wv)(A,{key:3,class:"drawer-action-btn sync-btn",size:"default",onClick:t[21]||(t[21]=e=>R.makeOts(N.typeOts))},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(fe)]),_:1}),t[53]||(t[53]=(0,a.eW)("确认同步 "))]),_:1,__:[53]})):(0,a.Q3)("",!0),(0,a.bF)(A,{class:"drawer-action-btn cancel-btn",size:"default",onClick:R.handleClose},{default:(0,a.k6)(()=>[(0,a.bF)(O,null,{default:(0,a.k6)(()=>[(0,a.bF)(ce)]),_:1}),t[54]||(t[54]=(0,a.eW)("关闭窗口 "))]),_:1,__:[54]},8,["onClick"])])]),_:1},8,["modelValue","title","onClose"])],64)}l(44114),l(18111),l(22489),l(20116),l(7588),l(61701);var N=l(60782),R=l(93851);const X={class:"page-container"},O={class:"page-header-card"},A={class:"header-content"},P={class:"action-toolbar"},q={class:"weight-settings"},j={slot:"header",class:"card-header"},Q={class:"card-content-wrapper"},B={key:0,class:"card-inner"},H={class:"card-left"},J={class:"step-icon"},K={class:"method-tag"},Y={key:0},G={style:{color:"#49cc90"}},Z={key:1},ee={style:{color:"#61affe"}},te={key:2},le={style:{color:"#fca130"}},ae={key:3},se={style:{color:"#50e3c2"}},ne={key:4},oe={style:{color:"#f93e3e"}},ie={key:5},de={style:{color:"rgb(201, 233, 104)"}},re={class:"card-center"},ce={class:"card-url"},ue={class:"card-name"},pe={key:1,class:"card-inner"},fe={class:"card-left"},me={class:"step-icon"},he={class:"card-center if-content"},ge={class:"if-controls-wrapper"},ke={key:2,class:"card-inner"},be={class:"card-left"},_e={class:"step-icon"},ve={class:"card-center"},ye={class:"for-controls-wrapper"},Fe={key:0},Ce={class:"loop"},xe={class:"loop-control"},Ve={class:"loop-control"},we={key:1},Ie={class:"loop"},Te={class:"loop-control"},Le={class:"loop-control"},Se={key:2},$e={class:"loop"},De={class:"while-loop-section"},Ee={class:"loop-control"},Ue={key:0,class:"condition-config"},We={class:"loop-control"},ze={class:"loop-control"},Me={class:"loop-control"},Ne={key:1,class:"condition-config"},Re={class:"loop-control"},Xe={class:"expression-help"},Oe={style:{"font-size":"12px"}},Ae={key:2,class:"condition-config"},Pe={class:"loop-control"},qe={class:"loop-control"},je={class:"function-help"},Qe={class:"while-common-config"},Be={class:"loop-control"},He={class:"loop-control"},Je={class:"loop-control"},Ke={class:"loop-control"},Ye={class:"advanced-options"},Ge={class:"loop-control"},Ze={class:"loop-control"},et={key:4,class:"card-inner"},tt={class:"card-left"},lt={class:"step-icon"},at={class:"card-center"},st={key:6,class:"card-inner"},nt={class:"card-left"},ot={class:"step-icon"},it={class:"card-center"},dt={key:7,class:"card-inner"},rt={class:"card-left"},ct={class:"step-icon"},ut={class:"card-center time-controller"},pt={class:"time-control"},ft={class:"action-buttons"};function mt(e,t,l,o,i,d){const r=(0,a.g2)("Plus"),c=(0,a.g2)("el-icon"),u=(0,a.g2)("el-tag"),p=(0,a.g2)("QuestionFilled"),f=(0,a.g2)("el-tooltip"),m=(0,a.g2)("el-input-number"),h=(0,a.g2)("el-input"),g=(0,a.g2)("el-option"),k=(0,a.g2)("el-select"),b=(0,a.g2)("el-radio"),_=(0,a.g2)("el-radio-group"),v=(0,a.g2)("el-alert"),y=(0,a.g2)("el-divider"),F=(0,a.g2)("el-checkbox"),C=(0,a.g2)("Edit"),x=(0,a.g2)("el-button"),V=(0,a.g2)("Editor"),w=(0,a.g2)("el-col"),I=(0,a.g2)("el-row"),T=(0,a.g2)("el-switch"),L=(0,a.g2)("Delete"),S=(0,a.g2)("el-card"),$=(0,a.g2)("el-tree"),D=(0,a.g2)("el-scrollbar"),E=(0,a.g2)("apiCite"),U=(0,a.g2)("editApi"),W=(0,a.g2)("el-drawer");return(0,a.uX)(),(0,a.CE)("div",X,[(0,a.Lk)("div",O,[(0,a.Lk)("div",A,[(0,a.Lk)("div",P,[(0,a.bF)(u,{color:"#61649f",class:"action-tag",onClick:d.clickApiDlg},{default:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(r)]),_:1}),t[15]||(t[15]=(0,a.eW)("HTTP请求 "))]),_:1,__:[15]},8,["onClick"]),(0,a.bF)(u,{color:"#E6A23C",class:"action-tag",onClick:t[0]||(t[0]=e=>d.addController([],"if"))},{default:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(r)]),_:1}),t[16]||(t[16]=(0,a.eW)("条件控制器 "))]),_:1,__:[16]}),(0,a.bF)(u,{color:"#7B4D12FF",class:"action-tag",onClick:t[1]||(t[1]=e=>d.addController([],"script"))},{default:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(r)]),_:1}),t[17]||(t[17]=(0,a.eW)("自定义脚本 "))]),_:1,__:[17]}),(0,a.bF)(u,{color:"#67C23AFF",class:"action-tag",onClick:t[2]||(t[2]=e=>d.addController([],"py"))},{default:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(r)]),_:1}),t[18]||(t[18]=(0,a.eW)("导入PY脚本 "))]),_:1,__:[18]})]),(0,a.Lk)("div",q,[(0,a.bF)(f,{content:"场景运行权重设置，默认1设置后会影响运行权重，请谨慎设置！",enterable:!1,placement:"top"},{default:(0,a.k6)(()=>[(0,a.bF)(c,{style:{"margin-right":"10px"}},{default:(0,a.k6)(()=>[(0,a.bF)(p)]),_:1})]),_:1}),(0,a.bF)(m,{size:"small",modelValue:l.scenceData.weight,"onUpdate:modelValue":t[3]||(t[3]=e=>l.scenceData.weight=e),min:1,max:10,onChange:e.handleChange},null,8,["modelValue","onChange"])])])]),(0,a.bF)(D,{height:"calc(100vh - 220px)"},{default:(0,a.k6)(()=>[(0,a.bF)($,{data:l.steps,props:d.defaultProps,draggable:"","default-expand-all":i.isExpand,"expand-on-click-node":!1,onNodeClick:d.handleStepClick,"allow-drop":d.allowDrop,onNodeDrop:d.updateStepOrder,"node-drag-start":d.handleDragScroll,class:"custom-tree"},{default:(0,a.k6)(({node:l,data:o})=>[o.stepInfo?((0,a.uX)(),(0,a.Wv)(S,{key:0,class:(0,n.C4)(["step-card",`step-card-${o.stepInfo.type}`])},{default:(0,a.k6)(()=>[(0,a.Lk)("div",j,[(0,a.bF)(I,{gutter:10,type:"flex",align:"middle",justify:"center"},{default:(0,a.k6)(()=>[(0,a.bF)(w,{span:20,class:"card-main-content"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",Q,["api"===o.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",B,[(0,a.Lk)("div",H,[(0,a.Lk)("span",J,(0,n.v_)(d.getCardIndex(l.parent,l)),1),(0,a.bF)(u,{color:"#61649f",class:"step-tag"},{default:(0,a.k6)(()=>t[19]||(t[19]=[(0,a.eW)("HTTP请求")])),_:1,__:[19]}),(0,a.Lk)("span",K,["POST"===o.stepInfo.content.method?((0,a.uX)(),(0,a.CE)("span",Y,[(0,a.Lk)("b",G,(0,n.v_)(o.stepInfo.content.method),1)])):(0,a.Q3)("",!0),"GET"===o.stepInfo.content.method?((0,a.uX)(),(0,a.CE)("span",Z,[(0,a.Lk)("b",ee,(0,n.v_)(o.stepInfo.content.method),1)])):(0,a.Q3)("",!0),"PUT"===o.stepInfo.content.method?((0,a.uX)(),(0,a.CE)("span",te,[(0,a.Lk)("b",le,(0,n.v_)(o.stepInfo.content.method),1)])):(0,a.Q3)("",!0),"PATCH"===o.stepInfo.content.method?((0,a.uX)(),(0,a.CE)("span",ae,[(0,a.Lk)("b",se,(0,n.v_)(o.stepInfo.content.method),1)])):(0,a.Q3)("",!0),"DELETE"===o.stepInfo.content.method?((0,a.uX)(),(0,a.CE)("span",ne,[(0,a.Lk)("b",oe,(0,n.v_)(o.stepInfo.content.method),1)])):(0,a.Q3)("",!0),"DEAD"===o.stepInfo.content.method?((0,a.uX)(),(0,a.CE)("span",ie,[(0,a.Lk)("b",de,(0,n.v_)(o.stepInfo.content.method),1)])):(0,a.Q3)("",!0)])]),(0,a.Lk)("div",re,[(0,a.Lk)("b",ce,(0,n.v_)(o.stepInfo.content.url),1),(0,a.Lk)("span",ue,(0,n.v_)(o.stepInfo.content.name),1)])])):(0,a.Q3)("",!0),"if"===o.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",pe,[(0,a.Lk)("div",fe,[(0,a.Lk)("span",me,(0,n.v_)(d.getCardIndex(l.parent,l)),1),(0,a.bF)(u,{color:"rgb(230, 162, 60)",class:"step-tag"},{default:(0,a.k6)(()=>t[20]||(t[20]=[(0,a.eW)("条件控制器")])),_:1,__:[20]})]),(0,a.Lk)("div",he,[(0,a.Lk)("div",ge,[(0,a.bF)(h,{class:"input-def",placeholder:"变量，例如{{name}}",modelValue:o.stepInfo.content.variable,"onUpdate:modelValue":e=>o.stepInfo.content.variable=e},null,8,["modelValue","onUpdate:modelValue"]),(0,a.bF)(k,{modelValue:o.stepInfo.content.JudgmentMode,"onUpdate:modelValue":e=>o.stepInfo.content.JudgmentMode=e,placeholder:"请选择",class:"judgment-select"},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(i.options,e=>((0,a.uX)(),(0,a.Wv)(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),(0,a.bF)(h,{class:"input-def",placeholder:"值",modelValue:o.stepInfo.content.value,"onUpdate:modelValue":e=>o.stepInfo.content.value=e},null,8,["modelValue","onUpdate:modelValue"])])])])):(0,a.Q3)("",!0),"for"===o.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",ke,[(0,a.Lk)("div",be,[(0,a.Lk)("span",_e,(0,n.v_)(d.getCardIndex(l.parent,l)),1),(0,a.bF)(u,{color:"rgb(2, 167, 240)",class:"step-tag"},{default:(0,a.k6)(()=>t[21]||(t[21]=[(0,a.eW)("循环控制器")])),_:1,__:[21]})]),(0,a.Lk)("div",ve,[(0,a.Lk)("div",ye,[(0,a.bF)(_,{modelValue:o.stepInfo.content.select,"onUpdate:modelValue":e=>o.stepInfo.content.select=e,onClick:t[4]||(t[4]=(0,s.D$)(()=>{},["stop"])),class:"radio-group"},{default:(0,a.k6)(()=>[(0,a.bF)(b,{label:"count",value:"count"},{default:(0,a.k6)(()=>t[22]||(t[22]=[(0,a.eW)("次数循环")])),_:1,__:[22]}),(0,a.bF)(b,{label:"for",value:"for"},{default:(0,a.k6)(()=>t[23]||(t[23]=[(0,a.eW)("for循环")])),_:1,__:[23]}),(0,a.bF)(b,{label:"while",value:"while"},{default:(0,a.k6)(()=>t[24]||(t[24]=[(0,a.eW)("while循环")])),_:1,__:[24]})]),_:2},1032,["modelValue","onUpdate:modelValue"])])])])):(0,a.Q3)("",!0),"for"===o.stepInfo.type&&o.stepInfo.dlg?((0,a.uX)(),(0,a.CE)("div",{key:3,class:"loop-details",onClick:t[5]||(t[5]=(0,s.D$)(()=>{},["stop"]))},["count"===o.stepInfo.content.select||""===o.stepInfo.content.select?((0,a.uX)(),(0,a.CE)("div",Fe,[(0,a.Lk)("div",Ce,[(0,a.Lk)("div",xe,[t[25]||(t[25]=(0,a.Lk)("span",null,"循环次数",-1)),(0,a.bF)(h,{modelValue:o.stepInfo.content.cycleIndex,"onUpdate:modelValue":e=>o.stepInfo.content.cycleIndex=e,style:{width:"200px"},placeholder:"循环次数"},null,8,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",Ve,[t[26]||(t[26]=(0,a.Lk)("span",null,"循环间隔",-1)),(0,a.bF)(m,{modelValue:o.stepInfo.content.cycleInterval,"onUpdate:modelValue":e=>o.stepInfo.content.cycleInterval=e,min:0,max:999,size:"small","controls-position":"right",placeholder:"秒"},null,8,["modelValue","onUpdate:modelValue"]),t[27]||(t[27]=(0,a.Lk)("span",null,"秒",-1))])])])):(0,a.Q3)("",!0),"for"===o.stepInfo.content.select?((0,a.uX)(),(0,a.CE)("div",we,[(0,a.Lk)("div",Ie,[(0,a.Lk)("div",Te,[(0,a.bF)(h,{style:{width:"200px"},placeholder:"定义变量名称",modelValue:o.stepInfo.content.variableName,"onUpdate:modelValue":e=>o.stepInfo.content.variableName=e},null,8,["modelValue","onUpdate:modelValue"]),t[28]||(t[28]=(0,a.Lk)("b",{style:{"margin-left":"10px","margin-right":"10px"}},"in",-1)),(0,a.bF)(h,{style:{width:"200px"},placeholder:"变量，例如{{name}}",modelValue:o.stepInfo.content.variable,"onUpdate:modelValue":e=>o.stepInfo.content.variable=e},null,8,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",Le,[t[29]||(t[29]=(0,a.Lk)("span",null,"循环间隔",-1)),(0,a.bF)(m,{modelValue:o.stepInfo.content.cycleInterval,"onUpdate:modelValue":e=>o.stepInfo.content.cycleInterval=e,min:0,max:999,size:"small","controls-position":"right",placeholder:"秒"},null,8,["modelValue","onUpdate:modelValue"]),t[30]||(t[30]=(0,a.Lk)("span",null,"秒",-1))])])])):(0,a.Q3)("",!0),"while"===o.stepInfo.content.select?((0,a.uX)(),(0,a.CE)("div",Se,[(0,a.Lk)("div",$e,[(0,a.Lk)("div",De,[t[56]||(t[56]=(0,a.Lk)("h4",{style:{margin:"0 0 10px 0",color:"#409eff","font-size":"14px"}},"循环条件设置",-1)),(0,a.Lk)("div",Ee,[t[31]||(t[31]=(0,a.Lk)("span",{style:{"min-width":"80px"}},"条件类型",-1)),(0,a.bF)(k,{modelValue:o.stepInfo.content.whileConditionType,"onUpdate:modelValue":e=>o.stepInfo.content.whileConditionType=e,placeholder:"选择条件类型",style:{width:"200px"}},{default:(0,a.k6)(()=>[(0,a.bF)(g,{label:"变量比较",value:"variable"}),(0,a.bF)(g,{label:"表达式",value:"expression"}),(0,a.bF)(g,{label:"脚本函数",value:"function"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"variable"===o.stepInfo.content.whileConditionType?((0,a.uX)(),(0,a.CE)("div",Ue,[(0,a.Lk)("div",We,[t[32]||(t[32]=(0,a.Lk)("span",null,"左操作数",-1)),(0,a.bF)(h,{style:{width:"180px"},placeholder:"变量，例如{{counter}}",modelValue:o.stepInfo.content.whileLeftOperand,"onUpdate:modelValue":e=>o.stepInfo.content.whileLeftOperand=e},null,8,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",ze,[t[33]||(t[33]=(0,a.Lk)("span",null,"比较操作符",-1)),(0,a.bF)(k,{modelValue:o.stepInfo.content.whileOperator,"onUpdate:modelValue":e=>o.stepInfo.content.whileOperator=e,placeholder:"选择操作符",style:{width:"120px"}},{default:(0,a.k6)(()=>[(0,a.bF)(g,{label:"<",value:"lt"}),(0,a.bF)(g,{label:"<=",value:"lte"}),(0,a.bF)(g,{label:">",value:"gt"}),(0,a.bF)(g,{label:">=",value:"gte"}),(0,a.bF)(g,{label:"==",value:"eq"}),(0,a.bF)(g,{label:"!=",value:"ne"}),(0,a.bF)(g,{label:"包含",value:"contains"}),(0,a.bF)(g,{label:"不包含",value:"not_contains"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",Me,[t[34]||(t[34]=(0,a.Lk)("span",null,"右操作数",-1)),(0,a.bF)(h,{style:{width:"180px"},placeholder:"值或变量，例如10或{{max}}",modelValue:o.stepInfo.content.whileRightOperand,"onUpdate:modelValue":e=>o.stepInfo.content.whileRightOperand=e},null,8,["modelValue","onUpdate:modelValue"])])])):(0,a.Q3)("",!0),"expression"===o.stepInfo.content.whileConditionType?((0,a.uX)(),(0,a.CE)("div",Ne,[(0,a.Lk)("div",Re,[t[35]||(t[35]=(0,a.Lk)("span",null,"条件表达式",-1)),(0,a.bF)(h,{style:{width:"100%","max-width":"400px"},placeholder:"例如: {{counter}} < 100 and {{status}} == 'running'",modelValue:o.stepInfo.content.whileExpression,"onUpdate:modelValue":e=>o.stepInfo.content.whileExpression=e,type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",Xe,[(0,a.bF)(v,{title:"表达式说明",type:"info","show-icon":"",closable:!1,style:{"margin-top":"10px"}},{default:(0,a.k6)(()=>[(0,a.Lk)("div",Oe,[(0,a.Lk)("div",null,"• 支持变量引用: "+(0,n.v_)(e.variable_name),1),t[36]||(t[36]=(0,a.Lk)("div",null,"• 支持比较操作: <, <=, >, >=, ==, !=",-1)),t[37]||(t[37]=(0,a.Lk)("div",null,"• 支持逻辑操作: and, or, not",-1)),(0,a.Lk)("div",null,"• 支持函数调用: len("+(0,n.v_)(e.list_var)+"), int("+(0,n.v_)(e.str_var)+")",1)])]),_:1})])])):(0,a.Q3)("",!0),"function"===o.stepInfo.content.whileConditionType?((0,a.uX)(),(0,a.CE)("div",Ae,[(0,a.Lk)("div",Pe,[t[38]||(t[38]=(0,a.Lk)("span",null,"函数名称",-1)),(0,a.bF)(h,{style:{width:"200px"},placeholder:"例如: check_condition",modelValue:o.stepInfo.content.whileFunctionName,"onUpdate:modelValue":e=>o.stepInfo.content.whileFunctionName=e},null,8,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",qe,[t[39]||(t[39]=(0,a.Lk)("span",null,"函数参数",-1)),(0,a.bF)(h,{style:{width:"300px"},placeholder:"例如: {{var1}}, {{var2}}, 'constant'",modelValue:o.stepInfo.content.whileFunctionArgs,"onUpdate:modelValue":e=>o.stepInfo.content.whileFunctionArgs=e},null,8,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",je,[(0,a.bF)(v,{title:"函数使用说明",type:"warning","show-icon":"",closable:!1,style:{"margin-top":"10px"}},{default:(0,a.k6)(()=>t[40]||(t[40]=[(0,a.Lk)("div",{style:{"font-size":"12px"}},[(0,a.Lk)("div",null,"• 函数必须返回布尔值 (True/False)"),(0,a.Lk)("div",null,"• 函数需要在全局作用域中定义"),(0,a.Lk)("div",null,"• 参数支持变量引用和常量值")],-1)])),_:1})])])):(0,a.Q3)("",!0),(0,a.Lk)("div",Qe,[t[55]||(t[55]=(0,a.Lk)("h4",{style:{margin:"15px 0 10px 0",color:"#409eff","font-size":"14px"}},"循环控制设置",-1)),(0,a.Lk)("div",Be,[t[41]||(t[41]=(0,a.Lk)("span",null,"最大循环次数",-1)),(0,a.bF)(m,{modelValue:o.stepInfo.content.whileMaxIterations,"onUpdate:modelValue":e=>o.stepInfo.content.whileMaxIterations=e,min:1,max:1e4,size:"small","controls-position":"right",placeholder:"次",style:{width:"150px"}},null,8,["modelValue","onUpdate:modelValue"]),t[42]||(t[42]=(0,a.Lk)("span",{style:{"margin-left":"10px","font-size":"12px",color:"#666"}},"防止无限循环",-1))]),(0,a.Lk)("div",He,[t[43]||(t[43]=(0,a.Lk)("span",null,"循环间隔",-1)),(0,a.bF)(m,{modelValue:o.stepInfo.content.cycleInterval,"onUpdate:modelValue":e=>o.stepInfo.content.cycleInterval=e,min:0,max:999,size:"small","controls-position":"right",placeholder:"秒",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"]),t[44]||(t[44]=(0,a.Lk)("span",null,"秒",-1))]),(0,a.Lk)("div",Je,[t[45]||(t[45]=(0,a.Lk)("span",null,"超时时间",-1)),(0,a.bF)(m,{modelValue:o.stepInfo.content.whileTimeout,"onUpdate:modelValue":e=>o.stepInfo.content.whileTimeout=e,min:0,max:3600,size:"small","controls-position":"right",placeholder:"秒",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"]),t[46]||(t[46]=(0,a.Lk)("span",null,"秒 (0表示无超时)",-1))]),(0,a.Lk)("div",Ke,[t[47]||(t[47]=(0,a.Lk)("span",null,"循环计数器变量",-1)),(0,a.bF)(h,{style:{width:"200px"},placeholder:"例如: loop_counter",modelValue:o.stepInfo.content.whileCounterVar,"onUpdate:modelValue":e=>o.stepInfo.content.whileCounterVar=e},null,8,["modelValue","onUpdate:modelValue"]),t[48]||(t[48]=(0,a.Lk)("span",{style:{"margin-left":"10px","font-size":"12px",color:"#666"}},"可在条件和子步骤中使用",-1))]),(0,a.Lk)("div",Ye,[(0,a.bF)(y,{"content-position":"left",style:{margin:"15px 0 10px 0"}},{default:(0,a.k6)(()=>t[49]||(t[49]=[(0,a.Lk)("span",{style:{color:"#909399","font-size":"12px"}},"高级选项",-1)])),_:1,__:[49]}),(0,a.Lk)("div",Ge,[(0,a.bF)(F,{modelValue:o.stepInfo.content.whileBreakOnError,"onUpdate:modelValue":e=>o.stepInfo.content.whileBreakOnError=e,style:{"margin-right":"20px"}},{default:(0,a.k6)(()=>t[50]||(t[50]=[(0,a.eW)(" 遇到错误时终止循环 ")])),_:2,__:[50]},1032,["modelValue","onUpdate:modelValue"]),(0,a.bF)(F,{modelValue:o.stepInfo.content.whileLogIterations,"onUpdate:modelValue":e=>o.stepInfo.content.whileLogIterations=e},{default:(0,a.k6)(()=>t[51]||(t[51]=[(0,a.eW)(" 记录每次迭代日志 ")])),_:2,__:[51]},1032,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",Ze,[t[54]||(t[54]=(0,a.Lk)("span",null,"条件检查时机",-1)),(0,a.bF)(_,{modelValue:o.stepInfo.content.whileCheckTiming,"onUpdate:modelValue":e=>o.stepInfo.content.whileCheckTiming=e,size:"small"},{default:(0,a.k6)(()=>[(0,a.bF)(b,{label:"before"},{default:(0,a.k6)(()=>t[52]||(t[52]=[(0,a.eW)("执行前检查")])),_:1,__:[52]}),(0,a.bF)(b,{label:"after"},{default:(0,a.k6)(()=>t[53]||(t[53]=[(0,a.eW)("执行后检查")])),_:1,__:[53]})]),_:2},1032,["modelValue","onUpdate:modelValue"])])])])])])])):(0,a.Q3)("",!0)])):(0,a.Q3)("",!0),"script"===o.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",et,[(0,a.Lk)("div",tt,[(0,a.Lk)("span",lt,(0,n.v_)(d.getCardIndex(l.parent,l)),1),(0,a.bF)(u,{color:"rgb(123, 77, 18)",class:"step-tag"},{default:(0,a.k6)(()=>t[57]||(t[57]=[(0,a.eW)("自定义脚本")])),_:1,__:[57]})]),(0,a.Lk)("div",at,[o.stepInfo.inputDlg?((0,a.uX)(),(0,a.Wv)(h,{key:0,onClick:t[6]||(t[6]=(0,s.D$)(()=>{},["stop"])),modelValue:o.stepInfo.name,"onUpdate:modelValue":e=>o.stepInfo.name=e,onBlur:e=>d.cancelEditing(o.stepInfo),ref:"input",maxlength:"50",class:"script-name-input"},null,8,["modelValue","onUpdate:modelValue","onBlur"])):((0,a.uX)(),(0,a.Wv)(x,{key:1,class:"script-button",plain:"",type:"text",onClick:[e=>d.startEditing(o.stepInfo),t[7]||(t[7]=(0,s.D$)(()=>{},["stop"]))]},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(o.stepInfo.name)+" ",1),(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(C)]),_:1})]),_:2},1032,["onClick"]))])])):(0,a.Q3)("",!0),"script"===o.stepInfo.type&&o.stepInfo.dlg?((0,a.uX)(),(0,a.CE)("div",{key:5,class:"script-editor",onClick:t[8]||(t[8]=(0,s.D$)(()=>{},["stop"]))},[(0,a.bF)(I,{gutter:10},{default:(0,a.k6)(()=>[(0,a.bF)(w,{span:24},{default:(0,a.k6)(()=>[(0,a.bF)(V,{modelValue:o.stepInfo.script,"onUpdate:modelValue":e=>o.stepInfo.script=e,lang:"python",theme:"chrome"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)])):(0,a.Q3)("",!0),"py"===o.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",st,[(0,a.Lk)("div",nt,[(0,a.Lk)("span",ot,(0,n.v_)(d.getCardIndex(l.parent,l)),1),(0,a.bF)(u,{color:"rgb(103, 194, 58)",class:"step-tag"},{default:(0,a.k6)(()=>t[58]||(t[58]=[(0,a.eW)("导入PY脚本")])),_:1,__:[58]})]),(0,a.Lk)("div",it,[o.stepInfo.inputDlg?((0,a.uX)(),(0,a.Wv)(h,{key:0,onClick:t[9]||(t[9]=(0,s.D$)(()=>{},["stop"])),modelValue:o.stepInfo.name,"onUpdate:modelValue":e=>o.stepInfo.name=e,onBlur:e=>d.cancelEditing(o.stepInfo),ref:"input",maxlength:"50",class:"script-name-input"},null,8,["modelValue","onUpdate:modelValue","onBlur"])):((0,a.uX)(),(0,a.Wv)(x,{key:1,class:"script-button",plain:"",type:"text",onClick:[e=>d.startEditing(o.stepInfo),t[10]||(t[10]=(0,s.D$)(()=>{},["stop"]))]},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(o.stepInfo.name)+" ",1),(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(C)]),_:1})]),_:2},1032,["onClick"]))])])):(0,a.Q3)("",!0),"time"===o.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",dt,[(0,a.Lk)("div",rt,[(0,a.Lk)("span",ct,(0,n.v_)(d.getCardIndex(l.parent,l)),1),(0,a.bF)(u,{color:"rgb(103, 194, 58)",class:"step-tag"},{default:(0,a.k6)(()=>t[59]||(t[59]=[(0,a.eW)("等待控制器")])),_:1,__:[59]})]),(0,a.Lk)("div",ut,[(0,a.Lk)("div",pt,[(0,a.bF)(m,{modelValue:o.stepInfo.content.time,"onUpdate:modelValue":e=>o.stepInfo.content.time=e,min:0,max:999,size:"small","controls-position":"right",placeholder:"秒"},null,8,["modelValue","onUpdate:modelValue"]),t[60]||(t[60]=(0,a.Lk)("span",null,"秒",-1))])])])):(0,a.Q3)("",!0)])]),_:2},1024),(0,a.bF)(w,{span:4,class:"card-actions"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",ft,["api"===o.stepInfo.type?((0,a.uX)(),(0,a.Wv)(f,{key:0,class:"item",effect:"light",content:"接口任务运行权重默认为1,设置后将影响运行结果！",placement:"top"},{default:(0,a.k6)(()=>[(0,a.bF)(m,{onClick:t[11]||(t[11]=(0,s.D$)(()=>{},["stop"])),modelValue:o.stepInfo.weight,"onUpdate:modelValue":e=>o.stepInfo.weight=e,min:1,max:10,size:"default","controls-position":"right",placeholder:"1",style:{width:"80px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)):(0,a.Q3)("",!0),(0,a.bF)(T,{onClick:[t[12]||(t[12]=(0,s.D$)(()=>{},["stop"])),t=>e.switchClick(o)],modelValue:o.stepInfo.status,"onUpdate:modelValue":e=>o.stepInfo.status=e,"inline-prompt":"",size:"default",style:{"--el-switch-on-color":"#53a8ff","--el-switch-off-color":"#dcdfe6"}},null,8,["modelValue","onUpdate:modelValue","onClick"]),(0,a.bF)(x,{onClick:[t[13]||(t[13]=(0,s.D$)(()=>{},["stop"])),e=>d.delTree(o)],size:"default",circle:"",type:"danger"},{default:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(L)]),_:1})]),_:2},1032,["onClick"])])]),_:2},1024)]),_:2},1024)])]),_:2},1032,["class"])):(0,a.Q3)("",!0)]),_:1},8,["data","props","default-expand-all","onNodeClick","allow-drop","onNodeDrop","node-drag-start"])]),_:1}),i.addApiDlg?((0,a.uX)(),(0,a.Wv)(E,{key:0,selectType:i.selectType,onChildEvent:d.addController,onCloseModal:d.handleCloseModal},null,8,["selectType","onChildEvent","onCloseModal"])):(0,a.Q3)("",!0),(0,a.bF)(W,{modelValue:i.editApiDlg,"onUpdate:modelValue":t[14]||(t[14]=e=>i.editApiDlg=e),"with-header":!1,size:"50%"},{default:(0,a.k6)(()=>[(0,a.bF)(U,{ref:"childRef",onCloseDrawer:d.handleClose,interfaceData:i.interfaceData,style:{padding:"0 10px"}},null,8,["onCloseDrawer","interfaceData"])]),_:1},8,["modelValue"])])}var ht=l(93491),gt=l(53629);const kt={style:{margin:"10px"}},bt={key:0},_t={key:0},vt={key:1},yt={key:2},Ft={class:"code_mod"},Ct={class:"code_mod"},xt={class:"code_mod"},Vt={class:"code_mod"},wt={class:"code_mod"},It={class:"code_mod"},Tt={class:"code_mod"},Lt={class:"code_mod"},St={class:"code_mod"},$t={class:"code_mod"},Dt={class:"code_mod"},Et={class:"code_mod"},Ut={class:"code_mod"},Wt={class:"code_mod"},zt={key:0};function Mt(e,t,l,o,i,d){const r=(0,a.g2)("el-divider"),c=(0,a.g2)("el-option"),u=(0,a.g2)("el-select"),p=(0,a.g2)("el-input"),f=(0,a.g2)("el-form-item"),m=(0,a.g2)("el-col"),h=(0,a.g2)("Promotion"),g=(0,a.g2)("el-icon"),k=(0,a.g2)("el-button"),b=(0,a.g2)("EditPen"),_=(0,a.g2)("Refresh"),v=(0,a.g2)("el-row"),y=(0,a.g2)("el-tag"),F=(0,a.g2)("el-scrollbar"),C=(0,a.g2)("el-form"),x=(0,a.g2)("Editor"),V=(0,a.g2)("el-tab-pane"),w=(0,a.g2)("el-radio"),I=(0,a.g2)("el-radio-group"),T=(0,a.g2)("FromData"),L=(0,a.g2)("el-tabs"),S=(0,a.g2)("caseResult");return(0,a.uX)(),(0,a.Wv)(F,{height:"calc(100vh);padding-right:10px;"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",kt,[(0,a.bF)(r,{"content-position":"left",style:{"margin-bottom":"30px"}},{default:(0,a.k6)(()=>t[27]||(t[27]=[(0,a.Lk)("b",null,"Api信息",-1)])),_:1,__:[27]}),(0,a.bF)(C,{rules:i.rulesinterface,ref:"interfaceRef",model:i.caseInfo},{default:(0,a.k6)(()=>[(0,a.bF)(v,{gutter:10,style:{"margin-bottom":"20px"}},{default:(0,a.k6)(()=>[(0,a.bF)(m,{span:16},{default:(0,a.k6)(()=>[(0,a.bF)(f,{prop:"url"},{default:(0,a.k6)(()=>[(0,a.bF)(p,{modelValue:i.caseInfo.url,"onUpdate:modelValue":t[1]||(t[1]=e=>i.caseInfo.url=e),placeholder:"请输入接口地址"},{prepend:(0,a.k6)(()=>[(0,a.bF)(u,{modelValue:i.caseInfo.method,"onUpdate:modelValue":t[0]||(t[0]=e=>i.caseInfo.method=e),placeholder:"请求类型",size:"small",style:{width:"96px",color:"black"}},{default:(0,a.k6)(()=>[(0,a.bF)(c,{label:"GET",value:"GET",style:{color:"rgba(204,73,145,0.87)"}}),(0,a.bF)(c,{label:"POST",value:"POST",style:{color:"#61affe"}}),(0,a.bF)(c,{label:"PUT",value:"PUT",style:{color:"#fca130"}}),(0,a.bF)(c,{label:"PATCH",value:"PATCH",style:{color:"#50e3c2"}}),(0,a.bF)(c,{label:"DELETE",value:"DELETE",style:{color:"#f93e3e"}}),(0,a.bF)(c,{label:"HEAD",value:"HEAD",style:{color:"rgb(201, 233, 104)"}})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1})]),_:1}),(0,a.bF)(m,{span:8,style:{"text-align":"right"}},{default:(0,a.k6)(()=>[(0,a.bF)(k,{onClick:d.runCase,type:"success"},{default:(0,a.k6)(()=>[(0,a.bF)(g,null,{default:(0,a.k6)(()=>[(0,a.bF)(h)]),_:1}),t[28]||(t[28]=(0,a.eW)("调试 "))]),_:1,__:[28]},8,["onClick"]),(0,a.bF)(k,{onClick:d.editClick,type:"primary"},{default:(0,a.k6)(()=>[(0,a.bF)(g,null,{default:(0,a.k6)(()=>[(0,a.bF)(b)]),_:1}),t[29]||(t[29]=(0,a.eW)("保存 "))]),_:1,__:[29]},8,["onClick"]),(0,a.bF)(k,{onClick:d.getNewInterface,type:"warning"},{default:(0,a.k6)(()=>[(0,a.bF)(g,null,{default:(0,a.k6)(()=>[(0,a.bF)(_)]),_:1}),t[30]||(t[30]=(0,a.eW)("同步 "))]),_:1,__:[30]},8,["onClick"])]),_:1})]),_:1}),(0,a.bF)(v,{gutter:24,style:{"margin-bottom":"20px"}},{default:(0,a.k6)(()=>[(0,a.bF)(m,{span:12},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"接口名称",prop:"name"},{default:(0,a.k6)(()=>[(0,a.bF)(p,{modelValue:i.caseInfo.name,"onUpdate:modelValue":t[2]||(t[2]=e=>i.caseInfo.name=e),placeholder:"请输入接口名称",clearable:"",style:{width:"300px"}},null,8,["modelValue"])]),_:1})]),_:1}),(0,a.bF)(m,{span:12},{default:(0,a.k6)(()=>[(0,a.bF)(F,{height:"60px"},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"接口标签"},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(i.caseInfo.interface_tag,e=>((0,a.uX)(),(0,a.Wv)(y,{key:e,size:"small",type:d.getRandomType(),closable:"","disable-transitions":!1,style:{"margin-right":"5px"},onClose:t=>d.removeTag(e),effect:"light"},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(e),1)]),_:2},1032,["type","onClose"]))),128)),i.state.editTag?((0,a.uX)(),(0,a.Wv)(p,{key:0,ref:"caseTagInputRef",modelValue:i.state.tagValue,"onUpdate:modelValue":t[3]||(t[3]=e=>i.state.tagValue=e),size:"small",onKeyup:(0,s.jR)(d.addTag,["enter"]),onBlur:d.addTag,style:{width:"100px"},maxlength:"30"},null,8,["modelValue","onKeyup","onBlur"])):((0,a.uX)(),(0,a.Wv)(k,{key:1,size:"small",onClick:d.showEditTag},{default:(0,a.k6)(()=>t[31]||(t[31]=[(0,a.eW)("+ New Tag")])),_:1,__:[31]},8,["onClick"]))]),_:1})]),_:1})]),_:1}),(0,a.bF)(m,{span:24},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"描述"},{default:(0,a.k6)(()=>[(0,a.bF)(p,{modelValue:i.caseInfo.desc,"onUpdate:modelValue":t[4]||(t[4]=e=>i.caseInfo.desc=e),type:"textarea",clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),(0,a.bF)(m,{span:5},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"创建用户：",style:{"margin-top":"10px"}},{default:(0,a.k6)(()=>[(0,a.Lk)("a",null,(0,n.v_)(this.caseInfo.creator),1)]),_:1})]),_:1}),(0,a.bF)(m,{span:7},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"创建时间：",style:{"margin-top":"10px"}},{default:(0,a.k6)(t=>[(0,a.Lk)("a",null,(0,n.v_)(e.$tools.rTime(this.caseInfo.create_time)),1)]),_:1})]),_:1}),(0,a.bF)(m,{span:5},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"修改用户：",style:{"margin-top":"10px"}},{default:(0,a.k6)(()=>[(0,a.Lk)("a",null,(0,n.v_)(this.caseInfo.modifier),1)]),_:1})]),_:1}),(0,a.bF)(m,{span:7},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"修改时间：",style:{"margin-top":"10px"}},{default:(0,a.k6)(t=>[this.caseInfo.update_time?((0,a.uX)(),(0,a.CE)("a",bt,(0,n.v_)(e.$tools.rTime(this.caseInfo.update_time)),1)):(0,a.Q3)("",!0)]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"]),(0,a.bF)(r,{"content-position":"left",style:{"margin-top":"0px"}},{default:(0,a.k6)(()=>t[32]||(t[32]=[(0,a.Lk)("b",null,"请求信息",-1)])),_:1,__:[32]}),(0,a.bF)(L,{type:"border-card",style:{"min-height":"370px"}},{default:(0,a.k6)(()=>[(0,a.bF)(V,{label:"请求头(headers)"},{default:(0,a.k6)(()=>[(0,a.bF)(x,{modelValue:i.headers,"onUpdate:modelValue":t[5]||(t[5]=e=>i.headers=e)},null,8,["modelValue"])]),_:1}),(0,a.bF)(V,{label:"查询参数(Params)"},{default:(0,a.k6)(()=>[(0,a.bF)(x,{modelValue:i.params,"onUpdate:modelValue":t[6]||(t[6]=e=>i.params=e)},null,8,["modelValue"])]),_:1}),(0,a.bF)(V,{label:"请求体(Body)"},{default:(0,a.k6)(()=>[(0,a.bF)(I,{modelValue:i.paramType,"onUpdate:modelValue":t[7]||(t[7]=e=>i.paramType=e),style:{"margin-bottom":"5px"}},{default:(0,a.k6)(()=>[(0,a.bF)(w,{label:"json"},{default:(0,a.k6)(()=>t[33]||(t[33]=[(0,a.eW)("application/json")])),_:1,__:[33]}),(0,a.bF)(w,{label:"data"},{default:(0,a.k6)(()=>t[34]||(t[34]=[(0,a.eW)("x-www-form-urlencoded")])),_:1,__:[34]}),(0,a.bF)(w,{label:"formData"},{default:(0,a.k6)(()=>t[35]||(t[35]=[(0,a.eW)("form-data")])),_:1,__:[35]})]),_:1},8,["modelValue"]),"json"===i.paramType?((0,a.uX)(),(0,a.CE)("div",_t,[(0,a.bF)(x,{modelValue:i.json,"onUpdate:modelValue":t[8]||(t[8]=e=>i.json=e)},null,8,["modelValue"])])):"data"===i.paramType?((0,a.uX)(),(0,a.CE)("div",vt,[(0,a.bF)(x,{modelValue:i.data,"onUpdate:modelValue":t[9]||(t[9]=e=>i.data=e)},null,8,["modelValue"])])):"formData"===i.paramType?((0,a.uX)(),(0,a.CE)("div",yt,[(0,a.bF)(T,{modelValue:i.file,"onUpdate:modelValue":t[10]||(t[10]=e=>i.file=e)},null,8,["modelValue"])])):(0,a.Q3)("",!0)]),_:1}),(0,a.bF)(V,{label:"前置脚本"},{default:(0,a.k6)(()=>[(0,a.bF)(v,{gutter:10},{default:(0,a.k6)(()=>[(0,a.bF)(m,{span:18},{default:(0,a.k6)(()=>[(0,a.bF)(x,{modelValue:i.caseInfo.setup_script,"onUpdate:modelValue":t[11]||(t[11]=e=>i.caseInfo.setup_script=e),lang:"python",theme:"monokai"},null,8,["modelValue"])]),_:1}),(0,a.bF)(m,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(r,{style:{width:"195px"}},{default:(0,a.k6)(()=>t[36]||(t[36]=[(0,a.eW)("脚本模板")])),_:1,__:[36]}),(0,a.Lk)("div",Ft,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[12]||(t[12]=e=>d.addSetUptCodeMod("ENV"))},{default:(0,a.k6)(()=>t[37]||(t[37]=[(0,a.eW)("预设全局变量")])),_:1,__:[37]})]),(0,a.Lk)("div",Ct,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[13]||(t[13]=e=>d.addSetUptCodeMod("env"))},{default:(0,a.k6)(()=>t[38]||(t[38]=[(0,a.eW)("预设局部变量")])),_:1,__:[38]})]),(0,a.Lk)("div",xt,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[14]||(t[14]=e=>d.addSetUptCodeMod("func"))},{default:(0,a.k6)(()=>t[39]||(t[39]=[(0,a.eW)("调用全局函数")])),_:1,__:[39]})]),(0,a.Lk)("div",Vt,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[15]||(t[15]=e=>d.addSetUptCodeMod("sql"))},{default:(0,a.k6)(()=>t[40]||(t[40]=[(0,a.eW)("执行sql查询")])),_:1,__:[40]})])]),_:1})]),_:1})]),_:1}),(0,a.bF)(V,{label:"后置脚本"},{default:(0,a.k6)(()=>[(0,a.bF)(v,{gutter:10},{default:(0,a.k6)(()=>[(0,a.bF)(m,{span:18},{default:(0,a.k6)(()=>[(0,a.bF)(x,{modelValue:i.caseInfo.teardown_script,"onUpdate:modelValue":t[16]||(t[16]=e=>i.caseInfo.teardown_script=e),lang:"python",theme:"monokai"},null,8,["modelValue"])]),_:1}),(0,a.bF)(m,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(r,{style:{width:"195px"}},{default:(0,a.k6)(()=>t[41]||(t[41]=[(0,a.eW)("脚本模板")])),_:1,__:[41]}),(0,a.bF)(F,{height:"250px"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",wt,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[17]||(t[17]=e=>d.addTearDownCodeMod("getBody"))},{default:(0,a.k6)(()=>t[42]||(t[42]=[(0,a.eW)("获取响应体")])),_:1,__:[42]})]),(0,a.Lk)("div",It,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[18]||(t[18]=e=>d.addTearDownCodeMod("JSextract"))},{default:(0,a.k6)(()=>t[43]||(t[43]=[(0,a.eW)("jsonpath提取数据")])),_:1,__:[43]})]),(0,a.Lk)("div",Tt,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[19]||(t[19]=e=>d.addTearDownCodeMod("REextract"))},{default:(0,a.k6)(()=>t[44]||(t[44]=[(0,a.eW)("正则提取数据")])),_:1,__:[44]})]),(0,a.Lk)("div",Lt,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[20]||(t[20]=e=>d.addTearDownCodeMod("ENV"))},{default:(0,a.k6)(()=>t[45]||(t[45]=[(0,a.eW)("设置全局变量")])),_:1,__:[45]})]),(0,a.Lk)("div",St,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[21]||(t[21]=e=>d.addTearDownCodeMod("env"))},{default:(0,a.k6)(()=>t[46]||(t[46]=[(0,a.eW)("设置局部变量")])),_:1,__:[46]})]),(0,a.Lk)("div",$t,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[22]||(t[22]=e=>d.addTearDownCodeMod("func"))},{default:(0,a.k6)(()=>t[47]||(t[47]=[(0,a.eW)("调用全局函数")])),_:1,__:[47]})]),(0,a.Lk)("div",Dt,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[23]||(t[23]=e=>d.addTearDownCodeMod("sql"))},{default:(0,a.k6)(()=>t[48]||(t[48]=[(0,a.eW)("执行sql查询")])),_:1,__:[48]})]),(0,a.Lk)("div",Et,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[24]||(t[24]=e=>d.addTearDownCodeMod("http"))},{default:(0,a.k6)(()=>t[49]||(t[49]=[(0,a.eW)("断言HTTP状态码")])),_:1,__:[49]})]),(0,a.Lk)("div",Ut,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[25]||(t[25]=e=>d.addTearDownCodeMod("eq"))},{default:(0,a.k6)(()=>t[50]||(t[50]=[(0,a.eW)("断言相对")])),_:1,__:[50]})]),(0,a.Lk)("div",Wt,[(0,a.bF)(k,{type:"success",size:"small",plain:"",onClick:t[26]||(t[26]=e=>d.addTearDownCodeMod("contain"))},{default:(0,a.k6)(()=>t[51]||(t[51]=[(0,a.eW)("断言包含")])),_:1,__:[51]})])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),i.runResult?((0,a.uX)(),(0,a.CE)("div",zt,[(0,a.bF)(r,{"content-position":"left"},{default:(0,a.k6)(()=>t[52]||(t[52]=[(0,a.Lk)("b",null,"执行结果",-1)])),_:1,__:[52]}),(0,a.bF)(S,{result:i.runResult},null,8,["result"])])):(0,a.Q3)("",!0)])]),_:1})}var Nt=l(67638),Rt=l(97167),Xt=l(57477),Ot={props:{interfaceData:{type:String,default:{}}},components:{caseResult:Nt.A,FromData:Rt.A,Editor:gt.A,Promotion:Xt.Promotion,EditPen:Xt.EditPen,Refresh:Xt.Refresh},data(){return{rulesinterface:{name:[{required:!0,message:"请输入接口名称",trigger:"blur"}],url:[{required:!0,message:"请输入接口信息",trigger:"blur"}]},state:{form:{item:[{type:""},{type:"success"},{type:"info"},{type:"danger"},{type:"warning"}]},editTag:!1,tagValue:""},caseInfo:{method:"POST",interface_tag:[],YApi_status:"",url:"",name:"",treenode:this.treeId,creator:"",modifier:"",desc:"",headers:{},request:{json:{},data:null,params:{}},file:[],setup_script:"# 前置脚本(python):\n# global_tools:全局工具函数\n# data:用例数据 \n# env: 局部环境\n# ENV: 全局环境\n# db: 数据库操作对象",teardown_script:"# 后置脚本(python):\n# global_tools:全局工具函数\n# data:用例数据 \n# response:响应对象response \n# env: 局部环境\n# ENV: 全局环境\n# db: 数据库操作对象"},paramType:"json",json:"{}",data:"{}",params:"{}",headers:"{}",interfaceparams:"{}",file:[],interface_tag:[],runResult:""}},computed:{...(0,N.aH)(["pro","envId"]),username(){return window.sessionStorage.getItem("username")}},methods:{focusInput(){this.$nextTick(()=>{this.$refs.caseTagInputRef.focus()})},addTag(){this.state.editTag&&this.state.tagValue&&(this.caseInfo.interface_tag||(this.caseInfo.interface_tag=[]),this.caseInfo.interface_tag.push(this.state.tagValue),this.focusInput()),this.state.editTag=!1,this.state.tagValue=""},removeTag(e){this.caseInfo.interface_tag.splice(this.caseInfo.interface_tag.indexOf(e),1)},showEditTag(){this.state.editTag=!0,this.focusInput()},getRandomType(){const e=Math.floor(Math.random()*this.state.form.item.length);return this.state.form.item[e].type},addSetUptCodeMod(e){switch(e){case"ENV":this.caseInfo.setup_script+='\n# 设置全局变量 \ntest.save_global_variable("变量名",变量值)';break;case"env":this.caseInfo.setup_script+='\n# 设置局部变量  \ntest.save_env_variable("变量名",变量值)';break;case"func":this.caseInfo.setup_script+="\n# 调用全局工具函数random_mobile随机生成一个手机号码  \nmobile = global_func.random_mobile()";break;case"sql":this.caseInfo.setup_script+='\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\n# db.连接名.execute_all(sql语句) \nsql = "SELECT count(*) as count FROM futureloan.member"\nres = db.aliyun.execute_all(sql)';break}},addTearDownCodeMod(e){switch(e){case"getBody":this.caseInfo.teardown_script+="\n# Demo:获取响应体(json)  \nbody = response.json()",this.caseInfo.teardown_script+="\n# Demo2:获取响应体(字符串)  \nbody = response.text";break;case"JSextract":this.caseInfo.teardown_script+='\n# Demo:jsonpath提取response中的msg字段  \nmsg = test.json_extract(response.json(),"$..msg")';break;case"REextract":this.caseInfo.teardown_script+='\n# Demo:正则提取响应体中的数据  \nres = test.re_extract(response.text,"正则表达式",)';break;case"ENV":this.caseInfo.teardown_script+='\n# 设置全局变量 \ntest.save_global_variable("变量名",变量值)';break;case"env":this.caseInfo.teardown_script+='\n# 设置局部变量  \ntest.save_env_variable("变量名",变量值)';break;case"func":this.caseInfo.teardown_script+="\n# 调用全局工具函数random_mobile随机生成一个手机号码  \nmobile = global_func.random_mobile()";break;case"sql":this.caseInfo.teardown_script+='\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\n# db.连接名.execute_all(sql语句) \nsql = "SELECT count(*) as count FROM futureloan.member"\nres = db.aliyun.execute_all(sql)';break;case"http":this.caseInfo.teardown_script+='\n# 断言http状态码 \n# Demo:断言http状态码是否为200  \ntest.assertion("相等",200,response.status_code)';break;case"eq":this.caseInfo.teardown_script+='\n# 断言相等 \ntest.assertion("相等","预期结果","实际结果")';break;case"contain":this.caseInfo.teardown_script+='\n# 断言包含:预期结果中的内容在实际结果中是否存在 \ntest.assertion("包含","预期结果","实际结果")';break}},interfaceInfo(){this.runResult=null,this.caseInfo={...this.interfaceData.content},this.json=JSON.stringify(this.caseInfo.request.json||{},null,4),this.data=JSON.stringify(this.caseInfo.request.data||{},null,4),this.params=JSON.stringify(this.caseInfo.request.params||{},null,4),this.headers=JSON.stringify(this.caseInfo.headers||{},null,4),this.caseInfo.interface_tag=Array.from(this.caseInfo.interface_tag.tag),this.file=this.caseInfo.file},getEditData(){let e={...this.caseInfo};delete e.status,e.interface_tag={tag:[...e.interface_tag]},e.modifier=this.username,e.update_time=this.$tools.newTime();try{e.headers=JSON.parse(this.headers)}catch(t){return this.$message({message:"提交的headers数据 json格式错误，请检查！",type:"warning",duration:1e3}),null}if("json"===this.paramType){const a=l(67160);try{e.request={json:a.parse(this.json)},e.request.data=null,e.file=[]}catch(t){return this.$message({message:"提交的app-``lication/json数据json格式错误，请检查！",type:"warning",duration:1e3}),null}}else if("data"===this.paramType)try{e.request={data:JSON.parse(this.data)},e.request.json=null,e.file=[]}catch(t){return this.$message({message:"提交的x-www-form-urlencoded数据json格式错误，请检查！",type:"warning",duration:1e3}),null}else"formData"===this.paramType&&(e.file=this.file,e.request={});try{return e.request.params=JSON.parse(this.params),e}catch(t){return this.$message({message:"提交的Params数据json格式错误，请检查！",type:"warning",duration:1e3}),null}},async editClick(){this.$refs.interfaceRef.validate(async e=>{if(!e)return;const t={content:this.getEditData()},l=await this.$api.updateScenceStep(this.interfaceData.id,t);200===l.status&&this.$message({type:"success",message:"保存成功",duration:1e3})})},async runCase(){this.$refs.interfaceRef.validate(async e=>{if(!e)return;const t=this.getEditData();t.interface={url:this.caseInfo.url,method:this.caseInfo.method};const l={data:t,env:this.envId},a=await this.$api.runNewCase(l);200===a.status&&(this.runResult=a.data,(0,R.df)({duration:500,title:"删除成功",type:"success"}))})},async getNewInterface(){const e=await this.$api.getnewInterface(this.interfaceData.content.id);200===e.status&&(this.$message({type:"success",message:"获取成功，若需要更新数据请保存",duration:1e3}),this.caseInfo={...e.data},this.runResult=null,this.json=JSON.stringify(this.caseInfo.request.json||{},null,4),this.data=JSON.stringify(this.caseInfo.request.data||{},null,4),this.params=JSON.stringify(this.caseInfo.request.params||{},null,4),this.headers=JSON.stringify(this.caseInfo.headers||{},null,4),this.caseInfo.interface_tag=Array.from(this.caseInfo.interface_tag.tag),this.file=this.caseInfo.file)}},watch:{interfaceData:{deep:!0,handler(e,t){this.interfaceInfo()}}},created(){this.interfaceInfo()}},At=l(71241);const Pt=(0,At.A)(Ot,[["render",Mt],["__scopeId","data-v-12ddac76"]]);var qt=Pt,jt={components:{apiCite:ht.A,Editor:gt.A,editApi:qt,Plus:Xt.Plus,Edit:Xt.Edit,Delete:Xt.Delete,QuestionFilled:Xt.QuestionFilled},props:{scenceId:{type:Number,default:[]},steps:{type:Array},scenceData:{type:String}},data(){return{addApiDlg:!1,editApiDlg:!1,selectType:"perf",treeKey:"",isExpand:!1,ControllerData:{scence:"",name:"",type:"",content:{},desc:"",script:"",creator:"",weight:""},step_id:"",interfaceData:"",options:[{value:"equal",label:"等于"},{value:"notEqual",label:"不等于"},{value:"contains",label:"包含"},{value:"notContains",label:"不包含"},{value:"greaterThan",label:"大于"},{value:"lessThan",label:"小于"},{value:"greaterThanOrEqual",label:"大于等于"},{value:"lessThanOrEqual",label:"小于等于"},{value:"empty",label:"空"},{value:"notEmpty",label:"非空"}]}},methods:{rowOpenORFold(e){this.treeKey=+new Date,this.isExpand=e},handleStepClick(e){"api"===e.stepInfo.type?(this.editApiDlg=!0,this.interfaceData=e.stepInfo):["for","script"].includes(e.stepInfo.type)&&(e.stepInfo.dlg=!e.stepInfo.dlg)},allowDrop(e,t,l){const a=["for","if"];return!!a.includes(t.data.stepInfo.type)||("prev"===l||"next"===l)},fetchSteps(e){this.$emit("fetch-steps",e)},async updateStepOrder(){const e=(t,l,a)=>{t.sort=a,t.children&&t.children.length>0&&t.children.forEach((l,a)=>{l.parent=t.id,l.sort=a+1,e(l,t.id,l.sort)})};this.steps.forEach((t,l)=>{t.sort=l+1,t.children&&t.children.length>0?e(t,t.id,t.sort):t.parent=null})},handleDragScroll(){document.addEventListener("mousemove",function(e){const t=e.clientY,l=document.querySelector(".el-tree").getBoundingClientRect().top;t<100&&l>0?window.scrollBy(0,-10):t>window.innerHeight-100&&window.scrollBy(0,10)})},getCardIndex(e,t){const l=e.childNodes.indexOf(t);return l+1},async addController(e,t){const l={...this.ControllerData};l.creator=this.username,l.type=t,l.scence=this.scenceId;const a=[];let s=this.steps.length>0?this.steps.length+1:1;if("if"===t?(l.name="条件控制器",l.content={variable:"",JudgmentMode:"",value:""},delete l.weight):"script"===t?(l.name="自定义脚本",delete l.weight):"time"===t?(l.name="定时控制器",l.content={time:""},delete l.weight):(l.name="HTTP接口",l.type="api",l.weight=1,e.forEach(e=>{let t={...l,content:e};a.push(t)})),["if","for","time","script"].includes(t)){const e=await this.$api.createSceneStep(l);201===e.status&&(this.step_id=e.data.id)}else{const e=await this.$api.createSceneStep(a);201===e.status&&(this.step_id=e.data.map(e=>e.id))}const n=await this.$api.createTaskSceneStep({task:this.perfTask.id,scence:this.scenceId,step:this.step_id,sort:s,creator:this.username,parent:null});201===n.status&&(0,R.df)({duration:500,title:"添加成功",type:"success"}),this.fetchSteps(this.scenceId)},handleCloseModal(){this.addApiDlg=!1},clickApiDlg(){this.addApiDlg=!0},cancelEditing(e){e.inputDlg=!1},startEditing(e){e.type,e.inputDlg=!0,this.$nextTick(()=>{this.$refs.input.focus()})},async delTree(e){event.stopPropagation(),console.log(e);const t=await this.$api.deleteTaskSceneStep(e.id,this.scenceId);if(204===t.status){const t=await this.$api.deleteSceneStep(e.stepInfo.id);204===t.status&&((0,R.df)({duration:500,title:"删除成功",type:"success"}),this.fetchSteps(this.scenceId))}},handleClose(){this.editApiDlg=!1}},computed:{...(0,N.aH)({envId:e=>e.envId,testEnvs:e=>e.testEnvs,pro:e=>e.pro,perfTask:e=>e.perfTask}),defaultProps(){return{children:"children",label:"name"}},username(){return window.sessionStorage.getItem("username")}}};const Qt=(0,At.A)(jt,[["render",mt],["__scopeId","data-v-21d2f755"]]);var Bt=Qt;const Ht={style:{display:"flex","justify-content":"space-between","align-items":"center","margin-bottom":"20px"}},Jt={style:{display:"flex",gap:"5px"}},Kt={key:0},Yt={key:1},Gt={style:{color:"#606266",display:"flex","align-items":"center","justify-content":"space-between"}};function Zt(e,t,l,s,o,i){const d=(0,a.g2)("el-button"),r=(0,a.g2)("el-form-item"),c=(0,a.g2)("el-input"),u=(0,a.g2)("timerTaskCron"),p=(0,a.g2)("el-popover"),f=(0,a.g2)("el-option"),m=(0,a.g2)("el-select"),h=(0,a.g2)("el-input-number"),g=(0,a.g2)("el-tooltip"),k=(0,a.g2)("el-radio"),b=(0,a.g2)("el-radio-group"),_=(0,a.g2)("el-form"),v=(0,a.g2)("el-card"),y=(0,a.g2)("el-table-column"),F=(0,a.g2)("el-table"),C=(0,a.g2)("el-scrollbar"),x=(0,a.g2)("makeSet"),V=(0,a.g2)("el-dialog");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.bF)(v,{shadow:"never"},{default:(0,a.k6)(()=>[(0,a.bF)(C,{height:"calc(100vh - 200px)"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",Ht,[t[20]||(t[20]=(0,a.Lk)("span",{style:{"font-size":"18px","margin-left":"7px"}},"任务配置",-1)),(0,a.Lk)("div",Jt,[(0,a.bF)(d,{type:"primary",onClick:i.clickSetEdit},{default:(0,a.k6)(()=>t[18]||(t[18]=[(0,a.eW)("保存")])),_:1,__:[18]},8,["onClick"]),(0,a.bF)(d,{type:"info",onClick:i.clickSetting},{default:(0,a.k6)(()=>t[19]||(t[19]=[(0,a.eW)("导入预设配置")])),_:1,__:[19]},8,["onClick"])])]),(0,a.bF)(_,{model:o.configForm,rules:o.rulesConfig,ref:"ConfigRef","label-width":"95px"},{default:(0,a.k6)(()=>[(0,a.bF)(r,{label:"任务类型：",prop:"taskType"},{default:(0,a.k6)(()=>[(0,a.eW)((0,n.v_)(o.taskTypeMap[o.configForm.taskType]||o.configForm.taskType),1)]),_:1}),(0,a.bF)(r,{prop:"name",label:"配置名称："},{default:(0,a.k6)(()=>[(0,a.bF)(c,{modelValue:o.configForm.name,"onUpdate:modelValue":t[0]||(t[0]=e=>o.configForm.name=e),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),"20"===o.configForm.taskType?((0,a.uX)(),(0,a.Wv)(r,{key:0,label:"时间配置：",prop:"rule"},{default:(0,a.k6)(()=>[(0,a.bF)(p,{visible:o.cronVisible,"onUpdate:visible":t[2]||(t[2]=e=>o.cronVisible=e),placement:"bottom-start",width:"30"},{reference:(0,a.k6)(()=>[(0,a.bF)(c,{modelValue:o.configForm.rule,"onUpdate:modelValue":t[1]||(t[1]=e=>o.configForm.rule=e),clearable:"",readonly:"",placeholder:"请选择定时任务时间配置",onClick:i.cronFun},null,8,["modelValue","onClick"])]),default:(0,a.k6)(()=>[(0,a.bF)(u,{runTimeStr:o.configForm.rule,onCloseTime:i.closeRunTimeCron,onRunTime:i.runTimeCron},null,8,["runTimeStr","onCloseTime","onRunTime"])]),_:1},8,["visible"])]),_:1})):(0,a.Q3)("",!0),(0,a.bF)(r,{prop:"logMode",label:"日志模式："},{default:(0,a.k6)(()=>[(0,a.bF)(m,{modelValue:i.selectedLogMode,"onUpdate:modelValue":t[3]||(t[3]=e=>i.selectedLogMode=e),placeholder:"请选择日志模式",style:{width:"100%"}},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"关闭",value:"0"}),(0,a.bF)(f,{label:"开启-全部日志",value:"10"}),(0,a.bF)(f,{label:"开启-仅成功日志",value:"20"}),(0,a.bF)(f,{label:"开启-仅失败日志",value:"30"})]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(r,{label:"控制模式：",prop:"control"},{default:(0,a.k6)(()=>[(0,a.bF)(m,{modelValue:i.selectControlMode,"onUpdate:modelValue":t[4]||(t[4]=e=>i.selectControlMode=e),placeholder:"请选择控制模式",style:{width:"100%"}},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"集合模式",value:"10"}),(0,a.bF)(f,{label:"单独模式",value:"20"})]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(r,{label:"压测模式：",prop:"pressureMode"},{default:(0,a.k6)(()=>[(0,a.bF)(m,{modelValue:i.selectPressureMode,"onUpdate:modelValue":t[5]||(t[5]=e=>i.selectPressureMode=e),placeholder:"请选择压测模式",style:{width:"100%"}},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"并发模式",value:"10"}),(0,a.bF)(f,{label:"阶梯模式",value:"20"})]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(r,{label:"时长单位：",prop:"pressureMode"},{default:(0,a.k6)(()=>[(0,a.bF)(m,{modelValue:o.configForm.timeUnit,"onUpdate:modelValue":t[6]||(t[6]=e=>o.configForm.timeUnit=e),placeholder:"请选择时长单位",style:{width:"100%"}},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"s",value:"s"}),(0,a.bF)(f,{label:"m",value:"m"}),(0,a.bF)(f,{label:"h",value:"h"})]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(r,{label:"思考时间：",prop:"thinkTime"},{default:(0,a.k6)(()=>[(0,a.bF)(m,{modelValue:i.selectTimeType,"onUpdate:modelValue":t[7]||(t[7]=e=>i.selectTimeType=e),placeholder:"请选择时间类型",style:{width:"100%","margin-bottom":"10px"}},{default:(0,a.k6)(()=>[(0,a.bF)(f,{label:"固定",value:"10"}),(0,a.bF)(f,{label:"随机",value:"20"})]),_:1},8,["modelValue"]),"20"===o.configForm.thinkTimeType?((0,a.uX)(),(0,a.CE)("span",Kt,[(0,a.bF)(h,{modelValue:o.configForm.thinkTime[0],"onUpdate:modelValue":t[8]||(t[8]=e=>o.configForm.thinkTime[0]=e),min:0,max:999,size:"small","controls-position":"right",onChange:e.handleChange,style:{width:"90px"}},null,8,["modelValue","onChange"]),t[21]||(t[21]=(0,a.Lk)("span",{style:{"margin-right":"5px","margin-left":"5px"}},"-",-1)),(0,a.bF)(h,{modelValue:o.configForm.thinkTime[1],"onUpdate:modelValue":t[9]||(t[9]=e=>o.configForm.thinkTime[1]=e),min:0,max:999,size:"small","controls-position":"right",onChange:e.handleChange,style:{width:"90px"}},null,8,["modelValue","onChange"])])):((0,a.uX)(),(0,a.CE)("span",Yt,[(0,a.bF)(h,{modelValue:o.configForm.thinkTime[0],"onUpdate:modelValue":t[10]||(t[10]=e=>o.configForm.thinkTime[0]=e),min:0,max:999,size:"small","controls-position":"right",onChange:e.handleChange,style:{width:"90px"}},null,8,["modelValue","onChange"])]))]),_:1}),(0,a.bF)(r,{style:{"margin-top":"15px","margin-bottom":"15px"},label:"运行机器：",prop:"resource"},{default:(0,a.k6)(()=>[(0,a.bF)(b,{modelValue:o.configForm.resource,"onUpdate:modelValue":t[13]||(t[13]=e=>o.configForm.resource=e)},{default:(0,a.k6)(()=>[(0,a.bF)(k,{label:"10",onClick:t[11]||(t[11]=e=>i.clickResource("10"))},{default:(0,a.k6)(()=>[t[23]||(t[23]=(0,a.eW)("默认 ")),(0,a.bF)(g,{content:"使用机器管理中默认机器运行",enterable:!1,placement:"top"},{default:(0,a.k6)(()=>t[22]||(t[22]=[(0,a.Lk)("i",{class:"el-icon-question",style:{color:"#909399","font-size":"16px"}},null,-1)])),_:1,__:[22]})]),_:1,__:[23]}),(0,a.bF)(k,{label:"20",onClick:t[12]||(t[12]=e=>i.clickResource("20"))},{default:(0,a.k6)(()=>[t[25]||(t[25]=(0,a.eW)(" 自定义 ")),(0,a.bF)(g,{content:"支持选择多机器分布式运行",enterable:!1,placement:"top"},{default:(0,a.k6)(()=>t[24]||(t[24]=[(0,a.Lk)("i",{class:"el-icon-question",style:{color:"#909399","font-size":"16px"}},null,-1)])),_:1,__:[24]})]),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1}),"10"===o.configForm.pressureMode?((0,a.uX)(),(0,a.Wv)(v,{key:1,style:{"background-color":"#f5f7f9"},class:"card",shadow:"always"},{default:(0,a.k6)(()=>[(0,a.bF)(_,{"label-width":"120px",model:o.FormConcurrency,rules:o.rulesConcurrencyMode,ref:"CaseRef"},{default:(0,a.k6)(()=>[(0,a.bF)(r,{label:"并发用户数：",prop:"concurrencyNumber"},{default:(0,a.k6)(()=>[(0,a.bF)(c,{modelValue:o.FormConcurrency.concurrencyNumber,"onUpdate:modelValue":t[14]||(t[14]=e=>o.FormConcurrency.concurrencyNumber=e)},null,8,["modelValue"])]),_:1}),(0,a.bF)(r,{label:"并发数步长：",prop:"concurrencyStep"},{default:(0,a.k6)(()=>[(0,a.bF)(c,{modelValue:o.FormConcurrency.concurrencyStep,"onUpdate:modelValue":t[15]||(t[15]=e=>o.FormConcurrency.concurrencyStep=e)},null,8,["modelValue"])]),_:1}),(0,a.bF)(r,{label:"持续时长：",prop:"lastLong"},{default:(0,a.k6)(()=>[(0,a.bF)(c,{modelValue:o.FormConcurrency.lastLong,"onUpdate:modelValue":t[16]||(t[16]=e=>o.FormConcurrency.lastLong=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1})):(0,a.Q3)("",!0),"20"===o.configForm.pressureMode?((0,a.uX)(),(0,a.Wv)(v,{key:2,style:{"margin-left":"7px","margin-right":"4px","background-color":"#f5f7f9"},class:"card",shadow:"always"},{default:(0,a.k6)(()=>[(0,a.bF)(_,{"label-width":"125px",model:o.FormLadder,rules:o.rulesLadderMode,ref:"CaseRef"},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(o.FormLadder.ladders,(e,l)=>((0,a.uX)(),(0,a.CE)("div",{key:l},[(0,a.Lk)("div",Gt,[(0,a.Lk)("span",null,"阶梯"+(0,n.v_)(l+1),1),(0,a.bF)(d,{disabled:l<1,size:"mini",type:"text",onClick:e=>i.removeLadder(l)},{default:(0,a.k6)(()=>t[26]||(t[26]=[(0,a.eW)(" 删除 ")])),_:2,__:[26]},1032,["disabled","onClick"])]),(0,a.bF)(r,{label:"并发用户数：",prop:"ladders."+l+".concurrencyNumber"},{default:(0,a.k6)(()=>[(0,a.bF)(c,{modelValue:e.concurrencyNumber,"onUpdate:modelValue":t=>e.concurrencyNumber=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),(0,a.bF)(r,{label:"并发数步长：",prop:"ladders."+l+".concurrencyStep"},{default:(0,a.k6)(()=>[(0,a.bF)(c,{modelValue:e.concurrencyStep,"onUpdate:modelValue":t=>e.concurrencyStep=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),(0,a.bF)(r,{label:"阶梯持续时长：",prop:"ladders."+l+".lastLong"},{default:(0,a.k6)(()=>[(0,a.bF)(c,{modelValue:e.lastLong,"onUpdate:modelValue":t=>e.lastLong=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]))),128))]),_:1},8,["model","rules"]),(0,a.bF)(d,{style:{width:"100%","margin-top":"20px","background-color":"#ecf5ff",color:"#409eff"},onClick:i.addLadder},{default:(0,a.k6)(()=>t[27]||(t[27]=[(0,a.eW)(" add Data ")])),_:1,__:[27]},8,["onClick"])]),_:1})):(0,a.Q3)("",!0),"20"===o.configForm.resource?((0,a.uX)(),(0,a.Wv)(F,{key:3,height:"200",data:o.serverData,style:{width:"100%","margin-top":"15px","margin-bottom":"35px"},border:"true",onSelectionChange:i.handleSelectionChange,ref:"serverTable"},{default:(0,a.k6)(()=>[(0,a.bF)(y,{type:"selection",width:"40px"}),(0,a.bF)(y,{align:"center",prop:"name",label:"机器名称"}),(0,a.bF)(y,{align:"center",prop:"host_ip",label:"IP",width:"130px"})]),_:1},8,["data","onSelectionChange"])):(0,a.Q3)("",!0)]),_:1},8,["model","rules"])]),_:1})]),_:1}),(0,a.bF)(V,{title:"导入预设配置",modelValue:o.SettingDlg,"onUpdate:modelValue":t[17]||(t[17]=e=>o.SettingDlg=e),"destroy-on-close":"","before-close":i.handleClose,width:"80%",top:"10px"},{default:(0,a.k6)(()=>[(0,a.bF)(x,{setButton:o.setButton,taskType:o.configForm.taskType,onSetDlg:i.handleClose,onSetData:i.handleSetData},null,8,["setButton","taskType","onSetDlg","onSetData"])]),_:1},8,["modelValue","before-close"])],64)}var el=l(70682),tl=l(57890),ll={components:{makeSet:el["default"],timerTaskCron:tl.A},data(){return{taskTypeMap:{10:"普通任务",20:"定时任务"},cronVisible:!1,configForm:{name:"",rule:"",taskType:"",logMode:"0",pressureMode:"10",timeUnit:"s",control:"20",resource:"10",pressureConfig:{},serverArray:[],project:"",creator:"",thinkTimeType:"10",thinkTime:[0]},FormConcurrency:{lastLong:"",concurrencyNumber:"",concurrencyStep:""},FormLadder:{ladders:[{concurrencyNumber:"",concurrencyStep:"",lastLong:""}]},rulesConfig:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],thinkTime:[{required:!0,message:"请输入思考时间",trigger:"blur"}]},rulesConcurrencyMode:{lastLong:[{required:!0,message:"请输入持续时长",trigger:"blur"}],concurrencyNumber:[{required:!0,message:"请输入并发数",trigger:"blur"}],concurrencyStep:[{required:!0,message:"请输入步长",trigger:"blur"}]},SettingDlg:!1,setButton:!0,serverData:[],defaultSelection:[],Selection:[],rulesLadderMode:{}}},computed:{...(0,N.aH)({...(0,N.aH)(["perfTask"]),server:e=>e.server,pro:e=>e.pro}),username(){return window.sessionStorage.getItem("username")},selectedLogMode:{get(){return this.configForm.logMode.toString()},set(e){this.configForm.logMode=Number(e)}},selectPressureMode:{get(){return this.configForm.pressureMode.toString()},set(e){this.configForm.pressureMode=e}},selectControlMode:{get(){return this.configForm.control.toString()},set(e){this.configForm.control=Number(e)}},selectTimeType:{get(){return this.configForm.thinkTimeType.toString()},set(e){this.configForm.thinkTimeType=e}}},mounted(){this.configForm.taskType=this.perfTask.taskType,this.setRules()},watch:{"configForm.thinkTimeType"(e){this.configForm.thinkTime="20"===e?[this.configForm.thinkTime[0],this.configForm.thinkTime[1]]:[this.configForm.thinkTime[0]]}},methods:{handleClose(e){this.SettingDlg=e,this.SettingDlg=!1},handleSetData(e){this.configForm=e;const t=this.configForm.serverArray;this.Selection=this.serverData.filter(e=>t.includes(e.id)),"10"===this.configForm.pressureMode?this.FormConcurrency=e.pressureConfig:"20"===this.configForm.pressureMode&&(this.FormLadder=e.pressureConfig),this.$nextTick(()=>{this.$refs.serverTable?this.Selection.forEach(e=>{this.$refs.serverTable.toggleRowSelection(e,!0)}):console.error("serverTable is undefined")})},async getPresetting(){const e=await this.$api.getPresetting({project_id:this.pro.id,isSetting:!0,task:this.perfTask.id});200===e.status&&e.data.result.length>0&&this.handleSetData(e.data.result[0])},clickResource(e){"20"===e?setTimeout(()=>{this.$nextTick(()=>{this.$refs.serverTable?this.Selection.forEach(e=>{this.$refs.serverTable.toggleRowSelection(e,!0)}):console.error("serverTable is undefined")})},2e3):"10"===e?this.configForm.serverArray=this.defaultSelection.map(e=>e.id):this.$message({message:"暂不支持该类型",type:"warning"})},clickSetting(){this.SettingDlg=!0},cronFun(){this.cronVisible=!0},closeRunTimeCron(e){this.cronVisible=e},runTimeCron(e){this.configForm.rule=e},async getServerData(){const e=await this.$api.getServers(this.pro.id,1);200===e.status&&(this.serverData=e.data.result,this.defaultSelection=this.serverData.filter(e=>!0===e.default_code),this.Selection=this.defaultSelection.map(e=>e.id))},handleSelectionChange(e){this.configForm.serverArray=e.map(e=>e.id)},dataSubmit(){const e={...this.configForm};if(e.task=this.perfTask.id,e.update_time=this.$tools.newTime(),e.modifier=this.username,e.creator=this.username,delete e.create_time,delete e.id,"10"===e.taskType&&delete e.rule,"10"===e.pressureMode){e.pressureConfig=this.FormConcurrency;const{ladders:t,...l}=e.pressureConfig;e.pressureConfig=l}else if("20"===e.pressureMode){e.pressureConfig=this.FormLadder;const{...t}=e.pressureConfig;e.pressureConfig=t}return e.project=this.pro.id,e},async clickSetEdit(){const e=this.dataSubmit(),t=await this.$api.setPresetting(e);200===t.status&&this.$message({message:"保存成功",type:"success"})},addLadder(){this.FormLadder.ladders.push({concurrencyNumber:"",concurrencyStep:"",lastLong:""}),this.setRules()},setRules(){const e={};this.FormLadder.ladders.forEach((t,l)=>{e[`ladders.${l}.concurrencyNumber`]=[{required:!0,message:"并发用户数不能为空",trigger:"blur"}],e[`ladders.${l}.concurrencyStep`]=[{required:!0,message:"并发数步长不能为空",trigger:"blur"}],e[`ladders.${l}.lastLong`]=[{required:!0,message:"阶梯持续时长不能为空",trigger:"blur"}]}),this.rulesLadderMode=e},removeLadder(e){this.FormLadder.ladders.length>1&&(this.FormLadder.ladders.splice(e,1),this.setRules())}},created(){this.getServerData(),this.getPresetting()}};const al=(0,At.A)(ll,[["render",Zt],["__scopeId","data-v-2b80dd13"]]);var sl=al,nl={components:{perfStep:Bt,configuration:sl,CaretLeft:Xt.CaretLeft,Edit:Xt.Edit,CaretRight:Xt.CaretRight,Plus:Xt.Plus,View:Xt.View,Remove:Xt.Remove,SuccessFilled:Xt.SuccessFilled,CircleClose:Xt.CircleClose,Refresh:Xt.Refresh,Document:Xt.Document,VideoPlay:Xt.VideoPlay,Delete:Xt.Delete,ArrowDown:Xt.ArrowDown},data(){return{dialogType:"",dialogTitle:"",dialogVisible:!1,dialogVisible1:!1,steps:[],scenceId:"",sceneForm:{name:"",task:""},importSetData:null,taskForm:{taskName:"",desc:"",update_time:"",modifier:""},taskTypeMap:{10:"普通任务",20:"定时任务"},inputDlg:!1,filterText:"",sceneList:[],scenceData:"",envInfo:{},showEnv:!1,otsDlg:!1,titleOts:"",typeOts:"",new_task_form:{case_checkAll:!1},isIndeterminate:!1}},methods:{...(0,N.PY)(["clearTask","checkedTask","selectEnv","selectEnvInfo"]),back(){window.history.back(),this.clearTask()},async getScenes(e){const t=await this.$api.getTaskScenes(this.perfTask.id,e);200===t.status&&(this.sceneList=t.data,t.data.length>0&&(this.scenceId=t.data[0].id,this.handleNodeClick(t.data[0])))},async addScene(){const e=this.sceneForm,t=await this.$api.createTaskScene(e);201===t.status&&(this.$message({type:"success",message:"添加成功",duration:1e3}),this.clearValidation(),this.getScenes())},async editScene(){const e=this.sceneForm,t=await this.$api.updateTaskScene(e.id,e);200===t.status&&(this.$message({type:"success",message:"编辑成功",duration:1e3}),this.clearValidation(),this.getScenes())},delScene(e){this.$confirm("此操作将永久删除该场景, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const t=await this.$api.deleteTaskScene(e);204===t.status&&(this.$message({type:"success",message:"删除成功!"}),this.getScenes())}).catch(()=>{this.$message({type:"info",message:"已取消删除"})})},async clickOts(e){switch(this.otsDlg=e,e){case"sync":this.titleOts="批量同步接口数据",this.typeOts=e;const t=await this.$api.getSceneStep(this.scenceId);200===t.status&&(this.steps=t.data);break;case"delete":this.titleOts="批量删除",this.typeOts=e;break;case"start":this.titleOts="批量启用",this.typeOts=e;break;case"stop":this.titleOts="批量禁用",this.typeOts=e;break}},handleClose(){this.otsDlg=!1,this.new_task_form.case_checkAll=!1,this.getTaskScenceStep(this.scenceId)},popup(e,t){switch(this.dialogType=e,e){case"add":this.dialogVisible=!0,this.dialogTitle="新增场景",this.sceneForm.task=this.perfTask.id;break;case"edit":this.dialogVisible=!0,this.dialogTitle="编辑场景",this.sceneForm={...t},this.sceneForm.id=t.id;break;case"task-edit":this.dialogVisible1=!0,this.dialogTitle="编辑任务",this.taskForm.taskName=this.perfTask.taskName,this.taskForm.desc=this.perfTask.desc;break;default:this.dialogTitle="";break}},clearValidation(){this.dialogVisible=!1,this.dialogVisible1=!1,this.sceneForm={name:"",task:""},this.taskForm={taskName:"",desc:"",update_time:"",modifier:""}},handleNodeClick(e){this.scenceId=e.id,this.scenceData=e,this.getTaskScenceStep(this.scenceId)},searchClick(){this.getScenes(this.filterText)},startEditing(){this.inputDlg=!0,this.$nextTick(()=>{this.$refs.input.focus()})},cancelEditing(){this.inputDlg=!1},async clickShowEnv(){const e=await this.$api.getEnvInfo(this.scenceData.env,this.pro.id);200===e.status&&(this.envInfo=e.data),this.showEnv=!0},editEnv(e){this.showEnv=!1,this.selectEnvInfo(e),this.$router.push({name:"testenv"})},async editTask(){const e=this.taskForm;e.modifier=this.username,e.update_time=this.$tools.newTime();const t=await this.$api.updatePerformanceTask(this.perfTask.id,e);200===t.status&&(this.$message({type:"success",message:"编辑成功",duration:1e3}),this.checkedTask(t.data),this.clearValidation())},async getTaskScenceStep(e){const t=await this.$api.getTaskSceneStep(e);200===t.status&&(this.steps=t.data)},async clickScenceStep(){const e={...this.scenceData};e.env?e.env=[e.env]:delete e.env,await this.$api.updateTaskScene(this.scenceId,e);const t=await this.$api.batchUpdateSceneStep(this.steps);200===t.status&&(0,R.df)({duration:500,title:"保存成功",type:"success"})},getCardIndex(e,t){const l=e.childNodes.indexOf(t);return l+1},case_check_change(e,t,l){let a=0,s=0,n=!1;for(let o=0;o<this.steps.length;o++)1==this.$refs.casetree.getNode(this.steps[o]).disabled&&(s+=1),1==this.$refs.casetree.getNode(this.steps[o]).checked&&(a+=1),1==this.$refs.casetree.getNode(this.steps[o]).indeterminate&&(n=!0);0==a?(this.isIndeterminate=!1,this.new_task_form.case_checkAll=!1,1==n&&(this.isIndeterminate=!0,this.new_task_form.case_checkAll=!1)):a+s==this.steps.length?(this.isIndeterminate=!1,this.new_task_form.case_checkAll=!0):(this.isIndeterminate=!0,this.new_task_form.case_checkAll=!1)},handleCheckAllChange(e){if(this.isIndeterminate=!1,e)for(let t=0;t<this.steps.length;t++)this.$refs.casetree.getNode(this.steps[t]).disabled||this.$refs.casetree.setChecked(this.steps[t].id,!0,!0);else this.$refs.casetree.setCheckedKeys([])},async makeOts(e){const t={type:e,data:this.$refs.casetree.getCheckedNodes()};if("sync"!==e){const e=await this.$api.batchTaskSceneStep(t);200===e.status&&((0,R.df)({duration:500,title:"批量变更成功",type:"success"}),this.handleClose())}else{const e=await this.$api.batchSaveApiStep(this.$refs.casetree.getCheckedNodes());200===e.status&&((0,R.df)({duration:500,title:"批量同步成功",type:"success"}),this.handleClose())}},async debugScence(){if(!this.scenceId)return void this.$message({type:"warning",message:"请先选择场景",duration:2e3});if(!this.steps||0===this.steps.length)return void this.$message({type:"warning",message:"当前场景没有步骤，无法调试",duration:2e3});const e=this.validateScenario();e.isValid?this.startDebugMode():this.$confirm(`场景配置存在问题：\n${e.errors.join("\n")}\n\n是否继续调试？`,"场景验证",{confirmButtonText:"继续调试",cancelButtonText:"取消",type:"warning",customClass:"debug-confirm-dialog"}).then(()=>{this.startDebugMode()}).catch(()=>{this.$message({type:"info",message:"已取消调试"})})},validateScenario(){const e=[];let t=!0;return this.scenceData.env||(e.push("• 未选择测试环境"),t=!1),this.steps.forEach((l,a)=>{const s=l.stepInfo;if(s)switch(s.type){case"api":s.content?.url||(e.push(`• 步骤${a+1}: HTTP请求缺少URL`),t=!1),s.content?.method||(e.push(`• 步骤${a+1}: HTTP请求缺少请求方法`),t=!1);break;case"if":s.content?.variable&&s.content?.JudgmentMode&&s.content?.value||(e.push(`• 步骤${a+1}: 条件控制器配置不完整`),t=!1);break;case"for":"count"!==s.content?.select||s.content?.cycleIndex||(e.push(`• 步骤${a+1}: 循环控制器缺少循环次数`),t=!1),"for"!==s.content?.select||s.content?.variable&&s.content?.variableName||(e.push(`• 步骤${a+1}: for循环控制器配置不完整`),t=!1);break;case"script":s.script&&""!==s.script.trim()||(e.push(`• 步骤${a+1}: 自定义脚本内容为空`),t=!1);break;case"time":s.content?.time||(e.push(`• 步骤${a+1}: 等待控制器缺少等待时间`),t=!1);break}}),{isValid:t,errors:e}},startDebugMode(){const e=this.buildDebugInfo();this.$alert(e,"场景调试信息",{confirmButtonText:"开始执行调试",cancelButtonText:"关闭",showCancelButton:!0,type:"info",dangerouslyUseHTMLString:!0,customClass:"debug-dialog",width:"80%"}).then(()=>{this.executeDebug()}).catch(()=>{this.$message({type:"info",message:"已关闭调试窗口"})})},buildDebugInfo(){const e=this.testEnvs.find(e=>e.id===this.scenceData.env),t=e?e.name:"未知环境";return`\n      <div style="max-height: 500px; overflow-y: auto; text-align: left;">\n        \x3c!-- 场景基本信息 --\x3e\n        <div style="margin-bottom: 20px;">\n          <h3 style="color: #409eff; margin-bottom: 10px;">🎯 场景基本信息</h3>\n          <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">\n            <div><strong>场景名称:</strong> ${this.scenceData.name||"未命名场景"}</div>\n            <div><strong>测试环境:</strong> ${t}</div>\n            <div><strong>场景权重:</strong> ${this.scenceData.weight||1}</div>\n            <div><strong>步骤数量:</strong> ${this.steps.length}</div>\n            <div><strong>启用步骤:</strong> ${this.steps.filter(e=>e.stepInfo?.status).length}</div>\n          </div>\n        </div>\n\n        \x3c!-- 执行步骤预览 --\x3e\n        <div style="margin-bottom: 20px;">\n          <h3 style="color: #409eff; margin-bottom: 10px;">📋 执行步骤预览</h3>\n          <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">\n            ${this.steps.map((e,t)=>this.buildStepDebugInfo(e,t)).join("")}\n          </div>\n        </div>\n\n        \x3c!-- 环境变量 --\x3e\n        ${this.buildEnvironmentInfo()}\n\n        \x3c!-- 调试建议 --\x3e\n        <div style="margin-bottom: 20px;">\n          <h3 style="color: #409eff; margin-bottom: 10px;">💡 调试建议</h3>\n          <div style="background: #fff3cd; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">\n            ${this.generateDebugSuggestions()}\n          </div>\n        </div>\n      </div>\n    `},buildStepDebugInfo(e,t){const l=e.stepInfo;if(!l)return"";const a=l.status?"✅":"❌",s=t+1;let n="";switch(l.type){case"api":n=`\n          <div style="margin-left: 20px; font-size: 12px; color: #666;">\n            <div>方法: <code>${l.content?.method||"GET"}</code></div>\n            <div>URL: <code>${l.content?.url||"未设置"}</code></div>\n            <div>权重: ${l.weight||1}</div>\n          </div>\n        `;break;case"if":n=`\n          <div style="margin-left: 20px; font-size: 12px; color: #666;">\n            <div>条件: <code>${l.content?.variable||""} ${l.content?.JudgmentMode||""} ${l.content?.value||""}</code></div>\n          </div>\n        `;break;case"for":n=`\n          <div style="margin-left: 20px; font-size: 12px; color: #666;">\n            <div>循环类型: ${"count"===l.content?.select?"次数循环":"for循环"}</div>\n            ${"count"===l.content?.select?`<div>循环次数: ${l.content?.cycleIndex||"未设置"}</div>`:`<div>循环变量: ${l.content?.variableName||""} in ${l.content?.variable||""}</div>`}\n            <div>循环间隔: ${l.content?.cycleInterval||0}秒</div>\n          </div>\n        `;break;case"script":n=`\n          <div style="margin-left: 20px; font-size: 12px; color: #666;">\n            <div>脚本行数: ${l.script?l.script.split("\n").length:0}</div>\n          </div>\n        `;break;case"time":n=`\n          <div style="margin-left: 20px; font-size: 12px; color: #666;">\n            <div>等待时间: ${l.content?.time||0}秒</div>\n          </div>\n        `;break}return`\n      <div style="margin-bottom: 10px; padding: 10px; border: 1px solid #e4e7ed; border-radius: 4px; ${l.status?"background: #f0f9ff;":"background: #fff2f0;"}">\n        <div style="font-weight: 500;">\n          ${a} 步骤${s}: ${this.getStepTypeName(l.type)} - ${l.name||l.content?.name||"未命名"}\n        </div>\n        ${n}\n      </div>\n    `},buildEnvironmentInfo(){return this.scenceData.env?`\n      <div style="margin-bottom: 20px;">\n        <h3 style="color: #409eff; margin-bottom: 10px;">🌍 环境变量</h3>\n        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">\n          <div style="margin-bottom: 10px;">\n            <strong>当前环境:</strong> ${this.testEnvs.find(e=>e.id===this.scenceData.env)?.name||"未知"}\n          </div>\n          <div style="font-size: 12px; color: #666;">\n            <div>调试将使用当前环境的配置变量</div>\n            <div>点击环境信息按钮可查看详细变量配置</div>\n          </div>\n        </div>\n      </div>\n    `:'\n        <div style="margin-bottom: 20px;">\n          <h3 style="color: #409eff; margin-bottom: 10px;">🌍 环境变量</h3>\n          <div style="background: #fff2f0; padding: 15px; border-radius: 6px; border-left: 4px solid #f56c6c;">\n            <div style="color: #f56c6c;">⚠️ 未选择测试环境</div>\n          </div>\n        </div>\n      '},generateDebugSuggestions(){const e=[],t=this.steps.filter(e=>!e.stepInfo?.status).length;t>0&&e.push(`有 ${t} 个步骤被禁用，调试时将跳过这些步骤`);const l=this.steps.filter(e=>"api"===e.stepInfo?.type).length;l>0&&e.push(`包含 ${l} 个HTTP请求，建议先测试单个接口的连通性`);const a=this.steps.filter(e=>"for"===e.stepInfo?.type).length;a>0&&e.push(`包含 ${a} 个循环控制器，注意循环次数设置避免过度执行`);const s=this.steps.filter(e=>"script"===e.stepInfo?.type).length;return s>0&&e.push(`包含 ${s} 个自定义脚本，确保脚本语法正确`),0===e.length&&e.push("场景配置良好，可以开始调试"),e.map(e=>`• ${e}`).join("<br>")},getStepTypeName(e){const t={api:"HTTP请求",if:"条件控制器",for:"循环控制器",script:"自定义脚本",py:"导入PY脚本",time:"等待控制器"};return t[e]||"未知类型"},async executeDebug(){try{this.$message({type:"info",message:"开始执行场景调试...",duration:2e3});const e={scene_id:this.scenceId,env_id:this.scenceData.env,debug_mode:!0},t=await this.$api.debugScenario(e);200===t.status&&this.showDebugResults(t.data)}catch(e){console.error("调试执行错误:",e),this.$message({type:"error",message:"调试执行失败: "+(e.response?.data?.message||e.message||"未知错误"),duration:3e3})}},showDebugResults(e){const t={total_steps:e.total_steps||e.totalSteps||0,successful_steps:e.successful_steps||e.successfulSteps||0,failed_steps:e.failed_steps||e.failedSteps||0,execution_time:e.execution_time||e.executionTime||0,success_rate:e.success_rate||e.successRate||0,step_results:e.debug_results?.step_results||[],error_summary:e.debug_results?.error_summary||[],overall_result:e.overall_result||e.overallResult||"unknown"},l=this.buildDebugResults(t);this.$alert(l,"调试执行结果",{confirmButtonText:"关闭",type:"info",dangerouslyUseHTMLString:!0,customClass:"debug-results-dialog",width:"85%"})},buildDebugResults(e){return e?`\n      <div style="max-height: 600px; overflow-y: auto; text-align: left;">\n        <div style="margin-bottom: 20px;">\n          <h3 style="color: #409eff; margin-bottom: 10px;">📊 执行概览</h3>\n          <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px;">\n            <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;">\n              <div style="font-size: 18px; font-weight: bold; color: #409eff;">${e.total_steps||e.totalSteps||0}</div>\n              <div style="font-size: 12px; color: #666;">总步骤</div>\n            </div>\n            <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;">\n              <div style="font-size: 18px; font-weight: bold; color: #67c23a;">${e.successful_steps||e.successfulSteps||e.success_steps||0}</div>\n              <div style="font-size: 12px; color: #666;">成功</div>\n            </div>\n            <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;">\n              <div style="font-size: 18px; font-weight: bold; color: #f56c6c;">${e.failed_steps||e.failedSteps||0}</div>\n              <div style="font-size: 12px; color: #666;">失败</div>\n            </div>\n            <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;">\n              <div style="font-size: 18px; font-weight: bold; color: #e6a23c;">${(1e3*(e.execution_time||e.executionTime||e.duration||0)).toFixed(0)}ms</div>\n              <div style="font-size: 12px; color: #666;">耗时</div>\n            </div>\n          </div>\n        </div>\n\n        ${e.step_results?this.buildStepResults(e.step_results):""}\n\n        ${e.logs?this.buildDebugLogs(e.logs):""}\n      </div>\n    `:'<div style="text-align: center; color: #999;">暂无调试结果</div>'},buildStepResults(e){return`\n      <div style="margin-bottom: 20px;">\n        <h3 style="color: #409eff; margin-bottom: 10px;">🎯 步骤执行结果</h3>\n        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">\n          ${e.map((e,t)=>`\n            <div style="margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid ${"success"===e.status?"#67c23a":"#f56c6c"};">\n              <div style="font-weight: 500; margin-bottom: 5px;">\n                ${"success"===e.status?"✅":"failed"===e.status?"❌":"⏭️"} 步骤${t+1}: ${e.step_name||"未命名"}\n              </div>\n              <div style="font-size: 12px; color: #666;">\n                <div>执行时间: ${(1e3*(e.execution_time||e.duration||0)).toFixed(0)}ms</div>\n                <div>状态: ${e.status||"未知"}</div>\n                ${e.message?`<div>信息: ${e.message}</div>`:""}\n                ${e.details&&e.details.status_code?`<div>响应码: ${e.details.status_code}</div>`:""}\n                ${"failed"===e.status&&e.message?`<div style="color: #f56c6c;">错误: ${e.message}</div>`:""}\n              </div>\n            </div>\n          `).join("")}\n        </div>\n      </div>\n    `},buildDebugLogs(e){return`\n      <div style="margin-bottom: 20px;">\n        <h3 style="color: #409eff; margin-bottom: 10px;">📝 执行日志</h3>\n        <div style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">\n          ${e.map(e=>`<div style="margin-bottom: 2px;">[${e.timestamp}] ${e.level}: ${e.message}</div>`).join("")}\n        </div>\n      </div>\n    `},async clickRun(){if(!this.envId)return void this.$message({type:"warning",message:"当前未选中执行环境!",duration:1e3});const e={taskId:this.perfTask.id,env:this.envId},t=await this.$api.runTask(this.perfTask.id,e);200===t.status&&(0,R.df)({title:"任务已启动",message:"请前往报告列表查看结果",type:"success",duration:3e3,showClose:!0,position:"top-right"})}},computed:{...(0,N.aH)(["pro","perfTask","envId","testEnvs"]),defaultProps(){return{children:"children",label:"name"}},selectTaskType:{get(){return this.form.taskType.toString()},set(e){this.form.taskType=e}},username(){return window.sessionStorage.getItem("username")}},mounted(){},watch:{},created(){this.getScenes(),setTimeout(()=>{this.getTaskScenceStep(this.scenceId)},500)}};const ol=(0,At.A)(nl,[["render",M],["__scopeId","data-v-491b2273"]]);var il=ol}}]);
//# sourceMappingURL=520.74183081.js.map