"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[484],{67247:function(e,n,t){t.r(n),t.d(n,{default:function(){return $}});var l=t(56768),a=t(24232);const s={class:"test-env-container"},i={class:"env-sidebar"},o={class:"sidebar-header"},d={class:"sidebar-title"},c={class:"env-list"},r=["onClick"],v={class:"env-icon"},u={class:"env-name"},b={key:1,class:"env-list-empty"},h={class:"main-content"},f={class:"env-header"},p={class:"env-header-info"},k={class:"env-title"},g={class:"env-subtitle"},E={class:"env-actions"},m={class:"env-content"},_={class:"card-header"},F={class:"form-group"},I={class:"form-group"},y={class:"config-grid"},L={class:"card-header"},C={class:"editor-wrapper"},V={class:"editor-wrapper"},S={class:"card-header"},w={class:"editor-wrapper"},O={class:"editor-wrapper"},A={class:"card-header"},J={class:"editor-wrapper"},N={key:1,class:"env-empty"};function T(e,n,t,T,P,x){const U=(0,l.g2)("Monitor"),D=(0,l.g2)("el-icon"),X=(0,l.g2)("el-button"),j=(0,l.g2)("el-tooltip"),M=(0,l.g2)("Connection"),W=(0,l.g2)("el-empty"),$=(0,l.g2)("el-scrollbar"),z=(0,l.g2)("Link"),B=(0,l.g2)("el-button-group"),K=(0,l.g2)("InfoFilled"),Q=(0,l.g2)("EditPen"),H=(0,l.g2)("el-input"),q=(0,l.g2)("el-card"),G=(0,l.g2)("Setting"),R=(0,l.g2)("Editor"),Y=(0,l.g2)("el-tab-pane"),Z=(0,l.g2)("el-tabs"),ee=(0,l.g2)("Paperclip"),ne=(0,l.g2)("Star"),te=(0,l.g2)("Plus");return(0,l.uX)(),(0,l.CE)("div",s,[(0,l.Lk)("div",i,[(0,l.Lk)("div",o,[(0,l.Lk)("div",d,[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(U)]),_:1}),n[9]||(n[9]=(0,l.Lk)("h2",null,"测试环境",-1))]),(0,l.bF)(j,{content:"添加新测试环境",placement:"right",effect:"light"},{default:(0,l.k6)(()=>[(0,l.bF)(X,{onClick:x.addEnv,type:"primary",class:"add-env-btn",round:"",icon:e.Plus},{default:(0,l.k6)(()=>n[10]||(n[10]=[(0,l.eW)(" 添加环境 ")])),_:1,__:[10]},8,["onClick","icon"])]),_:1})]),(0,l.bF)($,{class:"env-list-scrollbar"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",c,[e.testEnvs.length>0?((0,l.uX)(!0),(0,l.CE)(l.FK,{key:0},(0,l.pI)(e.testEnvs,e=>((0,l.uX)(),(0,l.CE)("div",{key:e.id,onClick:n=>x.selectEnv(e),class:(0,a.C4)(["env-item",{"env-item-active":P.active===e.id.toString()}])},[(0,l.Lk)("div",v,[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(M)]),_:1})]),(0,l.Lk)("span",u,(0,a.v_)(e.name),1)],10,r))),128)):((0,l.uX)(),(0,l.CE)("div",b,[(0,l.bF)(W,{"image-size":64,description:"无环境配置"})]))])]),_:1})]),(0,l.Lk)("div",h,[P.EnvInfo?((0,l.uX)(),(0,l.CE)(l.FK,{key:0},[(0,l.Lk)("div",f,[(0,l.Lk)("div",p,[(0,l.Lk)("div",k,[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(U)]),_:1}),(0,l.Lk)("h2",null,(0,a.v_)(P.EnvInfo.name),1)]),(0,l.Lk)("div",g,[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(z)]),_:1}),(0,l.Lk)("span",null,(0,a.v_)(P.EnvInfo.host||"未设置服务器地址"),1)])]),(0,l.Lk)("div",E,[(0,l.bF)(B,null,{default:(0,l.k6)(()=>[(0,l.bF)(j,{content:"保存环境配置",placement:"top"},{default:(0,l.k6)(()=>[(0,l.bF)(X,{onClick:x.saveEnv,type:"primary",icon:e.Check},{default:(0,l.k6)(()=>n[11]||(n[11]=[(0,l.eW)("保存")])),_:1,__:[11]},8,["onClick","icon"])]),_:1}),(0,l.bF)(j,{content:"复制环境配置",placement:"top"},{default:(0,l.k6)(()=>[(0,l.bF)(X,{onClick:x.copyEnv,type:"info",icon:e.CopyDocument},{default:(0,l.k6)(()=>n[12]||(n[12]=[(0,l.eW)("复制")])),_:1,__:[12]},8,["onClick","icon"])]),_:1}),(0,l.bF)(j,{content:"删除环境配置",placement:"top"},{default:(0,l.k6)(()=>[(0,l.bF)(X,{onClick:x.delEnv,type:"danger",icon:e.Delete},{default:(0,l.k6)(()=>n[13]||(n[13]=[(0,l.eW)("删除")])),_:1,__:[13]},8,["onClick","icon"])]),_:1})]),_:1})])]),(0,l.bF)($,{class:"env-content-scroll"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",m,[(0,l.bF)(q,{class:"config-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",_,[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(K)]),_:1}),n[14]||(n[14]=(0,l.Lk)("span",null,"基本信息",-1))])]),default:(0,l.k6)(()=>[(0,l.Lk)("div",F,[(0,l.bF)(H,{modelValue:P.EnvInfo.name,"onUpdate:modelValue":n[0]||(n[0]=e=>P.EnvInfo.name=e),placeholder:"环境名称",size:"default"},{prepend:(0,l.k6)(()=>[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(Q)]),_:1}),n[15]||(n[15]=(0,l.Lk)("span",{class:"prepend-text"},"环境名称",-1))]),_:1},8,["modelValue"])]),(0,l.Lk)("div",I,[(0,l.bF)(H,{modelValue:P.EnvInfo.host,"onUpdate:modelValue":n[1]||(n[1]=e=>P.EnvInfo.host=e),placeholder:"服务器域名或IP地址",size:"default"},{prepend:(0,l.k6)(()=>[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(z)]),_:1}),n[16]||(n[16]=(0,l.Lk)("span",{class:"prepend-text"},"服务器地址",-1))]),_:1},8,["modelValue"])])]),_:1}),(0,l.Lk)("div",y,[(0,l.bF)(q,{class:"config-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",L,[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(G)]),_:1}),n[17]||(n[17]=(0,l.Lk)("span",null,"环境配置",-1))])]),default:(0,l.k6)(()=>[(0,l.bF)(Z,{type:"border-card",class:"env-tabs",stretch:"",modelValue:P.headersActiveTab,"onUpdate:modelValue":n[4]||(n[4]=e=>P.headersActiveTab=e)},{default:(0,l.k6)(()=>[(0,l.bF)(Y,{label:"全局请求头",name:"headers"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",C,[(0,l.bF)(R,{modelValue:P.EnvInfo.headers,"onUpdate:modelValue":n[2]||(n[2]=e=>P.EnvInfo.headers=e),height:"260px",theme:"chrome",lang:"json"},null,8,["modelValue"])])]),_:1}),(0,l.bF)(Y,{label:"数据库配置",name:"db"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",V,[(0,l.bF)(R,{modelValue:P.EnvInfo.db,"onUpdate:modelValue":n[3]||(n[3]=e=>P.EnvInfo.db=e),height:"260px",theme:"chrome",lang:"json"},null,8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,l.bF)(q,{class:"config-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",S,[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(ee)]),_:1}),n[18]||(n[18]=(0,l.Lk)("span",null,"全局变量",-1))])]),default:(0,l.k6)(()=>[(0,l.bF)(Z,{type:"border-card",class:"env-tabs",stretch:"",modelValue:P.globalActiveTab,"onUpdate:modelValue":n[7]||(n[7]=e=>P.globalActiveTab=e)},{default:(0,l.k6)(()=>[(0,l.bF)(Y,{label:"全局变量",name:"global"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",w,[(0,l.bF)(R,{modelValue:P.EnvInfo.global_variable,"onUpdate:modelValue":n[5]||(n[5]=e=>P.EnvInfo.global_variable=e),height:"260px",theme:"chrome",lang:"json"},null,8,["modelValue"])])]),_:1}),(0,l.bF)(Y,{label:"调试运行变量",name:"debug"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",O,[(0,l.bF)(R,{modelValue:P.EnvInfo.debug_global_variable,"onUpdate:modelValue":n[6]||(n[6]=e=>P.EnvInfo.debug_global_variable=e),height:"260px",theme:"chrome",lang:"json"},null,8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),(0,l.bF)(q,{class:"config-card function-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",A,[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(ne)]),_:1}),n[19]||(n[19]=(0,l.Lk)("span",null,"全局函数",-1))])]),default:(0,l.k6)(()=>[(0,l.Lk)("div",J,[(0,l.bF)(R,{modelValue:P.EnvInfo.global_func,"onUpdate:modelValue":n[8]||(n[8]=e=>P.EnvInfo.global_func=e),height:"600px",lang:"python",theme:"monokai"},null,8,["modelValue"])])]),_:1})])]),_:1})],64)):(0,l.Q3)("",!0),0===e.testEnvs.length?((0,l.uX)(),(0,l.CE)("div",N,[(0,l.bF)(W,{description:"暂无测试环境"},{default:(0,l.k6)(()=>[(0,l.bF)(X,{type:"primary",onClick:x.addEnv,round:""},{default:(0,l.k6)(()=>[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(te)]),_:1}),n[20]||(n[20]=(0,l.Lk)("span",null,"添加测试环境",-1))]),_:1,__:[20]},8,["onClick"])]),_:1})])):(0,l.Q3)("",!0)])])}t(18111),t(20116);var P=t(60782),x=t(53629),U=t(51219),D=t(12933),X=t(57477),j={components:{Editor:x.A,Check:X.Check,Delete:X.Delete,CopyDocument:X.CopyDocument,EditPen:X.EditPen,Link:X.Link,Monitor:X.Monitor,Setting:X.Setting,Plus:X.Plus,Connection:X.Connection,CircleCheck:X.CircleCheck,InfoFilled:X.InfoFilled,Paperclip:X.Paperclip,Star:X.Star},data(){return{active:"1",EnvInfo:null,headersActiveTab:"headers",globalActiveTab:"global"}},computed:{...(0,P.aH)(["pro","testEnvs","envInfo"])},methods:{...(0,P.i0)(["getAllEnvs"]),async addEnv(){const e={project:this.pro.id,name:"新测试环境"},n=await this.$api.createTestEnv(e);if(201===n.status&&((0,U.nk)({type:"success",message:"添加成功",duration:1e3}),await this.getAllEnvs(),this.testEnvs.length>0)){const e=this.testEnvs.find(e=>"新测试环境"===e.name)||this.testEnvs[this.testEnvs.length-1];this.selectEnv(e)}},async delEnv(){D.s.confirm("确定要删除该测试环境吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",draggable:!0,closeOnClickModal:!1}).then(async()=>{const e=await this.$api.deleteTestEnv(this.EnvInfo.id);204===e.status&&((0,U.nk)({type:"success",message:"删除成功",duration:1e3}),await this.getAllEnvs(),this.testEnvs.length>0?(this.active=this.testEnvs[0].id.toString(),this.selectEnv(this.testEnvs[0])):this.EnvInfo=null)}).catch(()=>{(0,U.nk)({type:"info",message:"已取消删除",duration:1e3})})},async saveEnv(){try{let e={...this.EnvInfo};e.headers=JSON.parse(this.EnvInfo.headers),e.db=JSON.parse(this.EnvInfo.db),e.debug_global_variable=JSON.parse(this.EnvInfo.debug_global_variable),e.global_variable=JSON.parse(this.EnvInfo.global_variable);const n=await this.$api.updateTestEnv(e.id,e);200===n.status&&((0,U.nk)({type:"success",message:"保存成功",duration:1e3}),await this.getAllEnvs())}catch(e){(0,U.nk)({type:"error",message:"JSON格式错误，请检查配置",duration:2e3})}},async copyEnv(){try{let e={...this.EnvInfo};e.name=e.name+"_副本",e.headers=JSON.parse(this.EnvInfo.headers),e.db=JSON.parse(this.EnvInfo.db),e.debug_global_variable=JSON.parse(this.EnvInfo.debug_global_variable),e.global_variable=JSON.parse(this.EnvInfo.global_variable);const n=await this.$api.createTestEnv(e);if(201===n.status&&((0,U.nk)({type:"success",message:"复制成功",duration:1e3}),await this.getAllEnvs(),this.testEnvs.length>0)){const n=this.testEnvs.find(n=>n.name===e.name)||this.testEnvs[this.testEnvs.length-1];this.selectEnv(n)}}catch(e){(0,U.nk)({type:"error",message:"JSON格式错误，请检查配置",duration:2e3})}},selectEnv(e){this.active=e.id.toString(),this.EnvInfo={...e},this.EnvInfo.headers=JSON.stringify(this.EnvInfo.headers,null,4),this.EnvInfo.db=JSON.stringify(this.EnvInfo.db,null,4),this.EnvInfo.debug_global_variable=JSON.stringify(this.EnvInfo.debug_global_variable,null,4),this.EnvInfo.global_variable=JSON.stringify(this.EnvInfo.global_variable,null,4)}},created(){this.getAllEnvs()},mounted(){this.testEnvs.length>0&&(this.envInfo?this.selectEnv(this.envInfo):this.selectEnv(this.testEnvs[0]))}},M=t(71241);const W=(0,M.A)(j,[["render",T],["__scopeId","data-v-0b5dfd14"]]);var $=W}}]);
//# sourceMappingURL=484.ef522f0e.js.map