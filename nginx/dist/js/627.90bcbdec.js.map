{"version": 3, "file": "js/627.90bcbdec.js", "mappings": "gTAmCA,MAAMA,GAAMC,EAAAA,EAAAA,IAAI,MAChB,IAAIC,EAAQ,MACZC,EAAAA,EAAAA,IAAU,KACNC,MAOJ,MAAMA,EAAkBA,KACpBJ,EAAIK,MAAMC,MAAQN,EAAIK,MAAME,WAAWC,YACvCR,EAAIK,MAAMI,OAAST,EAAIK,MAAME,WAAWG,aAGxC,IAAIC,EAAY,UACZC,EAAS,UACTC,EAAQ,UACRC,EAAe,UACfC,EAAc,UACdC,EAAS,UAGTC,EAAO,IAAIC,IAAAA,cAAkB,CAE7BC,QAASnB,EAAIK,MACbe,YAAY,EACZC,KAAM,MAKNC,EAAO,IAAIJ,IAAAA,aAAiB,CAC5BK,MAAON,EACPX,MAAO,IACPG,OAAQ,IACRe,MAAO,QACPC,MAAM,EACNC,aAAc,GACdC,OAAQ,KAIZ,IAAIT,IAAAA,aAAiB,CACjBK,MAAOD,EACPhB,MAAO,IACPG,OAAQ,IACRe,MAAOZ,EACPa,MAAM,EACNC,aAAc,GACdC,OAAQ,IACRC,UAAW,CAAEC,GAAI,GAAIC,GAAI,MAI7B,IAAIC,EAAM,IAAIb,IAAAA,aAAiB,CAC3BK,MAAOD,EACPb,OAAQ,GACRH,MAAO,GACPqB,OAAQ,GACRF,MAAM,EACND,MAAOT,EACPa,UAAW,CAAEI,GAAI,IAAKF,GAAI,IAC1BJ,aAAc,MAGlB,IAAIR,IAAAA,aAAiB,CACjBK,MAAOQ,EACPtB,OAAQ,IACRH,MAAO,GACPqB,OAAQ,GACRF,MAAM,EACND,MAAOX,EACPe,UAAW,CAAEE,EAAG,KAChBJ,aAAc,MAIlB,IAAIO,EAAa,IAAIf,IAAAA,OAAW,CAC5BK,MAAOQ,EACPG,KAAM,CAAC,CAAEF,GAAI,IAAM,CAAEA,EAAG,KACxBL,OAAQ,GACRH,MAAOV,EACPc,UAAW,CAAEE,EAAG,OAGpBG,EAAWE,KAAK,CACZX,MAAOT,EACPa,UAAW,CAAEE,EAAG,OAIpB,IAAIZ,IAAAA,aAAiB,CACjBK,MAAOU,EACPxB,OAAQ,GACRH,MAAO,GACPsB,UAAW,CAAEI,GAAI,EAAGF,EAAG,IACvBL,MAAM,EACND,MAAOR,EACPW,OAAQ,KAGZ,IAAIT,IAAAA,aAAiB,CACjBK,MAAOU,EACPxB,OAAQ,GACRH,MAAO,EACPsB,UAAW,CAAEI,EAAG,GAAIF,EAAG,IACvBL,MAAM,EACND,MAAOZ,EACPe,OAAQ,KAGZI,EAAIK,UAAU,CACVR,UAAW,CAAEI,EAAG,IAAKF,GAAI,IACzBO,OAAQ,CAAEP,EAAGZ,IAAAA,IAAW,KAI5B,IAAIoB,EAAM,IAAIpB,IAAAA,aAAiB,CAC3BK,MAAOD,EACPb,OAAQ,IACRH,MAAO,GACPqB,OAAQ,GACRF,MAAM,EACND,MAAOX,EACPe,UAAW,CAAEI,GAAI,GAAIF,EAAG,KACxBJ,aAAc,MAIda,EAAa,IAAIrB,IAAAA,OAAW,CAC5BK,MAAOe,EACPJ,KAAM,CAAC,CAAEF,GAAI,IAAM,CAAEA,EAAG,KACxBL,OAAQ,GACRH,MAAOV,EACPc,UAAW,CAAEE,EAAG,OAGpBS,EAAWJ,KAAK,CACZX,MAAOT,EACPa,UAAW,CAAEE,EAAG,OAIpB,IAAIZ,IAAAA,aAAiB,CACjBK,MAAOe,EACPhC,MAAO,GACPG,OAAQ,GACRkB,OAAQ,GACRF,MAAM,EACND,MAAOR,EACPY,UAAW,CAAEI,GAAI,GAAIF,EAAG,KACxBJ,aAAc,KAGlBY,EAAIF,UAAU,CACVR,UAAW,CAAEI,EAAG,GAAIF,EAAG,KACvBO,OAAQ,CAAEP,EAAGZ,IAAAA,IAAW,KAK5B,IAAIsB,EAAO,IAAItB,IAAAA,aAAiB,CAC5BK,MAAOD,EACPhB,MAAO,IACPG,OAAQ,IACRgC,MAAO,GACPf,aAAc,GACdC,OAAQ,GACRH,MAAOX,EACPY,MAAM,EACNG,UAAW,CAAEE,GAAI,OAIjBY,EAAS,IAAIxB,IAAAA,aAAiB,CAC9BK,MAAOiB,EACPlC,MAAO,IACPG,OAAQ,IACRiB,aAAc,GACdF,MAAOb,EACPc,MAAM,EACNkB,UAAU,EACVf,UAAW,CAAEC,EAAG,MAIpB,IAAIX,IAAAA,MAAU,CACVK,MAAOmB,EACPpC,MAAO,GACPG,OAAQ,EACRkB,OAAQ,GACRC,UAAW,CAAEI,EAAG,GAAIF,GAAI,GAAID,EAAG,IAC/BL,MAAO,QACPmB,UAAU,IAId,IAAIC,EAAM,IAAI1B,IAAAA,aAAiB,CAC3BK,MAAOiB,EACPlC,MAAO,GACPG,OAAQ,GACRiB,aAAc,GACdC,OAAQ,GACRH,MAAOZ,EACPa,MAAM,EACNG,UAAW,CAAEI,GAAI,OAGrBY,EAAIT,KAAK,CACLP,UAAW,CAAEI,EAAG,OAIpB,IAAIa,EAAO,IAAI3B,IAAAA,OAAW,CACtBK,MAAOiB,EACPN,KAAM,CAAC,CAAEF,GAAI,KAAO,CAAEA,EAAG,MACzBJ,UAAW,CAAEE,EAAG,KAChBH,OAAQ,GACRH,MAAOV,IAGX+B,EAAKV,KAAK,CACNP,UAAW,CAAEE,EAAG,KAChBN,MAAOT,IAIX,IAAI+B,EAAW,IAAI5B,IAAAA,OAAW,CAC1BK,MAAOD,EACPY,KAAM,CAAC,CAAEF,GAAI,IAAM,CAAEA,EAAG,KACxBL,OAAQ,GACRC,UAAW,CAAEI,EAAG,IAAKH,EAAG,KACxBL,MAAOR,IAyCX,SAAS+B,IAEL9B,EAAKoB,OAAOP,GAAK,KACjBb,EAAKoB,OAAOL,GAAK,KACjBf,EAAKoB,OAAOR,GAAK,KACjBZ,EAAK+B,oBAEL9C,EAAQ+C,sBAAsBF,EAClC,CA9CAD,EAASX,KAAK,CACVP,UAAW,CAAEI,EAAG,IAAKF,EAAG,IAAKD,GAAI,KACjCL,MAAOR,IAGX8B,EAASX,KAAK,CACVP,UAAW,CAAEI,GAAI,IAAKF,EAAG,IAAKD,GAAI,KAClCL,MAAO,UAGXsB,EAASX,KAAK,CACVP,UAAW,CAAEI,GAAI,IAAKF,EAAG,IAAKD,GAAI,KAClCL,MAAOV,IAGXgC,EAASX,KAAK,CACVP,UAAW,CAAEI,EAAG,GAAIF,GAAI,GAAID,EAAG,KAC/BL,MAAOZ,IAGXkC,EAASX,KAAK,CACVP,UAAW,CAAEI,GAAI,IAAKF,EAAG,GAAID,EAAG,KAChCL,MAAOV,IAGXgC,EAASX,KAAK,CACVP,UAAW,CAAEI,GAAI,IAAKF,GAAI,IAAKD,EAAG,KAClCL,MAAOT,IAGX+B,EAASX,KAAK,CACVP,UAAW,CAAEI,EAAG,IAAKF,GAAI,IAAKD,GAAI,KAClCL,MAAO,UAIXP,EAAK+B,oBAaLD,K,OAEJG,EAAAA,EAAAA,IAAY,KACRC,qBAAqBjD,GACrBA,EAAQ,O,+EAlURkD,EAAAA,EAAAA,IA2BM,MA3BNC,EA2BM,C,0eAhBFC,EAAAA,EAAAA,IAYOC,EAAAC,OAAA,aAZP,IAYO,EAXHC,EAAAA,EAAAA,IAUM,MAVNC,EAUM,C,aATFD,EAAAA,EAAAA,IAAmC,OAA9BE,MAAM,gBAAe,OAAG,I,aAC7BF,EAAAA,EAAAA,IAAoD,OAA/CE,MAAM,mBAAkB,qBAAiB,I,aAC9CF,EAAAA,EAAAA,IAAmD,OAA9CE,MAAM,sBAAqB,iBAAa,KAC7CC,EAAAA,EAAAA,IAIcC,EAAA,CAJDC,GAAG,KAAG,C,iBACf,IAESC,EAAA,KAAAA,EAAA,KAFTN,EAAAA,EAAAA,IAES,UAFDE,MAAM,uCAAsC,UAEpD,M,uCAKZP,EAAAA,EAAAA,IAEM,MAFNY,EAEM,EADFP,EAAAA,EAAAA,IAA2B,U,QAAf,MAAJxD,IAAID,G,yBAD2BI,Q,cCpBnD,MAAM6D,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://frontend-web/./src/views/404.vue", "webpack://frontend-web/./src/views/404.vue?16d8"], "sourcesContent": ["\r\n<template>\r\n    <div class=\"ve_404\">\r\n        <!-- partial:index.partial.html -->\r\n        <div class=\"moon\"></div>\r\n        <div class=\"moon__crater moon__crater1\"></div>\r\n        <div class=\"moon__crater moon__crater2\"></div>\r\n        <div class=\"moon__crater moon__crater3\"></div>\r\n        <div class=\"star star1\">⭐</div>\r\n        <div class=\"star star2\">⭐</div>\r\n        <div class=\"star star3\">⭐</div>\r\n        <div class=\"star star4\">⭐</div>\r\n        <div class=\"star star5\">⭐</div>\r\n        <slot>\r\n            <div class=\"error\">\r\n                <div class=\"error__title\">404</div>\r\n                <div class=\"error__subtitle\">🐱🐱🐱(⓿_⓿)🐱🐱🐱</div>\r\n                <div class=\"error__description\">看来你是迷路了......</div>\r\n                <router-link to=\"/\">\r\n                    <button class=\"error__button error__button--active\">\r\n                        回到首页\r\n                    </button>\r\n                </router-link>\r\n                <!-- <button class=\"error__button\">CONTACT</button> -->\r\n            </div>\r\n        </slot>\r\n        <div class=\"astronaut\" v-resize=\"{ resize: draw3dAstronaut }\">\r\n            <canvas ref=\"cav\"></canvas>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport Zdog from \"zdog\";\r\nimport { ref, onUnmounted, onMounted } from \"vue\";\r\nconst cav = ref(null);\r\nlet timer = null;\r\nonMounted(() => {\r\n    draw3dAstronaut();\r\n});\r\n/**\r\n * @description: 画3d太空人\r\n * @param {*}\r\n * @return {*}\r\n */\r\nconst draw3dAstronaut = () => {\r\n    cav.value.width = cav.value.parentNode.clientWidth;\r\n    cav.value.height = cav.value.parentNode.clientHeight;\r\n\r\n    // colours\r\n    let dark_navy = \"#131e38\";\r\n    let orange = \"#fe9642\";\r\n    let cream = \"#FFF8E7\";\r\n    let light_purple = \"#7f3f98\";\r\n    let dark_purple = \"#563795\";\r\n    let cheese = \"#fbc715\";\r\n\r\n    // create illo\r\n    let illo = new Zdog.Illustration({\r\n        // set canvas with selector\r\n        element: cav.value,\r\n        dragRotate: true,\r\n        zoom: 0.65,\r\n    });\r\n\r\n    /** Body **/\r\n    // Body\r\n    let body = new Zdog.RoundedRect({\r\n        addTo: illo,\r\n        width: 200,\r\n        height: 220,\r\n        color: \"white\",\r\n        fill: true,\r\n        cornerRadius: 16,\r\n        stroke: 60,\r\n    });\r\n\r\n    // Backpack\r\n    new Zdog.RoundedRect({\r\n        addTo: body,\r\n        width: 180,\r\n        height: 310,\r\n        color: orange,\r\n        fill: true,\r\n        cornerRadius: 24,\r\n        stroke: 120,\r\n        translate: { z: -85, y: -60 },\r\n    });\r\n\r\n    /** arm **/\r\n    let arm = new Zdog.RoundedRect({\r\n        addTo: body,\r\n        height: 30,\r\n        width: 28,\r\n        stroke: 60,\r\n        fill: true,\r\n        color: dark_purple,\r\n        translate: { x: -140, y: -64 },\r\n        cornerRadius: 0.05,\r\n    });\r\n\r\n    new Zdog.RoundedRect({\r\n        addTo: arm,\r\n        height: 120,\r\n        width: 12,\r\n        stroke: 60,\r\n        fill: true,\r\n        color: cream,\r\n        translate: { y: 120 },\r\n        cornerRadius: 0.05,\r\n    });\r\n\r\n    // bubble_arm\r\n    let bubble_arm = new Zdog.Shape({\r\n        addTo: arm,\r\n        path: [{ x: -20 }, { x: 20 }],\r\n        stroke: 32,\r\n        color: light_purple,\r\n        translate: { y: 210 },\r\n    });\r\n\r\n    bubble_arm.copy({\r\n        color: dark_purple,\r\n        translate: { y: 230 },\r\n    });\r\n\r\n    // hand\r\n    new Zdog.RoundedRect({\r\n        addTo: bubble_arm,\r\n        height: 32,\r\n        width: 22,\r\n        translate: { x: -8, y: 60 },\r\n        fill: true,\r\n        color: cheese,\r\n        stroke: 30,\r\n    });\r\n\r\n    new Zdog.RoundedRect({\r\n        addTo: bubble_arm,\r\n        height: 24,\r\n        width: 0,\r\n        translate: { x: 24, y: 50 },\r\n        fill: true,\r\n        color: orange,\r\n        stroke: 20,\r\n    });\r\n\r\n    arm.copyGraph({\r\n        translate: { x: 140, y: -64 },\r\n        rotate: { y: Zdog.TAU / 2 },\r\n    });\r\n\r\n    /** Leg **/\r\n    let leg = new Zdog.RoundedRect({\r\n        addTo: body,\r\n        height: 160,\r\n        width: 28,\r\n        stroke: 60,\r\n        fill: true,\r\n        color: cream,\r\n        translate: { x: -56, y: 230 },\r\n        cornerRadius: 0.05,\r\n    });\r\n\r\n    // bubble_leg\r\n    let bubble_leg = new Zdog.Shape({\r\n        addTo: leg,\r\n        path: [{ x: -28 }, { x: 28 }],\r\n        stroke: 32,\r\n        color: light_purple,\r\n        translate: { y: 100 },\r\n    });\r\n\r\n    bubble_leg.copy({\r\n        color: dark_purple,\r\n        translate: { y: 124 },\r\n    });\r\n\r\n    // foot\r\n    new Zdog.RoundedRect({\r\n        addTo: leg,\r\n        width: 96,\r\n        height: 24,\r\n        stroke: 40,\r\n        fill: true,\r\n        color: cheese,\r\n        translate: { x: -24, y: 170 },\r\n        cornerRadius: 24,\r\n    });\r\n\r\n    leg.copyGraph({\r\n        translate: { x: 56, y: 230 },\r\n        rotate: { y: Zdog.TAU / 2 },\r\n    });\r\n\r\n    /** Head **/\r\n    // Head\r\n    let head = new Zdog.RoundedRect({\r\n        addTo: body,\r\n        width: 216,\r\n        height: 180,\r\n        depth: 40,\r\n        cornerRadius: 80,\r\n        stroke: 60,\r\n        color: cream,\r\n        fill: true,\r\n        translate: { y: -300 },\r\n    });\r\n\r\n    //add helmet\r\n    let helmet = new Zdog.RoundedRect({\r\n        addTo: head,\r\n        width: 210,\r\n        height: 165,\r\n        cornerRadius: 64,\r\n        color: dark_navy,\r\n        fill: true,\r\n        backface: false,\r\n        translate: { z: 20 },\r\n    });\r\n\r\n    //add refletion\r\n    new Zdog.Rect({\r\n        addTo: helmet,\r\n        width: 48,\r\n        height: 2,\r\n        stroke: 10,\r\n        translate: { x: 24, y: -24, z: 10 },\r\n        color: \"white\",\r\n        backface: false,\r\n    });\r\n\r\n    // add ear\r\n    let ear = new Zdog.RoundedRect({\r\n        addTo: head,\r\n        width: 36,\r\n        height: 72,\r\n        cornerRadius: 80,\r\n        stroke: 20,\r\n        color: orange,\r\n        fill: true,\r\n        translate: { x: -140 },\r\n    });\r\n\r\n    ear.copy({\r\n        translate: { x: 140 },\r\n    });\r\n\r\n    // neck\r\n    let neck = new Zdog.Shape({\r\n        addTo: head,\r\n        path: [{ x: -110 }, { x: 110 }],\r\n        translate: { y: 120 },\r\n        stroke: 40,\r\n        color: light_purple,\r\n    });\r\n\r\n    neck.copy({\r\n        translate: { y: 160 },\r\n        color: dark_purple,\r\n    });\r\n\r\n    /** extra **/\r\n    let stripe_1 = new Zdog.Shape({\r\n        addTo: body,\r\n        path: [{ x: -20 }, { x: 20 }],\r\n        stroke: 10,\r\n        translate: { x: 200, z: 200 },\r\n        color: cheese,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: 320, y: 200, z: -400 },\r\n        color: cheese,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: -220, y: 300, z: -400 },\r\n        color: \"white\",\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: -100, y: 400, z: -280 },\r\n        color: light_purple,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: 50, y: -60, z: 150 },\r\n        color: orange,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: -250, y: 80, z: 300 },\r\n        color: light_purple,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: -350, y: -280, z: 175 },\r\n        color: dark_purple,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: 250, y: -380, z: -175 },\r\n        color: \"white\",\r\n    });\r\n\r\n    // update & render\r\n    illo.updateRenderGraph();\r\n\r\n    function animate() {\r\n        // rotate illo each frame\r\n        illo.rotate.y += 0.005;\r\n        illo.rotate.x += 0.005;\r\n        illo.rotate.z += 0.005;\r\n        illo.updateRenderGraph();\r\n        // animate next frame\r\n        timer = requestAnimationFrame(animate);\r\n    }\r\n\r\n    // start animation\r\n    animate();\r\n};\r\nonUnmounted(() => {\r\n    cancelAnimationFrame(timer);\r\n    timer = null;\r\n});\r\n</script>\r\n\r\n<style  scoped>\r\n.ve_404 {\r\n    height: 100vh;\r\n    width: 100vw;\r\n    position: relative;\r\n    overflow: hidden;\r\n    background: linear-gradient(90deg, #2f3640 23%, #181b20 100%);\r\n}\r\n.moon {\r\n    background: linear-gradient(90deg, #d0d0d0 48%, #919191 100%);\r\n    position: absolute;\r\n    top: -30vh;\r\n    left: -80vh;\r\n    width: 160vh;\r\n    height: 160%;\r\n    content: \"\";\r\n    border-radius: 50%;\r\n    box-shadow: 0px 0px 30px -4px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.moon__crater {\r\n    position: absolute;\r\n    content: \"\";\r\n    border-radius: 100%;\r\n    background: linear-gradient(90deg, #7a7a7a 38%, #c3c3c3 100%);\r\n    opacity: 0.6;\r\n}\r\n\r\n.moon__crater1 {\r\n    top: 250px;\r\n    left: 500px;\r\n    width: 60px;\r\n    height: 180px;\r\n}\r\n\r\n.moon__crater2 {\r\n    top: 650px;\r\n    left: 340px;\r\n    width: 40px;\r\n    height: 80px;\r\n    transform: rotate(55deg);\r\n}\r\n\r\n.moon__crater3 {\r\n    top: -20px;\r\n    left: 40px;\r\n    width: 65px;\r\n    height: 120px;\r\n    transform: rotate(250deg);\r\n}\r\n\r\n.star {\r\n    color: grey;\r\n    position: absolute;\r\n    width: 10px;\r\n    height: 10px;\r\n    content: \"\";\r\n    border-radius: 100%;\r\n    transform: rotate(250deg);\r\n    opacity: 0.4;\r\n    animation-name: shimmer;\r\n    animation-duration: 1.5s;\r\n    animation-iteration-count: infinite;\r\n    animation-direction: alternate;\r\n}\r\n\r\n@keyframes shimmer {\r\n    from {\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        opacity: 0.7;\r\n    }\r\n}\r\n\r\n.star1 {\r\n    top: 40%;\r\n    left: 50%;\r\n    animation-delay: 1s;\r\n}\r\n\r\n.star2 {\r\n    top: 60%;\r\n    left: 90%;\r\n    animation-delay: 3s;\r\n}\r\n\r\n.star3 {\r\n    top: 10%;\r\n    left: 70%;\r\n    animation-delay: 2s;\r\n}\r\n\r\n.star4 {\r\n    top: 90%;\r\n    left: 40%;\r\n}\r\n\r\n.star5 {\r\n    top: 20%;\r\n    left: 30%;\r\n    animation-delay: 0.5s;\r\n}\r\n\r\n.astronaut {\r\n    position: absolute;\r\n    width: 60vw;\r\n    height: 100vh;\r\n    top: 0;\r\n    right: 0;\r\n    z-index: 0;\r\n}\r\n\r\n.error {\r\n    position: absolute;\r\n    left: 100px;\r\n    top: 400px;\r\n    transform: translateY(-60%);\r\n    font-family: \"Righteous\", cursive;\r\n    color: #363e49;\r\n    z-index: 1;\r\n}\r\n\r\n.error__title {\r\n    font-size: 10em;\r\n    font-weight: bold;\r\n    color: #d0d0d0;\r\n    text-shadow: -5px -5px 0 rgba(0, 0, 0, 0.7);\r\n    background-image: linear-gradient(90deg, #d0d0d0 48%, #919191 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n}\r\n\r\n.error__subtitle {\r\n    font-size: 2em;\r\n}\r\n\r\n.error__description {\r\n    opacity: 0.5;\r\n}\r\n\r\n.error__button {\r\n    min-width: 7em;\r\n    margin-top: 3em;\r\n    margin-right: 0.5em;\r\n    padding: 0.5em 2em;\r\n    outline: none;\r\n    border: 2px solid #2f3640;\r\n    background-color: transparent;\r\n    border-radius: 8em;\r\n    color: #576375;\r\n    cursor: pointer;\r\n    transition-duration: 0.2s;\r\n    font-size: 0.75em;\r\n    font-family: \"Righteous\", cursive;\r\n}\r\n\r\n.error__button:hover {\r\n    color: #21252c;\r\n}\r\n\r\n.error__button--active {\r\n    background-color: $base-color;\r\n    border: 2px solid $base-color;\r\n    color: white;\r\n}\r\n\r\n.error__button--active:hover {\r\n    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.5);\r\n    color: white;\r\n}\r\n</style>\r\n", "import script from \"./404.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./404.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./404.vue?vue&type=style&index=0&id=7c1fa82d&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-7c1fa82d\"]])\n\nexport default __exports__"], "names": ["cav", "ref", "timer", "onMounted", "draw3dAstronaut", "value", "width", "parentNode", "clientWidth", "height", "clientHeight", "dark_navy", "orange", "cream", "light_purple", "dark_purple", "cheese", "illo", "Zdog", "element", "dragRotate", "zoom", "body", "addTo", "color", "fill", "cornerRadius", "stroke", "translate", "z", "y", "arm", "x", "bubble_arm", "path", "copy", "copyGraph", "rotate", "leg", "bubble_leg", "head", "depth", "helmet", "backface", "ear", "neck", "stripe_1", "animate", "updateRenderGraph", "requestAnimationFrame", "onUnmounted", "cancelAnimationFrame", "_createElementBlock", "_hoisted_1", "_renderSlot", "_ctx", "$slots", "_createElementVNode", "_hoisted_2", "class", "_createVNode", "_component_router_link", "to", "_cache", "_hoisted_3", "__exports__"], "sourceRoot": ""}