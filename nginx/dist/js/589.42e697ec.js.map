{"version": 3, "file": "js/589.42e697ec.js", "mappings": "iNAESA,MAAM,uB,GAGJA,MAAM,8B,GACJA,MAAM,a,GAGNA,MAAM,gB,GACJA,MAAM,c,GAKVA,MAAM,8B,GACJA,MAAM,a,GAGNA,MAAM,gB,GACJA,MAAM,c,GAKVA,MAAM,8B,GACJA,MAAM,a,GAGNA,MAAM,gB,GACJA,MAAM,c,GAKVA,MAAM,2B,GACJA,MAAM,a,GAGNA,MAAM,gB,GACJA,MAAM,c,GAMVA,MAAM,0B,GAEJA,MAAM,c,GACJA,MAAM,e,GAEJA,MAAM,iB,GAURA,MAAM,gBAAgBC,IAAI,a,GAI5BD,MAAM,e,GAIJA,MAAM,kB,GAiBFA,MAAM,kB,GAcdA,MAAM,c,GACJA,MAAM,2B,GAEJA,MAAM,kB,GAqBFA,MAAM,a,GASNA,MAAM,a,GAuCNA,MAAM,a,SAS4BA,MAAM,kB,SAIjCA,MAAM,oB,GACXA,MAAM,gB,GAQNA,MAAM,gB,GACJA,MAAM,kB,GAINA,MAAM,mB,GAcVA,MAAM,kB,SASIA,MAAM,iB,guBA7NjCE,EAAAA,EAAAA,IAsOeC,GAAA,CAtODC,OAAO,QAAM,C,iBACzB,IAoOI,EApOJC,EAAAA,EAAAA,IAoOI,MApOJC,EAoOI,EAlOJD,EAAAA,EAAAA,IAwCM,OAxCDL,OAAKO,EAAAA,EAAAA,IAAA,CAAC,kBAAiB,iBAA4BC,EAAAC,c,EACtDJ,EAAAA,EAAAA,IAQM,MARNK,EAQM,EAPJL,EAAAA,EAAAA,IAEM,MAFNM,EAEM,EADJC,EAAAA,EAAAA,IAA+BC,EAAA,M,iBAAtB,IAAY,EAAZD,EAAAA,EAAAA,IAAYE,K,SAEvBT,EAAAA,EAAAA,IAGM,MAHNU,EAGM,EAFJV,EAAAA,EAAAA,IAAgD,MAAhDW,GAAgDC,EAAAA,EAAAA,IAArBC,EAAAC,cAAY,G,aACvCd,EAAAA,EAAAA,IAAmC,OAA9BL,MAAM,cAAa,SAAK,SAIjCK,EAAAA,EAAAA,IAQM,MARNe,EAQM,EAPJf,EAAAA,EAAAA,IAEM,MAFNgB,EAEM,EADJT,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,IAASU,K,SAEpBjB,EAAAA,EAAAA,IAGM,MAHNkB,EAGM,EAFJlB,EAAAA,EAAAA,IAAgD,MAAhDmB,GAAgDP,EAAAA,EAAAA,IAArBC,EAAAO,cAAY,G,aACvCpB,EAAAA,EAAAA,IAAkC,OAA7BL,MAAM,cAAa,QAAI,SAIhCK,EAAAA,EAAAA,IAQM,MARNqB,EAQM,EAPJrB,EAAAA,EAAAA,IAEM,MAFNsB,EAEM,EADJf,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWgB,K,SAEtBvB,EAAAA,EAAAA,IAGM,MAHNwB,EAGM,EAFJxB,EAAAA,EAAAA,IAAgD,MAAhDyB,GAAgDb,EAAAA,EAAAA,IAArBC,EAAAa,cAAY,G,aACvC1B,EAAAA,EAAAA,IAAiC,OAA5BL,MAAM,cAAa,OAAG,SAI/BK,EAAAA,EAAAA,IAQM,MARN2B,EAQM,EAPJ3B,EAAAA,EAAAA,IAEM,MAFN4B,EAEM,EADJrB,EAAAA,EAAAA,IAA+BC,EAAA,M,iBAAtB,IAAY,EAAZD,EAAAA,EAAAA,IAAYsB,K,SAEvB7B,EAAAA,EAAAA,IAGM,MAHN8B,EAGM,EAFJ9B,EAAAA,EAAAA,IAAoD,MAApD+B,GAAoDnB,EAAAA,EAAAA,IAAzBC,EAAAmB,iBAAkB,IAAC,G,aAC9ChC,EAAAA,EAAAA,IAAmC,OAA9BL,MAAM,cAAa,SAAK,S,IAKjCK,EAAAA,EAAAA,IAmDM,MAnDNiC,EAmDM,EAjDJjC,EAAAA,EAAAA,IAcM,MAdNkC,EAcM,EAbJlC,EAAAA,EAAAA,IAWM,MAXNmC,EAWM,C,eAVJnC,EAAAA,EAAAA,IAAgB,UAAZ,WAAO,KACXA,EAAAA,EAAAA,IAQM,MARNoC,EAQM,EAPJ7B,EAAAA,EAAAA,IAMiB8B,EAAA,C,WANQlC,EAAAmC,U,qCAAAnC,EAAAmC,UAASC,GAAEC,KAAK,QAASC,SAAQ5B,EAAA6B,uB,kBACxD,IAAiD,EAAjDnC,EAAAA,EAAAA,IAAiDoC,EAAA,CAAhCC,MAAM,OAAK,C,iBAAC,IAAEC,EAAA,KAAAA,EAAA,K,QAAF,S,cAC7BtC,EAAAA,EAAAA,IAAmDoC,EAAA,CAAlCC,MAAM,QAAM,C,iBAAC,IAAGC,EAAA,KAAAA,EAAA,K,QAAH,U,cAC9BtC,EAAAA,EAAAA,IAAmDoC,EAAA,CAAlCC,MAAM,QAAM,C,iBAAC,IAAGC,EAAA,KAAAA,EAAA,K,QAAH,U,cAC9BtC,EAAAA,EAAAA,IAAqDoC,EAAA,CAApCC,MAAM,SAAO,C,iBAAC,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,cAC/BtC,EAAAA,EAAAA,IAAiDoC,EAAA,CAAhCC,MAAM,OAAK,C,iBAAC,IAAEC,EAAA,MAAAA,EAAA,M,QAAF,S,uDAInC7C,EAAAA,EAAAA,IAAiD,MAAjD8C,EAAiD,aAInD9C,EAAAA,EAAAA,IA+BM,MA/BN+C,EA+BM,C,eA9BJ/C,EAAAA,EAAAA,IAEM,OAFDL,MAAM,eAAa,EACtBK,EAAAA,EAAAA,IAAa,UAAT,U,KAENA,EAAAA,EAAAA,IA0BM,MA1BNgD,EA0BM,EAzBJzC,EAAAA,EAAAA,IAwBU0C,GAAA,CAxBD,iBAAe,MAAMT,KAAK,S,kBACjC,IAce,EAdfjC,EAAAA,EAAAA,IAce2C,GAAA,CAdDN,MAAM,OAAOjD,MAAM,mB,kBAC/B,IAYE,EAZFY,EAAAA,EAAAA,IAYE4C,GAAA,C,WAXShD,EAAAiD,S,qCAAAjD,EAAAiD,SAAQb,GACjBc,KAAK,gBACL,oBAAkB,OAClB,kBAAgB,OAChB,eAAa,sBACZ,eAAclD,EAAAmD,mBACdC,UAAWpD,EAAAoD,UACZ,kBAAgB,IACfC,WAAW,EACZ7D,MAAM,cACL8D,MAAO,CAAAC,SAAA,S,2DAGZ1D,EAAAA,EAAAA,IAOM,MAPN2D,EAOM,EANJpD,EAAAA,EAAAA,IAEYqD,GAAA,CAFDC,MAAA,GAAOC,QAAOjD,EAAAkD,W,kBACvB,IAA8B,EAA9BxD,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWyD,M,6BAAU,U,6BAEhCzD,EAAAA,EAAAA,IAEYqD,GAAA,CAFDP,KAAK,UAAWS,QAAOjD,EAAAoD,Y,kBAChC,IAA6B,EAA7B1D,EAAAA,EAAAA,IAA6BC,EAAA,M,iBAApB,IAAU,EAAVD,EAAAA,EAAAA,IAAU2D,M,6BAAU,U,6CASzClE,EAAAA,EAAAA,IAiIM,MAjINmE,EAiIM,EAhIJnE,EAAAA,EAAAA,IAOM,MAPNoE,EAOM,C,eANJpE,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAIM,MAJNqE,EAIM,EAHJ9D,EAAAA,EAAAA,IAEYqD,GAAA,CAFDpB,KAAK,QAAQa,KAAK,UAAUQ,MAAA,GAAOC,QAAOjD,EAAAyD,Y,kBACnD,IAA+B,EAA/B/D,EAAAA,EAAAA,IAA+BC,EAAA,M,iBAAtB,IAAY,EAAZD,EAAAA,EAAAA,IAAYgE,M,6BAAU,Y,sDAKrC1E,EAAAA,EAAAA,IAsHW2E,GAAA,CArHRC,KAAMtE,EAAAuE,QAEP,uBAAqB,YACrB,0BAAwB,kBACxB,6BAA2B,2BAC3B/E,MAAM,eACL,oBAAmB,CAAAgF,WAAA,UAAAC,MAAA,UAAAC,WAAA,OACpBC,OAAA,I,kBAEA,IAAqF,EAArFvE,EAAAA,EAAAA,IAAqFwE,GAAA,CAApE1B,KAAK,QAAQT,MAAM,KAAKoC,MAAM,KAAKC,MAAM,YAE1D1E,EAAAA,EAAAA,IAOkBwE,GAAA,CAPDnC,MAAM,OAAOqC,MAAM,SAAS,YAAU,O,CAC1CC,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpF,EAAAA,EAAAA,IAGM,MAHNqF,EAGM,EAFJ9E,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,IAAS+E,M,OAClBtF,EAAAA,EAAAA,IAAsD,aAAAY,EAAAA,EAAAA,IAA7C2E,EAAAC,OAAOC,MAAML,EAAMM,IAAIC,cAAW,O,OAKjDpF,EAAAA,EAAAA,IAOkBwE,GAAA,CAPDnC,MAAM,MAAMqC,MAAM,SAAS,YAAU,O,CACzCC,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpF,EAAAA,EAAAA,IAGM,MAHN4F,EAGM,EAFJrF,EAAAA,EAAAA,IAA4FsF,GAAA,CAAhFrD,KAAM,GAAI7C,MAAM,e,kBAAc,IAAsC,E,iBAAnCyF,EAAMM,IAAII,OAAOC,UAAU,EAAG,IAAJ,K,YACvE/F,EAAAA,EAAAA,IAAmC,aAAAY,EAAAA,EAAAA,IAA1BwE,EAAMM,IAAII,QAAM,O,OAK/BvF,EAAAA,EAAAA,IAMkBwE,GAAA,CANDnC,MAAM,KAAKqC,MAAM,SAAS,YAAU,O,CACxCC,SAAOC,EAAAA,EAAAA,IAGPC,GAHc,EACvB7E,EAAAA,EAAAA,IAESyF,GAAA,CAFDC,OAAO,OAAQ5C,KAAMxC,EAAAqF,WAAWd,EAAMM,IAAIS,UAAWxG,MAAM,W,kBACjE,IAAwB,E,iBAArByF,EAAMM,IAAIS,UAAQ,K,6BAK3B5F,EAAAA,EAAAA,IAoBkBwE,GAAA,CApBDnC,MAAM,OAAOqC,MAAM,SAAS,YAAU,O,CAC1CC,SAAOC,EAAAA,EAAAA,IAOPC,GAPc,CAEY,SAA3BA,EAAMM,IAAIU,e,WADlBvG,EAAAA,EAAAA,IAMSmG,GAAA,C,MAJPC,OAAO,QACP5C,KAAK,UACL1D,MAAM,Y,kBACN,IAA2B,EAA3BY,EAAAA,EAAAA,IAA2BC,EAAA,M,iBAAlB,IAAQ,EAARD,EAAAA,EAAAA,IAAQ8F,M,eAAU,KAACzF,EAAAA,EAAAA,IAAGwE,EAAMM,IAAIU,cAAY,K,YAGf,SAA3BhB,EAAMM,IAAIU,e,WADvBvG,EAAAA,EAAAA,IAMSmG,GAAA,C,MAJPC,OAAO,QACP5C,KAAK,UACL1D,MAAM,Y,kBACN,IAAiC,EAAjCY,EAAAA,EAAAA,IAAiCC,EAAA,M,iBAAxB,IAAc,EAAdD,EAAAA,EAAAA,IAAc+F,M,eAAU,KAAC1F,EAAAA,EAAAA,IAAGwE,EAAMM,IAAIU,cAAY,K,yBAE7DvG,EAAAA,EAAAA,IAESmG,GAAA,C,MAFMC,OAAO,QAAQ5C,KAAK,OAAO1D,MAAM,Y,kBAC9C,IAAqC,EAArCY,EAAAA,EAAAA,IAAqCC,EAAA,M,iBAA5B,IAAkB,EAAlBD,EAAAA,EAAAA,IAAkBgG,M,6BAAU,W,wBAK3ChG,EAAAA,EAAAA,IAOkBwE,GAAA,CAPDnC,MAAM,OAAOqC,MAAM,SAAS,YAAU,O,CAC1CC,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpF,EAAAA,EAAAA,IAGM,MAHNwG,EAGM,EAFJjG,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWkG,M,OACpBzG,EAAAA,EAAAA,IAAsC,aAAAY,EAAAA,EAAAA,IAA7BwE,EAAMM,IAAIgB,WAAS,O,OAKlCnG,EAAAA,EAAAA,IA8BkBwE,GAAA,CA9BDnC,MAAM,OAAOqC,MAAM,SAAS,YAAU,O,CAC1CC,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,CACS,QAArBA,EAAMM,IAAIiB,S,WAArBC,EAAAA,EAAAA,IAGM,MAHNC,EAGMhE,EAAA,MAAAA,EAAA,MAFJ7C,EAAAA,EAAAA,IAA6B,OAAxBL,MAAM,aAAW,UACtBK,EAAAA,EAAAA,IAAqB,YAAf,YAAQ,S,WAEhB4G,EAAAA,EAAAA,IAsBM,MAtBNE,EAsBM,EArBJ9G,EAAAA,EAAAA,IAOM,MAPN+G,EAOM,EANJxG,EAAAA,EAAAA,IAKeyG,GAAA,CAJZC,WAAYC,OAAO9B,EAAMM,IAAIyB,WAC7BvC,MAAO/D,EAAAuG,uBAAuBhC,EAAMM,IAAIyB,WACxC,eAAc,EACd,aAAW,G,kCAGhBnH,EAAAA,EAAAA,IAYM,MAZNqH,EAYM,EAXJrH,EAAAA,EAAAA,IAGM,MAHNsH,EAGM,EAFJ/G,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,IAASU,K,OAClBjB,EAAAA,EAAAA,IAAoC,aAAAY,EAAAA,EAAAA,IAA3BwE,EAAMM,IAAI6B,SAAO,MAE5BvH,EAAAA,EAAAA,IAGM,MAHNwH,EAGM,EAFJjH,EAAAA,EAAAA,IAA+BC,EAAA,M,iBAAtB,IAAY,EAAZD,EAAAA,EAAAA,IAAYkH,M,OACrBzH,EAAAA,EAAAA,IAAgC,aAAAY,EAAAA,EAAAA,IAAvBwE,EAAMM,IAAIgC,KAAG,MAExB1H,EAAAA,EAAAA,IAEM,OAFDL,MAAM,iBAAkB8D,OAAKkE,EAAAA,EAAAA,IAAA,CAAA/C,MAAU/D,EAAA+G,eAAexC,EAAMM,IAAIyB,e,QAChE/B,EAAMM,IAAIyB,WAAY,KAC3B,U,OAMR5G,EAAAA,EAAAA,IAgBkBwE,GAAA,CAhBDnC,MAAM,KAAKqC,MAAM,SAASD,MAAM,O,CACpCE,SAAOC,EAAAA,EAAAA,IAaVC,GAbiB,EACvBpF,EAAAA,EAAAA,IAYM,MAZN6H,EAYM,CAVyB,QAArBzC,EAAMM,IAAIiB,S,WADlB9G,EAAAA,EAAAA,IAOY+D,GAAA,C,MALVP,KAAK,UACLb,KAAK,QACJsB,QAAKvB,GAAEgD,EAAAuC,QAAQC,KAAK,CAADC,KAAA,SAAAC,OAAA,CAAAC,GAAiC9C,EAAMM,IAAIwC,O,kBAE/D,IAA2B,EAA3B3H,EAAAA,EAAAA,IAA2BC,EAAA,M,iBAAlB,IAAQ,EAARD,EAAAA,EAAAA,IAAQ4H,M,6BAAU,U,6CAE7BvB,EAAAA,EAAAA,IAEO,OAFPwB,EAEO,EADL7H,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWgB,K,6BAAU,iB,+BA/G3BpB,EAAAC,mB,2DA6IrB,MAAMiI,EAASC,EAAQ,MACvB,SAASC,EAAwBC,EAASC,GACxC,MAAMC,EAAIL,EAAOM,GAAGH,EAASC,GAC7B,OAAOC,EAAEE,OAAO,sBAClB,CAEA,SAASC,EAAiBC,EAAMC,GAAW,GACzC,MAAMC,EAAOF,EAAKG,cACZC,EAAQC,OAAOL,EAAKM,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOL,EAAKS,WAAWF,SAAS,EAAG,KAC/C,IAAIG,EAAOC,EAASC,EAYpB,OAVIX,GACFS,EAAQ,KACRC,EAAU,KACVC,EAAU,OAEVF,EAAQ,KACRC,EAAU,KACVC,EAAU,MAGL,GAAGV,KAAQE,KAASI,KAAOE,KAASC,KAAWC,GACxD,CAEA,OACEC,WAAY,CACVC,SAAQ,WACRC,MAAK,QACLC,QAAO,UACPC,SAAQ,WACRC,QAAO,UACPC,OAAM,SACNC,SAAQ,WACRC,MAAK,QACLC,KAAI,OACJC,WAAU,aACVC,eAAc,iBACdC,QAAO,UACPC,SAAQ,WACRC,KAAIA,EAAAA,MAENhG,IAAAA,GACE,MAAO,CACLrE,WAAW,EACXsE,QAAS,GACTpC,UAAW,OACXc,SAAU,CAACyF,EAAiB,IAAI6B,MAAK,IAAIA,MAAOC,UAAY,SAClD9B,EAAiB,IAAI6B,MAAQ,IACvCpH,mBAAoB,CAAC,WAAY,YACjCC,UAAW,CACT,CACEqH,KAAM,KACNC,MAAQA,KACN,MAAMC,EAAM,IAAIJ,KACVK,EAAQ,IAAIL,KAGlB,OAFAK,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,KAGnB,CACEF,KAAM,MACNC,MAAQA,KACN,MAAMC,EAAM,IAAIJ,KACVK,EAAQ,IAAIL,KAIlB,OAHAK,EAAME,QAAQH,EAAIvB,UAAY,GAC9BwB,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,KAGnB,CACEF,KAAM,MACNC,MAAQA,KACN,MAAMC,EAAM,IAAIJ,KACVK,EAAQ,IAAIL,KAIlB,OAHAK,EAAME,QAAQH,EAAIvB,UAAY,GAC9BwB,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,KAGnB,CACEF,KAAM,OACNC,MAAQA,KACN,MAAMC,EAAM,IAAIJ,KACVK,EAAQ,IAAIL,KAIlB,OAHAK,EAAMG,SAASJ,EAAI1B,WAAa,GAChC2B,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,MAIrBK,YAAa,EACbC,SAAU,GAEd,EAEAC,QAAS,CACPpH,UAAAA,GACEqH,KAAKC,cACP,EAEAxH,SAAAA,GACEuH,KAAKlI,SAAW,CACdyF,EAAiB,IAAI6B,MAAK,IAAIA,MAAOC,UAAY,SACjD9B,EAAiB,IAAI6B,MAAQ,GAEjC,EAEAhI,qBAAAA,CAAsBmI,GACpB,MAAMW,EAAM,IAAId,KAChB,IAAIe,EACU,QAAVZ,GACFY,EAAY,IAAIf,KAChBe,EAAUR,QAAQO,EAAIjC,YACH,SAAVsB,GACTY,EAAY,IAAIf,KAChBe,EAAUR,QAAQO,EAAIjC,UAAY,IACd,SAAVsB,GACVY,EAAY,IAAIf,KAChBe,EAAUR,QAAQO,EAAIjC,UAAY,IACf,UAAVsB,GACTY,EAAY,IAAIf,KAChBe,EAAUR,QAAQO,EAAIjC,UAAY,KACf,QAAVsB,IAETY,EAAY,IAAIf,KAChBgB,QAAQC,IAAIF,GACZA,EAAUG,YAAYJ,EAAIvC,cAAgB,KAG5CqC,KAAKlI,SAAW,CACdyF,EAAiB4C,GACjB5C,EAAiB2C,GAAK,IAGxBF,KAAKC,cACP,EAEA,kBAAMA,GACJD,KAAKlL,WAAY,QAGX,IAAIyL,QAAQC,GAAWC,WAAWD,EAAS,MAEjD,MAAML,EAAYlD,EAAwB+C,KAAKlI,SAAS,GAAI,iBACtD4I,EAAUzD,EAAwB+C,KAAKlI,SAAS,GAAI,iBACpD6I,QAAiBX,KAAKY,KAAKC,cAAc,CAC7CC,QAASd,KAAKe,IAAInE,GAClBoE,WAAYb,EACZc,SAAUP,IAGW,KAAnBC,EAAStF,QACX2E,KAAK5G,QAAUuH,EAASxH,KACxB6G,KAAKkB,YAGLlB,KAAKmB,UAAU,KACbnB,KAAKlL,WAAY,KAGnBkL,KAAKlL,WAAY,CAErB,EAEAoM,SAAAA,GACElB,KAAKoB,OAAOC,OAAOrB,KAAKsB,MAAMC,UAAWvB,KAAKwB,SAASjC,MAAOS,KAAKwB,SAASlK,MAC9E,EAEAgF,cAAAA,CAAemF,GAEb,OADAA,EAAO7F,OAAO6F,GACVA,GAAQ,GAAW,UACnBA,GAAQ,GAAW,UAChB,SACT,EAEA3F,sBAAAA,CAAuB2F,GAErB,OADAA,EAAO7F,OAAO6F,GACVA,GAAQ,GAAW,CAAC,CAACnI,MAAO,UAAWoI,SAAU,GAAI,CAACpI,MAAO,UAAWoI,SAAU,IAClFD,GAAQ,GAAW,CAAC,CAACnI,MAAO,UAAWoI,SAAU,GAAI,CAACpI,MAAO,UAAWoI,SAAU,IAC/E,CAAC,CAACpI,MAAO,UAAWoI,SAAU,GAAI,CAACpI,MAAO,UAAWoI,SAAU,GACxE,EAEA9G,UAAAA,CAAW+G,GACT,MAAMC,EAAU,CACd,OAAQ,SACR,QAAS,UACT,OAAQ,UACR,OAAQ,QAEV,OAAOA,EAAQD,IAAQ,SACzB,EAEAE,gBAAAA,CAAiBC,GACf9B,KAAKF,SAAWgC,CAClB,EAEAC,mBAAAA,CAAoBD,GAClB9B,KAAKH,YAAciC,CACrB,EAEA9I,UAAAA,GAEGgH,KAAKgC,SAAS,CACbC,QAAS,iBACTlK,KAAM,UACNmK,SAAU,KAEd,GAGFC,SAAU,KACLC,EAAAA,EAAAA,IAAS,CAAC,QAEbZ,QAAAA,GACE,IAAIa,EAAW,GACXxG,EAAY,GAChB,IAAK,IAAIyG,KAAQtC,KAAK5G,QACpBiJ,EAAS5F,KAAKuD,KAAK9F,OAAOC,MAAMmI,EAAKjI,cACrCwB,EAAUY,KAAK6F,EAAKzG,WAEtB,MAAO,CACLvE,MAAO+K,EACP9C,MAAO1D,EAEX,EAEA/F,YAAAA,GACE,OAAOkK,KAAK5G,QAAQmJ,OAAO,CAACC,EAAKC,IACxBD,GAAyB,QAAlBC,EAAOpH,QAAmBqH,SAASD,EAAOxG,UAAgB,GACvE,EACL,EAEA7F,YAAAA,GACE,OAAO4J,KAAK5G,QAAQuJ,OAAOF,GAA4B,QAAlBA,EAAOpH,QAAkBuH,MAChE,EAEAlM,eAAAA,GACE,MAAMmM,EAAe7C,KAAK5G,QAAQuJ,OAAOG,GAAkB,QAAbA,EAAEzH,QAAoByH,EAAEjH,WACtE,GAA4B,IAAxBgH,EAAaD,OAAc,OAAO,EAEtC,MAAMJ,EAAMK,EAAaN,OAAO,CAACC,EAAKC,IAC7BD,EAAMO,WAAWN,EAAO5G,WAC9B,GAEH,OAAOmH,KAAKC,MAAMT,EAAMK,EAAaD,OACvC,EAEApN,YAAAA,GACE,OAAOwK,KAAK5G,QAAQwJ,MACtB,GAGFM,MAAO,CACLlM,SAAAA,CAAUmM,GAERnD,KAAK5I,sBAAsB+L,EAC7B,GAGFC,OAAAA,GAEEpD,KAAKlL,WAAY,EACjBkL,KAAKC,cACP,EAEAoD,OAAAA,GAEErD,KAAKmB,UAAU,MAERnB,KAAKlL,WAAakL,KAAK5G,QAAQwJ,OAAS,GAC3C5C,KAAKsD,gBAGX,G,WC5gBF,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/Reports/Records.vue", "webpack://frontend-web/./src/views/Reports/Records.vue?8faa"], "sourcesContent": ["<template>\r\n  <el-scrollbar height=\"97vh\">\r\n    <div class=\"dashboard-container\">\r\n    <!-- 顶部统计区域 -->\r\n    <div class=\"stats-container\" :class=\"{ 'stats-loaded': !isLoading }\">\r\n      <div class=\"stat-card primary-gradient\">\r\n        <div class=\"stat-icon\">\r\n          <el-icon><DataLine /></el-icon>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-value\">{{ recordsCount }}</div>\r\n          <div class=\"stat-title\">总执行次数</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card success-gradient\">\r\n        <div class=\"stat-icon\">\r\n          <el-icon><Check /></el-icon>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-value\">{{ totalSuccess }}</div>\r\n          <div class=\"stat-title\">通过用例</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card warning-gradient\">\r\n        <div class=\"stat-icon\">\r\n          <el-icon><Loading /></el-icon>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-value\">{{ runningCount }}</div>\r\n          <div class=\"stat-title\">执行中</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card info-gradient\">\r\n        <div class=\"stat-icon\">\r\n          <el-icon><PieChart /></el-icon>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-value\">{{ averagePassRate }}%</div>\r\n          <div class=\"stat-title\">平均通过率</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n      <!-- 图表和过滤器部分 -->\r\n      <div class=\"chart-filter-container\">\r\n        <!-- 图表卡片 -->\r\n        <div class=\"chart-card\">\r\n          <div class=\"card-header\">\r\n            <h3>测试通过率趋势</h3>\r\n            <div class=\"time-selector\">\r\n              <el-radio-group v-model=\"timeRange\" size=\"small\" @change=\"handleTimeRangeChange\">\r\n                <el-radio-button label=\"day\">当天</el-radio-button>\r\n                <el-radio-button label=\"day3\">近3天</el-radio-button>\r\n                <el-radio-button label=\"week\">近7天</el-radio-button>\r\n                <el-radio-button label=\"month\">近30天</el-radio-button>\r\n                <el-radio-button label=\"all\">全部</el-radio-button>\r\n              </el-radio-group>\r\n            </div>\r\n          </div>\r\n          <div class=\"chart-wrapper\" ref=\"chart_box\"></div>\r\n        </div>\r\n\r\n        <!-- 筛选卡片 -->\r\n        <div class=\"filter-card\">\r\n          <div class=\"card-header\">\r\n            <h3>筛选条件</h3>\r\n          </div>\r\n          <div class=\"filter-content\">\r\n            <el-form label-position=\"top\" size=\"small\">\r\n              <el-form-item label=\"执行时间\" class=\"date-range-item\">\r\n                <el-date-picker\r\n                  v-model=\"dataTime\"\r\n                  type=\"datetimerange\"\r\n                  start-placeholder=\"开始时间\"\r\n                  end-placeholder=\"结束时间\"\r\n                  value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n                  :default-time=\"defaultTimeOptions\"\r\n                  :shortcuts=\"shortcuts\"\r\n                  range-separator=\"至\"\r\n                  :clearable=\"false\"\r\n                  class=\"date-picker\"\r\n                  :style=\"{maxWidth: '100%'}\"\r\n                />\r\n              </el-form-item>\r\n              <div class=\"filter-buttons\">\r\n                <el-button plain @click=\"clearData\">\r\n                  <el-icon><Refresh /></el-icon>重置\r\n                </el-button>\r\n                <el-button type=\"primary\" @click=\"submitForm\">\r\n                  <el-icon><Search /></el-icon>查询\r\n                </el-button>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <div class=\"table-card\">\r\n        <div class=\"card-header with-border\">\r\n          <h3>执行记录</h3>\r\n          <div class=\"header-actions\">\r\n            <el-button size=\"small\" type=\"primary\" plain @click=\"exportData\">\r\n              <el-icon><Download /></el-icon>导出数据\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          :data=\"records\"\r\n          v-loading=\"isLoading\"\r\n          element-loading-text=\"正在加载数据...\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n          element-loading-background=\"rgba(255, 255, 255, 0.8)\"\r\n          class=\"custom-table\"\r\n          :header-cell-style=\"{ background: '#f8faff', color: '#606266', fontWeight: '600' }\"\r\n          border\r\n        >\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n\r\n          <el-table-column label=\"执行时间\" align=\"center\" min-width=\"160\">\r\n            <template #default=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <el-icon><Clock /></el-icon>\r\n                <span>{{ $tools.rTime(scope.row.create_time) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"执行人\" align=\"center\" min-width=\"110\">\r\n            <template #default=\"scope\">\r\n              <div class=\"user-cell\">\r\n                <el-avatar :size=\"24\" class=\"user-avatar\">{{ scope.row.tester.substring(0, 1) }}</el-avatar>\r\n                <span>{{ scope.row.tester }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"环境\" align=\"center\" min-width=\"110\">\r\n            <template #default=\"scope\">\r\n              <el-tag effect=\"dark\" :type=\"getEnvType(scope.row.env_name)\" class=\"env-tag\">\r\n                {{ scope.row.env_name }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"执行类型\" align=\"center\" min-width=\"120\">\r\n            <template #default=\"scope\">\r\n              <el-tag\r\n                v-if=\"scope.row.execute_type === '手动执行'\"\r\n                effect=\"plain\"\r\n                type=\"primary\"\r\n                class=\"type-tag\">\r\n                <el-icon><User /></el-icon> {{ scope.row.execute_type }}\r\n              </el-tag>\r\n              <el-tag\r\n                v-else-if=\"scope.row.execute_type === '定时执行'\"\r\n                effect=\"plain\"\r\n                type=\"success\"\r\n                class=\"type-tag\">\r\n                <el-icon><AlarmClock /></el-icon> {{ scope.row.execute_type }}\r\n              </el-tag>\r\n              <el-tag v-else effect=\"plain\" type=\"info\" class=\"type-tag\">\r\n                <el-icon><QuestionFilled /></el-icon> 未知\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"测试计划\" align=\"center\" min-width=\"120\">\r\n            <template #default=\"scope\">\r\n              <div class=\"plan-cell\">\r\n                <el-icon><Tickets /></el-icon>\r\n                <span>{{ scope.row.plan_name }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"执行情况\" align=\"center\" min-width=\"220\">\r\n            <template #default=\"scope\">\r\n              <div v-if=\"scope.row.status === '执行中'\" class=\"running-status\">\r\n                <div class=\"pulse-dot\"></div>\r\n                <span>正在执行中...</span>\r\n              </div>\r\n              <div v-else class=\"result-container\">\r\n                <div class=\"progress-bar\">\r\n                  <el-progress\r\n                    :percentage=\"Number(scope.row.pass_rate)\"\r\n                    :color=\"getStatusColorGradient(scope.row.pass_rate)\"\r\n                    :stroke-width=\"8\"\r\n                    :show-text=\"false\"\r\n                  ></el-progress>\r\n                </div>\r\n                <div class=\"result-stats\">\r\n                  <div class=\"stat-item pass\">\r\n                    <el-icon><Check /></el-icon>\r\n                    <span>{{ scope.row.success }}</span>\r\n                  </div>\r\n                  <div class=\"stat-item total\">\r\n                    <el-icon><Document /></el-icon>\r\n                    <span>{{ scope.row.all }}</span>\r\n                  </div>\r\n                  <div class=\"stat-item rate\" :style=\"{color: getStatusColor(scope.row.pass_rate)}\">\r\n                    {{ scope.row.pass_rate }}%\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"操作\" align=\"center\" width=\"160\">\r\n            <template #default=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  v-if=\"scope.row.status !== '执行中'\"\r\n                  type=\"primary\"\r\n                  size=\"small\"\r\n                  @click=\"$router.push({ name: 'report', params: { id: scope.row.id } })\"\r\n                >\r\n                  <el-icon><View /></el-icon>查看\r\n                </el-button>\r\n                <span v-else class=\"running-badge\">\r\n                  <el-icon><Loading /></el-icon> 执行中\r\n                </span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n  </div>\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport { \r\n  DataLine, \r\n  Check, \r\n  Loading, \r\n  PieChart, \r\n  Refresh, \r\n  Search, \r\n  Download, \r\n  Clock, \r\n  User, \r\n  AlarmClock, \r\n  QuestionFilled, \r\n  Tickets, \r\n  Document, \r\n  View \r\n} from '@element-plus/icons-vue';\r\n\r\nconst moment = require('moment-timezone');\r\nfunction convertToTimeZoneFormat(dateStr, timeZone) {\r\n  const m = moment.tz(dateStr, timeZone);\r\n  return m.format('YYYY-MM-DD HH:mm:ss');\r\n}\r\n\r\nfunction getFormattedDate(date, endOfDay = false) {\r\n  const year = date.getFullYear();\r\n  const month = String(date.getMonth() + 1).padStart(2, '0');\r\n  const day = String(date.getDate()).padStart(2, '0');\r\n  let hours, minutes, seconds;\r\n\r\n  if (endOfDay) {\r\n    hours = '23';\r\n    minutes = '59';\r\n    seconds = '59';\r\n  } else {\r\n    hours = '00';\r\n    minutes = '00';\r\n    seconds = '00';\r\n  }\r\n\r\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    DataLine,\r\n    Check,\r\n    Loading,\r\n    PieChart,\r\n    Refresh,\r\n    Search,\r\n    Download,\r\n    Clock,\r\n    User,\r\n    AlarmClock,\r\n    QuestionFilled,\r\n    Tickets,\r\n    Document,\r\n    View\r\n  },\r\n  data() {\r\n    return {\r\n      isLoading: false,\r\n      records: [],\r\n      timeRange: 'day3',\r\n      dataTime: [getFormattedDate(new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000)),\r\n                getFormattedDate(new Date(), true)],\r\n      defaultTimeOptions: ['00:00:00', '23:59:59'],\r\n      shortcuts: [\r\n        {\r\n          text: '今天',\r\n          value: (() => {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setHours(0, 0, 0);\r\n            end.setHours(23, 59, 59);\r\n            return [start, end];\r\n          })\r\n        },\r\n        {\r\n          text: '近三天',\r\n          value: (() => {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setDate(end.getDate() - 2);\r\n            start.setHours(0, 0, 0);\r\n            end.setHours(23, 59, 59);\r\n            return [start, end];\r\n          })\r\n        },\r\n        {\r\n          text: '近七天',\r\n          value: (() => {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setDate(end.getDate() - 6);\r\n            start.setHours(0, 0, 0);\r\n            end.setHours(23, 59, 59);\r\n            return [start, end];\r\n          })\r\n        },\r\n        {\r\n          text: '近一个月',\r\n          value: (() => {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setMonth(end.getMonth() - 1);\r\n            start.setHours(0, 0, 0);\r\n            end.setHours(23, 59, 59);\r\n            return [start, end];\r\n          })\r\n        }\r\n      ],\r\n      currentPage: 1,\r\n      pageSize: 10\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    submitForm() {\r\n      this.getAllRecord();\r\n    },\r\n\r\n    clearData() {\r\n      this.dataTime = [\r\n        getFormattedDate(new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000)),\r\n        getFormattedDate(new Date(), true)\r\n      ];\r\n    },\r\n\r\n    handleTimeRangeChange(value) {\r\n      const now = new Date();\r\n      let startDate;\r\n      if (value === 'day') {\r\n        startDate = new Date();\r\n        startDate.setDate(now.getDate());\r\n      } else if (value === 'day3') {\r\n        startDate = new Date();\r\n        startDate.setDate(now.getDate() - 2);\r\n      }  else if (value === 'week') {\r\n        startDate = new Date();\r\n        startDate.setDate(now.getDate() - 6);\r\n      } else if (value === 'month') {\r\n        startDate = new Date();\r\n        startDate.setDate(now.getDate() - 29);\r\n      } else if (value === 'all') {\r\n        // 对于 \"全部\"，我们可以设置一个很早的日期或者按项目情况调整\r\n        startDate = new Date();\r\n        console.log(startDate)\r\n        startDate.setFullYear(now.getFullYear() - 10);\r\n      }\r\n\r\n      this.dataTime = [\r\n        getFormattedDate(startDate),\r\n        getFormattedDate(now, true)\r\n      ];\r\n\r\n      this.getAllRecord();\r\n    },\r\n\r\n    async getAllRecord() {\r\n      this.isLoading = true;\r\n      \r\n      // 延迟一小段时间确保加载状态显示一致\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n      \r\n      const startDate = convertToTimeZoneFormat(this.dataTime[0], 'Asia/Shanghai');\r\n      const endDate = convertToTimeZoneFormat(this.dataTime[1], 'Asia/Shanghai');\r\n      const response = await this.$api.getTestRecord({\r\n        project: this.pro.id,\r\n        start_time: startDate,\r\n        end_time: endDate\r\n      });\r\n\r\n      if (response.status == 200) {\r\n        this.records = response.data;\r\n        this.chartView();\r\n        \r\n        // 使用nextTick确保DOM更新后再关闭loading状态\r\n        this.$nextTick(() => {\r\n          this.isLoading = false;\r\n        });\r\n      } else {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    chartView() {\r\n      this.$chart.chart3(this.$refs.chart_box, this.pateData.value, this.pateData.label);\r\n    },\r\n\r\n    getStatusColor(rate) {\r\n      rate = Number(rate);\r\n      if (rate >= 90) return '#67C23A';\r\n      if (rate >= 70) return '#E6A23C';\r\n      return '#F56C6C';\r\n    },\r\n\r\n    getStatusColorGradient(rate) {\r\n      rate = Number(rate);\r\n      if (rate >= 90) return [{color: '#95de64', position: 0}, {color: '#52c41a', position: 1}];\r\n      if (rate >= 70) return [{color: '#ffd666', position: 0}, {color: '#faad14', position: 1}];\r\n      return [{color: '#ff7875', position: 0}, {color: '#f5222d', position: 1}];\r\n    },\r\n\r\n    getEnvType(env) {\r\n      const typeMap = {\r\n        '生产环境': 'danger',\r\n        '预发布环境': 'warning',\r\n        '测试环境': 'success',\r\n        '开发环境': 'info'\r\n      };\r\n      return typeMap[env] || 'primary';\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      this.pageSize = val;\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n    },\r\n\r\n    exportData() {\r\n      // Inform the user that the export feature is not yet implemented\r\n       this.$message({\r\n        message: '导出功能尚未实现，敬请期待！',\r\n        type: 'warning',\r\n        duration: 3000\r\n      });\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    ...mapState(['pro']),\r\n\r\n    pateData() {\r\n      let run_date = [];\r\n      let pass_rate = [];\r\n      for (let item of this.records) {\r\n        run_date.push(this.$tools.rTime(item.create_time));\r\n        pass_rate.push(item.pass_rate);\r\n      }\r\n      return {\r\n        label: run_date,\r\n        value: pass_rate\r\n      };\r\n    },\r\n\r\n    totalSuccess() {\r\n      return this.records.reduce((sum, record) => {\r\n        return sum + (record.status !== '执行中' ? parseInt(record.success) || 0 : 0);\r\n      }, 0);\r\n    },\r\n\r\n    runningCount() {\r\n      return this.records.filter(record => record.status === '执行中').length;\r\n    },\r\n\r\n    averagePassRate() {\r\n      const validRecords = this.records.filter(r => r.status !== '执行中' && r.pass_rate);\r\n      if (validRecords.length === 0) return 0;\r\n\r\n      const sum = validRecords.reduce((sum, record) => {\r\n        return sum + parseFloat(record.pass_rate);\r\n      }, 0);\r\n\r\n      return Math.round(sum / validRecords.length);\r\n    },\r\n\r\n    recordsCount() {\r\n      return this.records.length;\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    timeRange(newValue) {\r\n      // Call handleTimeRangeChange when timeRange changes\r\n      this.handleTimeRangeChange(newValue);\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 先将isLoading设为true，保证显示一致性\r\n    this.isLoading = true;\r\n    this.getAllRecord();\r\n  },\r\n\r\n  mounted() {\r\n    // 确保在挂载后样式已经应用\r\n    this.$nextTick(() => {\r\n      // 如果数据已加载完成，强制更新一次视图\r\n      if (!this.isLoading && this.records.length > 0) {\r\n        this.$forceUpdate();\r\n      }\r\n    });\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.dashboard-container {\r\n  padding: 20px;\r\n  background-color: #f0f2f5;\r\n  min-height: 100%;\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-container {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, minmax(240px, 1fr)); /* 明确4列 */\r\n  gap: 24px;\r\n  margin-bottom: 24px; /* 添加底部间距确保布局稳定 */\r\n  min-height: 120px; /* 确保即使在加载状态也有最小高度 */\r\n  width: 100%;\r\n}\r\n\r\n/* 确保在不同状态下保持一致的布局 */\r\n.stats-container.stats-loaded {\r\n  opacity: 1;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.stat-card {\r\n  position: relative;\r\n  border-radius: 10px;\r\n  padding: 24px;\r\n  color: white;\r\n  overflow: hidden;\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card:after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  pointer-events: none;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  border-radius: 10px;\r\n}\r\n\r\n.stat-card:hover:after {\r\n  opacity: 1;\r\n}\r\n\r\n.primary-gradient {\r\n  background: linear-gradient(135deg, #1890ff, #096dd9);\r\n}\r\n\r\n.success-gradient {\r\n  background: linear-gradient(135deg, #52c41a, #389e0d);\r\n}\r\n\r\n.warning-gradient {\r\n  background: linear-gradient(135deg, #faad14, #d48806);\r\n}\r\n\r\n.info-gradient {\r\n  background: linear-gradient(135deg, #722ed1, #531dab);\r\n}\r\n\r\n.stat-icon {\r\n  height: 56px;\r\n  width: 56px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28px;\r\n  margin-right: 20px;\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 26px;\r\n  font-weight: 600;\r\n  margin-bottom: 4px;\r\n  line-height: 1.2;\r\n  white-space: nowrap;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.stat-title {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 图表和过滤器布局 */\r\n.chart-filter-container {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  gap: 24px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.chart-card, .filter-card, .table-card {\r\n  background: white;\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.chart-card:hover, .filter-card:hover, .table-card:hover {\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.card-header {\r\n  padding: 16px 24px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header.with-border {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n}\r\n\r\n.chart-wrapper {\r\n  padding: 16px;\r\n  height: 320px;\r\n  min-height: 320px; /* 确保最小高度一致 */\r\n}\r\n\r\n/* 确保内容在加载状态时保持稳定 */\r\n.el-scrollbar__wrap {\r\n  overflow-x: hidden;\r\n}\r\n\r\n/* 确保表格在加载时的高度稳定 */\r\n.table-card {\r\n  min-height: 200px;\r\n  margin-top: 24px;\r\n}\r\n\r\n/* 筛选器样式 */\r\n.filter-content {\r\n  padding: 20px;\r\n}\r\n\r\n.date-range-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.date-picker {\r\n  width: 100%;\r\n  max-width: 100%;\r\n}\r\n\r\n.filter-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n  margin-top: 10px;\r\n}\r\n\r\n/* 表格样式 */\r\n.custom-table {\r\n  margin: 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.custom-table :deep(td) {\r\n  padding: 12px 0;\r\n  height: auto;\r\n  line-height: 1.5;\r\n}\r\n\r\n.custom-table :deep(th) {\r\n  padding: 12px 0;\r\n  height: 50px;\r\n  font-weight: 600;\r\n}\r\n\r\n.time-cell, .user-cell, .plan-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  line-height: 1.5;\r\n  text-align: center;\r\n}\r\n\r\n.user-avatar {\r\n  background-color: #1890ff;\r\n  color: white;\r\n}\r\n\r\n.env-tag, .type-tag {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  line-height: 1.5;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 4px;\r\n}\r\n\r\n.running-status {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n  color: #faad14;\r\n  font-weight: 500;\r\n}\r\n\r\n.pulse-dot {\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #faad14;\r\n  position: relative;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(0.8);\r\n    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0.7);\r\n  }\r\n  70% {\r\n    transform: scale(1);\r\n    box-shadow: 0 0 0 6px rgba(250, 173, 20, 0);\r\n  }\r\n  100% {\r\n    transform: scale(0.8);\r\n  }\r\n}\r\n\r\n.result-container {\r\n  padding: 0 16px;\r\n  min-width: 220px;\r\n}\r\n\r\n.progress-bar {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.result-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: nowrap;\r\n  margin-top: 8px;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-size: 13px;\r\n  white-space: nowrap;\r\n  padding: 0 8px;\r\n}\r\n\r\n.stat-item.pass {\r\n  color: #52c41a;\r\n}\r\n\r\n.stat-item.total {\r\n  color: #8c8c8c;\r\n}\r\n\r\n.stat-item.rate {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.running-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #faad14;\r\n  font-size: 13px;\r\n}\r\n\r\n.pagination-container {\r\n  padding: 16px 24px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  border-top: 1px solid #f0f0f0;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1200px) {\r\n  .chart-filter-container {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .stats-container {\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 16px;\r\n  }\r\n\r\n  .date-picker:deep(.el-range-editor) {\r\n    width: 100% !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .dashboard-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .stats-container {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .stat-card {\r\n    padding: 16px;\r\n  }\r\n\r\n  .stat-icon {\r\n    height: 48px;\r\n    width: 48px;\r\n    font-size: 24px;\r\n  }\r\n\r\n  .stat-value {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .chart-filter-container {\r\n    gap: 16px;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .filter-content {\r\n    padding: 16px;\r\n  }\r\n\r\n  .chart-wrapper {\r\n    height: 260px;\r\n  }\r\n\r\n  .date-picker:deep(.el-range-editor) {\r\n    width: 100% !important;\r\n    flex-direction: column;\r\n    height: auto;\r\n  }\r\n\r\n  .date-picker:deep(.el-range-separator) {\r\n    padding: 5px 0;\r\n  }\r\n\r\n  .date-picker:deep(.el-range-input) {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n@media (max-width: 1300px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    margin-left: 0;\r\n    margin-bottom: 8px;\r\n  }\r\n}\r\n\r\n/* 添加表格行条纹效果 */\r\n.custom-table :deep(.el-table__row:nth-child(even)) {\r\n  background-color: #fafafa;\r\n}\r\n\r\n/* 确保表格有表头边界 */\r\n.custom-table :deep(.el-table__header) {\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n/* 确保测试计划内容不被截断 */\r\n.plan-cell span {\r\n  max-width: 100%;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  display: inline-block;\r\n}\r\n\r\n/* 调整操作按钮 */\r\n.action-buttons .el-button {\r\n  padding: 6px 12px;\r\n}\r\n\r\n/* 确保内容垂直居中 */\r\n.custom-table :deep(.cell) {\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n}\r\n</style>\r\n", "import { render } from \"./Records.vue?vue&type=template&id=0ffee73a&scoped=true\"\nimport script from \"./Records.vue?vue&type=script&lang=js\"\nexport * from \"./Records.vue?vue&type=script&lang=js\"\n\nimport \"./Records.vue?vue&type=style&index=0&id=0ffee73a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0ffee73a\"]])\n\nexport default __exports__"], "names": ["class", "ref", "_createBlock", "_component_el_scrollbar", "height", "_createElementVNode", "_hoisted_1", "_normalizeClass", "$data", "isLoading", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_icon", "_component_DataLine", "_hoisted_4", "_hoisted_5", "_toDisplayString", "$options", "recordsCount", "_hoisted_6", "_hoisted_7", "_component_Check", "_hoisted_8", "_hoisted_9", "totalSuccess", "_hoisted_10", "_hoisted_11", "_component_Loading", "_hoisted_12", "_hoisted_13", "runningCount", "_hoisted_14", "_hoisted_15", "_component_<PERSON><PERSON><PERSON>", "_hoisted_16", "_hoisted_17", "averagePassRate", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_el_radio_group", "timeRange", "$event", "size", "onChange", "handleTimeRangeChange", "_component_el_radio_button", "label", "_cache", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_component_el_form", "_component_el_form_item", "_component_el_date_picker", "dataTime", "type", "defaultTimeOptions", "shortcuts", "clearable", "style", "max<PERSON><PERSON><PERSON>", "_hoisted_25", "_component_el_button", "plain", "onClick", "clearData", "_component_Refresh", "submitForm", "_component_Search", "_hoisted_26", "_hoisted_27", "_hoisted_28", "exportData", "_component_Download", "_component_el_table", "data", "records", "background", "color", "fontWeight", "border", "_component_el_table_column", "width", "align", "default", "_withCtx", "scope", "_hoisted_29", "_component_Clock", "_ctx", "$tools", "rTime", "row", "create_time", "_hoisted_30", "_component_el_avatar", "tester", "substring", "_component_el_tag", "effect", "getEnvType", "env_name", "execute_type", "_component_User", "_component_AlarmClock", "_component_QuestionFilled", "_hoisted_31", "_component_Tickets", "plan_name", "status", "_createElementBlock", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_component_el_progress", "percentage", "Number", "pass_rate", "getStatusColorGradient", "_hoisted_35", "_hoisted_36", "success", "_hoisted_37", "_component_Document", "all", "_normalizeStyle", "getStatusColor", "_hoisted_38", "$router", "push", "name", "params", "id", "_component_View", "_hoisted_39", "moment", "require", "convertToTimeZoneFormat", "dateStr", "timeZone", "m", "tz", "format", "getFormattedDate", "date", "endOfDay", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "minutes", "seconds", "components", "DataLine", "Check", "Loading", "<PERSON><PERSON><PERSON>", "Refresh", "Search", "Download", "Clock", "User", "AlarmClock", "QuestionFilled", "Tickets", "Document", "View", "Date", "getTime", "text", "value", "end", "start", "setHours", "setDate", "setMonth", "currentPage", "pageSize", "methods", "this", "getAllRecord", "now", "startDate", "console", "log", "setFullYear", "Promise", "resolve", "setTimeout", "endDate", "response", "$api", "getTestRecord", "project", "pro", "start_time", "end_time", "chartView", "$nextTick", "$chart", "chart3", "$refs", "chart_box", "pate<PERSON><PERSON>", "rate", "position", "env", "typeMap", "handleSizeChange", "val", "handleCurrentChange", "$message", "message", "duration", "computed", "mapState", "run_date", "item", "reduce", "sum", "record", "parseInt", "filter", "length", "validRecords", "r", "parseFloat", "Math", "round", "watch", "newValue", "created", "mounted", "$forceUpdate", "__exports__", "render"], "sourceRoot": ""}