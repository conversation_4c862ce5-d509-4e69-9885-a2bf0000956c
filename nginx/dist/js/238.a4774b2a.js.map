{"version": 3, "file": "js/238.a4774b2a.js", "mappings": "0OAiBUA,MAAM,e,GAGPC,MAAA,mB,aAsCAD,MAAM,e,SAM6BC,MAAA,mB,SAOLA,MAAA,mB,SACKA,MAAA,mB,SAC3BA,MAAA,mB,SAK0BA,MAAA,mB,SAC1BA,MAAA,mB,SASRA,MAAA,0D,GAcID,MAAM,iB,0ZArGhBE,EAAAA,EAAAA,IAsFQC,EAAA,CAtFC,cAAY,KAAKF,MAAA,uBAA2BG,KAAK,cAAcC,MAAM,KAAKC,KAAK,Q,kBACzF,IAYgB,CAZkB,OAAfC,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IAYgBC,EAAA,C,MAZyBC,MAAM,MAAMC,KAAK,M,kBACtD,IAUM,CAVKL,EAAAC,OAAOK,kB,WAAlBC,EAAAA,EAAAA,IAUM,MAAAC,EAAA,CATOR,EAAAC,OAAOK,gBAAgB,gBAAgBG,SAAS,sB,WAA3DF,EAAAA,EAAAA,IAGM,MAAAG,EAAA,EADJf,EAAAA,EAAAA,IAA4FgB,EAAA,CAAnFC,UAAU,E,WAAeZ,EAAAC,OAAOY,c,qCAAPb,EAAAC,OAAOY,cAAaC,GAAEC,KAAK,OAAOC,MAAM,U,uCAE5ET,EAAAA,EAAAA,IAIM,MAAAU,EAAA,EAHJtB,EAAAA,EAAAA,IAEeuB,EAAA,CAFDC,OAAO,QAAUC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAA0G,EAA1G3B,EAAAA,EAAAA,IAA0GgB,EAAA,CAAjGC,UAAU,EAAMW,UAAQvB,EAAAC,OAAOY,cAAeE,KAAK,OAAOC,MAAM,SAASG,OAAO,S,6EAKjE,OAAfnB,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IAWcC,EAAA,C,MAX2BC,MAAM,MAAMC,KAAK,M,kBACtD,IASe,EATfV,EAAAA,EAAAA,IASeuB,EAAA,CATDC,OAAO,QAASC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAOI,CAP2BtB,EAAAC,OAAOK,kB,WAAtCC,EAAAA,EAAAA,IAOI,MAPJiB,EAOI,G,aANLjB,EAAAA,EAAAA,IAKMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IALsB1B,EAAAC,OAAOK,gBAAe,CAArCR,EAAO6B,M,WAApBpB,EAAAA,EAAAA,IAKM,aAJLZ,EAAAA,EAAAA,IAGSiC,EAAA,CAHDlC,MAAA,qBAAyBG,KAAK,Q,kBACrC,IAAgD,EAAhDgC,EAAAA,EAAAA,IAAgD,IAAhDC,GAAgDC,EAAAA,EAAAA,IAAlBJ,EAAM,OAAH,IACjCE,EAAAA,EAAAA,IAAwB,aAAAE,EAAAA,EAAAA,IAAfjC,GAAK,K,yEAMgB,OAAfE,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IA4BcC,EAAA,C,MA5B2BC,MAAM,OAAOC,KAAK,M,kBACvD,IA0Be,EA1BfV,EAAAA,EAAAA,IA0BeuB,EAAA,CA1BDC,OAAO,QAASC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAwBI,CAxBOtB,EAAAC,OAAO+B,gB,WAAlBzB,EAAAA,EAAAA,IAwBI,MAAA0B,EAAA,EAvBLtC,EAAAA,EAAAA,IAsBcuC,EAAA,C,WAtBQC,EAAAC,Y,qCAAAD,EAAAC,YAAWtB,GAAErB,MAAM,e,kBACxC,IAMmB,EANnBE,EAAAA,EAAAA,IAMmB0C,EAAA,CANDhC,KAAK,KAAG,CACdiC,OAAKC,EAAAA,EAAAA,IACf,IAAclB,EAAA,KAAAA,EAAA,KAAdQ,EAAAA,EAAAA,IAAc,SAAX,WAAO,M,iBAEX,IAA+C,EAA/CA,EAAAA,EAAAA,IAA+C,WAA1C,qBAAiBE,EAAAA,EAAAA,IAAG/B,EAAAC,OAAOuC,QAAM,IACtCX,EAAAA,EAAAA,IAAyC,WAApC,kBAAcE,EAAAA,EAAAA,IAAG/B,EAAAC,OAAOwC,KAAG,K,OAEjC9C,EAAAA,EAAAA,IAOmB0C,EAAA,CAPDhC,KAAK,KAAG,CACdiC,OAAKC,EAAAA,EAAAA,IACf,IAAsBlB,EAAA,MAAAA,EAAA,MAAtBQ,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEd,IAA8C,G,aAAnDtB,EAAAA,EAAAA,IAEMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFsB1B,EAAAC,OAAOyC,gBAAe,CAArC5C,EAAO6B,M,WAApBpB,EAAAA,EAAAA,IAEM,aADLsB,EAAAA,EAAAA,IAAsC,aAAAE,EAAAA,EAAAA,IAA7BJ,EAAM,MAAQ7B,GAAK,O,eAG9BH,EAAAA,EAAAA,IAKmB0C,EAAA,CALDhC,KAAK,KAAG,CACdiC,OAAKC,EAAAA,EAAAA,IACf,IAAsBlB,EAAA,MAAAA,EAAA,MAAtBQ,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEnB,IAAuC,EAAvCA,EAAAA,EAAAA,IAAuC,aAAAE,EAAAA,EAAAA,IAA9B/B,EAAAC,OAAO+B,eAAa,K,oFAMjCrC,EAAAA,EAAAA,IAYcQ,EAAA,CAZDC,MAAM,MAAI,C,iBACtB,IAUe,EAVfT,EAAAA,EAAAA,IAUeuB,EAAA,CAVDC,OAAO,QAASC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAQM,EARNO,EAAAA,EAAAA,IAQM,MARNc,EAQM,G,aAPLpC,EAAAA,EAAAA,IAMMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANuB1B,EAAAC,OAAO2C,SAAQ,CAA/BC,EAAMC,M,WAAnBvC,EAAAA,EAAAA,IAMM,YAL8C,UAAZsC,EAAK,K,WAA5C3C,EAAAA,EAAAA,IAAmF0B,EAAA,C,MAA3ElC,MAAA,sB,kBAAqD,IAAa,E,iBAAVmD,EAAK,IAAD,K,YACZ,YAAZA,EAAK,K,WAAjD3C,EAAAA,EAAAA,IAAwG0B,EAAA,C,MAAhGlC,MAAA,qBAA2DG,KAAK,W,kBAAU,IAAa,E,iBAAVgD,EAAK,IAAD,K,YACjC,UAAZA,EAAK,K,WAAjD3C,EAAAA,EAAAA,IAAqG0B,EAAA,C,MAA7FlC,MAAA,qBAAyDG,KAAK,U,kBAAS,IAAa,E,iBAAVgD,EAAK,IAAD,K,YAC9B,SAAZA,EAAK,K,WAAjD3C,EAAAA,EAAAA,IAAqG0B,EAAA,C,MAA7FlC,MAAA,qBAAwDG,KAAK,W,kBAAU,IAAa,E,iBAAVgD,EAAK,IAAD,K,YAC1D,WAAZA,EAAK,K,WAArBtC,EAAAA,EAAAA,IAAiF,MAAjFwC,GAAiFhB,EAAAA,EAAAA,IAAhBc,EAAK,IAAD,K,4CAKzElD,EAAAA,EAAAA,IAMcQ,EAAA,CAND6C,SAAA,IAAQ,CACT5C,OAAKmC,EAAAA,EAAAA,IACf,IAAkG,CAArE,OAAjBvC,EAAAC,OAAOgD,Q,WAAnB1C,EAAAA,EAAAA,IAAkG,OAAlG2C,GAAkGnB,EAAAA,EAAAA,IAAA,YAAtB/B,EAAAC,OAAOgD,OAAK,IACtD,OAAjBjD,EAAAC,OAAOgD,Q,WAAxB1C,EAAAA,EAAAA,IAAuG,OAAvG4C,GAAuGpB,EAAAA,EAAAA,IAAA,YAAtB/B,EAAAC,OAAOgD,OAAK,M,WAC7F1C,EAAAA,EAAAA,IAA8D,OAA9D6C,GAA8DrB,EAAAA,EAAAA,IAAtB/B,EAAAC,OAAOgD,OAAK,M,MAGpB,OAAfjD,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IAKcC,EAAA,C,MAL2B6C,SAAA,I,CAC7B5C,OAAKmC,EAAAA,EAAAA,IACf,IAA4G,CAAhGvC,EAAAC,OAAOoD,aAAe,M,WAAlC9C,EAAAA,EAAAA,IAA4G,OAA5G+C,GAA4GvB,EAAAA,EAAAA,IAAA,YAA5B/B,EAAAC,OAAOoD,aAAW,M,WAClG9C,EAAAA,EAAAA,IAAkF,OAAlFgD,GAAkFxB,EAAAA,EAAAA,IAAA,YAA5B/B,EAAAC,OAAOoD,aAAW,M,wBAG1E1D,EAAAA,EAAAA,IAIcQ,EAAA,CAJD6C,SAAA,IAAQ,CACT5C,OAAKmC,EAAAA,EAAAA,IACf,IAAiC,E,2BAAlBvC,EAAAC,OAAOuD,UAAQ,K,cAIuD,OAAjBxD,EAAAC,OAAOgD,OAAkBjD,EAAAyD,U,WAA7FlD,EAAAA,EAAAA,IAEM,MAFNmD,EAEM,EADJ/D,EAAAA,EAAAA,IAAqFgE,EAAA,CAAxEC,QAAOC,EAAAC,cAAejE,KAAK,UAAUkE,MAAA,GAAMhE,KAAK,Q,kBAAO,IAAKsB,EAAA,MAAAA,EAAA,M,QAAL,Y,gDAGtE1B,EAAAA,EAAAA,IAeYqE,EAAA,CAfD1B,MAAM,Q,WAAiBH,EAAA8B,U,qCAAA9B,EAAA8B,UAASnD,GAAEoD,MAAM,MAAO,eAAcL,EAAAM,mB,CAS3DC,QAAM7B,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHNwC,EAGM,EAFJ1E,EAAAA,EAAAA,IAAqDgE,EAAA,CAAzCC,QAAOC,EAAAM,mBAAiB,C,iBAAE,IAAG9C,EAAA,MAAAA,EAAA,M,QAAH,U,6BACtC1B,EAAAA,EAAAA,IAA0DgE,EAAA,CAA/C9D,KAAK,UAAW+D,QAAOC,EAAAS,S,kBAAS,IAAGjD,EAAA,MAAAA,EAAA,M,QAAH,U,iDAX/C,IAOU,EAPV1B,EAAAA,EAAAA,IAOU4E,EAAA,CAPAC,MAAOrC,EAAAsC,SAAO,C,iBACtB,IAIe,EAJf9E,EAAAA,EAAAA,IAIe+E,EAAA,CAJDtE,MAAM,QAAM,C,iBACxB,IAEY,EAFZT,EAAAA,EAAAA,IAEYgF,EAAA,CAFD5E,KAAK,Q,WAAiBoC,EAAAsC,QAAQG,U,qCAARzC,EAAAsC,QAAQG,UAAS9D,GAAE+D,YAAY,WAAWnF,MAAA,gB,kBACT,IAA0B,G,aAA1Fa,EAAAA,EAAAA,IAAsHkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAAvCS,EAAA2C,WAARC,K,WAAvE7E,EAAAA,EAAAA,IAAsH8E,EAAA,CAA1G5E,MAAO2E,EAAK1E,KAAO,IAAM0E,EAAKtC,IAAM3C,MAAOiF,EAAKE,GAAgCtD,IAAKoD,EAAKE,I,oEAG1GtF,EAAAA,EAAAA,IAAiK+E,EAAA,CAAnJtE,MAAM,SAAO,C,iBAAC,IAAsH,EAAtHT,EAAAA,EAAAA,IAAsHuF,EAAA,CAA3GC,SAAU,CAAAC,QAAA,EAAAC,QAAA,G,WAAqClD,EAAAsC,QAAQa,K,qCAARnD,EAAAsC,QAAQa,KAAIxE,GAAEjB,KAAK,WAAW0F,aAAa,O,0HAczI,GACCC,MAAO,CACNvF,OAAQ,CACPwF,QAAS,CAAC,GAEXhC,QAAS,CACRgC,SAAS,IAGXC,SAAU,KACNC,EAAAA,EAAAA,IAAS,CAAC,SAEdC,WAAY,CACXC,OAAMA,EAAAA,GAEPC,IAAAA,GACC,MAAO,CACN1D,YAAa,CAAC,IAAK,IAAK,KAExB6B,WAAW,EAEXQ,QAAS,CACRG,UAAW,KACXU,KAAM,GACNS,KAAM,GACNC,OAAQ,OAENlB,WAAW,GAEhB,EACAmB,QAAS,CACR,aAAM3B,GACL4B,KAAKzB,QAAQ0B,QAAUD,KAAKE,IAAInB,GAChCiB,KAAKzB,QAAQsB,KAAOG,KAAKjG,OACzB,MAAMoG,QAAiBH,KAAKI,KAAKC,WAAWL,KAAKzB,SACzB,MAApB4B,EAASL,SACZE,KAAKM,SAAS,CACb3G,KAAM,UACN4G,QAAS,UACTC,SAAU,MAEXR,KAAKjC,WAAY,EACjBiC,KAAKzB,QAAU,CACdG,UAAW,KACXU,KAAM,GACNS,KAAM,GACNC,OAAQ,OAGX,EAEE7B,iBAAAA,GACE+B,KAAKjC,WAAY,EACjBiC,KAAKzB,QAAU,CAChBG,UAAW,KACXU,KAAM,GACNS,KAAM,GACNC,OAAQ,MAEP,EAGF,mBAAMlC,GACJ,MAAMuC,QAAiBH,KAAKI,KAAKK,mBACT,MAApBN,EAASL,SACXE,KAAKpB,WAAauB,EAASP,KAC3BI,KAAKjC,WAAY,EAErB,I,WChLJ,MAAM2C,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,O,oHCNOpH,MAAM,uB,GAEJA,MAAM,e,GACJA,MAAM,e,GACJA,MAAM,oB,GACNA,MAAM,gB,GACJA,MAAM,kB,GAAyBA,MAAM,a,GAEvCA,MAAM,e,GACJA,MAAM,c,GAaNA,MAAM,gB,GAYNA,MAAM,W,GAQVA,MAAM,gB,SACJA,MAAM,a,GACJA,MAAM,Y,GAGNA,MAAM,Y,GAEJA,MAAM,Y,SAGVA,MAAM,a,GACJA,MAAM,oB,GAYZA,MAAM,gB,GACJA,MAAM,0B,GAEFA,MAAM,mB,GAkBEA,MAAM,c,GAQNA,MAAM,sB,GAMNA,MAAM,a,GAcNA,MAAM,iB,GAMNA,MAAM,gB,GAQNA,MAAM,iB,GAMNA,MAAM,kB,0BAcDqH,EAAE,KAAKC,EAAE,QAAQtH,MAAM,c,GAQ5BA,MAAM,iB,GAMNA,MAAM,e,GAcNA,MAAM,iB,GAMNA,MAAM,a,GAIJA,MAAM,e,GAIJA,MAAM,Q,GAQVA,MAAM,iB,SAMNA,MAAM,a,GACJA,MAAM,U,GAGNA,MAAM,e,GACJA,MAAM,Y,GAGNA,MAAM,Q,SAGVA,MAAM,a,GAMNA,MAAM,iB,GAMNA,MAAM,iB,GA4CpBA,MAAM,wB,GA0BJA,MAAM,kB,GAmCJA,MAAM,iB,IAaZC,MAAA,kB,IAEkCA,MAAA,mB,IACDA,MAAA,mB,IACAA,MAAA,qB,IACAA,MAAA,mB,gCAuBIA,MAAA,mB,UACYA,MAAA,mB,UACpCA,MAAA,mB,k0BAvXlBa,EAAAA,EAAAA,IA8XM,MA9XNC,EA8XM,EA5XJqB,EAAAA,EAAAA,IA4DM,MA5DNnB,EA4DM,EA3DJmB,EAAAA,EAAAA,IAsCM,MAtCNZ,EAsCM,EArCJY,EAAAA,EAAAA,IAoCE,MApCFL,EAoCE,EAnCFK,EAAAA,EAAAA,IAEM,MAFNC,EAEM,EADJD,EAAAA,EAAAA,IAA6F,MAA7FI,EAA6F,C,qBAAjE,QAAEJ,EAAAA,EAAAA,IAAqD,OAArDc,GAAqDZ,EAAAA,EAAAA,IAA1BI,GAAA6E,MAAMC,OAAS,GAAJ,G,qBAAe,cAErFpF,EAAAA,EAAAA,IA+BM,MA/BNkB,EA+BM,EA9BJlB,EAAAA,EAAAA,IAYM,MAZNqB,EAYM,EAXJvD,EAAAA,EAAAA,IAUWuF,GAAA,C,WATA/C,GAAA+E,W,qCAAA/E,GAAA+E,WAAUpG,GACnB+D,YAAY,SACZsC,UAAA,GACA1H,MAAM,c,CACK2H,QAAM7E,EAAAA,EAAAA,IACf,IAEY,EAFZ5C,EAAAA,EAAAA,IAEYgE,GAAA,CAFAC,QAAOC,GAAAwD,YAAa5H,MAAM,oB,kBACpC,IAAe4B,EAAA,MAAAA,EAAA,MAAfQ,EAAAA,EAAAA,IAAe,YAAT,MAAE,M,wDAKhBA,EAAAA,EAAAA,IAWM,MAXNsB,EAWM,EAVJxD,EAAAA,EAAAA,IASY2H,GAAA,C,WARDnF,GAAAoF,Q,qCAAApF,GAAAoF,QAAOzG,GAChBrB,MAAM,iBACN,cAAY,QACZ,gBAAc,OACdM,KAAK,QACJyH,SAAQ3D,GAAA4D,YACT/H,MAAA,qEACA,oB,qCAGJmC,EAAAA,EAAAA,IAIA,MAJAuB,EAIA,EAHEzD,EAAAA,EAAAA,IAEMgE,GAAA,CAFMC,QAAOC,GAAA6D,SAAUjI,MAAM,eAAeI,KAAK,W,kBACrD,IAA2B,EAA3BF,EAAAA,EAAAA,IAA2BgI,GAAA,M,iBAAlB,IAAQ,EAARhI,EAAAA,EAAAA,IAAQiI,M,6BAAU,Y,qCAMnC/F,EAAAA,EAAAA,IAmBM,MAnBNyB,EAmBM,CAlByBuE,EAAAC,Q,WAA7BvH,EAAAA,EAAAA,IAQM,MARNgD,EAQM,EAPJ1B,EAAAA,EAAAA,IAEM,MAFN6B,EAEM,EADJ/D,EAAAA,EAAAA,IAAiCgI,GAAA,M,iBAAxB,IAAc,EAAdhI,EAAAA,EAAAA,IAAcoI,M,SAEzBlG,EAAAA,EAAAA,IAGM,MAHNwC,EAGM,C,eAFJxC,EAAAA,EAAAA,IAAiC,OAA5BpC,MAAM,aAAY,QAAI,KAC3BoC,EAAAA,EAAAA,IAAuE,MAAvEmG,GAAuEjG,EAAAA,EAAAA,IAA9C8B,GAAAoE,WAAapE,GAAAoE,WAAW5H,KAAO,QAAH,U,WAGzDE,EAAAA,EAAAA,IAQM,MARN2H,EAQM,EAPJrG,EAAAA,EAAAA,IAEM,MAFNsG,EAEM,EADJxI,EAAAA,EAAAA,IAA8BgI,GAAA,M,iBAArB,IAAW,EAAXhI,EAAAA,EAAAA,IAAWyI,M,uBAEtBvG,EAAAA,EAAAA,IAGM,OAHDpC,MAAM,YAAU,EACnBoC,EAAAA,EAAAA,IAAkC,OAA7BpC,MAAM,aAAY,UACvBoC,EAAAA,EAAAA,IAAuC,OAAlCpC,MAAM,kBAAiB,W,YAOpCoC,EAAAA,EAAAA,IAyNI,MAzNJwG,EAyNI,EAxNFxG,EAAAA,EAAAA,IAuNM,MAvNNyG,EAuNM,EAtNJ3I,EAAAA,EAAAA,IAqNWuB,GAAA,CArNGC,OAAO,uBAAqB,C,iBACxC,IAwMA,EAxMAU,EAAAA,EAAAA,IAwMA,MAxMA0G,EAwMA,EAvME5I,EAAAA,EAAAA,IAsMG6I,GAAA,CArMA1C,KAAM3D,GAAAsG,SACP/I,MAAA,eACA,aAAW,OACX,iBAAe,WACd,YAAW,CAAAgJ,OAAA,WACX,yBAAwB,eACxB,kBAAiB7E,GAAA8E,cACjBC,WAAW/E,GAAAgF,gB,kBAEZ,IAWgB,EAXhBlJ,EAAAA,EAAAA,IAWgBmJ,GAAA,CAXCC,MAAM,SAASlJ,KAAK,QAAQqE,MAAM,M,CACtC8E,QAAMzG,EAAAA,EAAAA,IACf,IAEMlB,EAAA,MAAAA,EAAA,MAFNQ,EAAAA,EAAAA,IAEM,OAFDpC,MAAM,iBAAe,EACxBoC,EAAAA,EAAAA,IAAe,YAAT,Q,MAGD4D,SAAOlD,EAAAA,EAAAA,IAGR0G,GAHe,EACrBpH,EAAAA,EAAAA,IAEM,MAFNqH,EAEM,EADRrH,EAAAA,EAAAA,IAAmC,aAAAE,EAAAA,EAAAA,IAA1BkH,EAAME,OAAS,GAAH,O,OAKvBxJ,EAAAA,EAAAA,IAkBgBmJ,GAAA,CAlBC1I,MAAM,OAAO,YAAU,MAAM,2BAAsB,aAAW,oB,CAClE4I,QAAMzG,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHNuH,EAGM,EAFJzJ,EAAAA,EAAAA,IAA+BgI,GAAA,M,iBAAtB,IAAY,EAAZhI,EAAAA,EAAAA,IAAY0J,M,qBACrBxH,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QAGL4D,SAAOlD,EAAAA,EAAAA,IASR0G,GATe,EACrBpH,EAAAA,EAAAA,IAQM,MARNyH,EAQM,EAPJzH,EAAAA,EAAAA,IAKM,OALDpC,MAAM,YAAaC,OAAK6J,EAAAA,EAAAA,IAAA,C,WAAsC1F,GAAA2F,iBAAiBP,EAAMQ,IAAIC,c,CAG7ET,EAAMQ,IAAIC,UAAY,I,WAArCxJ,EAAAA,EAAAA,IAA8DyH,GAAA,CAAAhG,IAAA,I,iBAAtB,IAAY,EAAZhC,EAAAA,EAAAA,IAAY0J,M,oBACpDnJ,EAAAA,EAAAA,IAAqCyH,GAAA,CAAAhG,IAAA,I,iBAArB,IAAW,EAAXhC,EAAAA,EAAAA,IAAWyI,M,YAE7BvG,EAAAA,EAAAA,IAAiC,aAAAE,EAAAA,EAAAA,IAAxBkH,EAAMQ,IAAIpJ,MAAI,O,OAK7BV,EAAAA,EAAAA,IAYkBmJ,GAAA,CAZDC,MAAM,SAAS3I,MAAM,OAAO8D,MAAM,MAAM,4B,CAC5C8E,QAAMzG,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHN8H,EAGM,EAFJhK,EAAAA,EAAAA,IAA6BgI,GAAA,M,iBAApB,IAAU,EAAVhI,EAAAA,EAAAA,IAAUiK,M,qBACnB/H,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QAGL4D,SAAOlD,EAAAA,EAAAA,IAGR0G,GAHe,EACrBpH,EAAAA,EAAAA,IAEM,MAFNgI,EAEM,EADJlK,EAAAA,EAAAA,IAAiFiC,GAAA,CAAzE7B,KAAK,QAAQ+J,OAAO,S,kBAAQ,IAAoC,E,iBAAjCb,EAAMQ,IAAItD,SAAS9F,MAAQ,KAAJ,K,sBAKpEV,EAAAA,EAAAA,IA0BkBmJ,GAAA,CA1BD1I,MAAM,KAAK8D,MAAM,MAAM6E,MAAM,U,CACjCC,QAAMzG,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHNkI,EAGM,EAFJpK,EAAAA,EAAAA,IAA2BgI,GAAA,M,iBAAlB,IAAQ,EAARhI,EAAAA,EAAAA,IAAQqK,M,qBACjBnI,EAAAA,EAAAA,IAAe,YAAT,MAAE,QAGD4D,SAAOlD,EAAAA,EAAAA,IAiBV0G,GAjBiB,EACvBpH,EAAAA,EAAAA,IAgBM,MAhBNoI,EAgBM,G,WAfJ1J,EAAAA,EAAAA,IAcM,OAdD2J,QAAQ,YAAYzK,OAAK0K,EAAAA,EAAAA,IAAA,CAAC,iBAAyBtG,GAAAuG,aAAanB,EAAMQ,IAAIC,c,gBAC7E7H,EAAAA,EAAAA,IAIE,QAJIpC,MAAM,YACV4K,EAAE,mI,WAIJxI,EAAAA,EAAAA,IAME,QANIpC,MAAM,SACV,mBAAiB,UAEjB4K,EAAE,mI,WAIJxI,EAAAA,EAAAA,IAA0E,OAA1EyI,GAA0EvI,EAAAA,EAAAA,IAA7BkH,EAAMQ,IAAIC,WAAS,I,eAMxE/J,EAAAA,EAAAA,IAkBgBmJ,GAAA,CAlBC1I,MAAM,OAAO2I,MAAM,SAAS,YAAU,MAAM,4B,CAChDC,QAAMzG,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHN0I,EAGM,EAFJ5K,EAAAA,EAAAA,IAAmCgI,GAAA,M,iBAA1B,IAAgB,EAAhBhI,EAAAA,EAAAA,IAAgB6K,M,qBACzB3I,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QAGH4D,SAAOlD,EAAAA,EAAAA,IASV0G,GATiB,EACvBpH,EAAAA,EAAAA,IAQM,MARN4I,EAQM,EAPJ9K,EAAAA,EAAAA,IAMO+K,GAAA,CALLZ,OAAO,OACNa,QAAS1B,EAAMQ,IAAInE,MAAQ,OAC5BsF,UAAU,MACT5H,UAAWiG,EAAMQ,IAAInE,MAAQ2D,EAAMQ,IAAInE,KAAKuF,QAAU,I,kBACvD,IAA2C,EAA3ChJ,EAAAA,EAAAA,IAA2C,aAAAE,EAAAA,EAAAA,IAAlCkH,EAAMQ,IAAInE,MAAQ,QAAJ,K,6CAM/B3F,EAAAA,EAAAA,IAoBgBmJ,GAAA,CApBC1I,MAAM,OAAO8D,MAAM,MAAM6E,MAAM,U,CACnCC,QAAMzG,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHNiJ,EAGM,EAFJnL,EAAAA,EAAAA,IAAiCgI,GAAA,M,iBAAxB,IAAc,EAAdhI,EAAAA,EAAAA,IAAcoL,M,qBACvBlJ,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QAGL4D,SAAOlD,EAAAA,EAAAA,IAWR0G,GAXe,EACrBpH,EAAAA,EAAAA,IAUM,MAVNmJ,EAUM,EATJnJ,EAAAA,EAAAA,IAEM,OAFDpC,OAAK0K,EAAAA,EAAAA,IAAA,CAAC,SAAQ,eAAyBlB,EAAMQ,IAAIwB,UAAYpH,GAAAqH,c,QAC7DrH,GAAAsH,cAAclC,EAAMQ,IAAIwB,UAAO,IAEpCpJ,EAAAA,EAAAA,IAKM,MALNuJ,EAKM,EAJJvJ,EAAAA,EAAAA,IAEM,OAFDpC,OAAK0K,EAAAA,EAAAA,IAAA,CAAC,WAAU,aAAuBlB,EAAMQ,IAAIwB,UAAYpH,GAAAqH,c,QAC7DjC,EAAMQ,IAAIwB,SAAW,MAAJ,IAEtBpJ,EAAAA,EAAAA,IAA+D,MAA/DwJ,GAA+DtJ,EAAAA,EAAAA,IAA1C8B,GAAAyH,WAAWrC,EAAMQ,IAAI8B,cAAW,S,OAM7D5L,EAAAA,EAAAA,IAqBgBmJ,GAAA,CArBC1I,MAAM,OAAO8D,MAAM,MAAM6E,MAAM,U,CACnCC,QAAMzG,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHN2J,EAGM,EAFJ7L,EAAAA,EAAAA,IAA2BgI,GAAA,M,iBAAlB,IAAQ,EAARhI,EAAAA,EAAAA,IAAQ8L,M,qBACjB5J,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QAGL4D,SAAOlD,EAAAA,EAAAA,IAWR0G,GAXe,CACQA,EAAMQ,IAAIiC,c,WAAvCnL,EAAAA,EAAAA,IAUM,MAVNoL,EAUM,EATJ9J,EAAAA,EAAAA,IAEM,MAFN+J,GAEM7J,EAAAA,EAAAA,IADD8B,GAAAsH,cAAclC,EAAMQ,IAAIoC,WAAQ,IAErChK,EAAAA,EAAAA,IAKM,MALNiK,EAKM,EAJJjK,EAAAA,EAAAA,IAEM,MAFNkK,GAEMhK,EAAAA,EAAAA,IADDkH,EAAMQ,IAAIoC,UAAY,MAAJ,IAEvBhK,EAAAA,EAAAA,IAA+D,MAA/DmK,GAA+DjK,EAAAA,EAAAA,IAA1C8B,GAAAyH,WAAWrC,EAAMQ,IAAIiC,cAAW,U,WAGzDnL,EAAAA,EAAAA,IAAwC,MAAxC0L,EAA8B,W,OAIlCtM,EAAAA,EAAAA,IA+CgBmJ,GAAA,CA/CC1I,MAAM,KAAK8L,MAAM,QAAQhI,MAAM,MAAM6E,MAAM,U,CAC/CC,QAAMzG,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHNsK,EAGM,EAFJxM,EAAAA,EAAAA,IAAgCgI,GAAA,M,iBAAvB,IAAa,EAAbhI,EAAAA,EAAAA,IAAayM,M,qBACtBvK,EAAAA,EAAAA,IAAe,YAAT,MAAE,QAGH4D,SAAOlD,EAAAA,EAAAA,IAsCR0G,GAtCe,EACrBpH,EAAAA,EAAAA,IAqCM,MArCNwK,EAqCM,EApCJ1M,EAAAA,EAAAA,IASa+K,GAAA,CATDC,QAAQ,OAAOC,UAAU,O,kBACnC,IAOY,EAPZjL,EAAAA,EAAAA,IAOYgE,GAAA,CANTC,SAAKtC,EAAAA,EAAAA,IAAAR,GAAO+C,GAAAyI,QAAQrD,EAAMQ,KAAG,UAC9B8C,OAAA,GACC9M,OAAK0K,EAAAA,EAAAA,IAAA,kCAAyClB,EAAMQ,IAAIC,WAAa,KACrE1G,SAAUiG,EAAMQ,IAAIC,WAAa,EAClC7J,KAAK,W,kBACL,IAAgC,EAAhCF,EAAAA,EAAAA,IAAgCgI,GAAA,M,iBAAvB,IAAa,EAAbhI,EAAAA,EAAAA,IAAa6M,M,gEAG1B7M,EAAAA,EAAAA,IAQa+K,GAAA,CARDC,QAAQ,OAAOC,UAAU,O,kBACnC,IAMY,EANZjL,EAAAA,EAAAA,IAMYgE,GAAA,CALTC,SAAKtC,EAAAA,EAAAA,IAAAR,GAAO+C,GAAA4I,UAAUxD,EAAMQ,KAAG,UAChC8C,OAAA,GACA9M,MAAM,sBACNI,KAAK,W,kBACL,IAA4B,EAA5BF,EAAAA,EAAAA,IAA4BgI,GAAA,M,iBAAnB,IAAS,EAAThI,EAAAA,EAAAA,IAAS+M,M,6CAGtB/M,EAAAA,EAAAA,IAgBa+K,GAAA,CAhBDC,QAAQ,OAAOC,UAAU,O,kBACnC,IAcY,EAdZjL,EAAAA,EAAAA,IAcYgE,GAAA,CAbTC,QAAKvC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,WACXiL,OAAA,GACA9M,MAAM,wBACNI,KAAK,U,kBACL,IAQgB,EARhBF,EAAAA,EAAAA,IAQgBgN,GAAA,CAPdrK,MAAM,aACN,sBAAoB,KACpB,qBAAmB,KAClBsK,UAAO9L,GAAE+C,GAAAgJ,QAAQ5D,EAAMQ,IAAIxE,K,CACjB6H,WAASvK,EAAAA,EAAAA,IAClB,IAA6B,EAA7B5C,EAAAA,EAAAA,IAA6BgI,GAAA,M,iBAApB,IAAU,EAAVhI,EAAAA,EAAAA,IAAUoN,M,0HAY3ClL,EAAAA,EAAAA,IAQM,MARNmL,EAQM,EAPJrN,EAAAA,EAAAA,IAMgBsN,GAAA,CANAC,WAAA,GAAWC,OAAO,mCACnBC,gBAAgBvJ,GAAAwJ,aAChB,oBAAmB,IACnBC,MAAOnL,GAAA6E,MAAMC,MACb,eAAc9E,GAAA6E,MAAMuG,QACtB,YAAU,MAAM,YAAU,O,kEAQzC5N,EAAAA,EAAAA,IAwDSqE,GAAA,C,WAvDE7B,GAAAqL,O,qCAAArL,GAAAqL,OAAM1M,GACf,eAAa,gBACboD,MAAM,QACN,sBACC,cAAY,EACZ,wBAAsB,G,CACZ8E,QAAMzG,EAAAA,EAAAA,IACf,IAEMlB,EAAA,MAAAA,EAAA,MAFNQ,EAAAA,EAAAA,IAEM,OAFDpC,MAAM,iBAAe,EACxBoC,EAAAA,EAAAA,IAAqC,MAAjCpC,MAAM,gBAAe,a,MAqCtB2E,QAAM7B,EAAAA,EAAAA,IACX,IAOM,EAPNV,EAAAA,EAAAA,IAOM,MAPN4L,EAOM,EANJ9N,EAAAA,EAAAA,IAEYgE,GAAA,CAFAC,QAAOC,GAAA6J,gBAAiBjO,MAAM,c,kBAAa,IAEvD4B,EAAA,MAAAA,EAAA,M,QAFuD,W,6BAGvD1B,EAAAA,EAAAA,IAEYgE,GAAA,CAFD9D,KAAK,UAAW+D,QAAOC,GAAA8J,QAAUC,QAASzL,GAAA0L,WAAYpO,MAAM,e,kBAAc,IAErF4B,EAAA,MAAAA,EAAA,M,QAFqF,a,2DAvCzF,IAiCM,EAjCNQ,EAAAA,EAAAA,IAiCM,MAjCNiM,EAiCM,EAhCJnO,EAAAA,EAAAA,IA+BI4E,GAAA,CA9BDC,MAAOrC,GAAA4L,QACPC,MAAO7L,GAAA8L,UACRC,IAAI,UACJ,iBAAe,MACfzO,MAAM,a,kBACN,IAOW,EAPXE,EAAAA,EAAAA,IAOW+E,GAAA,CAPGtE,MAAM,OAAO+N,KAAK,Q,kBAC9B,IAKsB,EALtBxO,EAAAA,EAAAA,IAKsBuF,GAAA,C,WAJX/C,GAAA4L,QAAQ1N,K,qCAAR8B,GAAA4L,QAAQ1N,KAAIS,GACrB+D,YAAY,SACZuJ,UAAU,KACV,qBACA3O,MAAM,c,gCAEVE,EAAAA,EAAAA,IAMW+E,GAAA,CANGtE,MAAM,OAAO+N,KAAK,c,kBAC9B,IAIW,EAJXxO,EAAAA,EAAAA,IAIWuF,GAAA,C,WAHA/C,GAAA4L,QAAQM,W,qCAARlM,GAAA4L,QAAQM,WAAUvN,GAC3BrB,MAAM,aACNuD,SAAA,I,gCAGXrD,EAAAA,EAAAA,IASkB+E,GAAA,CATJtE,MAAM,OAAO+N,KAAK,Q,kBACvB,IAOsB,EAPtBxO,EAAAA,EAAAA,IAOsBuF,GAAA,CANpBrF,KAAK,W,WACIsC,GAAA4L,QAAQzI,K,qCAARnD,GAAA4L,QAAQzI,KAAIxE,GACrB+D,YAAY,SACZyJ,KAAK,IACLF,UAAU,MACV,qBACA3O,MAAM,c,uFAiBlBE,EAAAA,EAAAA,IAqCW4O,GAAA,C,WArCSpM,GAAAqM,U,qCAAArM,GAAAqM,UAAS1N,GAAG,eAAa,EAAOf,KAAK,O,kBACzD,IAmCM,EAnCN8B,EAAAA,EAAAA,IAmCM,MAnCN4M,GAmCM,EAlCL9O,EAAAA,EAAAA,IAKkB+O,GAAA,CALDpM,MAAM,OAAOqM,OAAA,GAAQC,OAAQ,EAAGlP,MAAA,yB,kBAChD,IAA+G,EAA/GC,EAAAA,EAAAA,IAA+GkP,GAAA,CAAzFzO,MAAM,MAAI,C,iBAAE,IAAsD,EAAtDyB,EAAAA,EAAAA,IAAsD,IAAtDiN,IAAsD/M,EAAAA,EAAAA,IAAzBI,GAAA4M,eAAeC,KAAG,K,OACjFrP,EAAAA,EAAAA,IAAkHkP,GAAA,CAA5FzO,MAAM,MAAI,C,iBAAC,IAA0D,EAA1DyB,EAAAA,EAAAA,IAA0D,IAA1DoN,IAA0DlN,EAAAA,EAAAA,IAA7BI,GAAA4M,eAAeG,SAAO,K,OACpFvP,EAAAA,EAAAA,IAAiHkP,GAAA,CAA3FzO,MAAM,MAAI,C,iBAAC,IAAyD,EAAzDyB,EAAAA,EAAAA,IAAyD,IAAzDsN,IAAyDpN,EAAAA,EAAAA,IAA1BI,GAAA4M,eAAeK,MAAI,K,OACnFzP,EAAAA,EAAAA,IAAgHkP,GAAA,CAA1FzO,MAAM,MAAI,C,iBAAC,IAAwD,EAAxDyB,EAAAA,EAAAA,IAAwD,IAAxDwN,IAAwDtN,EAAAA,EAAAA,IAA3BI,GAAA4M,eAAeO,OAAK,K,6BAEnFzN,EAAAA,EAAAA,IAA8D,OAAzDnC,MAAA,sCAAuC,EAACmC,EAAAA,EAAAA,IAAW,SAAR,U,KAChDlC,EAAAA,EAAAA,IA0BeuB,GAAA,CA1BDC,OAAO,uBAAqB,C,iBACzC,IAwBW,EAxBXxB,EAAAA,EAAAA,IAwBW6I,GAAA,CAxBA1C,KAAM3D,GAAA4M,eAAeQ,MAAO7P,MAAA,eAAoB,aAAW,Q,kBACrE,IAIkB,EAJlBC,EAAAA,EAAAA,IAIkBmJ,GAAA,CAJDjJ,KAAK,UAAQ,CAClB4F,SAAOlD,EAAAA,EAAAA,IAC4BiD,GADrB,EACxB7F,EAAAA,EAAAA,IAA6C6P,GAAA,CAAhCvP,OAAQuF,EAAMiE,K,4BAG7B9J,EAAAA,EAAAA,IAA2CmJ,GAAA,CAA1B1I,MAAM,MAAM+N,KAAK,UAClCxO,EAAAA,EAAAA,IAIuBmJ,GAAA,CAJN1I,MAAM,OAAO+N,KAAK,U,CACjB1I,SAAOlD,EAAAA,EAAAA,IACoDiD,GAD7C,CACS,QAAnBA,EAAMiE,IAAI5J,O,WAAtBU,EAAAA,EAAAA,IAAmE,OAAAkP,IAAA1N,EAAAA,EAAAA,IAA1ByD,EAAMiE,IAAIjH,QAAM,K,wBAGnE7C,EAAAA,EAAAA,IAIuBmJ,GAAA,CAJN1I,MAAM,QAAQ+N,KAAK,e,CAClB1I,SAAOlD,EAAAA,EAAAA,IACyDiD,GADlD,CACS,QAAnBA,EAAMiE,IAAI5J,O,WAAtBU,EAAAA,EAAAA,IAAwE,OAAAmP,IAAA3N,EAAAA,EAAAA,IAA/ByD,EAAMiE,IAAIpG,aAAW,K,wBAGxE1D,EAAAA,EAAAA,IAMkBmJ,GAAA,CAND1I,MAAM,OAAO+N,KAAK,QAAQ,YAAU,Q,CACzC1I,SAAOlD,EAAAA,EAAAA,IACwEiD,GADjE,CACO,MAAnBA,EAAMiE,IAAIxG,Q,WAAtB1C,EAAAA,EAAAA,IAAyF,OAAzFoP,IAAyF5N,EAAAA,EAAAA,IAAzByD,EAAMiE,IAAIxG,OAAK,IACpC,MAAnBuC,EAAMiE,IAAIxG,Q,WAA3B1C,EAAAA,EAAAA,IAA8F,OAA9FqP,IAA8F7N,EAAAA,EAAAA,IAAzByD,EAAMiE,IAAIxG,OAAK,M,WAC3F1C,EAAAA,EAAAA,IAA+D,OAA/DsP,IAA+D9N,EAAAA,EAAAA,IAAzByD,EAAMiE,IAAIxG,OAAK,M,6OA0B5D6M,GAAAA,EAAY,CAACC,GAAAA,EAAUC,GAAAA,EAAaC,GAAAA,IAEpC,QACEvK,SAAU,KACLC,EAAAA,GAAAA,IAAS,CAAC,MAAO,QAAS,aAC7BuF,QAAAA,GACD,OAAOgF,OAAOC,eAAeC,QAAQ,WACtC,EACCnI,UAAAA,GACC,OAAK/B,KAAK4B,OAAU5B,KAAKmK,UAAqC,IAAzBnK,KAAKmK,SAASxF,OAC5C3E,KAAKmK,SAASC,KAAKC,GAAOA,EAAItL,KAAOiB,KAAK4B,QAAU5B,KAAKmK,SAAS,GADD,IAE1E,GAEAzK,WAAY,CACV4K,WAAU,KACVC,KAAI,QAAEC,KAAI,QAAEC,OAAM,UAAEC,UAAS,aAAEC,WAAU,cAAEC,SAAQ,YAAEC,OAAM,UAAEC,KAAI,QAAEC,KAAI,QACvEC,YAAW,eAAEC,YAAW,eAAEC,QAAO,WAAEC,WAAU,cAAEC,cAAa,iBAAEC,MAAK,SAAEC,MAAK,SAC1EC,KAAI,QAAEC,QAAO,WAAEC,WAAU,cAAEC,WAAU,cAAEC,UAAS,aAAEC,aAAY,gBAAEC,OAAM,UAAEC,KAAI,QAC5EC,MAAK,SAAEC,aAAY,gBAAEC,OAAM,UAAEC,UAAS,aAAEC,aAAY,YAAEC,YAAW,eAAEC,QAAO,WAC1EC,WAAUA,GAAAA,YAEZ1M,IAAAA,GACE,MAAO,CACLoB,WAAY,GACZF,MAAO,CAAC,EACRyB,SAAU,GACV+F,WAAW,EACXO,eAAgB,CAAC,EACjBvB,QAAQ,EACRjG,SAAS,EACTsG,YAAY,EACZ4E,SAAU,GACVC,MAAO,KACPC,WAAY,CACV,4CACA,4CACA,4CACA,4CACA,6CAEF1E,UAAW,CACb5N,KAAM,CACL,CACCuS,UAAU,EACVnM,QAAS,UACToM,QAAS,SAGXxE,WAAY,CACX,CACCuE,UAAU,EACVnM,QAAS,QACToM,QAAS,UAIT9E,QAAS,CACP1N,KAAM,GACNgO,WAAY,GACZ/I,KAAM,GACN2F,QAAS,GACTvB,UAAW,GAEboJ,SAAU,CAAC,EACXC,cAAe,CACbC,QAAS,WAGf,EACD/M,QAAS,KACHgN,EAAAA,GAAAA,IAAa,CAAC,aAGjBtK,aAAAA,EAAc,IAACc,EAAG,OAAEmF,EAAM,SAAEsE,EAAQ,YAAEC,IACpC,MAAqB,OAAjBvE,EAAOxO,OAAkBqJ,EAAIC,WAAa,EACrC,mBAEF,EACT,EAIAF,gBAAAA,CAAiBE,GACf,GAAIA,GAAa,EACf,MAAO,4CAET,MAAM5G,EAAqB,EAAZ4G,EAAiBxD,KAAKyM,WAAW9H,OAChD,OAAO3E,KAAKyM,WAAW7P,EACzB,EAGAsH,YAAAA,CAAanD,GACX,OAAIA,GAAS,EAAU,cACnBA,EAAQ,EAAU,YAClBA,EAAQ,GAAW,eAChB,YACT,EAGAmM,kBAAAA,CAAmBC,GACjB,OAAKA,GACLA,EAAOC,SAASD,GACZA,GAAQ,KAAOA,EAAO,IAAY,UAClCA,GAAQ,KAAOA,EAAO,IAAY,OAClCA,GAAQ,KAAOA,EAAO,IAAY,UAClCA,GAAQ,IAAY,SACjB,IANW,EAOpB,EAGAE,iBAAAA,EAAkB,IAAC9J,EAAG,SAAEyJ,IACtB,OAAIzJ,EAAIC,WAAa,EACZ,kBAEFwJ,EAAW,IAAM,EAAI,WAAa,SAC3C,EAGArK,cAAAA,CAAeY,GACbvD,KAAKuG,UAAUhD,EACjB,EAGA+J,iBAAAA,CAAkBvM,GAChB,OAAKA,GAASA,GAAS,EAAU,EAC1BwM,KAAKC,IAAY,GAARzM,EAAY,IAC9B,EAGA0M,kBAAAA,CAAmB1Q,GACjB,MAAc,OAAVA,EAAuB,iBACb,OAAVA,EAAuB,iBACpB,cACT,EAGA2Q,YAAAA,CAAa3M,GACX,OAAKA,GAASA,GAAS,EAAU,UAC7BA,EAAQ,EAAU,UAClBA,EAAQ,GAAW,UAChB,SACT,EAGA4M,cAAAA,CAAe3I,GACb,IAAKA,EAAU,MAAO,UACtB,IAAKhF,KAAK4M,SAAS5H,GAAW,CAE5B,MAAM4I,EAAS,CACb,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAExChR,EAAQoI,EAAS6I,WAAW,GAAKD,EAAOjJ,OAC9C3E,KAAK4M,SAAS5H,GAAY4I,EAAOhR,EACnC,CACA,OAAOoD,KAAK4M,SAAS5H,EACvB,EAGAC,aAAAA,CAAcD,GACZ,OAAKA,EACEA,EAAS8I,UAAU,EAAG,GAAGC,cADV,GAExB,EAGA3I,UAAAA,CAAW4I,GACT,OAAKA,EACEhO,KAAKiO,OAAOC,MAAMF,GADF,MAEzB,EAGAG,aAAAA,CAAc7R,GACZ,IAAKA,EAAQ,MAAO,GAEpB,OADAA,EAASA,EAAOyR,cACRzR,GACN,IAAK,MAAO,MAAO,UACnB,IAAK,OAAQ,MAAO,UACpB,IAAK,MAAO,MAAO,UACnB,IAAK,SAAU,MAAO,SACtB,IAAK,QAAS,MAAO,OACrB,QAAS,MAAO,GAEpB,EAGA8R,aAAAA,CAAcjB,GACZ,OAAKA,GACLA,EAAOC,SAASD,GACZA,GAAQ,KAAOA,EAAO,IAAY,UAClCA,GAAQ,KAAOA,EAAO,IAAY,OAClCA,GAAQ,KAAOA,EAAO,IAAY,UAClCA,GAAQ,IAAY,SACjB,IANW,EAOpB,EAGAkB,cAAAA,GACE,OAAKrO,KAAK6I,eAAeC,KAAO9I,KAAK6I,eAAeC,KAAO,EAAU,EAC9DyE,KAAKe,MAAOtO,KAAK6I,eAAeG,QAAUhJ,KAAK6I,eAAeC,IAAO,IAC9E,EAGAyF,WAAAA,GACE,OAAKvO,KAAK6I,eAAeC,KAAO9I,KAAK6I,eAAeC,KAAO,EAAU,EAC9DyE,KAAKe,MAAOtO,KAAK6I,eAAeK,KAAOlJ,KAAK6I,eAAeC,IAAO,IAC3E,EAGA0F,YAAAA,GACE,OAAKxO,KAAK6I,eAAeC,KAAO9I,KAAK6I,eAAeC,KAAO,EAAU,EAC9DyE,KAAKe,MAAOtO,KAAK6I,eAAeO,MAAQpJ,KAAK6I,eAAeC,IAAO,IAC5E,EAGA2F,SAAAA,GACOzO,KAAKsI,WAActI,KAAK6I,eAAeC,KAE5C9I,KAAK0O,UAAU,KACb,GAAI1O,KAAK2O,MAAMC,eAAgB,CACzB5O,KAAKwM,OACPxM,KAAKwM,MAAMqC,UAGb7O,KAAKwM,MAAQ5C,GAAAA,GAAa5J,KAAK2O,MAAMC,gBAGrC,MAAME,EAAe,CACnBnV,KAAM,SACNiH,EAAG,EACHC,EAAG,EACHkO,GAAI,EACJC,GAAI,EACJC,WAAY,CAAC,CACXC,OAAQ,EACRC,MAAO,WACN,CACDD,OAAQ,EACRC,MAAO,aAILC,EAAY,CAChBzV,KAAM,SACNiH,EAAG,EACHC,EAAG,EACHkO,GAAI,EACJC,GAAI,EACJC,WAAY,CAAC,CACXC,OAAQ,EACRC,MAAO,WACN,CACDD,OAAQ,EACRC,MAAO,aAILE,EAAa,CACjB1V,KAAM,SACNiH,EAAG,EACHC,EAAG,EACHkO,GAAI,EACJC,GAAI,EACJC,WAAY,CAAC,CACXC,OAAQ,EACRC,MAAO,WACN,CACDD,OAAQ,EACRC,MAAO,aAILG,EAAS,CACbC,gBAAiB,cACjBC,QAAS,CACP7C,QAAS,OACT8C,UAAW,kBACXF,gBAAiB,wBACjBG,YAAa,yBACbC,UAAW,CACTR,MAAO,SAGXS,OAAQ,CACNC,OAAQ,WACRC,MAAO,GACPC,IAAK,SACLJ,UAAW,CACTR,MAAO,WAETa,UAAW,GACXC,WAAY,GACZC,QAAS,GACTC,KAAM,UAERC,OAAQ,CACN,CACEjW,KAAM,OACNR,KAAM,MACN0W,OAAQ,CAAC,MAAO,OAChBC,mBAAmB,EACnBC,UAAW,CACTC,aAAc,GACdd,YAAa,wBACbe,YAAa,GAEfvW,MAAO,CACLwW,MAAM,GAERC,SAAU,CACRzW,MAAO,CACLwW,MAAM,EACNE,SAAU,KACVC,WAAY,OACZ1B,MAAO,WAET2B,UAAW,IAEbC,UAAW,CACTL,MAAM,GAER9Q,KAAM,CACJ,CACEhG,MAAOoG,KAAK6I,eAAeG,SAAW,EACtC7O,KAAM,KACNoW,UAAW,CAAEpB,MAAOL,IAEtB,CACElV,MAAOoG,KAAK6I,eAAeK,MAAQ,EACnC/O,KAAM,KACNoW,UAAW,CAAEpB,MAAOC,IAEtB,CACExV,MAAOoG,KAAK6I,eAAeO,OAAS,EACpCjP,KAAM,KACNoW,UAAW,CAAEpB,MAAOE,KAGxB2B,cAAe,QACfC,gBAAiB,aACjBC,eAAgB,SAAUC,GACxB,OAAuB,IAAhB5D,KAAK6D,QACd,KAKNpR,KAAKwM,MAAM6E,UAAU/B,GACrBtF,OAAOsH,iBAAiB,SAAUtR,KAAKuR,YACzC,GAEJ,EAGAA,WAAAA,GACMvR,KAAKwM,OACPxM,KAAKwM,MAAMgF,QAEf,EAGDrQ,WAAAA,GACGnB,KAAKyR,YAAYzR,KAAKE,IAAInB,GAAIiB,KAAKc,MAAMuG,QAASrH,KAAKgB,WAC1D,EAGCuF,SAAAA,CAAUhD,GACXvD,KAAK0R,QAAQC,KAAK,CAAExX,KAAM,mBACvB6F,KAAK4R,SAASrO,EACjB,EAGA/B,QAAAA,GACGxB,KAAK6H,QAAQM,WAAanI,KAAKE,IAAI/F,KACnC6F,KAAKsH,QAAS,CAChB,EAGA,aAAMG,GACJzH,KAAK2O,MAAMkD,QAAQC,SAASC,UAC1B,GAAKC,EAAL,CAEAhS,KAAK2H,YAAa,EAClB,IACE,MAAMsK,EAAS,IAAIjS,KAAK6H,SACxBoK,EAAOlN,QAAU/E,KAAKgF,SACtBiN,EAAO9J,WAAanI,KAAKE,IAAInB,GAEnC,MAAMoB,QAAiBH,KAAKI,KAAK8R,eAAeD,GACtB,MAApB9R,EAASL,UACPqS,EAAAA,GAAAA,IAAe,CACb/V,MAAO,OACPmE,QAAS,YACb5G,KAAM,UACF6G,SAAU,MAEhBR,KAAKsH,QAAS,EACVtH,KAAKyR,YAAYzR,KAAKE,IAAInB,IAE9B,CAAE,MAAOqK,IACPgJ,EAAAA,GAAAA,IAAU,CACRzY,KAAM,QACN4G,QAAS,SAAW6I,EAAM7I,SAAW,QACrCC,SAAU,KAEd,CAAE,QACAR,KAAK2H,YAAa,CACpB,CA3BkB,GA6BtB,EAGApG,WAAAA,GACOvB,KAAKqB,QAERrB,KAAKyR,YAAYzR,KAAKE,IAAInB,GAAIiB,KAAKc,MAAMuG,QAASrH,KAAKgB,WAAWhB,KAAKgF,UAGvEhF,KAAKyR,aAET,EAGA,iBAAMA,CAAYxR,EAAQD,KAAKE,IAAInB,GAAIsT,EAAKrS,KAAKc,MAAMuG,QAASlN,EAAM6K,GACpE,IACE,MAAM7E,QAAiBH,KAAKI,KAAKkS,YAAYrS,EAASoS,EAAMlY,EAAM6K,GAC1C,MAApB7E,EAASL,SACXE,KAAKuC,SAAWpC,EAASP,KAAK7F,QAAU,GACxCiG,KAAKc,MAAQX,EAASP,MAAQ,CAAC,EAC/BI,KAAK6H,QAAQM,WAAanI,KAAKE,IAAI/F,KAEvC,CAAE,MAAOiP,IACPgJ,EAAAA,GAAAA,IAAU,CACRzY,KAAM,QACN4G,QAAS,cAAgB6I,EAAM7I,SAAW,QAC1CC,SAAU,KAEd,CAEF,EAGD2G,YAAAA,CAAaoL,GACVvS,KAAKyR,YAAYzR,KAAKE,IAAInB,GAAIwT,EAAavS,KAAKgB,WAAYhB,KAAKqB,QAAUrB,KAAKgF,cAAWwN,GAC3FxS,KAAKc,MAAMuG,QAAUkL,CACvB,EAGAE,gBAAAA,CAAiB5Y,GACfmG,KAAKuM,SAAW1S,EAChBmG,KAAKmH,aAAa,EACpB,EAGA,aAAMR,CAAQ5H,GACZ,IACE,MAAM2T,EAAkBC,GAAAA,GAAUC,QAAQ,CACxCC,KAAM,UACN7L,WAAY,0BAGR7G,QAAiBH,KAAKI,KAAK0S,YAAY/T,GAE7C2T,EAAgBK,QAEQ,MAApB5S,EAASL,UACXqS,EAAAA,GAAAA,IAAe,CACb/V,MAAO,OACPmE,QAAS,YACT5G,KAAM,UACN6G,SAAU,MAGZR,KAAKyR,YAAYzR,KAAKE,IAAInB,GAAIiB,KAAKc,MAAMuG,QAASrH,KAAKgB,WAAYhB,KAAKqB,QAAUrB,KAAKgF,cAAWwN,GAEtG,CAAE,MAAOpJ,IACPgJ,EAAAA,GAAAA,IAAU,CACRzY,KAAM,QACN4G,QAAS,SAAW6I,EAAM7I,SAAW,QACrCC,SAAU,KAEd,CACJ,EAGA,aAAM4F,CAAQxG,GACV,MAAM4D,EAAY4J,SAASxN,EAAK4D,WAEhC,GAAIA,GAAa,EAMf,YALA4O,EAAAA,GAAAA,IAAU,CACRzY,KAAM,UACN4G,QAAS,oBACTC,SAAU,MAKd,IAAKR,KAAK4B,MAKR,YAJAoR,GAAAA,EAAaC,MAAM,iBAAkB,QAAS,CAC5CC,kBAAmB,KACnBvZ,KAAM,YAMV,MAAM+Y,EAAkBC,GAAAA,GAAUC,QAAQ,CACxCC,KAAM,kBACN/F,QAAS,UACT9F,WAAY,4BAGd,IACF,MAAMiL,EAAS,CACd5H,IAAKrK,KAAK4B,MACVuR,MAAOvT,EAAKb,IAGPoB,QAAiBH,KAAKI,KAAKgT,SAASxT,EAAKb,GAAIkT,GACnD,GAAuB,KAAnB9R,EAASL,OAAe,CAEtB4S,EAAgBK,QAGhB/S,KAAK6I,eAAiB1I,EAASP,MAAQ,CAAC,EAC7CI,KAAKsI,WAAY,EAGZtI,KAAKyO,YAGL,MAAM4E,EAAcrT,KAAKqO,iBACL,MAAhBgF,GACFlB,EAAAA,GAAAA,IAAe,CACb/V,MAAO,OACPmE,QAAS,MAAMP,KAAK6I,eAAeC,cACnCnP,KAAM,UACN6G,SAAU,MAEH6S,GAAe,IACxBlB,EAAAA,GAAAA,IAAe,CACb/V,MAAO,OACPmE,QAAS,GAAGP,KAAK6I,eAAeG,WAAWhJ,KAAK6I,eAAeC,cACxEnP,KAAM,UACG6G,SAAU,OAGZ2R,EAAAA,GAAAA,IAAe,CACb/V,MAAO,OACPmE,QAAS,KAAKP,KAAK6I,eAAeG,WAAWhJ,KAAK6I,eAAeC,cACjEnP,KAAM,QACN6G,SAAU,KAGhB,CACF,CAAE,MAAO4I,GAEPsJ,EAAgBK,SAEhBX,EAAAA,GAAAA,IAAU,CACRzY,KAAM,QACN4G,QAAS,SAAW6I,EAAM7I,SAAW,QACrCC,SAAU,KAEd,CACF,EAGFgH,eAAAA,GACIxH,KAAKsH,QAAS,EACdtH,KAAK2O,MAAMkD,SAASyB,gBAEpBtT,KAAK6H,QAAU,CACb1N,KAAM,GACNgO,WAAYnI,KAAKE,IAAI/F,KACrBiF,KAAM,GACN2F,QAAS,GACTvB,UAAW,EAEf,GAEH+P,OAAAA,GACCvT,KAAKyR,YAAYzR,KAAKE,IAAInB,GAC1B,EACAyU,OAAAA,GAEOxT,KAAK4B,QACRwQ,EAAAA,GAAAA,IAAU,CACRzY,KAAM,OACN4G,QAAS,mBACTC,SAAU,KAGhB,EACAiT,aAAAA,GACEzJ,OAAO0J,oBAAoB,SAAU1T,KAAKuR,aACtCvR,KAAKwM,QACPxM,KAAKwM,MAAMqC,UACX7O,KAAKwM,MAAQ,KAEjB,EACAmH,MAAO,CACLrL,SAAAA,CAAUsL,GACJA,GACF5T,KAAKyO,WAET,I,YCx+BJ,MAAM/N,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASC,IAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/components/common/caseResult.vue", "webpack://frontend-web/./src/components/common/caseResult.vue?0f1a", "webpack://frontend-web/./src/views/TestCase/TestCase.vue", "webpack://frontend-web/./src/views/TestCase/TestCase.vue?e496"], "sourcesContent": ["<template>\n\t  <el-tabs model-value=\"rb\" style=\"min-height: 300px;\" type=\"border-card\" value=\"rb\" size=\"mini\">\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应体\" name=\"rb\">\n      <div v-if=\"result.response_header\">\n        <div v-if=\"result.response_header['Content-Type'].includes('application/json')\">\n          <!-- 如果 Content-Type 是 application/json，渲染 JSON 格式的 Editor -->\n          <Editor :readOnly=\"true\" v-model=\"result.response_body\" lang=\"json\" theme=\"chrome\"></Editor>\n        </div>\n        <div v-else>\n          <el-scrollbar height=\"400px\"  @wheel.stop>\n            <Editor :readOnly=\"true\" v-html=\"result.response_body\" lang=\"html\" theme=\"chrome\" height=\"400px\"></Editor>\n          </el-scrollbar>\n        </div>\n      </div>\n    </el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应头\" name=\"rh\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div class=\"tab-box-sli\" v-if=\"result.response_header\">\n\t\t\t\t<div v-for=\"(value, key) in result.response_header\">\n\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" type=\"info\">\n\t\t\t\t\t\t<b style=\"color: #747474;\">{{ key + ' : ' }}</b>\n\t\t\t\t\t\t<span>{{ value }}</span>\n\t\t\t\t\t</el-tag>\n\t\t\t\t</div>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"请求信息\" name=\"rq\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div v-if=\"result.requests_body\">\n\t\t\t\t<el-collapse v-model=\"activeNames\" class=\"tab-box-sli\">\n\t\t\t\t\t<el-collapse-item name=\"1\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>General</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div>Request Method : {{ result.method }}</div>\n\t\t\t\t\t\t<div>Request URL : {{ result.url }}</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"2\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Headers</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div v-for=\"(value, key) in result.requests_header\">\n\t\t\t\t\t\t\t<span>{{ key + ' : ' + value }}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"3\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Payload</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<span>{{ result.requests_body }}</span>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t</el-collapse>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane label=\"日志\">\n\t\t\t<el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t\t<div class=\"tab-box-sli\">\n\t\t\t\t\t<div v-for=\"(item, index) in result.log_data\">\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-if=\"item[0] === 'DEBUG'\" >{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'WARNING'\" type=\"warning\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'ERROR'\" type=\"danger\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'INFO'\" type=\"success\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<pre v-else-if=\"item[0] === 'EXCEPT'\" style=\"color: #d60000;\">{{ item[1] }}</pre>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.state === '成功'\" style=\"color: #00AA7F;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else-if=\"result.state === '失败'\" style=\"color: #d18d17;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else style=\"color: #ff0000;\">{{ result.state }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.status_cede <= 300\" style=\"color: #00AA7F;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t\t<span v-else style=\"color: #ff5500;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t{{ 'Time : ' + result.run_time }}\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t</el-tabs>\n    <div style=\"margin-top: 10px;width: 100%;text-align: center;\" v-if=\"result.state === '失败' && showbtn\">\n      <el-button  @click=\"getInterfaces\" type=\"success\" plain size=\"mini\">提交bug</el-button>\n    </div>\n    <!-- 添加bug的弹框 -->\n    <el-dialog title=\"提交bug\" v-model=\"addBugDlg\" width=\"40%\" :before-close=\"closeDialogResult\">\n      <el-form :model=\"bugForm\">\n        <el-form-item label=\"所属接口\">\n          <el-select size=\"small\" v-model=\"bugForm.interface\" placeholder=\"bug对应的接口\" style=\"width: 100%;\">\n            <el-option :label=\"iter.name + ' ' + iter.url\" :value=\"iter.id\" v-for=\"iter in interfaces\" :key=\"iter.id\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"bug描述\"><el-input :autosize=\"{ minRows: 3, maxRows: 4 }\" v-model=\"bugForm.desc\" type=\"textarea\" autocomplete=\"off\"></el-input></el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"closeDialogResult\">取 消</el-button>\n          <el-button type=\"success\" @click=\"saveBug\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n</template>\n\n<script>\nimport Editor from './Editor.vue';\nimport { mapState } from 'vuex';\nexport default {\n\tprops: {\n\t\tresult: {\n\t\t\tdefault: {}\n\t\t},\n\t\tshowbtn: {\n\t\t\tdefault: true\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState(['pro'])\n\t},\n\tcomponents: {\n\t\tEditor\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveNames: ['1', '2', '3'],\n\t\t\t// 提交bug的显示窗口\n\t\t\taddBugDlg: false,\n\t\t\t// 添加bug的表单\n\t\t\tbugForm: {\n\t\t\t\tinterface: null,\n\t\t\t\tdesc: '',\n\t\t\t\tinfo: '',\n\t\t\t\tstatus: '待处理'\n\t\t\t},\n      interfaces:[]\n\t\t};\n\t},\n\tmethods: {\n\t\tasync saveBug() {\n\t\t\tthis.bugForm.project = this.pro.id;\n\t\t\tthis.bugForm.info = this.result;\n\t\t\tconst response = await this.$api.createBugs(this.bugForm);\n\t\t\tif (response.status === 201) {\n\t\t\t\tthis.$message({\n\t\t\t\t\ttype: 'success',\n\t\t\t\t\tmessage: 'bug提交成功',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\tthis.addBugDlg = false;\n\t\t\t\tthis.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n\t\t\t}\n\t\t},\n    // 取消按钮时重置输入信息\n    closeDialogResult() {\n      this.addBugDlg = false;\n      this.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n      },\n\n    // 获取接口列表\n    async getInterfaces() {\n      const response = await this.$api.getNewInterfaces();\n      if (response.status === 200) {\n        this.interfaces = response.data\n        this.addBugDlg = true\n      }\n    }\n\t}\n};\n</script>\n\n<style></style>\n", "import { render } from \"./caseResult.vue?vue&type=template&id=3a14eb2a\"\nimport script from \"./caseResult.vue?vue&type=script&lang=js\"\nexport * from \"./caseResult.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\n  <div class=\"test-case-container\">\n    <!-- 页面顶部区域 -->\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <div class=\"action-container\">\n        <div class=\"panel-header\">\n          <div class=\"panel-subtitle\">共 <span class=\"highlight\">{{ pages.count || 0 }}</span> 条数据</div>\n        </div>\n        <div class=\"panel-tools\">\n          <div class=\"search-box\">\n            <el-input\n              v-model=\"filterText\"\n              placeholder=\"搜索用例名称\"\n              clearable\n              class=\"glow-input\">\n              <template #append>\n                <el-button @click=\"searchClick\" class=\"neon-btn-primary\">\n                  <span>查询</span>\n                </el-button>\n              </template>\n            </el-input>\n          </div>\n          <div class=\"filter-group\">\n            <el-switch\n              v-model=\"checked\"\n              class=\"creator-switch\"\n              active-text=\"仅看我创建\"\n              inactive-text=\"全部用例\"\n              size=\"large\"\n              @change=\"creatorCase\"\n              style=\"--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff\"\n              inline-prompt>\n            </el-switch>\n          </div>\n          <div class=\"actions\">\n            <el-button @click=\"clickAdd\" class=\"neon-btn-add\" type=\"primary\">\n              <el-icon><Plus /></el-icon>新增用例\n      </el-button>\n    </div>\n        </div>\n    </div>\n      </div>\n      <div class=\"header-right\">\n        <div class=\"env-badge\" v-if=\"envId\">\n          <div class=\"env-icon\">\n            <el-icon><Connection /></el-icon>\n          </div>\n          <div class=\"env-info\">\n            <div class=\"env-label\">当前环境</div>\n            <div class=\"env-name\">{{ currentEnv ? currentEnv.name : '未知环境' }}</div>\n          </div>\n        </div>\n        <div class=\"env-badge\" v-else>\n          <div class=\"env-icon warning\">\n            <el-icon><Warning /></el-icon>\n          </div>\n          <div class=\"env-info\">\n            <div class=\"env-label\">环境未设置</div>\n            <div class=\"env-name error\">请选择环境</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 表格区域 -->\n    <div class=\"main-content\">\n      <div class=\"glass-panel data-panel\">\n        <el-scrollbar height=\"calc(100vh - 300px)\">\n          <div class=\"table-container\">\n            <el-table \n              :data=\"caseList\" \n              style=\"width: 100%\"\n              empty-text=\"暂无数据\"\n              row-class-name=\"case-row\"\n              :row-style=\"{cursor: 'pointer'}\"\n              :header-cell-class-name=\"'table-header'\"\n              :cell-class-name=\"cellClassName\"\n              @row-click=\"handleRowClick\">\n              \n              <el-table-column align=\"center\" type=\"index\" width=\"80\">\n                <template #header>\n                  <div class=\"column-header\">\n                    <span>序号</span>\n                  </div>\n                </template>\n              <template #default=\"scope\">\n                  <div class=\"case-index\">\n                <span>{{ scope.$index + 1 }}</span>\n                  </div>\n              </template>\n            </el-table-column>\n              \n              <el-table-column label=\"用例名称\" min-width=\"220\" show-overflow-tooltip class-name=\"case-name-column\">\n                <template #header>\n                  <div class=\"column-name-header\">\n                    <el-icon><Document /></el-icon>\n                    <span>用例名称</span>\n                  </div>\n                </template>\n              <template #default=\"scope\">\n                  <div class=\"case-name\">\n                    <div class=\"case-icon\" :style=\"{\n                      background: getCaseIconColor(scope.row.stepCount)\n                    }\">\n                      <el-icon v-if=\"scope.row.stepCount > 0\"><Document /></el-icon>\n                      <el-icon v-else><Warning /></el-icon>\n                    </div>\n                    <span>{{ scope.row.name }}</span>\n                  </div>\n              </template>\n            </el-table-column>\n              \n              <el-table-column align=\"center\" label=\"所属项目\" width=\"180\" show-overflow-tooltip>\n                <template #header>\n                  <div class=\"column-header\">\n                    <el-icon><Folder /></el-icon>\n                    <span>所属项目</span>\n                  </div>\n                </template>\n              <template #default=\"scope\">\n                  <div class=\"project-name\">\n                    <el-tag size=\"small\" effect=\"plain\">{{ scope.row.project?.name || '-' }}</el-tag>\n                  </div>\n                </template>\n              </el-table-column>\n              \n              <el-table-column label=\"步骤\" width=\"120\" align=\"center\">\n                <template #header>\n                  <div class=\"column-header\">\n                    <el-icon><List /></el-icon>\n                    <span>步骤</span>\n                  </div>\n                </template>\n                <template #default=\"scope\">\n                  <div class=\"step-indicator\">\n                    <svg viewBox=\"0 0 36 36\" class=\"circular-chart\" :class=\"getStepClass(scope.row.stepCount)\">\n                      <path class=\"circle-bg\"\n                        d=\"M18 2.0845\n                        a 15.9155 15.9155 0 0 1 0 31.831\n                        a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      />\n                      <path class=\"circle\"\n                        stroke-dasharray=\"30, 100\"\n                        :stroke-dasharray=\"`${getStepPercentage(scope.row.stepCount)}, 100`\"\n                        d=\"M18 2.0845\n                        a 15.9155 15.9155 0 0 1 0 31.831\n                        a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      />\n                      <text x=\"18\" y=\"21.35\" class=\"percentage\">{{ scope.row.stepCount }}</text>\n                    </svg>\n                  </div>\n                </template>\n              </el-table-column>\n              \n              <el-table-column label=\"用例描述\" align=\"center\" min-width=\"250\" show-overflow-tooltip>\n                <template #header>\n                  <div class=\"column-header\">\n                    <el-icon><ChatDotRound /></el-icon>\n                    <span>用例描述</span>\n                  </div>\n                </template>\n                <template #default=\"scope\">\n                  <div class=\"description\">\n                    <el-tooltip \n                      effect=\"dark\" \n                      :content=\"scope.row.desc || '暂无描述'\" \n                      placement=\"top\" \n                      :disabled=\"!scope.row.desc || scope.row.desc.length <= 20\">\n                      <span>{{ scope.row.desc || '暂无描述' }}</span>\n              </el-tooltip>\n                  </div>\n              </template>\n            </el-table-column>\n              \n              <el-table-column label=\"创建信息\" width=\"220\" align=\"center\">\n                <template #header>\n                  <div class=\"column-header\">\n                    <el-icon><UserFilled /></el-icon>\n                    <span>创建信息</span>\n                  </div>\n                </template>\n              <template #default=\"scope\">\n                  <div class=\"user-info\">\n                    <div class=\"avatar\" :class=\"{'self-avatar': scope.row.creator === username}\">\n                      {{ getAvatarText(scope.row.creator) }}\n                    </div>\n                    <div class=\"info-detail\">\n                      <div class=\"username\" :class=\"{'self-name': scope.row.creator === username}\">\n                        {{ scope.row.creator || '未知' }}\n                      </div>\n                      <div class=\"time\">{{ formatTime(scope.row.create_time) }}</div>\n                    </div>\n                  </div>\n              </template>\n            </el-table-column>\n              \n              <el-table-column label=\"最后更新\" width=\"220\" align=\"center\">\n                <template #header>\n                  <div class=\"column-header\">\n                    <el-icon><Edit /></el-icon>\n                    <span>最后更新</span>\n                  </div>\n                </template>\n              <template #default=\"scope\">\n                  <div class=\"user-info\" v-if=\"scope.row.update_time\">\n                    <div class=\"avatar\">\n                      {{ getAvatarText(scope.row.modifier) }}\n                    </div>\n                    <div class=\"info-detail\">\n                      <div class=\"username\">\n                        {{ scope.row.modifier || '未知' }}\n                      </div>\n                      <div class=\"time\">{{ formatTime(scope.row.update_time) }}</div>\n                    </div>\n                  </div>\n                  <div class=\"no-update\" v-else>尚未更新</div>\n              </template>\n            </el-table-column>\n              \n              <el-table-column label=\"操作\" fixed=\"right\" width=\"200\" align=\"center\">\n                <template #header>\n                  <div class=\"column-header\">\n                    <el-icon><Operation /></el-icon>\n                    <span>操作</span>\n                  </div>\n                </template>\n              <template #default=\"scope\">\n                  <div class=\"actions-group\">\n                    <el-tooltip content=\"运行用例\" placement=\"top\">\n                      <el-button \n                        @click.stop=\"runCase(scope.row)\" \n                        circle \n                        :class=\"['action-btn', 'run-btn', {'disabled': scope.row.stepCount <= 0}]\"\n                        :disabled=\"scope.row.stepCount <= 0\"\n                        type=\"success\">\n                        <el-icon><VideoPlay /></el-icon>\n                      </el-button>\n                    </el-tooltip>\n                    <el-tooltip content=\"管理步骤\" placement=\"top\">\n                      <el-button \n                        @click.stop=\"clickEdit(scope.row)\" \n                        circle \n                        class=\"action-btn edit-btn\"\n                        type=\"primary\">\n                        <el-icon><SetUp /></el-icon>\n                      </el-button>\n                    </el-tooltip>\n                    <el-tooltip content=\"删除用例\" placement=\"top\">\n                      <el-button \n                        @click.stop \n                        circle \n                        class=\"action-btn delete-btn\"\n                        type=\"danger\">\n                        <el-popconfirm\n                          title=\"确定要删除此用例吗？\"\n                          confirm-button-text=\"确定\"\n                          cancel-button-text=\"取消\"\n                          @confirm=\"delCase(scope.row.id)\">\n                          <template #reference>\n                            <el-icon><Delete /></el-icon>\n                          </template>\n                        </el-popconfirm>\n                      </el-button>\n                    </el-tooltip>\n                  </div>\n              </template>\n            </el-table-column>\n    </el-table>\n    </div>\n\n          <!-- 分页器 -->\n    <div class=\"pagination-container\">\n      <el-pagination  background layout=\"total, prev, pager, next, jumper\"\n                    @current-change=\"currentPages\"\n                    :default-page-size=\"100\"\n                    :total=\"pages.count\"\n                    :current-page=\"pages.current\"\n                   next-text=\"下一页\" prev-text=\"上一页\">\n      </el-pagination>\n    </div>\n    </el-scrollbar>\n      </div>\n  </div>\n\n    <!-- 新增用例弹窗 -->\n    <el-dialog \n      v-model=\"addDlg\" \n      custom-class=\"custom-dialog\"\n      width=\"550px\" \n      destroy-on-close\n      :show-close=\"false\"\n      :close-on-click-modal=\"false\">\n      <template #header>\n        <div class=\"dialog-header\">\n          <h3 class=\"dialog-title\">创建新测试用例</h3>\n        </div>\n      </template>\n      <div class=\"dialog-content\">\n        <el-form \n          :model=\"addForm\"  \n          :rules=\"rulesCase\" \n          ref=\"CaseRef\" \n          label-position=\"top\" \n          class=\"case-form\">\n          <el-form-item label=\"用例名称\" prop=\"name\">\n            <el-input \n              v-model=\"addForm.name\" \n              placeholder=\"输入用例名称\"\n              maxlength=\"50\"\n              show-word-limit\n              class=\"form-input\"/>\n      </el-form-item>\n          <el-form-item label=\"所属项目\" prop=\"project_id\">\n            <el-input \n              v-model=\"addForm.project_id\" \n              class=\"form-input\" \n              disabled>\n            </el-input>\n      </el-form-item>\n\t\t\t<el-form-item label=\"用例描述\" prop=\"desc\">\n            <el-input \n              type=\"textarea\" \n              v-model=\"addForm.desc\" \n              placeholder=\"输入用例描述\"\n              rows=\"4\"\n              maxlength=\"200\"\n              show-word-limit\n              class=\"form-input\"/>\n      </el-form-item>\n\t\t</el-form>\n      </div>\n\t\t<template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"clearValidation\" class=\"cancel-btn\">\n            取消\n          </el-button>\n          <el-button type=\"primary\" @click=\"addCase\" :loading=\"submitting\" class=\"confirm-btn\">\n            确认创建\n          </el-button>\n        </div>\n\t\t</template>\n\t</el-dialog>\n\n  <!-- 显示运行结果 -->\n  <el-drawer v-model=\"ResultDlg\" :with-header=\"false\" size=\"50%\">\n\t\t<div style=\"padding:20px;\">\n\t\t\t<el-descriptions title=\"执行结果\" border :column=\"4\" style=\"text-align: center;\">\n\t\t\t\t<el-descriptions-item label=\"总数\" ><b style=\"color: #00aaff\">{{ runScentResult.all }}</b></el-descriptions-item>\n\t\t\t\t<el-descriptions-item label=\"通过\"><b style=\"color: #00aa7f\">{{ runScentResult.success }}</b></el-descriptions-item>\n\t\t\t\t<el-descriptions-item label=\"失败\"><b style=\"color: orangered\">{{ runScentResult.fail }}</b></el-descriptions-item>\n\t\t\t\t<el-descriptions-item label=\"错误\"><b style=\"color: #fca130\">{{ runScentResult.error }}</b></el-descriptions-item>\n\t\t\t</el-descriptions>\n\t\t\t<div style=\"height: 40px;line-height: 40px;\"><b>执行详情</b></div>\n\t\t\t<el-scrollbar height=\"calc(100vh - 180px)\">\n\t\t\t\t<el-table :data=\"runScentResult.cases\" style=\"width: 100%\" empty-text=\"暂无数据\">\n\t\t\t\t\t<el-table-column type=\"expand\">\n\t\t\t\t\t\t<template #default=\"props\">\n\t\t\t\t\t\t\t<caseResult :result=\"props.row\"></caseResult>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column label=\"步骤名\" prop=\"name\" />\n\t\t\t\t\t<el-table-column label=\"请求方法\" prop=\"method\">\n            <template #default=\"props\">\n               <span v-if=\"props.row.type === 'api'\">{{ props.row.method }}</span>\n\t\t\t\t\t\t</template>\n          </el-table-column>\n\t\t\t\t\t<el-table-column label=\"响应状态码\" prop=\"status_cede\">\n            <template #default=\"props\">\n               <span v-if=\"props.row.type === 'api'\">{{ props.row.status_cede }}</span>\n\t\t\t\t\t\t</template>\n          </el-table-column>\n\t\t\t\t\t<el-table-column label=\"执行结果\" prop=\"state\" min-width=\"40px\">\n\t\t\t\t\t\t<template #default=\"props\">\n\t\t\t\t\t\t\t<span v-if=\"props.row.state == '成功'\" style=\"color: #00AA7F;\">{{ props.row.state }}</span>\n              <span v-else-if=\"props.row.state == '错误'\" style=\"color: #fca130;\">{{ props.row.state }}</span>\n\t\t\t\t\t\t\t<span v-else style=\"color:#F56C6C\">{{ props.row.state }}</span>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t</el-table>\n\t\t\t</el-scrollbar>\n\t\t</div>\n\t</el-drawer>\n  </div>\n</template>\n\n<script>\nimport { mapMutations, mapState } from \"vuex\";\nimport caseResult from '../../components/common/caseResult.vue';\nimport { ElMessage, ElNotification, ElMessageBox, ElLoading } from \"element-plus\";\nimport * as echarts from 'echarts/core';\nimport { PieChart } from 'echarts/charts';\nimport { LabelLayout } from 'echarts/features';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport { \n  Plus, View, Search, VideoPlay, Management, Document, Delete, Grid, List,\n  CircleCheck, CircleClose, Warning, InfoFilled, WarningFilled, Close, Check,\n  User, Monitor, Connection, Collection, Operation, ChatDotRound, Avatar, Edit,\n  SetUp, DataAnalysis, Folder, Histogram, PieChart as PieChartIcon, Opportunity,\n  Refresh, UserFilled\n} from '@element-plus/icons-vue';\n\necharts.use([PieChart, LabelLayout, CanvasRenderer]);\n\nexport default {\n  computed: {\n    ...mapState(['pro', 'envId', 'testEnvs']),\n    username() {\n\t\t\treturn window.sessionStorage.getItem('username');\n\t\t},\n   currentEnv() {\n    if (!this.envId || !this.testEnvs || this.testEnvs.length === 0) return null;\n    return this.testEnvs.find(env => env.id === this.envId) || this.testEnvs[0];\n  },\n  },\n  components: {\n    caseResult,\n    Plus, View, Search, VideoPlay, Management, Document, Delete, Grid, List,\n    CircleCheck, CircleClose, Warning, InfoFilled, WarningFilled, Close, Check,\n    User, Monitor, Connection, Collection, Operation, ChatDotRound, Avatar, Edit,\n    SetUp, DataAnalysis, Folder, Histogram, PieChartIcon, Opportunity, Refresh,\n    UserFilled\n  },\n  data() {\n    return {\n      filterText: '',\n      pages: {},\n      caseList: [],\n      ResultDlg: false,\n      runScentResult: {},\n      addDlg: false,\n      checked: false,\n      submitting: false,\n      pageSize: 10,\n      chart: null,\n      iconColors: [\n        'linear-gradient(135deg, #1890ff, #2979ff)',\n        'linear-gradient(135deg, #38b6ff, #59c1ff)',\n        'linear-gradient(135deg, #0080ff, #42a5f5)',\n        'linear-gradient(135deg, #2196f3, #1976d2)',\n        'linear-gradient(135deg, #3f51b5, #303f9f)'\n      ],\n      rulesCase: {\n\t\t\t\tname: [\n\t\t\t\t\t{\n\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\tmessage: '请输入用例名称',\n\t\t\t\t\t\ttrigger: 'blur'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tproject_id: [\n\t\t\t\t\t{\n\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\tmessage: '请选择项目',\n\t\t\t\t\t\ttrigger: 'blur'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t},\n      addForm: {\n        name: '',\n        project_id: '',\n        desc: '',\n        creator: '',\n        stepCount: 0,\n      },\n      colorMap: {}, // 用于存储用户头像的颜色\n      loadingConfig: {\n        spinner: 'Loading', // 修改为Element Plus图标组件名称\n      }\n    }\n  },\n methods: {\n    ...mapMutations(['CaseInfo']),\n    \n    // 设置表格单元格样式\n    cellClassName({row, column, rowIndex, columnIndex}) {\n      if (column.label === '步骤' && row.stepCount <= 0) {\n        return 'empty-steps-cell';\n      }\n      return '';\n    },\n\n    \n    // 获取用例图标颜色\n    getCaseIconColor(stepCount) {\n      if (stepCount <= 0) {\n        return 'linear-gradient(135deg, #f5a623, #f27121)';\n      }\n      const index = (stepCount * 7) % this.iconColors.length;\n      return this.iconColors[index];\n    },\n    \n    // 获取步骤环形图样式类\n    getStepClass(count) {\n      if (count <= 0) return 'empty-chart';\n      if (count < 3) return 'low-chart';\n      if (count < 10) return 'medium-chart';\n      return 'high-chart';\n    },\n    \n    // 获取状态码样式类型\n    getStatusBadgeType(code) {\n      if (!code) return '';\n      code = parseInt(code);\n      if (code >= 200 && code < 300) return 'success';\n      if (code >= 300 && code < 400) return 'info';\n      if (code >= 400 && code < 500) return 'warning';\n      if (code >= 500) return 'danger';\n      return '';\n    },\n    \n    // 表格行样式\n    tableRowClassName({row, rowIndex}) {\n      if (row.stepCount <= 0) {\n        return 'empty-steps-row';\n      }\n      return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';\n    },\n    \n    // 行点击事件\n    handleRowClick(row) {\n      this.clickEdit(row);\n    },\n    \n    // 获取步骤百分比 (用于环形进度条)\n    getStepPercentage(count) {\n      if (!count || count <= 0) return 0;\n      return Math.min(count * 10, 100); // 每10个步骤为100%\n    },\n    \n    // 获取结果状态类名\n    getResultClassName(state) {\n      if (state === '成功') return 'result-success';\n      if (state === '错误') return 'result-warning';\n      return 'result-error';\n    },\n    \n    // 获取步骤颜色\n    getStepColor(count) {\n      if (!count || count <= 0) return '#909399';\n      if (count < 3) return '#E6A23C'; // 少于3个步骤黄色\n      if (count < 10) return '#409EFF'; // 少于10个步骤蓝色\n      return '#67C23A'; // 超过10个步骤绿色\n    },\n    \n    // 获取头像颜色\n    getAvatarColor(username) {\n      if (!username) return '#909399';\n      if (!this.colorMap[username]) {\n        // 生成一个随机但固定的颜色\n        const colors = [\n          '#f56c6c', '#e6a23c', '#409eff', '#67c23a', '#909399',\n          '#8a2be2', '#00bcd4', '#ff9800', '#9c27b0', '#2196f3'\n        ];\n        const index = username.charCodeAt(0) % colors.length;\n        this.colorMap[username] = colors[index];\n      }\n      return this.colorMap[username];\n    },\n    \n    // 获取头像文本\n    getAvatarText(username) {\n      if (!username) return '?';\n      return username.substring(0, 1).toUpperCase();\n    },\n    \n    // 格式化时间\n    formatTime(timestamp) {\n      if (!timestamp) return '未知时间';\n      return this.$tools.rTime(timestamp);\n    },\n    \n    // 获取请求方法类型\n    getMethodType(method) {\n      if (!method) return '';\n      method = method.toUpperCase();\n      switch (method) {\n        case 'GET': return 'success';\n        case 'POST': return 'primary';\n        case 'PUT': return 'warning';\n        case 'DELETE': return 'danger';\n        case 'PATCH': return 'info';\n        default: return '';\n      }\n    },\n    \n    // 获取状态码类型\n    getStatusType(code) {\n      if (!code) return '';\n      code = parseInt(code);\n      if (code >= 200 && code < 300) return 'success';\n      if (code >= 300 && code < 400) return 'info';\n      if (code >= 400 && code < 500) return 'warning';\n      if (code >= 500) return 'danger';\n      return '';\n    },\n    \n    // 获取成功率\n    getSuccessRate() {\n      if (!this.runScentResult.all || this.runScentResult.all <= 0) return 0;\n      return Math.round((this.runScentResult.success / this.runScentResult.all) * 100);\n    },\n    \n    // 获取失败率\n    getFailRate() {\n      if (!this.runScentResult.all || this.runScentResult.all <= 0) return 0;\n      return Math.round((this.runScentResult.fail / this.runScentResult.all) * 100);\n    },\n    \n    // 获取错误率\n    getErrorRate() {\n      if (!this.runScentResult.all || this.runScentResult.all <= 0) return 0;\n      return Math.round((this.runScentResult.error / this.runScentResult.all) * 100);\n    },\n    \n    // 初始化图表\n    initChart() {\n      if (!this.ResultDlg || !this.runScentResult.all) return;\n      \n      this.$nextTick(() => {\n        if (this.$refs.chartContainer) {\n          if (this.chart) {\n            this.chart.dispose();\n          }\n          \n          this.chart = echarts.init(this.$refs.chartContainer);\n          \n          // 主题色和渐变\n          const successColor = {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [{\n              offset: 0,\n              color: '#67C23A'\n            }, {\n              offset: 1,\n              color: '#4caf50'\n            }]\n          };\n          \n          const failColor = {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 1,\n            y2: 1,\n            colorStops: [{\n              offset: 0,\n              color: '#F56C6C'\n            }, {\n              offset: 1,\n              color: '#E53935'\n            }]\n          };\n          \n          const errorColor = {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 1,\n            y2: 0,\n            colorStops: [{\n              offset: 0,\n              color: '#E6A23C'\n            }, {\n              offset: 1,\n              color: '#FFA000'\n            }]\n          };\n          \n          const option = {\n            backgroundColor: 'transparent',\n            tooltip: {\n              trigger: 'item',\n              formatter: '{b}: {c} ({d}%)',\n              backgroundColor: 'rgba(30, 30, 45, 0.7)',\n              borderColor: 'rgba(80, 80, 180, 0.5)',\n              textStyle: {\n                color: '#fff'\n              }\n            },\n            legend: {\n              orient: 'vertical',\n              right: 10,\n              top: 'center',\n              textStyle: {\n                color: '#e0e0e0'\n              },\n              itemWidth: 14,\n              itemHeight: 14,\n              itemGap: 20,\n              icon: 'circle'\n            },\n            series: [\n              {\n                name: '执行结果',\n                type: 'pie',\n                radius: ['40%', '70%'],\n                avoidLabelOverlap: false,\n                itemStyle: {\n                  borderRadius: 10,\n                  borderColor: 'rgba(30, 30, 45, 0.8)',\n                  borderWidth: 2\n                },\n                label: {\n                  show: false\n                },\n                emphasis: {\n                  label: {\n                    show: true,\n                    fontSize: '18',\n                    fontWeight: 'bold',\n                    color: '#ffffff'\n                  },\n                  scaleSize: 15\n                },\n                labelLine: {\n                  show: false\n                },\n                data: [\n                  { \n                    value: this.runScentResult.success || 0, \n                    name: '通过', \n                    itemStyle: { color: successColor } \n                  },\n                  { \n                    value: this.runScentResult.fail || 0, \n                    name: '失败', \n                    itemStyle: { color: failColor } \n                  },\n                  { \n                    value: this.runScentResult.error || 0, \n                    name: '错误', \n                    itemStyle: { color: errorColor } \n                  }\n                ],\n                animationType: 'scale',\n                animationEasing: 'elasticOut',\n                animationDelay: function (idx) {\n                  return Math.random() * 200;\n                }\n              }\n            ]\n          };\n          \n          this.chart.setOption(option);\n          window.addEventListener('resize', this.resizeChart);\n        }\n      });\n    },\n    \n    // 调整图表大小\n    resizeChart() {\n      if (this.chart) {\n        this.chart.resize();\n      }\n    },\n    \n    // 查询\n   searchClick() {\n      this.allTestCase(this.pro.id, this.pages.current, this.filterText);\n   },\n\n   // 点击编辑用例\n    clickEdit(row) {\n   this.$router.push({ name: 'TestCaseDetail' });\n      this.CaseInfo(row);\n   },\n\n   // 点击新增用例\n   clickAdd() {\n      this.addForm.project_id = this.pro.name;\n      this.addDlg = true;\n    },\n    \n    // 添加用例\n    async addCase() {\n      this.$refs.CaseRef.validate(async valid => {\n        if (!valid) return;\n        \n        this.submitting = true;\n        try {\n          const params = {...this.addForm};\n          params.creator = this.username;\n          params.project_id = this.pro.id;\n          \n    const response = await this.$api.createTestCase(params);\n      if (response.status === 201) {\n            ElNotification({\n              title: '创建成功',\n              message: '测试用例已成功创建',\n          type: 'success',\n              duration: 2000\n        });\n        this.addDlg = false;\n            this.allTestCase(this.pro.id);\n          }\n        } catch (error) {\n          ElMessage({\n            type: 'error',\n            message: '创建失败：' + (error.message || '未知错误'),\n            duration: 3000\n          });\n        } finally {\n          this.submitting = false;\n        }\n      });\n    },\n\n    // 切换只看自己创建\n    creatorCase() {\n       if (this.checked) {\n        // 只看自己创建的逻辑\n        this.allTestCase(this.pro.id, this.pages.current, this.filterText,this.username);\n      } else {\n        // 取消只看自己创建的逻辑\n        this.allTestCase();\n      }\n    },\n    \n    // 获取用例列表\n    async allTestCase(project=this.pro.id, page=this.pages.current, name, username) {\n      try {\n        const response = await this.$api.getTestCase(project, page, name, username);\n        if (response.status === 200) {\n          this.caseList = response.data.result || [];\n          this.pages = response.data || {};\n          this.addForm.project_id = this.pro.name;\n        }\n      } catch (error) {\n        ElMessage({\n          type: 'error',\n          message: '获取用例列表失败: ' + (error.message || '未知错误'),\n          duration: 3000\n        });\n      } finally {\n      }\n    },\n\n    // 页码变化\n   currentPages(currentPage) {\n      this.allTestCase(this.pro.id, currentPage, this.filterText, this.checked ? this.username : undefined);\n      this.pages.current = currentPage;\n    },\n    \n    // 每页条数变化\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPages(1);\n    },\n\n    // 删除用例\n    async delCase(id) {\n      try {\n        const loadingInstance = ElLoading.service({\n          text: '正在删除...',\n          background: 'rgba(30, 30, 45, 0.7)'\n        });\n        \n        const response = await this.$api.delTestCase(id);\n        \n        loadingInstance.close();\n        \n        if (response.status === 204) {\n          ElNotification({\n            title: '删除成功',\n            message: '测试用例已成功删除',\n            type: 'success',\n            duration: 2000\n          });\n          // 刷新页面\n          this.allTestCase(this.pro.id, this.pages.current, this.filterText, this.checked ? this.username : undefined);\n        }\n      } catch (error) {\n        ElMessage({\n          type: 'error',\n          message: '删除失败：' + (error.message || '未知错误'),\n          duration: 3000\n        });\n      }\n  },\n\n     // 运行测试用例\n  async runCase(data) {\n      const stepCount = parseInt(data.stepCount);\n      \n      if (stepCount <= 0) {\n        ElMessage({\n          type: 'warning',\n          message: '该用例没有步骤，请先添加步骤再运行',\n          duration: 2000\n        });\n        return;\n      }\n      \n      if (!this.envId) {\n        ElMessageBox.alert('请先选择执行环境后再运行用例', '未选择环境', {\n          confirmButtonText: '确定',\n          type: 'warning'\n        });\n        return;\n      }\n      \n      // 显示加载动画\n      const loadingInstance = ElLoading.service({\n        text: '正在执行测试用例，请稍候...',\n        spinner: 'Loading',\n        background: 'rgba(41, 121, 255, 0.3)'\n      });\n      \n      try {\n\t\t\t\tconst params = {\n\t\t\t\t\tenv: this.envId,\n\t\t\t\t\tscene: data.id\n\t\t\t\t};\n        \n\t\t\t\tconst response = await this.$api.runCases(data.id, params);\n\t\t\t\tif (response.status == 200) {\n          // 关闭加载动画\n          loadingInstance.close();\n          \n\t\t\t\t\t// 显示执行结果到窗口页面\n          this.runScentResult = response.data || {};\n\t\t\t\t\tthis.ResultDlg = true;\n          \n          // 初始化图表\n          this.initChart();\n          \n          // 根据结果显示不同通知\n          const successRate = this.getSuccessRate();\n          if (successRate === 100) {\n            ElNotification({\n              title: '执行成功',\n              message: `全部 ${this.runScentResult.all} 个步骤执行成功`,\n              type: 'success',\n              duration: 3000\n            });\n          } else if (successRate >= 80) {\n            ElNotification({\n              title: '部分成功',\n              message: `${this.runScentResult.success}/${this.runScentResult.all} 个步骤执行成功`,\n\t\t\t\t\ttype: 'warning',\n              duration: 3000\n            });\n          } else {\n            ElNotification({\n              title: '执行失败',\n              message: `仅 ${this.runScentResult.success}/${this.runScentResult.all} 个步骤执行成功`,\n              type: 'error',\n              duration: 3000\n            });\n          }\n        }\n      } catch (error) {\n        // 关闭加载动画\n        loadingInstance.close();\n        \n        ElMessage({\n          type: 'error',\n          message: '执行失败：' + (error.message || '未知错误'),\n          duration: 3000\n        });\n      }\n    },\n    \n    // 清除验证\n  clearValidation() {\n      this.addDlg = false;\n      this.$refs.CaseRef?.clearValidate(); // 清除验证信息\n      // 重置表单\n      this.addForm = {\n        name: '',\n        project_id: this.pro.name,\n        desc: '',\n        creator: '',\n        stepCount: 0,\n      };\n    },\n },\n created() {\n  this.allTestCase(this.pro.id);\n  },\n  mounted() {\n    // 检查环境ID\n    if (!this.envId) {\n      ElMessage({\n        type: 'info',\n        message: '请先选择执行环境以便运行测试用例',\n        duration: 3000\n      });\n    }\n  },\n  beforeUnmount() {\n    window.removeEventListener('resize', this.resizeChart);\n    if (this.chart) {\n      this.chart.dispose();\n      this.chart = null;\n    }\n  },\n  watch: {\n    ResultDlg(val) {\n      if (val) {\n        this.initChart();\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 基础样式 */\n.test-case-container {\n  padding: 20px;\n  min-height: 100vh;\n  background: #ffffff;\n  color: #333333;\n  position: relative;\n  overflow: hidden;\n}\n\n.test-case-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: \n    radial-gradient(circle at 10% 20%, rgba(64, 158, 255, 0.03) 0%, transparent 25%),\n    radial-gradient(circle at 80% 80%, rgba(100, 181, 246, 0.05) 0%, transparent 35%);\n  pointer-events: none;\n}\n\n/* 页面头部样式 */\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  border-bottom: 1px solid rgba(64, 158, 255, 0.2);\n  position: relative;\n}\n\n.logo-container {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.logo-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #1890ff, #2979ff);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  color: white;\n  box-shadow: 0 0 15px rgba(41, 121, 255, 0.3);\n}\n\n.logo-icon i {\n  font-size: 24px;\n}\n\n.logo-text {\n  display: flex;\n  flex-direction: column;\n}\n\n.text-gradient {\n  font-size: 28px;\n  font-weight: bold;\n  background: linear-gradient(to right, #1890ff, #2979ff);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  margin-bottom: 5px;\n}\n\n.env-badge {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(64, 158, 255, 0.2);\n  border-radius: 10px;\n  padding: 10px 15px;\n  backdrop-filter: blur(10px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n}\n\n.env-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: rgba(103, 194, 58, 0.15);\n  color: #67C23A;\n  font-size: 18px;\n}\n\n.env-icon i {\n  font-size: 18px;\n}\n\n.env-icon.warning {\n  background-color: rgba(230, 162, 60, 0.15);\n  color: #E6A23C;\n}\n\n.env-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.env-label {\n  font-size: 12px;\n  color: #666666;\n}\n\n.env-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: #67C23A;\n}\n\n.env-name.error {\n  color: #E6A23C;\n}\n\n/* 功能区样式 */\n.action-container {\n  margin-bottom: 20px;\n}\n\n.glass-panel {\n  background: rgba(255, 255, 255, 0.85);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  border: 1px solid rgba(64, 158, 255, 0.2);\n  padding: 15px 20px;\n  box-shadow: 0 8px 32px rgba(41, 121, 255, 0.05);\n  position: relative;\n  overflow: hidden;\n}\n\n.glass-panel::before {\n  content: '';\n  position: absolute;\n  top: -10%;\n  left: -10%;\n  width: 120%;\n  height: 120%;\n  background: linear-gradient(45deg, transparent, rgba(64, 158, 255, 0.03), transparent);\n  transform: rotate(30deg);\n  pointer-events: none;\n}\n\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n\n.panel-subtitle {\n  font-size: 14px;\n  color: #666666;\n}\n\n.highlight {\n  color: #1890ff;\n  font-weight: 600;\n}\n\n.panel-tools {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.search-box {\n  flex: 1;\n  max-width: 450px;\n  margin-right: 20px;\n}\n\n.glow-input {\n  width: 100%;\n}\n\n.glow-input :deep(.el-input__wrapper) {\n  background-color: rgba(255, 255, 255, 0.95) !important;\n  border: 1px solid rgba(64, 158, 255, 0.3) !important;\n  box-shadow: none !important;\n  border-radius: 8px 0 0 8px !important;\n  transition: all 0.3s;\n}\n\n.glow-input:hover :deep(.el-input__wrapper) {\n  border-color: rgba(64, 158, 255, 0.6) !important;\n  box-shadow: 0 0 5px rgba(64, 158, 255, 0.2) !important;\n}\n\n.glow-input :deep(.el-input__inner) {\n  color: #333333 !important;\n}\n\n.glow-input :deep(.el-input__prefix) {\n  color: #1890ff;\n}\n\n.glow-input :deep(.el-input__prefix) i {\n  font-size: 18px;\n}\n\n.glow-input :deep(.el-input-group__append) {\n  padding: 0 !important;\n  border: none !important;\n  background-color: transparent !important;\n}\n\n.neon-btn-primary {\n  background: linear-gradient(45deg, #1890ff, #2979ff) !important;\n  border: none !important;\n  color: white !important;\n  padding: 0 20px !important;\n  height: 100% !important;\n  border-radius: 0 8px 8px 0 !important;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  box-shadow: 0 0 10px rgba(41, 121, 255, 0.3);\n  transition: all 0.3s;\n  margin-left: -1px !important;\n}\n\n.neon-btn-primary:hover {\n  box-shadow: 0 0 15px rgba(41, 121, 255, 0.5);\n  transform: translateY(-1px);\n}\n\n.neon-btn-primary i {\n  font-size: 16px;\n  margin-right: 5px;\n}\n\n.neon-btn-add {\n  background: linear-gradient(45deg, #1890ff, #2979ff) !important;\n  border: none !important;\n  border-radius: 8px !important;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  box-shadow: 0 0 15px rgba(41, 121, 255, 0.2);\n  transition: all 0.3s;\n}\n\n.neon-btn-add:hover {\n  box-shadow: 0 0 20px rgba(41, 121, 255, 0.4);\n  transform: translateY(-2px);\n}\n\n.neon-btn-add i {\n  font-size: 16px;\n  margin-right: 5px;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.creator-switch {\n  --el-switch-on-color: #1890ff;\n  --el-switch-off-color: #999999;\n}\n\n/* 表格区域样式 */\n.main-content {\n  background-color: rgba(255, 255, 255, 0.8);\n  border-radius: 10px;\n  padding: 15px;\n  box-shadow: 0 8px 24px rgba(41, 121, 255, 0.05);\n}\n\n.table-container {\n  position: relative;\n}\n\n.case-row {\n  transition: all 0.3s;\n}\n\n.case-row:hover {\n  background-color: rgba(230, 244, 255, 0.7) !important;\n}\n\n.table-header {\n  background-color: rgba(240, 248, 255, 0.7) !important;\n  color: #1890ff !important;\n  font-weight: 600 !important;\n  border-bottom: 2px solid rgba(64, 158, 255, 0.2) !important;\n  height: 50px !important;\n  text-align: center !important;\n}\n\n.column-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.column-name-header {\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  gap: 8px;\n  padding-left: 10px;\n}\n\n.column-header i, .column-name-header i {\n  font-size: 18px;\n  color: #1890ff;\n  display: flex;\n  align-items: center;\n}\n\n\n/* 表格单元格样式 */\n.case-index {\n  width: 34px;\n  height: 34px;\n  border-radius: 17px;\n  background: linear-gradient(135deg, #1890ff, #2979ff);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  margin: 0 auto;\n  box-shadow: 0 4px 8px rgba(41, 121, 255, 0.15);\n}\n\n.case-name {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  color: #333333;\n  justify-content: flex-start;\n}\n\n.case-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n\n.case-icon i {\n  font-size: 18px;\n}\n\n.project-name {\n  margin: 0;\n}\n\n.description {\n  color: #666666;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 图表样式 */\n.circular-chart {\n  display: block;\n  margin: 0 auto;\n  max-width: 100%;\n  max-height: 40px;\n}\n\n.circle-bg {\n  fill: none;\n  stroke: rgba(200, 200, 200, 0.3);\n  stroke-width: 3;\n}\n\n.circle {\n  fill: none;\n  stroke-width: 3;\n  stroke-linecap: round;\n  transition: all 0.3s;\n}\n\n.high-chart .circle {\n  stroke: #67C23A;\n}\n\n.medium-chart .circle {\n  stroke: #409EFF;\n}\n\n.low-chart .circle {\n  stroke: #E6A23C;\n}\n\n.empty-chart .circle {\n  stroke: #909399;\n}\n\n.percentage {\n  fill: #333333;\n  font-size: 12px;\n  text-anchor: middle;\n  font-weight: bold;\n}\n\n.empty-steps-cell {\n  background-color: rgba(229, 57, 53, 0.05) !important;\n}\n\n/* 用户信息 */\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #1890ff, #2979ff);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 16px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);\n}\n\n.self-avatar {\n  background: linear-gradient(135deg, #1890ff, #2979ff);\n  box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);\n}\n\n.info-detail {\n  display: flex;\n  flex-direction: column;\n  text-align: left;\n}\n\n.username {\n  font-weight: 500;\n  margin-bottom: 4px;\n  color: #333333;\n}\n\n.self-name {\n  color: #1890ff;\n  position: relative;\n}\n\n.self-name::after {\n  content: '(我)';\n  margin-left: 4px;\n  font-size: 12px;\n  color: #64b5f6;\n}\n\n.time {\n  font-size: 12px;\n  color: #666666;\n}\n\n.no-update {\n  color: #999999;\n  font-style: italic;\n}\n\n/* 操作按钮 */\n.actions-group {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.action-btn {\n  position: relative;\n  width: 36px !important;\n  height: 36px !important;\n  font-size: 16px !important;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);\n  border: none !important;\n  transition: all 0.3s !important;\n}\n\n.action-btn i {\n  font-size: 16px;\n}\n\n.action-btn::after {\n  content: '';\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  border-radius: 50%;\n  z-index: -1;\n  opacity: 0;\n  transition: opacity 0.3s;\n}\n\n.action-btn:hover {\n  transform: translateY(-2px);\n}\n\n.action-btn:hover::after {\n  opacity: 1;\n}\n\n.run-btn {\n  background: linear-gradient(135deg, #67C23A, #4caf50) !important;\n}\n\n.run-btn::after {\n  background: radial-gradient(circle at 50% 50%, rgba(103, 194, 58, 0.4), transparent 70%);\n}\n\n.edit-btn {\n  background: linear-gradient(135deg, #1890ff, #2979ff) !important;\n}\n\n.edit-btn::after {\n  background: radial-gradient(circle at 50% 50%, rgba(24, 144, 255, 0.4), transparent 70%);\n}\n\n.delete-btn {\n  background: linear-gradient(135deg, #F56C6C, #e53935) !important;\n}\n\n.delete-btn::after {\n  background: radial-gradient(circle at 50% 50%, rgba(245, 108, 108, 0.4), transparent 70%);\n}\n\n.action-btn.disabled {\n  background: #909399 !important;\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 分页器 */\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n}\n\n/* 对话框样式 */\n.dialog-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.dialog-icon {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #1890ff, #2979ff);\n  color: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\n}\n\n.dialog-icon i {\n  font-size: 20px;\n}\n\n.dialog-title {\n  margin: 0;\n  color: #1890ff;\n  font-size: 20px;\n}\n\n.case-form {\n  margin-top: 10px;\n}\n\n.form-input {\n  width: 100%;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n\n.cancel-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.cancel-btn i {\n  font-size: 16px;\n}\n\n.confirm-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  background: linear-gradient(135deg, #1890ff, #2979ff) !important;\n  border: none !important;\n}\n\n.confirm-btn i {\n  font-size: 16px;\n}\n\n/* 执行结果抽屉样式 */\n.drawer-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.drawer-title {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.drawer-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  background: linear-gradient(135deg, #1890ff, #2979ff);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\n}\n\n.drawer-icon i {\n  font-size: 20px;\n}\n\n.drawer-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.drawer-name {\n  font-size: 18px;\n  font-weight: 600;\n  color: #1890ff;\n}\n\n.drawer-time {\n  font-size: 12px;\n  color: #666666;\n}\n\n.refresh-btn {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(24, 144, 255, 0.2);\n  transition: all 0.3s;\n}\n\n.refresh-btn:hover {\n  transform: rotate(180deg);\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.refresh-btn i {\n  font-size: 18px;\n  color: #1890ff;\n}\n\n/* 抽屉内容样式 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20px;\n  padding: 20px;\n}\n\n.stats-card {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 12px;\n  padding: 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n  border: 1px solid rgba(24, 144, 255, 0.05);\n  transition: all 0.3s;\n}\n\n.stats-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);\n}\n\n.card-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.card-title {\n  font-size: 14px;\n  color: #666666;\n  margin-bottom: 5px;\n}\n\n.card-value {\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n}\n\n.card-desc {\n  font-size: 12px;\n  color: #999999;\n}\n\n.card-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(240, 240, 250, 0.7);\n}\n\n.card-icon i {\n  font-size: 24px;\n}\n\n.total-card .card-icon {\n  color: #1890ff;\n}\n\n.success-card .card-icon {\n  color: #67C23A;\n}\n\n.fail-card .card-icon {\n  color: #F56C6C;\n}\n\n.error-card .card-icon {\n  color: #E6A23C;\n}\n\n.total-card .card-value {\n  color: #1890ff;\n}\n\n.success-card .card-value {\n  color: #67C23A;\n}\n\n.fail-card .card-value {\n  color: #F56C6C;\n}\n\n.error-card .card-value {\n  color: #E6A23C;\n}\n\n/* 结果仪表盘 */\n.result-dashboard {\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 20px;\n  padding: 0 20px 20px;\n}\n\n.chart-area {\n  background: white;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n}\n\n.chart-header {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1890ff;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid rgba(24, 144, 255, 0.1);\n}\n\n.chart-container {\n  flex: 1;\n  min-height: 250px;\n}\n\n.detail-area {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  border-bottom: 1px solid rgba(24, 144, 255, 0.1);\n}\n\n.detail-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1890ff;\n}\n\n.detail-summary {\n  display: flex;\n  gap: 10px;\n}\n\n\n.detail-table {\n  width: 100%;\n}\n\n.expand-content {\n  padding: 15px;\n}\n\n.type-tag {\n  font-weight: 500;\n}\n\n.result-tag {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 5px;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.result-tag i {\n  font-size: 14px;\n}\n\n.result-success {\n  color: #67C23A;\n  background: rgba(103, 194, 58, 0.1);\n  border: 1px solid rgba(103, 194, 58, 0.2);\n}\n\n.result-warning {\n  color: #E6A23C;\n  background: rgba(230, 162, 60, 0.1);\n  border: 1px solid rgba(230, 162, 60, 0.2);\n}\n\n.result-error {\n  color: #F56C6C;\n  background: rgba(245, 108, 108, 0.1);\n  border: 1px solid rgba(245, 108, 108, 0.2);\n}\n\n/* 自定义对话框和抽屉类 */\n.custom-dialog {\n  border-radius: 15px;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid rgba(24, 144, 255, 0.2);\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);\n}\n\n.custom-dialog :deep(.el-dialog__header) {\n  padding: 20px;\n  border-bottom: 1px solid rgba(24, 144, 255, 0.1);\n  background: linear-gradient(90deg, rgba(240, 248, 255, 0.9), rgba(236, 246, 255, 0.9));\n}\n\n.custom-dialog :deep(.el-dialog__body) {\n  padding: 20px;\n  background: white;\n}\n\n.custom-dialog :deep(.el-dialog__footer) {\n  padding: 15px 20px;\n  border-top: 1px solid rgba(24, 144, 255, 0.1);\n  background: rgba(240, 248, 255, 0.5);\n}\n\n.result-drawer :deep(.el-drawer__header) {\n  margin-bottom: 0;\n  padding: 20px;\n  border-bottom: 1px solid rgba(24, 144, 255, 0.1);\n  background: linear-gradient(90deg, rgba(240, 248, 255, 0.95), rgba(236, 246, 255, 0.95));\n}\n\n.result-drawer :deep(.el-drawer__body) {\n  padding: 0;\n  background: white;\n}\n\n/* 表格悬停效果 */\n.el-table__row:hover {\n  background-color: rgba(230, 244, 255, 0.7) !important;\n}\n\n/* 斑马纹效果 */\n.el-table__row--striped td {\n  background-color: rgba(248, 250, 252, 0.7) !important;\n}\n\n.el-table {\n  background-color: transparent !important;\n  border-radius: 8px !important;\n  overflow: hidden !important;\n}\n\n.el-table::before {\n  display: none !important;\n}\n\n/* 表格边框 */\n.el-table td.el-table__cell, \n.el-table th.el-table__cell {\n  border-bottom: 1px solid rgba(64, 158, 255, 0.1) !important;\n}\n\n.el-table .cell {\n  text-align: center !important;\n}\n\n.el-table .case-name-column .cell {\n  text-align: left !important;\n}\n</style>\n", "import { render } from \"./TestCase.vue?vue&type=template&id=642df5aa&scoped=true\"\nimport script from \"./TestCase.vue?vue&type=script&lang=js\"\nexport * from \"./TestCase.vue?vue&type=script&lang=js\"\n\nimport \"./TestCase.vue?vue&type=style&index=0&id=642df5aa&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-642df5aa\"]])\n\nexport default __exports__"], "names": ["class", "style", "_createVNode", "_component_el_tabs", "type", "value", "size", "$props", "result", "_createBlock", "_component_el_tab_pane", "label", "name", "response_header", "_createElementBlock", "_hoisted_1", "includes", "_hoisted_2", "_component_Editor", "readOnly", "response_body", "$event", "lang", "theme", "_hoisted_3", "_component_el_scrollbar", "height", "onWheel", "_cache", "_withModifiers", "innerHTML", "_hoisted_4", "_Fragment", "_renderList", "key", "_component_el_tag", "_createElementVNode", "_hoisted_5", "_toDisplayString", "requests_body", "_hoisted_6", "_component_el_collapse", "$data", "activeNames", "_component_el_collapse_item", "title", "_withCtx", "method", "url", "requests_header", "_hoisted_7", "log_data", "item", "index", "_hoisted_8", "disabled", "state", "_hoisted_9", "_hoisted_10", "_hoisted_11", "status_cede", "_hoisted_12", "_hoisted_13", "run_time", "showbtn", "_hoisted_14", "_component_el_button", "onClick", "$options", "getInterfaces", "plain", "_component_el_dialog", "addBugDlg", "width", "closeDialogResult", "footer", "_hoisted_15", "saveBug", "_component_el_form", "model", "bugForm", "_component_el_form_item", "_component_el_select", "interface", "placeholder", "interfaces", "iter", "_component_el_option", "id", "_component_el_input", "autosize", "minRows", "maxRows", "desc", "autocomplete", "props", "default", "computed", "mapState", "components", "Editor", "data", "info", "status", "methods", "this", "project", "pro", "response", "$api", "createBugs", "$message", "message", "duration", "getNewInterfaces", "__exports__", "render", "x", "y", "pages", "count", "filterText", "clearable", "append", "searchClick", "_component_el_switch", "checked", "onChange", "creatorCase", "clickAdd", "_component_el_icon", "_component_Plus", "_ctx", "envId", "_component_Connection", "_hoisted_16", "currentEnv", "_hoisted_17", "_hoisted_18", "_component_Warning", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_el_table", "caseList", "cursor", "cellClassName", "onRowClick", "handleRowClick", "_component_el_table_column", "align", "header", "scope", "_hoisted_22", "$index", "_hoisted_23", "_component_Document", "_hoisted_24", "_normalizeStyle", "getCaseIconColor", "row", "stepCount", "_hoisted_25", "_component_Folder", "_hoisted_26", "effect", "_hoisted_27", "_component_List", "_hoisted_28", "viewBox", "_normalizeClass", "getStepClass", "d", "_hoisted_30", "_hoisted_31", "_component_ChatDotRound", "_hoisted_32", "_component_el_tooltip", "content", "placement", "length", "_hoisted_33", "_component_UserFilled", "_hoisted_34", "creator", "username", "getAvatarText", "_hoisted_35", "_hoisted_36", "formatTime", "create_time", "_hoisted_37", "_component_Edit", "update_time", "_hoisted_38", "_hoisted_39", "modifier", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "fixed", "_hoisted_44", "_component_Operation", "_hoisted_45", "runCase", "circle", "_component_VideoPlay", "clickEdit", "_component_SetUp", "_component_el_popconfirm", "onConfirm", "delCase", "reference", "_component_Delete", "_hoisted_46", "_component_el_pagination", "background", "layout", "onCurrentChange", "currentPages", "total", "current", "addDlg", "_hoisted_48", "clearValidation", "addCase", "loading", "submitting", "_hoisted_47", "addForm", "rules", "rulesCase", "ref", "prop", "maxlength", "project_id", "rows", "_component_el_drawer", "ResultDlg", "_hoisted_49", "_component_el_descriptions", "border", "column", "_component_el_descriptions_item", "_hoisted_50", "runScentResult", "all", "_hoisted_51", "success", "_hoisted_52", "fail", "_hoisted_53", "error", "cases", "_component_caseResult", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "echarts", "<PERSON><PERSON><PERSON>", "LabelLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "sessionStorage", "getItem", "testEnvs", "find", "env", "caseResult", "Plus", "View", "Search", "VideoPlay", "Management", "Document", "Delete", "Grid", "List", "CircleCheck", "CircleClose", "Warning", "InfoFilled", "WarningFilled", "Close", "Check", "User", "Monitor", "Connection", "Collection", "Operation", "ChatDotRound", "Avatar", "Edit", "SetUp", "DataAnalysis", "Folder", "Histogram", "PieChartIcon", "Opportunity", "Refresh", "UserFilled", "pageSize", "chart", "iconColors", "required", "trigger", "colorMap", "loadingConfig", "spinner", "mapMutations", "rowIndex", "columnIndex", "getStatusBadgeType", "code", "parseInt", "tableRowClassName", "getStepPercentage", "Math", "min", "getResultClassName", "getStepColor", "getAvatarColor", "colors", "charCodeAt", "substring", "toUpperCase", "timestamp", "$tools", "rTime", "getMethodType", "getStatusType", "getSuccessRate", "round", "getFailRate", "getErrorRate", "initChart", "$nextTick", "$refs", "chartContainer", "dispose", "successColor", "x2", "y2", "colorStops", "offset", "color", "failColor", "errorColor", "option", "backgroundColor", "tooltip", "formatter", "borderColor", "textStyle", "legend", "orient", "right", "top", "itemWidth", "itemHeight", "itemGap", "icon", "series", "radius", "avoidLabelOverlap", "itemStyle", "borderRadius", "borderWidth", "show", "emphasis", "fontSize", "fontWeight", "scaleSize", "labelLine", "animationType", "animationEasing", "animationDelay", "idx", "random", "setOption", "addEventListener", "resizeChart", "resize", "allTestCase", "$router", "push", "CaseInfo", "CaseRef", "validate", "async", "valid", "params", "createTestCase", "ElNotification", "ElMessage", "page", "getTestCase", "currentPage", "undefined", "handleSizeChange", "loadingInstance", "ElLoading", "service", "text", "delTestCase", "close", "ElMessageBox", "alert", "confirmButtonText", "scene", "runCases", "successRate", "clearValidate", "created", "mounted", "beforeUnmount", "removeEventListener", "watch", "val"], "sourceRoot": ""}