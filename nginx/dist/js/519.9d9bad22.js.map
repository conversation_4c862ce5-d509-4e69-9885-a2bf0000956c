{"version": 3, "file": "js/519.9d9bad22.js", "mappings": "wMACOA,MAAM,uB,GAIAA,MAAM,gB,GAoHNA,MAAM,gB,GAEFA,MAAM,kB,GACJA,MAAM,wB,GAGNA,MAAM,e,GACJA,MAAM,gB,GAOVA,MAAM,kB,GACJA,MAAM,6B,GAGNA,MAAM,e,GACJA,MAAM,gB,GAOVA,MAAM,kB,GACJA,MAAM,0B,GAGNA,MAAM,e,GACJA,MAAM,gB,GAOVA,MAAM,kB,GAIJA,MAAM,e,GACJA,MAAM,gB,GAUVA,MAAM,gB,GAURC,IAAI,iBAAiBC,MAAA,kB,GAWrBF,MAAM,e,GAKNA,MAAM,e,GAKNA,MAAM,e,GAKNA,MAAM,e,GAONA,MAAM,oB,GACJA,MAAM,kB,GAEHA,MAAM,oB,GAYXA,MAAM,iB,GAwBNA,MAAM,iB,GAQHA,MAAM,kB,GAGTA,MAAM,iB,GAQHA,MAAM,kB,GAGTA,MAAM,iB,GAEJA,MAAM,c,GA4FTA,MAAM,iB,itBAnXlBG,EAAAA,EAAAA,IAyXM,MAzXNC,EAyXM,EAvXJC,EAAAA,EAAAA,IAgHUC,GAAA,CAhHDN,MAAM,eAAeO,OAAO,S,CACxBC,QAAMC,EAAAA,EAAAA,IACf,IAgBM,EAhBNC,EAAAA,EAAAA,IAgBM,MAhBNC,EAgBM,C,eAfJD,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVL,EAAAA,EAAAA,IAakBO,EAAA,M,iBAZhB,IAOY,EAPZP,EAAAA,EAAAA,IAOYQ,EAAA,CANTC,KAAMC,EAAAC,UAAY,SAAW,UAC7BC,QAASF,EAAAG,WACTC,QAAOC,EAAAC,WACPC,UAAWP,EAAAQ,c,kBACZ,IAAuE,EAAvElB,EAAAA,EAAAA,IAAuEmB,EAAA,M,iBAA9D,IAA+B,CAAbT,EAAAC,Y,WAAaS,EAAAA,EAAAA,IAAqBC,EAAA,CAAAC,IAAA,O,WAApDF,EAAAA,EAAAA,IAA+BG,EAAA,CAAAD,IAAA,O,eAA+B,KACvEE,EAAAA,EAAAA,IAAGd,EAAAC,UAAY,OAAS,QAAZ,K,iDAEdX,EAAAA,EAAAA,IAGYQ,EAAA,CAHAM,QAAKW,EAAA,KAAAA,EAAA,GAAAC,GAAEhB,EAAAiB,kBAAmB,GAAOV,SAAUP,EAAAC,W,kBACrD,IAA8B,EAA9BX,EAAAA,EAAAA,IAA8BmB,EAAA,M,iBAArB,IAAW,EAAXnB,EAAAA,EAAAA,IAAW4B,K,6BAAU,W,0DAOtC,IA0FU,EA1FV5B,EAAAA,EAAAA,IA0FU6B,GAAA,CA1FAC,MAAOpB,EAAAqB,WAAY,cAAY,QAAQC,KAAK,W,kBACpD,IA4CS,EA5CThC,EAAAA,EAAAA,IA4CSiC,EAAA,CA5CAC,OAAQ,IAAE,C,iBACjB,IAeS,EAfTlC,EAAAA,EAAAA,IAeSmC,EAAA,CAfAC,KAAM,GAAC,C,iBACd,IAae,EAbfpC,EAAAA,EAAAA,IAaeqC,EAAA,CAbDC,MAAM,QAAM,C,iBACxB,IAWY,EAXZtC,EAAAA,EAAAA,IAWYuC,EAAA,C,WAVD7B,EAAAQ,a,qCAAAR,EAAAQ,aAAYQ,GACrBc,YAAY,UACXvB,SAAUP,EAAAC,UACXd,MAAA,gB,kBAEE,IAAqB,G,aADvBC,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJKhC,EAAAiC,MAARC,K,WADTxB,EAAAA,EAAAA,IAKYyB,EAAA,CAHTvB,IAAKsB,EAAKE,GACVR,MAAOM,EAAKG,SACZC,MAAOJ,EAAKE,I,uFAKrB9C,EAAAA,EAAAA,IAeSmC,EAAA,CAfAC,KAAM,GAAC,C,iBACd,IAae,EAbfpC,EAAAA,EAAAA,IAaeqC,EAAA,CAbDC,MAAM,QAAM,C,iBACxB,IAWY,EAXZtC,EAAAA,EAAAA,IAWYuC,EAAA,C,WAVD7B,EAAAqB,WAAWkB,O,qCAAXvC,EAAAqB,WAAWkB,OAAMvB,GAC1Bc,YAAY,UACXvB,SAAUP,EAAAC,UACXd,MAAA,gB,kBAEE,IAA2B,G,aAD7BC,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJIhC,EAAAwC,aAAPC,K,WADT/B,EAAAA,EAAAA,IAKYyB,EAAA,CAHTvB,IAAK6B,EAAIL,GACTR,MAAOa,EAAIC,KACXJ,MAAOG,EAAIL,I,uFAKpB9C,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,QAAM,C,iBACxB,IAMY,EANZtC,EAAAA,EAAAA,IAMYuC,EAAA,C,WALD7B,EAAAqB,WAAWsB,K,qCAAX3C,EAAAqB,WAAWsB,KAAI3B,GACvBT,SAAUP,EAAAC,UACXd,MAAA,gB,kBACA,IAAmD,EAAnDG,EAAAA,EAAAA,IAAmD6C,EAAA,CAAxCP,MAAM,OAAOU,MAAM,YAC9BhD,EAAAA,EAAAA,IAAyD6C,EAAA,CAA9CP,MAAM,QAAQU,MAAM,kB,2DAKvChD,EAAAA,EAAAA,IA2CSiC,EAAA,CA3CAC,OAAQ,IAAE,C,iBACjB,IAUS,EAVTlC,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,SAAO,C,iBACzB,IAMkB,EANlBtC,EAAAA,EAAAA,IAMkBsD,GAAA,C,WALP5C,EAAAqB,WAAWwB,M,qCAAX7C,EAAAqB,WAAWwB,MAAK7B,GACxB8B,IAAK,EACLC,IAAK,IACLxC,SAAUP,EAAAC,UACXd,MAAA,gB,mDAING,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,QAAM,C,iBACxB,IAMkB,EANlBtC,EAAAA,EAAAA,IAMkBsD,GAAA,C,WALP5C,EAAAqB,WAAW2B,W,qCAAXhD,EAAAqB,WAAW2B,WAAUhC,GAC7B8B,IAAK,EACLC,IAAK,IACLxC,SAAUP,EAAAC,UACXd,MAAA,gB,mDAING,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,YAAU,C,iBAC5B,IAMkB,EANlBtC,EAAAA,EAAAA,IAMkBsD,GAAA,C,WALP5C,EAAAqB,WAAW4B,S,qCAAXjD,EAAAqB,WAAW4B,SAAQjC,GAC3B8B,IAAK,EACLC,IAAK,KACLxC,SAAUP,EAAAC,UACXd,MAAA,gB,mDAING,EAAAA,EAAAA,IAQSmC,EAAA,CARAC,KAAM,GAAC,C,iBACd,IAMe,EANfpC,EAAAA,EAAAA,IAMeqC,EAAA,CANDC,MAAM,QAAM,C,iBACxB,IAIW,EAJXtC,EAAAA,EAAAA,IAIW4D,GAAA,C,WAHAlD,EAAAqB,WAAW8B,Y,qCAAXnD,EAAAqB,WAAW8B,YAAWnC,GAC/Bc,YAAY,OACXvB,SAAUP,EAAAC,W,uFASvBX,EAAAA,EAAAA,IAyKSiC,EAAA,CAzKAC,OAAQ,GAAIrC,MAAA,uB,kBAEnB,IAkES,EAlETG,EAAAA,EAAAA,IAkESmC,EAAA,CAlEAC,KAAM,IAAE,C,iBACf,IAgDM,EAhDN/B,EAAAA,EAAAA,IAgDM,MAhDNyD,EAgDM,EA/CJ9D,EAAAA,EAAAA,IAUUC,GAAA,CAVDN,MAAM,cAAcO,OAAO,S,kBAClC,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARN0D,EAQM,EAPJ1D,EAAAA,EAAAA,IAEM,MAFN2D,EAEM,EADJhE,EAAAA,EAAAA,IAAkCmB,EAAA,M,iBAAzB,IAAe,EAAfnB,EAAAA,EAAAA,IAAeiE,M,SAE1B5D,EAAAA,EAAAA,IAGM,MAHN6D,EAGM,EAFJ7D,EAAAA,EAAAA,IAA6D,MAA7D8D,GAA6D3C,EAAAA,EAAAA,IAAhCd,EAAA0D,eAAeC,KAAO,GAAJ,G,eAC/ChE,EAAAA,EAAAA,IAAmC,OAA9BV,MAAM,gBAAe,OAAG,U,OAKnCK,EAAAA,EAAAA,IAUUC,GAAA,CAVDN,MAAM,cAAcO,OAAO,S,kBAClC,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARNiE,EAQM,EAPJjE,EAAAA,EAAAA,IAEM,MAFNkE,EAEM,EADJvE,EAAAA,EAAAA,IAA4BmB,EAAA,M,iBAAnB,IAAS,EAATnB,EAAAA,EAAAA,IAASwE,M,SAEpBnE,EAAAA,EAAAA,IAGM,MAHNoE,EAGM,EAFJpE,EAAAA,EAAAA,IAA6E,MAA7EqE,GAA6ElD,EAAAA,EAAAA,IAAhDd,EAAA0D,eAAeO,mBAAqB,GAAI,KAAE,G,eACvEtE,EAAAA,EAAAA,IAAsC,OAAjCV,MAAM,gBAAe,UAAM,U,OAKtCK,EAAAA,EAAAA,IAUUC,GAAA,CAVDN,MAAM,cAAcO,OAAO,S,kBAClC,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARNuE,EAQM,EAPJvE,EAAAA,EAAAA,IAEM,MAFNwE,EAEM,EADJ7E,EAAAA,EAAAA,IAA2BmB,EAAA,M,iBAAlB,IAAQ,EAARnB,EAAAA,EAAAA,IAAQ8E,M,SAEnBzE,EAAAA,EAAAA,IAGM,MAHN0E,EAGM,EAFJ1E,EAAAA,EAAAA,IAA+D,MAA/D2E,GAA+DxD,EAAAA,EAAAA,IAAlCd,EAAA0D,eAAeb,OAAS,GAAJ,G,eACjDlD,EAAAA,EAAAA,IAAoC,OAA/BV,MAAM,gBAAe,QAAI,U,OAKpCK,EAAAA,EAAAA,IAUUC,GAAA,CAVDN,MAAM,cAAcO,OAAO,S,kBAClC,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARN4E,EAQM,EAPJ5E,EAAAA,EAAAA,IAEM,OAFDV,OAAKuF,EAAAA,EAAAA,IAAA,CAAC,yBAAiCnE,EAAAoE,kBAAkBzE,EAAA0D,eAAegB,e,EAC3EpF,EAAAA,EAAAA,IAAoCmB,EAAA,M,iBAA3B,IAAiB,EAAjBnB,EAAAA,EAAAA,IAAiBqF,M,WAE5BhF,EAAAA,EAAAA,IAGM,MAHNiF,EAGM,EAFJjF,EAAAA,EAAAA,IAAkF,MAAlFkF,GAAkF/D,EAAAA,EAAAA,KAApDd,EAAA0D,eAAegB,YAAc,GAAGI,QAAQ,IAAK,IAAC,G,eAC5EnF,EAAAA,EAAAA,IAAmC,OAA9BV,MAAM,gBAAe,OAAG,U,SAOrCK,EAAAA,EAAAA,IAaUC,GAAA,CAbDJ,MAAA,sBAA0BK,OAAO,S,CAC7BC,QAAMC,EAAAA,EAAAA,IACf,IAQM,EARNC,EAAAA,EAAAA,IAQM,MARNoF,EAQM,C,eAPJpF,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVL,EAAAA,EAAAA,IAKiB0F,GAAA,C,WALQhF,EAAAiF,U,qCAAAjF,EAAAiF,UAASjE,GAAEM,KAAK,S,kBACvC,IAAkD,EAAlDhC,EAAAA,EAAAA,IAAkD4F,GAAA,CAAjCtD,MAAM,OAAK,C,iBAAC,IAAGb,EAAA,MAAAA,EAAA,M,QAAH,U,eAC7BzB,EAAAA,EAAAA,IAA6D4F,GAAA,CAA5CtD,MAAM,iBAAe,C,iBAAC,IAAIb,EAAA,MAAAA,EAAA,M,QAAJ,W,eACvCzB,EAAAA,EAAAA,IAAoD4F,GAAA,CAAnCtD,MAAM,SAAO,C,iBAAC,IAAGb,EAAA,MAAAA,EAAA,M,QAAH,U,eAC/BzB,EAAAA,EAAAA,IAAyD4F,GAAA,CAAxCtD,MAAM,cAAY,C,iBAAC,IAAGb,EAAA,MAAAA,EAAA,M,QAAH,U,4DAI1C,IAAuD,EAAvDpB,EAAAA,EAAAA,IAAuD,MAAvDwF,EAAuD,Y,eAK3D7F,EAAAA,EAAAA,IAiGSmC,EAAA,CAjGAC,KAAM,GAAC,C,iBACd,IA0DU,EA1DVpC,EAAAA,EAAAA,IA0DUC,GAAA,CA1DDN,MAAM,eAAeO,OAAO,S,CACxBC,QAAMC,EAAAA,EAAAA,IACf,IAAaqB,EAAA,MAAAA,EAAA,MAAbpB,EAAAA,EAAAA,IAAa,UAAT,QAAI,M,iBAGV,IAGM,EAHNA,EAAAA,EAAAA,IAGM,MAHNyF,EAGM,C,eAFJzF,EAAAA,EAAAA,IAAuC,QAAjCV,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAkF+F,GAAA,CAAzEtF,KAAMM,EAAAiF,cAActF,EAAAuF,a,kBAAa,IAA+B,E,iBAA5BlF,EAAAmF,cAAcxF,EAAAuF,aAAU,K,oBAGvE5F,EAAAA,EAAAA,IAGM,MAHN8F,EAGM,C,eAFJ9F,EAAAA,EAAAA,IAAuC,QAAjCV,MAAM,gBAAe,SAAK,KAChCU,EAAAA,EAAAA,IAA+C,aAAAmB,EAAAA,EAAAA,IAAtCT,EAAAqF,eAAe1F,EAAA2F,eAAY,MAGtChG,EAAAA,EAAAA,IAGM,MAHNiG,EAGM,C,eAFJjG,EAAAA,EAAAA,IAAuC,QAAjCV,MAAM,gBAAe,SAAK,KAChCU,EAAAA,EAAAA,IAA4C,aAAAmB,EAAAA,EAAAA,IAAnCT,EAAAwF,WAAW7F,EAAA8F,gBAAa,MAGnCnG,EAAAA,EAAAA,IAGM,MAHNoG,EAGM,C,eAFJpG,EAAAA,EAAAA,IAAuC,QAAjCV,MAAM,gBAAe,SAAK,KAChCU,EAAAA,EAAAA,IAA+C,aAAAmB,EAAAA,EAAAA,IAAtCT,EAAAwF,WAAWxF,EAAA2F,mBAAgB,MAGtC1G,EAAAA,EAAAA,IAAyB2G,KAEzBtG,EAAAA,EAAAA,IAUM,MAVNuG,EAUM,EATJvG,EAAAA,EAAAA,IAGM,MAHNwG,EAGM,C,uBAHsB,YAE1BxG,EAAAA,EAAAA,IAAyD,OAAzDyG,GAAyDtF,EAAAA,EAAAA,IAAvBd,EAAAqG,cAAe,IAAC,MAEpD/G,EAAAA,EAAAA,IAIcgH,GAAA,CAHXC,WAAYvG,EAAAqG,aACZG,OAAyB,MAAjBxG,EAAAqG,aAAuB,UAAY,UAC3C,eAAc,G,mCAInB/G,EAAAA,EAAAA,IAAyB2G,KAGzBtG,EAAAA,EAAAA,IAeM,MAfN8G,EAeM,EAdJnH,EAAAA,EAAAA,IAMYQ,EAAA,CALVwB,KAAK,QACLvB,KAAK,UACJK,QAAOC,EAAAqG,WACPnG,UAAWP,EAAA2G,iB,kBAAiB,IAE/B5F,EAAA,MAAAA,EAAA,M,QAF+B,a,wCAG/BzB,EAAAA,EAAAA,IAMYQ,EAAA,CALVwB,KAAK,QACLvB,KAAK,OACJK,QAAOC,EAAAuG,cACPrG,UAAWP,EAAA2G,iB,kBAAiB,IAE/B5F,EAAA,MAAAA,EAAA,M,QAF+B,a,kDAOnCzB,EAAAA,EAAAA,IAkCUC,GAAA,CAlCDJ,MAAA,sBAA0BK,OAAO,S,CAC7BC,QAAMC,EAAAA,EAAAA,IACf,IAAaqB,EAAA,MAAAA,EAAA,MAAbpB,EAAAA,EAAAA,IAAa,UAAT,QAAI,M,iBAGV,IASM,EATNA,EAAAA,EAAAA,IASM,MATNkH,EASM,C,eARJlH,EAAAA,EAAAA,IAAwC,OAAnCV,MAAM,kBAAiB,UAAM,KAClCK,EAAAA,EAAAA,IAKcgH,GAAA,CAJXC,WAAYvG,EAAA8G,gBAAgBC,IAC5BP,OAAQnG,EAAA2G,aAAahH,EAAA8G,gBAAgBC,KACrC,aAAW,EACX,eAAc,G,iCAEjBpH,EAAAA,EAAAA,IAA8D,OAA9DsH,GAA8DnG,EAAAA,EAAAA,IAA9Bd,EAAA8G,gBAAgBC,KAAM,IAAC,MAGzDpH,EAAAA,EAAAA,IASM,MATNuH,EASM,C,eARJvH,EAAAA,EAAAA,IAAuC,OAAlCV,MAAM,kBAAiB,SAAK,KACjCK,EAAAA,EAAAA,IAKcgH,GAAA,CAJXC,WAAYvG,EAAA8G,gBAAgBK,OAC5BX,OAAQnG,EAAA+G,gBAAgBpH,EAAA8G,gBAAgBK,QACxC,aAAW,EACX,eAAc,G,iCAEjBxH,EAAAA,EAAAA,IAAiE,OAAjE0H,GAAiEvG,EAAAA,EAAAA,IAAjCd,EAAA8G,gBAAgBK,QAAS,IAAC,MAG5DxH,EAAAA,EAAAA,IAMM,MANN2H,EAMM,C,eALJ3H,EAAAA,EAAAA,IAAsC,OAAjCV,MAAM,kBAAiB,QAAI,KAChCU,EAAAA,EAAAA,IAGM,MAHN4H,EAGM,EAFJ5H,EAAAA,EAAAA,IAA8D,YAAxD,MAAEmB,EAAAA,EAAAA,IAAGT,EAAAmH,YAAYxH,EAAA8G,gBAAgBW,eAAY,IACnD9H,EAAAA,EAAAA,IAA8D,YAAxD,MAAEmB,EAAAA,EAAAA,IAAGT,EAAAmH,YAAYxH,EAAA8G,gBAAgBY,eAAY,S,uBAQ7DpI,EAAAA,EAAAA,IAuFYqI,GAAA,C,WAvFQ3H,EAAAiB,iB,uCAAAjB,EAAAiB,iBAAgBD,GAAE4G,MAAM,OAAOC,MAAM,O,CAiF5CC,QAAMpI,EAAAA,EAAAA,IACf,IAGO,EAHPC,EAAAA,EAAAA,IAGO,OAHPoI,EAGO,EAFLzI,EAAAA,EAAAA,IAA2DQ,EAAA,CAA/CM,QAAKW,EAAA,MAAAA,EAAA,IAAAC,GAAEhB,EAAAiB,kBAAmB,I,kBAAO,IAAEF,EAAA,MAAAA,EAAA,M,QAAF,S,eAC7CzB,EAAAA,EAAAA,IAAsEQ,EAAA,CAA3DC,KAAK,UAAWK,QAAOC,EAAA2H,oB,kBAAoB,IAAIjH,EAAA,MAAAA,EAAA,M,QAAJ,W,iDAnF1D,IA8EU,EA9EVzB,EAAAA,EAAAA,IA8EU2I,GAAA,C,WA9EQjI,EAAAkI,U,uCAAAlI,EAAAkI,UAASlH,I,kBACzB,IAiCc,EAjCd1B,EAAAA,EAAAA,IAiCc6I,GAAA,CAjCDvG,MAAM,OAAOc,KAAK,S,kBAC7B,IA+BU,EA/BVpD,EAAAA,EAAAA,IA+BU6B,GAAA,CA/BAC,MAAOpB,EAAAoI,eAAgB,cAAY,S,kBAC3C,IAWS,EAXT9I,EAAAA,EAAAA,IAWSiC,EAAA,CAXAC,OAAQ,IAAE,C,iBACjB,IAIS,EAJTlC,EAAAA,EAAAA,IAISmC,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFfpC,EAAAA,EAAAA,IAEeqC,EAAA,CAFDC,MAAM,WAAS,C,iBAC3B,IAA0F,EAA1FtC,EAAAA,EAAAA,IAA0FsD,GAAA,C,WAAhE5C,EAAAoI,eAAeC,W,qCAAfrI,EAAAoI,eAAeC,WAAUrH,GAAG8B,IAAK,EAAIC,IAAK,I,wCAGxEzD,EAAAA,EAAAA,IAISmC,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFfpC,EAAAA,EAAAA,IAEeqC,EAAA,CAFDC,MAAM,WAAS,C,iBAC3B,IAAwF,EAAxFtC,EAAAA,EAAAA,IAAwFsD,GAAA,C,WAA9D5C,EAAAoI,eAAeE,Q,uCAAftI,EAAAoI,eAAeE,QAAOtH,GAAG8B,IAAK,EAAIC,IAAK,K,gDAIvEzD,EAAAA,EAAAA,IAiBSiC,EAAA,CAjBAC,OAAQ,IAAE,C,iBACjB,IAIS,EAJTlC,EAAAA,EAAAA,IAISmC,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFfpC,EAAAA,EAAAA,IAEeqC,EAAA,CAFDC,MAAM,QAAM,C,iBACxB,IAA2F,EAA3FtC,EAAAA,EAAAA,IAA2FsD,GAAA,C,WAAjE5C,EAAAoI,eAAeG,Y,uCAAfvI,EAAAoI,eAAeG,YAAWvH,GAAG8B,IAAK,EAAIC,IAAK,I,wCAGzEzD,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,QAAM,C,iBACxB,IAMY,EANZtC,EAAAA,EAAAA,IAMYuC,EAAA,C,WANQ7B,EAAAoI,eAAeI,U,uCAAfxI,EAAAoI,eAAeI,UAASxH,GAAE7B,MAAA,gB,kBAC5C,IAA8C,EAA9CG,EAAAA,EAAAA,IAA8C6C,EAAA,CAAnCP,MAAM,KAAKU,MAAM,SAC5BhD,EAAAA,EAAAA,IAAgD6C,EAAA,CAArCP,MAAM,KAAKU,MAAM,WAC5BhD,EAAAA,EAAAA,IAAkD6C,EAAA,CAAvCP,MAAM,KAAKU,MAAM,aAC5BhD,EAAAA,EAAAA,IAA+C6C,EAAA,CAApCP,MAAM,KAAKU,MAAM,UAC5BhD,EAAAA,EAAAA,IAAgD6C,EAAA,CAArCP,MAAM,KAAKU,MAAM,Y,4EAOxChD,EAAAA,EAAAA,IAuBc6I,GAAA,CAvBDvG,MAAM,QAAQc,KAAK,e,kBAC9B,IAqBU,EArBVpD,EAAAA,EAAAA,IAqBU6B,GAAA,CArBAC,MAAOpB,EAAAoI,eAAgB,cAAY,S,kBAC3C,IASe,EATf9I,EAAAA,EAAAA,IASeqC,EAAA,CATDC,MAAM,OAAK,C,iBACvB,IAOY,EAPZtC,EAAAA,EAAAA,IAOYuC,EAAA,C,WAPQ7B,EAAAoI,eAAeK,c,uCAAfzI,EAAAoI,eAAeK,cAAazH,GAAE7B,MAAA,gB,kBAE9C,IAAyB,G,aAD3BC,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJOhC,EAAA0I,QAAVC,K,WADTjI,EAAAA,EAAAA,IAKYyB,EAAA,CAHTvB,IAAK+H,EAAOvG,GACZR,MAAK,GAAK+G,EAAOjG,SAASiG,EAAOC,WACjCtG,MAAOqG,EAAOvG,I,oEAIrB9C,EAAAA,EAAAA,IASeqC,EAAA,CATDC,MAAM,QAAM,C,iBACxB,IAOY,EAPZtC,EAAAA,EAAAA,IAOYuC,EAAA,C,WAPQ7B,EAAAoI,eAAeS,e,uCAAf7I,EAAAoI,eAAeS,eAAc7H,GAAE8H,SAAA,GAAS3J,MAAA,gB,kBAExD,IAAyB,G,aAD3BC,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJOhC,EAAA0I,QAAVC,K,WADTjI,EAAAA,EAAAA,IAKYyB,EAAA,CAHTvB,IAAK+H,EAAOvG,GACZR,MAAK,GAAK+G,EAAOjG,SAASiG,EAAOC,WACjCtG,MAAOqG,EAAOvG,I,gGAMzB9C,EAAAA,EAAAA,IAkBc6I,GAAA,CAlBDvG,MAAM,OAAOc,KAAK,U,kBAC7B,IAgBU,EAhBVpD,EAAAA,EAAAA,IAgBU6B,GAAA,CAhBAC,MAAOpB,EAAAoI,eAAgB,cAAY,S,kBAC3C,IAIe,EAJf9I,EAAAA,EAAAA,IAIeqC,EAAA,CAJDC,MAAM,UAAQ,C,iBAC1B,IAEkB,EAFlBtC,EAAAA,EAAAA,IAEkBsD,GAAA,C,WAFQ5C,EAAAoI,eAAeW,wB,uCAAf/I,EAAAoI,eAAeW,wBAAuB/H,GAAG8B,IAAK,G,CAC3DkG,QAAMtJ,EAAAA,EAAAA,IAAC,IAAEqB,EAAA,MAAAA,EAAA,M,QAAF,S,gCAGtBzB,EAAAA,EAAAA,IAIeqC,EAAA,CAJDC,MAAM,SAAO,C,iBACzB,IAEkB,EAFlBtC,EAAAA,EAAAA,IAEkBsD,GAAA,C,WAFQ5C,EAAAoI,eAAea,qB,uCAAfjJ,EAAAoI,eAAea,qBAAoBjI,GAAG8B,IAAK,EAAIC,IAAK,K,CACjEiG,QAAMtJ,EAAAA,EAAAA,IAAC,IAACqB,EAAA,MAAAA,EAAA,M,QAAD,Q,gCAGtBzB,EAAAA,EAAAA,IAIeqC,EAAA,CAJDC,MAAM,SAAO,C,iBACzB,IAEkB,EAFlBtC,EAAAA,EAAAA,IAEkBsD,GAAA,C,WAFQ5C,EAAAoI,eAAec,c,uCAAflJ,EAAAoI,eAAec,cAAalI,GAAG8B,IAAK,G,CACjDkG,QAAMtJ,EAAAA,EAAAA,IAAC,IAAGqB,EAAA,MAAAA,EAAA,M,QAAH,U,mKAqBlC,GACE2B,KAAM,gCACNyG,WAAY,CACVC,UAAS,YACTC,WAAU,aACVC,QAAO,UACPC,YAAW,cACXC,MAAK,QACLC,KAAI,OACJC,cAAaA,EAAAA,eAEfC,IAAAA,GACE,MAAO,CAELnJ,aAAc,KACda,WAAY,CACVkB,OAAQ,KACRI,KAAM,SACNE,MAAO,GACPG,WAAY,EACZC,SAAU,EACVE,YAAa,IAIfiF,eAAgB,CACdC,WAAY,EACZC,QAAS,GACTC,YAAa,EACbC,UAAW,OACXC,cAAe,KACfI,eAAgB,GAChBE,wBAAyB,IACzBE,qBAAsB,EACtBC,cAAe,KAIjBjJ,WAAW,EACXE,YAAY,EACZoF,WAAY,OACZO,cAAe,KACfH,aAAc,EACdU,aAAc,EACdM,gBAAiB,KAGjBjD,eAAgB,CACdC,IAAK,EACLM,kBAAmB,EACnBpB,MAAO,EACP6B,WAAY,GAEdoC,gBAAiB,CACfC,IAAK,EACLI,OAAQ,EACRM,aAAc,EACdC,aAAc,GAIhBkC,MAAO,KACP3E,UAAW,MACX4E,UAAW,CACTC,MAAO,GACPnG,IAAK,GACLoG,cAAe,GACflH,MAAO,GACP6B,WAAY,IAIdzC,MAAO,GACPO,aAAc,GACdkG,QAAS,GAGTzH,kBAAkB,EAClBiH,UAAW,QAGX8B,aAAc,KACdC,YAAa,KAGbC,OAAQ,GACRC,QAAS,KAEb,EACAC,SAAU,CACRpE,gBAAAA,GACE,IAAKqE,KAAKvE,gBAAkBuE,KAAKhJ,WAAW4B,SAAU,OAAO,KAC7D,MAAMqH,EAAY,IAAIC,KAAKF,KAAKvE,eAChC,OAAO,IAAIyE,KAAKD,EAAUE,UAAuC,GAA3BH,KAAKhJ,WAAW4B,SAAgB,IACxE,GAEF,aAAMwH,SACEJ,KAAKK,kBACXL,KAAKM,YACLN,KAAKO,kBACP,EACAC,aAAAA,GACER,KAAKS,SACP,EACAC,QAAS,CACP,qBAAML,GACJ,IAEE,MAAMM,QAAsBX,KAAKY,KAAKC,kCACtCb,KAAKpI,MAAQ+I,EAAcrB,KAAKwB,SAAW,GAG3C,MAAMC,QAAoBf,KAAKY,KAAKI,sBACpChB,KAAK7H,aAAe4I,EAAYzB,KAAKwB,SAAW,GAGhD,MAAMG,QAAwBjB,KAAKY,KAAKM,yBACxClB,KAAK3B,QAAU4C,EAAgB3B,KAAKwB,SAAW,EAEjD,CAAE,MAAOK,GACPC,QAAQD,MAAM,YAAaA,GAC3BnB,KAAKqB,SAASF,MAAM,SACtB,CACF,EAEA,gBAAMlL,GACA+J,KAAKpK,gBACDoK,KAAKsB,iBAELtB,KAAKuB,WAEf,EAEA,eAAMA,GACJ,GAAKvB,KAAK7J,aAAV,CAKA6J,KAAKlK,YAAa,EAElB,IACE,MAAM0L,QAAiBxB,KAAKY,KAAKa,4BAA4BzB,KAAK7J,aAAc,IAC3E6J,KAAKhJ,cACLgJ,KAAKjC,iBAGViC,KAAKpK,WAAY,EACjBoK,KAAK9E,WAAa,UAClB8E,KAAKvE,cAAgB,IAAIyE,KACzBF,KAAK1D,gBAAkBkF,EAASlC,KAAKoC,UAGjCF,EAASlC,KAAKqC,UAChB3B,KAAKH,OAAS2B,EAASlC,KAAKqC,QAC5B3B,KAAKF,QAAU0B,EAASlC,KAAKsC,SAC7B5B,KAAKqB,SAASQ,QAAQ,iBAAiBL,EAASlC,KAAKqC,YAIvD3B,KAAK8B,mBAEL9B,KAAKqB,SAASQ,QAAQ,UAExB,CAAE,MAAOV,GACPC,QAAQD,MAAM,UAAWA,GACzBnB,KAAKqB,SAASF,MAAM,YAAcA,EAAMK,UAAUlC,MAAMyC,SAAWZ,EAAMY,SAC3E,CAAE,QACA/B,KAAKlK,YAAa,CACpB,CAhCA,MAFEkK,KAAKqB,SAASW,QAAQ,YAmC1B,EAEA,cAAMV,GACJ,IACMtB,KAAK7J,oBACD6J,KAAKY,KAAKqB,6BAA6BjC,KAAK7J,cAGpD6J,KAAKpK,WAAY,EACjBoK,KAAK9E,WAAa,UAClB8E,KAAKkC,sBAELlC,KAAKqB,SAASQ,QAAQ,UAExB,CAAE,MAAOV,GACPC,QAAQD,MAAM,UAAWA,GACzBnB,KAAKqB,SAASF,MAAM,SACtB,CACF,EAEAW,gBAAAA,GACM9B,KAAKL,cACPK,KAAKL,aAAawC,QAGpB,MAAMC,EAAwC,WAA7BC,OAAOC,SAASF,SAAwB,OAAS,MAC5DG,EAAQ,GAAGH,MAAaC,OAAOC,SAASE,+BAA+BxC,KAAK7J,gBAElF6J,KAAKL,aAAe,IAAI8C,UAAUF,GAElCvC,KAAKL,aAAa+C,OAAS,KACzBtB,QAAQuB,IAAI,kBACZ3C,KAAKL,aAAaiD,KAAKC,KAAKC,UAAU,CAAEpN,KAAM,uBAGhDsK,KAAKL,aAAaoD,UAAaC,IAC7B,MAAM1D,EAAOuD,KAAKI,MAAMD,EAAM1D,MAC9BU,KAAKkD,uBAAuB5D,IAG9BU,KAAKL,aAAawD,QAAWhC,IAC3BC,QAAQD,MAAM,iBAAkBA,IAGlCnB,KAAKL,aAAayD,QAAU,KAC1BhC,QAAQuB,IAAI,kBACR3C,KAAKpK,WAEPyN,WAAW,KACTrD,KAAK8B,oBACJ,KAGT,EAEAI,mBAAAA,GACMlC,KAAKL,eACPK,KAAKL,aAAawC,QAClBnC,KAAKL,aAAe,KAExB,EAEAuD,sBAAAA,CAAuB5D,GACrB,OAAQA,EAAK5J,MACX,IAAK,qBACHsK,KAAKsD,cAAchE,EAAKA,MACxB,MACF,IAAK,iBACHU,KAAKuD,oBAAoBjE,EAAKA,MAC9B,MACF,IAAK,cACHU,KAAKwD,iBAAiBlE,EAAK6B,OAC3B,MACF,IAAK,iBACHnB,KAAKyD,oBAAoBnE,EAAKA,MAC9B,MAEN,EAEAgE,aAAAA,CAAchE,GACZU,KAAK3G,eAAiB,CACpBC,IAAKgG,EAAKoE,SAAW,EACrB9J,kBAAmB0F,EAAK1F,mBAAqB,EAC7CpB,MAAO8G,EAAKqE,eAAiB,EAC7BtJ,WAAYiF,EAAKjF,YAAc,GAIjC,MAAMuJ,GAAM,IAAI1D,MAAO2D,qBACvB7D,KAAKR,UAAUC,MAAMqE,KAAKF,GAC1B5D,KAAKR,UAAUlG,IAAIwK,KAAKxE,EAAKoE,SAAW,GACxC1D,KAAKR,UAAUE,cAAcoE,KAAKxE,EAAK1F,mBAAqB,GAC5DoG,KAAKR,UAAUhH,MAAMsL,KAAKxE,EAAKqE,eAAiB,GAChD3D,KAAKR,UAAUnF,WAAWyJ,KAAKxE,EAAKjF,YAAc,GAGlD0J,OAAOC,KAAKhE,KAAKR,WAAWyE,QAAQ1N,IAC9ByJ,KAAKR,UAAUjJ,GAAK2N,OAAS,IAC/BlE,KAAKR,UAAUjJ,GAAK4N,UAIxBnE,KAAKoE,aACP,EAEAX,mBAAAA,CAAoBnE,GACdA,IACFU,KAAK3G,eAAiB,CACpBC,IAAKgG,EAAKoE,SAAW,EACrB9J,kBAAmB0F,EAAK1F,mBAAqB,EAC7CpB,MAAO8G,EAAKqE,eAAiB,EAC7BtJ,WAAYiF,EAAKjF,YAAc,GAGrC,EAEAkJ,mBAAAA,CAAoBjE,GAClBU,KAAKpK,WAAY,EACjBoK,KAAK9E,WAAa,YAClB8E,KAAKhE,aAAe,IACpBgE,KAAKqB,SAASQ,QAAQ,WACtB7B,KAAKkC,qBACP,EAEAsB,gBAAAA,CAAiBrC,GACfnB,KAAKpK,WAAY,EACjBoK,KAAK9E,WAAa,SAClB8E,KAAKqB,SAASF,MAAM,WAAaA,GACjCnB,KAAKkC,qBACP,EAEA5B,SAAAA,GACEN,KAAKqE,UAAU,KACTrE,KAAKsE,MAAMC,iBACbvE,KAAKT,MAAQiF,EAAAA,GAAaxE,KAAKsE,MAAMC,gBACrCvE,KAAKoE,gBAGX,EAEAA,WAAAA,GACE,IAAKpE,KAAKT,MAAO,OAEjB,MAAMkF,EAAS,CACblH,MAAO,CACLmH,KAAM1E,KAAK2E,gBACXC,KAAM,SACNC,UAAW,CACTC,SAAU,KAGdC,QAAS,CACPC,QAAS,QAEXC,MAAO,CACLvP,KAAM,WACN4J,KAAMU,KAAKR,UAAUC,MACrByF,UAAW,CACTC,SAAU,OACVC,OAAQ,KAGZC,MAAO,CACL3P,KAAM,QACN2C,KAAM2H,KAAKsF,gBAEbC,OAAQ,CAAC,CACPjG,KAAMU,KAAKR,UAAUQ,KAAKpF,WAC1BlF,KAAM,OACN8P,QAAQ,EACRC,UAAW,CACTC,QAAS,IAEXC,UAAW,CACTC,MAAO5F,KAAK6F,mBAGhBC,KAAM,CACJlB,KAAM,KACNmB,MAAO,KACPC,OAAQ,MACRC,cAAc,IAIlBjG,KAAKT,MAAM2G,UAAUzB,EACvB,EAEAE,aAAAA,GACE,MAAMwB,EAAS,CACb7M,IAAK,QACLoG,cAAe,SACflH,MAAO,QACP6B,WAAY,SAEd,OAAO8L,EAAOnG,KAAKpF,YAAc,EACnC,EAEA0K,YAAAA,GACE,MAAMc,EAAQ,CACZ9M,IAAK,MACLoG,cAAe,SACflH,MAAO,MACP6B,WAAY,UAEd,OAAO+L,EAAMpG,KAAKpF,YAAc,EAClC,EAEAiL,aAAAA,GACE,MAAMQ,EAAS,CACb/M,IAAK,UACLoG,cAAe,UACflH,MAAO,UACP6B,WAAY,WAEd,OAAOgM,EAAOrG,KAAKpF,YAAc,SACnC,EAEA2F,gBAAAA,GACEP,KAAKJ,YAAc0G,YAAY,KAC7B,GAAItG,KAAKpK,WAAaoK,KAAKvE,cAAe,CAIxC,GAHAuE,KAAK1E,aAAeiL,KAAKC,OAAOtG,KAAK0D,MAAQ,IAAI1D,KAAKF,KAAKvE,eAAe0E,WAAa,KAGnFH,KAAKhJ,WAAW4B,SAAU,CAC5B,MAAM6N,EAA0C,GAA3BzG,KAAKhJ,WAAW4B,SACrCoH,KAAKhE,aAAeuK,KAAK9N,IAAI,IAAMuH,KAAK1E,aAAemL,EAAgB,IACzE,CAGIzG,KAAKhE,cAAgB,KAAOgE,KAAKpK,WACnCoK,KAAKsB,UAET,CAGAtB,KAAK0G,yBACJ,IACL,EAEAA,qBAAAA,GAEE1G,KAAKvD,gBAAkB,CACrBC,IAAK6J,KAAK7N,IAAI,EAAG6N,KAAK9N,IAAI,IAAKuH,KAAKvD,gBAAgBC,IAA8B,IAAvB6J,KAAKI,SAAW,MAC3E7J,OAAQyJ,KAAK7N,IAAI,EAAG6N,KAAK9N,IAAI,IAAKuH,KAAKvD,gBAAgBK,OAAiC,GAAvByJ,KAAKI,SAAW,MACjFvJ,aAAc4C,KAAKvD,gBAAgBW,aAA+B,IAAhBmJ,KAAKI,SACvDtJ,aAAc2C,KAAKvD,gBAAgBY,aAA+B,IAAhBkJ,KAAKI,SAE3D,EAEAlG,OAAAA,GACET,KAAKkC,sBACDlC,KAAKJ,aACPgH,cAAc5G,KAAKJ,aAEjBI,KAAKT,OACPS,KAAKT,MAAMsH,SAEf,EAEAlJ,kBAAAA,GACEqC,KAAKpJ,kBAAmB,EACxBoJ,KAAKqB,SAASQ,QAAQ,QACxB,EAEAxF,UAAAA,GACM2D,KAAK1D,iBACP0D,KAAK8G,QAAQhD,KAAK,CAChBzL,KAAM,2BACN0O,OAAQ,CAAEhP,GAAIiI,KAAK1D,kBAGzB,EAEAC,aAAAA,GACMyD,KAAK1D,iBACP0D,KAAKY,KAAKoG,iBAAiBhH,KAAK1D,gBAEpC,EAGArB,aAAAA,CAAckB,GACZ,MAAM8K,EAAQ,CACZC,KAAM,OACNC,QAAS,UACTC,UAAW,UACXC,OAAQ,SACRC,QAAS,WAEX,OAAOL,EAAM9K,IAAW,MAC1B,EAEAhB,aAAAA,CAAcgB,GACZ,MAAMoL,EAAQ,CACZL,KAAM,MACNC,QAAS,MACTC,UAAW,MACXC,OAAQ,KACRC,QAAS,OAEX,OAAOC,EAAMpL,IAAW,IAC1B,EAEA/B,iBAAAA,CAAkBoN,GAChB,OAAIA,EAAO,EAAU,aACjBA,EAAO,EAAU,eACd,WACT,EAEA7K,YAAAA,CAAa8K,GACX,OAAIA,EAAU,GAAW,YACrBA,EAAU,GAAW,UAClB,SACT,EAEA1K,eAAAA,CAAgB0K,GACd,OAAIA,EAAU,GAAW,YACrBA,EAAU,GAAW,UAClB,SACT,EAEApM,cAAAA,CAAeqM,GACb,IAAKA,EAAS,MAAO,KACrB,MAAMC,EAAQpB,KAAKC,MAAMkB,EAAU,MAC7BE,EAAUrB,KAAKC,MAAOkB,EAAU,KAAQ,IACxCG,EAAOH,EAAU,GACvB,MAAO,GAAGC,MAAUC,MAAYC,IAClC,EAEArM,UAAAA,CAAWsM,GACT,OAAKA,EACE,IAAI5H,KAAK4H,GAAMC,iBADJ,IAEpB,EAEA5K,WAAAA,CAAY6K,GACV,IAAKA,EAAO,MAAO,KACnB,MAAMC,EAAI,KACJC,EAAQ,CAAC,IAAK,KAAM,KAAM,MAC1BC,EAAI5B,KAAKC,MAAMD,KAAK5D,IAAIqF,GAASzB,KAAK5D,IAAIsF,IAChD,OAAOG,YAAYJ,EAAQzB,KAAK8B,IAAIJ,EAAGE,IAAI1N,QAAQ,IAAMyN,EAAMC,EACjE,GAGFG,MAAO,CACL1N,SAAAA,GACEoF,KAAKoE,aACP,I,WC93BJ,MAAMmE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceExecutionOptimized.vue", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceExecutionOptimized.vue?66b5"], "sourcesContent": ["<template>\n  <div class=\"execution-container\">\n    <!-- 测试配置面板 -->\n    <el-card class=\"config-panel\" shadow=\"hover\">\n      <template #header>\n        <div class=\"panel-header\">\n          <h3>性能测试执行</h3>\n          <el-button-group>\n            <el-button \n              :type=\"isRunning ? 'danger' : 'primary'\" \n              :loading=\"isStarting\"\n              @click=\"toggleTest\"\n              :disabled=\"!selectedTask\">\n              <el-icon><VideoPlay v-if=\"!isRunning\" /><VideoPause v-else /></el-icon>\n              {{ isRunning ? '停止测试' : '开始测试' }}\n            </el-button>\n            <el-button @click=\"showConfigDialog = true\" :disabled=\"isRunning\">\n              <el-icon><Setting /></el-icon>\n              配置\n            </el-button>\n          </el-button-group>\n        </div>\n      </template>\n\n      <el-form :model=\"testConfig\" label-width=\"120px\" size=\"default\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"选择任务\">\n              <el-select \n                v-model=\"selectedTask\" \n                placeholder=\"请选择性能任务\"\n                :disabled=\"isRunning\"\n                style=\"width: 100%\">\n                <el-option\n                  v-for=\"task in tasks\"\n                  :key=\"task.id\"\n                  :label=\"task.taskName\"\n                  :value=\"task.id\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"测试环境\">\n              <el-select \n                v-model=\"testConfig.env_id\" \n                placeholder=\"请选择测试环境\"\n                :disabled=\"isRunning\"\n                style=\"width: 100%\">\n                <el-option\n                  v-for=\"env in environments\"\n                  :key=\"env.id\"\n                  :label=\"env.name\"\n                  :value=\"env.id\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"执行模式\">\n              <el-select \n                v-model=\"testConfig.mode\" \n                :disabled=\"isRunning\"\n                style=\"width: 100%\">\n                <el-option label=\"单机模式\" value=\"single\"></el-option>\n                <el-option label=\"分布式模式\" value=\"distributed\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <el-form-item label=\"并发用户数\">\n              <el-input-number \n                v-model=\"testConfig.users\" \n                :min=\"1\" \n                :max=\"10000\"\n                :disabled=\"isRunning\"\n                style=\"width: 100%\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item label=\"启动速率\">\n              <el-input-number \n                v-model=\"testConfig.spawn_rate\" \n                :min=\"1\" \n                :max=\"100\"\n                :disabled=\"isRunning\"\n                style=\"width: 100%\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item label=\"持续时间(分钟)\">\n              <el-input-number \n                v-model=\"testConfig.duration\" \n                :min=\"1\" \n                :max=\"1440\"\n                :disabled=\"isRunning\"\n                style=\"width: 100%\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item label=\"报告名称\">\n              <el-input \n                v-model=\"testConfig.report_name\" \n                placeholder=\"自动生成\"\n                :disabled=\"isRunning\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-card>\n\n    <!-- 实时监控面板 -->\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <!-- 左侧指标卡片 -->\n      <el-col :span=\"18\">\n        <div class=\"metrics-grid\">\n          <el-card class=\"metric-card\" shadow=\"hover\">\n            <div class=\"metric-content\">\n              <div class=\"metric-icon tps-icon\">\n                <el-icon><TrendCharts /></el-icon>\n              </div>\n              <div class=\"metric-info\">\n                <div class=\"metric-value\">{{ currentMetrics.tps || 0 }}</div>\n                <div class=\"metric-label\">TPS</div>\n              </div>\n            </div>\n          </el-card>\n\n          <el-card class=\"metric-card\" shadow=\"hover\">\n            <div class=\"metric-content\">\n              <div class=\"metric-icon response-icon\">\n                <el-icon><Timer /></el-icon>\n              </div>\n              <div class=\"metric-info\">\n                <div class=\"metric-value\">{{ currentMetrics.avg_response_time || 0 }}ms</div>\n                <div class=\"metric-label\">平均响应时间</div>\n              </div>\n            </div>\n          </el-card>\n\n          <el-card class=\"metric-card\" shadow=\"hover\">\n            <div class=\"metric-content\">\n              <div class=\"metric-icon users-icon\">\n                <el-icon><User /></el-icon>\n              </div>\n              <div class=\"metric-info\">\n                <div class=\"metric-value\">{{ currentMetrics.users || 0 }}</div>\n                <div class=\"metric-label\">活跃用户</div>\n              </div>\n            </div>\n          </el-card>\n\n          <el-card class=\"metric-card\" shadow=\"hover\">\n            <div class=\"metric-content\">\n              <div class=\"metric-icon error-icon\" :class=\"getErrorRateClass(currentMetrics.error_rate)\">\n                <el-icon><WarningFilled /></el-icon>\n              </div>\n              <div class=\"metric-info\">\n                <div class=\"metric-value\">{{ (currentMetrics.error_rate || 0).toFixed(2) }}%</div>\n                <div class=\"metric-label\">错误率</div>\n              </div>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 实时图表 -->\n        <el-card style=\"margin-top: 20px;\" shadow=\"hover\">\n          <template #header>\n            <div class=\"chart-header\">\n              <h4>实时性能趋势</h4>\n              <el-radio-group v-model=\"chartType\" size=\"small\">\n                <el-radio-button label=\"tps\">TPS</el-radio-button>\n                <el-radio-button label=\"response_time\">响应时间</el-radio-button>\n                <el-radio-button label=\"users\">用户数</el-radio-button>\n                <el-radio-button label=\"error_rate\">错误率</el-radio-button>\n              </el-radio-group>\n            </div>\n          </template>\n          <div ref=\"chartContainer\" style=\"height: 300px;\"></div>\n        </el-card>\n      </el-col>\n\n      <!-- 右侧状态面板 -->\n      <el-col :span=\"6\">\n        <el-card class=\"status-panel\" shadow=\"hover\">\n          <template #header>\n            <h4>测试状态</h4>\n          </template>\n\n          <div class=\"status-item\">\n            <span class=\"status-label\">测试状态:</span>\n            <el-tag :type=\"getStatusType(testStatus)\">{{ getStatusText(testStatus) }}</el-tag>\n          </div>\n\n          <div class=\"status-item\">\n            <span class=\"status-label\">运行时长:</span>\n            <span>{{ formatDuration(testDuration) }}</span>\n          </div>\n\n          <div class=\"status-item\">\n            <span class=\"status-label\">开始时间:</span>\n            <span>{{ formatTime(testStartTime) }}</span>\n          </div>\n\n          <div class=\"status-item\">\n            <span class=\"status-label\">预计结束:</span>\n            <span>{{ formatTime(estimatedEndTime) }}</span>\n          </div>\n\n          <el-divider></el-divider>\n\n          <div class=\"progress-section\">\n            <div class=\"progress-label\">\n              测试进度\n              <span class=\"progress-percent\">{{ testProgress }}%</span>\n            </div>\n            <el-progress \n              :percentage=\"testProgress\" \n              :status=\"testProgress === 100 ? 'success' : 'primary'\"\n              :stroke-width=\"8\">\n            </el-progress>\n          </div>\n\n          <el-divider></el-divider>\n\n          <!-- 快捷操作 -->\n          <div class=\"quick-actions\">\n            <el-button \n              size=\"small\" \n              type=\"primary\" \n              @click=\"viewReport\"\n              :disabled=\"!currentReportId\">\n              查看报告\n            </el-button>\n            <el-button \n              size=\"small\" \n              type=\"info\" \n              @click=\"exportResults\"\n              :disabled=\"!currentReportId\">\n              导出结果\n            </el-button>\n          </div>\n        </el-card>\n\n        <!-- 系统资源监控 -->\n        <el-card style=\"margin-top: 20px;\" shadow=\"hover\">\n          <template #header>\n            <h4>系统资源</h4>\n          </template>\n\n          <div class=\"resource-item\">\n            <div class=\"resource-label\">CPU使用率</div>\n            <el-progress \n              :percentage=\"systemResources.cpu\" \n              :status=\"getCpuStatus(systemResources.cpu)\"\n              :show-text=\"false\"\n              :stroke-width=\"6\">\n            </el-progress>\n            <span class=\"resource-value\">{{ systemResources.cpu }}%</span>\n          </div>\n\n          <div class=\"resource-item\">\n            <div class=\"resource-label\">内存使用率</div>\n            <el-progress \n              :percentage=\"systemResources.memory\" \n              :status=\"getMemoryStatus(systemResources.memory)\"\n              :show-text=\"false\"\n              :stroke-width=\"6\">\n            </el-progress>\n            <span class=\"resource-value\">{{ systemResources.memory }}%</span>\n          </div>\n\n          <div class=\"resource-item\">\n            <div class=\"resource-label\">网络IO</div>\n            <div class=\"network-io\">\n              <span>↑ {{ formatBytes(systemResources.network_sent) }}</span>\n              <span>↓ {{ formatBytes(systemResources.network_recv) }}</span>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 配置对话框 -->\n    <el-dialog v-model=\"showConfigDialog\" title=\"高级配置\" width=\"60%\">\n      <el-tabs v-model=\"configTab\">\n        <el-tab-pane label=\"基础配置\" name=\"basic\">\n          <el-form :model=\"advancedConfig\" label-width=\"120px\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"思考时间(秒)\">\n                  <el-input-number v-model=\"advancedConfig.think_time\" :min=\"0\" :max=\"60\"></el-input-number>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"超时时间(秒)\">\n                  <el-input-number v-model=\"advancedConfig.timeout\" :min=\"1\" :max=\"300\"></el-input-number>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"重试次数\">\n                  <el-input-number v-model=\"advancedConfig.retry_count\" :min=\"0\" :max=\"10\"></el-input-number>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"日志级别\">\n                  <el-select v-model=\"advancedConfig.log_level\" style=\"width: 100%\">\n                    <el-option label=\"关闭\" value=\"off\"></el-option>\n                    <el-option label=\"错误\" value=\"error\"></el-option>\n                    <el-option label=\"警告\" value=\"warning\"></el-option>\n                    <el-option label=\"信息\" value=\"info\"></el-option>\n                    <el-option label=\"调试\" value=\"debug\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"分布式配置\" name=\"distributed\">\n          <el-form :model=\"advancedConfig\" label-width=\"120px\">\n            <el-form-item label=\"主节点\">\n              <el-select v-model=\"advancedConfig.master_server\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"server in servers\"\n                  :key=\"server.id\"\n                  :label=\"`${server.name} (${server.host_ip})`\"\n                  :value=\"server.id\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"工作节点\">\n              <el-select v-model=\"advancedConfig.worker_servers\" multiple style=\"width: 100%\">\n                <el-option\n                  v-for=\"server in servers\"\n                  :key=\"server.id\"\n                  :label=\"`${server.name} (${server.host_ip})`\"\n                  :value=\"server.id\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"告警设置\" name=\"alerts\">\n          <el-form :model=\"advancedConfig\" label-width=\"120px\">\n            <el-form-item label=\"响应时间告警\">\n              <el-input-number v-model=\"advancedConfig.response_time_threshold\" :min=\"0\">\n                <template #append>ms</template>\n              </el-input-number>\n            </el-form-item>\n            <el-form-item label=\"错误率告警\">\n              <el-input-number v-model=\"advancedConfig.error_rate_threshold\" :min=\"0\" :max=\"100\">\n                <template #append>%</template>\n              </el-input-number>\n            </el-form-item>\n            <el-form-item label=\"TPS告警\">\n              <el-input-number v-model=\"advancedConfig.tps_threshold\" :min=\"0\">\n                <template #append>TPS</template>\n              </el-input-number>\n            </el-form-item>\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n      \n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showConfigDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveAdvancedConfig\">保存配置</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { VideoPlay, VideoPause, Setting, TrendCharts, Timer, User, WarningFilled } from '@element-plus/icons-vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'PerformanceExecutionOptimized',\n  components: {\n    VideoPlay,\n    VideoPause, \n    Setting,\n    TrendCharts,\n    Timer,\n    User,\n    WarningFilled\n  },\n  data() {\n    return {\n      // 测试配置\n      selectedTask: null,\n      testConfig: {\n        env_id: null,\n        mode: 'single',\n        users: 10,\n        spawn_rate: 2,\n        duration: 5,\n        report_name: ''\n      },\n      \n      // 高级配置\n      advancedConfig: {\n        think_time: 1,\n        timeout: 30,\n        retry_count: 0,\n        log_level: 'info',\n        master_server: null,\n        worker_servers: [],\n        response_time_threshold: 1000,\n        error_rate_threshold: 5,\n        tps_threshold: 100\n      },\n      \n      // 状态管理\n      isRunning: false,\n      isStarting: false,\n      testStatus: 'idle',\n      testStartTime: null,\n      testDuration: 0,\n      testProgress: 0,\n      currentReportId: null,\n      \n      // 实时数据\n      currentMetrics: {\n        tps: 0,\n        avg_response_time: 0,\n        users: 0,\n        error_rate: 0\n      },\n      systemResources: {\n        cpu: 0,\n        memory: 0,\n        network_sent: 0,\n        network_recv: 0\n      },\n      \n      // 图表\n      chart: null,\n      chartType: 'tps',\n      chartData: {\n        times: [],\n        tps: [],\n        response_time: [],\n        users: [],\n        error_rate: []\n      },\n      \n      // 数据源\n      tasks: [],\n      environments: [],\n      servers: [],\n      \n      // UI状态\n      showConfigDialog: false,\n      configTab: 'basic',\n      \n      // WebSocket\n      wsConnection: null,\n      updateTimer: null,\n      \n      // GUI相关\n      guiUrl: '',\n      webPort: 8089\n    }\n  },\n  computed: {\n    estimatedEndTime() {\n      if (!this.testStartTime || !this.testConfig.duration) return null\n      const startTime = new Date(this.testStartTime)\n      return new Date(startTime.getTime() + this.testConfig.duration * 60 * 1000)\n    }\n  },\n  async mounted() {\n    await this.loadInitialData()\n    this.initChart()\n    this.startUpdateTimer()\n  },\n  beforeUnmount() {\n    this.cleanup()\n  },\n  methods: {\n    async loadInitialData() {\n      try {\n        // 加载任务列表\n        const tasksResponse = await this.$api.getPerformanceTasksForExecution()\n        this.tasks = tasksResponse.data.results || []\n        \n        // 加载环境列表\n        const envResponse = await this.$api.getTestEnvironments()\n        this.environments = envResponse.data.results || []\n        \n        // 加载服务器列表\n        const serversResponse = await this.$api.getServersForExecution()\n        this.servers = serversResponse.data.results || []\n        \n      } catch (error) {\n        console.error('加载初始数据失败:', error)\n        this.$message.error('加载数据失败')\n      }\n    },\n    \n    async toggleTest() {\n      if (this.isRunning) {\n        await this.stopTest()\n      } else {\n        await this.startTest()\n      }\n    },\n    \n    async startTest() {\n      if (!this.selectedTask) {\n        this.$message.warning('请选择要执行的任务')\n        return\n      }\n      \n      this.isStarting = true\n      \n      try {\n        const response = await this.$api.runPerformanceTestOptimized(this.selectedTask, {\n          ...this.testConfig,\n          ...this.advancedConfig\n        })\n        \n        this.isRunning = true\n        this.testStatus = 'running'\n        this.testStartTime = new Date()\n        this.currentReportId = response.data.report_id\n        \n        // 保存GUI信息\n        if (response.data.gui_url) {\n          this.guiUrl = response.data.gui_url\n          this.webPort = response.data.web_port\n          this.$message.success(`测试启动成功！GUI地址: ${response.data.gui_url}`)\n        }\n        \n        // 连接WebSocket\n        this.connectWebSocket()\n        \n        this.$message.success('性能测试已开始')\n        \n      } catch (error) {\n        console.error('启动测试失败:', error)\n        this.$message.error('启动测试失败: ' + (error.response?.data?.message || error.message))\n      } finally {\n        this.isStarting = false\n      }\n    },\n    \n    async stopTest() {\n      try {\n        if (this.selectedTask) {\n          await this.$api.stopPerformanceTestOptimized(this.selectedTask)\n        }\n        \n        this.isRunning = false\n        this.testStatus = 'stopped'\n        this.disconnectWebSocket()\n        \n        this.$message.success('性能测试已停止')\n        \n      } catch (error) {\n        console.error('停止测试失败:', error)\n        this.$message.error('停止测试失败')\n      }\n    },\n    \n    connectWebSocket() {\n      if (this.wsConnection) {\n        this.wsConnection.close()\n      }\n      \n      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'\n      const wsUrl = `${protocol}//${window.location.host}/ws/performance/monitor/${this.selectedTask}/`\n      \n      this.wsConnection = new WebSocket(wsUrl)\n      \n      this.wsConnection.onopen = () => {\n        console.log('WebSocket连接已建立')\n        this.wsConnection.send(JSON.stringify({ type: 'start_monitoring' }))\n      }\n      \n      this.wsConnection.onmessage = (event) => {\n        const data = JSON.parse(event.data)\n        this.handleWebSocketMessage(data)\n      }\n      \n      this.wsConnection.onerror = (error) => {\n        console.error('WebSocket连接错误:', error)\n      }\n      \n      this.wsConnection.onclose = () => {\n        console.log('WebSocket连接已关闭')\n        if (this.isRunning) {\n          // 如果测试还在运行，尝试重连\n          setTimeout(() => {\n            this.connectWebSocket()\n          }, 5000)\n        }\n      }\n    },\n    \n    disconnectWebSocket() {\n      if (this.wsConnection) {\n        this.wsConnection.close()\n        this.wsConnection = null\n      }\n    },\n    \n    handleWebSocketMessage(data) {\n      switch (data.type) {\n        case 'performance_update':\n          this.updateMetrics(data.data)\n          break\n        case 'test_completed':\n          this.handleTestCompleted(data.data)\n          break\n        case 'test_failed':\n          this.handleTestFailed(data.error)\n          break\n        case 'current_status':\n          this.updateCurrentStatus(data.data)\n          break\n      }\n    },\n    \n    updateMetrics(data) {\n      this.currentMetrics = {\n        tps: data.avg_tps || 0,\n        avg_response_time: data.avg_response_time || 0,\n        users: data.current_users || 0,\n        error_rate: data.error_rate || 0\n      }\n      \n      // 更新图表数据\n      const now = new Date().toLocaleTimeString()\n      this.chartData.times.push(now)\n      this.chartData.tps.push(data.avg_tps || 0)\n      this.chartData.response_time.push(data.avg_response_time || 0)\n      this.chartData.users.push(data.current_users || 0)\n      this.chartData.error_rate.push(data.error_rate || 0)\n      \n      // 保持最新50个数据点\n      Object.keys(this.chartData).forEach(key => {\n        if (this.chartData[key].length > 50) {\n          this.chartData[key].shift()\n        }\n      })\n      \n      this.updateChart()\n    },\n    \n    updateCurrentStatus(data) {\n      if (data) {\n        this.currentMetrics = {\n          tps: data.avg_tps || 0,\n          avg_response_time: data.avg_response_time || 0,\n          users: data.current_users || 0,\n          error_rate: data.error_rate || 0\n        }\n      }\n    },\n    \n    handleTestCompleted(data) {\n      this.isRunning = false\n      this.testStatus = 'completed'\n      this.testProgress = 100\n      this.$message.success('性能测试已完成')\n      this.disconnectWebSocket()\n    },\n    \n    handleTestFailed(error) {\n      this.isRunning = false\n      this.testStatus = 'failed'\n      this.$message.error('性能测试失败: ' + error)\n      this.disconnectWebSocket()\n    },\n    \n    initChart() {\n      this.$nextTick(() => {\n        if (this.$refs.chartContainer) {\n          this.chart = echarts.init(this.$refs.chartContainer)\n          this.updateChart()\n        }\n      })\n    },\n    \n    updateChart() {\n      if (!this.chart) return\n      \n      const option = {\n        title: {\n          text: this.getChartTitle(),\n          left: 'center',\n          textStyle: {\n            fontSize: 14\n          }\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: this.chartData.times,\n          axisLabel: {\n            interval: 'auto',\n            rotate: 45\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: this.getChartUnit()\n        },\n        series: [{\n          data: this.chartData[this.chartType],\n          type: 'line',\n          smooth: true,\n          areaStyle: {\n            opacity: 0.3\n          },\n          itemStyle: {\n            color: this.getChartColor()\n          }\n        }],\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '15%',\n          containLabel: true\n        }\n      }\n      \n      this.chart.setOption(option)\n    },\n    \n    getChartTitle() {\n      const titles = {\n        tps: 'TPS趋势',\n        response_time: '响应时间趋势', \n        users: '用户数趋势',\n        error_rate: '错误率趋势'\n      }\n      return titles[this.chartType] || ''\n    },\n    \n    getChartUnit() {\n      const units = {\n        tps: 'TPS',\n        response_time: '毫秒(ms)',\n        users: '用户数',\n        error_rate: '百分比(%)'\n      }\n      return units[this.chartType] || ''\n    },\n    \n    getChartColor() {\n      const colors = {\n        tps: '#409eff',\n        response_time: '#67c23a',\n        users: '#e6a23c',\n        error_rate: '#f56c6c'\n      }\n      return colors[this.chartType] || '#409eff'\n    },\n    \n    startUpdateTimer() {\n      this.updateTimer = setInterval(() => {\n        if (this.isRunning && this.testStartTime) {\n          this.testDuration = Math.floor((Date.now() - new Date(this.testStartTime).getTime()) / 1000)\n          \n          // 计算进度\n          if (this.testConfig.duration) {\n            const totalSeconds = this.testConfig.duration * 60\n            this.testProgress = Math.min(100, (this.testDuration / totalSeconds) * 100)\n          }\n          \n          // 如果超过预定时间，自动停止\n          if (this.testProgress >= 100 && this.isRunning) {\n            this.stopTest()\n          }\n        }\n        \n        // 更新系统资源（模拟数据）\n        this.updateSystemResources()\n      }, 1000)\n    },\n    \n    updateSystemResources() {\n      // 模拟系统资源数据更新\n      this.systemResources = {\n        cpu: Math.max(0, Math.min(100, this.systemResources.cpu + (Math.random() - 0.5) * 10)),\n        memory: Math.max(0, Math.min(100, this.systemResources.memory + (Math.random() - 0.5) * 5)),\n        network_sent: this.systemResources.network_sent + Math.random() * 1000000,\n        network_recv: this.systemResources.network_recv + Math.random() * 1000000\n      }\n    },\n    \n    cleanup() {\n      this.disconnectWebSocket()\n      if (this.updateTimer) {\n        clearInterval(this.updateTimer)\n      }\n      if (this.chart) {\n        this.chart.dispose()\n      }\n    },\n    \n    saveAdvancedConfig() {\n      this.showConfigDialog = false\n      this.$message.success('配置已保存')\n    },\n    \n    viewReport() {\n      if (this.currentReportId) {\n        this.$router.push({ \n          name: 'PerformanceResult-Detail', \n          params: { id: this.currentReportId }\n        })\n      }\n    },\n    \n    exportResults() {\n      if (this.currentReportId) {\n        this.$api.exportTaskReport(this.currentReportId)\n      }\n    },\n    \n    // 工具方法\n    getStatusType(status) {\n      const types = {\n        idle: 'info',\n        running: 'primary', \n        completed: 'success',\n        failed: 'danger',\n        stopped: 'warning'\n      }\n      return types[status] || 'info'\n    },\n    \n    getStatusText(status) {\n      const texts = {\n        idle: '待运行',\n        running: '运行中',\n        completed: '已完成',\n        failed: '失败',\n        stopped: '已停止'\n      }\n      return texts[status] || '未知'\n    },\n    \n    getErrorRateClass(rate) {\n      if (rate > 5) return 'error-high'\n      if (rate > 1) return 'error-medium'\n      return 'error-low'\n    },\n    \n    getCpuStatus(percent) {\n      if (percent > 80) return 'exception'\n      if (percent > 60) return 'warning'\n      return 'success'\n    },\n    \n    getMemoryStatus(percent) {\n      if (percent > 85) return 'exception'\n      if (percent > 70) return 'warning'\n      return 'success'\n    },\n    \n    formatDuration(seconds) {\n      if (!seconds) return '0s'\n      const hours = Math.floor(seconds / 3600)\n      const minutes = Math.floor((seconds % 3600) / 60)\n      const secs = seconds % 60\n      return `${hours}h ${minutes}m ${secs}s`\n    },\n    \n    formatTime(time) {\n      if (!time) return '--'\n      return new Date(time).toLocaleString()\n    },\n    \n    formatBytes(bytes) {\n      if (!bytes) return '0B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]\n    }\n  },\n  \n  watch: {\n    chartType() {\n      this.updateChart()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.execution-container {\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: calc(100vh - 60px);\n}\n\n.config-panel {\n  margin-bottom: 20px;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.panel-header h3 {\n  margin: 0;\n  color: #409eff;\n}\n\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.metric-card {\n  border: none;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.metric-content {\n  display: flex;\n  align-items: center;\n  padding: 10px;\n}\n\n.metric-icon {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 15px;\n  font-size: 24px;\n  color: white;\n}\n\n.tps-icon {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.response-icon {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.users-icon {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.error-icon {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.error-icon.error-medium {\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\n}\n\n.error-icon.error-high {\n  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);\n}\n\n.metric-info {\n  flex: 1;\n}\n\n.metric-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n  line-height: 1;\n}\n\n.metric-label {\n  font-size: 14px;\n  color: #909399;\n  margin-top: 5px;\n}\n\n.status-panel {\n  height: fit-content;\n}\n\n.status-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.status-label {\n  color: #606266;\n  font-size: 14px;\n}\n\n.progress-section {\n  margin: 20px 0;\n}\n\n.progress-label {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 10px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.progress-percent {\n  font-weight: bold;\n  color: #409eff;\n}\n\n.quick-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.quick-actions .el-button {\n  flex: 1;\n}\n\n.resource-item {\n  margin-bottom: 15px;\n}\n\n.resource-label {\n  font-size: 12px;\n  color: #909399;\n  margin-bottom: 5px;\n}\n\n.resource-value {\n  font-size: 12px;\n  color: #606266;\n  margin-left: 10px;\n}\n\n.network-io {\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n  color: #606266;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.chart-header h4 {\n  margin: 0;\n  color: #409eff;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .metrics-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n  .metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .panel-header {\n    flex-direction: column;\n    gap: 10px;\n  }\n}\n</style>", "import { render } from \"./PerformanceExecutionOptimized.vue?vue&type=template&id=1cb4d7ec&scoped=true\"\nimport script from \"./PerformanceExecutionOptimized.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceExecutionOptimized.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceExecutionOptimized.vue?vue&type=style&index=0&id=1cb4d7ec&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-1cb4d7ec\"]])\n\nexport default __exports__"], "names": ["class", "ref", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "shadow", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button_group", "_component_el_button", "type", "$data", "isRunning", "loading", "isStarting", "onClick", "$options", "toggleTest", "disabled", "selectedTask", "_component_el_icon", "_createBlock", "_component_VideoPause", "key", "_component_VideoPlay", "_toDisplayString", "_cache", "$event", "showConfigDialog", "_component_Setting", "_component_el_form", "model", "testConfig", "size", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_form_item", "label", "_component_el_select", "placeholder", "_Fragment", "_renderList", "tasks", "task", "_component_el_option", "id", "taskName", "value", "env_id", "environments", "env", "name", "mode", "_component_el_input_number", "users", "min", "max", "spawn_rate", "duration", "_component_el_input", "report_name", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_component_Trend<PERSON><PERSON>s", "_hoisted_6", "_hoisted_7", "currentMetrics", "tps", "_hoisted_8", "_hoisted_9", "_component_Timer", "_hoisted_10", "_hoisted_11", "avg_response_time", "_hoisted_12", "_hoisted_13", "_component_User", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_normalizeClass", "getErrorRateClass", "error_rate", "_component_WarningFilled", "_hoisted_17", "_hoisted_18", "toFixed", "_hoisted_19", "_component_el_radio_group", "chartType", "_component_el_radio_button", "_hoisted_20", "_hoisted_21", "_component_el_tag", "getStatusType", "testStatus", "getStatusText", "_hoisted_22", "formatDuration", "testDuration", "_hoisted_23", "formatTime", "testStartTime", "_hoisted_24", "estimatedEndTime", "_component_el_divider", "_hoisted_25", "_hoisted_26", "_hoisted_27", "testProgress", "_component_el_progress", "percentage", "status", "_hoisted_28", "viewReport", "currentReportId", "exportResults", "_hoisted_29", "systemResources", "cpu", "getCpuStatus", "_hoisted_30", "_hoisted_31", "memory", "getMemoryStatus", "_hoisted_32", "_hoisted_33", "_hoisted_34", "formatBytes", "network_sent", "network_recv", "_component_el_dialog", "title", "width", "footer", "_hoisted_35", "saveAdvancedConfig", "_component_el_tabs", "configTab", "_component_el_tab_pane", "advancedConfig", "think_time", "timeout", "retry_count", "log_level", "master_server", "servers", "server", "host_ip", "worker_servers", "multiple", "response_time_threshold", "append", "error_rate_threshold", "tps_threshold", "components", "VideoPlay", "VideoPause", "Setting", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Timer", "User", "WarningFilled", "data", "chart", "chartData", "times", "response_time", "wsConnection", "updateTimer", "guiUrl", "webPort", "computed", "this", "startTime", "Date", "getTime", "mounted", "loadInitialData", "initChart", "startUpdateTimer", "beforeUnmount", "cleanup", "methods", "tasksResponse", "$api", "getPerformanceTasksForExecution", "results", "envResponse", "getTestEnvironments", "serversResponse", "getServersForExecution", "error", "console", "$message", "stopTest", "startTest", "response", "runPerformanceTestOptimized", "report_id", "gui_url", "web_port", "success", "connectWebSocket", "message", "warning", "stopPerformanceTestOptimized", "disconnectWebSocket", "close", "protocol", "window", "location", "wsUrl", "host", "WebSocket", "onopen", "log", "send", "JSON", "stringify", "onmessage", "event", "parse", "handleWebSocketMessage", "onerror", "onclose", "setTimeout", "updateMetrics", "handleTestCompleted", "handleTestFailed", "updateCurrentStatus", "avg_tps", "current_users", "now", "toLocaleTimeString", "push", "Object", "keys", "for<PERSON>ach", "length", "shift", "updateChart", "$nextTick", "$refs", "chartContainer", "echarts", "option", "text", "getChartTitle", "left", "textStyle", "fontSize", "tooltip", "trigger", "xAxis", "axisLabel", "interval", "rotate", "yAxis", "getChartUnit", "series", "smooth", "areaStyle", "opacity", "itemStyle", "color", "getChartColor", "grid", "right", "bottom", "containLabel", "setOption", "titles", "units", "colors", "setInterval", "Math", "floor", "totalSeconds", "updateSystemResources", "random", "clearInterval", "dispose", "$router", "params", "exportTaskReport", "types", "idle", "running", "completed", "failed", "stopped", "texts", "rate", "percent", "seconds", "hours", "minutes", "secs", "time", "toLocaleString", "bytes", "k", "sizes", "i", "parseFloat", "pow", "watch", "__exports__", "render"], "sourceRoot": ""}