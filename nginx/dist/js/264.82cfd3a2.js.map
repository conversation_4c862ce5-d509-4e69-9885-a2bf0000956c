{"version": 3, "file": "js/264.82cfd3a2.js", "mappings": "2GAIE,SAAUA,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,OAAkBA,EAAQ,OACxDA,EAAQ,OAAaA,EAAQ,YAC5B,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAKC,WAAaL,EAASI,EAAMA,EAAKE,OAAQF,EAAKG,OAAQH,EAAKI,QAClE,CACD,EAXC,CAWCC,KAAM,SAAkBC,EAAOJ,EAAQC,EAAQC,GAElD,IAAIH,EAAaG,EAAQG,SAAS,CAChCC,MAAM,IAGJC,EAAMH,EAAMG,IAEhBR,EAAWS,UAAUC,OAAS,WAE5BP,EAAQM,UAAUC,OAAOC,MAAOP,KAAMQ,WAEtCR,KAAKS,KAAO,IAAIX,EAAO,CACrBY,MAAOV,KACPW,UAAW,CAAEC,EAAGZ,KAAKa,SAAW,KAGlCb,KAAKc,eAAiB,IAAIjB,CAC5B,EAEAD,EAAWS,UAAUU,gBAAkB,WAErCf,KAAKc,eAAeE,IAAKhB,KAAKiB,cAC3BC,KAAMlB,KAAKS,KAAKQ,aAAc,EAAE,GACnCjB,KAAKmB,UAAYnB,KAAKc,eAAeF,CACvC,EAEAhB,EAAWS,UAAUe,OAAS,SAAUC,EAAKC,GAC3CtB,KAAKuB,WAAYF,EAAKC,GAEtBvB,EAAQM,UAAUe,OAAOb,MAAOP,KAAMQ,UACxC,EAEAZ,EAAWS,UAAUkB,WAAa,SAAUF,EAAKC,GAC/C,GAAMtB,KAAKwB,QAAX,CAGA,IAAIC,EAAOzB,KAAK0B,qBAAsBL,EAAKC,GACvCK,EAAeC,KAAKC,MAAO7B,KAAK8B,aAAaC,EAAG/B,KAAK8B,aAAaE,GAClEC,EAAajC,KAAKa,SAAW,EAAIb,KAAK8B,aAAaI,YACnDF,EAAIhC,KAAKiB,aAAae,EACtBD,EAAI/B,KAAKiB,aAAac,EAE1B,GAAKT,EAASa,SAAW,CAEvB,IAAIC,EAAaT,EAAevB,EAAI,EAChCiC,EAAWV,EAAevB,EAAI,EAClCiB,EAAIiB,YACJjB,EAAIkB,IAAKP,EAAGD,EAAGE,EAAYG,EAAYC,EACzC,MAAYf,EAASkB,QAEnBb,GAAiBA,EAAevB,EAAI,GAAMA,EAAM,IAChDJ,KAAKyC,eAAeC,aAAc,IAAK,MAAQT,EAAa,QACxDA,EAAa,IAAMA,EAAa,UAAYA,EAAa,MAC7DjC,KAAKyC,eAAeC,aAAc,YAC9B,aAAeV,EAAI,IAAMD,EAAI,aAAeJ,EAAe,MAGjEL,EAASqB,OAAQtB,EAAKI,EAAMzB,KAAK2C,OAAQ3C,KAAK4C,MAAO5C,KAAK6C,gBAC1DvB,EAASnB,KAAMkB,EAAKI,EAAMzB,KAAKG,KAAMH,KAAK4C,OAC1CtB,EAASwB,IAAKzB,EAAKI,EAxBnB,CAyBF,EAEA,IAAIsB,EAAS,6BAeb,OAbAnD,EAAWS,UAAUqB,qBAAuB,SAAUL,EAAKC,GACzD,GAAMA,EAASkB,MASf,OANMxC,KAAKyC,iBAETzC,KAAKyC,eAAiBO,SAASC,gBAAiBF,EAAQ,QACxD/C,KAAKyC,eAAeC,aAAc,iBAAkB,SACpD1C,KAAKyC,eAAeC,aAAc,kBAAmB,UAEhD1C,KAAKyC,cACd,EAEO7C,CAEP,E,kDC3FE,SAAUN,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,YAC7B,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAKuD,YAAc3D,EAASI,EAAKE,OACnC,CACD,CAVC,CAUCG,KAAM,SAAkBH,GAE3B,SAASqD,EAAaC,EAAQC,EAAQC,GACpCrD,KAAKmD,OAASA,EACdnD,KAAKoD,OAASA,EAAOE,IAAKC,GAC1BvD,KAAKwD,aAAeJ,EAAOE,IAAKG,GAChCzD,KAAKqD,cAAgBA,EACrBrD,KAAK0D,eAAiB1D,KAAKwD,aAAcxD,KAAKwD,aAAaG,OAAS,GAGrD,OAAVR,IACHnD,KAAK4D,cAAgB,CAAE,IAAI/D,EAAU,IAAIA,GAE7C,CAEA,SAAS0D,EAAgBM,GACvB,OAAKA,aAAiBhE,EACbgE,EAEA,IAAIhE,EAAQgE,EAEvB,CAEA,SAASJ,EAAcI,GACrB,OAAO,IAAIhE,EAAQgE,EACrB,CAEAX,EAAY7C,UAAUyD,MAAQ,WAE5B,IAAIV,EAASpD,KAAKoD,OAClBpD,KAAKwD,aAAaO,QAAS,SAAUC,EAAaC,GAChD,IAAIJ,EAAQT,EAAOa,GACnBD,EAAYhD,IAAK6C,EACnB,EACF,EAEAX,EAAY7C,UAAU6D,UAAY,SAAUC,EAAaC,EAAUC,GACjErE,KAAKwD,aAAaO,QAAS,SAAUC,GACnCA,EAAYE,UAAWC,EAAaC,EAAUC,EAChD,EACF,EAEAnB,EAAY7C,UAAUe,OAAS,SAAUC,EAAKI,EAAMH,GAClD,OAAOtB,KAAMA,KAAKmD,QAAU9B,EAAKI,EAAMH,EACzC,EAEA4B,EAAY7C,UAAUiE,KAAO,SAAUjD,EAAKI,EAAMH,GAChD,OAAOA,EAASgD,KAAMjD,EAAKI,EAAMzB,KAAKwD,aAAa,GACrD,EAEAN,EAAY7C,UAAUkE,KAAO,SAAUlD,EAAKI,EAAMH,GAChD,OAAOA,EAASiD,KAAMlD,EAAKI,EAAMzB,KAAKwD,aAAa,GACrD,EAEAN,EAAY7C,UAAUmE,OAAS,SAAUnD,EAAKI,EAAMH,GAClD,IAAImD,EAAMzE,KAAKwD,aAAa,GACxBkB,EAAM1E,KAAKwD,aAAa,GACxBV,EAAM9C,KAAKwD,aAAa,GAC5B,OAAOlC,EAASkD,OAAQnD,EAAKI,EAAMgD,EAAKC,EAAK5B,EAC/C,EAEA,IAAI6B,EAAkB,EAAE,GAaxB,OAXAzB,EAAY7C,UAAUkC,IAAM,SAAUlB,EAAKI,EAAMH,GAC/C,IAAIsD,EAAO5E,KAAKqD,cACZwB,EAAS7E,KAAKwD,aAAa,GAC3BV,EAAM9C,KAAKwD,aAAa,GACxBiB,EAAMzE,KAAK4D,cAAc,GACzBc,EAAM1E,KAAK4D,cAAc,GAG7B,OAFAa,EAAIzD,IAAK4D,GAAO1D,KAAM2D,EAAQF,GAC9BD,EAAI1D,IAAK8B,GAAM5B,KAAM2D,EAAQF,GACtBrD,EAASkD,OAAQnD,EAAKI,EAAMgD,EAAKC,EAAK5B,EAC/C,EAEOI,CAEP,E,yBCtFE,SAAU5D,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,OAAkBA,EAAQ,OACxDA,EAAQ,OAAmBA,EAAQ,OAAaA,EAAQ,YACvD,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAKmF,KAAOvF,EAASI,EAAMA,EAAKE,OAAQF,EAAKuD,YACzCvD,EAAKG,OAAQH,EAAKI,QACxB,CACD,EAZC,CAYCC,KAAM,SAAkBC,EAAOJ,EAAQqD,EAAapD,EAAQC,GAE/D,IAAI+E,EAAO/E,EAAQG,SAAS,CAC1ByD,OAAQ,EACRxD,MAAM,IAGJC,EAAMH,EAAMG,IAEhB0E,EAAKzE,UAAUC,OAAS,WAEtBP,EAAQM,UAAUC,OAAOC,MAAOP,KAAMQ,WAEtCR,KAAKS,KAAO,IAAIX,EAAO,CACrBY,MAAOV,KACPW,UAAW,CAAEC,EAAGZ,KAAK2D,UAIvB3D,KAAK+E,WAAa,IAAIlF,EACtBG,KAAKc,eAAiB,IAAIjB,EAC1BG,KAAKgF,SAAW,IAAInF,EACpBG,KAAKiF,SAAW,IAAIpF,EAEpBG,KAAKkF,oBAAsB,CACzB,IAAIhC,EAAa,OAAQ,CAAE,CAAC,IAC5B,IAAIA,EAAa,OAAQ,CAAE,CAAC,IAC5B,IAAIA,EAAa,OAAQ,CAAE,CAAC,IAEhC,EAEA4B,EAAKzE,UAAUU,gBAAkB,WAE/Bf,KAAKc,eAAeE,IAAKhB,KAAKiB,cAC3BC,KAAMlB,KAAKS,KAAKQ,aAAc,EAAE,GACnCjB,KAAKmB,UAAYnB,KAAKc,eAAeF,CACvC,EAEAkE,EAAKzE,UAAUe,OAAS,SAAUC,EAAKC,GACrCtB,KAAKmF,kBAAmB9D,EAAKC,GAC7BvB,EAAQM,UAAUe,OAAOb,MAAOP,KAAMQ,UACxC,EAEAsE,EAAKzE,UAAU8E,kBAAoB,SAAU9D,EAAKC,GAChD,GAAMtB,KAAKwB,QAAX,CAGAxB,KAAK+E,WAAW/D,IAAKhB,KAAKS,KAAKQ,cAC5BmE,SAAUpF,KAAKiB,cAElB,IAAIoD,EAAQrE,KAAK8B,aAAaI,YAC1BmD,EAAerF,KAAK+E,WAAWO,cAC/BC,EAAiBvF,KAAK8B,aAAawD,cAEnCE,EAAa5D,KAAK6D,KAAMF,EAAelB,GACvCqB,EAAQ9D,KAAK+D,IAAKH,GAClBI,EAAS5F,KAAKa,SAAW,EAAIwD,EAE7BwB,EAAgBD,EAASF,EAAQL,EACrC,GAAMQ,EAAN,CAIA,IAAIC,EAAYlE,KAAKC,MAAO7B,KAAK8B,aAAaC,EAAG/B,KAAK8B,aAAaE,GAC/D5B,EAAI,EACJ2F,EAAgBV,EAAaK,EAC7BM,EAAepE,KAAK6D,KAAMG,EAAOG,GAEjCf,EAAWhF,KAAKgF,SAChBC,EAAWjF,KAAKiF,SAEpBD,EAAShD,EAAIJ,KAAKqE,IAAKD,GAAiBJ,EAASF,EACjDV,EAASjD,EAAIH,KAAK+D,IAAKK,GAAiBJ,EAExCX,EAASjE,IAAKhB,KAAKgF,UACnBC,EAASlD,IAAM,EAEfiD,EAASkB,QAASJ,GAClBb,EAASiB,QAASJ,GAClBd,EAASmB,IAAKnG,KAAKiB,cACnBgE,EAASkB,IAAKnG,KAAKiB,cAEnBjB,KAAKoG,sBAAuB,EAAGpB,GAC/BhF,KAAKoG,sBAAuB,EAAGpG,KAAKS,KAAKQ,cACzCjB,KAAKoG,sBAAuB,EAAGnB,GAG/B,IAAIxD,EAAOzB,KAAKqG,wBAAyBhF,EAAKC,GAC9CA,EAASgF,WAAYjF,EAAKI,EAAMzB,KAAKkF,qBACrC5D,EAASqB,OAAQtB,EAAKI,EAAMzB,KAAK2C,OAAQ3C,KAAK4C,MAAO5C,KAAK6C,gBAC1DvB,EAASnB,KAAMkB,EAAKI,EAAMzB,KAAKG,KAAMH,KAAK4C,OAC1CtB,EAASwB,IAAKzB,EAAKI,EA9BnB,CAfA,CA8CF,EAEA,IAAIsB,EAAS,6BAoBb,OAlBA+B,EAAKzE,UAAUgG,wBAA0B,SAAUhF,EAAKC,GACtD,GAAMA,EAASkB,MASf,OANMxC,KAAKuG,oBAETvG,KAAKuG,kBAAoBvD,SAASC,gBAAiBF,EAAQ,QAC3D/C,KAAKuG,kBAAkB7D,aAAc,iBAAkB,SACvD1C,KAAKuG,kBAAkB7D,aAAc,kBAAmB,UAEnD1C,KAAKuG,iBACd,EAEAzB,EAAKzE,UAAU+F,sBAAwB,SAAUI,EAAO3C,GACtD,IAAIG,EAAchE,KAAKkF,oBAAqBsB,GAAQhD,aAAa,GACjEQ,EAAYhD,IAAK6C,EACnB,EAEOiB,CAEP,E,yBChIE,SAAUxF,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,OAC9BA,EAAQ,OAAmBA,EAAQ,OAAYA,EAAQ,OACvDA,EAAQ,YACP,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAK8G,SAAWlH,EAASI,EAAMA,EAAKuD,YAAavD,EAAK+G,MAClD/G,EAAKgH,MAAOhH,EAAKI,QACvB,CACD,EAbC,CAaCC,KAAM,SAAkBC,EAAOiD,EAAawD,EAAOC,EAAO5G,GAE7D,SAAS6G,IAAQ,CAIjB,IAAIC,EAAgBF,EAAMzG,SAAS,CACjC0C,MAAO,OACPkE,YAAY,IAGdD,EAAcxG,UAAUC,OAAS,WAC/BqG,EAAMtG,UAAUC,OAAOC,MAAOP,KAAMQ,WACpCR,KAAK+G,aAAe,CAClB,IAAI7D,EAAa,OAAQ,CAAE,CAAC,IAC5B,IAAIA,EAAa,OAAQ,CAAE,CAAC,IAEhC,EAEA2D,EAAcxG,UAAUe,OAAS,SAAUC,EAAKC,GAC9CtB,KAAKgH,sBAAuB3F,EAAKC,GACjCqF,EAAMtG,UAAUe,OAAOb,MAAOP,KAAMQ,UACtC,EAEAqG,EAAcxG,UAAU2G,sBAAwB,SAAU3F,EAAKC,GAC7D,GAAMtB,KAAKwB,QAAX,CAIA,IAAIC,EAAOzB,KAAKiH,iBAAkB5F,EAAKC,GACnC4F,EAAYlH,KAAKkH,UACjBC,EAAWnH,KAAKmH,SAChB9C,EAAQ6C,EAAUpF,aAAaI,YAC/BkF,EAAcF,EAAUrG,SAAWwD,EAAQ6C,EAAUrE,eAEzD7C,KAAK+G,aAAa,GAAGvD,aAAa,GAAGxC,IAAKkG,EAAUjG,cACpDjB,KAAK+G,aAAa,GAAGvD,aAAa,GAAGxC,IAAKmG,EAASlG,cAE9CK,EAASa,WACZd,EAAIgG,QAAU,QAEhB/F,EAASgF,WAAYjF,EAAKI,EAAMzB,KAAK+G,cACrCzF,EAASqB,OAAQtB,EAAKI,GAAM,EAAMzB,KAAK4C,MAAOwE,GAC9C9F,EAASwB,IAAKzB,EAAKI,GAEdH,EAASa,WACZd,EAAIgG,QAAU,QAnBhB,CAqBF,EAEA,IAAItE,EAAS,6BAEb8D,EAAcxG,UAAU4G,iBAAmB,SAAU5F,EAAKC,GACxD,GAAMA,EAASkB,MAOf,OAJMxC,KAAKsH,aAETtH,KAAKsH,WAAatE,SAASC,gBAAiBF,EAAQ,SAE/C/C,KAAKsH,UACd,EAIAT,EAAcxG,UAAUkH,UAAYX,EAIpC,IAAIY,EAAkBzH,EAAQG,WAE9BsH,EAAgBnH,UAAUkH,UAAYX,EAItC,IAAIH,EAAWC,EAAMxG,SAAS,CAC5BW,SAAU,EACV8C,OAAQ,EACR8D,eAAWC,EACXvH,MAAM,IAGJC,EAAMH,EAAMG,IAEhBqG,EAASpG,UAAUC,OAAS,WAE1BoG,EAAMrG,UAAUC,OAAOC,MAAOP,KAAMQ,WAGpCR,KAAK2H,MAAQ,IAAId,EAAc,CAC7BnG,MAAOV,KACP4C,MAAO5C,KAAK4C,MACZpB,QAASxB,KAAKwB,UAEhB,IAAIoG,EAAQ5H,KAAK2D,OAAS,EACtBkE,EAAY7H,KAAK8H,WAAY,EAEjC9H,KAAKkH,UAAYlH,KAAK2H,MAAMT,UAAY,IAAInH,EAAQ,CAClDW,MAAOV,KAAK2H,MACZ9G,SAAUb,KAAKa,SACfF,UAAW,CAAEC,EAAGgH,GAChBG,OAAQ,CAAEhG,EAAG3B,EAAI,GACjBwC,MAAO5C,KAAK4C,MACZD,OAAQ3C,KAAK2C,OACbxC,KAAMH,KAAKG,KACX2H,SAAU9H,KAAKyH,WAAaI,EAC5BrG,QAASxB,KAAKwB,UAGhBxB,KAAKmH,SAAWnH,KAAK2H,MAAMR,SAAWnH,KAAKkH,UAAUc,KAAK,CACxDrH,UAAW,CAAEC,GAAIgH,GACjBG,OAAQ,CAAEhG,EAAG,GACb+F,SAAUD,GAEd,EAGApB,EAASpG,UAAUe,OAAS,WAAY,EAIxC,IAAI6G,EAAkB,CAAE,SAAU,OAAQ,QAAS,WAsBnD,OArBAA,EAAgBlE,QAAS,SAAUmE,GAEjC,IAAIC,EAAQ,IAAMD,EAClBE,OAAOC,eAAgB5B,EAASpG,UAAW6H,EAAU,CACnDI,IAAK,WACH,OAAOtI,KAAMmI,EACf,EACAnH,IAAK,SAAUuH,GACbvI,KAAMmI,GAAUI,EAEXvI,KAAKkH,YACRlH,KAAKkH,UAAWgB,GAAaK,EAC7BvI,KAAKmH,SAAUe,GAAaK,EAC5BvI,KAAK2H,MAAOO,GAAaK,EAE7B,GAEJ,GAIO9B,CAEP,E,yBC9JE,SAAUnH,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,OAAkBA,EAAQ,OACxDA,EAAQ,OAAYA,EAAQ,YAC3B,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAK6I,IAAMjJ,EAASI,EAAMA,EAAKG,OAAQH,EAAK+G,MAAO/G,EAAK8I,KAC1D,CACD,EAXC,CAWCzI,KAAM,SAAkBC,EAAOH,EAAQ4G,EAAO+B,GAIjD,IAAIC,EAAUD,EAAKvI,WAGnBwI,EAAQrI,UAAUkH,UAAY,WAAY,EAI1C,IAAInH,EAAMH,EAAMG,IACZuI,EAAY,CACd,YACA,WACA,WACA,YACA,UACA,cAGEC,EAAc3I,EAAM4I,OAAQ,CAAC,EAAGnC,EAAMoC,iBACnCF,EAAYG,KACnBJ,EAAU5E,QAAS,SAAUiF,GAC3BJ,EAAaI,IAAa,CAC5B,GACA/I,EAAM4I,OAAQD,EAAa,CACzBK,MAAO,EACPC,OAAQ,EACRC,MAAO,EACPhJ,MAAM,IAGR,IAAIqI,EAAM1I,EAAOI,SAAU0I,GAG3BJ,EAAInI,UAAUC,OAAS,SAAU8I,GAC/BtJ,EAAOO,UAAUC,OAAO+I,KAAMrJ,KAAMoJ,GACpCpJ,KAAKsJ,aAELtJ,KAAKG,KAAOH,KAAKG,IACnB,EAEAqI,EAAInI,UAAUiJ,WAAa,WAEzBX,EAAU5E,QAAS,SAAUiF,GAC3BhJ,KAAMgJ,GAAahJ,KAAMgJ,EAC3B,EAAGhJ,KACL,EAGA2I,EAAU5E,QAAS,SAAUiF,GAC3B,IAAIO,EAAY,IAAMP,EACtBZ,OAAOC,eAAgBG,EAAInI,UAAW2I,EAAU,CAC9CV,IAAK,WACH,OAAOtI,KAAMuJ,EACf,EACAvI,IAAK,SAAUuH,GACbvI,KAAMuJ,GAAchB,EACpBvI,KAAKwJ,QAASR,EAAUT,EAC1B,GAEJ,GAEAC,EAAInI,UAAUmJ,QAAU,SAAUR,EAAUT,GAC1C,IAAIkB,EAAeT,EAAW,OAC1BU,EAAO1J,KAAMyJ,GAEjB,GAAMlB,EAAN,CAKA,IAAIa,EAAUpJ,KAAK2J,eAAgBX,GACnCI,EAAQxG,MAAwB,iBAAT2F,EAAoBA,EAAQvI,KAAK4C,MAEnD8G,EAEHA,EAAKE,WAAYR,GAGjBM,EAAO1J,KAAMyJ,GAAiB,IAAIf,EAASU,GAE7CM,EAAKJ,aACLtJ,KAAK6J,SAAUH,EAbf,MAFE1J,KAAK8J,YAAaJ,EAgBtB,EAEAlB,EAAInI,UAAUsJ,eAAiB,SAAUX,GACvC,MAAO,CACLvB,UAAW,CACTwB,MAAOjJ,KAAKiJ,MACZC,OAAQlJ,KAAKkJ,OACbvI,UAAW,CAAEC,EAAGZ,KAAKmJ,MAAQ,IAE/BY,SAAU,CACRd,MAAOjJ,KAAKiJ,MACZC,OAAQlJ,KAAKkJ,OACbvI,UAAW,CAAEC,GAAIZ,KAAKmJ,MAAQ,GAC9BpB,OAAQ,CAAEhG,EAAG3B,EAAI,IAEnB4J,SAAU,CACRf,MAAOjJ,KAAKmJ,MACZD,OAAQlJ,KAAKkJ,OACbvI,UAAW,CAAEqB,GAAIhC,KAAKiJ,MAAQ,GAC9BlB,OAAQ,CAAEhG,GAAI3B,EAAI,IAEpB6J,UAAW,CACThB,MAAOjJ,KAAKmJ,MACZD,OAAQlJ,KAAKkJ,OACbvI,UAAW,CAAEqB,EAAGhC,KAAKiJ,MAAQ,GAC7BlB,OAAQ,CAAEhG,EAAG3B,EAAI,IAEnB8J,QAAS,CACPjB,MAAOjJ,KAAKiJ,MACZC,OAAQlJ,KAAKmJ,MACbxI,UAAW,CAAEoB,GAAI/B,KAAKkJ,OAAS,GAC/BnB,OAAQ,CAAE/F,GAAI5B,EAAI,IAEpB+J,WAAY,CACVlB,MAAOjJ,KAAKiJ,MACZC,OAAQlJ,KAAKmJ,MACbxI,UAAW,CAAEoB,EAAG/B,KAAKkJ,OAAS,GAC9BnB,OAAQ,CAAE/F,EAAG5B,EAAI,KAElB4I,EACL,EAIA,IAAIf,EAAkB,CAAE,QAAS,SAAU,OAAQ,WAAY,QAC7D,WAsBF,OArBAA,EAAgBlE,QAAS,SAAUmE,GAEjC,IAAIC,EAAQ,IAAMD,EAClBE,OAAOC,eAAgBG,EAAInI,UAAW6H,EAAU,CAC9CI,IAAK,WACH,OAAOtI,KAAMmI,EACf,EACAnH,IAAK,SAAUuH,GACbvI,KAAMmI,GAAUI,EAChBI,EAAU5E,QAAS,SAAUiF,GAC3B,IAAIU,EAAO1J,KAAMgJ,EAAW,QACxBoB,EAAyC,iBAApBpK,KAAMgJ,GAC3BqB,EAAgC,SAAZnC,GAAuBkC,EAC1CV,IAASW,IACZX,EAAMxB,GAAaK,EAEvB,EAAGvI,KACL,GAEJ,GAEOwI,CAEP,E,yBCrKE,SAAUlJ,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,YAC7B,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAK8I,KAAOlJ,EAASI,EAAK+G,MAC5B,CACD,EAVC,CAUC1G,KAAM,SAAkB0G,GAE3B,IAAI+B,EAAO/B,EAAMxG,SAAS,CACxB+I,MAAO,EACPC,OAAQ,IAeV,OAZAT,EAAKpI,UAAUiK,QAAU,WACvB,IAAItI,EAAIhC,KAAKiJ,MAAQ,EACjBlH,EAAI/B,KAAKkJ,OAAS,EAEtBlJ,KAAK+I,KAAO,CACV,CAAE/G,GAAIA,EAAGD,GAAIA,GACb,CAAEC,EAAIA,EAAGD,GAAIA,GACb,CAAEC,EAAIA,EAAGD,EAAIA,GACb,CAAEC,GAAIA,EAAGD,EAAIA,GAEjB,EAEO0G,CAEP,E,kDC/BE,SAAUnJ,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,OAAkBA,EAAQ,OACxDA,EAAQ,OAAmBA,EAAQ,YAClC,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAK+G,MAAQnH,EAASI,EAAMA,EAAKE,OAAQF,EAAKuD,YAAavD,EAAKG,OAClE,CACD,CAXC,CAWCE,KAAM,SAAkBC,EAAOJ,EAAQqD,EAAapD,GAEvD,IAAI4G,EAAQ5G,EAAOI,SAAS,CAC1ByC,OAAQ,EACRxC,MAAM,EACNyC,MAAO,OACP2H,QAAQ,EACR/I,SAAS,EACTuH,KAAM,CAAE,CAAC,GACTyB,MAAO,CAAE5J,EAAG,GACZkH,UAAU,IAGZpB,EAAMrG,UAAUC,OAAS,SAAU8I,GACjCtJ,EAAOO,UAAUC,OAAO+I,KAAMrJ,KAAMoJ,GACpCpJ,KAAKsJ,aAELtJ,KAAKwK,MAAQ,IAAI3K,EAAQuJ,EAAQoB,OAASxK,KAAKwK,OAC/CxK,KAAKyK,YAAc,IAAI5K,EAAQG,KAAKwK,OACpCxK,KAAK8B,aAAe,IAAIjC,CAC1B,EAEA,IAAI6K,EAAc,CAChB,OACA,OACA,SACA,OAGFhE,EAAMrG,UAAUiJ,WAAa,WAC3BtJ,KAAKsK,UACLtK,KAAK2K,oBACP,EAGAjE,EAAMrG,UAAUiK,QAAU,WAAY,EAGtC5D,EAAMrG,UAAUsK,mBAAqB,WACnC,IAAItH,EACJrD,KAAK+G,aAAe/G,KAAK+I,KAAKzF,IAAK,SAAUsH,EAAU3G,GAGrD,IAAI4G,EAAOzC,OAAOyC,KAAMD,GACpBzH,EAAS0H,EAAK,GACdzH,EAASwH,EAAUzH,GAEnB2H,EAA+B,GAAfD,EAAKlH,SAAiD,GAAlC+G,EAAYK,QAAS5H,GACvD2H,IACJ3H,EAAS,OACTC,EAASwH,GAGX,IAAII,EAAyB,QAAV7H,GAA8B,QAAVA,EACnC8H,EAAgBC,MAAMC,QAAS/H,GAC9B4H,IAAiBC,IACpB7H,EAAS,CAAEA,IAIbD,EAAe,IAANc,EAAU,OAASd,EAE5B,IAAIiI,EAAU,IAAIlI,EAAaC,EAAQC,EAAQC,GAG/C,OADAA,EAAgB+H,EAAQ1H,eACjB0H,CACT,EACF,EAIA1E,EAAMrG,UAAUyD,MAAQ,WACtB9D,KAAKiB,aAAaD,IAAKhB,KAAKqL,QAC5BrL,KAAKyK,YAAYzJ,IAAKhB,KAAKwK,OAE3BxK,KAAK+G,aAAahD,QAAS,SAAUqH,GACnCA,EAAQtH,OACV,EACF,EAEA4C,EAAMrG,UAAU6D,UAAY,SAAUC,EAAaC,EAAUC,GAE3DrE,KAAKiB,aAAaiD,UAAWC,EAAaC,EAAUC,GACpDrE,KAAKyK,YAAYvG,UAAWC,EAAaC,EAAUC,GACnDrE,KAAK8B,aAAad,IAAKhB,KAAKiB,cAAemE,SAAUpF,KAAKyK,aAE1DzK,KAAK+G,aAAahD,QAAS,SAAUqH,GACnCA,EAAQlH,UAAWC,EAAaC,EAAUC,EAC5C,GAEArE,KAAKsL,SAASvH,QAAS,SAAUwH,GAC/BA,EAAMrH,UAAWC,EAAaC,EAAUC,EAC1C,EACF,EAEAqC,EAAMrG,UAAUU,gBAAkB,WAGhC,IAAIyK,EAAaxL,KAAK+G,aAAapD,OAC/B8H,EAAazL,KAAK+G,aAAa,GAAGrD,eAClCgI,EAAY1L,KAAK+G,aAAcyE,EAAa,GAAI9H,eAEhDiI,EAAgBH,EAAa,GAAKC,EAAWG,OAAQF,GACpDC,IACHH,GAAc,GAIhB,IADA,IAAIK,EAAiB,EACX5H,EAAI,EAAGA,EAAIuH,EAAYvH,IAC/B4H,GAAkB7L,KAAK+G,aAAa9C,GAAGP,eAAe9C,EAExDZ,KAAKmB,UAAY0K,EAAeL,CAClC,EAIA9E,EAAMrG,UAAUe,OAAS,SAAUC,EAAKC,GACtC,IAAIqC,EAAS3D,KAAK+G,aAAapD,OAC/B,GAAM3D,KAAKwB,SAAYmC,IAIvB3D,KAAK8L,aAAe9L,KAAK8B,aAAalB,EAAI,EACpCZ,KAAK8H,WAAY9H,KAAK8L,cAA5B,CAGA,IAAMxK,EACJ,MAAM,IAAIyK,MAAO,kCAAoCzK,GAGvD,IAAI0K,EAAkB,GAAVrI,EACPrC,EAASa,UAAY6J,EACxBhM,KAAKiM,gBAAiB5K,EAAKC,GAE3BtB,KAAKsG,WAAYjF,EAAKC,EATxB,CAWF,EAEA,IAAIlB,EAAMH,EAAMG,IAEhBsG,EAAMrG,UAAU4L,gBAAkB,SAAU5K,GAC1C,IAAI6K,EAAYlM,KAAK6C,eACrB,GAAMqJ,EAAN,CAGA7K,EAAI8K,UAAYnM,KAAKoM,iBACrB,IAAIvI,EAAQ7D,KAAK+G,aAAa,GAAGrD,eACjCrC,EAAIiB,YACJ,IAAIsD,EAASsG,EAAU,EACvB7K,EAAIkB,IAAKsB,EAAM7B,EAAG6B,EAAM9B,EAAG6D,EAAQ,EAAGxF,GACtCiB,EAAIlB,MANJ,CAOF,EAEAuG,EAAMrG,UAAUwC,aAAe,WAC7B,OAAM7C,KAAK2C,OAGS,GAAf3C,KAAK2C,OACD,EAEF3C,KAAK2C,OALH,CAMX,EAEA+D,EAAMrG,UAAU+L,eAAiB,WAE/B,IAAIC,EAA0C,iBAAjBrM,KAAK8H,UAAwB9H,KAAK8L,aAC3DlJ,EAAQyJ,EAAkBrM,KAAK8H,SAAW9H,KAAK4C,MACnD,OAAOA,CACT,EAEA8D,EAAMrG,UAAUiG,WAAa,SAAUjF,EAAKC,GAC1C,IAAIG,EAAOzB,KAAKiH,iBAAkB5F,EAAKC,GACnCgL,EAA0C,GAA5BtM,KAAK+G,aAAapD,QACH,QAA/B3D,KAAK+G,aAAa,GAAG5D,OACnBoJ,GAAYD,GAAetM,KAAKuK,OAChC3H,EAAQ5C,KAAKoM,iBAEjB9K,EAASgF,WAAYjF,EAAKI,EAAMzB,KAAK+G,aAAcwF,GACnDjL,EAASqB,OAAQtB,EAAKI,EAAMzB,KAAK2C,OAAQC,EAAO5C,KAAK6C,gBACrDvB,EAASnB,KAAMkB,EAAKI,EAAMzB,KAAKG,KAAMyC,GACrCtB,EAASwB,IAAKzB,EAAKI,EACrB,EAEA,IAAIsB,EAAS,6BAeb,OAbA2D,EAAMrG,UAAU4G,iBAAmB,SAAU5F,EAAKC,GAChD,GAAMA,EAASkB,MASf,OANMxC,KAAKsH,aAETtH,KAAKsH,WAAatE,SAASC,gBAAiBF,EAAQ,QACpD/C,KAAKsH,WAAW5E,aAAc,iBAAkB,SAChD1C,KAAKsH,WAAW5E,aAAc,kBAAmB,UAE5C1C,KAAKsH,UACd,EAEOZ,CAEP,E,iCCnNE,SAAUpH,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,YAC7B,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAK6M,YAAcjN,EAASI,EAAK+G,MACnC,CACD,CAVC,CAUC1G,KAAM,SAAkB0G,GAE3B,IAAI8F,EAAc9F,EAAMxG,SAAS,CAC/B+I,MAAO,EACPC,OAAQ,EACRuD,aAAc,IACdlC,QAAQ,IAsDV,OAnDAiC,EAAYnM,UAAUiK,QAAU,WAI9B,IAAIoC,EAAK1M,KAAKiJ,MAAQ,EAClB0D,EAAK3M,KAAKkJ,OAAS,EACnB0D,EAAYhL,KAAKiL,IAAKH,EAAIC,GAC1BF,EAAe7K,KAAKiL,IAAK7M,KAAKyM,aAAcG,GAC5CE,EAAKJ,EAAKD,EACVM,EAAKJ,EAAKF,EACV1D,EAAO,CAET,CAAE/G,EAAG8K,EAAI/K,GAAI4K,GACb,CAAEpK,IAAK,CACL,CAAEP,EAAG0K,EAAI3K,GAAI4K,GACb,CAAE3K,EAAG0K,EAAI3K,GAAIgL,MAIZA,GACHhE,EAAKiE,KAAK,CAAEhL,EAAG0K,EAAI3K,EAAGgL,IAExBhE,EAAKiE,KAAK,CAAEzK,IAAK,CACf,CAAEP,EAAG0K,EAAI3K,EAAI4K,GACb,CAAE3K,EAAG8K,EAAI/K,EAAI4K,MAGVG,GACH/D,EAAKiE,KAAK,CAAEhL,GAAI8K,EAAI/K,EAAG4K,IAEzB5D,EAAKiE,KAAK,CAAEzK,IAAK,CACf,CAAEP,GAAI0K,EAAI3K,EAAI4K,GACd,CAAE3K,GAAI0K,EAAI3K,EAAIgL,MAGXA,GACHhE,EAAKiE,KAAK,CAAEhL,GAAI0K,EAAI3K,GAAIgL,IAE1BhE,EAAKiE,KAAK,CAAEzK,IAAK,CACf,CAAEP,GAAI0K,EAAI3K,GAAI4K,GACd,CAAE3K,GAAI8K,EAAI/K,GAAI4K,MAIXG,GACH/D,EAAKiE,KAAK,CAAEhL,EAAG8K,EAAI/K,GAAI4K,IAGzB3M,KAAK+I,KAAOA,CACd,EAEOyD,CAEP,E,kDCxEE,SAAUlN,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,OAAkBA,EAAQ,OACxDA,EAAQ,OAAsBA,EAAQ,YACrC,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAKG,OAASP,EAASI,EAAMA,EAAKE,OAAQF,EAAKsN,eAC3CtN,EAAKuN,YACX,CACD,CAZC,CAYClN,KAAM,SAAkBC,EAAOJ,EAAQoN,EAAgBC,GAE1D,IAAI9M,EAAMH,EAAMG,IACZ+M,EAAW,CAAEnL,EAAG,EAAGD,EAAG,EAAGnB,EAAG,GAEhC,SAASd,EAAQsJ,GACfpJ,KAAKM,OAAQ8I,GAAW,CAAC,EAC3B,CAiMA,SAASgE,EAAaC,GACpB,OAAO,SAAUvE,GAEf,SAASwE,EAAMlE,GACbpJ,KAAKM,OAAQ8I,GAAW,CAAC,EAC3B,CAkBA,OAhBAkE,EAAKjN,UAAY+H,OAAO9H,OAAQ+M,EAAMhN,WACtCiN,EAAKjN,UAAUkN,YAAcD,EAE7BA,EAAKxE,SAAW7I,EAAM4I,OAAQ,CAAC,EAAGwE,EAAMvE,UACxC7I,EAAM4I,OAAQyE,EAAKxE,SAAUA,GAE7BwE,EAAKE,WAAaH,EAAMG,WAAWC,MAAO,GAE1CrF,OAAOyC,KAAMyC,EAAKxE,UAAW/E,QAAS,SAAU2J,GACN,IAAlCJ,EAAKE,WAAWzC,QAAS2C,IAC7BJ,EAAKE,WAAWR,KAAMU,EAE1B,GAEAJ,EAAKpN,SAAWkN,EAAaE,GAEtBA,CACT,CACF,CAIA,OA5NAxN,EAAOO,UAAUC,OAAS,SAAU8I,GAClCpJ,KAAKsL,SAAW,GAEhBrL,EAAM4I,OAAQ7I,KAAMA,KAAKuN,YAAYzE,UACrC9I,KAAK4J,WAAYR,GAGjBpJ,KAAKW,UAAY,IAAId,EAAQuJ,EAAQzI,WACrCX,KAAK+H,OAAS,IAAIlI,EAAQuJ,EAAQrB,QAClC/H,KAAKqE,MAAQ,IAAIxE,EAAQsN,GAAWQ,SAAU3N,KAAKqE,OAEnDrE,KAAKqL,OAAS,IAAIxL,EAClBG,KAAKiB,aAAe,IAAIpB,EAEnBG,KAAKU,OACRV,KAAKU,MAAMmJ,SAAU7J,KAEzB,EAEAF,EAAOgJ,SAAW,CAAC,EAEnBhJ,EAAO0N,WAAapF,OAAOyC,KAAM/K,EAAOgJ,UAAW8E,OAAO,CACxD,SACA,YACA,QACA,UAGF9N,EAAOO,UAAUuJ,WAAa,SAAUR,GACtC,IAAIoE,EAAaxN,KAAKuN,YAAYC,WAElC,IAAM,IAAIE,KAAOtE,GACoB,GAA9BoE,EAAWzC,QAAS2C,KACvB1N,KAAM0N,GAAQtE,EAASsE,GAG7B,EAEA5N,EAAOO,UAAUwJ,SAAW,SAAUgE,IACI,GAAnC7N,KAAKsL,SAASP,QAAS8C,KAG5BA,EAAMC,SACND,EAAMnN,MAAQV,KACdA,KAAKsL,SAAS0B,KAAMa,GACtB,EAEA/N,EAAOO,UAAUyJ,YAAc,SAAU+D,GACvC,IAAIrH,EAAQxG,KAAKsL,SAASP,QAAS8C,IACpB,GAAVrH,GACHxG,KAAKsL,SAASyC,OAAQvH,EAAO,EAEjC,EAEA1G,EAAOO,UAAUyN,OAAS,WACnB9N,KAAKU,OACRV,KAAKU,MAAMoJ,YAAa9J,KAE5B,EAIAF,EAAOO,UAAU2N,OAAS,WAExBhO,KAAK8D,QAEL9D,KAAKsL,SAASvH,QAAS,SAAUwH,GAC/BA,EAAMyC,QACR,GACAhO,KAAKkE,UAAWlE,KAAKW,UAAWX,KAAK+H,OAAQ/H,KAAKqE,MACpD,EAEAvE,EAAOO,UAAUyD,MAAQ,WACvB9D,KAAKiB,aAAaD,IAAKhB,KAAKqL,OAC9B,EAEAvL,EAAOO,UAAU6D,UAAY,SAAUC,EAAaC,EAAUC,GAC5DrE,KAAKiB,aAAaiD,UAAWC,EAAaC,EAAUC,GAEpDrE,KAAKsL,SAASvH,QAAS,SAAUwH,GAC/BA,EAAMrH,UAAWC,EAAaC,EAAUC,EAC1C,EACF,EAEAvE,EAAOO,UAAU4N,YAAc,WAC7BjO,KAAKgO,SACLhO,KAAKkO,kBACLlO,KAAKmO,UAAUpK,QAAS,SAAUqK,GAChCA,EAAKrN,iBACP,GAEAf,KAAKmO,UAAUE,KAAMvO,EAAOwO,YAC9B,EAEAxO,EAAOwO,YAAc,SAAUC,EAAGC,GAChC,OAAOD,EAAEpN,UAAYqN,EAAErN,SACzB,EAGAiH,OAAOC,eAAgBvI,EAAOO,UAAW,YAAa,CACpDiI,IAAK,WAIH,OAHMtI,KAAKyO,YACTzO,KAAKkO,kBAEAlO,KAAKyO,UACd,EACAzN,IAAK,SAAU0N,GACb1O,KAAKyO,WAAaC,CACpB,IAGF5O,EAAOO,UAAU6N,gBAAkB,WACjClO,KAAKmO,UAAYnO,KAAK2O,cACxB,EAGA7O,EAAOO,UAAUsO,aAAe,WAC9B,IAAIR,EAAY,CAAEnO,MAClB,OAAOA,KAAK4O,kBAAmBT,EACjC,EAEArO,EAAOO,UAAUuO,kBAAoB,SAAUT,GAK7C,OAJAnO,KAAKsL,SAASvH,QAAS,SAAUwH,GAC/B,IAAIsD,EAAiBtD,EAAMoD,eAC3BzD,MAAM7K,UAAU2M,KAAKzM,MAAO4N,EAAWU,EACzC,GACOV,CACT,EAEArO,EAAOO,UAAUU,gBAAkB,WACjCf,KAAKmB,UAAYnB,KAAKiB,aAAaL,CACrC,EAIAd,EAAOO,UAAUe,OAAS,WAAY,EAGtCtB,EAAOO,UAAUyO,kBAAoB,SAAUzN,GAC7C,IAAMA,EACJ,MAAM,IAAI0K,MAAO,UAAY1K,EAAZ,0EAGnBrB,KAAKmO,UAAUpK,QAAS,SAAUqK,GAChCA,EAAKhN,OAAQC,EAAK4L,EACpB,EACF,EAEAnN,EAAOO,UAAU0O,eAAiB,SAAUC,GAC1C,IAAMA,EACJ,MAAM,IAAIjD,MAAO,UAAYiD,EAAZ,4DAGnBhP,KAAKmO,UAAUpK,QAAS,SAAUqK,GAChCA,EAAKhN,OAAQ4N,EAAK9B,EACpB,EACF,EAIApN,EAAOO,UAAU2H,KAAO,SAAUoB,GAEhC,IAAI6F,EAAc,CAAC,EACfzB,EAAaxN,KAAKuN,YAAYC,WAClCA,EAAWzJ,QAAS,SAAU2J,GAC5BuB,EAAavB,GAAQ1N,KAAM0N,EAC7B,EAAG1N,MAEHC,EAAM4I,OAAQoG,EAAa7F,GAC3B,IAAI8F,EAAYlP,KAAKuN,YACrB,OAAO,IAAI2B,EAAWD,EACxB,EAEAnP,EAAOO,UAAUkH,UAAY,SAAU6B,GACrC,IAAI+F,EAAQnP,KAAKgI,KAAMoB,GAMvB,OALApJ,KAAKsL,SAASvH,QAAS,SAAUwH,GAC/BA,EAAMhE,UAAU,CACd7G,MAAOyO,GAEX,GACOA,CACT,EAEArP,EAAOO,UAAU+O,gBAAkB,WACjCpP,KAAK+H,OAAO/F,EAAI/B,EAAMoP,OAAQrP,KAAK+H,OAAO/F,EAAG5B,GAC7CJ,KAAK+H,OAAOhG,EAAI9B,EAAMoP,OAAQrP,KAAK+H,OAAOhG,EAAG3B,GAC7CJ,KAAK+H,OAAOnH,EAAIX,EAAMoP,OAAQrP,KAAK+H,OAAOnH,EAAGR,EAC/C,EA+BAN,EAAOI,SAAWkN,EAAatN,GAExBA,CAEP,E,yCCnPE,SAAUR,EAAMC,GAEkBC,EAAOC,QAEvCD,EAAOC,QAAUF,IAGjBD,EAAKK,KAAKuN,YAAc3N,GAE3B,CATC,CASCS,KAAM,WAET,IAAIkN,EAAc,CAAE1K,OAAO,GAGvB8M,EAAQpC,EAAYoC,MAAQ,SAAUC,GACxC,OAAO3N,KAAK0N,MAAa,IAANC,GAAe,GACpC,EAEA,SAASC,EAAgB3L,GACvB,OAAOyL,EAAOzL,EAAM7B,GAAM,IAAMsN,EAAOzL,EAAM9B,GAAM,GACrD,CAqDA,OAnDAmL,EAAYuC,MAAQ,WAAY,EAEhCvC,EAAY5I,KAAO,SAAU0K,EAAKvN,EAAMoC,GACtC,MAAO,IAAM2L,EAAgB3L,EAC/B,EAEAqJ,EAAY3I,KAAO,SAAUyK,EAAKvN,EAAMoC,GACtC,MAAO,IAAM2L,EAAgB3L,EAC/B,EAEAqJ,EAAY1I,OAAS,SAAUwK,EAAKvN,EAAMgD,EAAKC,EAAK5B,GAClD,MAAO,IAAM0M,EAAgB/K,GAAQ+K,EAAgB9K,GACnD8K,EAAgB1M,EACpB,EAEAoK,EAAYwC,UAAY,WACtB,MAAO,GACT,EAEAxC,EAAY5C,QAAU,SAAU0E,EAAKvN,EAAMkO,GACzClO,EAAKiB,aAAc,IAAKiN,EAC1B,EAEAzC,EAAY5G,WAAa,SAAU0I,EAAKvN,EAAMsF,EAAcwF,GAC1D,IAAIoD,EAAY,GAChB5I,EAAahD,QAAS,SAAUqH,GAC9BuE,GAAavE,EAAQhK,OAAQ4N,EAAKvN,EAAMyL,EAC1C,GACKX,IACHoD,GAAa3P,KAAK0P,UAAWV,EAAKvN,IAEpCzB,KAAKsK,QAAS0E,EAAKvN,EAAMkO,EAC3B,EAEAzC,EAAYvK,OAAS,SAAUqM,EAAKvN,EAAMmO,EAAUhN,EAAOsJ,GACnD0D,IAGNnO,EAAKiB,aAAc,SAAUE,GAC7BnB,EAAKiB,aAAc,eAAgBwJ,GACrC,EAEAgB,EAAY/M,KAAO,SAAU6O,EAAKvN,EAAMoO,EAAQjN,GAC9C,IAAIkN,EAAYD,EAASjN,EAAQ,OACjCnB,EAAKiB,aAAc,OAAQoN,EAC7B,EAEA5C,EAAYpK,IAAM,SAAUkM,EAAKvN,GAC/BuN,EAAIe,YAAatO,EACnB,EAEOyL,CAEP,E,yCC3EE,SAAU5N,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,YAC7B,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAKgH,MAAQpH,EAASI,EAAKG,OAC7B,CACD,CAVC,CAUCE,KAAM,SAAkBF,GAE3B,IAAI6G,EAAQ7G,EAAOI,SAAS,CAC1B4G,YAAY,EACZtF,SAAS,IA4CX,OAvCAmF,EAAMtG,UAAUU,gBAAkB,WAChC,IAAI8K,EAAiB,EACrB7L,KAAKmO,UAAUpK,QAAS,SAAUqK,GAChCA,EAAKrN,kBACL8K,GAAkBuC,EAAKjN,SACzB,GAGAnB,KAAKmB,UAAY0K,EAAiB7L,KAAKmO,UAAUxK,OAE5C3D,KAAK8G,YACR9G,KAAKmO,UAAUE,KAAMvO,EAAOwO,YAEhC,EAIA3H,EAAMtG,UAAUe,OAAS,SAAUC,EAAKC,GAChCtB,KAAKwB,SAIXxB,KAAKmO,UAAUpK,QAAS,SAAUqK,GAChCA,EAAKhN,OAAQC,EAAKC,EACpB,EACF,EAGAqF,EAAMtG,UAAU6N,gBAAkB,WAEhC,IAAIC,EAAY,GAChBnO,KAAKmO,UAAYnO,KAAK4O,kBAAmBT,EAC3C,EAGAxH,EAAMtG,UAAUsO,aAAe,WAC7B,MAAO,CAAE3O,KACX,EAEO2G,CAEP,E;;;;;;;;CCpDE,SAAUrH,EAAMC,GAEkBC,EAAOC,QAEvCD,EAAOC,QAAUF,IAGjBD,EAAKK,KAAOJ,GAEf,EATC,CASCS,KAAM,WAET,IAAIL,EAAO,CAAC,EAEZA,EAAKS,IAAgB,EAAVwB,KAAKoO,GAEhBrQ,EAAKkJ,OAAS,SAAU0F,EAAGC,GACzB,IAAM,IAAIyB,KAAQzB,EAChBD,EAAG0B,GAASzB,EAAGyB,GAEjB,OAAO1B,CACT,EAEA5O,EAAKuB,KAAO,SAAUqN,EAAGC,EAAG0B,GAC1B,OAAS1B,EAAID,GAAM2B,EAAQ3B,CAC7B,EAEA5O,EAAK0P,OAAS,SAAUE,EAAKY,GAC3B,OAAWZ,EAAMY,EAAQA,GAAQA,CACnC,EAEA,IAAIC,EAAmB,CACrB,EAAG,SAAU7B,GACX,OAAOA,EAAIA,CACb,EACA,EAAG,SAAUA,GACX,OAAOA,EAAIA,EAAIA,CACjB,EACA,EAAG,SAAUA,GACX,OAAOA,EAAIA,EAAIA,EAAIA,CACrB,EACA,EAAG,SAAUA,GACX,OAAOA,EAAIA,EAAIA,EAAIA,EAAIA,CACzB,GAkBF,OAfA5O,EAAK0Q,UAAY,SAAUH,EAAOI,GAChC,GAAc,GAATA,EACH,OAAOJ,EAETA,EAAQtO,KAAK2O,IAAK,EAAG3O,KAAKiL,IAAK,EAAGqD,IAClC,IAAIM,EAAcN,EAAQ,GACtBO,EAAQD,EAAcN,EAAQ,EAAIA,EACtCO,GAAS,GAET,IAAIC,EAAkBN,EAAkBE,IAAWF,EAAiB,GAChEO,EAAQD,EAAiBD,GAE7B,OADAE,GAAS,EACFH,EAAcG,EAAQ,EAAIA,CACnC,EAEOhR,CAEP,E,iCCtEE,SAAUL,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,OAAkBA,EAAQ,YACvD,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAKiR,QAAUrR,EAASI,EAAMA,EAAK+G,MACrC,CACD,CAVC,CAUC1G,KAAM,SAAkBC,EAAOyG,GAElC,IAAIkK,EAAUlK,EAAMxG,SAAS,CAC3B2Q,MAAO,EACPjL,OAAQ,KAGNxF,EAAMH,EAAMG,IAYhB,OAVAwQ,EAAQvQ,UAAUiK,QAAU,WAC1BtK,KAAK+I,KAAO,GACZ,IAAM,IAAI9E,EAAI,EAAGA,EAAIjE,KAAK6Q,MAAO5M,IAAM,CACrC,IAAI6M,EAAQ7M,EAAIjE,KAAK6Q,MAAQzQ,EAAMA,EAAI,EACnC4B,EAAIJ,KAAKqE,IAAK6K,GAAU9Q,KAAK4F,OAC7B7D,EAAIH,KAAK+D,IAAKmL,GAAU9Q,KAAK4F,OACjC5F,KAAK+I,KAAKiE,KAAK,CAAEhL,EAAGA,EAAGD,EAAGA,GAC5B,CACF,EAEO6O,CAEP,E,yBC/BE,SAAUtR,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,OAAkBA,EAAQ,OACxDA,EAAQ,YACP,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAKoR,aAAexR,EAASI,EAAMA,EAAKG,OAAQH,EAAKqR,QACvD,CACD,EAXC,CAWChR,KAAM,SAAkBC,EAAOH,EAAQkR,GAE1C,SAASpK,IAAQ,CACjB,IAAIxG,EAAMH,EAAMG,IAEZ2Q,EAAejR,EAAOI,SAAS,CACjC+Q,aAASvJ,EACTwJ,UAAU,EACVC,KAAM,EACNC,YAAY,EACZC,QAAQ,EACRC,YAAa1K,EACb2K,YAAa3K,EACb4K,WAAY5K,EACZ6K,UAAW7K,EACX8K,SAAU9K,IAkLZ,SAAS+K,EAAOV,GACd,MAAQA,EAAQW,WACdX,EAAQnH,YAAamH,EAAQW,WAEjC,CAiCA,OApNA3R,EAAM4I,OAAQkI,EAAa1Q,UAAW2Q,EAAQ3Q,WAE9C0Q,EAAa1Q,UAAUC,OAAS,SAAU8I,GACxCtJ,EAAOO,UAAUC,OAAO+I,KAAMrJ,KAAMoJ,GACpC4H,EAAQ3Q,UAAUC,OAAO+I,KAAMrJ,KAAMoJ,GACrCpJ,KAAK6R,WAAY7R,KAAKiR,SACtBjR,KAAK8R,cAAe9R,KAAKoR,YACzBpR,KAAK+R,UAAW/R,KAAKqR,OACvB,EAEAN,EAAa1Q,UAAUwR,WAAa,SAAUZ,GAE5C,GADAA,EAAUjR,KAAKgS,gBAAiBf,IAC1BA,EACJ,MAAM,IAAIlF,MAAO,8CAAgDkF,GAGnE,IAAIgB,EAAWhB,EAAQgB,SAASC,cACf,UAAZD,EACHjS,KAAKmS,UAAWlB,GACM,OAAZgB,GACVjS,KAAKoS,OAAQnB,EAEjB,EAEAF,EAAa1Q,UAAUgS,QAAU,SAAUpJ,EAAOC,GAChDD,EAAQrH,KAAK0N,MAAOrG,GACpBC,EAAStH,KAAK0N,MAAOpG,GAChBlJ,KAAKmC,SACRnC,KAAKsS,cAAerJ,EAAOC,GACjBlJ,KAAKwC,OACfxC,KAAKuS,WAAYtJ,EAAOC,EAE5B,EAEA6H,EAAa1Q,UAAU0R,UAAY,SAAUV,GAC3CrR,KAAKqR,OAASA,EAERrR,KAAKwS,iBACTxS,KAAKwS,eAAiBxS,KAAKyS,eAAeC,KAAM1S,OAG7CqR,GACHsB,OAAOC,iBAAkB,SAAU5S,KAAKwS,gBACxCxS,KAAKyS,kBAELE,OAAOE,oBAAqB,SAAU7S,KAAKwS,eAE/C,EAGAzB,EAAa1Q,UAAUoS,eAAiB,WACtCzS,KAAK8S,kBACL9S,KAAK0R,SAAU1R,KAAKiJ,MAAOjJ,KAAKkJ,OAClC,EAEA6H,EAAa1Q,UAAUyS,gBAAkB,WACvC,IAAI7J,EAAOC,EACP6J,EAA8B,cAAf/S,KAAKqR,OACxB,GAAK0B,EACH9J,EAAQ0J,OAAOK,WACf9J,EAASyJ,OAAOM,gBACX,CACL,IAAIvJ,EAAO1J,KAAKiR,QAAQiC,wBACxBjK,EAAQS,EAAKT,MACbC,EAASQ,EAAKR,MAChB,CACAlJ,KAAKqS,QAASpJ,EAAOC,EACvB,EAIA6H,EAAa1Q,UAAU8S,YAAc,SAAU/E,GACxCpO,KAAKmC,SACRnC,KAAK8O,kBAAmBV,GACdpO,KAAKwC,OACfxC,KAAK+O,eAAgBX,EAEzB,EAGA2C,EAAa1Q,UAAU+S,kBAAoB,SAAUhF,GACnDpO,KAAKiO,cACLjO,KAAKmT,YAAa/E,EACpB,EAIA2C,EAAa1Q,UAAU8R,UAAY,SAAUlB,GAC3CjR,KAAKiR,QAAUA,EACfjR,KAAKmC,UAAW,EAEhBnC,KAAKqB,IAAMrB,KAAKiR,QAAQoC,WAAW,MAEnCrT,KAAKsS,cAAerB,EAAQhI,MAAOgI,EAAQ/H,OAC7C,EAEA6H,EAAa1Q,UAAUiS,cAAgB,SAAUrJ,EAAOC,GACtDlJ,KAAKiJ,MAAQA,EACbjJ,KAAKkJ,OAASA,EAEd,IAAIoK,EAAatT,KAAKsT,WAAaX,OAAOY,kBAAoB,EAC9DvT,KAAKiR,QAAQhI,MAAQjJ,KAAKwT,YAAcvK,EAAQqK,EAChDtT,KAAKiR,QAAQ/H,OAASlJ,KAAKyT,aAAevK,EAASoK,EACnD,IAAII,EAA4BJ,EAAa,IAAMtT,KAAKqR,OACnDqC,IACH1T,KAAKiR,QAAQ0C,MAAM1K,MAAQA,EAAQ,KACnCjJ,KAAKiR,QAAQ0C,MAAMzK,OAASA,EAAS,KAEzC,EAEA6H,EAAa1Q,UAAUyO,kBAAoB,SAAUV,GACnDA,EAAOA,GAAQpO,KACfA,KAAK4T,kBACL9T,EAAOO,UAAUyO,kBAAkBzF,KAAM+E,EAAMpO,KAAKqB,KACpDrB,KAAK6T,kBACP,EAEA9C,EAAa1Q,UAAUuT,gBAAkB,WACvC,IAAIvS,EAAMrB,KAAKqB,IAKf,GAJAA,EAAIgG,QAAU,QACdhG,EAAIyS,SAAW,QACfzS,EAAI0S,UAAW,EAAG,EAAG/T,KAAKwT,YAAaxT,KAAKyT,cAC5CpS,EAAI2S,OACChU,KAAKkR,SAAW,CACnB,IAAI+C,EAAUjU,KAAKiJ,MAAQ,EAAIjJ,KAAKsT,WAChCY,EAAUlU,KAAKkJ,OAAS,EAAIlJ,KAAKsT,WACrCjS,EAAIV,UAAWsT,EAASC,EAC1B,CACA,IAAI7P,EAAQrE,KAAKsT,WAAatT,KAAKmR,KACnC9P,EAAIgD,MAAOA,EAAOA,GAClBrE,KAAKsR,YAAajQ,EACpB,EAEA0P,EAAa1Q,UAAUwT,iBAAmB,WACxC7T,KAAKqB,IAAI8S,SACX,EAIApD,EAAa1Q,UAAU+R,OAAS,SAAUnB,GACxCjR,KAAKiR,QAAUA,EACfjR,KAAKwC,OAAQ,EACbxC,KAAKsT,WAAa,EAElB,IAAIrK,EAAQgI,EAAQmD,aAAa,SAC7BlL,EAAS+H,EAAQmD,aAAa,UAClCpU,KAAKuS,WAAYtJ,EAAOC,EAC1B,EAEA6H,EAAa1Q,UAAUkS,WAAa,SAAUtJ,EAAOC,GACnDlJ,KAAKiJ,MAAQA,EACbjJ,KAAKkJ,OAASA,EACd,IAAImL,EAAYpL,EAAQjJ,KAAKmR,KACzBmD,EAAapL,EAASlJ,KAAKmR,KAC3BoD,EAAQvU,KAAKkR,UAAYmD,EAAU,EAAI,EACvCG,EAAQxU,KAAKkR,UAAYoD,EAAW,EAAI,EAC5CtU,KAAKiR,QAAQvO,aAAc,UAAW6R,EAAQ,IAAMC,EAAQ,IAC1DH,EAAY,IAAMC,GACftU,KAAKqR,QAERrR,KAAKiR,QAAQwD,gBAAgB,SAC7BzU,KAAKiR,QAAQwD,gBAAgB,YAE7BzU,KAAKiR,QAAQvO,aAAc,QAASuG,GACpCjJ,KAAKiR,QAAQvO,aAAc,SAAUwG,GAEzC,EAEA6H,EAAa1Q,UAAU0O,eAAiB,SAAUX,GAChDA,EAAOA,GAAQpO,KACf2R,EAAO3R,KAAKiR,SACZjR,KAAKsR,YAAatR,KAAKiR,SACvBnR,EAAOO,UAAU0O,eAAe1F,KAAM+E,EAAMpO,KAAKiR,QACnD,EAUAF,EAAa1Q,UAAUyR,cAAgB,SAAU1D,GACzCA,KAEe,IAATA,IAEVA,EAAOpO,MAETA,KAAKoR,WAAahD,EAElBpO,KAAK0U,SAAU1U,KAAKiR,SACtB,EAEAF,EAAa1Q,UAAUsU,UAAY,WACjC3U,KAAK4U,YAAc5U,KAAKoR,WAAWrJ,OAAO/F,EAC1ChC,KAAK6U,YAAc7U,KAAKoR,WAAWrJ,OAAOhG,EAC1CiP,EAAQ3Q,UAAUsU,UAAUpU,MAAOP,KAAMQ,UAC3C,EAEAuQ,EAAa1Q,UAAUyU,SAAW,SAAUC,EAAOC,GACjD,IAAIC,EAAQD,EAAQE,MAAQlV,KAAKmV,WAC7BC,EAAQJ,EAAQK,MAAQrV,KAAKsV,WAC7BC,EAAc3T,KAAKiL,IAAK7M,KAAKiJ,MAAOjJ,KAAKkJ,QACzCsM,EAASP,EAAMM,EAAcnV,EAC7BqV,EAASL,EAAMG,EAAcnV,EACjCJ,KAAKoR,WAAWrJ,OAAO/F,EAAIhC,KAAK4U,YAAca,EAC9CzV,KAAKoR,WAAWrJ,OAAOhG,EAAI/B,KAAK6U,YAAcW,EAC9CxE,EAAQ3Q,UAAUyU,SAASvU,MAAOP,KAAMQ,UAC1C,EAEOuQ,CAEP,E,qBCnPE,SAAUzR,EAAMC,GAEkBC,EAAOC,QAEvCD,EAAOC,QAAUF,IAGjBD,EAAKK,KAAKqR,QAAUzR,GAEvB,EATC,CASCS,KAAM,WAMT,IAAI0V,EAA6B,oBAAV/C,OAEnBgD,EAAY,YACZC,EAAY,YACZC,EAAU,UAed,SAASjP,IAAQ,CAEjB,SAASoK,EAAS5H,GAChBpJ,KAAKM,OAAQ8I,GAAW,CAAC,EAC3B,CAiFA,OAnGKsM,IACE/C,OAAOmD,cAEVH,EAAY,cACZC,EAAY,cACZC,EAAU,aACA,iBAAkBlD,SAE5BgD,EAAY,aACZC,EAAY,YACZC,EAAU,aAUd7E,EAAQ3Q,UAAUC,OAAS,SAAU8I,GACnCpJ,KAAKuR,YAAcnI,EAAQmI,aAAe3K,EAC1C5G,KAAKwR,WAAapI,EAAQoI,YAAc5K,EACxC5G,KAAKyR,UAAYrI,EAAQqI,WAAa7K,EAEtC5G,KAAK0U,SAAUtL,EAAQ2M,aACzB,EAEA/E,EAAQ3Q,UAAUqU,SAAW,SAAUzD,GACrCA,EAAUjR,KAAKgS,gBAAiBf,GAC1BA,IAINA,EAAQ0C,MAAMqC,YAAc,OAC5B/E,EAAQ2B,iBAAkB+C,EAAW3V,MACvC,EAEAgR,EAAQ3Q,UAAU2R,gBAAkB,SAAUf,GAK5C,MAJuB,iBAAXA,IAEVA,EAAUjO,SAASiT,cAAehF,IAE7BA,CACT,EAEAD,EAAQ3Q,UAAU6V,YAAc,SAAUnB,GACxC,IAAI5R,EAASnD,KAAM,KAAO+U,EAAMoB,MAC3BhT,GACHA,EAAOkG,KAAMrJ,KAAM+U,EAEvB,EAEA/D,EAAQ3Q,UAAU+V,YAClBpF,EAAQ3Q,UAAUgW,cAAgB,SAAUtB,GAC1C/U,KAAK2U,UAAWI,EAAOA,EACzB,EAEA/D,EAAQ3Q,UAAUiW,aAAe,SAAUvB,GACzC/U,KAAK2U,UAAWI,EAAOA,EAAMwB,eAAe,GAC9C,EAEAvF,EAAQ3Q,UAAUsU,UAAY,SAAUI,EAAOC,GAC7CD,EAAMyB,iBACNxW,KAAKmV,WAAaH,EAAQE,MAC1BlV,KAAKsV,WAAaN,EAAQK,MACrBK,IACH/C,OAAOC,iBAAkBgD,EAAW5V,MACpC2S,OAAOC,iBAAkBiD,EAAS7V,OAEpCA,KAAKuR,YAAayD,EACpB,EAEAhE,EAAQ3Q,UAAUoW,YAAc,SAAU1B,GAExC/U,KAAK8U,SAAUC,EAAOA,EAAMwB,eAAe,GAC7C,EAEAvF,EAAQ3Q,UAAUqW,YAClB1F,EAAQ3Q,UAAUsW,cAAgB,SAAU5B,GAC1C/U,KAAK8U,SAAUC,EAAOA,EACxB,EAEA/D,EAAQ3Q,UAAUyU,SAAW,SAAUC,EAAOC,GAC5CD,EAAMyB,iBACN,IAAIvB,EAAQD,EAAQE,MAAQlV,KAAKmV,WAC7BC,EAAQJ,EAAQK,MAAQrV,KAAKsV,WACjCtV,KAAKwR,WAAYwD,EAASC,EAAOG,EACnC,EAEApE,EAAQ3Q,UAAUuW,UAClB5F,EAAQ3Q,UAAUwW,YAClB7F,EAAQ3Q,UAAUyW,WAClB9F,EAAQ3Q,UAAU0W,QAAU,WAC1BpE,OAAOE,oBAAqB+C,EAAW5V,MACvC2S,OAAOE,oBAAqBgD,EAAS7V,MACrCA,KAAKyR,WACP,EAEOT,CAEP,E,yCCzHE,SAAU1R,EAAMC,GAEkBC,EAAOC,QAEvCD,EAAOC,QAAUF,IAGjBD,EAAKK,KAAKsN,eAAiB1N,GAE9B,CATC,CASCS,KAAM,WAET,IAAIiN,EAAiB,CAAE9K,UAAU,EAEjC8K,MAAuB,SAAU5L,GAC/BA,EAAIiB,WACN,EAEA2K,KAAsB,SAAU5L,EAAKI,EAAMoC,GACzCxC,EAAI2V,OAAQnT,EAAM7B,EAAG6B,EAAM9B,EAC7B,EAEAkL,KAAsB,SAAU5L,EAAKI,EAAMoC,GACzCxC,EAAI4V,OAAQpT,EAAM7B,EAAG6B,EAAM9B,EAC7B,EAEAkL,OAAwB,SAAU5L,EAAKI,EAAMgD,EAAKC,EAAK5B,GACrDzB,EAAI6V,cAAezS,EAAIzC,EAAGyC,EAAI1C,EAAG2C,EAAI1C,EAAG0C,EAAI3C,EAAGe,EAAId,EAAGc,EAAIf,EAC5D,EAEAkL,UAA2B,SAAU5L,GACnCA,EAAIqO,WACN,EAEAzC,QAAyB,WAAY,EAErCA,WAA4B,SAAU5L,EAAKI,EAAMsF,EAAcwF,GAC7DvM,KAAKyP,MAAOpO,EAAKI,GACjBsF,EAAahD,QAAS,SAAUqH,GAC9BA,EAAQhK,OAAQC,EAAKI,EAAMwL,EAC7B,GACKV,GACHvM,KAAK0P,UAAWrO,EAAKI,EAEzB,EAEAwL,OAAwB,SAAU5L,EAAKI,EAAMmO,EAAUhN,EAAOsJ,GACtD0D,IAGNvO,EAAI8V,YAAcvU,EAClBvB,EAAI6K,UAAYA,EAChB7K,EAAIsB,SACN,EAEAsK,KAAsB,SAAU5L,EAAKI,EAAMoO,EAAQjN,GAC3CiN,IAGNxO,EAAI8K,UAAYvJ,EAChBvB,EAAIlB,OACN,EAEA8M,IAAqB,WAAY,GAEjC,OAAOA,CAEP,E,wBCtEA,WAIA,SAAY3N,EAAMC,GAEkBC,EAAOC,QAEvCD,EAAOC,QAAUF,EACbG,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,MACRA,EAAQ,OACRA,EAAQ,OACRA,EAAQ,SAIZ0X,EAAgB,GAAI,EAAA9X,EAAU,sEAGjC,EA7BD,CA6BKU,KAAM,SAAkBL,EAAMsN,EAAgBC,EAAarN,EAAQC,EACpEkR,EAASD,EAAc7N,EAAawD,EAAOC,EAAO8B,EAAM+D,EACxDzM,EAAS6Q,EAAShR,EAAY6G,EAAU3B,EAAM0D,GAqB5C,OAlBA7I,EAAKsN,eAAiBA,EACtBtN,EAAKuN,YAAcA,EACnBvN,EAAKE,OAASA,EACdF,EAAKG,OAASA,EACdH,EAAKqR,QAAUA,EACfrR,EAAKoR,aAAeA,EACpBpR,EAAKuD,YAAcA,EACnBvD,EAAK+G,MAAQA,EACb/G,EAAKgH,MAAQA,EACbhH,EAAK8I,KAAOA,EACZ9I,EAAK6M,YAAcA,EACnB7M,EAAKI,QAAUA,EACfJ,EAAKiR,QAAUA,EACfjR,EAAKC,WAAaA,EAClBD,EAAK8G,SAAWA,EAChB9G,EAAKmF,KAAOA,EACZnF,EAAK6I,IAAMA,EAEJ7I,CACb,E,iCCrDE,SAAUL,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,YAC7B,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAKI,QAAUR,EAASI,EAAK+G,MAC/B,CAED,CAXC,CAWC1G,KAAM,SAAkB0G,GAE3B,IAAI3G,EAAU2G,EAAMxG,SAAS,CAC3BW,SAAU,EACVoI,WAAOvB,EACPwB,YAAQxB,EACR2P,SAAU,EACV9M,QAAQ,IAsCV,OAnCAxK,EAAQM,UAAUiK,QAAU,WAC1B,IAAIrB,OAAsBvB,GAAd1H,KAAKiJ,MAAqBjJ,KAAKiJ,MAAQjJ,KAAKa,SACpDqI,OAAwBxB,GAAf1H,KAAKkJ,OAAsBlJ,KAAKkJ,OAASlJ,KAAKa,SACvDmB,EAAIiH,EAAM,EACVlH,EAAImH,EAAO,EACflJ,KAAK+I,KAAO,CACV,CAAE/G,EAAG,EAAGD,GAAIA,GACZ,CAAEQ,IAAK,CACL,CAAEP,EAAGA,EAAGD,GAAIA,GACZ,CAAEC,EAAGA,EAAGD,EAAG,MAIV/B,KAAKqX,SAAW,GACnBrX,KAAK+I,KAAKiE,KAAK,CAAEzK,IAAK,CACpB,CAAEP,EAAGA,EAAGD,EAAGA,GACX,CAAEC,EAAG,EAAGD,EAAGA,MAIV/B,KAAKqX,SAAW,GACnBrX,KAAK+I,KAAKiE,KAAK,CAAEzK,IAAK,CACpB,CAAEP,GAAIA,EAAGD,EAAGA,GACZ,CAAEC,GAAIA,EAAGD,EAAG,MAIX/B,KAAKqX,SAAW,GACnBrX,KAAK+I,KAAKiE,KAAK,CAAEzK,IAAK,CACpB,CAAEP,GAAIA,EAAGD,GAAIA,GACb,CAAEC,EAAG,EAAGD,GAAIA,KAGlB,EAEOhC,CAEP,E,yBC1DE,SAAUT,EAAMC,GAEhB,GAAkCC,EAAOC,QAEvCD,EAAOC,QAAUF,EAASG,EAAQ,YAC7B,CAEL,IAAIC,EAAOL,EAAKK,KAChBA,EAAKE,OAASN,EAASI,EACzB,CAED,EAXC,CAWCK,KAAM,SAAkBC,GAE3B,SAASJ,EAAQyX,GACftX,KAAKgB,IAAKsW,EACZ,CAEA,IAAIlX,EAAMH,EAAMG,IA4ChB,SAASmX,EAAgBC,EAAKC,EAAOC,EAAOC,GAC1C,GAAMF,GAASA,EAAQrX,IAAQ,EAA/B,CAGA,IAAI6F,EAAMrE,KAAKqE,IAAKwR,GAChB9R,EAAM/D,KAAK+D,IAAK8R,GAChBlJ,EAAIiJ,EAAKE,GACTlJ,EAAIgJ,EAAKG,GACbH,EAAKE,GAAUnJ,EAAItI,EAAMuI,EAAI7I,EAC7B6R,EAAKG,GAAUnJ,EAAIvI,EAAMsI,EAAI5I,CAN7B,CAOF,CAkEA,SAASiS,EAAkBC,GAEzB,OAAKjW,KAAKkW,IAAKD,EAAM,GAAM,KAClB,EAEFjW,KAAKmW,KAAMF,EACpB,CAWA,OAtIAhY,EAAOQ,UAAUW,IAAM,SAAUgX,GAI/B,OAHAhY,KAAKgC,EAAIgW,GAAOA,EAAIhW,GAAK,EACzBhC,KAAK+B,EAAIiW,GAAOA,EAAIjW,GAAK,EACzB/B,KAAKY,EAAIoX,GAAOA,EAAIpX,GAAK,EAClBZ,IACT,EAIAH,EAAOQ,UAAU4X,MAAQ,SAAUD,GACjC,OAAMA,GAGNhY,KAAKgC,OAAa0F,GAATsQ,EAAIhW,EAAiBgW,EAAIhW,EAAIhC,KAAKgC,EAC3ChC,KAAK+B,OAAa2F,GAATsQ,EAAIjW,EAAiBiW,EAAIjW,EAAI/B,KAAK+B,EAC3C/B,KAAKY,OAAa8G,GAATsQ,EAAIpX,EAAiBoX,EAAIpX,EAAIZ,KAAKY,EACpCZ,MALEA,IAMX,EAEAH,EAAOQ,UAAU0H,OAAS,SAAU3D,GAClC,GAAMA,EAMN,OAHApE,KAAKkG,QAAS9B,EAASxD,GACvBZ,KAAKkY,QAAS9T,EAASrC,GACvB/B,KAAKmY,QAAS/T,EAASpC,GAChBhC,IACT,EAEAH,EAAOQ,UAAU6F,QAAU,SAAUuR,GACnCF,EAAgBvX,KAAMyX,EAAO,IAAK,IACpC,EAEA5X,EAAOQ,UAAU8X,QAAU,SAAUV,GACnCF,EAAgBvX,KAAMyX,EAAO,IAAK,IACpC,EAEA5X,EAAOQ,UAAU6X,QAAU,SAAUT,GACnCF,EAAgBvX,KAAMyX,EAAO,IAAK,IACpC,EAcA5X,EAAOQ,UAAUuL,OAAS,SAAUoM,GAClC,QAAMA,IAGChY,KAAKgC,IAAMgW,EAAIhW,GAAKhC,KAAK+B,IAAMiW,EAAIjW,GAAK/B,KAAKY,IAAMoX,EAAIpX,EAChE,EAEAf,EAAOQ,UAAU8F,IAAM,SAAU6R,GAC/B,OAAMA,GAGNhY,KAAKgC,GAAKgW,EAAIhW,GAAK,EACnBhC,KAAK+B,GAAKiW,EAAIjW,GAAK,EACnB/B,KAAKY,GAAKoX,EAAIpX,GAAK,EACZZ,MALEA,IAMX,EAEAH,EAAOQ,UAAU+E,SAAW,SAAU4S,GACpC,OAAMA,GAGNhY,KAAKgC,GAAKgW,EAAIhW,GAAK,EACnBhC,KAAK+B,GAAKiW,EAAIjW,GAAK,EACnB/B,KAAKY,GAAKoX,EAAIpX,GAAK,EACZZ,MALEA,IAMX,EAEAH,EAAOQ,UAAUsN,SAAW,SAAUqK,GACpC,YAAYtQ,GAAPsQ,IAIc,iBAAPA,GACVhY,KAAKgC,GAAKgW,EACVhY,KAAK+B,GAAKiW,EACVhY,KAAKY,GAAKoX,IAGVhY,KAAKgC,QAAc0F,GAATsQ,EAAIhW,EAAiBgW,EAAIhW,EAAI,EACvChC,KAAK+B,QAAc2F,GAATsQ,EAAIjW,EAAiBiW,EAAIjW,EAAI,EACvC/B,KAAKY,QAAc8G,GAATsQ,EAAIpX,EAAiBoX,EAAIpX,EAAI,IAXhCZ,IAcX,EAEAH,EAAOQ,UAAU6D,UAAY,SAAUC,EAAaC,EAAUC,GAI5D,OAHArE,KAAK2N,SAAUtJ,GACfrE,KAAK+H,OAAQ3D,GACbpE,KAAKmG,IAAKhC,GACHnE,IACT,EAEAH,EAAOQ,UAAUa,KAAO,SAAU8W,EAAK9H,GAIrC,OAHAlQ,KAAKgC,EAAI/B,EAAMiB,KAAMlB,KAAKgC,EAAGgW,EAAIhW,GAAK,EAAGkO,GACzClQ,KAAK+B,EAAI9B,EAAMiB,KAAMlB,KAAK+B,EAAGiW,EAAIjW,GAAK,EAAGmO,GACzClQ,KAAKY,EAAIX,EAAMiB,KAAMlB,KAAKY,EAAGoX,EAAIpX,GAAK,EAAGsP,GAClClQ,IACT,EAEAH,EAAOQ,UAAU6B,UAAY,WAC3B,IAAI2V,EAAM7X,KAAKgC,EAAIhC,KAAKgC,EAAIhC,KAAK+B,EAAI/B,KAAK+B,EAAI/B,KAAKY,EAAIZ,KAAKY,EAC5D,OAAOgX,EAAkBC,EAC3B,EAUAhY,EAAOQ,UAAUiF,YAAc,WAC7B,IAAIuS,EAAM7X,KAAKgC,EAAIhC,KAAKgC,EAAIhC,KAAK+B,EAAI/B,KAAK+B,EAC1C,OAAO6V,EAAkBC,EAC3B,EAEAhY,EAAOQ,UAAU2H,KAAO,WACtB,OAAO,IAAInI,EAAQG,KACrB,EAEOH,CAEP,E", "sources": ["webpack://frontend-web/./node_modules/zdog/js/hemisphere.js", "webpack://frontend-web/./node_modules/zdog/js/path-command.js", "webpack://frontend-web/./node_modules/zdog/js/cone.js", "webpack://frontend-web/./node_modules/zdog/js/cylinder.js", "webpack://frontend-web/./node_modules/zdog/js/box.js", "webpack://frontend-web/./node_modules/zdog/js/rect.js", "webpack://frontend-web/./node_modules/zdog/js/shape.js", "webpack://frontend-web/./node_modules/zdog/js/rounded-rect.js", "webpack://frontend-web/./node_modules/zdog/js/anchor.js", "webpack://frontend-web/./node_modules/zdog/js/svg-renderer.js", "webpack://frontend-web/./node_modules/zdog/js/group.js", "webpack://frontend-web/./node_modules/zdog/js/boilerplate.js", "webpack://frontend-web/./node_modules/zdog/js/polygon.js", "webpack://frontend-web/./node_modules/zdog/js/illustration.js", "webpack://frontend-web/./node_modules/zdog/js/dragger.js", "webpack://frontend-web/./node_modules/zdog/js/canvas-renderer.js", "webpack://frontend-web/./node_modules/zdog/js/index.js", "webpack://frontend-web/./node_modules/zdog/js/ellipse.js", "webpack://frontend-web/./node_modules/zdog/js/vector.js"], "sourcesContent": ["/**\n * Hemisphere composite shape\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./boilerplate'), require('./vector'),\n        require('./anchor'), require('./ellipse') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Hemisphere = factory( Zdog, Zdog.Vector, Zdog.Anchor, Zdog.Ellipse );\n  }\n}( this, function factory( utils, Vector, Anchor, Ellipse ) {\n\nvar Hemisphere = Ellipse.subclass({\n  fill: true,\n});\n\nvar TAU = utils.TAU;\n\nHemisphere.prototype.create = function( /* options */) {\n  // call super\n  Ellipse.prototype.create.apply( this, arguments );\n  // composite shape, create child shapes\n  this.apex = new Anchor({\n    addTo: this,\n    translate: { z: this.diameter / 2 },\n  });\n  // vector used for calculation\n  this.renderCentroid = new Vector();\n};\n\nHemisphere.prototype.updateSortValue = function() {\n  // centroid of hemisphere is 3/8 between origin and apex\n  this.renderCentroid.set( this.renderOrigin )\n    .lerp( this.apex.renderOrigin, 3/8 );\n  this.sortValue = this.renderCentroid.z;\n};\n\nHemisphere.prototype.render = function( ctx, renderer ) {\n  this.renderDome( ctx, renderer );\n  // call super\n  Ellipse.prototype.render.apply( this, arguments );\n};\n\nHemisphere.prototype.renderDome = function( ctx, renderer ) {\n  if ( !this.visible ) {\n    return;\n  }\n  var elem = this.getDomeRenderElement( ctx, renderer );\n  var contourAngle = Math.atan2( this.renderNormal.y, this.renderNormal.x );\n  var domeRadius = this.diameter / 2 * this.renderNormal.magnitude();\n  var x = this.renderOrigin.x;\n  var y = this.renderOrigin.y;\n\n  if ( renderer.isCanvas ) {\n    // canvas\n    var startAngle = contourAngle + TAU/4;\n    var endAngle = contourAngle - TAU/4;\n    ctx.beginPath();\n    ctx.arc( x, y, domeRadius, startAngle, endAngle );\n  } else if ( renderer.isSvg ) {\n    // svg\n    contourAngle = ( contourAngle - TAU/4 ) / TAU * 360;\n    this.domeSvgElement.setAttribute( 'd', 'M ' + -domeRadius + ',0 A ' +\n        domeRadius + ',' + domeRadius + ' 0 0 1 ' + domeRadius + ',0' );\n    this.domeSvgElement.setAttribute( 'transform',\n        'translate(' + x + ',' + y + ' ) rotate(' + contourAngle + ')' );\n  }\n\n  renderer.stroke( ctx, elem, this.stroke, this.color, this.getLineWidth() );\n  renderer.fill( ctx, elem, this.fill, this.color );\n  renderer.end( ctx, elem );\n};\n\nvar svgURI = 'http://www.w3.org/2000/svg';\n\nHemisphere.prototype.getDomeRenderElement = function( ctx, renderer ) {\n  if ( !renderer.isSvg ) {\n    return;\n  }\n  if ( !this.domeSvgElement ) {\n    // create svgElement\n    this.domeSvgElement = document.createElementNS( svgURI, 'path' );\n    this.domeSvgElement.setAttribute( 'stroke-linecap', 'round' );\n    this.domeSvgElement.setAttribute( 'stroke-linejoin', 'round' );\n  }\n  return this.domeSvgElement;\n};\n\nreturn Hemisphere;\n\n} ) );\n", "/**\n * PathCommand\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./vector') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.PathCommand = factory( Zdog.Vector );\n  }\n}( this, function factory( Vector ) {\n\nfunction PathCommand( method, points, previousPoint ) {\n  this.method = method;\n  this.points = points.map( mapVectorPoint );\n  this.renderPoints = points.map( mapNewVector );\n  this.previousPoint = previousPoint;\n  this.endRenderPoint = this.renderPoints[ this.renderPoints.length - 1 ];\n  // arc actions come with previous point & corner point\n  // but require bezier control points\n  if ( method == 'arc' ) {\n    this.controlPoints = [ new Vector(), new Vector() ];\n  }\n}\n\nfunction mapVectorPoint( point ) {\n  if ( point instanceof Vector ) {\n    return point;\n  } else {\n    return new Vector( point );\n  }\n}\n\nfunction mapNewVector( point ) {\n  return new Vector( point );\n}\n\nPathCommand.prototype.reset = function() {\n  // reset renderPoints back to orignal points position\n  var points = this.points;\n  this.renderPoints.forEach( function( renderPoint, i ) {\n    var point = points[i];\n    renderPoint.set( point );\n  } );\n};\n\nPathCommand.prototype.transform = function( translation, rotation, scale ) {\n  this.renderPoints.forEach( function( renderPoint ) {\n    renderPoint.transform( translation, rotation, scale );\n  } );\n};\n\nPathCommand.prototype.render = function( ctx, elem, renderer ) {\n  return this[ this.method ]( ctx, elem, renderer );\n};\n\nPathCommand.prototype.move = function( ctx, elem, renderer ) {\n  return renderer.move( ctx, elem, this.renderPoints[0] );\n};\n\nPathCommand.prototype.line = function( ctx, elem, renderer ) {\n  return renderer.line( ctx, elem, this.renderPoints[0] );\n};\n\nPathCommand.prototype.bezier = function( ctx, elem, renderer ) {\n  var cp0 = this.renderPoints[0];\n  var cp1 = this.renderPoints[1];\n  var end = this.renderPoints[2];\n  return renderer.bezier( ctx, elem, cp0, cp1, end );\n};\n\nvar arcHandleLength = 9/16;\n\nPathCommand.prototype.arc = function( ctx, elem, renderer ) {\n  var prev = this.previousPoint;\n  var corner = this.renderPoints[0];\n  var end = this.renderPoints[1];\n  var cp0 = this.controlPoints[0];\n  var cp1 = this.controlPoints[1];\n  cp0.set( prev ).lerp( corner, arcHandleLength );\n  cp1.set( end ).lerp( corner, arcHandleLength );\n  return renderer.bezier( ctx, elem, cp0, cp1, end );\n};\n\nreturn PathCommand;\n\n} ) );\n", "/**\n * Cone composite shape\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./boilerplate'), require('./vector'),\n        require('./path-command'), require('./anchor'), require('./ellipse') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Cone = factory( Zdog, Zdog.Vector, Zdog.PathCommand,\n        Zdog.Anchor, Zdog.Ellipse );\n  }\n}( this, function factory( utils, Vector, PathCommand, Anchor, Ellipse ) {\n\nvar Cone = Ellipse.subclass({\n  length: 1,\n  fill: true,\n});\n\nvar TAU = utils.TAU;\n\nCone.prototype.create = function( /* options */) {\n  // call super\n  Ellipse.prototype.create.apply( this, arguments );\n  // composite shape, create child shapes\n  this.apex = new Anchor({\n    addTo: this,\n    translate: { z: this.length },\n  });\n\n  // vectors used for calculation\n  this.renderApex = new Vector();\n  this.renderCentroid = new Vector();\n  this.tangentA = new Vector();\n  this.tangentB = new Vector();\n\n  this.surfacePathCommands = [\n    new PathCommand( 'move', [ {} ] ), // points set in renderConeSurface\n    new PathCommand( 'line', [ {} ] ),\n    new PathCommand( 'line', [ {} ] ),\n  ];\n};\n\nCone.prototype.updateSortValue = function() {\n  // center of cone is one third of its length\n  this.renderCentroid.set( this.renderOrigin )\n    .lerp( this.apex.renderOrigin, 1/3 );\n  this.sortValue = this.renderCentroid.z;\n};\n\nCone.prototype.render = function( ctx, renderer ) {\n  this.renderConeSurface( ctx, renderer );\n  Ellipse.prototype.render.apply( this, arguments );\n};\n\nCone.prototype.renderConeSurface = function( ctx, renderer ) {\n  if ( !this.visible ) {\n    return;\n  }\n  this.renderApex.set( this.apex.renderOrigin )\n    .subtract( this.renderOrigin );\n\n  var scale = this.renderNormal.magnitude();\n  var apexDistance = this.renderApex.magnitude2d();\n  var normalDistance = this.renderNormal.magnitude2d();\n  // eccentricity\n  var eccenAngle = Math.acos( normalDistance/scale );\n  var eccen = Math.sin( eccenAngle );\n  var radius = this.diameter / 2 * scale;\n  // does apex extend beyond eclipse of face\n  var isApexVisible = radius * eccen < apexDistance;\n  if ( !isApexVisible ) {\n    return;\n  }\n  // update tangents\n  var apexAngle = Math.atan2( this.renderNormal.y, this.renderNormal.x ) +\n      TAU/2;\n  var projectLength = apexDistance/eccen;\n  var projectAngle = Math.acos( radius/projectLength );\n  // set tangent points\n  var tangentA = this.tangentA;\n  var tangentB = this.tangentB;\n\n  tangentA.x = Math.cos( projectAngle ) * radius * eccen;\n  tangentA.y = Math.sin( projectAngle ) * radius;\n\n  tangentB.set( this.tangentA );\n  tangentB.y *= -1;\n\n  tangentA.rotateZ( apexAngle );\n  tangentB.rotateZ( apexAngle );\n  tangentA.add( this.renderOrigin );\n  tangentB.add( this.renderOrigin );\n\n  this.setSurfaceRenderPoint( 0, tangentA );\n  this.setSurfaceRenderPoint( 1, this.apex.renderOrigin );\n  this.setSurfaceRenderPoint( 2, tangentB );\n\n  // render\n  var elem = this.getSurfaceRenderElement( ctx, renderer );\n  renderer.renderPath( ctx, elem, this.surfacePathCommands );\n  renderer.stroke( ctx, elem, this.stroke, this.color, this.getLineWidth() );\n  renderer.fill( ctx, elem, this.fill, this.color );\n  renderer.end( ctx, elem );\n};\n\nvar svgURI = 'http://www.w3.org/2000/svg';\n\nCone.prototype.getSurfaceRenderElement = function( ctx, renderer ) {\n  if ( !renderer.isSvg ) {\n    return;\n  }\n  if ( !this.surfaceSvgElement ) {\n    // create svgElement\n    this.surfaceSvgElement = document.createElementNS( svgURI, 'path' );\n    this.surfaceSvgElement.setAttribute( 'stroke-linecap', 'round' );\n    this.surfaceSvgElement.setAttribute( 'stroke-linejoin', 'round' );\n  }\n  return this.surfaceSvgElement;\n};\n\nCone.prototype.setSurfaceRenderPoint = function( index, point ) {\n  var renderPoint = this.surfacePathCommands[ index ].renderPoints[0];\n  renderPoint.set( point );\n};\n\nreturn Cone;\n\n} ) );\n", "/**\n * Cylinder composite shape\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./boilerplate'),\n        require('./path-command'), require('./shape'), require('./group'),\n        require('./ellipse') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Cylinder = factory( Zdog, Zdog.PathCommand, Zdog.Shape,\n        Zdog.Group, Zdog.Ellipse );\n  }\n}( this, function factory( utils, PathCommand, Shape, Group, Ellipse ) {\n\nfunction noop() {}\n\n// ----- CylinderGroup ----- //\n\nvar CylinderGroup = Group.subclass({\n  color: '#333',\n  updateSort: true,\n});\n\nCylinderGroup.prototype.create = function() {\n  Group.prototype.create.apply( this, arguments );\n  this.pathCommands = [\n    new PathCommand( 'move', [ {} ] ),\n    new PathCommand( 'line', [ {} ] ),\n  ];\n};\n\nCylinderGroup.prototype.render = function( ctx, renderer ) {\n  this.renderCylinderSurface( ctx, renderer );\n  Group.prototype.render.apply( this, arguments );\n};\n\nCylinderGroup.prototype.renderCylinderSurface = function( ctx, renderer ) {\n  if ( !this.visible ) {\n    return;\n  }\n  // render cylinder surface\n  var elem = this.getRenderElement( ctx, renderer );\n  var frontBase = this.frontBase;\n  var rearBase = this.rearBase;\n  var scale = frontBase.renderNormal.magnitude();\n  var strokeWidth = frontBase.diameter * scale + frontBase.getLineWidth();\n  // set path command render points\n  this.pathCommands[0].renderPoints[0].set( frontBase.renderOrigin );\n  this.pathCommands[1].renderPoints[0].set( rearBase.renderOrigin );\n\n  if ( renderer.isCanvas ) {\n    ctx.lineCap = 'butt'; // nice\n  }\n  renderer.renderPath( ctx, elem, this.pathCommands );\n  renderer.stroke( ctx, elem, true, this.color, strokeWidth );\n  renderer.end( ctx, elem );\n\n  if ( renderer.isCanvas ) {\n    ctx.lineCap = 'round'; // reset\n  }\n};\n\nvar svgURI = 'http://www.w3.org/2000/svg';\n\nCylinderGroup.prototype.getRenderElement = function( ctx, renderer ) {\n  if ( !renderer.isSvg ) {\n    return;\n  }\n  if ( !this.svgElement ) {\n    // create svgElement\n    this.svgElement = document.createElementNS( svgURI, 'path' );\n  }\n  return this.svgElement;\n};\n\n// prevent double-creation in parent.copyGraph()\n// only create in Cylinder.create()\nCylinderGroup.prototype.copyGraph = noop;\n\n// ----- CylinderEllipse ----- //\n\nvar CylinderEllipse = Ellipse.subclass();\n\nCylinderEllipse.prototype.copyGraph = noop;\n\n// ----- Cylinder ----- //\n\nvar Cylinder = Shape.subclass({\n  diameter: 1,\n  length: 1,\n  frontFace: undefined,\n  fill: true,\n});\n\nvar TAU = utils.TAU;\n\nCylinder.prototype.create = function( /* options */) {\n  // call super\n  Shape.prototype.create.apply( this, arguments );\n  // composite shape, create child shapes\n  // CylinderGroup to render cylinder surface then bases\n  this.group = new CylinderGroup({\n    addTo: this,\n    color: this.color,\n    visible: this.visible,\n  });\n  var baseZ = this.length / 2;\n  var baseColor = this.backface || true;\n  // front outside base\n  this.frontBase = this.group.frontBase = new Ellipse({\n    addTo: this.group,\n    diameter: this.diameter,\n    translate: { z: baseZ },\n    rotate: { y: TAU/2 },\n    color: this.color,\n    stroke: this.stroke,\n    fill: this.fill,\n    backface: this.frontFace || baseColor,\n    visible: this.visible,\n  });\n  // back outside base\n  this.rearBase = this.group.rearBase = this.frontBase.copy({\n    translate: { z: -baseZ },\n    rotate: { y: 0 },\n    backface: baseColor,\n  });\n};\n\n// Cylinder shape does not render anything\nCylinder.prototype.render = function() {};\n\n// ----- set child properties ----- //\n\nvar childProperties = [ 'stroke', 'fill', 'color', 'visible' ];\nchildProperties.forEach( function( property ) {\n  // use proxy property for custom getter & setter\n  var _prop = '_' + property;\n  Object.defineProperty( Cylinder.prototype, property, {\n    get: function() {\n      return this[ _prop ];\n    },\n    set: function( value ) {\n      this[ _prop ] = value;\n      // set property on children\n      if ( this.frontBase ) {\n        this.frontBase[ property ] = value;\n        this.rearBase[ property ] = value;\n        this.group[ property ] = value;\n      }\n    },\n  } );\n} );\n\n// TODO child property setter for backface, frontBaseColor, & rearBaseColor\n\nreturn Cylinder;\n\n} ) );\n", "/**\n * Box composite shape\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./boilerplate'), require('./anchor'),\n        require('./shape'), require('./rect') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Box = factory( Zdog, Zdog.Anchor, Zdog.Shape, Zdog.Rect );\n  }\n}( this, function factory( utils, Anchor, Shape, Rect ) {\n\n// ----- BoxRect ----- //\n\nvar BoxRect = Rect.subclass();\n// prevent double-creation in parent.copyGraph()\n// only create in Box.create()\nBoxRect.prototype.copyGraph = function() {};\n\n// ----- Box ----- //\n\nvar TAU = utils.TAU;\nvar faceNames = [\n  'frontFace',\n  'rearFace',\n  'leftFace',\n  'rightFace',\n  'topFace',\n  'bottomFace',\n];\n\nvar boxDefaults = utils.extend( {}, Shape.defaults );\ndelete boxDefaults.path;\nfaceNames.forEach( function( faceName ) {\n  boxDefaults[ faceName ] = true;\n} );\nutils.extend( boxDefaults, {\n  width: 1,\n  height: 1,\n  depth: 1,\n  fill: true,\n} );\n\nvar Box = Anchor.subclass( boxDefaults );\n\n/* eslint-disable no-self-assign */\nBox.prototype.create = function( options ) {\n  Anchor.prototype.create.call( this, options );\n  this.updatePath();\n  // HACK reset fill to trigger face setter\n  this.fill = this.fill;\n};\n\nBox.prototype.updatePath = function() {\n  // reset all faces to trigger setters\n  faceNames.forEach( function( faceName ) {\n    this[ faceName ] = this[ faceName ];\n  }, this );\n};\n/* eslint-enable no-self-assign */\n\nfaceNames.forEach( function( faceName ) {\n  var _faceName = '_' + faceName;\n  Object.defineProperty( Box.prototype, faceName, {\n    get: function() {\n      return this[ _faceName ];\n    },\n    set: function( value ) {\n      this[ _faceName ] = value;\n      this.setFace( faceName, value );\n    },\n  } );\n} );\n\nBox.prototype.setFace = function( faceName, value ) {\n  var rectProperty = faceName + 'Rect';\n  var rect = this[ rectProperty ];\n  // remove if false\n  if ( !value ) {\n    this.removeChild( rect );\n    return;\n  }\n  // update & add face\n  var options = this.getFaceOptions( faceName );\n  options.color = typeof value == 'string' ? value : this.color;\n\n  if ( rect ) {\n    // update previous\n    rect.setOptions( options );\n  } else {\n    // create new\n    rect = this[ rectProperty ] = new BoxRect( options );\n  }\n  rect.updatePath();\n  this.addChild( rect );\n};\n\nBox.prototype.getFaceOptions = function( faceName ) {\n  return {\n    frontFace: {\n      width: this.width,\n      height: this.height,\n      translate: { z: this.depth / 2 },\n    },\n    rearFace: {\n      width: this.width,\n      height: this.height,\n      translate: { z: -this.depth / 2 },\n      rotate: { y: TAU/2 },\n    },\n    leftFace: {\n      width: this.depth,\n      height: this.height,\n      translate: { x: -this.width / 2 },\n      rotate: { y: -TAU/4 },\n    },\n    rightFace: {\n      width: this.depth,\n      height: this.height,\n      translate: { x: this.width / 2 },\n      rotate: { y: TAU/4 },\n    },\n    topFace: {\n      width: this.width,\n      height: this.depth,\n      translate: { y: -this.height / 2 },\n      rotate: { x: -TAU/4 },\n    },\n    bottomFace: {\n      width: this.width,\n      height: this.depth,\n      translate: { y: this.height / 2 },\n      rotate: { x: TAU/4 },\n    },\n  }[ faceName ];\n};\n\n// ----- set face properties ----- //\n\nvar childProperties = [ 'color', 'stroke', 'fill', 'backface', 'front',\n  'visible' ];\nchildProperties.forEach( function( property ) {\n  // use proxy property for custom getter & setter\n  var _prop = '_' + property;\n  Object.defineProperty( Box.prototype, property, {\n    get: function() {\n      return this[ _prop ];\n    },\n    set: function( value ) {\n      this[ _prop ] = value;\n      faceNames.forEach( function( faceName ) {\n        var rect = this[ faceName + 'Rect' ];\n        var isFaceColor = typeof this[ faceName ] == 'string';\n        var isColorUnderwrite = property == 'color' && isFaceColor;\n        if ( rect && !isColorUnderwrite ) {\n          rect[ property ] = value;\n        }\n      }, this );\n    },\n  } );\n} );\n\nreturn Box;\n\n} ) );\n", "/**\n * Rect\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./shape') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Rect = factory( Zdog.Shape );\n  }\n}( this, function factory( Shape ) {\n\nvar Rect = Shape.subclass({\n  width: 1,\n  height: 1,\n});\n\nRect.prototype.setPath = function() {\n  var x = this.width / 2;\n  var y = this.height / 2;\n  /* eslint key-spacing: \"off\" */\n  this.path = [\n    { x: -x, y: -y },\n    { x:  x, y: -y },\n    { x:  x, y:  y },\n    { x: -x, y:  y },\n  ];\n};\n\nreturn Rect;\n\n} ) );\n", "/**\n * Shape\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./boilerplate'), require('./vector'),\n        require('./path-command'), require('./anchor') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Shape = factory( Zdog, Zdog.Vector, Zdog.PathCommand, Zdog.Anchor );\n  }\n}( this, function factory( utils, Vector, PathCommand, Anchor ) {\n\nvar Shape = Anchor.subclass({\n  stroke: 1,\n  fill: false,\n  color: '#333',\n  closed: true,\n  visible: true,\n  path: [ {} ],\n  front: { z: 1 },\n  backface: true,\n});\n\nShape.prototype.create = function( options ) {\n  Anchor.prototype.create.call( this, options );\n  this.updatePath();\n  // front\n  this.front = new Vector( options.front || this.front );\n  this.renderFront = new Vector( this.front );\n  this.renderNormal = new Vector();\n};\n\nvar actionNames = [\n  'move',\n  'line',\n  'bezier',\n  'arc',\n];\n\nShape.prototype.updatePath = function() {\n  this.setPath();\n  this.updatePathCommands();\n};\n\n// place holder for Ellipse, Rect, etc.\nShape.prototype.setPath = function() {};\n\n// parse path into PathCommands\nShape.prototype.updatePathCommands = function() {\n  var previousPoint;\n  this.pathCommands = this.path.map( function( pathPart, i ) {\n    // pathPart can be just vector coordinates -> { x, y, z }\n    // or path instruction -> { arc: [ {x0,y0,z0}, {x1,y1,z1} ] }\n    var keys = Object.keys( pathPart );\n    var method = keys[0];\n    var points = pathPart[ method ];\n    // default to line if no instruction\n    var isInstruction = keys.length == 1 && actionNames.indexOf( method ) != -1;\n    if ( !isInstruction ) {\n      method = 'line';\n      points = pathPart;\n    }\n    // munge single-point methods like line & move without arrays\n    var isLineOrMove = method == 'line' || method == 'move';\n    var isPointsArray = Array.isArray( points );\n    if ( isLineOrMove && !isPointsArray ) {\n      points = [ points ];\n    }\n\n    // first action is always move\n    method = i === 0 ? 'move' : method;\n    // arcs require previous last point\n    var command = new PathCommand( method, points, previousPoint );\n    // update previousLastPoint\n    previousPoint = command.endRenderPoint;\n    return command;\n  } );\n};\n\n// ----- update ----- //\n\nShape.prototype.reset = function() {\n  this.renderOrigin.set( this.origin );\n  this.renderFront.set( this.front );\n  // reset command render points\n  this.pathCommands.forEach( function( command ) {\n    command.reset();\n  } );\n};\n\nShape.prototype.transform = function( translation, rotation, scale ) {\n  // calculate render points backface visibility & cone/hemisphere shapes\n  this.renderOrigin.transform( translation, rotation, scale );\n  this.renderFront.transform( translation, rotation, scale );\n  this.renderNormal.set( this.renderOrigin ).subtract( this.renderFront );\n  // transform points\n  this.pathCommands.forEach( function( command ) {\n    command.transform( translation, rotation, scale );\n  } );\n  // transform children\n  this.children.forEach( function( child ) {\n    child.transform( translation, rotation, scale );\n  } );\n};\n\nShape.prototype.updateSortValue = function() {\n  // sort by average z of all points\n  // def not geometrically correct, but works for me\n  var pointCount = this.pathCommands.length;\n  var firstPoint = this.pathCommands[0].endRenderPoint;\n  var lastPoint = this.pathCommands[ pointCount - 1 ].endRenderPoint;\n  // ignore the final point if self closing shape\n  var isSelfClosing = pointCount > 2 && firstPoint.isSame( lastPoint );\n  if ( isSelfClosing ) {\n    pointCount -= 1;\n  }\n\n  var sortValueTotal = 0;\n  for ( var i = 0; i < pointCount; i++ ) {\n    sortValueTotal += this.pathCommands[i].endRenderPoint.z;\n  }\n  this.sortValue = sortValueTotal/pointCount;\n};\n\n// ----- render ----- //\n\nShape.prototype.render = function( ctx, renderer ) {\n  var length = this.pathCommands.length;\n  if ( !this.visible || !length ) {\n    return;\n  }\n  // do not render if hiding backface\n  this.isFacingBack = this.renderNormal.z > 0;\n  if ( !this.backface && this.isFacingBack ) {\n    return;\n  }\n  if ( !renderer ) {\n    throw new Error( 'Zdog renderer required. Set to ' + renderer );\n  }\n  // render dot or path\n  var isDot = length == 1;\n  if ( renderer.isCanvas && isDot ) {\n    this.renderCanvasDot( ctx, renderer );\n  } else {\n    this.renderPath( ctx, renderer );\n  }\n};\n\nvar TAU = utils.TAU;\n// Safari does not render lines with no size, have to render circle instead\nShape.prototype.renderCanvasDot = function( ctx ) {\n  var lineWidth = this.getLineWidth();\n  if ( !lineWidth ) {\n    return;\n  }\n  ctx.fillStyle = this.getRenderColor();\n  var point = this.pathCommands[0].endRenderPoint;\n  ctx.beginPath();\n  var radius = lineWidth/2;\n  ctx.arc( point.x, point.y, radius, 0, TAU );\n  ctx.fill();\n};\n\nShape.prototype.getLineWidth = function() {\n  if ( !this.stroke ) {\n    return 0;\n  }\n  if ( this.stroke == true ) {\n    return 1;\n  }\n  return this.stroke;\n};\n\nShape.prototype.getRenderColor = function() {\n  // use backface color if applicable\n  var isBackfaceColor = typeof this.backface == 'string' && this.isFacingBack;\n  var color = isBackfaceColor ? this.backface : this.color;\n  return color;\n};\n\nShape.prototype.renderPath = function( ctx, renderer ) {\n  var elem = this.getRenderElement( ctx, renderer );\n  var isTwoPoints = this.pathCommands.length == 2 &&\n    this.pathCommands[1].method == 'line';\n  var isClosed = !isTwoPoints && this.closed;\n  var color = this.getRenderColor();\n\n  renderer.renderPath( ctx, elem, this.pathCommands, isClosed );\n  renderer.stroke( ctx, elem, this.stroke, color, this.getLineWidth() );\n  renderer.fill( ctx, elem, this.fill, color );\n  renderer.end( ctx, elem );\n};\n\nvar svgURI = 'http://www.w3.org/2000/svg';\n\nShape.prototype.getRenderElement = function( ctx, renderer ) {\n  if ( !renderer.isSvg ) {\n    return;\n  }\n  if ( !this.svgElement ) {\n    // create svgElement\n    this.svgElement = document.createElementNS( svgURI, 'path' );\n    this.svgElement.setAttribute( 'stroke-linecap', 'round' );\n    this.svgElement.setAttribute( 'stroke-linejoin', 'round' );\n  }\n  return this.svgElement;\n};\n\nreturn Shape;\n\n} ) );\n", "/**\n * RoundedRect\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./shape') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.RoundedRect = factory( Zdog.Shape );\n  }\n}( this, function factory( Shape ) {\n\nvar RoundedRect = Shape.subclass({\n  width: 1,\n  height: 1,\n  cornerRadius: 0.25,\n  closed: false,\n});\n\nRoundedRect.prototype.setPath = function() {\n  /* eslint\n     id-length: [ \"error\", { \"min\": 2, \"exceptions\": [ \"x\", \"y\" ] }],\n     key-spacing: \"off\" */\n  var xA = this.width / 2;\n  var yA = this.height / 2;\n  var shortSide = Math.min( xA, yA );\n  var cornerRadius = Math.min( this.cornerRadius, shortSide );\n  var xB = xA - cornerRadius;\n  var yB = yA - cornerRadius;\n  var path = [\n    // top right corner\n    { x: xB, y: -yA },\n    { arc: [\n      { x: xA, y: -yA },\n      { x: xA, y: -yB },\n    ] },\n  ];\n  // bottom right corner\n  if ( yB ) {\n    path.push({ x: xA, y: yB });\n  }\n  path.push({ arc: [\n    { x: xA, y:  yA },\n    { x: xB, y:  yA },\n  ] });\n  // bottom left corner\n  if ( xB ) {\n    path.push({ x: -xB, y: yA });\n  }\n  path.push({ arc: [\n    { x: -xA, y:  yA },\n    { x: -xA, y:  yB },\n  ] });\n  // top left corner\n  if ( yB ) {\n    path.push({ x: -xA, y: -yB });\n  }\n  path.push({ arc: [\n    { x: -xA, y: -yA },\n    { x: -xB, y: -yA },\n  ] });\n\n  // back to top right corner\n  if ( xB ) {\n    path.push({ x: xB, y: -yA });\n  }\n\n  this.path = path;\n};\n\nreturn RoundedRect;\n\n} ) );\n", "/**\n * Anchor\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./boilerplate'), require('./vector'),\n        require('./canvas-renderer'), require('./svg-renderer') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Anchor = factory( Zdog, Zdog.Vector, Zdog.CanvasRenderer,\n        Zdog.SvgRenderer );\n  }\n}( this, function factory( utils, Vector, CanvasRenderer, SvgRenderer ) {\n\nvar TAU = utils.TAU;\nvar onePoint = { x: 1, y: 1, z: 1 };\n\nfunction Anchor( options ) {\n  this.create( options || {} );\n}\n\nAnchor.prototype.create = function( options ) {\n  this.children = [];\n  // set defaults & options\n  utils.extend( this, this.constructor.defaults );\n  this.setOptions( options );\n\n  // transform\n  this.translate = new Vector( options.translate );\n  this.rotate = new Vector( options.rotate );\n  this.scale = new Vector( onePoint ).multiply( this.scale );\n  // origin\n  this.origin = new Vector();\n  this.renderOrigin = new Vector();\n\n  if ( this.addTo ) {\n    this.addTo.addChild( this );\n  }\n};\n\nAnchor.defaults = {};\n\nAnchor.optionKeys = Object.keys( Anchor.defaults ).concat([\n  'rotate',\n  'translate',\n  'scale',\n  'addTo',\n]);\n\nAnchor.prototype.setOptions = function( options ) {\n  var optionKeys = this.constructor.optionKeys;\n\n  for ( var key in options ) {\n    if ( optionKeys.indexOf( key ) != -1 ) {\n      this[ key ] = options[ key ];\n    }\n  }\n};\n\nAnchor.prototype.addChild = function( shape ) {\n  if ( this.children.indexOf( shape ) != -1 ) {\n    return;\n  }\n  shape.remove(); // remove previous parent\n  shape.addTo = this; // keep parent reference\n  this.children.push( shape );\n};\n\nAnchor.prototype.removeChild = function( shape ) {\n  var index = this.children.indexOf( shape );\n  if ( index != -1 ) {\n    this.children.splice( index, 1 );\n  }\n};\n\nAnchor.prototype.remove = function() {\n  if ( this.addTo ) {\n    this.addTo.removeChild( this );\n  }\n};\n\n// ----- update ----- //\n\nAnchor.prototype.update = function() {\n  // update self\n  this.reset();\n  // update children\n  this.children.forEach( function( child ) {\n    child.update();\n  } );\n  this.transform( this.translate, this.rotate, this.scale );\n};\n\nAnchor.prototype.reset = function() {\n  this.renderOrigin.set( this.origin );\n};\n\nAnchor.prototype.transform = function( translation, rotation, scale ) {\n  this.renderOrigin.transform( translation, rotation, scale );\n  // transform children\n  this.children.forEach( function( child ) {\n    child.transform( translation, rotation, scale );\n  } );\n};\n\nAnchor.prototype.updateGraph = function() {\n  this.update();\n  this.updateFlatGraph();\n  this.flatGraph.forEach( function( item ) {\n    item.updateSortValue();\n  } );\n  // z-sort\n  this.flatGraph.sort( Anchor.shapeSorter );\n};\n\nAnchor.shapeSorter = function( a, b ) {\n  return a.sortValue - b.sortValue;\n};\n\n// custom getter to check for flatGraph before using it\nObject.defineProperty( Anchor.prototype, 'flatGraph', {\n  get: function() {\n    if ( !this._flatGraph ) {\n      this.updateFlatGraph();\n    }\n    return this._flatGraph;\n  },\n  set: function( graph ) {\n    this._flatGraph = graph;\n  },\n} );\n\nAnchor.prototype.updateFlatGraph = function() {\n  this.flatGraph = this.getFlatGraph();\n};\n\n// return Array of self & all child graph items\nAnchor.prototype.getFlatGraph = function() {\n  var flatGraph = [ this ];\n  return this.addChildFlatGraph( flatGraph );\n};\n\nAnchor.prototype.addChildFlatGraph = function( flatGraph ) {\n  this.children.forEach( function( child ) {\n    var childFlatGraph = child.getFlatGraph();\n    Array.prototype.push.apply( flatGraph, childFlatGraph );\n  } );\n  return flatGraph;\n};\n\nAnchor.prototype.updateSortValue = function() {\n  this.sortValue = this.renderOrigin.z;\n};\n\n// ----- render ----- //\n\nAnchor.prototype.render = function() {};\n\n// TODO refactor out CanvasRenderer so its not a dependency within anchor.js\nAnchor.prototype.renderGraphCanvas = function( ctx ) {\n  if ( !ctx ) {\n    throw new Error( 'ctx is ' + ctx + '. ' +\n      'Canvas context required for render. Check .renderGraphCanvas( ctx ).' );\n  }\n  this.flatGraph.forEach( function( item ) {\n    item.render( ctx, CanvasRenderer );\n  } );\n};\n\nAnchor.prototype.renderGraphSvg = function( svg ) {\n  if ( !svg ) {\n    throw new Error( 'svg is ' + svg + '. ' +\n      'SVG required for render. Check .renderGraphSvg( svg ).' );\n  }\n  this.flatGraph.forEach( function( item ) {\n    item.render( svg, SvgRenderer );\n  } );\n};\n\n// ----- misc ----- //\n\nAnchor.prototype.copy = function( options ) {\n  // copy options\n  var itemOptions = {};\n  var optionKeys = this.constructor.optionKeys;\n  optionKeys.forEach( function( key ) {\n    itemOptions[ key ] = this[ key ];\n  }, this );\n  // add set options\n  utils.extend( itemOptions, options );\n  var ItemClass = this.constructor;\n  return new ItemClass( itemOptions );\n};\n\nAnchor.prototype.copyGraph = function( options ) {\n  var clone = this.copy( options );\n  this.children.forEach( function( child ) {\n    child.copyGraph({\n      addTo: clone,\n    });\n  } );\n  return clone;\n};\n\nAnchor.prototype.normalizeRotate = function() {\n  this.rotate.x = utils.modulo( this.rotate.x, TAU );\n  this.rotate.y = utils.modulo( this.rotate.y, TAU );\n  this.rotate.z = utils.modulo( this.rotate.z, TAU );\n};\n\n// ----- subclass ----- //\n\nfunction getSubclass( Super ) {\n  return function( defaults ) {\n    // create constructor\n    function Item( options ) {\n      this.create( options || {} );\n    }\n\n    Item.prototype = Object.create( Super.prototype );\n    Item.prototype.constructor = Item;\n\n    Item.defaults = utils.extend( {}, Super.defaults );\n    utils.extend( Item.defaults, defaults );\n    // create optionKeys\n    Item.optionKeys = Super.optionKeys.slice( 0 );\n    // add defaults keys to optionKeys, dedupe\n    Object.keys( Item.defaults ).forEach( function( key ) {\n      if ( !Item.optionKeys.indexOf( key ) != 1 ) {\n        Item.optionKeys.push( key );\n      }\n    } );\n\n    Item.subclass = getSubclass( Item );\n\n    return Item;\n  };\n}\n\nAnchor.subclass = getSubclass( Anchor );\n\nreturn Anchor;\n\n} ) );\n", "/**\n * SvgRenderer\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory();\n  } else {\n    // browser global\n    root.Zdog.SvgRenderer = factory();\n  }\n}( this, function factory() {\n\nvar SvgRenderer = { isSvg: true };\n\n// round path coordinates to 3 decimals\nvar round = SvgRenderer.round = function( num ) {\n  return Math.round( num * 1000 ) / 1000;\n};\n\nfunction getPointString( point ) {\n  return round( point.x ) + ',' + round( point.y ) + ' ';\n}\n\nSvgRenderer.begin = function() {};\n\nSvgRenderer.move = function( svg, elem, point ) {\n  return 'M' + getPointString( point );\n};\n\nSvgRenderer.line = function( svg, elem, point ) {\n  return 'L' + getPointString( point );\n};\n\nSvgRenderer.bezier = function( svg, elem, cp0, cp1, end ) {\n  return 'C' + getPointString( cp0 ) + getPointString( cp1 ) +\n    getPointString( end );\n};\n\nSvgRenderer.closePath = function( /* elem */) {\n  return 'Z';\n};\n\nSvgRenderer.setPath = function( svg, elem, pathValue ) {\n  elem.setAttribute( 'd', pathValue );\n};\n\nSvgRenderer.renderPath = function( svg, elem, pathCommands, isClosed ) {\n  var pathValue = '';\n  pathCommands.forEach( function( command ) {\n    pathValue += command.render( svg, elem, SvgRenderer );\n  } );\n  if ( isClosed ) {\n    pathValue += this.closePath( svg, elem );\n  }\n  this.setPath( svg, elem, pathValue );\n};\n\nSvgRenderer.stroke = function( svg, elem, isStroke, color, lineWidth ) {\n  if ( !isStroke ) {\n    return;\n  }\n  elem.setAttribute( 'stroke', color );\n  elem.setAttribute( 'stroke-width', lineWidth );\n};\n\nSvgRenderer.fill = function( svg, elem, isFill, color ) {\n  var fillColor = isFill ? color : 'none';\n  elem.setAttribute( 'fill', fillColor );\n};\n\nSvgRenderer.end = function( svg, elem ) {\n  svg.appendChild( elem );\n};\n\nreturn SvgRenderer;\n\n} ) );\n", "/**\n * Group\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./anchor') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Group = factory( Zdog.Anchor );\n  }\n}( this, function factory( Anchor ) {\n\nvar Group = Anchor.subclass({\n  updateSort: false,\n  visible: true,\n});\n\n// ----- update ----- //\n\nGroup.prototype.updateSortValue = function() {\n  var sortValueTotal = 0;\n  this.flatGraph.forEach( function( item ) {\n    item.updateSortValue();\n    sortValueTotal += item.sortValue;\n  } );\n  // average sort value of all points\n  // def not geometrically correct, but works for me\n  this.sortValue = sortValueTotal / this.flatGraph.length;\n\n  if ( this.updateSort ) {\n    this.flatGraph.sort( Anchor.shapeSorter );\n  }\n};\n\n// ----- render ----- //\n\nGroup.prototype.render = function( ctx, renderer ) {\n  if ( !this.visible ) {\n    return;\n  }\n\n  this.flatGraph.forEach( function( item ) {\n    item.render( ctx, renderer );\n  } );\n};\n\n// actual group flatGraph only used inside group\nGroup.prototype.updateFlatGraph = function() {\n  // do not include self\n  var flatGraph = [];\n  this.flatGraph = this.addChildFlatGraph( flatGraph );\n};\n\n// do not include children, group handles rendering & sorting internally\nGroup.prototype.getFlatGraph = function() {\n  return [ this ];\n};\n\nreturn Group;\n\n} ) );\n", "/*!\n * Zdog v1.1.3\n * Round, flat, designer-friendly pseudo-3D engine\n * Licensed MIT\n * https://zzz.dog\n * Copyright 2020 Metafizzy\n */\n\n/**\n * Boilerplate & utils\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory();\n  } else {\n    // browser global\n    root.Zdog = factory();\n  }\n}( this, function factory() {\n\nvar Zdog = {};\n\nZdog.TAU = Math.PI * 2;\n\nZdog.extend = function( a, b ) {\n  for ( var prop in b ) {\n    a[ prop ] = b[ prop ];\n  }\n  return a;\n};\n\nZdog.lerp = function( a, b, alpha ) {\n  return ( b - a ) * alpha + a;\n};\n\nZdog.modulo = function( num, div ) {\n  return ( ( num % div ) + div ) % div;\n};\n\nvar powerMultipliers = {\n  2: function( a ) {\n    return a * a;\n  },\n  3: function( a ) {\n    return a * a * a;\n  },\n  4: function( a ) {\n    return a * a * a * a;\n  },\n  5: function( a ) {\n    return a * a * a * a * a;\n  },\n};\n\nZdog.easeInOut = function( alpha, power ) {\n  if ( power == 1 ) {\n    return alpha;\n  }\n  alpha = Math.max( 0, Math.min( 1, alpha ) );\n  var isFirstHalf = alpha < 0.5;\n  var slope = isFirstHalf ? alpha : 1 - alpha;\n  slope /= 0.5;\n  // make easing steeper with more multiples\n  var powerMultiplier = powerMultipliers[ power ] || powerMultipliers[2];\n  var curve = powerMultiplier( slope );\n  curve /= 2;\n  return isFirstHalf ? curve : 1 - curve;\n};\n\nreturn Zdog;\n\n} ) );\n", "/**\n * Shape\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./boilerplate'), require('./shape') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Polygon = factory( Zdog, Zdog.Shape );\n  }\n}( this, function factory( utils, Shape ) {\n\nvar Polygon = Shape.subclass({\n  sides: 3,\n  radius: 0.5,\n});\n\nvar TAU = utils.TAU;\n\nPolygon.prototype.setPath = function() {\n  this.path = [];\n  for ( var i = 0; i < this.sides; i++ ) {\n    var theta = i / this.sides * TAU - TAU/4;\n    var x = Math.cos( theta ) * this.radius;\n    var y = Math.sin( theta ) * this.radius;\n    this.path.push({ x: x, y: y });\n  }\n};\n\nreturn Polygon;\n\n} ) );\n", "/**\n * Illustration\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./boilerplate'), require('./anchor'),\n        require('./dragger') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Illustration = factory( Zdog, Zdog.Anchor, Zdog.Dragger );\n  }\n}( this, function factory( utils, <PERSON><PERSON>, Dragger ) {\n\nfunction noop() {}\nvar TAU = utils.TAU;\n\nvar Illustration = Anchor.subclass({\n  element: undefined,\n  centered: true,\n  zoom: 1,\n  dragRotate: false,\n  resize: false,\n  onPrerender: noop,\n  onDragStart: noop,\n  onDragMove: noop,\n  onDragEnd: noop,\n  onResize: noop,\n});\n\nutils.extend( Illustration.prototype, Dragger.prototype );\n\nIllustration.prototype.create = function( options ) {\n  Anchor.prototype.create.call( this, options );\n  Dragger.prototype.create.call( this, options );\n  this.setElement( this.element );\n  this.setDragRotate( this.dragRotate );\n  this.setResize( this.resize );\n};\n\nIllustration.prototype.setElement = function( element ) {\n  element = this.getQueryElement( element );\n  if ( !element ) {\n    throw new Error( 'Zdog.Illustration element required. Set to ' + element );\n  }\n\n  var nodeName = element.nodeName.toLowerCase();\n  if ( nodeName == 'canvas' ) {\n    this.setCanvas( element );\n  } else if ( nodeName == 'svg' ) {\n    this.setSvg( element );\n  }\n};\n\nIllustration.prototype.setSize = function( width, height ) {\n  width = Math.round( width );\n  height = Math.round( height );\n  if ( this.isCanvas ) {\n    this.setSizeCanvas( width, height );\n  } else if ( this.isSvg ) {\n    this.setSizeSvg( width, height );\n  }\n};\n\nIllustration.prototype.setResize = function( resize ) {\n  this.resize = resize;\n  // create resize event listener\n  if ( !this.resizeListener ) {\n    this.resizeListener = this.onWindowResize.bind( this );\n  }\n  // add/remove event listener\n  if ( resize ) {\n    window.addEventListener( 'resize', this.resizeListener );\n    this.onWindowResize();\n  } else {\n    window.removeEventListener( 'resize', this.resizeListener );\n  }\n};\n\n// TODO debounce this?\nIllustration.prototype.onWindowResize = function() {\n  this.setMeasuredSize();\n  this.onResize( this.width, this.height );\n};\n\nIllustration.prototype.setMeasuredSize = function() {\n  var width, height;\n  var isFullscreen = this.resize == 'fullscreen';\n  if ( isFullscreen ) {\n    width = window.innerWidth;\n    height = window.innerHeight;\n  } else {\n    var rect = this.element.getBoundingClientRect();\n    width = rect.width;\n    height = rect.height;\n  }\n  this.setSize( width, height );\n};\n\n// ----- render ----- //\n\nIllustration.prototype.renderGraph = function( item ) {\n  if ( this.isCanvas ) {\n    this.renderGraphCanvas( item );\n  } else if ( this.isSvg ) {\n    this.renderGraphSvg( item );\n  }\n};\n\n// combo method\nIllustration.prototype.updateRenderGraph = function( item ) {\n  this.updateGraph();\n  this.renderGraph( item );\n};\n\n// ----- canvas ----- //\n\nIllustration.prototype.setCanvas = function( element ) {\n  this.element = element;\n  this.isCanvas = true;\n  // update related properties\n  this.ctx = this.element.getContext('2d');\n  // set initial size\n  this.setSizeCanvas( element.width, element.height );\n};\n\nIllustration.prototype.setSizeCanvas = function( width, height ) {\n  this.width = width;\n  this.height = height;\n  // up-rez for hi-DPI devices\n  var pixelRatio = this.pixelRatio = window.devicePixelRatio || 1;\n  this.element.width = this.canvasWidth = width * pixelRatio;\n  this.element.height = this.canvasHeight = height * pixelRatio;\n  var needsHighPixelRatioSizing = pixelRatio > 1 && !this.resize;\n  if ( needsHighPixelRatioSizing ) {\n    this.element.style.width = width + 'px';\n    this.element.style.height = height + 'px';\n  }\n};\n\nIllustration.prototype.renderGraphCanvas = function( item ) {\n  item = item || this;\n  this.prerenderCanvas();\n  Anchor.prototype.renderGraphCanvas.call( item, this.ctx );\n  this.postrenderCanvas();\n};\n\nIllustration.prototype.prerenderCanvas = function() {\n  var ctx = this.ctx;\n  ctx.lineCap = 'round';\n  ctx.lineJoin = 'round';\n  ctx.clearRect( 0, 0, this.canvasWidth, this.canvasHeight );\n  ctx.save();\n  if ( this.centered ) {\n    var centerX = this.width / 2 * this.pixelRatio;\n    var centerY = this.height / 2 * this.pixelRatio;\n    ctx.translate( centerX, centerY );\n  }\n  var scale = this.pixelRatio * this.zoom;\n  ctx.scale( scale, scale );\n  this.onPrerender( ctx );\n};\n\nIllustration.prototype.postrenderCanvas = function() {\n  this.ctx.restore();\n};\n\n// ----- svg ----- //\n\nIllustration.prototype.setSvg = function( element ) {\n  this.element = element;\n  this.isSvg = true;\n  this.pixelRatio = 1;\n  // set initial size from width & height attributes\n  var width = element.getAttribute('width');\n  var height = element.getAttribute('height');\n  this.setSizeSvg( width, height );\n};\n\nIllustration.prototype.setSizeSvg = function( width, height ) {\n  this.width = width;\n  this.height = height;\n  var viewWidth = width / this.zoom;\n  var viewHeight = height / this.zoom;\n  var viewX = this.centered ? -viewWidth/2 : 0;\n  var viewY = this.centered ? -viewHeight/2 : 0;\n  this.element.setAttribute( 'viewBox', viewX + ' ' + viewY + ' ' +\n    viewWidth + ' ' + viewHeight );\n  if ( this.resize ) {\n    // remove size attributes, let size be determined by viewbox\n    this.element.removeAttribute('width');\n    this.element.removeAttribute('height');\n  } else {\n    this.element.setAttribute( 'width', width );\n    this.element.setAttribute( 'height', height );\n  }\n};\n\nIllustration.prototype.renderGraphSvg = function( item ) {\n  item = item || this;\n  empty( this.element );\n  this.onPrerender( this.element );\n  Anchor.prototype.renderGraphSvg.call( item, this.element );\n};\n\nfunction empty( element ) {\n  while ( element.firstChild ) {\n    element.removeChild( element.firstChild );\n  }\n}\n\n// ----- drag ----- //\n\nIllustration.prototype.setDragRotate = function( item ) {\n  if ( !item ) {\n    return;\n  } else if ( item === true ) {\n    /* eslint consistent-this: \"off\" */\n    item = this;\n  }\n  this.dragRotate = item;\n\n  this.bindDrag( this.element );\n};\n\nIllustration.prototype.dragStart = function( /* event, pointer */) {\n  this.dragStartRX = this.dragRotate.rotate.x;\n  this.dragStartRY = this.dragRotate.rotate.y;\n  Dragger.prototype.dragStart.apply( this, arguments );\n};\n\nIllustration.prototype.dragMove = function( event, pointer ) {\n  var moveX = pointer.pageX - this.dragStartX;\n  var moveY = pointer.pageY - this.dragStartY;\n  var displaySize = Math.min( this.width, this.height );\n  var moveRY = moveX/displaySize * TAU;\n  var moveRX = moveY/displaySize * TAU;\n  this.dragRotate.rotate.x = this.dragStartRX - moveRX;\n  this.dragRotate.rotate.y = this.dragStartRY - moveRY;\n  Dragger.prototype.dragMove.apply( this, arguments );\n};\n\nreturn Illustration;\n\n} ) );\n", "/**\n * <PERSON><PERSON>\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory();\n  } else {\n    // browser global\n    root.Zdog.Dragger = factory();\n  }\n}( this, function factory() {\n\n// quick & dirty drag event stuff\n// messes up if multiple pointers/touches\n\n// check for browser window #85\nvar hasWindow = typeof window != 'undefined';\n// event support, default to mouse events\nvar downEvent = 'mousedown';\nvar moveEvent = 'mousemove';\nvar upEvent = 'mouseup';\nif ( hasWindow ) {\n  if ( window.PointerEvent ) {\n    // PointerEvent, Chrome\n    downEvent = 'pointerdown';\n    moveEvent = 'pointermove';\n    upEvent = 'pointerup';\n  } else if ( 'ontouchstart' in window ) {\n    // Touch Events, iOS Safari\n    downEvent = 'touchstart';\n    moveEvent = 'touchmove';\n    upEvent = 'touchend';\n  }\n}\n\nfunction noop() {}\n\nfunction Dragger( options ) {\n  this.create( options || {} );\n}\n\nDragger.prototype.create = function( options ) {\n  this.onDragStart = options.onDragStart || noop;\n  this.onDragMove = options.onDragMove || noop;\n  this.onDragEnd = options.onDragEnd || noop;\n\n  this.bindDrag( options.startElement );\n};\n\nDragger.prototype.bindDrag = function( element ) {\n  element = this.getQueryElement( element );\n  if ( !element ) {\n    return;\n  }\n  // disable browser gestures #53\n  element.style.touchAction = 'none';\n  element.addEventListener( downEvent, this );\n};\n\nDragger.prototype.getQueryElement = function( element ) {\n  if ( typeof element == 'string' ) {\n    // with string, query selector\n    element = document.querySelector( element );\n  }\n  return element;\n};\n\nDragger.prototype.handleEvent = function( event ) {\n  var method = this[ 'on' + event.type ];\n  if ( method ) {\n    method.call( this, event );\n  }\n};\n\nDragger.prototype.onmousedown =\nDragger.prototype.onpointerdown = function( event ) {\n  this.dragStart( event, event );\n};\n\nDragger.prototype.ontouchstart = function( event ) {\n  this.dragStart( event, event.changedTouches[0] );\n};\n\nDragger.prototype.dragStart = function( event, pointer ) {\n  event.preventDefault();\n  this.dragStartX = pointer.pageX;\n  this.dragStartY = pointer.pageY;\n  if ( hasWindow ) {\n    window.addEventListener( moveEvent, this );\n    window.addEventListener( upEvent, this );\n  }\n  this.onDragStart( pointer );\n};\n\nDragger.prototype.ontouchmove = function( event ) {\n  // HACK, moved touch may not be first\n  this.dragMove( event, event.changedTouches[0] );\n};\n\nDragger.prototype.onmousemove =\nDragger.prototype.onpointermove = function( event ) {\n  this.dragMove( event, event );\n};\n\nDragger.prototype.dragMove = function( event, pointer ) {\n  event.preventDefault();\n  var moveX = pointer.pageX - this.dragStartX;\n  var moveY = pointer.pageY - this.dragStartY;\n  this.onDragMove( pointer, moveX, moveY );\n};\n\nDragger.prototype.onmouseup =\nDragger.prototype.onpointerup =\nDragger.prototype.ontouchend =\nDragger.prototype.dragEnd = function( /* event */) {\n  window.removeEventListener( moveEvent, this );\n  window.removeEventListener( upEvent, this );\n  this.onDragEnd();\n};\n\nreturn Dragger;\n\n} ) );\n", "/**\n * CanvasRenderer\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory();\n  } else {\n    // browser global\n    root.Zdog.CanvasRenderer = factory();\n  }\n}( this, function factory() {\n\nvar CanvasRenderer = { isCanvas: true };\n\nCanvasRenderer.begin = function( ctx ) {\n  ctx.beginPath();\n};\n\nCanvasRenderer.move = function( ctx, elem, point ) {\n  ctx.moveTo( point.x, point.y );\n};\n\nCanvasRenderer.line = function( ctx, elem, point ) {\n  ctx.lineTo( point.x, point.y );\n};\n\nCanvasRenderer.bezier = function( ctx, elem, cp0, cp1, end ) {\n  ctx.bezierCurveTo( cp0.x, cp0.y, cp1.x, cp1.y, end.x, end.y );\n};\n\nCanvasRenderer.closePath = function( ctx ) {\n  ctx.closePath();\n};\n\nCanvasRenderer.setPath = function() {};\n\nCanvasRenderer.renderPath = function( ctx, elem, pathCommands, isClosed ) {\n  this.begin( ctx, elem );\n  pathCommands.forEach( function( command ) {\n    command.render( ctx, elem, CanvasRenderer );\n  } );\n  if ( isClosed ) {\n    this.closePath( ctx, elem );\n  }\n};\n\nCanvasRenderer.stroke = function( ctx, elem, isStroke, color, lineWidth ) {\n  if ( !isStroke ) {\n    return;\n  }\n  ctx.strokeStyle = color;\n  ctx.lineWidth = lineWidth;\n  ctx.stroke();\n};\n\nCanvasRenderer.fill = function( ctx, elem, isFill, color ) {\n  if ( !isFill ) {\n    return;\n  }\n  ctx.fillStyle = color;\n  ctx.fill();\n};\n\nCanvasRenderer.end = function() {};\n\nreturn CanvasRenderer;\n\n} ) );\n", "/**\n * Index\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory(\n        require('./boilerplate'),\n        require('./canvas-renderer'),\n        require('./svg-renderer'),\n        require('./vector'),\n        require('./anchor'),\n        require('./dragger'),\n        require('./illustration'),\n        require('./path-command'),\n        require('./shape'),\n        require('./group'),\n        require('./rect'),\n        require('./rounded-rect'),\n        require('./ellipse'),\n        require('./polygon'),\n        require('./hemisphere'),\n        require('./cylinder'),\n        require('./cone'),\n        require('./box')\n    );\n  } else if ( typeof define == 'function' && define.amd ) {\n    /* globals define */ // AMD\n    define( 'zdog', [], root.Zdog );\n  }\n/* eslint-disable max-params */\n} )( this, function factory( <PERSON><PERSON>, <PERSON>, Svg<PERSON><PERSON><PERSON>, <PERSON>ector, Anchor,\n    Dragger, Illustration, PathCommand, Shape, Group, Rect, RoundedRect,\n    Ellipse, Polygon, Hemisphere, Cylinder, Cone, Box ) {\n/* eslint-enable max-params */\n\n      Zdog.CanvasRenderer = CanvasRenderer;\n      Zdog.SvgRenderer = SvgRenderer;\n      Zdog.Vector = Vector;\n      Zdog.Anchor = Anchor;\n      Zdog.Dragger = Dragger;\n      Zdog.Illustration = Illustration;\n      Zdog.PathCommand = PathCommand;\n      Zdog.Shape = Shape;\n      Zdog.Group = Group;\n      Zdog.Rect = Rect;\n      Zdog.RoundedRect = RoundedRect;\n      Zdog.Ellipse = Ellipse;\n      Zdog.Polygon = Polygon;\n      Zdog.Hemisphere = Hemisphere;\n      Zdog.Cylinder = Cylinder;\n      Zdog.Cone = Cone;\n      Zdog.Box = Box;\n\n      return Zdog;\n} );\n", "/**\n * Ellipse\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./shape') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Ellipse = factory( Zdog.Shape );\n  }\n\n}( this, function factory( Shape ) {\n\nvar Ellipse = Shape.subclass({\n  diameter: 1,\n  width: undefined,\n  height: undefined,\n  quarters: 4,\n  closed: false,\n});\n\nEllipse.prototype.setPath = function() {\n  var width = this.width != undefined ? this.width : this.diameter;\n  var height = this.height != undefined ? this.height : this.diameter;\n  var x = width/2;\n  var y = height/2;\n  this.path = [\n    { x: 0, y: -y },\n    { arc: [ // top right\n      { x: x, y: -y },\n      { x: x, y: 0 },\n    ] },\n  ];\n  // bottom right\n  if ( this.quarters > 1 ) {\n    this.path.push({ arc: [\n      { x: x, y: y },\n      { x: 0, y: y },\n    ] });\n  }\n  // bottom left\n  if ( this.quarters > 2 ) {\n    this.path.push({ arc: [\n      { x: -x, y: y },\n      { x: -x, y: 0 },\n    ] });\n  }\n  // top left\n  if ( this.quarters > 3 ) {\n    this.path.push({ arc: [\n      { x: -x, y: -y },\n      { x: 0, y: -y },\n    ] });\n  }\n};\n\nreturn Ellipse;\n\n} ) );\n", "/**\n * Vector\n */\n\n( function( root, factory ) {\n  // module definition\n  if ( typeof module == 'object' && module.exports ) {\n    // CommonJS\n    module.exports = factory( require('./boilerplate') );\n  } else {\n    // browser global\n    var Zdog = root.Zdog;\n    Zdog.Vector = factory( Zdog );\n  }\n\n}( this, function factory( utils ) {\n\nfunction Vector( position ) {\n  this.set( position );\n}\n\nvar TAU = utils.TAU;\n\n// 'pos' = 'position'\nVector.prototype.set = function( pos ) {\n  this.x = pos && pos.x || 0;\n  this.y = pos && pos.y || 0;\n  this.z = pos && pos.z || 0;\n  return this;\n};\n\n// set coordinates without sanitizing\n// vec.write({ y: 2 }) only sets y coord\nVector.prototype.write = function( pos ) {\n  if ( !pos ) {\n    return this;\n  }\n  this.x = pos.x != undefined ? pos.x : this.x;\n  this.y = pos.y != undefined ? pos.y : this.y;\n  this.z = pos.z != undefined ? pos.z : this.z;\n  return this;\n};\n\nVector.prototype.rotate = function( rotation ) {\n  if ( !rotation ) {\n    return;\n  }\n  this.rotateZ( rotation.z );\n  this.rotateY( rotation.y );\n  this.rotateX( rotation.x );\n  return this;\n};\n\nVector.prototype.rotateZ = function( angle ) {\n  rotateProperty( this, angle, 'x', 'y' );\n};\n\nVector.prototype.rotateX = function( angle ) {\n  rotateProperty( this, angle, 'y', 'z' );\n};\n\nVector.prototype.rotateY = function( angle ) {\n  rotateProperty( this, angle, 'x', 'z' );\n};\n\nfunction rotateProperty( vec, angle, propA, propB ) {\n  if ( !angle || angle % TAU === 0 ) {\n    return;\n  }\n  var cos = Math.cos( angle );\n  var sin = Math.sin( angle );\n  var a = vec[ propA ];\n  var b = vec[ propB ];\n  vec[ propA ] = a * cos - b * sin;\n  vec[ propB ] = b * cos + a * sin;\n}\n\nVector.prototype.isSame = function( pos ) {\n  if ( !pos ) {\n    return false;\n  }\n  return this.x === pos.x && this.y === pos.y && this.z === pos.z;\n};\n\nVector.prototype.add = function( pos ) {\n  if ( !pos ) {\n    return this;\n  }\n  this.x += pos.x || 0;\n  this.y += pos.y || 0;\n  this.z += pos.z || 0;\n  return this;\n};\n\nVector.prototype.subtract = function( pos ) {\n  if ( !pos ) {\n    return this;\n  }\n  this.x -= pos.x || 0;\n  this.y -= pos.y || 0;\n  this.z -= pos.z || 0;\n  return this;\n};\n\nVector.prototype.multiply = function( pos ) {\n  if ( pos == undefined ) {\n    return this;\n  }\n  // multiple all values by same number\n  if ( typeof pos == 'number' ) {\n    this.x *= pos;\n    this.y *= pos;\n    this.z *= pos;\n  } else {\n    // multiply object\n    this.x *= pos.x != undefined ? pos.x : 1;\n    this.y *= pos.y != undefined ? pos.y : 1;\n    this.z *= pos.z != undefined ? pos.z : 1;\n  }\n  return this;\n};\n\nVector.prototype.transform = function( translation, rotation, scale ) {\n  this.multiply( scale );\n  this.rotate( rotation );\n  this.add( translation );\n  return this;\n};\n\nVector.prototype.lerp = function( pos, alpha ) {\n  this.x = utils.lerp( this.x, pos.x || 0, alpha );\n  this.y = utils.lerp( this.y, pos.y || 0, alpha );\n  this.z = utils.lerp( this.z, pos.z || 0, alpha );\n  return this;\n};\n\nVector.prototype.magnitude = function() {\n  var sum = this.x * this.x + this.y * this.y + this.z * this.z;\n  return getMagnitudeSqrt( sum );\n};\n\nfunction getMagnitudeSqrt( sum ) {\n  // PERF: check if sum ~= 1 and skip sqrt\n  if ( Math.abs( sum - 1 ) < 0.00000001 ) {\n    return 1;\n  }\n  return Math.sqrt( sum );\n}\n\nVector.prototype.magnitude2d = function() {\n  var sum = this.x * this.x + this.y * this.y;\n  return getMagnitudeSqrt( sum );\n};\n\nVector.prototype.copy = function() {\n  return new Vector( this );\n};\n\nreturn Vector;\n\n} ) );\n"], "names": ["root", "factory", "module", "exports", "require", "Zdog", "Hemisphere", "Vector", "<PERSON><PERSON>", "Ellipse", "this", "utils", "subclass", "fill", "TAU", "prototype", "create", "apply", "arguments", "apex", "addTo", "translate", "z", "diameter", "renderCentroid", "updateSortValue", "set", "render<PERSON>rigin", "lerp", "sortValue", "render", "ctx", "renderer", "renderDome", "visible", "elem", "getDomeRenderElement", "contourAngle", "Math", "atan2", "renderNormal", "y", "x", "domeRadius", "magnitude", "isCanvas", "startAngle", "endAngle", "beginPath", "arc", "isSvg", "domeSvgElement", "setAttribute", "stroke", "color", "getLineWidth", "end", "svgURI", "document", "createElementNS", "PathCommand", "method", "points", "previousPoint", "map", "mapVectorPoint", "renderPoints", "mapNewVector", "endRenderPoint", "length", "controlPoints", "point", "reset", "for<PERSON>ach", "renderPoint", "i", "transform", "translation", "rotation", "scale", "move", "line", "bezier", "cp0", "cp1", "arc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prev", "corner", "Cone", "renderApex", "tangentA", "tangentB", "surfacePathCommands", "renderConeSurface", "subtract", "apexDistance", "magnitude2d", "normalDistance", "eccenAngle", "acos", "eccen", "sin", "radius", "isApexVisible", "apexAngle", "projectLength", "projectAngle", "cos", "rotateZ", "add", "setSurfaceRenderPoint", "getSurfaceRenderElement", "<PERSON><PERSON><PERSON>", "surfaceSvgElement", "index", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Group", "noop", "CylinderGroup", "updateSort", "pathCommands", "renderCylinderSurface", "getRenderElement", "frontBase", "rearBase", "strokeWidth", "lineCap", "svgElement", "copyGraph", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frontFace", "undefined", "group", "baseZ", "baseColor", "backface", "rotate", "copy", "childProperties", "property", "_prop", "Object", "defineProperty", "get", "value", "Box", "Rect", "BoxRect", "faceNames", "boxDefaults", "extend", "defaults", "path", "faceName", "width", "height", "depth", "options", "call", "updatePath", "_faceName", "setFace", "rectProperty", "rect", "getFaceOptions", "setOptions", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "rearFace", "leftFace", "rightFace", "topFace", "bottomFace", "isFaceColor", "isColorUnderwrite", "set<PERSON>ath", "closed", "front", "renderFront", "actionNames", "updatePathCommands", "pathPart", "keys", "isInstruction", "indexOf", "isLineOrMove", "isPointsArray", "Array", "isArray", "command", "origin", "children", "child", "pointCount", "firstPoint", "lastPoint", "isSelfClosing", "isSame", "sortValueTotal", "isFacingBack", "Error", "isDot", "renderCanvasDot", "lineWidth", "fillStyle", "getRenderColor", "isBackfaceColor", "isTwoPoints", "isClosed", "RoundedRect", "cornerRadius", "xA", "yA", "shortSide", "min", "xB", "yB", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Svg<PERSON><PERSON><PERSON>", "onePoint", "getSubclass", "Super", "<PERSON><PERSON>", "constructor", "optionKeys", "slice", "key", "multiply", "concat", "shape", "remove", "splice", "update", "updateGraph", "updateFlatGraph", "flatGraph", "item", "sort", "shapeSorter", "a", "b", "_flatGraph", "graph", "getFlatGraph", "addChildFlatGraph", "childFlatGraph", "renderGraphCanvas", "renderGraphSvg", "svg", "itemOptions", "ItemClass", "clone", "normalizeRotate", "modulo", "round", "num", "getPointString", "begin", "closePath", "pathValue", "isStroke", "isFill", "fillColor", "append<PERSON><PERSON><PERSON>", "PI", "prop", "alpha", "div", "powerMultipliers", "easeInOut", "power", "max", "isFirstHalf", "slope", "powerMultiplier", "curve", "Polygon", "sides", "theta", "Illustration", "<PERSON><PERSON>", "element", "centered", "zoom", "dragRotate", "resize", "onP<PERSON>ender", "onDragStart", "onDragMove", "onDragEnd", "onResize", "empty", "<PERSON><PERSON><PERSON><PERSON>", "setElement", "setDragRotate", "setResize", "getQueryElement", "nodeName", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON>", "setSvg", "setSize", "setSizeCanvas", "setSizeSvg", "resizeListener", "onWindowResize", "bind", "window", "addEventListener", "removeEventListener", "setMeasuredSize", "isFullscreen", "innerWidth", "innerHeight", "getBoundingClientRect", "renderGraph", "updateRenderGraph", "getContext", "pixelRatio", "devicePixelRatio", "canvasWidth", "canvasHeight", "needsHighPixelRatioSizing", "style", "prerenderCanvas", "postrenderCanvas", "lineJoin", "clearRect", "save", "centerX", "centerY", "restore", "getAttribute", "viewWidth", "viewHeight", "viewX", "viewY", "removeAttribute", "bindDrag", "dragStart", "dragStartRX", "dragStartRY", "dragMove", "event", "pointer", "moveX", "pageX", "dragStartX", "moveY", "pageY", "dragStartY", "displaySize", "moveRY", "moveRX", "hasW<PERSON>ow", "downEvent", "moveEvent", "upEvent", "PointerEvent", "startElement", "touchAction", "querySelector", "handleEvent", "type", "onmousedown", "onpointerdown", "ontouchstart", "changedTouches", "preventDefault", "ontouchmove", "<PERSON><PERSON><PERSON><PERSON>", "onpointermove", "onmouseup", "onpointerup", "ontouchend", "dragEnd", "moveTo", "lineTo", "bezierCurveTo", "strokeStyle", "define", "quarters", "position", "rotateProperty", "vec", "angle", "propA", "propB", "getMagnitudeSqrt", "sum", "abs", "sqrt", "pos", "write", "rotateY", "rotateX"], "sourceRoot": ""}