"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[542],{51821:function(e,t,s){s.r(t),s.d(t,{default:function(){return ue}});var a=s(56768),l=s(24232),r=s(45130);const o={key:0,class:"report-dashboard"},n={key:0,class:"dashboard-container"},c={class:"status-bar"},i={class:"status-left"},u={class:"report-status"},d={class:"status-title"},h={class:"status-time"},k={class:"status-right"},p={class:"badge-value"},v={class:"dashboard-content"},g={class:"overview-panel"},b={class:"overview-header"},f={class:"task-info"},_={class:"task-name"},m={class:"env-tag"},y={class:"stat-cards"},C={class:"stat-card total"},F={class:"stat-icon"},L={class:"stat-info"},w={class:"stat-value"},X={class:"stat-card success"},S={class:"stat-icon"},E={class:"stat-info"},x={class:"stat-value"},D={class:"stat-card fail"},R={class:"stat-icon"},W={class:"stat-info"},I={class:"stat-value"},$={class:"stat-card error"},A={class:"stat-icon"},T={class:"stat-info"},B={class:"stat-value"},V={class:"chart-section"},Q={class:"chart-card"},q={class:"chart-content",ref:"chart1"},G={class:"chart-card"},K={class:"chart-content",ref:"chart2"},N={class:"detail-panel"},O={class:"detail-header"},U={class:"filter-tabs"},P=["onClick"],z={class:"tab-count"},H=["onClick"],M={class:"scene-status"},j={class:"scene-info"},J={class:"scene-name"},Y={class:"scene-action"},Z={class:"scene-detail"},ee={class:"case-detail"},te={class:"case-name"};function se(e,t,s,se,ae,le){const re=(0,a.g2)("Back"),oe=(0,a.g2)("el-icon"),ne=(0,a.g2)("el-button"),ce=(0,a.g2)("DataAnalysis"),ie=(0,a.g2)("SuccessFilled"),ue=(0,a.g2)("Warning"),de=(0,a.g2)("CircleCloseFilled"),he=(0,a.g2)("ArrowRight"),ke=(0,a.g2)("caseRes"),pe=(0,a.g2)("el-table-column"),ve=(0,a.g2)("el-table"),ge=(0,a.g2)("el-scrollbar");return ae.record?((0,a.uX)(),(0,a.CE)("div",o,[ae.report?((0,a.uX)(),(0,a.CE)("div",n,[(0,a.Lk)("div",c,[(0,a.Lk)("div",i,[(0,a.bF)(ne,{type:"text",class:"back-btn",onClick:le.goBack},{default:(0,a.k6)(()=>[(0,a.bF)(oe,null,{default:(0,a.k6)(()=>[(0,a.bF)(re)]),_:1})]),_:1},8,["onClick"]),(0,a.Lk)("div",u,[(0,a.Lk)("div",d,"测试报告 #"+(0,l.v_)(e.$route.params.id),1),(0,a.Lk)("div",h,(0,l.v_)(e.$tools.rTime(ae.record.create_time)),1)])]),(0,a.Lk)("div",k,[(0,a.Lk)("div",{class:(0,l.C4)(["status-badge",le.getPassRateClass])},[t[0]||(t[0]=(0,a.Lk)("span",{class:"badge-label"},"通过率",-1)),(0,a.Lk)("span",p,(0,l.v_)(ae.record.pass_rate||0)+"%",1)],2)])]),(0,a.bF)(ge,{style:{height:"calc(100vh - 21vh)",minHeight:"700px"}},{default:(0,a.k6)(()=>[(0,a.Lk)("div",v,[(0,a.Lk)("div",g,[(0,a.Lk)("div",b,[t[1]||(t[1]=(0,a.Lk)("h2",null,"测试概览",-1)),(0,a.Lk)("div",f,[(0,a.Lk)("div",_,(0,l.v_)(ae.record.plan_name||"未命名计划"),1),(0,a.Lk)("div",m,(0,l.v_)(ae.record.env_name||"未知环境"),1)])]),(0,a.Lk)("div",y,[(0,a.Lk)("div",C,[(0,a.Lk)("div",F,[(0,a.bF)(oe,null,{default:(0,a.k6)(()=>[(0,a.bF)(ce)]),_:1})]),(0,a.Lk)("div",L,[(0,a.Lk)("div",w,(0,l.v_)(ae.report.results&&ae.report.results.length||0),1),t[2]||(t[2]=(0,a.Lk)("div",{class:"stat-label"},"场景总数",-1))]),t[3]||(t[3]=(0,a.Lk)("div",{class:"stat-wave"},null,-1))]),(0,a.Lk)("div",X,[(0,a.Lk)("div",S,[(0,a.bF)(oe,null,{default:(0,a.k6)(()=>[(0,a.bF)(ie)]),_:1})]),(0,a.Lk)("div",E,[(0,a.Lk)("div",x,(0,l.v_)(le.successscent.length),1),t[4]||(t[4]=(0,a.Lk)("div",{class:"stat-label"},"通过场景",-1))]),t[5]||(t[5]=(0,a.Lk)("div",{class:"stat-wave"},null,-1))]),(0,a.Lk)("div",D,[(0,a.Lk)("div",R,[(0,a.bF)(oe,null,{default:(0,a.k6)(()=>[(0,a.bF)(ue)]),_:1})]),(0,a.Lk)("div",W,[(0,a.Lk)("div",I,(0,l.v_)(le.failscent.length),1),t[6]||(t[6]=(0,a.Lk)("div",{class:"stat-label"},"失败场景",-1))]),t[7]||(t[7]=(0,a.Lk)("div",{class:"stat-wave"},null,-1))]),(0,a.Lk)("div",$,[(0,a.Lk)("div",A,[(0,a.bF)(oe,null,{default:(0,a.k6)(()=>[(0,a.bF)(de)]),_:1})]),(0,a.Lk)("div",T,[(0,a.Lk)("div",B,(0,l.v_)(le.errorscent.length),1),t[8]||(t[8]=(0,a.Lk)("div",{class:"stat-label"},"错误场景",-1))]),t[9]||(t[9]=(0,a.Lk)("div",{class:"stat-wave"},null,-1))])]),(0,a.Lk)("div",V,[(0,a.Lk)("div",Q,[t[10]||(t[10]=(0,a.Lk)("div",{class:"chart-header"},[(0,a.Lk)("h3",null,"执行统计"),(0,a.Lk)("div",{class:"chart-legend"},[(0,a.Lk)("span",{class:"legend-item success"},"通过"),(0,a.Lk)("span",{class:"legend-item fail"},"失败"),(0,a.Lk)("span",{class:"legend-item error"},"错误")])],-1)),(0,a.Lk)("div",q,null,512)]),(0,a.Lk)("div",G,[t[11]||(t[11]=(0,a.Lk)("div",{class:"chart-header"},[(0,a.Lk)("h3",null,"结果分布")],-1)),(0,a.Lk)("div",K,null,512)])])]),(0,a.Lk)("div",N,[(0,a.Lk)("div",O,[t[12]||(t[12]=(0,a.Lk)("h2",null,"测试场景",-1)),(0,a.Lk)("div",U,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ae.filterTabs,e=>((0,a.uX)(),(0,a.CE)("div",{key:e.value,class:(0,l.C4)(["filter-tab",{active:ae.currentFilter===e.value,[e.value]:!0}]),onClick:t=>le.handleFilterChange(e.value)},[(0,a.bF)(oe,{class:"tab-icon"},{default:(0,a.k6)(()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(e.icon)))]),_:2},1024),(0,a.Lk)("span",null,(0,l.v_)(e.label),1),(0,a.Lk)("span",z,(0,l.v_)(le.getTabCount(e.value)),1)],10,P))),128))])]),(0,a.bF)(ge,{class:"scene-list"},{default:(0,a.k6)(()=>[(0,a.bF)(r.F,{name:"scene-fade"},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ae.showScentDatas,(e,t)=>((0,a.uX)(),(0,a.CE)("div",{key:e.name+t,class:(0,l.C4)(["scene-card",{expanded:ae.expandedScenes.includes(t)}])},[(0,a.Lk)("div",{class:"scene-header",onClick:e=>le.toggleScene(t)},[(0,a.Lk)("div",M,[(0,a.bF)(oe,null,{default:(0,a.k6)(()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(le.getSceneIcon(e.state))))]),_:2},1024)]),(0,a.Lk)("div",j,[(0,a.Lk)("div",J,(0,l.v_)(e.name||"未命名场景"),1),(0,a.Lk)("div",{class:(0,l.C4)(["scene-state",le.getSceneStateClass(e.state)])},(0,l.v_)(le.getSceneStateText(e.state)),3)]),(0,a.Lk)("div",Y,[(0,a.bF)(oe,{class:(0,l.C4)({rotate:ae.expandedScenes.includes(t)})},{default:(0,a.k6)(()=>[(0,a.bF)(he)]),_:2},1032,["class"])])],8,H),(0,a.bo)((0,a.Lk)("div",Z,[(0,a.bF)(ve,{data:e.cases||[],class:"case-table",size:"small"},{default:(0,a.k6)(()=>[(0,a.bF)(pe,{type:"expand"},{default:(0,a.k6)(e=>[(0,a.Lk)("div",ee,[(0,a.bF)(ke,{result:e.row},null,8,["result"])])]),_:1}),(0,a.bF)(pe,{label:"用例名称","min-width":"200"},{default:(0,a.k6)(e=>[(0,a.Lk)("div",te,[(0,a.bF)(oe,null,{default:(0,a.k6)(()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(le.getCaseTypeIcon(e.row))))]),_:2},1024),(0,a.Lk)("span",null,(0,l.v_)(e.row.name||"未命名用例"),1)])]),_:1}),(0,a.bF)(pe,{label:"请求方法",width:"100",align:"center"},{default:(0,a.k6)(e=>["api"===e.row.type&&e.row.method?((0,a.uX)(),(0,a.CE)("div",{key:0,class:(0,l.C4)(["method-badge",e.row.method.toLowerCase()])},(0,l.v_)(e.row.method),3)):(0,a.Q3)("",!0)]),_:1}),(0,a.bF)(pe,{label:"状态码",width:"100",align:"center"},{default:(0,a.k6)(e=>["api"===e.row.type&&void 0!==e.row.status_cede?((0,a.uX)(),(0,a.CE)("div",{key:0,class:(0,l.C4)(["status-code",le.getStatusCodeClass(e.row.status_cede)])},(0,l.v_)(e.row.status_cede),3)):(0,a.Q3)("",!0)]),_:1}),(0,a.bF)(pe,{label:"结果",width:"100",align:"center"},{default:(0,a.k6)(e=>[e.row.state?((0,a.uX)(),(0,a.CE)("div",{key:0,class:(0,l.C4)(["result-badge",le.getCaseResultClass(e.row.state)])},(0,l.v_)(e.row.state),3)):(0,a.Q3)("",!0)]),_:1})]),_:2},1032,["data"])],512),[[r.aG,ae.expandedScenes.includes(t)]])],2))),128))]),_:1})]),_:1})])])]),_:1})])):(0,a.Q3)("",!0)])):(0,a.Q3)("",!0)}s(44114),s(18111),s(22489);var ae=s(67638),le=s(57477),re=s(88559),oe=s(51219),ne={name:"TestReport",components:{caseRes:ae.A,Back:le.Back,DataAnalysis:le.DataAnalysis,SuccessFilled:le.SuccessFilled,Warning:le.Warning,CircleCloseFilled:le.CircleCloseFilled,ArrowRight:le.ArrowRight,Grid:le.Grid,Connection:le.Connection,Cpu:le.Cpu,ElScrollbar:re.kA},data(){return{record:null,report:null,showScentDatas:[],currentFilter:"all",expandedScenes:[],filterTabs:[{label:"全部场景",value:"all",icon:"Grid"},{label:"通过场景",value:"success",icon:"SuccessFilled"},{label:"失败场景",value:"fail",icon:"Warning"},{label:"错误场景",value:"error",icon:"CircleCloseFilled"}]}},computed:{successscent(){return this.report&&this.report.results?this.report.results.filter(e=>"success"===e.state):[]},failscent(){return this.report&&this.report.results?this.report.results.filter(e=>"fail"===e.state):[]},errorscent(){return this.report&&this.report.results?this.report.results.filter(e=>"error"===e.state):[]},getPassRateClass(){if(!this.record||!this.record.pass_rate)return"poor";const e=parseFloat(this.record.pass_rate);return e>=90?"excellent":e>=70?"good":e>=50?"fair":"poor"}},methods:{goBack(){window.history.back()},async getReportInfo(e){try{console.log("获取报告信息, ID:",e);const t=await this.$api.getTestReport(e);console.log("报告API响应:",t),200===t.status&&(t.data&&t.data.results?this.report=t.data:t.data&&t.data.info&&(this.report=t.data.info),console.log("处理后的报告数据:",this.report),this.report&&this.report.results?(this.handleFilterChange("all"),console.log("过滤后的场景数据:",this.showScentDatas)):(console.error("报告数据结构不正确"),oe.nk.error("报告数据结构不正确")))}catch(t){console.error("获取报告信息失败:",t),oe.nk.error("获取报告信息失败")}},async getRecordInfo(e){try{console.log("获取记录信息, ID:",e);const t=await this.$api.getRecordInfo(e);console.log("记录API响应:",t),200===t.status&&t.data&&(this.record=t.data,console.log("处理后的记录数据:",this.record))}catch(t){console.error("获取记录信息失败:",t),oe.nk.error("获取记录信息失败")}},chart1(){if(!this.report||!this.$refs.chart1)return void console.log("无法渲染图表1 - 数据或DOM不存在");const e=[this.report.all||0,this.report.success||0,this.report.fail||0,this.report.error||0],t=["用例总数","通过用例","失败用例","错误用例"];console.log("图表1数据:",{value:e,label:t}),this.$chart.chart1(this.$refs.chart1,e,t)},chart2(){if(!this.report||!this.$refs.chart2)return void console.log("无法渲染图表2 - 数据或DOM不存在");const e=[{value:this.report.success||0,name:"通过"},{value:this.report.fail||0,name:"失败"},{value:this.report.error||0,name:"错误"}];console.log("图表2数据:",e),this.$chart.chart2(this.$refs.chart2,e)},handleFilterChange(e){if(!this.report||!this.report.results||!Array.isArray(this.report.results))return console.error("没有场景数据或数据格式不正确"),void(this.showScentDatas=[]);this.currentFilter=e,console.log("切换场景过滤:",e),"all"===e?this.showScentDatas=[...this.report.results]:"success"===e?this.showScentDatas=[...this.successscent]:"fail"===e?this.showScentDatas=[...this.failscent]:"error"===e&&(this.showScentDatas=[...this.errorscent]),console.log("过滤后场景数量:",this.showScentDatas.length),this.expandedScenes=[]},toggleScene(e){const t=this.expandedScenes.indexOf(e);-1===t?this.expandedScenes.push(e):this.expandedScenes.splice(t,1)},getTabCount(e){return this.report&&this.report.results?"all"===e?this.report.results.length:"success"===e?this.successscent.length:"fail"===e?this.failscent.length:this.errorscent.length:0},getSceneIcon(e){return"success"===e?"SuccessFilled":"fail"===e?"Warning":"CircleCloseFilled"},getSceneStateClass(e){return`state-${e}`},getSceneStateText(e){const t={success:"通过",fail:"失败",error:"错误"};return t[e]||e},getCaseTypeIcon(e){return e&&e.type&&"api"===e.type?"Connection":"Cpu"},getStatusCodeClass(e){return e=parseInt(e||0),e>=200&&e<300?"success":e>=300&&e<400?"redirect":e>=400&&e<500?"client-error":"server-error"},getCaseResultClass(e){return e?"成功"===e?"success":"失败"===e?"fail":"error":"error"}},async created(){try{console.log("组件创建");const e=this.$route.params.id;e?(console.log("报告ID:",e),await this.getReportInfo(e),await this.getRecordInfo(e)):(console.error("没有找到报告ID"),oe.nk.error("没有找到报告ID"))}catch(e){console.error("初始化报告失败:",e),oe.nk.error("初始化报告失败")}},mounted(){this.$refs.chart1&&this.chart1(),this.$refs.chart2&&this.chart2()},updated(){this.$refs.chart1&&this.chart1(),this.$refs.chart2&&this.chart2()}},ce=s(71241);const ie=(0,ce.A)(ne,[["render",se],["__scopeId","data-v-cf72507a"]]);var ue=ie},67638:function(e,t,s){s.d(t,{A:function(){return S}});var a=s(56768),l=s(45130),r=s(24232);const o={key:0},n={key:0},c={key:1},i={key:0,class:"tab-box-sli"},u={style:{color:"#747474"}},d={key:0},h={class:"tab-box-sli"},k={key:4,style:{color:"#d60000"}},p={key:0,style:{color:"#00AA7F"}},v={key:1,style:{color:"#d18d17"}},g={key:2,style:{color:"#ff0000"}},b={key:0,style:{color:"#00AA7F"}},f={key:1,style:{color:"#ff5500"}},_={key:0,style:{"margin-top":"10px",width:"100%","text-align":"center"}},m={class:"dialog-footer"};function y(e,t,s,y,C,F){const L=(0,a.g2)("Editor"),w=(0,a.g2)("el-scrollbar"),X=(0,a.g2)("el-tab-pane"),S=(0,a.g2)("el-tag"),E=(0,a.g2)("el-collapse-item"),x=(0,a.g2)("el-collapse"),D=(0,a.g2)("el-tabs"),R=(0,a.g2)("el-button"),W=(0,a.g2)("el-option"),I=(0,a.g2)("el-select"),$=(0,a.g2)("el-form-item"),A=(0,a.g2)("el-input"),T=(0,a.g2)("el-form"),B=(0,a.g2)("el-dialog");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.bF)(D,{"model-value":"rb",style:{"min-height":"300px"},type:"border-card",value:"rb",size:"mini"},{default:(0,a.k6)(()=>["api"==s.result.type?((0,a.uX)(),(0,a.Wv)(X,{key:0,label:"响应体",name:"rb"},{default:(0,a.k6)(()=>[s.result.response_header?((0,a.uX)(),(0,a.CE)("div",o,[s.result.response_header["Content-Type"].includes("application/json")?((0,a.uX)(),(0,a.CE)("div",n,[(0,a.bF)(L,{readOnly:!0,modelValue:s.result.response_body,"onUpdate:modelValue":t[0]||(t[0]=e=>s.result.response_body=e),lang:"json",theme:"chrome"},null,8,["modelValue"])])):((0,a.uX)(),(0,a.CE)("div",c,[(0,a.bF)(w,{height:"400px",onWheel:t[1]||(t[1]=(0,l.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>[(0,a.bF)(L,{readOnly:!0,innerHTML:s.result.response_body,lang:"html",theme:"chrome",height:"400px"},null,8,["innerHTML"])]),_:1})]))])):(0,a.Q3)("",!0)]),_:1})):(0,a.Q3)("",!0),"api"==s.result.type?((0,a.uX)(),(0,a.Wv)(X,{key:1,label:"响应头",name:"rh"},{default:(0,a.k6)(()=>[(0,a.bF)(w,{height:"400px",onWheel:t[2]||(t[2]=(0,l.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>[s.result.response_header?((0,a.uX)(),(0,a.CE)("div",i,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(s.result.response_header,(e,t)=>((0,a.uX)(),(0,a.CE)("div",null,[(0,a.bF)(S,{style:{"margin-top":"3px"},type:"info"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",u,(0,r.v_)(t+" : "),1),(0,a.Lk)("span",null,(0,r.v_)(e),1)]),_:2},1024)]))),256))])):(0,a.Q3)("",!0)]),_:1})]),_:1})):(0,a.Q3)("",!0),"api"==s.result.type?((0,a.uX)(),(0,a.Wv)(X,{key:2,label:"请求信息",name:"rq"},{default:(0,a.k6)(()=>[(0,a.bF)(w,{height:"400px",onWheel:t[4]||(t[4]=(0,l.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>[s.result.requests_body?((0,a.uX)(),(0,a.CE)("div",d,[(0,a.bF)(x,{modelValue:C.activeNames,"onUpdate:modelValue":t[3]||(t[3]=e=>C.activeNames=e),class:"tab-box-sli"},{default:(0,a.k6)(()=>[(0,a.bF)(E,{name:"1"},{title:(0,a.k6)(()=>t[9]||(t[9]=[(0,a.Lk)("b",null,"General",-1)])),default:(0,a.k6)(()=>[(0,a.Lk)("div",null,"Request Method : "+(0,r.v_)(s.result.method),1),(0,a.Lk)("div",null,"Request URL : "+(0,r.v_)(s.result.url),1)]),_:1}),(0,a.bF)(E,{name:"2"},{title:(0,a.k6)(()=>t[10]||(t[10]=[(0,a.Lk)("b",null,"Request Headers",-1)])),default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(s.result.requests_header,(e,t)=>((0,a.uX)(),(0,a.CE)("div",null,[(0,a.Lk)("span",null,(0,r.v_)(t+" : "+e),1)]))),256))]),_:1}),(0,a.bF)(E,{name:"3"},{title:(0,a.k6)(()=>t[11]||(t[11]=[(0,a.Lk)("b",null,"Request Payload",-1)])),default:(0,a.k6)(()=>[(0,a.Lk)("span",null,(0,r.v_)(s.result.requests_body),1)]),_:1})]),_:1},8,["modelValue"])])):(0,a.Q3)("",!0)]),_:1})]),_:1})):(0,a.Q3)("",!0),(0,a.bF)(X,{label:"日志"},{default:(0,a.k6)(()=>[(0,a.bF)(w,{height:"400px",onWheel:t[5]||(t[5]=(0,l.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>[(0,a.Lk)("div",h,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(s.result.log_data,(e,t)=>((0,a.uX)(),(0,a.CE)("div",null,["DEBUG"===e[0]?((0,a.uX)(),(0,a.Wv)(S,{key:0,style:{"margin-top":"3px"}},{default:(0,a.k6)(()=>[(0,a.eW)((0,r.v_)(e[1]),1)]),_:2},1024)):"WARNING"===e[0]?((0,a.uX)(),(0,a.Wv)(S,{key:1,style:{"margin-top":"3px"},type:"warning"},{default:(0,a.k6)(()=>[(0,a.eW)((0,r.v_)(e[1]),1)]),_:2},1024)):"ERROR"===e[0]?((0,a.uX)(),(0,a.Wv)(S,{key:2,style:{"margin-top":"3px"},type:"danger"},{default:(0,a.k6)(()=>[(0,a.eW)((0,r.v_)(e[1]),1)]),_:2},1024)):"INFO"===e[0]?((0,a.uX)(),(0,a.Wv)(S,{key:3,style:{"margin-top":"3px"},type:"success"},{default:(0,a.k6)(()=>[(0,a.eW)((0,r.v_)(e[1]),1)]),_:2},1024)):"EXCEPT"===e[0]?((0,a.uX)(),(0,a.CE)("pre",k,(0,r.v_)(e[1]),1)):(0,a.Q3)("",!0)]))),256))])]),_:1})]),_:1}),(0,a.bF)(X,{disabled:""},{label:(0,a.k6)(()=>["成功"===s.result.state?((0,a.uX)(),(0,a.CE)("span",p,(0,r.v_)("Assert : "+s.result.state),1)):"失败"===s.result.state?((0,a.uX)(),(0,a.CE)("span",v,(0,r.v_)("Assert : "+s.result.state),1)):((0,a.uX)(),(0,a.CE)("span",g,(0,r.v_)(s.result.state),1))]),_:1}),"api"==s.result.type?((0,a.uX)(),(0,a.Wv)(X,{key:3,disabled:""},{label:(0,a.k6)(()=>[s.result.status_cede<=300?((0,a.uX)(),(0,a.CE)("span",b,(0,r.v_)("Status : "+s.result.status_cede),1)):((0,a.uX)(),(0,a.CE)("span",f,(0,r.v_)("Status : "+s.result.status_cede),1))]),_:1})):(0,a.Q3)("",!0),(0,a.bF)(X,{disabled:""},{label:(0,a.k6)(()=>[(0,a.eW)((0,r.v_)("Time : "+s.result.run_time),1)]),_:1})]),_:1}),"失败"===s.result.state&&s.showbtn?((0,a.uX)(),(0,a.CE)("div",_,[(0,a.bF)(R,{onClick:F.getInterfaces,type:"success",plain:"",size:"mini"},{default:(0,a.k6)(()=>t[12]||(t[12]=[(0,a.eW)("提交bug")])),_:1,__:[12]},8,["onClick"])])):(0,a.Q3)("",!0),(0,a.bF)(B,{title:"提交bug",modelValue:C.addBugDlg,"onUpdate:modelValue":t[8]||(t[8]=e=>C.addBugDlg=e),width:"40%","before-close":F.closeDialogResult},{footer:(0,a.k6)(()=>[(0,a.Lk)("div",m,[(0,a.bF)(R,{onClick:F.closeDialogResult},{default:(0,a.k6)(()=>t[13]||(t[13]=[(0,a.eW)("取 消")])),_:1,__:[13]},8,["onClick"]),(0,a.bF)(R,{type:"success",onClick:F.saveBug},{default:(0,a.k6)(()=>t[14]||(t[14]=[(0,a.eW)("确 定")])),_:1,__:[14]},8,["onClick"])])]),default:(0,a.k6)(()=>[(0,a.bF)(T,{model:C.bugForm},{default:(0,a.k6)(()=>[(0,a.bF)($,{label:"所属接口"},{default:(0,a.k6)(()=>[(0,a.bF)(I,{size:"small",modelValue:C.bugForm.interface,"onUpdate:modelValue":t[6]||(t[6]=e=>C.bugForm.interface=e),placeholder:"bug对应的接口",style:{width:"100%"}},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(C.interfaces,e=>((0,a.uX)(),(0,a.Wv)(W,{label:e.name+" "+e.url,value:e.id,key:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)($,{label:"bug描述"},{default:(0,a.k6)(()=>[(0,a.bF)(A,{autosize:{minRows:3,maxRows:4},modelValue:C.bugForm.desc,"onUpdate:modelValue":t[7]||(t[7]=e=>C.bugForm.desc=e),type:"textarea",autocomplete:"off"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","before-close"])],64)}var C=s(53629),F=s(60782),L={props:{result:{default:{}},showbtn:{default:!0}},computed:{...(0,F.aH)(["pro"])},components:{Editor:C.A},data(){return{activeNames:["1","2","3"],addBugDlg:!1,bugForm:{interface:null,desc:"",info:"",status:"待处理"},interfaces:[]}},methods:{async saveBug(){this.bugForm.project=this.pro.id,this.bugForm.info=this.result;const e=await this.$api.createBugs(this.bugForm);201===e.status&&(this.$message({type:"success",message:"bug提交成功",duration:1e3}),this.addBugDlg=!1,this.bugForm={interface:null,desc:"",info:"",status:"待处理"})},closeDialogResult(){this.addBugDlg=!1,this.bugForm={interface:null,desc:"",info:"",status:"待处理"}},async getInterfaces(){const e=await this.$api.getNewInterfaces();200===e.status&&(this.interfaces=e.data,this.addBugDlg=!0)}}},w=s(71241);const X=(0,w.A)(L,[["render",y]]);var S=X}}]);
//# sourceMappingURL=542.f8adc9e4.js.map