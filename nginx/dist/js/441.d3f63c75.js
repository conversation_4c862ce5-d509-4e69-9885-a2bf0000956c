"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[441],{72441:function(t,e,s){s.r(e),s.d(e,{default:function(){return L}});var n=s(56768),o=s(24232);const a={class:"performance-monitor"},i={class:"monitor-header"},r={class:"header-info"},c={class:"connection-status"},h={class:"monitor-controls"},l={class:"monitor-panels"},d={class:"card-header"},u={ref:"tpsChart",class:"chart-container"},p={class:"card-header"},m={ref:"responseChart",class:"chart-container"},k={class:"card-header"},b={ref:"cpuChart",class:"chart-container"},y={class:"card-header"},g={ref:"memoryChart",class:"chart-container"},f={class:"card-header"},S={ref:"networkChart",class:"chart-container"},C={class:"card-header"};function w(t,e,s,w,T,v){const _=(0,n.g2)("Connection"),D=(0,n.g2)("el-icon"),W=(0,n.g2)("el-tag"),F=(0,n.g2)("VideoPlay"),x=(0,n.g2)("VideoPause"),L=(0,n.g2)("el-button"),R=(0,n.g2)("Refresh"),P=(0,n.g2)("el-button-group"),A=(0,n.g2)("el-card"),M=(0,n.g2)("el-col"),O=(0,n.g2)("el-row"),E=(0,n.g2)("el-table-column"),I=(0,n.g2)("el-table");return(0,n.uX)(),(0,n.CE)("div",a,[(0,n.Lk)("div",i,[(0,n.Lk)("div",r,[e[0]||(e[0]=(0,n.Lk)("h2",null,"性能实时监控",-1)),(0,n.Lk)("div",c,[(0,n.bF)(W,{type:"connected"===T.connectionStatus?"success":"danger",effect:"dark"},{default:(0,n.k6)(()=>[(0,n.bF)(D,null,{default:(0,n.k6)(()=>[(0,n.bF)(_)]),_:1}),(0,n.eW)(" "+(0,o.v_)("connected"===T.connectionStatus?"已连接":"未连接"),1)]),_:1},8,["type"])])]),(0,n.Lk)("div",h,[(0,n.bF)(P,null,{default:(0,n.k6)(()=>[(0,n.bF)(L,{type:T.isMonitoring?"danger":"primary",onClick:v.toggleMonitoring,loading:T.loading},{default:(0,n.k6)(()=>[(0,n.bF)(D,null,{default:(0,n.k6)(()=>[T.isMonitoring?((0,n.uX)(),(0,n.Wv)(x,{key:1})):((0,n.uX)(),(0,n.Wv)(F,{key:0}))]),_:1}),(0,n.eW)(" "+(0,o.v_)(T.isMonitoring?"停止监控":"开始监控"),1)]),_:1},8,["type","onClick","loading"]),(0,n.bF)(L,{onClick:v.refreshData},{default:(0,n.k6)(()=>[(0,n.bF)(D,null,{default:(0,n.k6)(()=>[(0,n.bF)(R)]),_:1}),e[1]||(e[1]=(0,n.eW)(" 刷新 "))]),_:1,__:[1]},8,["onClick"])]),_:1})])]),(0,n.Lk)("div",l,[(0,n.bF)(O,{gutter:20},{default:(0,n.k6)(()=>[(0,n.bF)(M,{span:12},{default:(0,n.k6)(()=>[(0,n.bF)(A,{class:"monitor-card"},{header:(0,n.k6)(()=>[(0,n.Lk)("div",d,[e[2]||(e[2]=(0,n.Lk)("span",null,"TPS 趋势",-1)),(0,n.bF)(W,{size:"small"},{default:(0,n.k6)(()=>[(0,n.eW)((0,o.v_)(T.currentData.avgTps||0),1)]),_:1})])]),default:(0,n.k6)(()=>[(0,n.Lk)("div",u,null,512)]),_:1})]),_:1}),(0,n.bF)(M,{span:12},{default:(0,n.k6)(()=>[(0,n.bF)(A,{class:"monitor-card"},{header:(0,n.k6)(()=>[(0,n.Lk)("div",p,[e[3]||(e[3]=(0,n.Lk)("span",null,"响应时间",-1)),(0,n.bF)(W,{size:"small"},{default:(0,n.k6)(()=>[(0,n.eW)((0,o.v_)(T.currentData.avgResponseTime||0)+"ms",1)]),_:1})])]),default:(0,n.k6)(()=>[(0,n.Lk)("div",m,null,512)]),_:1})]),_:1})]),_:1}),(0,n.bF)(O,{gutter:20,style:{"margin-top":"20px"}},{default:(0,n.k6)(()=>[(0,n.bF)(M,{span:8},{default:(0,n.k6)(()=>[(0,n.bF)(A,{class:"monitor-card"},{header:(0,n.k6)(()=>[(0,n.Lk)("div",k,[e[4]||(e[4]=(0,n.Lk)("span",null,"CPU 使用率",-1)),(0,n.bF)(W,{size:"small"},{default:(0,n.k6)(()=>[(0,n.eW)((0,o.v_)(T.currentSystem.cpu?.percent?.toFixed(1)||0)+"%",1)]),_:1})])]),default:(0,n.k6)(()=>[(0,n.Lk)("div",b,null,512)]),_:1})]),_:1}),(0,n.bF)(M,{span:8},{default:(0,n.k6)(()=>[(0,n.bF)(A,{class:"monitor-card"},{header:(0,n.k6)(()=>[(0,n.Lk)("div",y,[e[5]||(e[5]=(0,n.Lk)("span",null,"内存使用率",-1)),(0,n.bF)(W,{size:"small"},{default:(0,n.k6)(()=>[(0,n.eW)((0,o.v_)(T.currentSystem.memory?.percent?.toFixed(1)||0)+"%",1)]),_:1})])]),default:(0,n.k6)(()=>[(0,n.Lk)("div",g,null,512)]),_:1})]),_:1}),(0,n.bF)(M,{span:8},{default:(0,n.k6)(()=>[(0,n.bF)(A,{class:"monitor-card"},{header:(0,n.k6)(()=>[(0,n.Lk)("div",f,[e[6]||(e[6]=(0,n.Lk)("span",null,"网络流量",-1)),(0,n.bF)(W,{size:"small"},{default:(0,n.k6)(()=>[(0,n.eW)((0,o.v_)(v.formatBytes(T.currentSystem.network?.bytes_recv||0)),1)]),_:1})])]),default:(0,n.k6)(()=>[(0,n.Lk)("div",S,null,512)]),_:1})]),_:1})]),_:1}),(0,n.bF)(O,{style:{"margin-top":"20px"}},{default:(0,n.k6)(()=>[(0,n.bF)(M,{span:24},{default:(0,n.k6)(()=>[(0,n.bF)(A,{class:"monitor-card"},{header:(0,n.k6)(()=>[(0,n.Lk)("div",C,[e[8]||(e[8]=(0,n.Lk)("span",null,"实时数据",-1)),(0,n.bF)(L,{size:"small",onClick:v.clearData},{default:(0,n.k6)(()=>e[7]||(e[7]=[(0,n.eW)("清空数据")])),_:1,__:[7]},8,["onClick"])])]),default:(0,n.k6)(()=>[(0,n.bF)(I,{data:T.recentData.slice(-10),size:"small",stripe:""},{default:(0,n.k6)(()=>[(0,n.bF)(E,{prop:"timestamp",label:"时间",width:"200"},{default:(0,n.k6)(({row:t})=>[(0,n.eW)((0,o.v_)(new Date(t.timestamp).toLocaleTimeString()),1)]),_:1}),(0,n.bF)(E,{prop:"avgTps",label:"TPS",width:"100"}),(0,n.bF)(E,{prop:"avgResponseTime",label:"响应时间(ms)",width:"120"}),(0,n.bF)(E,{prop:"totalRequests",label:"总请求数",width:"120"}),(0,n.bF)(E,{prop:"errorRate",label:"错误率(%)",width:"100"}),(0,n.bF)(E,{prop:"cpuPercent",label:"CPU(%)",width:"100"}),(0,n.bF)(E,{prop:"memoryPercent",label:"内存(%)",width:"100"})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1})])])}s(44114),s(18111),s(7588),s(61701);var T=s(51219),v=s(60782),_=s(57477),D=s(91006),W={name:"PerformanceMonitor",components:{Connection:_.Connection,VideoPlay:_.VideoPlay,VideoPause:_.VideoPause,Refresh:_.Refresh},data(){return{websocket:null,connectionStatus:"disconnected",isMonitoring:!1,loading:!1,currentData:{},currentSystem:{},recentData:[],charts:{},maxDataPoints:50,reconnectAttempts:0,maxReconnectAttempts:5,reconnectInterval:5e3,reconnectTimer:null,heartbeatTimer:null,heartbeatInterval:3e4,isComponentDestroyed:!1,connectionRetryBackoff:[1e3,2e3,5e3,1e4,3e4]}},computed:{...(0,v.aH)({currentTaskId:t=>t.currentTaskId||(void 0).$route.params.taskId})},mounted(){this.initCharts(),this.connectWebSocket(),document.addEventListener("visibilitychange",this.handleVisibilityChange),window.addEventListener("focus",this.handleWindowFocus),window.addEventListener("blur",this.handleWindowBlur)},beforeUnmount(){this.isComponentDestroyed=!0,this.cleanupWebSocket(),this.clearAllTimers(),this.destroyCharts(),document.removeEventListener("visibilitychange",this.handleVisibilityChange),window.removeEventListener("focus",this.handleWindowFocus),window.removeEventListener("blur",this.handleWindowBlur)},activated(){"disconnected"!==this.connectionStatus||this.isComponentDestroyed||this.connectWebSocket()},deactivated(){this.pauseConnection()},methods:{initCharts(){this.$nextTick(()=>{this.initTpsChart(),this.initResponseChart(),this.initCpuChart(),this.initMemoryChart(),this.initNetworkChart()})},initTpsChart(){const t=this.$refs.tpsChart;if(!t)return;this.charts.tps=D.Ts(t);const e={title:{text:"TPS 趋势",left:"center"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:[]},yAxis:{type:"value",name:"TPS"},series:[{name:"TPS",type:"line",smooth:!0,data:[],lineStyle:{color:"#409EFF"},areaStyle:{color:"rgba(64, 158, 255, 0.3)"}}]};this.charts.tps.setOption(e)},initResponseChart(){const t=this.$refs.responseChart;if(!t)return;this.charts.response=D.Ts(t);const e={title:{text:"响应时间",left:"center"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:[]},yAxis:{type:"value",name:"毫秒"},series:[{name:"平均响应时间",type:"line",smooth:!0,data:[],lineStyle:{color:"#67C23A"}}]};this.charts.response.setOption(e)},initCpuChart(){const t=this.$refs.cpuChart;if(!t)return;this.charts.cpu=D.Ts(t);const e={tooltip:{trigger:"axis"},xAxis:{type:"category",data:[]},yAxis:{type:"value",name:"%",max:100},series:[{name:"CPU",type:"line",smooth:!0,data:[],lineStyle:{color:"#E6A23C"},areaStyle:{color:"rgba(230, 162, 60, 0.3)"}}]};this.charts.cpu.setOption(e)},initMemoryChart(){const t=this.$refs.memoryChart;if(!t)return;this.charts.memory=D.Ts(t);const e={tooltip:{trigger:"axis"},xAxis:{type:"category",data:[]},yAxis:{type:"value",name:"%",max:100},series:[{name:"内存",type:"line",smooth:!0,data:[],lineStyle:{color:"#F56C6C"},areaStyle:{color:"rgba(245, 108, 108, 0.3)"}}]};this.charts.memory.setOption(e)},initNetworkChart(){const t=this.$refs.networkChart;if(!t)return;this.charts.network=D.Ts(t);const e={tooltip:{trigger:"axis"},xAxis:{type:"category",data:[]},yAxis:{type:"value",name:"MB"},series:[{name:"接收",type:"line",smooth:!0,data:[],lineStyle:{color:"#909399"}},{name:"发送",type:"line",smooth:!0,data:[],lineStyle:{color:"#909399",type:"dashed"}}]};this.charts.network.setOption(e)},connectWebSocket(){if(this.isComponentDestroyed)return;if(!this.currentTaskId)return void T.nk.error("未找到任务ID");this.cleanupWebSocket();const t="https:"===window.location.protocol?"wss:":"ws:",e=`${t}//${window.location.host}/ws/performance/monitor/${this.currentTaskId}/`;try{this.websocket=new WebSocket(e),this.websocket.onopen=this.handleWebSocketOpen,this.websocket.onmessage=this.handleWebSocketMessage,this.websocket.onclose=this.handleWebSocketClose,this.websocket.onerror=this.handleWebSocketError,this.connectionTimeout=setTimeout(()=>{this.websocket&&this.websocket.readyState===WebSocket.CONNECTING&&(this.websocket.close(),this.handleConnectionTimeout())},1e4)}catch(s){console.error("WebSocket连接创建失败:",s),this.handleConnectionError(s)}},handleWebSocketOpen(){this.isComponentDestroyed||(this.connectionStatus="connected",this.reconnectAttempts=0,this.clearReconnectTimer(),T.nk.success("WebSocket连接成功"),this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),this.startHeartbeat(),this.getCurrentStatus())},handleWebSocketMessage(t){if(!this.isComponentDestroyed)try{const e=JSON.parse(t.data);this.processWebSocketMessage(e)}catch(e){console.error("WebSocket消息解析失败:",e)}},handleWebSocketClose(t){this.isComponentDestroyed||(this.connectionStatus="disconnected",this.isMonitoring=!1,this.stopHeartbeat(),this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),console.log("WebSocket连接关闭:",t.code,t.reason),1e3!==t.code&&1001!==t.code&&(T.nk.warning("WebSocket连接断开"),this.attemptReconnect()))},handleWebSocketError(t){this.isComponentDestroyed||(console.error("WebSocket错误:",t),this.connectionStatus="disconnected",this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null))},handleConnectionTimeout(){console.warn("WebSocket连接超时"),T.nk.error("WebSocket连接超时"),this.connectionStatus="disconnected",this.attemptReconnect()},handleConnectionError(t){console.error("WebSocket连接错误:",t),T.nk.error("WebSocket连接错误"),this.connectionStatus="disconnected",this.attemptReconnect()},processWebSocketMessage(t){if("pong"!==t.type)switch(t.type){case"connection_established":console.log("连接建立:",t.message);break;case"current_status":this.updateCurrentData(t.data);break;case"performance_update":this.updatePerformanceData(t.data);break;case"test_started":T.nk.success("性能测试已开始");break;case"test_completed":T.nk.success("性能测试已完成"),this.isMonitoring=!1;break;case"test_failed":T.nk.error("性能测试失败: "+t.error),this.isMonitoring=!1;break;case"error":T.nk.error(t.message);break;default:console.warn("未知的WebSocket消息类型:",t.type)}else this.lastPongTime=Date.now()},attemptReconnect(){if(this.isComponentDestroyed||this.reconnectAttempts>=this.maxReconnectAttempts)return void(this.reconnectAttempts>=this.maxReconnectAttempts&&T.nk.error("WebSocket重连失败，已达到最大重试次数"));this.clearReconnectTimer();const t=Math.min(this.reconnectAttempts,this.connectionRetryBackoff.length-1),e=this.connectionRetryBackoff[t];console.log(`准备第 ${this.reconnectAttempts+1} 次重连，延迟 ${e}ms`),this.reconnectTimer=setTimeout(()=>{this.isComponentDestroyed||(this.reconnectAttempts++,this.connectWebSocket())},e)},startHeartbeat(){this.stopHeartbeat(),this.heartbeatTimer=setInterval(()=>{this.websocket&&this.websocket.readyState===WebSocket.OPEN&&this.websocket.send(JSON.stringify({type:"ping"}))},this.heartbeatInterval)},stopHeartbeat(){this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null)},clearReconnectTimer(){this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null)},clearAllTimers(){this.clearReconnectTimer(),this.stopHeartbeat(),this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null)},cleanupWebSocket(){this.websocket&&(this.websocket.onopen=null,this.websocket.onmessage=null,this.websocket.onclose=null,this.websocket.onerror=null,this.websocket.readyState!==WebSocket.OPEN&&this.websocket.readyState!==WebSocket.CONNECTING||this.websocket.close(1e3,"Component cleanup"),this.websocket=null),this.stopHeartbeat(),this.clearReconnectTimer()},pauseConnection(){this.websocket&&this.websocket.close(1e3,"Component deactivated"),this.stopHeartbeat(),this.clearReconnectTimer()},handleVisibilityChange(){this.isComponentDestroyed||(document.hidden?this.pauseConnection():"disconnected"===this.connectionStatus&&(this.reconnectAttempts=0,this.connectWebSocket()))},handleWindowFocus(){this.isComponentDestroyed||"disconnected"===this.connectionStatus&&(this.reconnectAttempts=0,this.connectWebSocket())},handleWindowBlur(){console.log("窗口失去焦点")},disconnectWebSocket(){this.cleanupWebSocket()},updateCurrentData(t){if(!t||this.isComponentDestroyed)return;this.currentData={...t};const e=(new Date).toISOString(),s={timestamp:e,avgTps:t.avg_tps||0,avgResponseTime:t.avgResponseTime||0,totalRequests:t.total_requests||0,errorRate:t.error_rate||0,cpuPercent:this.currentSystem.cpu?.percent||0,memoryPercent:this.currentSystem.memory?.percent||0,networkRecv:this.currentSystem.network?.bytes_recv||0,networkSent:this.currentSystem.network?.bytes_sent||0};this.addDataPoint(s),this.scheduleChartUpdate()},updatePerformanceData(t){t&&!this.isComponentDestroyed&&("system_resources"===t.type?this.currentSystem={...t.data}:this.currentData={...this.currentData,...t},this.scheduleChartUpdate())},addDataPoint(t){if(this.recentData.push(t),this.recentData.length>this.maxDataPoints){const t=this.recentData.length-this.maxDataPoints;this.recentData.splice(0,t)}},scheduleChartUpdate(){this.chartUpdateTimer&&clearTimeout(this.chartUpdateTimer),this.chartUpdateTimer=setTimeout(()=>{this.isComponentDestroyed||this.updateCharts()},100)},updateCharts(){if(this.isComponentDestroyed||!this.recentData.length)return;const t=this.recentData.map(t=>new Date(t.timestamp).toLocaleTimeString());this.updateChartsInBatch(t)},updateChartsInBatch(t){const e=[{chart:this.charts.tps,data:this.recentData.map(t=>t.avgTps)},{chart:this.charts.response,data:this.recentData.map(t=>t.avgResponseTime)},{chart:this.charts.cpu,data:this.recentData.map(t=>t.cpuPercent)},{chart:this.charts.memory,data:this.recentData.map(t=>t.memoryPercent)}];e.forEach(({chart:e,data:s})=>{if(e&&e.setOption)try{e.setOption({xAxis:{data:t},series:[{data:s}]},!1,!0)}catch(n){console.warn("图表更新失败:",n)}}),this.updateNetworkChart()},updateNetworkChart(){if(!this.charts.network)return;const t=this.recentData.map(t=>new Date(t.timestamp).toLocaleTimeString()),e=this.recentData.map(t=>{const e=t.networkRecv||this.currentSystem.network?.bytes_recv||0;return(e/1024/1024).toFixed(2)}),s=this.recentData.map(t=>{const e=t.networkSent||this.currentSystem.network?.bytes_sent||0;return(e/1024/1024).toFixed(2)});this.charts.network.setOption({xAxis:{data:t},series:[{data:e},{data:s}]})},toggleMonitoring(){if("connected"!==this.connectionStatus)return void T.nk.error("WebSocket未连接");this.loading=!0;const t=this.isMonitoring?"stop_monitoring":"start_monitoring";this.websocket.send(JSON.stringify({type:t})),setTimeout(()=>{this.isMonitoring=!this.isMonitoring,this.loading=!1},1e3)},getCurrentStatus(){"connected"===this.connectionStatus&&this.websocket.send(JSON.stringify({type:"get_current_status"}))},refreshData(){this.getCurrentStatus(),T.nk.success("数据已刷新")},clearData(){this.recentData.length=0,this.currentData={},this.currentSystem={},this.updateCharts(),T.nk.success("数据已清空")},formatBytes(t){if(0===t)return"0 B";const e=1024,s=["B","KB","MB","GB"],n=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,n)).toFixed(2))+" "+s[n]},destroyCharts(){this.chartUpdateTimer&&(clearTimeout(this.chartUpdateTimer),this.chartUpdateTimer=null),Object.keys(this.charts).forEach(t=>{const e=this.charts[t];if(e&&"function"===typeof e.dispose)try{e.dispose()}catch(s){console.warn(`销毁图表 ${t} 时出错:`,s)}}),this.charts={}}}},F=s(71241);const x=(0,F.A)(W,[["render",w],["__scopeId","data-v-e2def50a"]]);var L=x}}]);
//# sourceMappingURL=441.d3f63c75.js.map