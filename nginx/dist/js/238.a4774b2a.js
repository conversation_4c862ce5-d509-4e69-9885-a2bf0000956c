"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[238],{67638:function(e,t,a){a.d(t,{A:function(){return R}});var l=a(56768),s=a(45130),r=a(24232);const n={key:0},i={key:0},o={key:1},d={key:0,class:"tab-box-sli"},u={style:{color:"#747474"}},c={key:0},p={class:"tab-box-sli"},h={key:4,style:{color:"#d60000"}},g={key:0,style:{color:"#00AA7F"}},k={key:1,style:{color:"#d18d17"}},m={key:2,style:{color:"#ff0000"}},f={key:0,style:{color:"#00AA7F"}},b={key:1,style:{color:"#ff5500"}},v={key:0,style:{"margin-top":"10px",width:"100%","text-align":"center"}},C={class:"dialog-footer"};function _(e,t,a,_,y,F){const w=(0,l.g2)("Editor"),L=(0,l.g2)("el-scrollbar"),x=(0,l.g2)("el-tab-pane"),R=(0,l.g2)("el-tag"),E=(0,l.g2)("el-collapse-item"),S=(0,l.g2)("el-collapse"),T=(0,l.g2)("el-tabs"),V=(0,l.g2)("el-button"),D=(0,l.g2)("el-option"),X=(0,l.g2)("el-select"),W=(0,l.g2)("el-form-item"),A=(0,l.g2)("el-input"),I=(0,l.g2)("el-form"),$=(0,l.g2)("el-dialog");return(0,l.uX)(),(0,l.CE)(l.FK,null,[(0,l.bF)(T,{"model-value":"rb",style:{"min-height":"300px"},type:"border-card",value:"rb",size:"mini"},{default:(0,l.k6)(()=>["api"==a.result.type?((0,l.uX)(),(0,l.Wv)(x,{key:0,label:"响应体",name:"rb"},{default:(0,l.k6)(()=>[a.result.response_header?((0,l.uX)(),(0,l.CE)("div",n,[a.result.response_header["Content-Type"].includes("application/json")?((0,l.uX)(),(0,l.CE)("div",i,[(0,l.bF)(w,{readOnly:!0,modelValue:a.result.response_body,"onUpdate:modelValue":t[0]||(t[0]=e=>a.result.response_body=e),lang:"json",theme:"chrome"},null,8,["modelValue"])])):((0,l.uX)(),(0,l.CE)("div",o,[(0,l.bF)(L,{height:"400px",onWheel:t[1]||(t[1]=(0,s.D$)(()=>{},["stop"]))},{default:(0,l.k6)(()=>[(0,l.bF)(w,{readOnly:!0,innerHTML:a.result.response_body,lang:"html",theme:"chrome",height:"400px"},null,8,["innerHTML"])]),_:1})]))])):(0,l.Q3)("",!0)]),_:1})):(0,l.Q3)("",!0),"api"==a.result.type?((0,l.uX)(),(0,l.Wv)(x,{key:1,label:"响应头",name:"rh"},{default:(0,l.k6)(()=>[(0,l.bF)(L,{height:"400px",onWheel:t[2]||(t[2]=(0,s.D$)(()=>{},["stop"]))},{default:(0,l.k6)(()=>[a.result.response_header?((0,l.uX)(),(0,l.CE)("div",d,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(a.result.response_header,(e,t)=>((0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(R,{style:{"margin-top":"3px"},type:"info"},{default:(0,l.k6)(()=>[(0,l.Lk)("b",u,(0,r.v_)(t+" : "),1),(0,l.Lk)("span",null,(0,r.v_)(e),1)]),_:2},1024)]))),256))])):(0,l.Q3)("",!0)]),_:1})]),_:1})):(0,l.Q3)("",!0),"api"==a.result.type?((0,l.uX)(),(0,l.Wv)(x,{key:2,label:"请求信息",name:"rq"},{default:(0,l.k6)(()=>[(0,l.bF)(L,{height:"400px",onWheel:t[4]||(t[4]=(0,s.D$)(()=>{},["stop"]))},{default:(0,l.k6)(()=>[a.result.requests_body?((0,l.uX)(),(0,l.CE)("div",c,[(0,l.bF)(S,{modelValue:y.activeNames,"onUpdate:modelValue":t[3]||(t[3]=e=>y.activeNames=e),class:"tab-box-sli"},{default:(0,l.k6)(()=>[(0,l.bF)(E,{name:"1"},{title:(0,l.k6)(()=>t[9]||(t[9]=[(0,l.Lk)("b",null,"General",-1)])),default:(0,l.k6)(()=>[(0,l.Lk)("div",null,"Request Method : "+(0,r.v_)(a.result.method),1),(0,l.Lk)("div",null,"Request URL : "+(0,r.v_)(a.result.url),1)]),_:1}),(0,l.bF)(E,{name:"2"},{title:(0,l.k6)(()=>t[10]||(t[10]=[(0,l.Lk)("b",null,"Request Headers",-1)])),default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(a.result.requests_header,(e,t)=>((0,l.uX)(),(0,l.CE)("div",null,[(0,l.Lk)("span",null,(0,r.v_)(t+" : "+e),1)]))),256))]),_:1}),(0,l.bF)(E,{name:"3"},{title:(0,l.k6)(()=>t[11]||(t[11]=[(0,l.Lk)("b",null,"Request Payload",-1)])),default:(0,l.k6)(()=>[(0,l.Lk)("span",null,(0,r.v_)(a.result.requests_body),1)]),_:1})]),_:1},8,["modelValue"])])):(0,l.Q3)("",!0)]),_:1})]),_:1})):(0,l.Q3)("",!0),(0,l.bF)(x,{label:"日志"},{default:(0,l.k6)(()=>[(0,l.bF)(L,{height:"400px",onWheel:t[5]||(t[5]=(0,s.D$)(()=>{},["stop"]))},{default:(0,l.k6)(()=>[(0,l.Lk)("div",p,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(a.result.log_data,(e,t)=>((0,l.uX)(),(0,l.CE)("div",null,["DEBUG"===e[0]?((0,l.uX)(),(0,l.Wv)(R,{key:0,style:{"margin-top":"3px"}},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(e[1]),1)]),_:2},1024)):"WARNING"===e[0]?((0,l.uX)(),(0,l.Wv)(R,{key:1,style:{"margin-top":"3px"},type:"warning"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(e[1]),1)]),_:2},1024)):"ERROR"===e[0]?((0,l.uX)(),(0,l.Wv)(R,{key:2,style:{"margin-top":"3px"},type:"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(e[1]),1)]),_:2},1024)):"INFO"===e[0]?((0,l.uX)(),(0,l.Wv)(R,{key:3,style:{"margin-top":"3px"},type:"success"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(e[1]),1)]),_:2},1024)):"EXCEPT"===e[0]?((0,l.uX)(),(0,l.CE)("pre",h,(0,r.v_)(e[1]),1)):(0,l.Q3)("",!0)]))),256))])]),_:1})]),_:1}),(0,l.bF)(x,{disabled:""},{label:(0,l.k6)(()=>["成功"===a.result.state?((0,l.uX)(),(0,l.CE)("span",g,(0,r.v_)("Assert : "+a.result.state),1)):"失败"===a.result.state?((0,l.uX)(),(0,l.CE)("span",k,(0,r.v_)("Assert : "+a.result.state),1)):((0,l.uX)(),(0,l.CE)("span",m,(0,r.v_)(a.result.state),1))]),_:1}),"api"==a.result.type?((0,l.uX)(),(0,l.Wv)(x,{key:3,disabled:""},{label:(0,l.k6)(()=>[a.result.status_cede<=300?((0,l.uX)(),(0,l.CE)("span",f,(0,r.v_)("Status : "+a.result.status_cede),1)):((0,l.uX)(),(0,l.CE)("span",b,(0,r.v_)("Status : "+a.result.status_cede),1))]),_:1})):(0,l.Q3)("",!0),(0,l.bF)(x,{disabled:""},{label:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)("Time : "+a.result.run_time),1)]),_:1})]),_:1}),"失败"===a.result.state&&a.showbtn?((0,l.uX)(),(0,l.CE)("div",v,[(0,l.bF)(V,{onClick:F.getInterfaces,type:"success",plain:"",size:"mini"},{default:(0,l.k6)(()=>t[12]||(t[12]=[(0,l.eW)("提交bug")])),_:1,__:[12]},8,["onClick"])])):(0,l.Q3)("",!0),(0,l.bF)($,{title:"提交bug",modelValue:y.addBugDlg,"onUpdate:modelValue":t[8]||(t[8]=e=>y.addBugDlg=e),width:"40%","before-close":F.closeDialogResult},{footer:(0,l.k6)(()=>[(0,l.Lk)("div",C,[(0,l.bF)(V,{onClick:F.closeDialogResult},{default:(0,l.k6)(()=>t[13]||(t[13]=[(0,l.eW)("取 消")])),_:1,__:[13]},8,["onClick"]),(0,l.bF)(V,{type:"success",onClick:F.saveBug},{default:(0,l.k6)(()=>t[14]||(t[14]=[(0,l.eW)("确 定")])),_:1,__:[14]},8,["onClick"])])]),default:(0,l.k6)(()=>[(0,l.bF)(I,{model:y.bugForm},{default:(0,l.k6)(()=>[(0,l.bF)(W,{label:"所属接口"},{default:(0,l.k6)(()=>[(0,l.bF)(X,{size:"small",modelValue:y.bugForm.interface,"onUpdate:modelValue":t[6]||(t[6]=e=>y.bugForm.interface=e),placeholder:"bug对应的接口",style:{width:"100%"}},{default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(y.interfaces,e=>((0,l.uX)(),(0,l.Wv)(D,{label:e.name+" "+e.url,value:e.id,key:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,l.bF)(W,{label:"bug描述"},{default:(0,l.k6)(()=>[(0,l.bF)(A,{autosize:{minRows:3,maxRows:4},modelValue:y.bugForm.desc,"onUpdate:modelValue":t[7]||(t[7]=e=>y.bugForm.desc=e),type:"textarea",autocomplete:"off"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","before-close"])],64)}var y=a(53629),F=a(60782),w={props:{result:{default:{}},showbtn:{default:!0}},computed:{...(0,F.aH)(["pro"])},components:{Editor:y.A},data(){return{activeNames:["1","2","3"],addBugDlg:!1,bugForm:{interface:null,desc:"",info:"",status:"待处理"},interfaces:[]}},methods:{async saveBug(){this.bugForm.project=this.pro.id,this.bugForm.info=this.result;const e=await this.$api.createBugs(this.bugForm);201===e.status&&(this.$message({type:"success",message:"bug提交成功",duration:1e3}),this.addBugDlg=!1,this.bugForm={interface:null,desc:"",info:"",status:"待处理"})},closeDialogResult(){this.addBugDlg=!1,this.bugForm={interface:null,desc:"",info:"",status:"待处理"}},async getInterfaces(){const e=await this.$api.getNewInterfaces();200===e.status&&(this.interfaces=e.data,this.addBugDlg=!0)}}},L=a(71241);const x=(0,L.A)(w,[["render",_]]);var R=x},72773:function(e,t,a){a.r(t),a.d(t,{default:function(){return xe}});var l=a(56768),s=a(24232),r=a(45130);const n={class:"test-case-container"},i={class:"page-header"},o={class:"header-left"},d={class:"action-container"},u={class:"panel-header"},c={class:"panel-subtitle"},p={class:"highlight"},h={class:"panel-tools"},g={class:"search-box"},k={class:"filter-group"},m={class:"actions"},f={class:"header-right"},b={key:0,class:"env-badge"},v={class:"env-icon"},C={class:"env-info"},_={class:"env-name"},y={key:1,class:"env-badge"},F={class:"env-icon warning"},w={class:"main-content"},L={class:"glass-panel data-panel"},x={class:"table-container"},R={class:"case-index"},E={class:"column-name-header"},S={class:"case-name"},T={class:"column-header"},V={class:"project-name"},D={class:"column-header"},X={class:"step-indicator"},W=["stroke-dasharray"],A={x:"18",y:"21.35",class:"percentage"},I={class:"column-header"},$={class:"description"},U={class:"column-header"},z={class:"user-info"},M={class:"info-detail"},P={class:"time"},j={class:"column-header"},B={key:0,class:"user-info"},O={class:"avatar"},N={class:"info-detail"},Q={class:"username"},q={class:"time"},H={key:1,class:"no-update"},G={class:"column-header"},K={class:"actions-group"},Y={class:"pagination-container"},J={class:"dialog-content"},Z={class:"dialog-footer"},ee={style:{padding:"20px"}},te={style:{color:"#00aaff"}},ae={style:{color:"#00aa7f"}},le={style:{color:"orangered"}},se={style:{color:"#fca130"}},re={key:0},ne={key:0},ie={key:0,style:{color:"#00AA7F"}},oe={key:1,style:{color:"#fca130"}},de={key:2,style:{color:"#F56C6C"}};function ue(e,t,a,ue,ce,pe){const he=(0,l.g2)("el-button"),ge=(0,l.g2)("el-input"),ke=(0,l.g2)("el-switch"),me=(0,l.g2)("Plus"),fe=(0,l.g2)("el-icon"),be=(0,l.g2)("Connection"),ve=(0,l.g2)("Warning"),Ce=(0,l.g2)("el-table-column"),_e=(0,l.g2)("Document"),ye=(0,l.g2)("Folder"),Fe=(0,l.g2)("el-tag"),we=(0,l.g2)("List"),Le=(0,l.g2)("ChatDotRound"),xe=(0,l.g2)("el-tooltip"),Re=(0,l.g2)("UserFilled"),Ee=(0,l.g2)("Edit"),Se=(0,l.g2)("Operation"),Te=(0,l.g2)("VideoPlay"),Ve=(0,l.g2)("SetUp"),De=(0,l.g2)("Delete"),Xe=(0,l.g2)("el-popconfirm"),We=(0,l.g2)("el-table"),Ae=(0,l.g2)("el-pagination"),Ie=(0,l.g2)("el-scrollbar"),$e=(0,l.g2)("el-form-item"),Ue=(0,l.g2)("el-form"),ze=(0,l.g2)("el-dialog"),Me=(0,l.g2)("el-descriptions-item"),Pe=(0,l.g2)("el-descriptions"),je=(0,l.g2)("caseResult"),Be=(0,l.g2)("el-drawer");return(0,l.uX)(),(0,l.CE)("div",n,[(0,l.Lk)("div",i,[(0,l.Lk)("div",o,[(0,l.Lk)("div",d,[(0,l.Lk)("div",u,[(0,l.Lk)("div",c,[t[8]||(t[8]=(0,l.eW)("共 ")),(0,l.Lk)("span",p,(0,s.v_)(ce.pages.count||0),1),t[9]||(t[9]=(0,l.eW)(" 条数据"))])]),(0,l.Lk)("div",h,[(0,l.Lk)("div",g,[(0,l.bF)(ge,{modelValue:ce.filterText,"onUpdate:modelValue":t[0]||(t[0]=e=>ce.filterText=e),placeholder:"搜索用例名称",clearable:"",class:"glow-input"},{append:(0,l.k6)(()=>[(0,l.bF)(he,{onClick:pe.searchClick,class:"neon-btn-primary"},{default:(0,l.k6)(()=>t[10]||(t[10]=[(0,l.Lk)("span",null,"查询",-1)])),_:1,__:[10]},8,["onClick"])]),_:1},8,["modelValue"])]),(0,l.Lk)("div",k,[(0,l.bF)(ke,{modelValue:ce.checked,"onUpdate:modelValue":t[1]||(t[1]=e=>ce.checked=e),class:"creator-switch","active-text":"仅看我创建","inactive-text":"全部用例",size:"large",onChange:pe.creatorCase,style:{"--el-switch-on-color":"#13ce66","--el-switch-off-color":"#409eff"},"inline-prompt":""},null,8,["modelValue","onChange"])]),(0,l.Lk)("div",m,[(0,l.bF)(he,{onClick:pe.clickAdd,class:"neon-btn-add",type:"primary"},{default:(0,l.k6)(()=>[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(me)]),_:1}),t[11]||(t[11]=(0,l.eW)("新增用例 "))]),_:1,__:[11]},8,["onClick"])])])])]),(0,l.Lk)("div",f,[e.envId?((0,l.uX)(),(0,l.CE)("div",b,[(0,l.Lk)("div",v,[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(be)]),_:1})]),(0,l.Lk)("div",C,[t[12]||(t[12]=(0,l.Lk)("div",{class:"env-label"},"当前环境",-1)),(0,l.Lk)("div",_,(0,s.v_)(pe.currentEnv?pe.currentEnv.name:"未知环境"),1)])])):((0,l.uX)(),(0,l.CE)("div",y,[(0,l.Lk)("div",F,[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(ve)]),_:1})]),t[13]||(t[13]=(0,l.Lk)("div",{class:"env-info"},[(0,l.Lk)("div",{class:"env-label"},"环境未设置"),(0,l.Lk)("div",{class:"env-name error"},"请选择环境")],-1))]))])]),(0,l.Lk)("div",w,[(0,l.Lk)("div",L,[(0,l.bF)(Ie,{height:"calc(100vh - 300px)"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",x,[(0,l.bF)(We,{data:ce.caseList,style:{width:"100%"},"empty-text":"暂无数据","row-class-name":"case-row","row-style":{cursor:"pointer"},"header-cell-class-name":"table-header","cell-class-name":pe.cellClassName,onRowClick:pe.handleRowClick},{default:(0,l.k6)(()=>[(0,l.bF)(Ce,{align:"center",type:"index",width:"80"},{header:(0,l.k6)(()=>t[14]||(t[14]=[(0,l.Lk)("div",{class:"column-header"},[(0,l.Lk)("span",null,"序号")],-1)])),default:(0,l.k6)(e=>[(0,l.Lk)("div",R,[(0,l.Lk)("span",null,(0,s.v_)(e.$index+1),1)])]),_:1}),(0,l.bF)(Ce,{label:"用例名称","min-width":"220","show-overflow-tooltip":"","class-name":"case-name-column"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",E,[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(_e)]),_:1}),t[15]||(t[15]=(0,l.Lk)("span",null,"用例名称",-1))])]),default:(0,l.k6)(e=>[(0,l.Lk)("div",S,[(0,l.Lk)("div",{class:"case-icon",style:(0,s.Tr)({background:pe.getCaseIconColor(e.row.stepCount)})},[e.row.stepCount>0?((0,l.uX)(),(0,l.Wv)(fe,{key:0},{default:(0,l.k6)(()=>[(0,l.bF)(_e)]),_:1})):((0,l.uX)(),(0,l.Wv)(fe,{key:1},{default:(0,l.k6)(()=>[(0,l.bF)(ve)]),_:1}))],4),(0,l.Lk)("span",null,(0,s.v_)(e.row.name),1)])]),_:1}),(0,l.bF)(Ce,{align:"center",label:"所属项目",width:"180","show-overflow-tooltip":""},{header:(0,l.k6)(()=>[(0,l.Lk)("div",T,[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(ye)]),_:1}),t[16]||(t[16]=(0,l.Lk)("span",null,"所属项目",-1))])]),default:(0,l.k6)(e=>[(0,l.Lk)("div",V,[(0,l.bF)(Fe,{size:"small",effect:"plain"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(e.row.project?.name||"-"),1)]),_:2},1024)])]),_:1}),(0,l.bF)(Ce,{label:"步骤",width:"120",align:"center"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",D,[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(we)]),_:1}),t[17]||(t[17]=(0,l.Lk)("span",null,"步骤",-1))])]),default:(0,l.k6)(e=>[(0,l.Lk)("div",X,[((0,l.uX)(),(0,l.CE)("svg",{viewBox:"0 0 36 36",class:(0,s.C4)(["circular-chart",pe.getStepClass(e.row.stepCount)])},[t[18]||(t[18]=(0,l.Lk)("path",{class:"circle-bg",d:"M18 2.0845\n                        a 15.9155 15.9155 0 0 1 0 31.831\n                        a 15.9155 15.9155 0 0 1 0 -31.831"},null,-1)),(0,l.Lk)("path",{class:"circle","stroke-dasharray":"30, 100",d:"M18 2.0845\n                        a 15.9155 15.9155 0 0 1 0 31.831\n                        a 15.9155 15.9155 0 0 1 0 -31.831"},null,8,W),(0,l.Lk)("text",A,(0,s.v_)(e.row.stepCount),1)],2))])]),_:1}),(0,l.bF)(Ce,{label:"用例描述",align:"center","min-width":"250","show-overflow-tooltip":""},{header:(0,l.k6)(()=>[(0,l.Lk)("div",I,[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(Le)]),_:1}),t[19]||(t[19]=(0,l.Lk)("span",null,"用例描述",-1))])]),default:(0,l.k6)(e=>[(0,l.Lk)("div",$,[(0,l.bF)(xe,{effect:"dark",content:e.row.desc||"暂无描述",placement:"top",disabled:!e.row.desc||e.row.desc.length<=20},{default:(0,l.k6)(()=>[(0,l.Lk)("span",null,(0,s.v_)(e.row.desc||"暂无描述"),1)]),_:2},1032,["content","disabled"])])]),_:1}),(0,l.bF)(Ce,{label:"创建信息",width:"220",align:"center"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",U,[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(Re)]),_:1}),t[20]||(t[20]=(0,l.Lk)("span",null,"创建信息",-1))])]),default:(0,l.k6)(e=>[(0,l.Lk)("div",z,[(0,l.Lk)("div",{class:(0,s.C4)(["avatar",{"self-avatar":e.row.creator===pe.username}])},(0,s.v_)(pe.getAvatarText(e.row.creator)),3),(0,l.Lk)("div",M,[(0,l.Lk)("div",{class:(0,s.C4)(["username",{"self-name":e.row.creator===pe.username}])},(0,s.v_)(e.row.creator||"未知"),3),(0,l.Lk)("div",P,(0,s.v_)(pe.formatTime(e.row.create_time)),1)])])]),_:1}),(0,l.bF)(Ce,{label:"最后更新",width:"220",align:"center"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",j,[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(Ee)]),_:1}),t[21]||(t[21]=(0,l.Lk)("span",null,"最后更新",-1))])]),default:(0,l.k6)(e=>[e.row.update_time?((0,l.uX)(),(0,l.CE)("div",B,[(0,l.Lk)("div",O,(0,s.v_)(pe.getAvatarText(e.row.modifier)),1),(0,l.Lk)("div",N,[(0,l.Lk)("div",Q,(0,s.v_)(e.row.modifier||"未知"),1),(0,l.Lk)("div",q,(0,s.v_)(pe.formatTime(e.row.update_time)),1)])])):((0,l.uX)(),(0,l.CE)("div",H,"尚未更新"))]),_:1}),(0,l.bF)(Ce,{label:"操作",fixed:"right",width:"200",align:"center"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",G,[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(Se)]),_:1}),t[22]||(t[22]=(0,l.Lk)("span",null,"操作",-1))])]),default:(0,l.k6)(e=>[(0,l.Lk)("div",K,[(0,l.bF)(xe,{content:"运行用例",placement:"top"},{default:(0,l.k6)(()=>[(0,l.bF)(he,{onClick:(0,r.D$)(t=>pe.runCase(e.row),["stop"]),circle:"",class:(0,s.C4)(["action-btn","run-btn",{disabled:e.row.stepCount<=0}]),disabled:e.row.stepCount<=0,type:"success"},{default:(0,l.k6)(()=>[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(Te)]),_:1})]),_:2},1032,["onClick","class","disabled"])]),_:2},1024),(0,l.bF)(xe,{content:"管理步骤",placement:"top"},{default:(0,l.k6)(()=>[(0,l.bF)(he,{onClick:(0,r.D$)(t=>pe.clickEdit(e.row),["stop"]),circle:"",class:"action-btn edit-btn",type:"primary"},{default:(0,l.k6)(()=>[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(Ve)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),(0,l.bF)(xe,{content:"删除用例",placement:"top"},{default:(0,l.k6)(()=>[(0,l.bF)(he,{onClick:t[2]||(t[2]=(0,r.D$)(()=>{},["stop"])),circle:"",class:"action-btn delete-btn",type:"danger"},{default:(0,l.k6)(()=>[(0,l.bF)(Xe,{title:"确定要删除此用例吗？","confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:t=>pe.delCase(e.row.id)},{reference:(0,l.k6)(()=>[(0,l.bF)(fe,null,{default:(0,l.k6)(()=>[(0,l.bF)(De)]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)]),_:2},1024)])]),_:1})]),_:1},8,["data","cell-class-name","onRowClick"])]),(0,l.Lk)("div",Y,[(0,l.bF)(Ae,{background:"",layout:"total, prev, pager, next, jumper",onCurrentChange:pe.currentPages,"default-page-size":100,total:ce.pages.count,"current-page":ce.pages.current,"next-text":"下一页","prev-text":"上一页"},null,8,["onCurrentChange","total","current-page"])])]),_:1})])]),(0,l.bF)(ze,{modelValue:ce.addDlg,"onUpdate:modelValue":t[6]||(t[6]=e=>ce.addDlg=e),"custom-class":"custom-dialog",width:"550px","destroy-on-close":"","show-close":!1,"close-on-click-modal":!1},{header:(0,l.k6)(()=>t[23]||(t[23]=[(0,l.Lk)("div",{class:"dialog-header"},[(0,l.Lk)("h3",{class:"dialog-title"},"创建新测试用例")],-1)])),footer:(0,l.k6)(()=>[(0,l.Lk)("div",Z,[(0,l.bF)(he,{onClick:pe.clearValidation,class:"cancel-btn"},{default:(0,l.k6)(()=>t[24]||(t[24]=[(0,l.eW)(" 取消 ")])),_:1,__:[24]},8,["onClick"]),(0,l.bF)(he,{type:"primary",onClick:pe.addCase,loading:ce.submitting,class:"confirm-btn"},{default:(0,l.k6)(()=>t[25]||(t[25]=[(0,l.eW)(" 确认创建 ")])),_:1,__:[25]},8,["onClick","loading"])])]),default:(0,l.k6)(()=>[(0,l.Lk)("div",J,[(0,l.bF)(Ue,{model:ce.addForm,rules:ce.rulesCase,ref:"CaseRef","label-position":"top",class:"case-form"},{default:(0,l.k6)(()=>[(0,l.bF)($e,{label:"用例名称",prop:"name"},{default:(0,l.k6)(()=>[(0,l.bF)(ge,{modelValue:ce.addForm.name,"onUpdate:modelValue":t[3]||(t[3]=e=>ce.addForm.name=e),placeholder:"输入用例名称",maxlength:"50","show-word-limit":"",class:"form-input"},null,8,["modelValue"])]),_:1}),(0,l.bF)($e,{label:"所属项目",prop:"project_id"},{default:(0,l.k6)(()=>[(0,l.bF)(ge,{modelValue:ce.addForm.project_id,"onUpdate:modelValue":t[4]||(t[4]=e=>ce.addForm.project_id=e),class:"form-input",disabled:""},null,8,["modelValue"])]),_:1}),(0,l.bF)($e,{label:"用例描述",prop:"desc"},{default:(0,l.k6)(()=>[(0,l.bF)(ge,{type:"textarea",modelValue:ce.addForm.desc,"onUpdate:modelValue":t[5]||(t[5]=e=>ce.addForm.desc=e),placeholder:"输入用例描述",rows:"4",maxlength:"200","show-word-limit":"",class:"form-input"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),(0,l.bF)(Be,{modelValue:ce.ResultDlg,"onUpdate:modelValue":t[7]||(t[7]=e=>ce.ResultDlg=e),"with-header":!1,size:"50%"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",ee,[(0,l.bF)(Pe,{title:"执行结果",border:"",column:4,style:{"text-align":"center"}},{default:(0,l.k6)(()=>[(0,l.bF)(Me,{label:"总数"},{default:(0,l.k6)(()=>[(0,l.Lk)("b",te,(0,s.v_)(ce.runScentResult.all),1)]),_:1}),(0,l.bF)(Me,{label:"通过"},{default:(0,l.k6)(()=>[(0,l.Lk)("b",ae,(0,s.v_)(ce.runScentResult.success),1)]),_:1}),(0,l.bF)(Me,{label:"失败"},{default:(0,l.k6)(()=>[(0,l.Lk)("b",le,(0,s.v_)(ce.runScentResult.fail),1)]),_:1}),(0,l.bF)(Me,{label:"错误"},{default:(0,l.k6)(()=>[(0,l.Lk)("b",se,(0,s.v_)(ce.runScentResult.error),1)]),_:1})]),_:1}),t[26]||(t[26]=(0,l.Lk)("div",{style:{height:"40px","line-height":"40px"}},[(0,l.Lk)("b",null,"执行详情")],-1)),(0,l.bF)(Ie,{height:"calc(100vh - 180px)"},{default:(0,l.k6)(()=>[(0,l.bF)(We,{data:ce.runScentResult.cases,style:{width:"100%"},"empty-text":"暂无数据"},{default:(0,l.k6)(()=>[(0,l.bF)(Ce,{type:"expand"},{default:(0,l.k6)(e=>[(0,l.bF)(je,{result:e.row},null,8,["result"])]),_:1}),(0,l.bF)(Ce,{label:"步骤名",prop:"name"}),(0,l.bF)(Ce,{label:"请求方法",prop:"method"},{default:(0,l.k6)(e=>["api"===e.row.type?((0,l.uX)(),(0,l.CE)("span",re,(0,s.v_)(e.row.method),1)):(0,l.Q3)("",!0)]),_:1}),(0,l.bF)(Ce,{label:"响应状态码",prop:"status_cede"},{default:(0,l.k6)(e=>["api"===e.row.type?((0,l.uX)(),(0,l.CE)("span",ne,(0,s.v_)(e.row.status_cede),1)):(0,l.Q3)("",!0)]),_:1}),(0,l.bF)(Ce,{label:"执行结果",prop:"state","min-width":"40px"},{default:(0,l.k6)(e=>["成功"==e.row.state?((0,l.uX)(),(0,l.CE)("span",ie,(0,s.v_)(e.row.state),1)):"错误"==e.row.state?((0,l.uX)(),(0,l.CE)("span",oe,(0,s.v_)(e.row.state),1)):((0,l.uX)(),(0,l.CE)("span",de,(0,s.v_)(e.row.state),1))]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1},8,["modelValue"])])}a(44114),a(18111),a(20116);var ce=a(60782),pe=a(67638),he=a(93851),ge=a(51219),ke=a(25320),me=a(12933),fe=a(22212),be=a(82563),ve=a(80311),Ce=a(18915),_e=a(75095),ye=a(57477);fe.Y([ve.a,Ce._,_e.a]);var Fe={computed:{...(0,ce.aH)(["pro","envId","testEnvs"]),username(){return window.sessionStorage.getItem("username")},currentEnv(){return this.envId&&this.testEnvs&&0!==this.testEnvs.length?this.testEnvs.find(e=>e.id===this.envId)||this.testEnvs[0]:null}},components:{caseResult:pe.A,Plus:ye.Plus,View:ye.View,Search:ye.Search,VideoPlay:ye.VideoPlay,Management:ye.Management,Document:ye.Document,Delete:ye.Delete,Grid:ye.Grid,List:ye.List,CircleCheck:ye.CircleCheck,CircleClose:ye.CircleClose,Warning:ye.Warning,InfoFilled:ye.InfoFilled,WarningFilled:ye.WarningFilled,Close:ye.Close,Check:ye.Check,User:ye.User,Monitor:ye.Monitor,Connection:ye.Connection,Collection:ye.Collection,Operation:ye.Operation,ChatDotRound:ye.ChatDotRound,Avatar:ye.Avatar,Edit:ye.Edit,SetUp:ye.SetUp,DataAnalysis:ye.DataAnalysis,Folder:ye.Folder,Histogram:ye.Histogram,PieChartIcon:ye.PieChart,Opportunity:ye.Opportunity,Refresh:ye.Refresh,UserFilled:ye.UserFilled},data(){return{filterText:"",pages:{},caseList:[],ResultDlg:!1,runScentResult:{},addDlg:!1,checked:!1,submitting:!1,pageSize:10,chart:null,iconColors:["linear-gradient(135deg, #1890ff, #2979ff)","linear-gradient(135deg, #38b6ff, #59c1ff)","linear-gradient(135deg, #0080ff, #42a5f5)","linear-gradient(135deg, #2196f3, #1976d2)","linear-gradient(135deg, #3f51b5, #303f9f)"],rulesCase:{name:[{required:!0,message:"请输入用例名称",trigger:"blur"}],project_id:[{required:!0,message:"请选择项目",trigger:"blur"}]},addForm:{name:"",project_id:"",desc:"",creator:"",stepCount:0},colorMap:{},loadingConfig:{spinner:"Loading"}}},methods:{...(0,ce.PY)(["CaseInfo"]),cellClassName({row:e,column:t,rowIndex:a,columnIndex:l}){return"步骤"===t.label&&e.stepCount<=0?"empty-steps-cell":""},getCaseIconColor(e){if(e<=0)return"linear-gradient(135deg, #f5a623, #f27121)";const t=7*e%this.iconColors.length;return this.iconColors[t]},getStepClass(e){return e<=0?"empty-chart":e<3?"low-chart":e<10?"medium-chart":"high-chart"},getStatusBadgeType(e){return e?(e=parseInt(e),e>=200&&e<300?"success":e>=300&&e<400?"info":e>=400&&e<500?"warning":e>=500?"danger":""):""},tableRowClassName({row:e,rowIndex:t}){return e.stepCount<=0?"empty-steps-row":t%2===0?"even-row":"odd-row"},handleRowClick(e){this.clickEdit(e)},getStepPercentage(e){return!e||e<=0?0:Math.min(10*e,100)},getResultClassName(e){return"成功"===e?"result-success":"错误"===e?"result-warning":"result-error"},getStepColor(e){return!e||e<=0?"#909399":e<3?"#E6A23C":e<10?"#409EFF":"#67C23A"},getAvatarColor(e){if(!e)return"#909399";if(!this.colorMap[e]){const t=["#f56c6c","#e6a23c","#409eff","#67c23a","#909399","#8a2be2","#00bcd4","#ff9800","#9c27b0","#2196f3"],a=e.charCodeAt(0)%t.length;this.colorMap[e]=t[a]}return this.colorMap[e]},getAvatarText(e){return e?e.substring(0,1).toUpperCase():"?"},formatTime(e){return e?this.$tools.rTime(e):"未知时间"},getMethodType(e){if(!e)return"";switch(e=e.toUpperCase(),e){case"GET":return"success";case"POST":return"primary";case"PUT":return"warning";case"DELETE":return"danger";case"PATCH":return"info";default:return""}},getStatusType(e){return e?(e=parseInt(e),e>=200&&e<300?"success":e>=300&&e<400?"info":e>=400&&e<500?"warning":e>=500?"danger":""):""},getSuccessRate(){return!this.runScentResult.all||this.runScentResult.all<=0?0:Math.round(this.runScentResult.success/this.runScentResult.all*100)},getFailRate(){return!this.runScentResult.all||this.runScentResult.all<=0?0:Math.round(this.runScentResult.fail/this.runScentResult.all*100)},getErrorRate(){return!this.runScentResult.all||this.runScentResult.all<=0?0:Math.round(this.runScentResult.error/this.runScentResult.all*100)},initChart(){this.ResultDlg&&this.runScentResult.all&&this.$nextTick(()=>{if(this.$refs.chartContainer){this.chart&&this.chart.dispose(),this.chart=be.Ts(this.$refs.chartContainer);const e={type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"#67C23A"},{offset:1,color:"#4caf50"}]},t={type:"linear",x:0,y:0,x2:1,y2:1,colorStops:[{offset:0,color:"#F56C6C"},{offset:1,color:"#E53935"}]},a={type:"linear",x:0,y:0,x2:1,y2:0,colorStops:[{offset:0,color:"#E6A23C"},{offset:1,color:"#FFA000"}]},l={backgroundColor:"transparent",tooltip:{trigger:"item",formatter:"{b}: {c} ({d}%)",backgroundColor:"rgba(30, 30, 45, 0.7)",borderColor:"rgba(80, 80, 180, 0.5)",textStyle:{color:"#fff"}},legend:{orient:"vertical",right:10,top:"center",textStyle:{color:"#e0e0e0"},itemWidth:14,itemHeight:14,itemGap:20,icon:"circle"},series:[{name:"执行结果",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"rgba(30, 30, 45, 0.8)",borderWidth:2},label:{show:!1},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold",color:"#ffffff"},scaleSize:15},labelLine:{show:!1},data:[{value:this.runScentResult.success||0,name:"通过",itemStyle:{color:e}},{value:this.runScentResult.fail||0,name:"失败",itemStyle:{color:t}},{value:this.runScentResult.error||0,name:"错误",itemStyle:{color:a}}],animationType:"scale",animationEasing:"elasticOut",animationDelay:function(e){return 200*Math.random()}}]};this.chart.setOption(l),window.addEventListener("resize",this.resizeChart)}})},resizeChart(){this.chart&&this.chart.resize()},searchClick(){this.allTestCase(this.pro.id,this.pages.current,this.filterText)},clickEdit(e){this.$router.push({name:"TestCaseDetail"}),this.CaseInfo(e)},clickAdd(){this.addForm.project_id=this.pro.name,this.addDlg=!0},async addCase(){this.$refs.CaseRef.validate(async e=>{if(e){this.submitting=!0;try{const e={...this.addForm};e.creator=this.username,e.project_id=this.pro.id;const t=await this.$api.createTestCase(e);201===t.status&&((0,he.df)({title:"创建成功",message:"测试用例已成功创建",type:"success",duration:2e3}),this.addDlg=!1,this.allTestCase(this.pro.id))}catch(t){(0,ge.nk)({type:"error",message:"创建失败："+(t.message||"未知错误"),duration:3e3})}finally{this.submitting=!1}}})},creatorCase(){this.checked?this.allTestCase(this.pro.id,this.pages.current,this.filterText,this.username):this.allTestCase()},async allTestCase(e=this.pro.id,t=this.pages.current,a,l){try{const s=await this.$api.getTestCase(e,t,a,l);200===s.status&&(this.caseList=s.data.result||[],this.pages=s.data||{},this.addForm.project_id=this.pro.name)}catch(s){(0,ge.nk)({type:"error",message:"获取用例列表失败: "+(s.message||"未知错误"),duration:3e3})}},currentPages(e){this.allTestCase(this.pro.id,e,this.filterText,this.checked?this.username:void 0),this.pages.current=e},handleSizeChange(e){this.pageSize=e,this.currentPages(1)},async delCase(e){try{const t=ke.Ks.service({text:"正在删除...",background:"rgba(30, 30, 45, 0.7)"}),a=await this.$api.delTestCase(e);t.close(),204===a.status&&((0,he.df)({title:"删除成功",message:"测试用例已成功删除",type:"success",duration:2e3}),this.allTestCase(this.pro.id,this.pages.current,this.filterText,this.checked?this.username:void 0))}catch(t){(0,ge.nk)({type:"error",message:"删除失败："+(t.message||"未知错误"),duration:3e3})}},async runCase(e){const t=parseInt(e.stepCount);if(t<=0)return void(0,ge.nk)({type:"warning",message:"该用例没有步骤，请先添加步骤再运行",duration:2e3});if(!this.envId)return void me.s.alert("请先选择执行环境后再运行用例","未选择环境",{confirmButtonText:"确定",type:"warning"});const a=ke.Ks.service({text:"正在执行测试用例，请稍候...",spinner:"Loading",background:"rgba(41, 121, 255, 0.3)"});try{const t={env:this.envId,scene:e.id},l=await this.$api.runCases(e.id,t);if(200==l.status){a.close(),this.runScentResult=l.data||{},this.ResultDlg=!0,this.initChart();const e=this.getSuccessRate();100===e?(0,he.df)({title:"执行成功",message:`全部 ${this.runScentResult.all} 个步骤执行成功`,type:"success",duration:3e3}):e>=80?(0,he.df)({title:"部分成功",message:`${this.runScentResult.success}/${this.runScentResult.all} 个步骤执行成功`,type:"warning",duration:3e3}):(0,he.df)({title:"执行失败",message:`仅 ${this.runScentResult.success}/${this.runScentResult.all} 个步骤执行成功`,type:"error",duration:3e3})}}catch(l){a.close(),(0,ge.nk)({type:"error",message:"执行失败："+(l.message||"未知错误"),duration:3e3})}},clearValidation(){this.addDlg=!1,this.$refs.CaseRef?.clearValidate(),this.addForm={name:"",project_id:this.pro.name,desc:"",creator:"",stepCount:0}}},created(){this.allTestCase(this.pro.id)},mounted(){this.envId||(0,ge.nk)({type:"info",message:"请先选择执行环境以便运行测试用例",duration:3e3})},beforeUnmount(){window.removeEventListener("resize",this.resizeChart),this.chart&&(this.chart.dispose(),this.chart=null)},watch:{ResultDlg(e){e&&this.initChart()}}},we=a(71241);const Le=(0,we.A)(Fe,[["render",ue],["__scopeId","data-v-642df5aa"]]);var xe=Le}}]);
//# sourceMappingURL=238.a4774b2a.js.map