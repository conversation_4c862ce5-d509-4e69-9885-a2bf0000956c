{"version": 3, "file": "js/484.8d32479d.js", "mappings": "wMACMA,MAAM,sB,GAELA,MAAM,e,GACLA,MAAM,kB,GACLA,MAAM,iB,GAgBNA,MAAM,Y,iBAQHA,MAAM,Y,GAGLA,MAAM,Y,SAGFA,MAAM,kB,GAQhBA,MAAM,gB,GAEJA,MAAM,c,GACLA,MAAM,mB,GACLA,MAAM,a,GAINA,MAAM,gB,GAKPA,MAAM,e,GAiBNA,MAAM,e,GAIHA,MAAM,e,GAKPA,MAAM,c,GAQNA,MAAM,c,GAUPA,MAAM,e,GAIHA,MAAM,e,GAOLA,MAAM,kB,GAUNA,MAAM,kB,GAePA,MAAM,e,GAOLA,MAAM,kB,GAUNA,MAAM,kB,GAgBRA,MAAM,e,GAKPA,MAAM,kB,SAcVA,MAAM,a,igBA3LbC,EAAAA,EAAAA,IAoMM,MApMNC,EAoMM,EAlMLC,EAAAA,EAAAA,IAqCM,MArCNC,EAqCM,EApCLD,EAAAA,EAAAA,IAeM,MAfNE,EAeM,EAdLF,EAAAA,EAAAA,IAGM,MAHNG,EAGM,EAFLC,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWE,K,mBACpBN,EAAAA,EAAAA,IAAa,UAAT,QAAI,OAETI,EAAAA,EAAAA,IASaG,EAAA,CATDC,QAAQ,UAAUC,UAAU,QAAQC,OAAO,S,kBACtD,IAOY,EAPZN,EAAAA,EAAAA,IAOYO,EAAA,CANVC,QAAOC,EAAAC,OACRC,KAAK,UACLlB,MAAM,cACNmB,MAAA,GACCC,KAAMC,EAAAC,M,kBAAM,IAEdC,EAAA,MAAAA,EAAA,M,QAFc,a,8CAKhBhB,EAAAA,EAAAA,IAmBeiB,EAAA,CAnBDxB,MAAM,sBAAoB,C,iBACvC,IAiBM,EAjBNG,EAAAA,EAAAA,IAiBM,MAjBNsB,EAiBM,CAhBWJ,EAAAK,SAASC,OAAS,I,aACjC1B,EAAAA,EAAAA,IAUM2B,EAAAA,GAAA,CAAAC,IAAA,IAAAC,EAAAA,EAAAA,IATUT,EAAAK,SAARK,K,WADR9B,EAAAA,EAAAA,IAUM,OARJ4B,IAAKE,EAAKC,GACVjB,QAAKkB,GAAEjB,EAAAkB,UAAUH,GAClB/B,OAAKmC,EAAAA,EAAAA,IAAA,CAAC,WAAU,mBACaC,EAAAC,SAAWN,EAAKC,GAAGM,e,EAChDnC,EAAAA,EAAAA,IAEM,MAFNoC,EAEM,EADLhC,EAAAA,EAAAA,IAAiCC,EAAA,M,iBAAxB,IAAc,EAAdD,EAAAA,EAAAA,IAAciC,K,SAExBrC,EAAAA,EAAAA,IAA6C,OAA7CsC,GAA6CC,EAAAA,EAAAA,IAAnBX,EAAKY,MAAI,I,2BAGrC1C,EAAAA,EAAAA,IAEM,MAFN2C,EAEM,EADLrC,EAAAA,EAAAA,IAA0DsC,EAAA,CAA/C,aAAY,GAAIC,YAAY,iB,SAO3C3C,EAAAA,EAAAA,IAyJM,MAzJN4C,EAyJM,CAxJWX,EAAAY,U,WAAhB/C,EAAAA,EAAAA,IA6IW2B,EAAAA,GAAA,CAAAC,IAAA,KA5IV1B,EAAAA,EAAAA,IAwBM,MAxBN8C,EAwBM,EAvBL9C,EAAAA,EAAAA,IASM,MATN+C,EASM,EARL/C,EAAAA,EAAAA,IAGM,MAHNgD,EAGM,EAFL5C,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWE,K,OACpBN,EAAAA,EAAAA,IAA2B,WAAAuC,EAAAA,EAAAA,IAApBN,EAAAY,QAAQL,MAAI,MAEpBxC,EAAAA,EAAAA,IAGM,MAHNiD,EAGM,EAFL7C,EAAAA,EAAAA,IAA2BC,EAAA,M,iBAAlB,IAAQ,EAARD,EAAAA,EAAAA,IAAQ8C,K,OACjBlD,EAAAA,EAAAA,IAA6C,aAAAuC,EAAAA,EAAAA,IAApCN,EAAAY,QAAQM,MAAQ,YAAJ,QAGvBnD,EAAAA,EAAAA,IAYM,MAZNoD,EAYM,EAXLhD,EAAAA,EAAAA,IAUkBiD,EAAA,M,iBATjB,IAEa,EAFbjD,EAAAA,EAAAA,IAEaG,EAAA,CAFDC,QAAQ,SAASC,UAAU,O,kBACtC,IAAuE,EAAvEL,EAAAA,EAAAA,IAAuEO,EAAA,CAA3DC,QAAOC,EAAAyC,QAASvC,KAAK,UAAWE,KAAMC,EAAAqC,O,kBAAO,IAAEnC,EAAA,MAAAA,EAAA,M,QAAF,S,4CAE1DhB,EAAAA,EAAAA,IAEaG,EAAA,CAFDC,QAAQ,SAASC,UAAU,O,kBACtC,IAA2E,EAA3EL,EAAAA,EAAAA,IAA2EO,EAAA,CAA/DC,QAAOC,EAAA2C,QAASzC,KAAK,OAAQE,KAAMC,EAAAuC,c,kBAAc,IAAErC,EAAA,MAAAA,EAAA,M,QAAF,S,4CAE9DhB,EAAAA,EAAAA,IAEaG,EAAA,CAFDC,QAAQ,SAASC,UAAU,O,kBACtC,IAAsE,EAAtEL,EAAAA,EAAAA,IAAsEO,EAAA,CAA1DC,QAAOC,EAAA6C,OAAQ3C,KAAK,SAAUE,KAAMC,EAAAyC,Q,kBAAQ,IAAEvC,EAAA,MAAAA,EAAA,M,QAAF,S,wDAO5DhB,EAAAA,EAAAA,IAgHeiB,EAAA,CAhHDxB,MAAM,sBAAoB,C,iBACvC,IA8GM,EA9GNG,EAAAA,EAAAA,IA8GM,MA9GN4D,EA8GM,EA5GLxD,EAAAA,EAAAA,IAuBUyD,EAAA,CAvBDhE,MAAM,eAAa,CAChBiE,QAAMC,EAAAA,EAAAA,IAChB,IAGM,EAHN/D,EAAAA,EAAAA,IAGM,MAHNgE,EAGM,EAFL5D,EAAAA,EAAAA,IAAiCC,EAAA,M,iBAAxB,IAAc,EAAdD,EAAAA,EAAAA,IAAc6D,K,qBACvBjE,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGZ,IAOM,EAPNA,EAAAA,EAAAA,IAOM,MAPNkE,EAOM,EANL9D,EAAAA,EAAAA,IAKW+D,EAAA,C,WALQlC,EAAAY,QAAQL,K,qCAARP,EAAAY,QAAQL,KAAIV,GAAEsC,YAAY,OAAOC,KAAK,W,CAC7CC,SAAOP,EAAAA,EAAAA,IACjB,IAA8B,EAA9B3D,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWmE,K,qBACpBvE,EAAAA,EAAAA,IAAsC,QAAhCH,MAAM,gBAAe,QAAI,M,0BAIlCG,EAAAA,EAAAA,IAOM,MAPNwE,EAOM,EANLpE,EAAAA,EAAAA,IAKW+D,EAAA,C,WALQlC,EAAAY,QAAQM,K,qCAARlB,EAAAY,QAAQM,KAAIrB,GAAEsC,YAAY,aAAaC,KAAK,W,CACnDC,SAAOP,EAAAA,EAAAA,IACjB,IAA2B,EAA3B3D,EAAAA,EAAAA,IAA2BC,EAAA,M,iBAAlB,IAAQ,EAARD,EAAAA,EAAAA,IAAQ8C,K,qBACjBlD,EAAAA,EAAAA,IAAuC,QAAjCH,MAAM,gBAAe,SAAK,M,kCAMpCG,EAAAA,EAAAA,IAgEM,MAhENyE,EAgEM,EA9DLrE,EAAAA,EAAAA,IA6BUyD,EAAA,CA7BDhE,MAAM,eAAa,CAChBiE,QAAMC,EAAAA,EAAAA,IAChB,IAGM,EAHN/D,EAAAA,EAAAA,IAGM,MAHN0E,EAGM,EAFLtE,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWuE,K,qBACpB3E,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGZ,IAqBU,EArBVI,EAAAA,EAAAA,IAqBUwE,EAAA,CArBD7D,KAAK,cAAclB,MAAM,WAAWgF,QAAA,G,WAAiB5C,EAAA6C,iB,qCAAA7C,EAAA6C,iBAAgBhD,I,kBAC7E,IASc,EATd1B,EAAAA,EAAAA,IASc2E,EAAA,CATDC,MAAM,QAAQxC,KAAK,W,kBAC/B,IAOM,EAPNxC,EAAAA,EAAAA,IAOM,MAPNiF,EAOM,EANL7E,EAAAA,EAAAA,IAKS8E,EAAA,C,WAJCjD,EAAAY,QAAQsC,Q,qCAARlD,EAAAY,QAAQsC,QAAOrD,GACxBsD,OAAO,QACPC,MAAM,SACNC,KAAK,Q,kCAIRlF,EAAAA,EAAAA,IASc2E,EAAA,CATDC,MAAM,QAAQxC,KAAK,M,kBAC/B,IAOM,EAPNxC,EAAAA,EAAAA,IAOM,MAPNuF,EAOM,EANLnF,EAAAA,EAAAA,IAKS8E,EAAA,C,WAJCjD,EAAAY,QAAQ2C,G,qCAARvD,EAAAY,QAAQ2C,GAAE1D,GACnBsD,OAAO,QACPC,MAAM,SACNC,KAAK,Q,mEAQVlF,EAAAA,EAAAA,IA6BUyD,EAAA,CA7BDhE,MAAM,eAAa,CAChBiE,QAAMC,EAAAA,EAAAA,IAChB,IAGM,EAHN/D,EAAAA,EAAAA,IAGM,MAHNyF,EAGM,EAFLrF,EAAAA,EAAAA,IAAgCC,EAAA,M,iBAAvB,IAAa,EAAbD,EAAAA,EAAAA,IAAasF,M,qBACtB1F,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGZ,IAqBU,EArBVI,EAAAA,EAAAA,IAqBUwE,EAAA,CArBD7D,KAAK,cAAclB,MAAM,WAAWgF,QAAA,G,WAAiB5C,EAAA0D,gB,qCAAA1D,EAAA0D,gBAAe7D,I,kBAC5E,IASc,EATd1B,EAAAA,EAAAA,IASc2E,EAAA,CATDC,MAAM,OAAOxC,KAAK,U,kBAC9B,IAOM,EAPNxC,EAAAA,EAAAA,IAOM,MAPN4F,EAOM,EANLxF,EAAAA,EAAAA,IAKS8E,EAAA,C,WAJCjD,EAAAY,QAAQgD,gB,qCAAR5D,EAAAY,QAAQgD,gBAAe/D,GAChCsD,OAAO,QACPC,MAAM,SACNC,KAAK,Q,kCAIRlF,EAAAA,EAAAA,IASc2E,EAAA,CATDC,MAAM,SAASxC,KAAK,S,kBAChC,IAOM,EAPNxC,EAAAA,EAAAA,IAOM,MAPN8F,EAOM,EANL1F,EAAAA,EAAAA,IAKS8E,EAAA,C,WAJCjD,EAAAY,QAAQkD,sB,qCAAR9D,EAAAY,QAAQkD,sBAAqBjE,GACtCsD,OAAO,QACPC,MAAM,SACNC,KAAK,Q,qEASXlF,EAAAA,EAAAA,IAeUyD,EAAA,CAfDhE,MAAM,6BAA2B,CAC9BiE,QAAMC,EAAAA,EAAAA,IAChB,IAGM,EAHN/D,EAAAA,EAAAA,IAGM,MAHNgG,EAGM,EAFL5F,EAAAA,EAAAA,IAA2BC,EAAA,M,iBAAlB,IAAQ,EAARD,EAAAA,EAAAA,IAAQ6F,M,qBACjBjG,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGZ,IAOM,EAPNA,EAAAA,EAAAA,IAOM,MAPNkG,EAOM,EANL9F,EAAAA,EAAAA,IAKS8E,EAAA,C,WAJCjD,EAAAY,QAAQsD,Y,qCAARlE,EAAAY,QAAQsD,YAAWrE,GAC5BsD,OAAO,QACPE,KAAK,SACLD,MAAM,W,iEASqC,IAApBnE,EAAAK,SAASC,S,WAAtC1B,EAAAA,EAAAA,IAOM,MAPNsG,EAOM,EANLhG,EAAAA,EAAAA,IAKWsC,EAAA,CALDC,YAAY,UAAQ,C,iBAC7B,IAGY,EAHZvC,EAAAA,EAAAA,IAGYO,EAAA,CAHDI,KAAK,UAAWH,QAAOC,EAAAC,OAAQE,MAAA,I,kBACzC,IAA2B,EAA3BZ,EAAAA,EAAAA,IAA2BC,EAAA,M,iBAAlB,IAAQ,EAARD,EAAAA,EAAAA,IAAQiG,M,qBACjBrG,EAAAA,EAAAA,IAAmB,YAAb,UAAM,M,wIAgClB,GACCsG,WAAY,CACXC,OAAM,IACNhD,MAAK,QACLI,OAAM,SACNF,aAAY,eACZ+C,QAAO,UACPC,KAAI,OACJC,QAAO,UACPC,QAAO,UACPxF,KAAI,OACJyF,WAAU,aACVC,YAAW,cACXC,WAAU,aACVC,UAAS,YACTC,KAAIA,EAAAA,MAELC,IAAAA,GACC,MAAO,CACN/E,OAAQ,IACRW,QAAS,KACTiC,iBAAkB,UAClBa,gBAAiB,SAEnB,EACAuB,SAAU,KACNC,EAAAA,EAAAA,IAAS,CAAC,MAAO,WAAY,aAEjCC,QAAS,KACLC,EAAAA,EAAAA,IAAW,CAAC,eAGf,YAAMvG,GACL,MAAMwG,EAAS,CAAEC,QAASC,KAAKC,IAAI5F,GAAIW,KAAM,SACvCkF,QAAiBF,KAAKG,KAAKC,cAAcN,GAC/C,GAAwB,MAApBI,EAASG,UACZC,EAAAA,EAAAA,IAAU,CACT/G,KAAM,UACNgH,QAAS,OACTC,SAAU,YAELR,KAAKS,aAGPT,KAAKjG,SAASC,OAAS,GAAG,CAC7B,MAAM0G,EAASV,KAAKjG,SAAS4G,KAAKC,GAAoB,UAAbA,EAAI5F,OAAqBgF,KAAKjG,SAASiG,KAAKjG,SAASC,OAAS,GACvGgG,KAAKzF,UAAUmG,EAChB,CAEF,EAEA,YAAMxE,GACL2E,EAAAA,EAAaC,QAAQ,uBAAwB,OAAQ,CACpDC,kBAAmB,OACnBC,iBAAkB,KAClBzH,KAAM,UACN0H,WAAW,EACXC,mBAAmB,IAElBC,KAAKC,UACL,MAAMlB,QAAiBF,KAAKG,KAAKkB,cAAcrB,KAAK3E,QAAQhB,IACpC,MAApB6F,EAASG,UACZC,EAAAA,EAAAA,IAAU,CACT/G,KAAM,UACNgH,QAAS,OACTC,SAAU,YAELR,KAAKS,aAEPT,KAAKjG,SAASC,OAAS,GAE1BgG,KAAKtF,OAASsF,KAAKjG,SAAS,GAAGM,GAAGM,WAClCqF,KAAKzF,UAAUyF,KAAKjG,SAAS,KAE7BiG,KAAK3E,QAAU,QAIjBiG,MAAM,MACNhB,EAAAA,EAAAA,IAAU,CACT/G,KAAM,OACNgH,QAAS,QACTC,SAAU,OAGd,EAGA,aAAM1E,GACL,IACC,IAAIgE,EAAS,IAAIE,KAAK3E,SACtByE,EAAOnC,QAAU4D,KAAKC,MAAMxB,KAAK3E,QAAQsC,SACzCmC,EAAO9B,GAAKuD,KAAKC,MAAMxB,KAAK3E,QAAQ2C,IACpC8B,EAAOvB,sBAAwBgD,KAAKC,MAAMxB,KAAK3E,QAAQkD,uBACvDuB,EAAOzB,gBAAkBkD,KAAKC,MAAMxB,KAAK3E,QAAQgD,iBACjD,MAAM6B,QAAiBF,KAAKG,KAAKsB,cAAc3B,EAAOzF,GAAIyF,GAClC,MAApBI,EAASG,UACZC,EAAAA,EAAAA,IAAU,CACT/G,KAAM,UACNgH,QAAS,OACTC,SAAU,YAELR,KAAKS,aAEb,CAAE,MAAOiB,IACRpB,EAAAA,EAAAA,IAAU,CACT/G,KAAM,QACNgH,QAAS,iBACTC,SAAU,KAEZ,CACD,EAEA,aAAMxE,GACL,IACC,IAAI8D,EAAS,IAAIE,KAAK3E,SACtByE,EAAO9E,KAAO8E,EAAO9E,KAAO,MAC5B8E,EAAOnC,QAAU4D,KAAKC,MAAMxB,KAAK3E,QAAQsC,SACzCmC,EAAO9B,GAAKuD,KAAKC,MAAMxB,KAAK3E,QAAQ2C,IACpC8B,EAAOvB,sBAAwBgD,KAAKC,MAAMxB,KAAK3E,QAAQkD,uBACvDuB,EAAOzB,gBAAkBkD,KAAKC,MAAMxB,KAAK3E,QAAQgD,iBACjD,MAAM6B,QAAiBF,KAAKG,KAAKC,cAAcN,GAC/C,GAAwB,MAApBI,EAASG,UACZC,EAAAA,EAAAA,IAAU,CACT/G,KAAM,UACNgH,QAAS,OACTC,SAAU,YAELR,KAAKS,aAGPT,KAAKjG,SAASC,OAAS,GAAG,CAC7B,MAAMgC,EAAUgE,KAAKjG,SAAS4G,KAAKC,GAAOA,EAAI5F,OAAS8E,EAAO9E,OAASgF,KAAKjG,SAASiG,KAAKjG,SAASC,OAAS,GAC5GgG,KAAKzF,UAAUyB,EAChB,CAEF,CAAE,MAAO0F,IACRpB,EAAAA,EAAAA,IAAU,CACT/G,KAAM,QACNgH,QAAS,iBACTC,SAAU,KAEZ,CACD,EAEAjG,SAAAA,CAAUqG,GACTZ,KAAKtF,OAASkG,EAAIvG,GAAGM,WACrBqF,KAAK3E,QAAU,IAAKuF,GACpBZ,KAAK3E,QAAQsC,QAAU4D,KAAKI,UAAU3B,KAAK3E,QAAQsC,QAAS,KAAM,GAClEqC,KAAK3E,QAAQ2C,GAAKuD,KAAKI,UAAU3B,KAAK3E,QAAQ2C,GAAI,KAAM,GACxDgC,KAAK3E,QAAQkD,sBAAwBgD,KAAKI,UAAU3B,KAAK3E,QAAQkD,sBAAuB,KAAM,GAC9FyB,KAAK3E,QAAQgD,gBAAkBkD,KAAKI,UAAU3B,KAAK3E,QAAQgD,gBAAiB,KAAM,EACnF,GAEDuD,OAAAA,GACC5B,KAAKS,YACN,EACAoB,OAAAA,GACK7B,KAAKjG,SAASC,OAAS,IACtBgG,KAAK8B,QACR9B,KAAKzF,UAAUyF,KAAK8B,SAGpB9B,KAAKzF,UAAUyF,KAAKjG,SAAS,IAGhC,G,WC/XD,MAAMgI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/TestEnv.vue", "webpack://frontend-web/./src/views/TestEnv.vue?ff5b"], "sourcesContent": ["<template>\r\n\t<div class=\"test-env-container\">\r\n\t\t<!-- 左侧环境列表 -->\r\n\t\t<div class=\"env-sidebar\">\r\n\t\t\t<div class=\"sidebar-header\">\r\n\t\t\t\t<div class=\"sidebar-title\">\r\n\t\t\t\t\t<el-icon><Monitor /></el-icon>\r\n\t\t\t\t\t<h2>测试环境</h2>\r\n\t\t\t\t</div>\r\n\t\t\t\t<el-tooltip content=\"添加新测试环境\" placement=\"right\" effect=\"light\">\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t@click=\"addEnv\" \r\n\t\t\t\t\t\ttype=\"primary\" \r\n\t\t\t\t\t\tclass=\"add-env-btn\"\r\n\t\t\t\t\t\tround\r\n\t\t\t\t\t\t:icon=\"Plus\">\r\n\t\t\t\t\t\t添加环境\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</el-tooltip>\r\n\t\t\t</div>\r\n\t\t\t<el-scrollbar class=\"env-list-scrollbar\">\r\n\t\t\t\t<div class=\"env-list\">\r\n\t\t\t\t\t<template v-if=\"testEnvs.length > 0\">\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-for=\"item in testEnvs\" \r\n\t\t\t\t\t\t\t:key=\"item.id\"\r\n\t\t\t\t\t\t\t@click=\"selectEnv(item)\"\r\n\t\t\t\t\t\t\tclass=\"env-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'env-item-active': active === item.id.toString() }\">\r\n\t\t\t\t\t\t\t<div class=\"env-icon\">\r\n\t\t\t\t\t\t\t\t<el-icon><Connection /></el-icon>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<span class=\"env-name\">{{ item.name }}</span>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t<div v-else class=\"env-list-empty\">\r\n\t\t\t\t\t\t<el-empty :image-size=\"64\" description=\"无环境配置\"></el-empty>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</el-scrollbar>\r\n\t\t</div>\r\n\r\n\t\t<!-- 右侧内容区 -->\r\n\t\t<div class=\"main-content\">\r\n\t\t\t<template v-if=\"EnvInfo\">\r\n\t\t\t\t<div class=\"env-header\">\r\n\t\t\t\t\t<div class=\"env-header-info\">\r\n\t\t\t\t\t\t<div class=\"env-title\">\r\n\t\t\t\t\t\t\t<el-icon><Monitor /></el-icon>\r\n\t\t\t\t\t\t\t<h2>{{ EnvInfo.name }}</h2>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"env-subtitle\">\r\n\t\t\t\t\t\t\t<el-icon><Link /></el-icon>\r\n\t\t\t\t\t\t\t<span>{{ EnvInfo.host || '未设置服务器地址' }}</span>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"env-actions\">\r\n\t\t\t\t\t\t<el-button-group>\r\n\t\t\t\t\t\t\t<el-tooltip content=\"保存环境配置\" placement=\"top\">\r\n\t\t\t\t\t\t\t\t<el-button @click=\"saveEnv\" type=\"primary\" :icon=\"Check\">保存</el-button>\r\n\t\t\t\t\t\t\t</el-tooltip>\r\n\t\t\t\t\t\t\t<el-tooltip content=\"复制环境配置\" placement=\"top\">\r\n\t\t\t\t\t\t\t\t<el-button @click=\"copyEnv\" type=\"info\" :icon=\"CopyDocument\">复制</el-button>\r\n\t\t\t\t\t\t\t</el-tooltip>\r\n\t\t\t\t\t\t\t<el-tooltip content=\"删除环境配置\" placement=\"top\">\r\n\t\t\t\t\t\t\t\t<el-button @click=\"delEnv\" type=\"danger\" :icon=\"Delete\">删除</el-button>\r\n\t\t\t\t\t\t\t</el-tooltip>\r\n\t\t\t\t\t\t</el-button-group>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<!-- 主要内容区域，使用el-scrollbar确保内容可滚动 -->\r\n\t\t\t\t<el-scrollbar class=\"env-content-scroll\">\r\n\t\t\t\t\t<div class=\"env-content\">\r\n\t\t\t\t\t\t<!-- 基本配置信息 -->\r\n\t\t\t\t\t\t<el-card class=\"config-card\">\r\n\t\t\t\t\t\t\t<template #header>\r\n\t\t\t\t\t\t\t\t<div class=\"card-header\">\r\n\t\t\t\t\t\t\t\t\t<el-icon><InfoFilled /></el-icon>\r\n\t\t\t\t\t\t\t\t\t<span>基本信息</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t\t\t\t<el-input v-model=\"EnvInfo.name\" placeholder=\"环境名称\" size=\"default\">\r\n\t\t\t\t\t\t\t\t\t<template #prepend>\r\n\t\t\t\t\t\t\t\t\t\t<el-icon><EditPen /></el-icon>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"prepend-text\">环境名称</span>\r\n\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t\t\t\t<el-input v-model=\"EnvInfo.host\" placeholder=\"服务器域名或IP地址\" size=\"default\">\r\n\t\t\t\t\t\t\t\t\t<template #prepend>\r\n\t\t\t\t\t\t\t\t\t\t<el-icon><Link /></el-icon>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"prepend-text\">服务器地址</span>\r\n\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t<div class=\"config-grid\">\r\n\t\t\t\t\t\t\t<!-- 环境配置卡片 -->\r\n\t\t\t\t\t\t\t<el-card class=\"config-card\">\r\n\t\t\t\t\t\t\t\t<template #header>\r\n\t\t\t\t\t\t\t\t\t<div class=\"card-header\">\r\n\t\t\t\t\t\t\t\t\t\t<el-icon><Setting /></el-icon>\r\n\t\t\t\t\t\t\t\t\t\t<span>环境配置</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t<el-tabs type=\"border-card\" class=\"env-tabs\" stretch v-model=\"headersActiveTab\">\r\n\t\t\t\t\t\t\t\t\t<el-tab-pane label=\"全局请求头\" name=\"headers\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"editor-wrapper\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Editor \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"EnvInfo.headers\" \r\n\t\t\t\t\t\t\t\t\t\t\t\theight=\"260px\" \r\n\t\t\t\t\t\t\t\t\t\t\t\ttheme=\"chrome\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tlang=\"json\">\r\n\t\t\t\t\t\t\t\t\t\t\t</Editor>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</el-tab-pane>\r\n\t\t\t\t\t\t\t\t\t<el-tab-pane label=\"数据库配置\" name=\"db\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"editor-wrapper\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Editor \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"EnvInfo.db\" \r\n\t\t\t\t\t\t\t\t\t\t\t\theight=\"260px\" \r\n\t\t\t\t\t\t\t\t\t\t\t\ttheme=\"chrome\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tlang=\"json\">\r\n\t\t\t\t\t\t\t\t\t\t\t</Editor>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</el-tab-pane>\r\n\t\t\t\t\t\t\t\t</el-tabs>\r\n\t\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t\t<!-- 全局变量卡片 -->\r\n\t\t\t\t\t\t\t<el-card class=\"config-card\">\r\n\t\t\t\t\t\t\t\t<template #header>\r\n\t\t\t\t\t\t\t\t\t<div class=\"card-header\">\r\n\t\t\t\t\t\t\t\t\t\t<el-icon><Paperclip /></el-icon>\r\n\t\t\t\t\t\t\t\t\t\t<span>全局变量</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t<el-tabs type=\"border-card\" class=\"env-tabs\" stretch v-model=\"globalActiveTab\">\r\n\t\t\t\t\t\t\t\t\t<el-tab-pane label=\"全局变量\" name=\"global\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"editor-wrapper\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Editor \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"EnvInfo.global_variable\" \r\n\t\t\t\t\t\t\t\t\t\t\t\theight=\"260px\" \r\n\t\t\t\t\t\t\t\t\t\t\t\ttheme=\"chrome\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tlang=\"json\">\r\n\t\t\t\t\t\t\t\t\t\t\t</Editor>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</el-tab-pane>\r\n\t\t\t\t\t\t\t\t\t<el-tab-pane label=\"调试运行变量\" name=\"debug\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"editor-wrapper\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Editor \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"EnvInfo.debug_global_variable\" \r\n\t\t\t\t\t\t\t\t\t\t\t\theight=\"260px\" \r\n\t\t\t\t\t\t\t\t\t\t\t\ttheme=\"chrome\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tlang=\"json\">\r\n\t\t\t\t\t\t\t\t\t\t\t</Editor>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</el-tab-pane>\r\n\t\t\t\t\t\t\t\t</el-tabs>\r\n\t\t\t\t\t\t\t</el-card>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 全局函数卡片 -->\r\n\t\t\t\t\t\t<el-card class=\"config-card function-card\">\r\n\t\t\t\t\t\t\t<template #header>\r\n\t\t\t\t\t\t\t\t<div class=\"card-header\">\r\n\t\t\t\t\t\t\t\t\t<el-icon><Star /></el-icon>\r\n\t\t\t\t\t\t\t\t\t<span>全局函数</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t<div class=\"editor-wrapper\">\r\n\t\t\t\t\t\t\t\t<Editor \r\n\t\t\t\t\t\t\t\t\tv-model=\"EnvInfo.global_func\" \r\n\t\t\t\t\t\t\t\t\theight=\"600px\"\r\n\t\t\t\t\t\t\t\t\tlang=\"python\" \r\n\t\t\t\t\t\t\t\t\ttheme=\"monokai\">\r\n\t\t\t\t\t\t\t\t</Editor>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</el-card>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-scrollbar>\r\n\t\t\t</template>\r\n\r\n\t\t\t<!-- 空状态 -->\r\n\t\t\t<div class=\"env-empty\" v-if=\"testEnvs.length === 0\">\r\n\t\t\t\t<el-empty description=\"暂无测试环境\">\r\n\t\t\t\t\t<el-button type=\"primary\" @click=\"addEnv\" round>\r\n\t\t\t\t\t\t<el-icon><Plus /></el-icon>\r\n\t\t\t\t\t\t<span>添加测试环境</span>\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</el-empty>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n/*\r\n功能实现:\r\n\t测试环境的增删查改\r\n*/\r\nimport { mapState, mapActions, mapMutations } from 'vuex';\r\nimport Editor from '../components/common/Editor.vue';\r\nimport { ElMessage, ElMessageBox } from 'element-plus';\r\nimport { \r\n\tCheck, \r\n\tDelete, \r\n\tCopyDocument, \r\n\tEditPen, \r\n\tLink, \r\n\tMonitor, \r\n\tSetting,\r\n\tPlus,\r\n\tConnection,\r\n\tCircleCheck,\r\n\tInfoFilled,\r\n\tPaperclip,\r\n\tStar\r\n} from '@element-plus/icons-vue';\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\tEditor,\r\n\t\tCheck,\r\n\t\tDelete,\r\n\t\tCopyDocument,\r\n\t\tEditPen,\r\n\t\tLink,\r\n\t\tMonitor,\r\n\t\tSetting,\r\n\t\tPlus,\r\n\t\tConnection,\r\n\t\tCircleCheck,\r\n\t\tInfoFilled,\r\n\t\tPaperclip,\r\n\t\tStar\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tactive: '1',\r\n\t\t\tEnvInfo: null,\r\n\t\t\theadersActiveTab: 'headers',\r\n\t\t\tglobalActiveTab: 'global'\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\t...mapState(['pro', 'testEnvs', 'envInfo'])\r\n\t},\r\n\tmethods: {\r\n\t\t...mapActions(['getAllEnvs']),\r\n\r\n\t\t// 创建环境\r\n\t\tasync addEnv() {\r\n\t\t\tconst params = { project: this.pro.id, name: \"新测试环境\" }\r\n\t\t\tconst response = await this.$api.createTestEnv(params);\r\n\t\t\tif (response.status === 201) {\r\n\t\t\t\tElMessage({\r\n\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\tmessage: '添加成功',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\t\t\t\tawait this.getAllEnvs();\r\n\t\t\t\t\r\n\t\t\t\t// 选中新创建的环境\r\n\t\t\t\tif (this.testEnvs.length > 0) {\r\n\t\t\t\t\tconst newEnv = this.testEnvs.find(env => env.name === \"新测试环境\") || this.testEnvs[this.testEnvs.length - 1];\r\n\t\t\t\t\tthis.selectEnv(newEnv);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 删除环境\r\n\t\tasync delEnv() {\r\n\t\t\tElMessageBox.confirm('确定要删除该测试环境吗？此操作不可恢复。', '删除确认', {\r\n\t\t\t\tconfirmButtonText: '确定删除',\r\n\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\ttype: 'warning',\r\n\t\t\t\tdraggable: true,\r\n\t\t\t\tcloseOnClickModal: false\r\n\t\t\t})\r\n\t\t\t\t.then(async () => {\r\n\t\t\t\t\tconst response = await this.$api.deleteTestEnv(this.EnvInfo.id);\r\n\t\t\t\t\tif (response.status === 204) {\r\n\t\t\t\t\t\tElMessage({\r\n\t\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\t\tmessage: '删除成功',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tawait this.getAllEnvs();\r\n\t\t\t\t\t\t// 重新选中环境\r\n\t\t\t\t\t\tif (this.testEnvs.length > 0) {\r\n\t\t\t\t\t\t\t// 设置默认显示激活的测试场景\r\n\t\t\t\t\t\t\tthis.active = this.testEnvs[0].id.toString();\r\n\t\t\t\t\t\t\tthis.selectEnv(this.testEnvs[0]);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.EnvInfo = null;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(() => {\r\n\t\t\t\t\tElMessage({\r\n\t\t\t\t\t\ttype: 'info',\r\n\t\t\t\t\t\tmessage: '已取消删除',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 保存修改\r\n\t\tasync saveEnv() {\r\n\t\t\ttry {\r\n\t\t\t\tlet params = {...this.EnvInfo}\r\n\t\t\t\tparams.headers = JSON.parse(this.EnvInfo.headers);\r\n\t\t\t\tparams.db = JSON.parse(this.EnvInfo.db);\r\n\t\t\t\tparams.debug_global_variable = JSON.parse(this.EnvInfo.debug_global_variable);\r\n\t\t\t\tparams.global_variable = JSON.parse(this.EnvInfo.global_variable);\r\n\t\t\t\tconst response = await this.$api.updateTestEnv(params.id, params)\r\n\t\t\t\tif (response.status === 200) {\r\n\t\t\t\t\tElMessage({\r\n\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\tmessage: '保存成功',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tawait this.getAllEnvs();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tElMessage({\r\n\t\t\t\t\ttype: 'error',\r\n\t\t\t\t\tmessage: 'JSON格式错误，请检查配置',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 复制环境\r\n\t\tasync copyEnv() {\r\n\t\t\ttry {\r\n\t\t\t\tlet params = {...this.EnvInfo}\r\n\t\t\t\tparams.name = params.name + '_副本'\r\n\t\t\t\tparams.headers = JSON.parse(this.EnvInfo.headers);\r\n\t\t\t\tparams.db = JSON.parse(this.EnvInfo.db);\r\n\t\t\t\tparams.debug_global_variable = JSON.parse(this.EnvInfo.debug_global_variable);\r\n\t\t\t\tparams.global_variable = JSON.parse(this.EnvInfo.global_variable);\r\n\t\t\t\tconst response = await this.$api.createTestEnv(params);\r\n\t\t\t\tif (response.status === 201) {\r\n\t\t\t\t\tElMessage({\r\n\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\tmessage: '复制成功',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tawait this.getAllEnvs();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 选中新复制的环境\r\n\t\t\t\t\tif (this.testEnvs.length > 0) {\r\n\t\t\t\t\t\tconst copyEnv = this.testEnvs.find(env => env.name === params.name) || this.testEnvs[this.testEnvs.length - 1];\r\n\t\t\t\t\t\tthis.selectEnv(copyEnv);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tElMessage({\r\n\t\t\t\t\ttype: 'error',\r\n\t\t\t\t\tmessage: 'JSON格式错误，请检查配置',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 选中环境\r\n\t\tselectEnv(env) {\r\n\t\t\tthis.active = env.id.toString()\r\n\t\t\tthis.EnvInfo = { ...env };\r\n\t\t\tthis.EnvInfo.headers = JSON.stringify(this.EnvInfo.headers, null, 4);\r\n\t\t\tthis.EnvInfo.db = JSON.stringify(this.EnvInfo.db, null, 4);\r\n\t\t\tthis.EnvInfo.debug_global_variable = JSON.stringify(this.EnvInfo.debug_global_variable, null, 4);\r\n\t\t\tthis.EnvInfo.global_variable = JSON.stringify(this.EnvInfo.global_variable, null, 4);\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tthis.getAllEnvs();\r\n\t},\r\n\tmounted() {\r\n\t\tif (this.testEnvs.length > 0) {\r\n\t\t\tif (this.envInfo) {\r\n\t\t\t\tthis.selectEnv(this.envInfo);\r\n\t\t\t} else {\r\n\t\t\t\t// 设置默认选中的测试场景\r\n\t\t\t\tthis.selectEnv(this.testEnvs[0]);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.test-env-container {\r\n\tdisplay: flex;\r\n\theight: 100vh;\r\n\tbackground-color: #f5f7fa;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n/* 侧边栏样式 */\r\n.env-sidebar {\r\n\twidth: 240px;\r\n\tmin-width: 240px;\r\n\tbackground-color: #fff;\r\n\tbox-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n\tz-index: 10;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\theight: 100%;\r\n\toverflow: hidden;\r\n\tborder-right: 1px solid #ebeef5;\r\n}\r\n\r\n.sidebar-header {\r\n\tpadding: 16px;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 16px;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n\tbackground-color: #f7f9fc;\r\n}\r\n\r\n.sidebar-title {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8px;\r\n}\r\n\r\n.sidebar-title h2 {\r\n\tmargin: 0;\r\n\tfont-size: 18px;\r\n\tfont-weight: 600;\r\n\tcolor: #303133;\r\n}\r\n\r\n.add-env-btn {\r\n\twidth: 100%;\r\n}\r\n\r\n.env-list-scrollbar {\r\n\theight: calc(100% - 95px);\r\n\toverflow: hidden;\r\n}\r\n\r\n.env-list {\r\n\tpadding: 8px 0;\r\n\tmin-height: 100%;\r\n}\r\n\r\n.env-list-empty {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\theight: 80%;\r\n}\r\n\r\n.env-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 14px 16px;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s;\r\n\tborder-radius: 6px;\r\n\tmargin: 4px 8px;\r\n\tbackground-color: #f7f8fa;\r\n\tborder: 1px solid transparent;\r\n}\r\n\r\n.env-item:hover {\r\n\tbackground-color: #ecf5ff;\r\n\tborder-color: #d9ecff;\r\n\ttransform: translateY(-2px);\r\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.env-item-active {\r\n\tbackground-color: #ecf5ff;\r\n\tcolor: #409eff;\r\n\tfont-weight: 500;\r\n\tborder-left: 3px solid #409eff;\r\n\tbox-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);\r\n}\r\n\r\n.env-item-active:hover {\r\n\tbackground-color: #ecf5ff;\r\n}\r\n\r\n.env-icon {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\twidth: 32px;\r\n\theight: 32px;\r\n\tborder-radius: 8px;\r\n\tbackground-color: rgba(64, 158, 255, 0.1);\r\n\tmargin-right: 12px;\r\n\tcolor: #409eff;\r\n}\r\n\r\n.env-name {\r\n\tflex: 1;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\tfont-size: 14px;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\theight: 100%;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n/* 环境头部 */\r\n.env-header {\r\n\tpadding: 16px 24px;\r\n\tbackground-color: #fff;\r\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tz-index: 5;\r\n}\r\n\r\n.env-header-info {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 4px;\r\n}\r\n\r\n.env-title {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 12px;\r\n\tmargin-bottom: 4px;\r\n}\r\n\r\n.env-title h2 {\r\n\tmargin: 0;\r\n\tfont-size: 20px;\r\n\tfont-weight: 600;\r\n\tcolor: #303133;\r\n}\r\n\r\n.env-subtitle {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8px;\r\n\tcolor: #909399;\r\n\tfont-size: 13px;\r\n}\r\n\r\n.env-status {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 4px;\r\n}\r\n\r\n.env-actions .el-button-group {\r\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n\tborder-radius: 4px;\r\n\toverflow: hidden;\r\n}\r\n\r\n/* 内容滚动区 */\r\n.env-content-scroll {\r\n\tflex: 1;\r\n\toverflow: hidden;\r\n\theight: calc(100% - 80px);\r\n}\r\n\r\n.env-content {\r\n\tpadding: 16px 24px 32px;\r\n}\r\n\r\n/* 卡片样式 */\r\n.config-card {\r\n\tmargin-bottom: 20px;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n\toverflow: hidden;\r\n}\r\n\r\n.card-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8px;\r\n\tfont-weight: 600;\r\n\tcolor: #303133;\r\n\tfont-size: 16px;\r\n}\r\n\r\n.config-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\r\n\tgap: 20px;\r\n\tmargin-bottom: 20px;\r\n}\r\n\r\n.form-group {\r\n\tmargin-bottom: 16px;\r\n}\r\n\r\n.form-group:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.prepend-text {\r\n\tmargin-left: 4px;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.env-tabs {\r\n\tborder-radius: 4px;\r\n\toverflow: hidden;\r\n\tborder: none;\r\n\tbox-shadow: none;\r\n}\r\n\r\n.editor-wrapper {\r\n\tborder-radius: 4px;\r\n\toverflow: hidden;\r\n\tborder: 1px solid #ebeef5;\r\n}\r\n\r\n.function-card .editor-wrapper {\r\n\tborder: none;\r\n}\r\n\r\n/* 空状态 */\r\n.env-empty {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1400px) {\r\n\t.config-grid {\r\n\t\tgrid-template-columns: 1fr;\r\n\t}\r\n}\r\n\r\n@media (max-width: 768px) {\r\n\t.env-sidebar {\r\n\t\twidth: 200px;\r\n\t\tmin-width: 200px;\r\n\t}\r\n\t\r\n\t.env-icon {\r\n\t\twidth: 28px;\r\n\t\theight: 28px;\r\n\t\tmargin-right: 8px;\r\n\t}\r\n\t\r\n\t.env-title h2 {\r\n\t\tfont-size: 18px;\r\n\t}\r\n}\r\n\r\n/* 深色模式支持的预留样式 */\r\n@media (prefers-color-scheme: dark) {\r\n\t/* 未来可添加深色模式样式 */\r\n}\r\n\r\n/* 滚动条美化 */\r\n:deep(.el-scrollbar__bar) {\r\n\topacity: 0.3;\r\n}\r\n\r\n:deep(.el-scrollbar__bar:hover) {\r\n\topacity: 0.8;\r\n}\r\n\r\n:deep(.el-tabs__nav) {\r\n\tborder: none !important;\r\n}\r\n\r\n:deep(.el-tabs--border-card) {\r\n\tbackground: #fff;\r\n\tborder: none;\r\n\tbox-shadow: none;\r\n}\r\n\r\n:deep(.el-tabs--border-card > .el-tabs__header) {\r\n\tbackground-color: #f7f9fc;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {\r\n\tbackground-color: #ecf5ff;\r\n\tcolor: #409eff;\r\n\tborder-right-color: #f0f0f0;\r\n\tborder-left-color: #f0f0f0;\r\n}\r\n\r\n:deep(.el-card__header) {\r\n\tpadding: 12px 16px;\r\n\tbackground-color: #f7f9fc;\r\n}\r\n\r\n:deep(.el-tag) {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 4px;\r\n}\r\n</style>\r\n", "import { render } from \"./TestEnv.vue?vue&type=template&id=0b5dfd14&scoped=true\"\nimport script from \"./TestEnv.vue?vue&type=script&lang=js\"\nexport * from \"./TestEnv.vue?vue&type=script&lang=js\"\n\nimport \"./TestEnv.vue?vue&type=style&index=0&id=0b5dfd14&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0b5dfd14\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_icon", "_component_Monitor", "_component_el_tooltip", "content", "placement", "effect", "_component_el_button", "onClick", "$options", "addEnv", "type", "round", "icon", "_ctx", "Plus", "_cache", "_component_el_scrollbar", "_hoisted_5", "testEnvs", "length", "_Fragment", "key", "_renderList", "item", "id", "$event", "selectEnv", "_normalizeClass", "$data", "active", "toString", "_hoisted_7", "_component_Connection", "_hoisted_8", "_toDisplayString", "name", "_hoisted_9", "_component_el_empty", "description", "_hoisted_10", "EnvInfo", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_component_Link", "host", "_hoisted_15", "_component_el_button_group", "saveEnv", "Check", "copyEnv", "CopyDocument", "delEnv", "Delete", "_hoisted_16", "_component_el_card", "header", "_withCtx", "_hoisted_17", "_component_InfoFilled", "_hoisted_18", "_component_el_input", "placeholder", "size", "prepend", "_component_EditPen", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_Setting", "_component_el_tabs", "stretch", "headersActiveTab", "_component_el_tab_pane", "label", "_hoisted_22", "_component_Editor", "headers", "height", "theme", "lang", "_hoisted_23", "db", "_hoisted_24", "_component_Paperclip", "globalActiveTab", "_hoisted_25", "global_variable", "_hoisted_26", "debug_global_variable", "_hoisted_27", "_component_Star", "_hoisted_28", "global_func", "_hoisted_29", "_component_Plus", "components", "Editor", "EditPen", "Link", "Monitor", "Setting", "Connection", "CircleCheck", "InfoFilled", "Paperclip", "Star", "data", "computed", "mapState", "methods", "mapActions", "params", "project", "this", "pro", "response", "$api", "createTestEnv", "status", "ElMessage", "message", "duration", "getAllEnvs", "newEnv", "find", "env", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "draggable", "closeOnClickModal", "then", "async", "deleteTestEnv", "catch", "JSON", "parse", "updateTestEnv", "error", "stringify", "created", "mounted", "envInfo", "__exports__", "render"], "sourceRoot": ""}