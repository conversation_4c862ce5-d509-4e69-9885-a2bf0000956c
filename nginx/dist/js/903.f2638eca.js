"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[903],{62278:function(e,l,a){a.r(l),a.d(l,{default:function(){return ae}});a(18111),a(22489),a(20116);var t=a(56768),u=a(90144),s=a(24232),n=a(45130),i=a(60782),o=a(67638),r=a(51219),d=a(57477);const c={class:"bug-dashboard"},v={class:"dashboard-container"},k={class:"dashboard-header"},g={class:"header-right"},p={class:"stats-section"},b={class:"stat-icon"},f={class:"stat-data"},_={class:"stat-value"},m={class:"stat-label"},h={class:"charts-section"},F={class:"chart-wrapper"},y={class:"chart-header"},L={class:"chart-actions"},w={class:"chart-wrapper"},C={class:"chart-header"},W={class:"chart-actions"},R={class:"bug-list-section"},X={class:"list-header"},E={class:"title-section"},B={class:"bug-counter"},x={class:"filter-section"},$={class:"bug-expand-detail"},V={class:"bug-title"},K={class:"time-info"},z={class:"action-buttons"},D={class:"pagination-wrapper"},I={class:"drawer-header"},U={key:0,class:"bug-detail-content"},P={class:"card-header"},A={class:"bug-description"},Q={class:"card-header"},T={class:"card-header"},q={class:"activity-title"},N={key:0,class:"activity-remark"},j={class:"activity-footer"},S={class:"operator"},G={class:"time"},H={class:"drawer-actions"},M={class:"status-options"},O=["onClick"],Y={class:"option-label"},J={class:"dialog-footer"};var Z={__name:"BugManage",setup(e){const{proxy:l}=(0,t.nI)()||{proxy:null},a=(0,i.Pj)(),Z=(0,t.EW)(()=>a.state.pro),ee=(0,u.KR)([]),le=(0,u.KR)(null),ae=(0,u.KR)(null),te=(0,u.KR)([]),ue=(0,u.KR)(""),se=(0,u.KR)("all"),ne=(0,u.KR)(!1),ie=(0,u.KR)(!1),oe=(0,u.KR)(!1),re=(0,u.KR)(!1),de=(0,u.KR)(null),ce=(0,u.KR)(null),ve=(0,u.KR)(1),ke=(0,u.KR)(10),ge={"待处理":{type:"danger",icon:"Warning",class:"pending"},"处理中":{type:"warning",icon:"Loading",class:"in-progress"},"处理完成":{type:"success",icon:"CircleCheck",class:"completed"},"无需处理":{type:"info",icon:"Remove",class:"unnecessary"},"已关闭":{type:"info",icon:"CircleClose",class:"closed"}},pe=[{label:"待处理",value:"待处理",icon:"Warning"},{label:"处理中",value:"处理中",icon:"Loading"},{label:"处理完成",value:"处理完成",icon:"CircleCheck"},{label:"无需处理",value:"无需处理",icon:"Remove"},{label:"已关闭",value:"已关闭",icon:"CircleClose"}],be=(0,u.KR)({id:"",status:"",remark:""}),fe=(0,t.EW)(()=>({pending:ee.value.filter(e=>"待处理"===e.status),inProgress:ee.value.filter(e=>"处理中"===e.status),completed:ee.value.filter(e=>"处理完成"===e.status),unnecessary:ee.value.filter(e=>"无需处理"===e.status),closed:ee.value.filter(e=>"已关闭"===e.status)})),_e=(0,t.EW)(()=>({total:ee.value.length,pending:fe.value.pending.length,inProgress:fe.value.inProgress.length,completed:fe.value.completed.length,unnecessary:fe.value.unnecessary.length,closed:fe.value.closed.length})),me=(0,t.EW)(()=>[{type:"total",icon:"Tickets",value:_e.value.total,label:"Bug总数"},{type:"pending",icon:"Warning",value:_e.value.pending,label:"待处理"},{type:"in-progress",icon:"Loading",value:_e.value.inProgress,label:"处理中"},{type:"completed",icon:"CircleCheck",value:_e.value.completed,label:"已完成"},{type:"unnecessary",icon:"Remove",value:_e.value.unnecessary,label:"无需处理"},{type:"closed",icon:"CircleClose",value:_e.value.closed,label:"已关闭"}]),he=(0,t.EW)(()=>{if(!ue.value)return te.value;const e=ue.value.toLowerCase();return te.value.filter(l=>l.desc.toLowerCase().includes(e)||l.interface_url.toLowerCase().includes(e)||String(l.id).includes(e))}),Fe=(0,t.EW)(()=>{const e=(ve.value-1)*ke.value,l=e+ke.value;return he.value.slice(e,l)}),ye=async()=>{if(Z.value?.id){ne.value=!0;try{if(!l||!l.$api)return void r.nk.error("API 初始化失败");const e=await l.$api.getBugs({project:Z.value.id});200===e.status&&(ee.value=e.data,Le(se.value))}catch(e){r.nk.error("获取Bug列表失败"),console.error("Failed to fetch bugs:",e)}finally{ne.value=!1}}},Le=e=>{switch(se.value=e,ve.value=1,e){case"pending":te.value=fe.value.pending;break;case"inProgress":te.value=fe.value.inProgress;break;case"completed":te.value=fe.value.completed;break;case"unnecessary":te.value=fe.value.unnecessary;break;case"closed":te.value=fe.value.closed;break;default:te.value=ee.value}},we=async e=>{le.value=e,ie.value=!0;try{if(!l||!l.$api)return;const a=await l.$api.getBugLogs({bug:e.id});200===a.status&&a.data.length>0?ae.value=a.data:ae.value=[]}catch(a){r.nk.error("获取Bug处理记录失败"),console.error("Failed to fetch bug logs:",a),ae.value=[]}},Ce=e=>{be.value={id:e.id,status:e.status,remark:""},oe.value=!0},We=async()=>{if(be.value.id)if(be.value.remark.trim()){re.value=!0;try{if(!l||!l.$api)return;const e=await l.$api.updateBug(be.value.id,be.value);if(200===e.status&&(r.nk.success({message:"Bug状态更新成功",type:"success",duration:2e3}),oe.value=!1,await ye(),ie.value&&le.value)){const e=await l.$api.getBugLogs({bug:le.value.id});if(200===e.status){ae.value=e.data;const l=ee.value.find(e=>e.id===le.value.id);l&&(le.value=l)}}}catch(e){r.nk.error("更新Bug状态失败"),console.error("Failed to update bug:",e)}finally{re.value=!1}}else r.nk.warning("请输入处理备注")},Re=async e=>{ne.value=!0;try{if(!l||!l.$api)return;const a=await l.$api.deleteBug(e);204===a.status&&(r.nk.success({message:"删除成功",type:"success",duration:2e3}),await ye(),le.value&&le.value.id===e&&(ie.value=!1))}catch(a){r.nk.error("删除Bug失败"),console.error("Failed to delete bug:",a)}finally{ne.value=!1}},Xe=()=>{if(!de.value||!ce.value)return;if(!l||!l.$chart)return;const e=[ee.value.length,fe.value.completed.length,fe.value.inProgress.length,fe.value.pending.length,fe.value.unnecessary.length,fe.value.closed.length],a=["Bug总数","处理完成","处理中","待处理","无需处理","已关闭"];l.$chart.chart1(de.value,e,a),l.$chart.chart2(ce.value,[{value:fe.value.completed.length,name:"处理完成"},{value:fe.value.inProgress.length,name:"处理中"},{value:fe.value.pending.length,name:"待处理"},{value:fe.value.unnecessary.length,name:"无需处理"},{value:fe.value.closed.length,name:"已关闭"}])},Ee=e=>{ke.value=e},Be=e=>{ve.value=e},xe=e=>ge[e]?.type||"info",$e=e=>ge[e]?.class||"default",Ve=e=>e.includes("处理完成")?"success":e.includes("处理中")?"warning":e.includes("待处理")?"danger":e.includes("关闭")?"info":"primary",Ke=e=>e.includes("处理完成")?"CircleCheck":e.includes("处理中")?"Loading":e.includes("待处理")?"Warning":e.includes("关闭")?"CircleClose":e.includes("无需处理")?"Remove":"InfoFilled",ze=({row:e})=>`bug-row bug-status-${$e(e.status)}`;(0,t.sV)(async()=>{await ye(),(0,t.dY)(()=>{Xe()})}),(0,t.wB)(()=>ee.value,()=>{(0,t.dY)(()=>{Xe()})},{deep:!0}),(0,t.wB)(ue,()=>{ve.value=1});const De=e=>l&&l.$tools?l.$tools.rTime(e):e,Ie=e=>l&&l.$tools?l.$tools.rDate?l.$tools.rDate(e):l.$tools.rTime(e):e;return(e,l)=>{const a=(0,t.g2)("el-icon"),i=(0,t.g2)("el-button"),r=(0,t.g2)("el-badge"),Z=(0,t.g2)("el-col"),ge=(0,t.g2)("el-row"),fe=(0,t.g2)("el-radio-button"),he=(0,t.g2)("el-radio-group"),ye=(0,t.g2)("el-input"),Xe=(0,t.g2)("el-table-column"),Ue=(0,t.g2)("Time"),Pe=(0,t.g2)("el-tag"),Ae=(0,t.g2)("el-tooltip"),Qe=(0,t.g2)("el-popconfirm"),Te=(0,t.g2)("el-table"),qe=(0,t.g2)("el-pagination"),Ne=(0,t.g2)("el-descriptions-item"),je=(0,t.g2)("el-descriptions"),Se=(0,t.g2)("el-card"),Ge=(0,t.g2)("el-timeline-item"),He=(0,t.g2)("el-timeline"),Me=(0,t.g2)("el-empty"),Oe=(0,t.g2)("el-scrollbar"),Ye=(0,t.g2)("el-drawer"),Je=(0,t.g2)("el-form-item"),Ze=(0,t.g2)("el-form"),el=(0,t.g2)("el-dialog"),ll=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",c,[(0,t.bF)(Oe,{height:"calc(100vh - 55px)"},{default:(0,t.k6)(()=>[(0,t.Lk)("div",v,[(0,t.Lk)("div",k,[l[12]||(l[12]=(0,t.Lk)("div",{class:"header-left"},[(0,t.Lk)("h1",{class:"page-title"},"Bug 管理")],-1)),(0,t.Lk)("div",g,[(0,t.bF)(r,{value:_e.value.pending,hidden:0===_e.value.pending,class:"pending-badge"},{default:(0,t.k6)(()=>[(0,t.bF)(i,{type:"danger",size:"large",class:"action-btn"},{default:(0,t.k6)(()=>[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Warning))]),_:1}),(0,t.Lk)("span",null,"待处理 "+(0,s.v_)(_e.value.pending),1)]),_:1})]),_:1},8,["value","hidden"])])]),(0,t.Lk)("div",p,[(0,t.bF)(ge,{gutter:20},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(me.value,(e,l)=>((0,t.uX)(),(0,t.Wv)(Z,{span:4,key:l},{default:(0,t.k6)(()=>[(0,t.Lk)("div",{class:(0,s.C4)(["stat-card",`stat-card-${e.type}`])},[(0,t.Lk)("div",b,[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(e.icon)))]),_:2},1024)]),(0,t.Lk)("div",f,[(0,t.Lk)("div",_,(0,s.v_)(e.value),1),(0,t.Lk)("div",m,(0,s.v_)(e.label),1)])],2)]),_:2},1024))),128))]),_:1})]),(0,t.Lk)("div",h,[(0,t.bF)(ge,{gutter:20},{default:(0,t.k6)(()=>[(0,t.bF)(Z,{span:12},{default:(0,t.k6)(()=>[(0,t.Lk)("div",F,[(0,t.Lk)("div",y,[l[13]||(l[13]=(0,t.Lk)("h3",null,"Bug 分布",-1)),(0,t.Lk)("div",L,[(0,t.bF)(i,{size:"small",text:""},{default:(0,t.k6)(()=>[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Refresh))]),_:1})]),_:1})])]),(0,t.Lk)("div",{class:"chart-content",ref_key:"chart1Box",ref:de},null,512)])]),_:1}),(0,t.bF)(Z,{span:12},{default:(0,t.k6)(()=>[(0,t.Lk)("div",w,[(0,t.Lk)("div",C,[l[14]||(l[14]=(0,t.Lk)("h3",null,"状态占比",-1)),(0,t.Lk)("div",W,[(0,t.bF)(i,{size:"small",text:""},{default:(0,t.k6)(()=>[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Refresh))]),_:1})]),_:1})])]),(0,t.Lk)("div",{class:"chart-content",ref_key:"chart2Box",ref:ce},null,512)])]),_:1})]),_:1})]),(0,t.Lk)("div",R,[(0,t.Lk)("div",X,[(0,t.Lk)("div",E,[l[15]||(l[15]=(0,t.Lk)("h2",null,"BUG 列表",-1)),(0,t.Lk)("span",B,"共 "+(0,s.v_)(ee.value.length)+" 项",1)]),(0,t.Lk)("div",x,[(0,t.bF)(he,{modelValue:se.value,"onUpdate:modelValue":l[0]||(l[0]=e=>se.value=e),size:"large",onChange:l[1]||(l[1]=e=>Le(e))},{default:(0,t.k6)(()=>[(0,t.bF)(fe,{label:"all"},{default:(0,t.k6)(()=>l[16]||(l[16]=[(0,t.eW)("全部")])),_:1,__:[16]}),(0,t.bF)(fe,{label:"pending"},{default:(0,t.k6)(()=>l[17]||(l[17]=[(0,t.eW)("待处理")])),_:1,__:[17]}),(0,t.bF)(fe,{label:"inProgress"},{default:(0,t.k6)(()=>l[18]||(l[18]=[(0,t.eW)("处理中")])),_:1,__:[18]}),(0,t.bF)(fe,{label:"completed"},{default:(0,t.k6)(()=>l[19]||(l[19]=[(0,t.eW)("已完成")])),_:1,__:[19]}),(0,t.bF)(fe,{label:"unnecessary"},{default:(0,t.k6)(()=>l[20]||(l[20]=[(0,t.eW)("无需处理")])),_:1,__:[20]}),(0,t.bF)(fe,{label:"closed"},{default:(0,t.k6)(()=>l[21]||(l[21]=[(0,t.eW)("已关闭")])),_:1,__:[21]})]),_:1},8,["modelValue"]),(0,t.bF)(ye,{modelValue:ue.value,"onUpdate:modelValue":l[2]||(l[2]=e=>ue.value=e),placeholder:"搜索Bug...",class:"search-input",clearable:""},{prefix:(0,t.k6)(()=>[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Search))]),_:1})]),_:1},8,["modelValue"])])]),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(Te,{data:Fe.value,style:{width:"100%"},"row-key":"id","row-class-name":ze,onRowClick:we,class:"bug-table"},{default:(0,t.k6)(()=>[(0,t.bF)(Xe,{type:"expand"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",$,[(0,t.Lk)("p",null,[l[22]||(l[22]=(0,t.Lk)("strong",null,"Bug描述:",-1)),(0,t.eW)(" "+(0,s.v_)(e.row.desc),1)]),(0,t.Lk)("p",null,[l[23]||(l[23]=(0,t.Lk)("strong",null,"所属接口:",-1)),(0,t.eW)(" "+(0,s.v_)(e.row.interface_url),1)]),(0,t.Lk)("p",null,[l[24]||(l[24]=(0,t.Lk)("strong",null,"提交时间:",-1)),(0,t.eW)(" "+(0,s.v_)(De(e.row.create_time)),1)])])]),_:1}),(0,t.bF)(Xe,{label:"",width:"50"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",{class:(0,s.C4)(["bug-status-dot","bug-status-"+$e(e.row.status)])},null,2)]),_:1}),(0,t.bF)(Xe,{prop:"id",label:"接口名称"}),(0,t.bF)(Xe,{prop:"desc",label:"Bug描述","show-overflow-tooltip":""},{default:(0,t.k6)(e=>[(0,t.Lk)("div",V,(0,s.v_)(e.row.desc),1)]),_:1}),(0,t.bF)(Xe,{prop:"interface_url",label:"所属接口","min-width":"150","show-overflow-tooltip":""}),(0,t.bF)(Xe,{label:"提交时间"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",K,[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)(Ue)]),_:1}),(0,t.Lk)("span",null,(0,s.v_)(De(e.row.create_time)),1)])]),_:1}),(0,t.bF)(Xe,{label:"状态",width:"120",align:"center"},{default:(0,t.k6)(e=>[(0,t.bF)(Pe,{type:xe(e.row.status),effect:"light",class:(0,s.C4)(["status-tag","status-"+$e(e.row.status)])},{default:(0,t.k6)(()=>[(0,t.Lk)("span",null,(0,s.v_)(e.row.status),1)]),_:2},1032,["type","class"])]),_:1}),(0,t.bF)(Xe,{width:"140",align:"center",fixed:"right",label:"操作"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",z,[(0,t.bF)(Ae,{content:"查看详情",placement:"top"},{default:(0,t.k6)(()=>[(0,t.bF)(i,{circle:"",type:"primary",plain:"",onClick:(0,n.D$)(l=>we(e.row),["stop"])},{default:(0,t.k6)(()=>[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.View))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),(0,t.bF)(Ae,{content:"更新状态",placement:"top"},{default:(0,t.k6)(()=>[(0,t.bF)(i,{circle:"",type:"warning",plain:"",onClick:(0,n.D$)(l=>Ce(e.row),["stop"])},{default:(0,t.k6)(()=>[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Edit))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),(0,t.bF)(Ae,{content:"删除Bug",placement:"top"},{default:(0,t.k6)(()=>[(0,t.bF)(Qe,{title:"确定要删除此Bug吗?","confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:l=>Re(e.row.id)},{reference:(0,t.k6)(()=>[(0,t.bF)(i,{circle:"",type:"danger",plain:"",onClick:l[3]||(l[3]=(0,n.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Delete))]),_:1})]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[ll,ne.value]]),(0,t.Lk)("div",D,[(0,t.bF)(qe,{"current-page":ve.value,"onUpdate:currentPage":l[4]||(l[4]=e=>ve.value=e),"page-size":ke.value,"onUpdate:pageSize":l[5]||(l[5]=e=>ke.value=e),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:te.value.length,onSizeChange:Ee,onCurrentChange:Be},null,8,["current-page","page-size","total"])])])]),(0,t.bF)(Ye,{modelValue:ie.value,"onUpdate:modelValue":l[8]||(l[8]=e=>ie.value=e),direction:"rtl",size:"55%","destroy-on-close":"","custom-class":"bug-detail-drawer"},{header:(0,t.k6)(()=>[(0,t.Lk)("div",I,[l[25]||(l[25]=(0,t.Lk)("h2",{class:"drawer-title"},"Bug详情",-1)),le.value?((0,t.uX)(),(0,t.Wv)(Pe,{key:0,type:xe(le.value.status),effect:"dark",class:"drawer-status-tag"},{default:(0,t.k6)(()=>[(0,t.eW)((0,s.v_)(le.value.status),1)]),_:1},8,["type"])):(0,t.Q3)("",!0)])]),default:(0,t.k6)(()=>[(0,t.bF)(Oe,{height:"calc(100vh - 120px)"},{default:(0,t.k6)(()=>[le.value?((0,t.uX)(),(0,t.CE)("div",U,[(0,t.bF)(Se,{class:"detail-card info-card",shadow:"hover"},{header:(0,t.k6)(()=>[(0,t.Lk)("div",P,[(0,t.Lk)("h3",null,[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.InfoFilled))]),_:1}),l[26]||(l[26]=(0,t.eW)(" 基本信息"))])])]),default:(0,t.k6)(()=>[(0,t.bF)(je,{column:2,border:""},{default:(0,t.k6)(()=>[(0,t.bF)(Ne,{label:"Bug ID"},{default:(0,t.k6)(()=>[(0,t.eW)((0,s.v_)(le.value.id),1)]),_:1}),(0,t.bF)(Ne,{label:"提交者"},{default:(0,t.k6)(()=>l[27]||(l[27]=[(0,t.eW)("admin")])),_:1,__:[27]}),(0,t.bF)(Ne,{label:"所属接口"},{default:(0,t.k6)(()=>[(0,t.eW)((0,s.v_)(le.value.interface_url),1)]),_:1}),(0,t.bF)(Ne,{label:"提交时间"},{default:(0,t.k6)(()=>[(0,t.eW)((0,s.v_)(De(le.value.create_time)),1)]),_:1}),(0,t.bF)(Ne,{label:"Bug描述",span:2},{default:(0,t.k6)(()=>[(0,t.Lk)("div",A,(0,s.v_)(le.value.desc),1)]),_:1})]),_:1})]),_:1}),(0,t.bF)(Se,{class:"detail-card test-info-card",shadow:"hover"},{header:(0,t.k6)(()=>[(0,t.Lk)("div",Q,[(0,t.Lk)("h3",null,[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Tickets))]),_:1}),l[28]||(l[28]=(0,t.eW)(" 用例执行信息"))])])]),default:(0,t.k6)(()=>[(0,t.bF)(o.A,{result:le.value.info,showbtn:!1},null,8,["result"])]),_:1}),ae.value&&ae.value.length>0?((0,t.uX)(),(0,t.Wv)(Se,{key:0,class:"detail-card timeline-card",shadow:"hover"},{header:(0,t.k6)(()=>[(0,t.Lk)("div",T,[(0,t.Lk)("h3",null,[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Tickets))]),_:1}),l[29]||(l[29]=(0,t.eW)(" 处理记录"))])])]),default:(0,t.k6)(()=>[(0,t.bF)(He,null,{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(ae.value,(e,l)=>((0,t.uX)(),(0,t.Wv)(Ge,{key:l,timestamp:Ie(e.create_time),type:Ve(e.handle),hollow:!0,size:0===l?"large":"normal"},{default:(0,t.k6)(()=>[(0,t.bF)(Se,{class:"timeline-item-card"},{default:(0,t.k6)(()=>[(0,t.Lk)("h4",q,[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(Ke(e.handle))))]),_:2},1024),(0,t.eW)(" "+(0,s.v_)(e.handle),1)]),e.remark?((0,t.uX)(),(0,t.CE)("p",N,(0,s.v_)(e.remark),1)):(0,t.Q3)("",!0),(0,t.Lk)("div",j,[(0,t.Lk)("span",S,[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.User))]),_:1}),(0,t.eW)(" "+(0,s.v_)(e.update_user),1)]),(0,t.Lk)("span",G,[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)(Ue)]),_:1}),(0,t.eW)(" "+(0,s.v_)(De(e.create_time)),1)])])]),_:2},1024)]),_:2},1032,["timestamp","type","size"]))),128))]),_:1})]),_:1})):((0,t.uX)(),(0,t.Wv)(Me,{key:1,description:"暂无处理记录","image-size":200})),(0,t.Lk)("div",H,[(0,t.bF)(i,{size:"default",onClick:l[6]||(l[6]=e=>ie.value=!1)},{default:(0,t.k6)(()=>l[30]||(l[30]=[(0,t.eW)("关闭")])),_:1,__:[30]}),(0,t.bF)(i,{size:"default",type:"primary",onClick:l[7]||(l[7]=e=>Ce(le.value))},{default:(0,t.k6)(()=>[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Edit))]),_:1}),l[31]||(l[31]=(0,t.eW)(" 更新状态 "))]),_:1,__:[31]})])])):(0,t.Q3)("",!0)]),_:1})]),_:1},8,["modelValue"]),(0,t.bF)(el,{modelValue:oe.value,"onUpdate:modelValue":l[11]||(l[11]=e=>oe.value=e),title:"更新Bug状态",width:"50%","destroy-on-close":"","custom-class":"update-dialog"},{footer:(0,t.k6)(()=>[(0,t.Lk)("div",J,[(0,t.bF)(i,{onClick:l[10]||(l[10]=e=>oe.value=!1)},{default:(0,t.k6)(()=>l[32]||(l[32]=[(0,t.eW)("取消")])),_:1,__:[32]}),(0,t.bF)(i,{type:"primary",onClick:We,loading:re.value},{default:(0,t.k6)(()=>[(0,t.bF)(a,null,{default:(0,t.k6)(()=>[(0,t.bF)((0,u.R1)(d.Check))]),_:1}),l[33]||(l[33]=(0,t.eW)(" 确认更新 "))]),_:1,__:[33]},8,["loading"])])]),default:(0,t.k6)(()=>[(0,t.bF)(Ze,{model:be.value,"label-position":"top","status-icon":"",class:"update-form"},{default:(0,t.k6)(()=>[(0,t.bF)(Je,{label:"选择状态"},{default:(0,t.k6)(()=>[(0,t.Lk)("div",M,[((0,t.uX)(),(0,t.CE)(t.FK,null,(0,t.pI)(pe,(e,l)=>(0,t.Lk)("div",{key:l,class:(0,s.C4)(["status-option",{active:be.value.status===e.value}]),onClick:l=>be.value.status=e.value},[(0,t.bF)(a,{class:"option-icon"},{default:(0,t.k6)(()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(e.icon)))]),_:2},1024),(0,t.Lk)("div",Y,(0,s.v_)(e.label),1)],10,O)),64))])]),_:1}),(0,t.bF)(Je,{label:"处理备注"},{default:(0,t.k6)(()=>[(0,t.bF)(ye,{modelValue:be.value.remark,"onUpdate:modelValue":l[9]||(l[9]=e=>be.value.remark=e),type:"textarea",autosize:{minRows:3,maxRows:6},placeholder:"请输入处理备注...",class:"remark-input"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),_:1})])}}},ee=a(71241);const le=(0,ee.A)(Z,[["__scopeId","data-v-8cb5ea16"]]);var ae=le},67638:function(e,l,a){a.d(l,{A:function(){return R}});var t=a(56768),u=a(45130),s=a(24232);const n={key:0},i={key:0},o={key:1},r={key:0,class:"tab-box-sli"},d={style:{color:"#747474"}},c={key:0},v={class:"tab-box-sli"},k={key:4,style:{color:"#d60000"}},g={key:0,style:{color:"#00AA7F"}},p={key:1,style:{color:"#d18d17"}},b={key:2,style:{color:"#ff0000"}},f={key:0,style:{color:"#00AA7F"}},_={key:1,style:{color:"#ff5500"}},m={key:0,style:{"margin-top":"10px",width:"100%","text-align":"center"}},h={class:"dialog-footer"};function F(e,l,a,F,y,L){const w=(0,t.g2)("Editor"),C=(0,t.g2)("el-scrollbar"),W=(0,t.g2)("el-tab-pane"),R=(0,t.g2)("el-tag"),X=(0,t.g2)("el-collapse-item"),E=(0,t.g2)("el-collapse"),B=(0,t.g2)("el-tabs"),x=(0,t.g2)("el-button"),$=(0,t.g2)("el-option"),V=(0,t.g2)("el-select"),K=(0,t.g2)("el-form-item"),z=(0,t.g2)("el-input"),D=(0,t.g2)("el-form"),I=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.CE)(t.FK,null,[(0,t.bF)(B,{"model-value":"rb",style:{"min-height":"300px"},type:"border-card",value:"rb",size:"mini"},{default:(0,t.k6)(()=>["api"==a.result.type?((0,t.uX)(),(0,t.Wv)(W,{key:0,label:"响应体",name:"rb"},{default:(0,t.k6)(()=>[a.result.response_header?((0,t.uX)(),(0,t.CE)("div",n,[a.result.response_header["Content-Type"].includes("application/json")?((0,t.uX)(),(0,t.CE)("div",i,[(0,t.bF)(w,{readOnly:!0,modelValue:a.result.response_body,"onUpdate:modelValue":l[0]||(l[0]=e=>a.result.response_body=e),lang:"json",theme:"chrome"},null,8,["modelValue"])])):((0,t.uX)(),(0,t.CE)("div",o,[(0,t.bF)(C,{height:"400px",onWheel:l[1]||(l[1]=(0,u.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[(0,t.bF)(w,{readOnly:!0,innerHTML:a.result.response_body,lang:"html",theme:"chrome",height:"400px"},null,8,["innerHTML"])]),_:1})]))])):(0,t.Q3)("",!0)]),_:1})):(0,t.Q3)("",!0),"api"==a.result.type?((0,t.uX)(),(0,t.Wv)(W,{key:1,label:"响应头",name:"rh"},{default:(0,t.k6)(()=>[(0,t.bF)(C,{height:"400px",onWheel:l[2]||(l[2]=(0,u.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[a.result.response_header?((0,t.uX)(),(0,t.CE)("div",r,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(a.result.response_header,(e,l)=>((0,t.uX)(),(0,t.CE)("div",null,[(0,t.bF)(R,{style:{"margin-top":"3px"},type:"info"},{default:(0,t.k6)(()=>[(0,t.Lk)("b",d,(0,s.v_)(l+" : "),1),(0,t.Lk)("span",null,(0,s.v_)(e),1)]),_:2},1024)]))),256))])):(0,t.Q3)("",!0)]),_:1})]),_:1})):(0,t.Q3)("",!0),"api"==a.result.type?((0,t.uX)(),(0,t.Wv)(W,{key:2,label:"请求信息",name:"rq"},{default:(0,t.k6)(()=>[(0,t.bF)(C,{height:"400px",onWheel:l[4]||(l[4]=(0,u.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[a.result.requests_body?((0,t.uX)(),(0,t.CE)("div",c,[(0,t.bF)(E,{modelValue:y.activeNames,"onUpdate:modelValue":l[3]||(l[3]=e=>y.activeNames=e),class:"tab-box-sli"},{default:(0,t.k6)(()=>[(0,t.bF)(X,{name:"1"},{title:(0,t.k6)(()=>l[9]||(l[9]=[(0,t.Lk)("b",null,"General",-1)])),default:(0,t.k6)(()=>[(0,t.Lk)("div",null,"Request Method : "+(0,s.v_)(a.result.method),1),(0,t.Lk)("div",null,"Request URL : "+(0,s.v_)(a.result.url),1)]),_:1}),(0,t.bF)(X,{name:"2"},{title:(0,t.k6)(()=>l[10]||(l[10]=[(0,t.Lk)("b",null,"Request Headers",-1)])),default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(a.result.requests_header,(e,l)=>((0,t.uX)(),(0,t.CE)("div",null,[(0,t.Lk)("span",null,(0,s.v_)(l+" : "+e),1)]))),256))]),_:1}),(0,t.bF)(X,{name:"3"},{title:(0,t.k6)(()=>l[11]||(l[11]=[(0,t.Lk)("b",null,"Request Payload",-1)])),default:(0,t.k6)(()=>[(0,t.Lk)("span",null,(0,s.v_)(a.result.requests_body),1)]),_:1})]),_:1},8,["modelValue"])])):(0,t.Q3)("",!0)]),_:1})]),_:1})):(0,t.Q3)("",!0),(0,t.bF)(W,{label:"日志"},{default:(0,t.k6)(()=>[(0,t.bF)(C,{height:"400px",onWheel:l[5]||(l[5]=(0,u.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[(0,t.Lk)("div",v,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(a.result.log_data,(e,l)=>((0,t.uX)(),(0,t.CE)("div",null,["DEBUG"===e[0]?((0,t.uX)(),(0,t.Wv)(R,{key:0,style:{"margin-top":"3px"}},{default:(0,t.k6)(()=>[(0,t.eW)((0,s.v_)(e[1]),1)]),_:2},1024)):"WARNING"===e[0]?((0,t.uX)(),(0,t.Wv)(R,{key:1,style:{"margin-top":"3px"},type:"warning"},{default:(0,t.k6)(()=>[(0,t.eW)((0,s.v_)(e[1]),1)]),_:2},1024)):"ERROR"===e[0]?((0,t.uX)(),(0,t.Wv)(R,{key:2,style:{"margin-top":"3px"},type:"danger"},{default:(0,t.k6)(()=>[(0,t.eW)((0,s.v_)(e[1]),1)]),_:2},1024)):"INFO"===e[0]?((0,t.uX)(),(0,t.Wv)(R,{key:3,style:{"margin-top":"3px"},type:"success"},{default:(0,t.k6)(()=>[(0,t.eW)((0,s.v_)(e[1]),1)]),_:2},1024)):"EXCEPT"===e[0]?((0,t.uX)(),(0,t.CE)("pre",k,(0,s.v_)(e[1]),1)):(0,t.Q3)("",!0)]))),256))])]),_:1})]),_:1}),(0,t.bF)(W,{disabled:""},{label:(0,t.k6)(()=>["成功"===a.result.state?((0,t.uX)(),(0,t.CE)("span",g,(0,s.v_)("Assert : "+a.result.state),1)):"失败"===a.result.state?((0,t.uX)(),(0,t.CE)("span",p,(0,s.v_)("Assert : "+a.result.state),1)):((0,t.uX)(),(0,t.CE)("span",b,(0,s.v_)(a.result.state),1))]),_:1}),"api"==a.result.type?((0,t.uX)(),(0,t.Wv)(W,{key:3,disabled:""},{label:(0,t.k6)(()=>[a.result.status_cede<=300?((0,t.uX)(),(0,t.CE)("span",f,(0,s.v_)("Status : "+a.result.status_cede),1)):((0,t.uX)(),(0,t.CE)("span",_,(0,s.v_)("Status : "+a.result.status_cede),1))]),_:1})):(0,t.Q3)("",!0),(0,t.bF)(W,{disabled:""},{label:(0,t.k6)(()=>[(0,t.eW)((0,s.v_)("Time : "+a.result.run_time),1)]),_:1})]),_:1}),"失败"===a.result.state&&a.showbtn?((0,t.uX)(),(0,t.CE)("div",m,[(0,t.bF)(x,{onClick:L.getInterfaces,type:"success",plain:"",size:"mini"},{default:(0,t.k6)(()=>l[12]||(l[12]=[(0,t.eW)("提交bug")])),_:1,__:[12]},8,["onClick"])])):(0,t.Q3)("",!0),(0,t.bF)(I,{title:"提交bug",modelValue:y.addBugDlg,"onUpdate:modelValue":l[8]||(l[8]=e=>y.addBugDlg=e),width:"40%","before-close":L.closeDialogResult},{footer:(0,t.k6)(()=>[(0,t.Lk)("div",h,[(0,t.bF)(x,{onClick:L.closeDialogResult},{default:(0,t.k6)(()=>l[13]||(l[13]=[(0,t.eW)("取 消")])),_:1,__:[13]},8,["onClick"]),(0,t.bF)(x,{type:"success",onClick:L.saveBug},{default:(0,t.k6)(()=>l[14]||(l[14]=[(0,t.eW)("确 定")])),_:1,__:[14]},8,["onClick"])])]),default:(0,t.k6)(()=>[(0,t.bF)(D,{model:y.bugForm},{default:(0,t.k6)(()=>[(0,t.bF)(K,{label:"所属接口"},{default:(0,t.k6)(()=>[(0,t.bF)(V,{size:"small",modelValue:y.bugForm.interface,"onUpdate:modelValue":l[6]||(l[6]=e=>y.bugForm.interface=e),placeholder:"bug对应的接口",style:{width:"100%"}},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(y.interfaces,e=>((0,t.uX)(),(0,t.Wv)($,{label:e.name+" "+e.url,value:e.id,key:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(K,{label:"bug描述"},{default:(0,t.k6)(()=>[(0,t.bF)(z,{autosize:{minRows:3,maxRows:4},modelValue:y.bugForm.desc,"onUpdate:modelValue":l[7]||(l[7]=e=>y.bugForm.desc=e),type:"textarea",autocomplete:"off"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","before-close"])],64)}var y=a(53629),L=a(60782),w={props:{result:{default:{}},showbtn:{default:!0}},computed:{...(0,L.aH)(["pro"])},components:{Editor:y.A},data(){return{activeNames:["1","2","3"],addBugDlg:!1,bugForm:{interface:null,desc:"",info:"",status:"待处理"},interfaces:[]}},methods:{async saveBug(){this.bugForm.project=this.pro.id,this.bugForm.info=this.result;const e=await this.$api.createBugs(this.bugForm);201===e.status&&(this.$message({type:"success",message:"bug提交成功",duration:1e3}),this.addBugDlg=!1,this.bugForm={interface:null,desc:"",info:"",status:"待处理"})},closeDialogResult(){this.addBugDlg=!1,this.bugForm={interface:null,desc:"",info:"",status:"待处理"}},async getInterfaces(){const e=await this.$api.getNewInterfaces();200===e.status&&(this.interfaces=e.data,this.addBugDlg=!0)}}},C=a(71241);const W=(0,C.A)(w,[["render",F]]);var R=W}}]);
//# sourceMappingURL=903.f2638eca.js.map