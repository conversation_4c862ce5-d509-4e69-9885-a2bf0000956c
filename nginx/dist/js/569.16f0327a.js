"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[569],{65950:function(e,t,s){s.r(t),s.d(t,{default:function(){return D}});var a=s(56768),r=s(24232);const i={class:"server-manage-container"},n={class:"server-header"},o={class:"server-stats"},l={class:"server-list-container"},d={class:"server-card-header"},u={class:"server-name"},c={class:"name-text"},p={class:"server-actions"},v={class:"server-info"},g={class:"server-details"},_={class:"detail-item"},m={class:"detail-item"},f={class:"detail-item"},h={class:"server-operations"},b={class:"pagination-container"},k={class:"system-icon-content"},y={slot:"footer",class:"dialog-footer"};function S(e,t,s,S,F,x){const C=(0,a.g2)("Plus"),w=(0,a.g2)("el-icon"),L=(0,a.g2)("el-button"),V=(0,a.g2)("el-tag"),$=(0,a.g2)("el-switch"),D=(0,a.g2)("Location"),T=(0,a.g2)("User"),I=(0,a.g2)("Calendar"),W=(0,a.g2)("Connection"),U=(0,a.g2)("View"),H=(0,a.g2)("Edit"),P=(0,a.g2)("Delete"),E=(0,a.g2)("el-card"),R=(0,a.g2)("el-empty"),A=(0,a.g2)("el-pagination"),z=(0,a.g2)("el-scrollbar"),N=(0,a.g2)("Monitor"),M=(0,a.g2)("el-input"),O=(0,a.g2)("el-form-item"),X=(0,a.g2)("Position"),q=(0,a.g2)("Lock"),B=(0,a.g2)("el-checkbox"),Q=(0,a.g2)("el-form"),j=(0,a.g2)("el-dialog");return(0,a.uX)(),(0,a.CE)("div",i,[(0,a.Lk)("div",n,[(0,a.bF)(L,{onClick:t[0]||(t[0]=e=>x.popup("add")),type:"primary",class:"add-server-btn"},{default:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(C)]),_:1}),t[9]||(t[9]=(0,a.eW)("添加机器 "))]),_:1,__:[9]}),(0,a.Lk)("div",o,[(0,a.bF)(V,{type:"info"},{default:(0,a.k6)(()=>[(0,a.eW)("总机器数: "+(0,r.v_)(F.serverList.length),1)]),_:1}),(0,a.bF)(V,{type:"success"},{default:(0,a.k6)(()=>[(0,a.eW)("在线: "+(0,r.v_)(x.getActiveServersCount),1)]),_:1})])]),(0,a.bF)(z,{height:"calc(100vh - 150px)"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",l,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(F.serverList,(s,i)=>((0,a.uX)(),(0,a.Wv)(E,{key:s.id,class:(0,r.C4)(["server-card",{"default-server":s.default_code}]),shadow:s.default_code?"always":"hover"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",d,[(0,a.Lk)("div",u,[(0,a.bF)(V,{size:"small",type:"info",class:"server-index"},{default:(0,a.k6)(()=>[(0,a.eW)((0,r.v_)(i+1),1)]),_:2},1024),(0,a.Lk)("span",c,(0,r.v_)(s.name),1),s.default_code?((0,a.uX)(),(0,a.Wv)(V,{key:0,size:"small",type:"success"},{default:(0,a.k6)(()=>t[10]||(t[10]=[(0,a.eW)("默认")])),_:1,__:[10]})):(0,a.Q3)("",!0)]),(0,a.Lk)("div",p,[(0,a.bF)($,{onChange:e=>x.switchStatus(s),modelValue:s.default_code,"onUpdate:modelValue":e=>s.default_code=e,"active-color":"#66b1ff","inactive-color":"#b1b1b1","active-text":"默认","inactive-text":"非默认"},null,8,["onChange","modelValue","onUpdate:modelValue"])])])]),default:(0,a.k6)(()=>[(0,a.Lk)("div",v,[(0,a.Lk)("div",g,[(0,a.Lk)("div",_,[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(D)]),_:1}),(0,a.Lk)("span",null,(0,r.v_)(s.host_ip)+":"+(0,r.v_)(s.host_port),1)]),(0,a.Lk)("div",m,[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(T)]),_:1}),(0,a.Lk)("span",null,(0,r.v_)(s.creator),1)]),(0,a.Lk)("div",f,[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(I)]),_:1}),(0,a.Lk)("span",null,(0,r.v_)(e.$tools.rTime(s.create_time)),1)])]),(0,a.Lk)("div",{class:(0,r.C4)(["server-status",{"server-active":"active"===s.server_status}])},[(0,a.bF)(V,{type:"active"===s.server_status?"success":"info",size:"small"},{default:(0,a.k6)(()=>[(0,a.eW)((0,r.v_)("active"===s.server_status?"在线":"未知"),1)]),_:2},1032,["type"])],2)]),(0,a.Lk)("div",h,[(0,a.bF)(L,{onClick:e=>x.testConnection(s),type:"warning",size:"small",loading:s.testing},{default:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(W)]),_:1}),t[11]||(t[11]=(0,a.eW)("测试连接 "))]),_:2,__:[11]},1032,["onClick","loading"]),(0,a.bF)(L,{onClick:e=>x.terminal(s.id),type:"success",size:"small"},{default:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(U)]),_:1}),t[12]||(t[12]=(0,a.eW)("进入终端 "))]),_:2,__:[12]},1032,["onClick"]),(0,a.bF)(L,{onClick:e=>x.popup("edit",s),type:"primary",size:"small"},{default:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(H)]),_:1}),t[13]||(t[13]=(0,a.eW)("编辑 "))]),_:2,__:[13]},1032,["onClick"]),(0,a.bF)(L,{onClick:e=>x.delServer(s.id),type:"danger",size:"small"},{default:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(P)]),_:1}),t[14]||(t[14]=(0,a.eW)("删除 "))]),_:2,__:[14]},1032,["onClick"])])]),_:2},1032,["shadow","class"]))),128)),0===F.serverList.length?((0,a.uX)(),(0,a.Wv)(R,{key:0,description:"暂无服务器数据"},{default:(0,a.k6)(()=>[(0,a.bF)(L,{onClick:t[1]||(t[1]=e=>x.popup("add")),type:"primary"},{default:(0,a.k6)(()=>t[15]||(t[15]=[(0,a.eW)("添加第一台服务器")])),_:1,__:[15]})]),_:1})):(0,a.Q3)("",!0)]),(0,a.Lk)("div",b,[(0,a.bF)(A,{background:"",layout:"total, prev, pager, next, jumper",onCurrentChange:x.currentPages,"default-page-size":100,total:F.pages.count,"current-page":F.pages.current,"next-text":"下一页","prev-text":"上一页"},null,8,["onCurrentChange","total","current-page"])])]),_:1}),(0,a.bF)(j,{title:F.dialogTitle,modelValue:F.dialogVisible,"onUpdate:modelValue":t[8]||(t[8]=e=>F.dialogVisible=e),"before-close":x.closeDialog,top:"40px","destroy-on-close":"","custom-class":"class_dialog",width:"500px"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",k,[(0,a.bF)(Q,{model:F.serverData,rules:F.rulesServer,ref:"ServerRef","label-position":"top"},{default:(0,a.k6)(()=>[(0,a.bF)(O,{label:"名称",prop:"name"},{default:(0,a.k6)(()=>[(0,a.bF)(M,{modelValue:F.serverData.name,"onUpdate:modelValue":t[2]||(t[2]=e=>F.serverData.name=e),placeholder:"请输入名称"},{prefix:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(N)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(O,{label:"服务器IP",prop:"host_ip"},{default:(0,a.k6)(()=>[(0,a.bF)(M,{modelValue:F.serverData.host_ip,"onUpdate:modelValue":t[3]||(t[3]=e=>F.serverData.host_ip=e),maxlength:"15",placeholder:"请输入服务器IP"},{prefix:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(D)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(O,{label:"端口号",prop:"host_port"},{default:(0,a.k6)(()=>[(0,a.bF)(M,{modelValue:F.serverData.host_port,"onUpdate:modelValue":t[4]||(t[4]=e=>F.serverData.host_port=e),maxlength:"15",placeholder:"请输入端口号"},{prefix:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(X)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(O,{label:"用户名",prop:"sys_user_name"},{default:(0,a.k6)(()=>[(0,a.bF)(M,{modelValue:F.serverData.sys_user_name,"onUpdate:modelValue":t[5]||(t[5]=e=>F.serverData.sys_user_name=e),maxlength:"200",placeholder:"请输入用户名"},{prefix:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(T)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(O,{label:"密码",prop:"sys_user_passwd"},{default:(0,a.k6)(()=>[(0,a.bF)(M,{modelValue:F.serverData.sys_user_passwd,"onUpdate:modelValue":t[6]||(t[6]=e=>F.serverData.sys_user_passwd=e),maxlength:"200",placeholder:"请输入密码","show-password":""},{prefix:(0,a.k6)(()=>[(0,a.bF)(w,null,{default:(0,a.k6)(()=>[(0,a.bF)(q)]),_:1})]),_:1},8,["modelValue"])]),_:1}),"add"===F.dialogType?((0,a.uX)(),(0,a.Wv)(O,{key:0},{default:(0,a.k6)(()=>[(0,a.bF)(B,{modelValue:F.serverData.default_code,"onUpdate:modelValue":t[7]||(t[7]=e=>F.serverData.default_code=e),label:"设为默认服务器",border:""},null,8,["modelValue"])]),_:1})):(0,a.Q3)("",!0)]),_:1},8,["model","rules"]),(0,a.Lk)("div",y,[(0,a.bF)(L,{onClick:x.closeDialog},{default:(0,a.k6)(()=>t[16]||(t[16]=[(0,a.eW)("取 消")])),_:1,__:[16]},8,["onClick"]),"添加机器"===F.dialogTitle?((0,a.uX)(),(0,a.Wv)(L,{key:0,type:"primary",onClick:x.clickAdd},{default:(0,a.k6)(()=>t[17]||(t[17]=[(0,a.eW)("保 存")])),_:1,__:[17]},8,["onClick"])):(0,a.Q3)("",!0),"编辑机器"===F.dialogTitle?((0,a.uX)(),(0,a.Wv)(L,{key:1,type:"primary",onClick:x.clickUpdate},{default:(0,a.k6)(()=>t[18]||(t[18]=[(0,a.eW)("保 存")])),_:1,__:[18]},8,["onClick"])):(0,a.Q3)("",!0)])])]),_:1},8,["title","modelValue","before-close"])])}s(44114),s(18111),s(22489),s(61701);var F=s(60782),x=s(51219),C=s(12933),w=s(57477),L={components:{Plus:w.Plus,View:w.View,Connection:w.Connection,Location:w.Location,User:w.User,Calendar:w.Calendar,Edit:w.Edit,Delete:w.Delete,Monitor:w.Monitor,Position:w.Position,Lock:w.Lock},computed:{...(0,F.aH)({server:e=>e.server,pro:e=>e.pro}),username(){return window.sessionStorage.getItem("username")},getActiveServersCount(){return this.serverList.filter(e=>"active"===e.server_status).length}},data(){return{dialogVisible:!1,dialogType:"",dialogTitle:"",serverList:[],serverData:{name:"",host_ip:"",host_port:"",sys_user_name:"",sys_user_passwd:"",creator:"",project:"",default_code:!1},pages:{count:0,current:1},rulesServer:{name:[{required:!0,message:"请输入机器名称",trigger:"blur"}],host_ip:[{required:!0,message:"请输入服务器IP",trigger:"blur"}],host_port:[{required:!0,message:"请输入端口号",trigger:"blur"}],sys_user_name:[{required:!0,message:"请输入用户名",trigger:"blur"}],sys_user_passwd:[{required:!0,message:"请输入密码",trigger:"blur"}]}}},methods:{...(0,F.PY)(["servers"]),async testConnection(e){if(!e.testing){e.testing=!0;try{const t={host_ip:e.host_ip,host_port:e.host_port,sys_user_name:e.sys_user_name,sys_user_passwd:e.sys_user_passwd},s=await this.$api.testServerConnections(t,e.id);if(200===s.status){const t=s.data;t.success?(this.$message({type:"success",message:`${t.message||""}`,duration:3e3}),t.details&&this.showConnectionDetails(e.name,t.details),e.server_status="active"):(this.$message({type:"error",message:`连接失败：${t.message||t.error||"未知错误"}`,duration:5e3}),e.server_status="error")}}catch(t){console.error("连接测试错误:",t),e.server_status="error";let s="连接测试失败";if(t.response&&t.response.data){const e=t.response.data;switch(s=e.message||"连接测试失败",e.error_code){case"AUTHENTICATION_FAILED":s+="\n\n💡 解决建议：\n• 请检查用户名和密码是否正确\n• 确认账户是否被锁定\n• 验证SSH服务是否允许密码登录";break;case"NETWORK_UNREACHABLE":s+="\n\n💡 解决建议：\n• 检查服务器IP地址是否正确\n• 验证网络连接是否正常\n• 确认防火墙是否开放SSH端口";break;case"CONNECTION_TIMEOUT":s+="\n\n💡 解决建议：\n• 检查服务器是否正在运行\n• 验证SSH服务是否启动\n• 确认网络延迟是否过高";break;case"SSH_ERROR":s+="\n\n💡 解决建议：\n• 检查SSH配置是否正确\n• 验证SSH版本兼容性\n• 确认服务器SSH服务状态";break;case"MISSING_HOSTNAME":case"MISSING_USERNAME":case"MISSING_PASSWORD":s+="\n\n💡 解决建议：\n• 请填写完整的服务器连接信息\n• 检查必填字段是否为空";break;default:s+="\n\n💡 建议：\n• 请检查服务器连接信息是否正确\n• 确认服务器SSH服务是否正常运行"}}else s=t.message?`连接测试失败：${t.message}`:"连接测试失败：网络错误或服务器无响应";this.$alert(s,"连接测试失败",{confirmButtonText:"确定",type:"error",customClass:"connection-error-dialog"})}finally{e.testing=!1}}},showConnectionDetails(e,t){const s=this.buildConnectionDetailsHtml(e,t);this.$alert(s,"连接测试详情",{confirmButtonText:"关闭",type:"info",dangerouslyUseHTMLString:!0,customClass:"connection-details-dialog",width:"600px"})},buildConnectionDetailsHtml(e,t){return`\n        <div style="text-align: left; max-height: 400px; overflow-y: auto;">\n          <div style="margin-bottom: 15px;">\n            <h3 style="color: #409eff; margin-bottom: 10px;">🖥️ 服务器：${e}</h3>\n          </div>\n          \n          <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 15px;">\n            <h4 style="color: #409eff; margin-bottom: 10px;">📊 连接信息</h4>\n            <div style="font-size: 14px; line-height: 1.6;">\n              <div><strong>连接时间:</strong> ${t.connect_time||"-"}ms</div>\n              <div><strong>系统类型:</strong> ${t.os_type||"-"}</div>\n              <div><strong>主机名:</strong> ${t.hostname||"-"}</div>\n              <div><strong>连接协议:</strong> SSH</div>\n              <div><strong>响应状态:</strong> <span style="color: #67c23a;">✅ 正常</span></div>\n            </div>\n          </div>\n\n          ${t.system_info?this.buildSystemInfoHtml(t.system_info):""}\n          \n          ${t.performance_info?this.buildPerformanceInfoHtml(t.performance_info):""}\n          \n          <div style="background: #fff3cd; padding: 10px; border-radius: 6px; border-left: 4px solid #ffc107;">\n            <div style="font-size: 12px; color: #856404;">\n              <strong>💡 提示:</strong> 连接测试成功表示服务器可以正常访问，但实际性能测试时可能受网络环境影响。\n            </div>\n          </div>\n        </div>\n      `},buildSystemInfoHtml(e){return`\n        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 15px;">\n          <h4 style="color: #409eff; margin-bottom: 10px;">💻 系统信息</h4>\n          <div style="font-size: 14px; line-height: 1.6;">\n            <div><strong>操作系统:</strong> ${e.os_name||"-"}</div>\n            <div><strong>系统版本:</strong> ${e.os_version||"-"}</div>\n            <div><strong>内核版本:</strong> ${e.kernel_version||"-"}</div>\n            <div><strong>CPU架构:</strong> ${e.cpu_arch||"-"}</div>\n            <div><strong>内存总量:</strong> ${e.total_memory||"-"}</div>\n            <div><strong>磁盘空间:</strong> ${e.disk_space||"-"}</div>\n          </div>\n        </div>\n      `},buildPerformanceInfoHtml(e){return`\n        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 15px;">\n          <h4 style="color: #409eff; margin-bottom: 10px;">⚡ 性能指标</h4>\n          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">\n            <div><strong>CPU使用率:</strong> ${e.cpu_usage||"-"}%</div>\n            <div><strong>内存使用率:</strong> ${e.memory_usage||"-"}%</div>\n            <div><strong>负载平均值:</strong> ${e.load_average||"-"}</div>\n            <div><strong>网络延迟:</strong> ${e.network_latency||"-"}ms</div>\n          </div>\n        </div>\n      `},terminal(e){this.$router.push({name:"terminal"}),this.servers(e)},currentPages(e){this.getServerList(this.pro.id,e),this.pages.current=e},async getServerList(){const e=await this.$api.getServers(this.pro.id,1);200===e.status&&(this.serverList=e.data.result.map(e=>({...e,testing:!1,server_status:e.server_status||"unknown"})),this.pages=e.data)},async switchStatus(e){let t={default_code:e.default_code};const s=await this.$api.updateServer(e.id,t);200===s.status&&(0,x.nk)({type:"success",message:"设置成功"}),this.getServerList()},delServer(e){C.s.confirm("此操作将永久删除该机器, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const t=await this.$api.delServer(e);204===t.status&&((0,x.nk)({type:"success",message:"删除成功!"}),this.getServerList(this.pro.id,1))}).catch(()=>{(0,x.nk)({type:"info",message:"已取消删除"})})},async clickUpdate(){this.$refs.ServerRef.validate(async e=>{if(!e)return;const t={...this.serverData},s=await this.$api.updateServer(this.serverData.id,t);200===s.status&&((0,x.nk)({type:"success",message:"修改成功",duration:1e3}),this.dialogVisible=!1,this.$refs.ServerRef.clearValidate(),this.getServerList(this.pro.id,1))})},async clickAdd(){this.$refs.ServerRef.validate(async e=>{if(!e)return;const t={...this.serverData},s=await this.$api.createServer(t);201===s.status&&((0,x.nk)({type:"success",message:"添加成功",duration:1e3}),this.dialogVisible=!1,this.$refs.ServerRef.clearValidate(),this.getServerList(this.pro.id,1))})},popup(e,t){switch(this.dialogType=e,this.dialogVisible=!0,e){case"add":this.dialogTitle="添加机器",this.serverData={name:"",host_ip:"",host_port:"",sys_user_name:"",sys_user_passwd:"",creator:this.username,project:this.pro.id,default_code:!1};break;case"edit":this.dialogTitle="编辑机器",this.serverData={...t};break;default:this.dialogTitle="";break}},closeDialog(){this.dialogVisible=!1,this.getServerList(this.pro.id,1),this.serverData={name:"",host_ip:"",host_port:"",sys_user_name:"",sys_user_passwd:"",creator:"",project:"",default_code:!1},this.$refs.ServerRef&&this.$refs.ServerRef.clearValidate()}},created(){this.getServerList()}},V=s(71241);const $=(0,V.A)(L,[["render",S],["__scopeId","data-v-0b3b4ce3"]]);var D=$}}]);
//# sourceMappingURL=569.16f0327a.js.map