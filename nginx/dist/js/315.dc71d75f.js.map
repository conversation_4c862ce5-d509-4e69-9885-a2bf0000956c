{"version": 3, "file": "js/315.dc71d75f.js", "mappings": "yMACOA,MAAM,yB,GAEJA,MAAM,oB,GACJA,MAAM,gB,GAINA,MAAM,kB,GACJA,MAAM,kB,GAmBVA,MAAM,c,GACJA,MAAM,a,GACJA,MAAM,c,GAENA,MAAM,2B,GAIRA,MAAM,a,GACJA,MAAM,c,GAENA,MAAM,0B,GAIRA,MAAM,a,GACJA,MAAM,c,GAENA,MAAM,4B,GAIRA,MAAM,a,GACJA,MAAM,c,GAENA,MAAM,wB,GAQRA,MAAM,mB,GAkBEA,MAAM,a,GACJA,MAAM,yB,GAKNA,MAAM,e,GACJA,MAAM,a,GAKNA,MAAM,a,GAOJA,MAAM,a,GAUZA,MAAM,e,GAEHA,MAAM,e,GAiCTA,MAAM,a,GACJA,MAAM,e,GAKLA,MAAM,a,SAQ8BA,MAAM,e,GAC3CA,MAAM,a,GACNA,MAAM,e,SAEoBA,MAAM,e,GAChCA,MAAM,a,GACNA,MAAM,e,GAgBNA,MAAM,a,SAEAA,MAAM,W,GAOdA,MAAM,kB,GAsCRA,MAAM,c,GAYVA,MAAM,wB,GAsBVA,MAAM,kB,GAEFA,MAAM,Y,GASNA,MAAM,Y,GAIEA,MAAM,iB,GAMNA,MAAM,iB,SAMoBA,MAAM,mB,GAQhCA,MAAM,iB,GAMNA,MAAM,iB,UAMqCA,MAAM,gB,UAQVA,MAAM,sB,IACnDA,MAAM,Y,IAYEA,MAAM,iB,IACHA,MAAM,e,IAOTA,MAAM,kB,IAgBNA,MAAM,e,IACHA,MAAM,e,IAYjBA,MAAM,Y,IAYEA,MAAM,iB,IACHA,MAAM,e,IAOTA,MAAM,kB,IAMdA,MAAM,Y,IAQFA,MAAM,e,IAkBdA,MAAM,iB,0jCAhafC,EAAAA,EAAAA,IAyPM,MAzPNC,EAyPM,EAvPJD,EAAAA,EAAAA,IAsBM,MAtBNE,EAsBM,EArBJF,EAAAA,EAAAA,IAGM,MAHNG,EAGM,C,eAFJH,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVI,EAAAA,EAAAA,IAAmEC,GAAA,CAA3DC,KAAK,OAAOC,OAAO,QAAQR,MAAM,c,kBAAa,IAAIS,EAAA,MAAAA,EAAA,M,QAAJ,W,iBAExDR,EAAAA,EAAAA,IAgBM,MAhBNS,EAgBM,EAfJT,EAAAA,EAAAA,IAWM,MAXNU,EAWM,EAVJN,EAAAA,EAAAA,IAQWO,GAAA,C,WAPAC,GAAAC,W,qCAAAD,GAAAC,WAAUC,GACnBC,YAAY,SACZhB,MAAM,eACNiB,UAAA,I,CACWC,QAAMC,EAAAA,EAAAA,IACf,IAAiD,EAAjDd,EAAAA,EAAAA,IAAiDe,GAAA,CAAxCpB,MAAM,eAAa,C,iBAAC,IAAU,EAAVK,EAAAA,EAAAA,IAAUgB,M,gCAG3ChB,EAAAA,EAAAA,IAAgFiB,GAAA,CAArEf,KAAK,UAAWgB,QAAOC,GAAAC,YAAazB,MAAM,c,kBAAa,IAAES,EAAA,MAAAA,EAAA,M,QAAF,S,+BAEpEJ,EAAAA,EAAAA,IAEYiB,GAAA,CAFDf,KAAK,UAAWgB,QAAKd,EAAA,KAAAA,EAAA,GAAAM,GAAES,GAAAE,MAAM,QAAQ1B,MAAM,c,kBACpD,IAA2B,EAA3BK,EAAAA,EAAAA,IAA2Be,GAAA,M,iBAAlB,IAAQ,EAARf,EAAAA,EAAAA,IAAQsB,M,6BAAU,Y,mBAMjC1B,EAAAA,EAAAA,IA6BM,MA7BN2B,EA6BM,EA5BJ3B,EAAAA,EAAAA,IAMM,MANN4B,EAMM,EALJ5B,EAAAA,EAAAA,IAAmD,MAAnD6B,GAAmDC,EAAAA,EAAAA,IAAxBlB,GAAAmB,SAASC,QAAM,G,eAC1ChC,EAAAA,EAAAA,IAAkC,OAA7BD,MAAM,cAAa,QAAI,KAC5BC,EAAAA,EAAAA,IAEM,MAFNiC,EAEM,EADJ7B,EAAAA,EAAAA,IAA2Be,GAAA,M,iBAAlB,IAAQ,EAARf,EAAAA,EAAAA,IAAQ8B,M,WAGrBlC,EAAAA,EAAAA,IAMM,MANNmC,EAMM,EALJnC,EAAAA,EAAAA,IAAmD,MAAnDoC,GAAmDN,EAAAA,EAAAA,IAAxBP,GAAAc,iBAAe,G,eAC1CrC,EAAAA,EAAAA,IAAiC,OAA5BD,MAAM,cAAa,OAAG,KAC3BC,EAAAA,EAAAA,IAEM,MAFNsC,EAEM,EADJlC,EAAAA,EAAAA,IAA8Be,GAAA,M,iBAArB,IAAW,EAAXf,EAAAA,EAAAA,IAAWmC,M,WAGxBvC,EAAAA,EAAAA,IAMM,MANNwC,EAMM,EALJxC,EAAAA,EAAAA,IAAqD,MAArDyC,GAAqDX,EAAAA,EAAAA,IAA1BP,GAAAmB,mBAAiB,G,eAC5C1C,EAAAA,EAAAA,IAAiC,OAA5BD,MAAM,cAAa,OAAG,KAC3BC,EAAAA,EAAAA,IAEM,MAFN2C,EAEM,EADJvC,EAAAA,EAAAA,IAAoCe,GAAA,M,iBAA3B,IAAiB,EAAjBf,EAAAA,EAAAA,IAAiBwC,M,WAG9B5C,EAAAA,EAAAA,IAMM,MANN6C,EAMM,EALJ7C,EAAAA,EAAAA,IAAiD,MAAjD8C,GAAiDhB,EAAAA,EAAAA,IAAtBP,GAAAwB,eAAa,G,eACxC/C,EAAAA,EAAAA,IAAgC,OAA3BD,MAAM,cAAa,MAAE,KAC1BC,EAAAA,EAAAA,IAEM,MAFNgD,EAEM,EADJ5C,EAAAA,EAAAA,IAAoCe,GAAA,M,iBAA3B,IAAiB,EAAjBf,EAAAA,EAAAA,IAAiB6C,M,aAMhC7C,EAAAA,EAAAA,IA6Le8C,GAAA,CA7LDnD,MAAM,kBAAkBoD,OAAO,uB,kBAC3C,IA2LM,EA3LNnD,EAAAA,EAAAA,IA2LM,MA3LNoD,EA2LM,E,qBA1LJC,EAAAA,EAAAA,IA6KWC,GAAA,CA3KRC,KAAM3C,GAAAmB,SACPyB,MAAA,eACA,UAAQ,KACPC,QAAQ,EACTC,OAAA,GACCP,OAAQvC,GAAA+C,YACT,2BACC,oBAAmB,CAAAC,WAAA,UAAAC,MAAA,UAAAC,WAAA,S,CA0JTC,OAAK7C,EAAAA,EAAAA,IACd,IAOM,EAPNlB,EAAAA,EAAAA,IAOM,MAPNgE,EAOM,EANJ5D,EAAAA,EAAAA,IAKW6D,GAAA,CALA,aAAY,IAAKC,YAAY,U,CAC3BA,aAAWhD,EAAAA,EAAAA,IACpB,IAAaV,EAAA,MAAAA,EAAA,MAAbR,EAAAA,EAAAA,IAAa,SAAV,UAAM,M,iBAEX,IAAgE,EAAhEI,EAAAA,EAAAA,IAAgEiB,GAAA,CAArDf,KAAK,UAAWgB,QAAKd,EAAA,KAAAA,EAAA,GAAAM,GAAES,GAAAE,MAAM,S,kBAAQ,IAAIjB,EAAA,MAAAA,EAAA,M,QAAJ,W,2CA7JtD,IAA8D,EAA9DJ,EAAAA,EAAAA,IAA8D+D,GAAA,CAA7C7D,KAAK,YAAY8D,MAAM,KAAKC,MAAM,YAGnDjE,EAAAA,EAAAA,IA0BkB+D,GAAA,CA1BDG,MAAM,OAAO,YAAU,O,CAC3BC,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBxE,EAAAA,EAAAA,IAsBM,MAtBNyE,EAsBM,EArBJzE,EAAAA,EAAAA,IAIM,MAJN0E,EAIM,EAHJtE,EAAAA,EAAAA,IAEYuE,GAAA,CAFAC,KAAM,GAAI7E,MAAM,cAAeyD,OAAKqB,EAAAA,EAAAA,IAAA,CAAAC,gBAAoBvD,GAAAwD,eAAeP,EAAIQ,a,kBACrF,IAA0C,E,iBAAxCR,EAAIQ,SAASC,OAAO,EAAD,GAAMC,eAAW,K,wBAG1ClF,EAAAA,EAAAA,IAeM,MAfNmF,EAeM,EAdJnF,EAAAA,EAAAA,IAIM,MAJNoF,EAIM,EAHJhF,EAAAA,EAAAA,IAEciF,GAAA,CAFAC,GAAI,kBAAoBhE,QAAKR,GAAES,GAAAgE,gBAAgBf,GAAMzE,MAAM,kB,kBACvE,IAAkB,E,iBAAfyE,EAAIQ,UAAQ,K,0BAGnBhF,EAAAA,EAAAA,IAQM,MARNwF,EAQM,EAPJpF,EAAAA,EAAAA,IAKSC,GAAA,CALAC,KAAuB,OAAjBkE,EAAIiB,SAAoB,UAAY,UACjDb,KAAK,QACLrE,OAAO,QACPR,MAAM,Y,kBACN,IAA0D,E,iBAAvDa,GAAA8E,YAAYlB,EAAIiB,SAASE,aAAenB,EAAIiB,UAAQ,K,qBAEzDzF,EAAAA,EAAAA,IAAqE,MAArE4F,EAAuB,QAAI9D,EAAAA,EAAAA,IAAGP,GAAAsE,cAAcrB,EAAIsB,cAAW,W,OAQrE1F,EAAAA,EAAAA,IAOkB+D,GAAA,CAPDG,MAAM,OAAOD,MAAM,SAASD,MAAM,O,CACtCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBxE,EAAAA,EAAAA,IAGM,MAHN+F,EAGM,EAFJ/F,EAAAA,EAAAA,IAAqE,OAA/DD,OAAKiG,EAAAA,EAAAA,IAAA,oBAAuBzE,GAAA0E,eAAezB,EAAI0B,W,SACrDlG,EAAAA,EAAAA,IAA+F,OAA/FmG,GAA+FrE,EAAAA,EAAAA,IAAlE0C,EAAI4B,gBAAkB7E,GAAA8E,cAAc7B,EAAI0B,SAAW,OAAL,O,OAMjF9F,EAAAA,EAAAA,IAUkB+D,GAAA,CAVDG,MAAM,OAAOD,MAAM,SAASD,MAAM,O,CACtCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBpE,EAAAA,EAAAA,IAMSC,GAAA,CALPE,OAAO,QACPqE,KAAK,QACL7E,MAAM,WACLO,KAAMiB,GAAA+E,kBAAkB9B,EAAI+B,a,kBAC7B,IAAyB,E,iBAAtB/B,EAAIgC,kBAAgB,K,6BAM7BpG,EAAAA,EAAAA,IASkB+D,GAAA,CATDG,MAAM,QAAQD,MAAM,SAASD,MAAM,O,CACvCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBpE,EAAAA,EAAAA,IAKSC,GAAA,CAJPE,OAAO,QACPqE,KAAK,QACJtE,KAAMiB,GAAAkF,uBAAuBjC,EAAIkC,mB,kBAClC,IAAkD,E,iBAA/CnF,GAAAoF,uBAAuBnC,EAAIkC,mBAAgB,K,6BAMpDtG,EAAAA,EAAAA,IAWkB+D,GAAA,CAXDG,MAAM,QAAQD,MAAM,SAASD,MAAM,O,CACvCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBxE,EAAAA,EAAAA,IAOM,MAPN4G,EAOM,EANJ5G,EAAAA,EAAAA,IAIM,MAJN6G,EAIM,EAHJzG,EAAAA,EAAAA,IAEYuE,GAAA,CAFAC,KAAM,GAAKpB,OAAKqB,EAAAA,EAAAA,IAAA,CAAAC,gBAAoBvD,GAAAwD,eAAeP,EAAIsC,Y,kBACjE,IAA+D,E,iBAA5DtC,EAAIsC,QAAUtC,EAAIsC,QAAQ7B,OAAO,EAAD,GAAMC,cAAgB,KAAL,K,wBAGxDlF,EAAAA,EAAAA,IAAgD,OAAhD+G,GAAgDjF,EAAAA,EAAAA,IAArB0C,EAAIsC,SAAO,O,OAM5C1G,EAAAA,EAAAA,IAYkB+D,GAAA,CAZDG,MAAM,OAAOD,MAAM,SAASD,MAAM,O,CACtCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,CACZA,EAAIwC,UAAYxC,EAAIyC,c,WAA/BC,EAAAA,EAAAA,IAGM,MAHNC,EAGM,EAFJnH,EAAAA,EAAAA,IAA+C,MAA/CoH,GAA+CtF,EAAAA,EAAAA,IAArB0C,EAAIwC,UAAQ,IACtChH,EAAAA,EAAAA,IAAmE,MAAnEqH,GAAmEvF,EAAAA,EAAAA,IAAvCP,GAAAsE,cAAcrB,EAAIyC,cAAW,MAE3CzC,EAAIsB,c,WAApBoB,EAAAA,EAAAA,IAGM,MAHNI,EAGM,EAFJtH,EAAAA,EAAAA,IAAsD,MAAtDuH,GAAsDzF,EAAAA,EAAAA,IAA5B0C,EAAIsC,SAAW,MAAJ,IACrC9G,EAAAA,EAAAA,IAAmE,MAAnEwH,GAAmE1F,EAAAA,EAAAA,IAAvCP,GAAAsE,cAAcrB,EAAIsB,cAAW,Q,WAE3DzC,EAAAA,EAAAA,IAAmEhD,GAAA,C,MAApDuE,KAAK,QAAQrE,OAAO,QAAQD,KAAK,Q,kBAAO,IAAGE,EAAA,MAAAA,EAAA,M,QAAH,U,wBAK3DJ,EAAAA,EAAAA,IAakB+D,GAAA,CAbDG,MAAM,KAAKD,MAAM,SAAS,YAAU,O,CACxCE,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,CAEfA,EAAIiD,O,WADZpE,EAAAA,EAAAA,IAQaqE,GAAA,C,MANX3H,MAAM,WACNQ,OAAO,OACNoH,QAASnD,EAAIiD,KACdG,UAAU,a,kBAEV,IAA2C,EAA3C5H,EAAAA,EAAAA,IAA2C,MAA3C6H,GAA2C/F,EAAAA,EAAAA,IAAjB0C,EAAIiD,MAAI,K,qCAEpCP,EAAAA,EAAAA,IAAwC,OAAxCY,EAA6B,W,OAKjC1H,EAAAA,EAAAA,IAoCkB+D,GAAA,CApCDG,MAAM,KAAKyD,MAAM,QAAQ3D,MAAM,MAAMC,MAAM,U,CAC/CE,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBxE,EAAAA,EAAAA,IAgCM,MAhCNgI,EAgCM,EA/BJ5H,EAAAA,EAAAA,IAOYiB,GAAA,CANTC,QAAKR,GAAES,GAAA0G,QAAQzD,EAAI0D,IACpB5H,KAAK,UACL6H,MAAA,GACAvD,KAAK,QACL7E,MAAM,sB,kBACN,IAAgC,EAAhCK,EAAAA,EAAAA,IAAgCe,GAAA,M,iBAAvB,IAAa,EAAbf,EAAAA,EAAAA,IAAagI,M,6BAAU,W,gCAElChI,EAAAA,EAAAA,IAOYiB,GAAA,CANTC,QAAKR,GAAES,GAAAgE,gBAAgBf,GACxBlE,KAAK,UACL6H,MAAA,GACAvD,KAAK,QACL7E,MAAM,yB,kBACN,IAA2B,EAA3BK,EAAAA,EAAAA,IAA2Be,GAAA,M,iBAAlB,IAAQ,EAARf,EAAAA,EAAAA,IAAQiI,M,6BAAU,W,gCAE7BjI,EAAAA,EAAAA,IAcckI,GAAA,CAdDC,QAAQ,QAASC,UAAO1H,GAAES,GAAAkH,cAAc3H,EAAQ0D,GAAMzE,MAAM,mB,CAI5D2I,UAAQxH,EAAAA,EAAAA,IACjB,IAOmB,EAPnBd,EAAAA,EAAAA,IAOmBuI,GAAA,M,iBANjB,IAEmB,EAFnBvI,EAAAA,EAAAA,IAEmBwI,GAAA,CAFDC,QAAQ,QAAM,C,iBAC9B,IAAmC,EAAnCzI,EAAAA,EAAAA,IAAmCe,GAAA,M,iBAA1B,IAAgB,EAAhBf,EAAAA,EAAAA,IAAgB0I,M,6BAAU,a,eAErC1I,EAAAA,EAAAA,IAEmBwI,GAAA,CAFDG,QAAA,GAAQF,QAAQ,SAAS9I,MAAM,e,kBAC/C,IAA6B,EAA7BK,EAAAA,EAAAA,IAA6Be,GAAA,M,iBAApB,IAAU,EAAVf,EAAAA,EAAAA,IAAU4I,M,6BAAU,a,yCATnC,IAEY,EAFZ5I,EAAAA,EAAAA,IAEYiB,GAAA,CAFDuD,KAAK,QAAQ7E,MAAM,Y,kBAC5B,IAA2B,EAA3BK,EAAAA,EAAAA,IAA2Be,GAAA,M,iBAAlB,IAAQ,EAARf,EAAAA,EAAAA,IAAQ6I,M,qFAhJhBrI,GAAAsI,iBA+KblJ,EAAAA,EAAAA,IASM,MATNmJ,EASM,EARJ/I,EAAAA,EAAAA,IAOEgJ,GAAA,CANQ,eAAcxI,GAAAyI,MAAMC,Q,sCAAN1I,GAAAyI,MAAMC,QAAOxI,GACnC8C,WAAA,GACA2F,OAAO,mCACN,YAAW,IACXC,MAAO5I,GAAAyI,MAAMI,MACbC,gBAAgBnI,GAAAoI,c,kEAQ3BvJ,EAAAA,EAAAA,IAyKYwJ,GAAA,C,WAxKDhJ,GAAAiJ,c,uCAAAjJ,GAAAiJ,cAAa/I,GACrBgJ,MAAOlJ,GAAAmJ,YACR3F,MAAM,QACN,sBACC,eAAc7C,GAAAyI,gBACfjK,MAAM,e,CA6JKkK,QAAM/I,EAAAA,EAAAA,IACf,IAGM,EAHNlB,EAAAA,EAAAA,IAGM,MAHNkK,GAGM,EAFJ9J,EAAAA,EAAAA,IAAwDiB,GAAA,CAA5CC,QAAOC,GAAAyI,gBAAiB7B,MAAA,I,kBAAM,IAAE3H,EAAA,MAAAA,EAAA,M,QAAF,S,6BAC1CJ,EAAAA,EAAAA,IAAyDiB,GAAA,CAA9Cf,KAAK,UAAWgB,QAAOC,GAAA4I,S,kBAAS,IAAE3J,EAAA,MAAAA,EAAA,M,QAAF,S,iDA/J/C,IA2JM,EA3JNR,EAAAA,EAAAA,IA2JM,MA3JNoK,EA2JM,EA1JJhK,EAAAA,EAAAA,IAyJUiK,GAAA,CAzJAC,MAAO1J,GAAA2J,KAAOC,MAAO5J,GAAA6J,UAAWC,IAAI,UAAU,iBAAe,O,kBACrE,IAOM,EAPN1K,EAAAA,EAAAA,IAOM,MAPN2K,EAOM,EANJvK,EAAAA,EAAAA,IAEewK,GAAA,CAFDtG,MAAM,OAAOuG,KAAK,WAAW9K,MAAM,kB,kBAC/C,IAAyE,EAAzEK,EAAAA,EAAAA,IAAyEO,GAAA,C,WAAtDC,GAAA2J,KAAKvF,S,qCAALpE,GAAA2J,KAAKvF,SAAQlE,GAAEgK,UAAU,KAAK/J,YAAY,W,gCAE/DX,EAAAA,EAAAA,IAEewK,GAAA,CAFDtG,MAAM,OAAOuG,KAAK,UAAU9K,MAAM,kB,kBAC9C,IAA4C,EAA5CK,EAAAA,EAAAA,IAA4CO,GAAA,C,WAAzBC,GAAA2J,KAAKQ,Q,qCAALnK,GAAA2J,KAAKQ,QAAOjK,GAAEkK,SAAA,I,kCAIrChL,EAAAA,EAAAA,IAyCM,MAzCNiL,EAyCM,EAxCJ7K,EAAAA,EAAAA,IAmBewK,GAAA,CAnBDtG,MAAM,OAAOuG,KAAK,WAAW9K,MAAM,kB,kBAC/C,IAaiB,EAbjBK,EAAAA,EAAAA,IAaiB8K,GAAA,C,WAbQ3J,GAAA4J,e,qCAAA5J,GAAA4J,eAAcrK,GAAEf,MAAM,mB,kBAC7C,IAKW,EALXK,EAAAA,EAAAA,IAKWgL,GAAA,CALD9G,MAAM,MAAI,C,iBAClB,IAGM,EAHNtE,EAAAA,EAAAA,IAGM,MAHNqL,EAGM,EAFJjL,EAAAA,EAAAA,IAAiDe,GAAA,CAAxCpB,MAAM,cAAY,C,iBAAC,IAAW,EAAXK,EAAAA,EAAAA,IAAWkL,M,qBACvCtL,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,OAGdI,EAAAA,EAAAA,IAKWgL,GAAA,CALD9G,MAAM,MAAI,C,iBAClB,IAGM,EAHNtE,EAAAA,EAAAA,IAGM,MAHNuL,EAGM,EAFJnL,EAAAA,EAAAA,IAA+Ce,GAAA,CAAtCpB,MAAM,cAAY,C,iBAAC,IAAS,EAATK,EAAAA,EAAAA,IAASoL,M,qBACrCxL,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,+BAIa,OAAlBY,GAAA2J,KAAK9E,W,WAAhByB,EAAAA,EAAAA,IAGM,MAHNuE,EAGM,EAFJrL,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAcsL,M,qBACvB1L,EAAAA,EAAAA,IAA4B,YAAtB,mBAAe,Q,wBAGzBI,EAAAA,EAAAA,IAmBewK,GAAA,CAnBDtG,MAAM,OAAOuG,KAAK,mBAAmB9K,MAAM,kB,kBACvD,IAaiB,EAbjBK,EAAAA,EAAAA,IAaiB8K,GAAA,C,WAbQtK,GAAA2J,KAAK7D,iB,qCAAL9F,GAAA2J,KAAK7D,iBAAgB5F,GAAEf,MAAM,kB,kBACpD,IAKW,EALXK,EAAAA,EAAAA,IAKWgL,GAAA,CALD9G,MAAM,UAAQ,C,iBACtB,IAGM,EAHNtE,EAAAA,EAAAA,IAGM,MAHN2L,EAGM,EAFJvL,EAAAA,EAAAA,IAAiDe,GAAA,CAAxCpB,MAAM,cAAY,C,iBAAC,IAAW,EAAXK,EAAAA,EAAAA,IAAWwL,M,qBACvC5L,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,OAGdI,EAAAA,EAAAA,IAKWgL,GAAA,CALD9G,MAAM,eAAa,C,iBAC3B,IAGM,EAHNtE,EAAAA,EAAAA,IAGM,MAHN6L,EAGM,EAFJzL,EAAAA,EAAAA,IAAoDe,GAAA,CAA3CpB,MAAM,cAAY,C,iBAAC,IAAc,EAAdK,EAAAA,EAAAA,IAAc0L,M,qBAC1C9L,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,Q,+BAIoB,gBAA1BY,GAAA2J,KAAK7D,mB,WAAhBQ,EAAAA,EAAAA,IAGM,MAHN6E,GAGM,EAFJ3L,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAcsL,M,qBACvB1L,EAAAA,EAAAA,IAAgC,YAA1B,uBAAmB,Q,yBAMM,gBAA1BY,GAAA2J,KAAK7D,mB,WAAhBQ,EAAAA,EAAAA,IA0FM,MA1FN8E,GA0FM,EAzFJhM,EAAAA,EAAAA,IA+CM,MA/CNiM,GA+CM,EA9CJ7L,EAAAA,EAAAA,IAsBewK,GAAA,CAtBDtG,MAAM,OAAOuG,KAAK,gBAAgB9K,MAAM,kB,kBACpD,IAoBY,EApBZK,EAAAA,EAAAA,IAoBY8L,GAAA,C,WAnBDtL,GAAA2J,KAAK4B,c,qCAALvL,GAAA2J,KAAK4B,cAAarL,GAC3BC,YAAY,UACZyC,MAAA,eACC4I,SAAQ7K,GAAA8K,0B,kBAEP,IAAkC,G,aADpCnF,EAAAA,EAAAA,IAcYoF,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAbO3L,GAAA4L,iBAAVC,K,WADTpJ,EAAAA,EAAAA,IAcYqJ,GAAA,CAZTC,IAAKF,EAAOvE,GACZ5D,MAAK,GAAKmI,EAAOG,SAASH,EAAOI,WAAWJ,EAAOK,aACnDC,MAAON,EAAOvE,I,kBACf,IAOM,EAPNlI,EAAAA,EAAAA,IAOM,MAPNgN,GAOM,EANJhN,EAAAA,EAAAA,IAAkD,OAAlDiN,IAAkDnL,EAAAA,EAAAA,IAArB2K,EAAOG,MAAI,IACxCxM,EAAAA,EAAAA,IAISC,GAAA,CAHNC,KAAwB,WAAlBmM,EAAOvG,OAAsB,UAAY,SAChDtB,KAAK,S,kBACL,IAA+C,E,iBAA1B,WAAlB6H,EAAOvG,OAAsB,KAAO,OAAvB,K,uBAGpBlG,EAAAA,EAAAA,IAA6E,MAA7EkN,IAA6EpL,EAAAA,EAAAA,IAA9C2K,EAAOI,SAAU,KAAC/K,EAAAA,EAAAA,IAAG2K,EAAOK,WAAS,K,kFAK1E1M,EAAAA,EAAAA,IAqBewK,GAAA,CArBDtG,MAAM,QAAQuG,KAAK,WAAW9K,MAAM,kB,kBAChD,IAmBY,EAnBZK,EAAAA,EAAAA,IAmBY8L,GAAA,C,WAlBDtL,GAAA2J,KAAK4C,S,qCAALvM,GAAA2J,KAAK4C,SAAQrM,GACtBC,YAAY,WACZyC,MAAA,gB,kBAEE,IAAwB,G,aAD1B0D,EAAAA,EAAAA,IAcYoF,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAbK3L,GAAAwM,SAARC,K,WADThK,EAAAA,EAAAA,IAcYqJ,GAAA,CAZTC,IAAKU,EAAKA,KACV/I,MAAK,MAAQ+I,EAAKA,OAClBN,MAAOM,EAAKA,KACZrC,SAAUqC,EAAKC,U,kBAChB,IAOM,EAPNtN,EAAAA,EAAAA,IAOM,MAPNuN,GAOM,EANJvN,EAAAA,EAAAA,IAAgD,OAAhDwN,IAAgD1L,EAAAA,EAAAA,IAAnBuL,EAAKA,MAAI,IACtCjN,EAAAA,EAAAA,IAISC,GAAA,CAHNC,KAAM+M,EAAKC,SAAW,SAAW,UAClC1I,KAAK,S,kBACL,IAAkC,E,iBAA/ByI,EAAKC,SAAW,MAAQ,MAAX,K,4GAQ5BtN,EAAAA,EAAAA,IAwBM,MAxBNyN,GAwBM,EAvBJrN,EAAAA,EAAAA,IAsBewK,GAAA,CAtBDtG,MAAM,QAAQuG,KAAK,iBAAiB9K,MAAM,kB,kBACtD,IAoBY,EApBZK,EAAAA,EAAAA,IAoBY8L,GAAA,C,WAnBDtL,GAAA2J,KAAKmD,e,uCAAL9M,GAAA2J,KAAKmD,eAAc5M,GAC5B6M,SAAA,GACA5M,YAAY,WACZyC,MAAA,gB,kBAEE,IAAwC,G,aAD1C0D,EAAAA,EAAAA,IAcYoF,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAbOhL,GAAAqM,uBAAVnB,K,WADTpJ,EAAAA,EAAAA,IAcYqJ,GAAA,CAZTC,IAAKF,EAAOvE,GACZ5D,MAAK,GAAKmI,EAAOG,SAASH,EAAOI,WAAWJ,EAAOK,aACnDC,MAAON,EAAOvE,I,kBACf,IAOM,EAPNlI,EAAAA,EAAAA,IAOM,MAPN6N,GAOM,EANJ7N,EAAAA,EAAAA,IAAkD,OAAlD8N,IAAkDhM,EAAAA,EAAAA,IAArB2K,EAAOG,MAAI,IACxCxM,EAAAA,EAAAA,IAISC,GAAA,CAHNC,KAAwB,WAAlBmM,EAAOvG,OAAsB,UAAY,SAChDtB,KAAK,S,kBACL,IAA+C,E,iBAA1B,WAAlB6H,EAAOvG,OAAsB,KAAO,OAAvB,K,uBAGpBlG,EAAAA,EAAAA,IAA6E,MAA7E+N,IAA6EjM,EAAAA,EAAAA,IAA9C2K,EAAOI,SAAU,KAAC/K,EAAAA,EAAAA,IAAG2K,EAAOK,WAAS,K,yEAM5E9M,EAAAA,EAAAA,IAaM,MAbNgO,GAaM,EAZJ5N,EAAAA,EAAAA,IAWewK,GAAA,CAXDtG,MAAM,QAAQuG,KAAK,gBAAgB9K,MAAM,kB,kBACrD,IAKwB,EALxBK,EAAAA,EAAAA,IAKwB6N,GAAA,C,WAJbrN,GAAA2J,KAAK2D,c,uCAALtN,GAAA2J,KAAK2D,cAAapN,GAC1BqN,IAAK,EACLC,IAAK7M,GAAA8M,WACNtN,YAAY,SACZyC,MAAA,gB,8BACFxD,EAAAA,EAAAA,IAGM,MAHNsO,GAGM,EAFJtO,EAAAA,EAAAA,IAA4C,YAAtC,WAAO8B,EAAAA,EAAAA,IAAGP,GAAAgN,oBAAkB,IAClCvO,EAAAA,EAAAA,IAAmC,YAA7B,UAAM8B,EAAAA,EAAAA,IAAGP,GAAA8M,YAAU,O,4BAMjCjO,EAAAA,EAAAA,IAMewK,GAAA,CANDtG,MAAM,OAAOuG,KAAK,Q,kBAC9B,IAIc,EAJdzK,EAAAA,EAAAA,IAIcO,GAAA,CAHZL,KAAK,W,WACIM,GAAA2J,KAAK9C,K,uCAAL7G,GAAA2J,KAAK9C,KAAI3G,GAClBC,YAAY,UACXyN,KAAM,G,8NAqCnB,IACEC,WAAY,CACVC,KAAI,QACJC,UAAS,aACTC,aAAY,gBACZC,KAAI,QACJC,OAAM,UACNC,WAAU,cACVC,OAAM,UACNC,KAAI,QACJC,QAAO,WACPC,cAAa,iBACbC,cAAa,iBACbC,KAAI,QACJC,KAAI,QACJC,KAAI,QACJC,QAAO,WACPC,MAAK,SACLC,QAAO,WACPC,WAAUA,GAAAA,YAEZpM,IAAAA,GACE,MAAO,CAELmC,YAAa,CAAC,GAAM,OAAQ,GAAM,QAClC7E,WAAY,GACZkB,SAAU,GACVsH,MAAO,CACLI,MAAO,EACPH,QAAS,GAEXsG,QAAQ,EACRrF,KAAM,CACJvF,SAAU,GACVyC,KAAM,GACNoI,QAAS,GACTpK,SAAU,KACVqB,QAAU,GACVE,SAAW,GACXN,iBAAkB,SAClByF,cAAe,KACfuB,eAAgB,GAChBQ,cAAe,EACff,SAAU,MAEZtD,eAAe,EACfE,YAAa,GACbb,cAAc,EACduB,UAAW,CACTzF,SAAU,CACR,CAAE8K,UAAU,EAAMC,QAAS,UAAWxH,QAAS,QAC/C,CAAE4F,IAAK,EAAGC,IAAK,GAAI2B,QAAS,iBAAkBxH,QAAS,SAEzD7B,iBAAkB,CAChB,CAAEoJ,UAAU,EAAMC,QAAS,UAAWxH,QAAS,WAEjD4D,cAAe,CACb,CACE6D,UAAWA,CAACC,EAAMlD,EAAOmD,KACY,gBAA/BC,KAAK5F,KAAK7D,kBAAuCqG,EAGnDmD,IAFAA,EAAS,IAAIE,MAAM,oBAKvB7H,QAAS,WAGbmF,eAAgB,CACd,CACEsC,UAAWA,CAACC,EAAMlD,EAAOmD,KACY,gBAA/BC,KAAK5F,KAAK7D,kBAAwCqG,GAA0B,IAAjBA,EAAM/K,OAGnEkO,IAFAA,EAAS,IAAIE,MAAM,yBAKvB7H,QAAS,WAGb4E,SAAU,CACR,CACE6C,UAAWA,CAACC,EAAMlD,EAAOmD,KACY,gBAA/BC,KAAK5F,KAAK7D,kBAAuCqG,EAGnDmD,IAFAA,EAAS,IAAIE,MAAM,qBAKvB7H,QAAS,WAGb2F,cAAe,CACb,CACE8B,UAAWA,CAACC,EAAMlD,EAAOmD,KACvB,GAAmC,gBAA/BC,KAAK5F,KAAK7D,iBAAoC,CAChD,MAAM2H,EAAa8B,KAAK9B,YACnBtB,GAASA,EAAQ,EACpBmD,EAAS,IAAIE,MAAM,eACVrD,EAAQsB,EACjB6B,EAAS,IAAIE,MAAM,YAAY/B,MAE/B6B,GAEJ,MACEA,KAGJ3H,QAAS,UAIf8H,UAAW,CACT,EAAK,CAAEC,KAAM,OAAQhQ,KAAM,UAAWP,MAAO,oBAC7C,EAAK,CAAEuQ,KAAM,MAAOhQ,KAAM,UAAWP,MAAO,kBAC5C,GAAM,CAAEuQ,KAAM,OAAQhQ,KAAM,SAAUP,MAAO,iBAE/CwQ,WAAY,CAAC,EACb5M,YAAa,sBACb6M,eAAgB,KAGhBhE,iBAAkB,GAClBY,SAAU,CACR,CAAEC,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,IAE1BmD,eAAgB,KAEpB,EACAC,OAAAA,GACEP,KAAKQ,uBACLR,KAAKS,cACLT,KAAKU,iBAGLV,KAAKK,eAAiB,IAAIM,eAAe,KACvCX,KAAKQ,yBAIP,MAAMI,EAAYC,SAASC,cAAc,oBACrCF,GACFZ,KAAKK,eAAeU,QAAQH,GAI9BI,OAAOC,iBAAiB,SAAUjB,KAAKQ,qBACzC,EACAU,aAAAA,GAEEF,OAAOG,oBAAoB,SAAUnB,KAAKQ,sBAEtCR,KAAKK,gBACPL,KAAKK,eAAee,aAIlBpB,KAAKM,gBACPe,cAAcrB,KAAKM,eAEvB,EACAgB,SAAU,KACLC,EAAAA,GAAAA,IAAS,CACVC,IAAKC,GAASA,EAAMD,IACpBE,MAAOD,GAASA,EAAMC,QAExBC,QAAAA,GACE,OAAOX,OAAOY,eAAeC,QAAQ,WACvC,EACA7G,eAAgB,CACd8G,GAAAA,GACE,OAAO9B,KAAK5F,KAAK9E,SAASE,UAC5B,EACAuM,GAAAA,CAAInF,GACFoD,KAAK5F,KAAK9E,SAAWsH,CACvB,GAEF1K,eAAAA,GACE,OAAO8N,KAAKpO,SAASoQ,OAAOC,GAAwB,MAAhBA,EAAKlM,QAAgBlE,MAC3D,EACAU,iBAAAA,GACE,OAAOyN,KAAKpO,SAASoQ,OAAOC,GAAwB,MAAhBA,EAAKlM,QAAgBlE,MAC3D,EACAe,aAAAA,GACE,OAAOoN,KAAKpO,SAASoQ,OAAOC,GAAwB,OAAhBA,EAAKlM,QAAiBlE,MAC5D,EAEAqQ,cAAAA,GACE,OAAOlC,KAAKpO,SAASC,OAAS,OAAIsQ,EAAY,OAChD,EAGA1E,sBAAAA,GACE,OAAOuC,KAAK3D,iBAAiB2F,OAAO1F,GAClCA,EAAOvE,KAAOiI,KAAK5F,KAAK4B,cAE5B,EAGAkC,UAAAA,GACE,IAAK8B,KAAK5F,KAAKmD,gBAAsD,IAApCyC,KAAK5F,KAAKmD,eAAe1L,OACxD,OAAO,EAGT,MAAMuQ,EAAkBpC,KAAK3D,iBAAiB2F,OAAO1F,GACnD0D,KAAK5F,KAAKmD,eAAe8E,SAAS/F,EAAOvE,KAG3C,OAAOqK,EAAgBE,OAAO,CAACjJ,EAAOiD,IAC7BjD,GAASiD,EAAOiG,aAAe,GACrC,EACL,EAGAnE,kBAAAA,GACE,OAAOoE,KAAKC,KAAuB,GAAlBzC,KAAK9B,WACxB,GAEFwE,QAAS,KACJC,EAAAA,GAAAA,IAAa,CAAC,gBAGjBnC,oBAAAA,GAEER,KAAK4C,UAAU,KACb,MAAMC,EAAehC,SAASC,cAAc,sBAAsBgC,cAAgB,EAC5EC,EAAclC,SAASC,cAAc,gBAAgBgC,cAAgB,EACrEE,EAAmBnC,SAASC,cAAc,0BAA0BgC,cAAgB,EAGpFG,EAAkBjC,OAAOkC,YAAcL,EAAeE,EAAcC,EAAmB,IAG7FhD,KAAKxM,YAAcgP,KAAKvE,IAAIgF,EAAiB,KAAO,MAExD,EAEA,aAAME,CAAQC,EAAMC,GAClBrD,KAAKjH,cAAe,EACpB,IACE,MAAMuK,QAAiBtD,KAAKuD,KAAKC,mBAAmBxD,KAAKwB,IAAIzJ,GAAIqL,EAAMC,GAC/C,MAApBC,EAASvN,SACXiK,KAAKpO,SAAW0R,EAASlQ,KAAKqQ,OAC9BzD,KAAK9G,MAAMI,MAAQgK,EAASlQ,KAAKkG,MACjC0G,KAAK9G,MAAMC,QAAUmK,EAASlQ,KAAK+F,QAGnC6G,KAAK4C,UAAU,KACb5C,KAAKQ,yBAGX,CAAE,MAAOkD,IACPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,WACTgE,SAAU,KAEd,CAAE,QACA5D,KAAKjH,cAAe,CACtB,CACF,EAEAS,YAAAA,CAAaqK,GACX7D,KAAKmD,QAAQU,EAAa7D,KAAKtP,WACjC,EAEAW,WAAAA,GACE2O,KAAKmD,QAAQ,EAAGnD,KAAKtP,WACvB,EAEA0E,eAAAA,CAAgBhC,GACd4M,KAAK8D,QAAQC,KAAK,CAAEtH,KAAM,kBAC1BuD,KAAKgE,YAAY5Q,EACnB,EAEA9B,KAAAA,CAAMnB,GAGJ,OAFA6P,KAAKtG,eAAgB,EAEbvJ,GACN,IAAK,MACH6P,KAAKpG,YAAc,QACnBoG,KAAK5F,KAAKzD,QAAUqJ,KAAK2B,SACzB3B,KAAK5F,KAAKsF,QAAUM,KAAKwB,IAAIzJ,GAC7BiI,KAAK5F,KAAKQ,QAAUoF,KAAKwB,IAAI/E,KAE7BuD,KAAKS,qBACET,KAAK5F,KAAKvD,SACjB,MAEF,QACEmJ,KAAKpG,YAAc,GACnB,MAEN,EAGA,iBAAM6G,GACJ,IACE,MAAM6C,QAAiBtD,KAAKuD,KAAKU,WAAWjE,KAAKwB,IAAIzJ,GAAI,GACjC,MAApBuL,EAASvN,SACXiK,KAAK3D,iBAAmBiH,EAASlQ,KAAKqQ,QAAU,GAEpD,CAAE,MAAOC,GACPQ,QAAQR,MAAM,aAAcA,GAC5B1D,KAAKmE,SAAST,MAAM,YACtB,CACF,EAEA,qBAAMU,GACJ,GAAKpE,KAAK5F,KAAK4B,cAIf,IACE,MAAMsH,QAAiBtD,KAAKuD,KAAKc,iBAAiBrE,KAAK5F,KAAK4B,eAC5D,GAAwB,MAApBsH,EAASvN,OAAgB,CAC3B,MAAMuO,EAAgBhB,EAASlQ,KAAKmR,gBAAkB,GACtDvE,KAAK/C,SAASuH,QAAQtH,IACpBA,EAAKC,SAAWmH,EAAcjC,SAASnF,EAAKA,OAEhD,CACF,CAAE,MAAOwG,GACPQ,QAAQR,MAAM,YAAaA,EAC7B,CACF,EAEAhD,cAAAA,GAEEV,KAAKM,eAAiBmE,YAAY,KACG,gBAA/BzE,KAAK5F,KAAK7D,kBAAsCyJ,KAAK5F,KAAK4B,eAC5DgE,KAAKoE,mBAEN,IACL,EAEAlI,wBAAAA,GAEE8D,KAAK5F,KAAK4C,SAAW,KACrBgD,KAAKoE,kBAGDpE,KAAK5F,KAAKmD,eAAe8E,SAASrC,KAAK5F,KAAK4B,iBAC9CgE,KAAK5F,KAAKmD,eAAiByC,KAAK5F,KAAKmD,eAAeyE,OAClDjK,GAAMA,IAAOiI,KAAK5F,KAAK4B,eAG7B,EAEAnC,eAAAA,GACEmG,KAAKtG,eAAgB,EACjBsG,KAAK0E,MAAMC,SACb3E,KAAK0E,MAAMC,QAAQC,gBAErB5E,KAAK5F,KAAO,CACVvF,SAAU,GACVyC,KAAM,GACNoI,QAAS,GACTpK,SAAU,KACVqB,QAAU,GACVE,SAAW,GACXN,iBAAkB,SAClByF,cAAe,KACfuB,eAAgB,GAChBQ,cAAe,EACff,SAAU,KAEd,EAEA,aAAMhD,GACJgG,KAAK0E,MAAMC,QAAQE,SAASC,UAC1B,IAAKC,EAAO,OACZ,MAAMC,EAAShF,KAAK5F,KACpB,IACE,MAAMkJ,QAAiBtD,KAAKuD,KAAK0B,sBAAsBD,GAC/B,MAApB1B,EAASvN,SAEXiK,KAAK5F,KAAO,CACVvF,SAAU,GACVyC,KAAM,GACNoI,QAAS,GACTpK,SAAU,KACVqB,QAAU,GACVE,SAAW,GACXN,iBAAkB,SAClByF,cAAe,KACfuB,eAAgB,GAChBQ,cAAe,EACff,SAAU,MAIZgD,KAAKtG,eAAgB,GAGrBiK,EAAAA,GAAAA,IAAU,CACRxT,KAAM,UACNyP,QAAS,SACTgE,SAAU,MAEZ5D,KAAKmD,QAAQ,GAEjB,CAAE,MAAOO,IACPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,UAAY8D,EAAM9D,SAAW,QACtCgE,SAAU,KAEd,GAEJ,EAEAsB,OAAAA,CAAQnN,GACNoN,GAAAA,EAAaC,QAAQ,qBAAsB,OAAQ,CACjDC,kBAAmB,KACnBC,iBAAkB,KAClBnV,KAAM,UACNoV,mBAAmB,IAElBC,KAAKV,UACJ,IACE,MAAMxB,QAAiBtD,KAAKuD,KAAKkC,mBAAmB1N,GAC7B,MAApBuL,EAASvN,UACV4N,EAAAA,GAAAA,IAAU,CACRxT,KAAM,UACNyP,QAAS,UAGXI,KAAKmD,QAAQnD,KAAK9G,MAAMC,SAE5B,CAAE,MAAOuK,IACPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,UAAY8D,EAAM9D,SAAW,QACtCgE,SAAU,KAEd,IAED8B,MAAM,MACL/B,EAAAA,GAAAA,IAAU,CACRxT,KAAM,OACNyP,QAAS,WAGjB,EAEA,eAAM+F,CAAUvS,GACd,MAAM4R,EAAS,IAAI5R,GACnB4R,EAAOnQ,SAAWmQ,EAAOnQ,SAAW,MACpC,IACE,MAAMyO,QAAiBtD,KAAKuD,KAAK0B,sBAAsBD,GAC/B,MAApB1B,EAASvN,UACX4N,EAAAA,GAAAA,IAAU,CACRxT,KAAM,UACNyP,QAAS,OACTgE,SAAU,MAEZ5D,KAAKmD,QAAQnD,KAAK9G,MAAMC,SAE5B,CAAE,MAAOuK,IACPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,UAAY8D,EAAM9D,SAAW,QACtCgE,SAAU,KAEd,CACF,EAEA,aAAM9L,CAAQC,GACZ,IAAKiI,KAAK0B,MAMR,YALA1B,KAAKmE,SAAS,CACZhU,KAAM,UACNyP,QAAS,aACTgE,SAAU,MAId,MAAMoB,EAAS,CAAEY,OAAQ7N,EAAI8N,IAAK7F,KAAK0B,OACvC,IAEE,MAAM4B,QAAiBtD,KAAKuD,KAAKzL,QAAQC,EAAIiN,GACrB,MAApB1B,EAASvN,UACX+P,EAAAA,GAAAA,IAAe,CACbnM,MAAO,QACPiG,QAAS,cACTzP,KAAM,UACNyT,SAAU,IACVmC,WAAW,EACXC,SAAU,cAIZhG,KAAKiG,kBAET,CAAE,MAAOvC,IAEPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,YAAc8D,EAAM9D,SAAW,QACxCgE,SAAU,MAIZ5D,KAAKiG,iBACP,CACF,EAGAA,eAAAA,GAEEC,WAAW,KACTlG,KAAKmD,QAAQnD,KAAK9G,MAAMC,UACvB,IACL,EAEAjD,aAAAA,CAAcH,GACZ,OAAOiK,KAAKE,UAAUnK,IAASoK,MAAQpK,CACzC,EAEAD,cAAAA,CAAeC,GACb,OAAOiK,KAAKE,UAAUnK,IAASnG,OAAS,gBAC1C,EAGAuG,iBAAAA,CAAkBC,GAChB,MAAM+P,EAAU,CACd,SAAY,UACZ,WAAc,UACd,UAAa,SACb,WAAc,UACd,cAAiB,OACjB,aAAgB,IAElB,OAAOA,EAAQ/P,IAAe,MAChC,EAGAI,sBAAAA,CAAuB4P,GACrB,MAAMC,EAAU,CACd,OAAU,OACV,YAAe,SAEjB,OAAOA,EAAQD,IAASA,GAAQ,KAClC,EAEA9P,sBAAAA,CAAuB8P,GACrB,MAAMD,EAAU,CACd,OAAU,OACV,YAAe,WAEjB,OAAOA,EAAQC,IAAS,MAC1B,EAEA1Q,aAAAA,CAAc4Q,GACZ,IAAKA,EAAW,MAAO,GAEvB,MAAMC,EAAO,IAAIC,KAAKF,GAChBG,EAAM,IAAID,KACVE,EAAUlE,KAAKmE,OAAOF,EAAMF,GAAQ,KAE1C,GAAIG,EAAU,GAAI,MAAO,KAEzB,MAAME,EAAUpE,KAAKmE,MAAMD,EAAU,IACrC,GAAIE,EAAU,GAAI,MAAO,GAAGA,OAE5B,MAAMC,EAAQrE,KAAKmE,MAAMC,EAAU,IACnC,GAAIC,EAAQ,GAAI,MAAO,GAAGA,OAE1B,MAAMC,EAAOtE,KAAKmE,MAAME,EAAQ,IAChC,OAAIC,EAAO,EAAU,GAAGA,MAEjB9G,KAAK+G,OAAOC,MAAMV,GAAWW,MAAM,KAAK,EACjD,EAEArS,cAAAA,CAAesS,GACb,IAAKA,EAAK,MAAO,UAEjB,GAAIlH,KAAKI,WAAW8G,GAClB,OAAOlH,KAAKI,WAAW8G,GAIzB,IAAIC,EAAO,EACX,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAIrV,OAAQuV,IAC9BD,EAAOD,EAAIG,WAAWD,KAAOD,GAAQ,GAAKA,GAG5C,MAAMG,EAAS,CACb,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAGxCC,EAAQ/E,KAAKgF,IAAIL,GAAQG,EAAOzV,OAEtC,OADAmO,KAAKI,WAAW8G,GAAOI,EAAOC,GACvBvH,KAAKI,WAAW8G,EACzB,EAEA5O,aAAAA,CAAcI,EAASrE,GACrB,OAAQqE,GACN,IAAK,OACHsH,KAAK2F,UAAUtR,GACf,MACF,IAAK,SACH2L,KAAKkF,QAAQ7Q,EAAI0D,IACjB,MAEN,GAEF0P,OAAAA,GACEzH,KAAKmD,QAAQ,EACf,G,YCniCF,MAAMuE,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASC,IAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceTask.vue", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceTask.vue?697b"], "sourcesContent": ["<template>\n  <div class=\"performance-container\">\n    <!-- 顶部区域 -->\n    <div class=\"dashboard-header\">\n      <div class=\"header-title\">\n        <h2>性能测试任务</h2>\n        <el-tag type=\"info\" effect=\"plain\" class=\"header-tag\">任务列表</el-tag>\n      </div>\n      <div class=\"header-actions\">\n        <div class=\"search-wrapper\">\n          <el-input \n            v-model=\"filterText\" \n            placeholder=\"搜索任务名称\" \n            class=\"search-input\"\n            clearable>\n            <template #prefix>\n              <el-icon class=\"search-icon\"><Search /></el-icon>\n            </template>\n          </el-input>\n          <el-button type=\"primary\" @click=\"searchClick\" class=\"search-btn\">搜索</el-button>\n        </div>\n        <el-button type=\"primary\" @click=\"popup('add')\" class=\"create-btn\">\n          <el-icon><Plus /></el-icon>新建任务\n        </el-button>\n      </div>\n    </div>\n    \n    <!-- 数据卡片 -->\n    <div class=\"task-stats\">\n      <div class=\"stat-card\">\n        <div class=\"stat-value\">{{ taskList.length }}</div>\n        <div class=\"stat-label\">当前任务</div>\n        <div class=\"stat-icon running-icon1\">\n          <el-icon><List /></el-icon>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-value\">{{ getRunningCount }}</div>\n        <div class=\"stat-label\">运行中</div>\n        <div class=\"stat-icon running-icon\">\n          <el-icon><Loading /></el-icon>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-value\">{{ getCompletedCount }}</div>\n        <div class=\"stat-label\">已完成</div>\n        <div class=\"stat-icon completed-icon\">\n          <el-icon><SuccessFilled /></el-icon>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-value\">{{ getErrorCount }}</div>\n        <div class=\"stat-label\">异常</div>\n        <div class=\"stat-icon error-icon\">\n          <el-icon><WarningFilled /></el-icon>\n        </div>\n      </div>\n    </div>\n\n    <!-- 表格区域 - 使用滚动容器 -->\n    <el-scrollbar class=\"table-scrollbar\" height=\"calc(100vh - 280px)\">\n      <div class=\"task-table-card\">\n        <el-table\n          v-loading=\"tableLoading\"\n          :data=\"taskList\"\n          style=\"width: 100%\"\n          row-key=\"id\"\n          :border=\"false\"\n          stripe\n          :height=\"tableHeight\"\n          highlight-current-row\n          :header-cell-style=\"{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }\">\n          \n          <!-- 选择列 -->\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n          \n          <!-- 任务信息 -->\n          <el-table-column label=\"任务信息\" min-width=\"300\">\n            <template #default=\"{ row }\">\n              <div class=\"task-info\">\n                <div class=\"task-avatar-container\">\n                  <el-avatar :size=\"40\" class=\"task-avatar\" :style=\"{backgroundColor: getRandomColor(row.taskName)}\">\n                    {{row.taskName.substr(0,1).toUpperCase()}}\n                  </el-avatar>\n                </div>\n                <div class=\"task-detail\">\n                  <div class=\"task-name\">\n                    <router-link :to=\"`/maskMgrDetail/`\" @click=\"clickTaskManage(row)\" class=\"task-name-link\">\n                      {{ row.taskName }}\n                    </router-link>\n                  </div>\n                  <div class=\"task-meta\">\n                    <el-tag :type=\"row.taskType === '10' ? 'primary' : 'success'\" \n                      size=\"small\" \n                      effect=\"plain\" \n                      class=\"meta-tag\">\n                      {{ taskTypeMap[row.taskType.toString()] || row.taskType }}\n                    </el-tag>\n                    <div class=\"meta-info\">创建于 {{ formatTimeAgo(row.create_time) }}</div>\n                  </div>\n                </div>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <!-- 状态列 -->\n          <el-table-column label=\"运行状态\" align=\"center\" width=\"120\">\n            <template #default=\"{ row }\">\n              <div class=\"status-cell\">\n                <div :class=\"['status-indicator', getStatusClass(row.status)]\"></div>\n                <span class=\"status-text\">{{ row.status_display || getStatusText(row.status) || '未设置' }}</span>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <!-- 运行模式 -->\n          <el-table-column label=\"运行模式\" align=\"center\" width=\"120\">\n            <template #default=\"{ row }\">\n              <el-tag \n                effect=\"plain\" \n                size=\"small\" \n                class=\"mode-tag\"\n                :type=\"getRunPatternType(row.runPattern)\">\n                {{ row.taskType_display}}\n              </el-tag>\n            </template>\n          </el-table-column>\n\n          <!-- 分布式模式 -->\n          <el-table-column label=\"分布式模式\" align=\"center\" width=\"120\">\n            <template #default=\"{ row }\">\n              <el-tag \n                effect=\"plain\" \n                size=\"small\" \n                :type=\"getDistributedModeType(row.distributed_mode)\">\n                {{ getDistributedModeText(row.distributed_mode) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          \n          <!-- 创建/更新信息 -->\n          <el-table-column label=\"任务创建者\" align=\"center\" width=\"160\">\n            <template #default=\"{ row }\">\n              <div class=\"user-info\">\n                <div class=\"user-avatar\">\n                  <el-avatar :size=\"28\" :style=\"{backgroundColor: getRandomColor(row.creator)}\">\n                    {{ row.creator ? row.creator.substr(0,1).toUpperCase() : 'U' }}\n                  </el-avatar>\n                </div>\n                <span class=\"user-name\">{{ row.creator }}</span>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <!-- 更新者 -->\n          <el-table-column label=\"最后更新\" align=\"center\" width=\"160\">\n            <template #default=\"{ row }\">\n              <div v-if=\"row.modifier && row.update_time\" class=\"update-info\">\n                <div class=\"update-by\">{{ row.modifier }}</div>\n                <div class=\"update-time\">{{ formatTimeAgo(row.update_time) }}</div>\n              </div>\n              <div v-else-if=\"row.create_time\" class=\"update-info\">\n                <div class=\"update-by\">{{ row.creator || '系统' }}</div>\n                <div class=\"update-time\">{{ formatTimeAgo(row.create_time) }}</div>\n              </div>\n              <el-tag v-else size=\"small\" effect=\"plain\" type=\"info\">未更新</el-tag>\n            </template>\n          </el-table-column>\n          \n          <!-- 备注 -->\n          <el-table-column label=\"备注\" align=\"center\" min-width=\"200\">\n            <template #default=\"{ row }\">\n              <el-tooltip\n                v-if=\"row.desc\"\n                class=\"box-item\"\n                effect=\"dark\"\n                :content=\"row.desc\"\n                placement=\"top-start\"\n              >\n                <div class=\"desc-text\">{{ row.desc }}</div>\n              </el-tooltip>\n              <span v-else class=\"no-data\">暂无备注</span>\n            </template>\n          </el-table-column>\n          \n          <!-- 操作列 -->\n          <el-table-column label=\"操作\" fixed=\"right\" width=\"280\" align=\"center\">\n            <template #default=\"{ row }\">\n              <div class=\"action-buttons\">\n                <el-button \n                  @click=\"runTask(row.id)\" \n                  type=\"primary\" \n                  plain \n                  size=\"small\" \n                  class=\"action-btn run-btn\">\n                  <el-icon><Promotion /></el-icon> 运行\n                </el-button>\n                <el-button \n                  @click=\"clickTaskManage(row)\" \n                  type=\"success\" \n                  plain \n                  size=\"small\" \n                  class=\"action-btn manage-btn\">\n                  <el-icon><Menu /></el-icon> 管理\n                </el-button>\n                <el-dropdown trigger=\"click\" @command=\"handleCommand($event, row)\" class=\"action-dropdown\">\n                  <el-button size=\"small\" class=\"more-btn\">\n                    <el-icon><More /></el-icon>\n                  </el-button>\n                  <template #dropdown>\n                    <el-dropdown-menu>\n                      <el-dropdown-item command=\"copy\">\n                        <el-icon><CopyDocument /></el-icon> 复制任务\n                      </el-dropdown-item>\n                      <el-dropdown-item divided command=\"delete\" class=\"danger-item\">\n                        <el-icon><Delete /></el-icon> 删除任务\n                      </el-dropdown-item>\n                    </el-dropdown-menu>\n                  </template>\n                </el-dropdown>\n              </div>\n            </template>\n          </el-table-column>\n\n          <!-- 空状态处理 -->\n          <template #empty>\n            <div class=\"empty-data\">\n              <el-empty :image-size=\"120\" description=\"暂无任务数据\">\n                <template #description>\n                  <p>暂无任务数据</p>\n                </template>\n                <el-button type=\"primary\" @click=\"popup('add')\">新建任务</el-button>\n              </el-empty>\n            </div>\n          </template>\n        </el-table>\n\n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            v-model:current-page=\"pages.current\"\n            background\n            layout=\"total, prev, pager, next, jumper\"\n            :page-size=\"100\"\n            :total=\"pages.count\"\n            @current-change=\"currentPages\"\n          />\n        </div>\n      </div>\n    </el-scrollbar>\n  </div>\n\n  <!-- 新增任务弹窗 -->\n  <el-dialog\n    v-model=\"dialogVisible\"\n    :title=\"dialogTitle\"\n    width=\"800px\"\n    destroy-on-close\n    :before-close=\"clearValidation\"\n    class=\"task-dialog\">\n    <div class=\"dialog-content\">\n      <el-form :model=\"form\" :rules=\"rulesPerf\" ref=\"perfRef\" label-position=\"top\">\n        <div class=\"form-row\">\n          <el-form-item label=\"任务名称\" prop=\"taskName\" class=\"form-item-half\">\n            <el-input v-model=\"form.taskName\" maxlength=\"50\" placeholder=\"请输入任务名称\" />\n          </el-form-item>\n          <el-form-item label=\"所属项目\" prop=\"project\" class=\"form-item-half\">\n            <el-input v-model=\"form.proName\" disabled />\n          </el-form-item>\n        </div>\n\n        <div class=\"form-row\">\n          <el-form-item label=\"任务类型\" prop=\"taskType\" class=\"form-item-half\">\n            <el-radio-group v-model=\"selectTaskType\" class=\"task-type-radio\">\n              <el-radio label=\"10\">\n                <div class=\"radio-content\">\n                  <el-icon class=\"radio-icon\"><Tickets /></el-icon>\n                  <span>普通任务</span>\n                </div>\n              </el-radio>\n              <el-radio label=\"20\">\n                <div class=\"radio-content\">\n                  <el-icon class=\"radio-icon\"><Timer /></el-icon>\n                  <span>定时任务</span>\n                </div>\n              </el-radio>\n            </el-radio-group>\n            <div v-if=\"form.taskType === '20'\" class=\"tip-box warning\">\n              <el-icon><InfoFilled /></el-icon>\n              <span>定时任务下只允许创建一个场景！</span>\n            </div>\n          </el-form-item>\n          <el-form-item label=\"运行模式\" prop=\"distributed_mode\" class=\"form-item-half\">\n            <el-radio-group v-model=\"form.distributed_mode\" class=\"run-mode-radio\">\n              <el-radio label=\"single\">\n                <div class=\"radio-content\">\n                  <el-icon class=\"radio-icon\"><Monitor /></el-icon>\n                  <span>单机模式</span>\n                </div>\n              </el-radio>\n              <el-radio label=\"distributed\">\n                <div class=\"radio-content\">\n                  <el-icon class=\"radio-icon\"><Connection /></el-icon>\n                  <span>分布式模式</span>\n                </div>\n              </el-radio>\n            </el-radio-group>\n            <div v-if=\"form.distributed_mode === 'distributed'\" class=\"tip-box info\">\n              <el-icon><InfoFilled /></el-icon>\n              <span>分布式模式需要配置主服务器和工作服务器</span>\n            </div>\n          </el-form-item>\n        </div>\n\n        <!-- 分布式配置 -->\n        <div v-if=\"form.distributed_mode === 'distributed'\" class=\"distributed-config\">\n          <div class=\"form-row\">\n            <el-form-item label=\"主服务器\" prop=\"master_server\" class=\"form-item-half\">\n              <el-select \n                v-model=\"form.master_server\" \n                placeholder=\"请选择主服务器\"\n                style=\"width: 100%\"\n                @change=\"handleMasterServerChange\">\n                <el-option\n                  v-for=\"server in availableServers\"\n                  :key=\"server.id\"\n                  :label=\"`${server.name} (${server.host_ip}:${server.host_port})`\"\n                  :value=\"server.id\">\n                  <div class=\"server-option\">\n                    <span class=\"server-name\">{{ server.name }}</span>\n                    <el-tag \n                      :type=\"server.status === 'active' ? 'success' : 'danger'\" \n                      size=\"small\">\n                      {{ server.status === 'active' ? '可用' : '不可用' }}\n                    </el-tag>\n                  </div>\n                  <div class=\"server-address\">{{ server.host_ip }}:{{ server.host_port }}</div>\n                </el-option>\n              </el-select>\n            </el-form-item>\n\n            <el-form-item label=\"GUI端口\" prop=\"gui_port\" class=\"form-item-half\">\n              <el-select \n                v-model=\"form.gui_port\" \n                placeholder=\"请选择GUI端口\"\n                style=\"width: 100%\">\n                <el-option\n                  v-for=\"port in guiPorts\"\n                  :key=\"port.port\"\n                  :label=\"`端口 ${port.port}`\"\n                  :value=\"port.port\"\n                  :disabled=\"port.occupied\">\n                  <div class=\"port-option\">\n                    <span class=\"port-number\">{{ port.port }}</span>\n                    <el-tag \n                      :type=\"port.occupied ? 'danger' : 'success'\" \n                      size=\"small\">\n                      {{ port.occupied ? '已占用' : '可用' }}\n                    </el-tag>\n                  </div>\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </div>\n\n          <div class=\"form-row\">\n            <el-form-item label=\"工作服务器\" prop=\"worker_servers\" class=\"form-item-full\">\n              <el-select \n                v-model=\"form.worker_servers\" \n                multiple\n                placeholder=\"请选择工作服务器\"\n                style=\"width: 100%\">\n                <el-option\n                  v-for=\"server in availableWorkerServers\"\n                  :key=\"server.id\"\n                  :label=\"`${server.name} (${server.host_ip}:${server.host_port})`\"\n                  :value=\"server.id\">\n                  <div class=\"server-option\">\n                    <span class=\"server-name\">{{ server.name }}</span>\n                    <el-tag \n                      :type=\"server.status === 'active' ? 'success' : 'danger'\" \n                      size=\"small\">\n                      {{ server.status === 'active' ? '可用' : '不可用' }}\n                    </el-tag>\n                  </div>\n                  <div class=\"server-address\">{{ server.host_ip }}:{{ server.host_port }}</div>\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </div>\n\n          <div class=\"form-row\">\n            <el-form-item label=\"工作进程数\" prop=\"total_workers\" class=\"form-item-half\">\n              <el-input-number \n                v-model=\"form.total_workers\" \n                :min=\"1\" \n                :max=\"maxWorkers\"\n                placeholder=\"总工作进程数\"\n                style=\"width: 100%\" />\n              <div class=\"worker-info\">\n                <span>建议进程数: {{ recommendedWorkers }}</span>\n                <span>最大可用: {{ maxWorkers }}</span>\n              </div>\n            </el-form-item>\n          </div>\n        </div>\n\n        <el-form-item label=\"任务描述\" prop=\"desc\">\n          <el-input \n            type=\"textarea\" \n            v-model=\"form.desc\" \n            placeholder=\"请输入任务描述\" \n            :rows=\"3\" />\n        </el-form-item>\n      </el-form>\n    </div>\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <el-button @click=\"clearValidation\" plain>取消</el-button>\n        <el-button type=\"primary\" @click=\"addTask\">确定</el-button>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n\n<script>\nimport {mapMutations, mapState} from \"vuex\";\nimport {ElNotification, ElMessage, ElMessageBox} from \"element-plus\";\nimport { \n  Plus, \n  Promotion, \n  CopyDocument, \n  Menu, \n  Delete, \n  InfoFilled, \n  Search, \n  List, \n  Loading, \n  SuccessFilled, \n  WarningFilled,\n  More,\n  User,\n  Edit,\n  Tickets,\n  Timer,\n  Monitor,\n  Connection\n} from '@element-plus/icons-vue';\n\nexport default {\n  components: {\n    Plus,\n    Promotion,\n    CopyDocument,\n    Menu,\n    Delete,\n    InfoFilled,\n    Search,\n    List,\n    Loading,\n    SuccessFilled,\n    WarningFilled,\n    More,\n    User,\n    Edit,\n    Tickets,\n    Timer,\n    Monitor,\n    Connection\n  },\n  data() {\n    return {\n      // 状态码映射\n      taskTypeMap: {'10': '普通任务', '20': '定时任务'},\n      filterText: '',\n      taskList: [],\n      pages: {\n        count: 0,\n        current: 1\n      },\n      addDlg: false,\n      form: {\n        taskName: '',\n        desc: '',\n        project: '',\n        taskType: '10',\n        creator : '',\n        modifier : '',\n        distributed_mode: 'single',\n        master_server: null,\n        worker_servers: [],\n        total_workers: 1,\n        gui_port: 8089\n      },\n      dialogVisible: false,\n      dialogTitle: '',\n      tableLoading: false,\n      rulesPerf: {\n        taskName: [\n          { required: true, message: '请输入任务名称', trigger: 'blur' },\n          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }\n        ],\n        distributed_mode: [\n          { required: true, message: '请选择运行模式', trigger: 'change' }\n        ],\n        master_server: [\n          { \n            validator: (rule, value, callback) => {\n              if (this.form.distributed_mode === 'distributed' && !value) {\n                callback(new Error('分布式模式下必须选择主服务器'));\n              } else {\n                callback();\n              }\n            }, \n            trigger: 'change' \n          }\n        ],\n        worker_servers: [\n          { \n            validator: (rule, value, callback) => {\n              if (this.form.distributed_mode === 'distributed' && (!value || value.length === 0)) {\n                callback(new Error('分布式模式下必须选择至少一个工作服务器'));\n              } else {\n                callback();\n              }\n            }, \n            trigger: 'change' \n          }\n        ],\n        gui_port: [\n          { \n            validator: (rule, value, callback) => {\n              if (this.form.distributed_mode === 'distributed' && !value) {\n                callback(new Error('分布式模式下必须选择GUI端口'));\n              } else {\n                callback();\n              }\n            }, \n            trigger: 'change' \n          }\n        ],\n        total_workers: [\n          { \n            validator: (rule, value, callback) => {\n              if (this.form.distributed_mode === 'distributed') {\n                const maxWorkers = this.maxWorkers;\n                if (!value || value < 1) {\n                  callback(new Error('工作进程数不能小于1'));\n                } else if (value > maxWorkers) {\n                  callback(new Error(`工作进程数不能超过${maxWorkers}`));\n                } else {\n                  callback();\n                }\n              } else {\n                callback();\n              }\n            }, \n            trigger: 'blur' \n          }\n        ]\n      },\n      statusMap: {\n        '0': { text: '执行完成', type: 'success', class: 'status-completed' },\n        '1': { text: '执行中', type: 'warning', class: 'status-running' },\n        '99': { text: '执行失败', type: 'danger', class: 'status-error' }\n      },\n      colorCache: {},\n      tableHeight: 'calc(100vh - 330px)', // 初始高度，将在mounted中重新计算\n      resizeObserver: null,\n      \n      // 新增字段\n      availableServers: [],\n      guiPorts: [\n        { port: 8089, occupied: false },\n        { port: 8090, occupied: false },\n        { port: 8091, occupied: false },\n        { port: 8092, occupied: false },\n        { port: 8093, occupied: false },\n        { port: 8094, occupied: false },\n        { port: 8095, occupied: false },\n        { port: 8096, occupied: false }\n      ],\n      portCheckTimer: null\n    }\n  },\n  mounted() {\n    this.calculateTableHeight();\n    this.loadServers();\n    this.startPortCheck();\n    \n    // 监听窗口大小变化\n    this.resizeObserver = new ResizeObserver(() => {\n      this.calculateTableHeight();\n    });\n    \n    // 监听容器大小变化\n    const container = document.querySelector('.table-scrollbar');\n    if (container) {\n      this.resizeObserver.observe(container);\n    }\n    \n    // 监听窗口大小变化\n    window.addEventListener('resize', this.calculateTableHeight);\n  },\n  beforeUnmount() {\n    // 组件卸载时移除事件监听器和观察器\n    window.removeEventListener('resize', this.calculateTableHeight);\n    \n    if (this.resizeObserver) {\n      this.resizeObserver.disconnect();\n    }\n    \n    // 清理端口检查定时器\n    if (this.portCheckTimer) {\n      clearInterval(this.portCheckTimer);\n    }\n  },\n  computed: {\n    ...mapState({\n      pro: state => state.pro,\n      envId: state => state.envId\n    }),\n    username() {\n      return window.sessionStorage.getItem('username');\n    },\n    selectTaskType: {\n      get() {\n        return this.form.taskType.toString();\n      },\n      set(value) {\n        this.form.taskType = value;\n      }\n    },\n    getRunningCount() {\n      return this.taskList.filter(task => task.status === '1').length;\n    },\n    getCompletedCount() {\n      return this.taskList.filter(task => task.status === '0').length;\n    },\n    getErrorCount() {\n      return this.taskList.filter(task => task.status === '99').length;\n    },\n    // 无数据时固定表格最小高度\n    minTableHeight() {\n      return this.taskList.length > 0 ? undefined : '400px';\n    },\n    \n    // 计算可用的工作服务器（排除已选为主服务器的）\n    availableWorkerServers() {\n      return this.availableServers.filter(server => \n        server.id !== this.form.master_server\n      );\n    },\n    \n    // 计算最大工作进程数\n    maxWorkers() {\n      if (!this.form.worker_servers || this.form.worker_servers.length === 0) {\n        return 1;\n      }\n      \n      const selectedServers = this.availableServers.filter(server => \n        this.form.worker_servers.includes(server.id)\n      );\n      \n      return selectedServers.reduce((total, server) => {\n        return total + (server.max_workers || 4);\n      }, 0);\n    },\n    \n    // 计算推荐工作进程数\n    recommendedWorkers() {\n      return Math.ceil(this.maxWorkers * 0.7); // 推荐使用70%的资源\n    }\n  },\n  methods: {\n    ...mapMutations(['checkedTask']),\n\n    // 计算表格高度\n    calculateTableHeight() {\n      // 根据实际DOM高度计算\n      this.$nextTick(() => {\n        const headerHeight = document.querySelector('.dashboard-header')?.offsetHeight || 0;\n        const statsHeight = document.querySelector('.task-stats')?.offsetHeight || 0;\n        const paginationHeight = document.querySelector('.pagination-container')?.offsetHeight || 0;\n        \n        // 计算表格可用高度（减去其他元素高度和间距）\n        const availableHeight = window.innerHeight - headerHeight - statsHeight - paginationHeight - 120; // 120为各种padding和margin\n        \n        // 确保最小高度\n        this.tableHeight = Math.max(availableHeight, 300) + 'px';\n      });\n    },\n\n    async allTask(page, query) {\n      this.tableLoading = true;\n      try {\n        const response = await this.$api.getPerformanceTask(this.pro.id, page, query)\n        if (response.status === 200) {\n          this.taskList = response.data.result;\n          this.pages.count = response.data.count;\n          this.pages.current = response.data.current;\n          \n          // 数据加载完成后重新计算表格高度\n          this.$nextTick(() => {\n            this.calculateTableHeight();\n          });\n        }\n      } catch (error) {\n        ElMessage({\n          type: 'error',\n          message: '获取任务列表失败',\n          duration: 3000\n        });\n      } finally {\n        this.tableLoading = false;\n      }\n    },\n\n    currentPages(currentPage) {\n      this.allTask(currentPage, this.filterText);\n    },\n\n    searchClick() {\n      this.allTask(1, this.filterText)\n    },\n\n    clickTaskManage(data) {\n      this.$router.push({ name: 'maskMgrDetail' });\n      this.checkedTask(data)\n    },\n\n    popup(type) {\n      this.dialogVisible = true;\n      // 根据不同的对话框类型设置标题\n      switch (type) {\n        case 'add':\n          this.dialogTitle = '创建新任务';\n          this.form.creator = this.username;\n          this.form.project = this.pro.id;\n          this.form.proName = this.pro.name;\n          // 加载服务器列表\n          this.loadServers();\n          delete this.form.modifier;\n          break;\n\n        default:\n          this.dialogTitle = '';\n          break;\n      }\n    },\n\n    // 新增方法\n    async loadServers() {\n      try {\n        const response = await this.$api.getServers(this.pro.id, 1);\n        if (response.status === 200) {\n          this.availableServers = response.data.result || [];\n        }\n      } catch (error) {\n        console.error('加载服务器列表失败:', error);\n        this.$message.error('加载服务器列表失败');\n      }\n    },\n\n    async checkPortStatus() {\n      if (!this.form.master_server) {\n        return;\n      }\n      \n      try {\n        const response = await this.$api.checkServerPorts(this.form.master_server);\n        if (response.status === 200) {\n          const occupiedPorts = response.data.occupied_ports || [];\n          this.guiPorts.forEach(port => {\n            port.occupied = occupiedPorts.includes(port.port);\n          });\n        }\n      } catch (error) {\n        console.error('检查端口状态失败:', error);\n      }\n    },\n\n    startPortCheck() {\n      // 每30秒检查一次端口状态\n      this.portCheckTimer = setInterval(() => {\n        if (this.form.distributed_mode === 'distributed' && this.form.master_server) {\n          this.checkPortStatus();\n        }\n      }, 30000);\n    },\n\n    handleMasterServerChange() {\n      // 主服务器变化时重新检查端口状态\n      this.form.gui_port = 8089; // 重置为默认端口\n      this.checkPortStatus();\n      \n      // 清空已选择的工作服务器中包含主服务器的选项\n      if (this.form.worker_servers.includes(this.form.master_server)) {\n        this.form.worker_servers = this.form.worker_servers.filter(\n          id => id !== this.form.master_server\n        );\n      }\n    },\n\n    clearValidation() {\n      this.dialogVisible = false;\n      if (this.$refs.perfRef) {\n        this.$refs.perfRef.clearValidate(); // 添加条件检查\n      }\n      this.form = {\n        taskName: '',\n        desc: '',\n        project: '',\n        taskType: '10',\n        creator : '',\n        modifier : '',\n        distributed_mode: 'single',\n        master_server: null,\n        worker_servers: [],\n        total_workers: 1,\n        gui_port: 8089\n      }\n    },\n\n    async addTask() {\n      this.$refs.perfRef.validate(async vaild => {\n        if (!vaild) return;\n        const params = this.form\n        try {\n          const response = await this.$api.createPerformanceTask(params);\n          if (response.status === 201) {\n            // 先重置表单数据\n            this.form = {\n              taskName: '',\n              desc: '',\n              project: '',\n              taskType: '10',\n              creator : '',\n              modifier : '',\n              distributed_mode: 'single',\n              master_server: null,\n              worker_servers: [],\n              total_workers: 1,\n              gui_port: 8089\n            };\n            \n            // 再关闭弹窗\n            this.dialogVisible = false;\n            \n            // 最后显示成功消息并刷新列表\n            ElMessage({\n              type: 'success',\n              message: '任务创建成功',\n              duration: 1000\n            });\n            this.allTask(1);\n          }\n        } catch (error) {\n          ElMessage({\n            type: 'error',\n            message: '添加失败: ' + (error.message || '未知错误'),\n            duration: 3000\n          });\n        }\n      })\n    },\n\n    delTask(id) {\n      ElMessageBox.confirm('此操作将永久删除该任务, 是否继续?', '删除确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n        closeOnClickModal: false\n      })\n        .then(async () => {\n          try {\n            const response = await this.$api.delPerformanceTask(id)\n            if(response.status === 204) {\n              ElMessage({\n                type: 'success',\n                message: '删除成功!'\n              });\n              // 刷新页面\n              this.allTask(this.pages.current);\n            }\n          } catch (error) {\n            ElMessage({\n              type: 'error',\n              message: '删除失败: ' + (error.message || '未知错误'),\n              duration: 3000\n            });\n          }\n        })\n        .catch(() => {\n          ElMessage({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n    },\n\n    async clickCopy(data) {\n      const params = {...data};\n      params.taskName = params.taskName + '_副本';\n      try {\n        const response = await this.$api.createPerformanceTask(params);\n        if (response.status === 201) {\n          ElMessage({\n            type: 'success',\n            message: '复制成功',\n            duration: 1000\n          });\n          this.allTask(this.pages.current);\n        }\n      } catch (error) {\n        ElMessage({\n          type: 'error',\n          message: '复制失败: ' + (error.message || '未知错误'),\n          duration: 3000\n        });\n      }\n    },\n\n    async runTask(id) {\n      if (!this.envId) {\n        this.$message({\n          type: 'warning',\n          message: '当前未选中执行环境!',\n          duration: 1000\n        });\n        return\n      }\n      const params = { taskId: id, env: this.envId };\n      try {\n        // 注意：这里不再调用updateTaskStatus方法，后端需要在运行任务时更新状态为\"执行中\"\n        const response = await this.$api.runTask(id, params);\n        if (response.status === 200) {\n          ElNotification({\n            title: '任务已启动',\n            message: '请前往报告列表查看结果',\n            type: 'success',\n            duration: 3000,\n            showClose: true,\n            position: 'top-right',\n          });\n          \n          // 刷新任务列表以获取最新状态\n          this.refreshTaskList();\n        }\n      } catch (error) {\n        // 注意：后端需要在任务启动失败时更新状态为\"执行失败\"\n        ElMessage({\n          type: 'error',\n          message: '任务启动失败: ' + (error.message || '未知错误'),\n          duration: 3000\n        });\n        \n        // 刷新任务列表以获取最新状态\n        this.refreshTaskList();\n      }\n    },\n    \n    // 刷新任务列表\n    refreshTaskList() {\n      // 延迟一小段时间后刷新列表，确保后端状态已更新\n      setTimeout(() => {\n        this.allTask(this.pages.current);\n      }, 1000);\n    },\n\n    getStatusText(status) {\n      return this.statusMap[status]?.text || status;\n    },\n\n    getStatusClass(status) {\n      return this.statusMap[status]?.class || 'status-unknown';\n    },\n\n\n    getRunPatternType(runPattern) {\n      const typeMap = {\n        'loadtest': 'primary',\n        'stresstest': 'warning',\n        'spiketest': 'danger',\n        'volumetest': 'success',\n        'endurancetest': 'info',\n        'baselinetest': ''\n      };\n      return typeMap[runPattern] || 'info';\n    },\n\n    // 分布式模式相关方法\n    getDistributedModeText(mode) {\n      const modeMap = {\n        'single': '单机模式',\n        'distributed': '分布式模式'\n      };\n      return modeMap[mode] || mode || '未设置';\n    },\n\n    getDistributedModeType(mode) {\n      const typeMap = {\n        'single': 'info',\n        'distributed': 'warning'\n      };\n      return typeMap[mode] || 'info';\n    },\n\n    formatTimeAgo(timestamp) {\n      if (!timestamp) return '';\n      \n      const date = new Date(timestamp);\n      const now = new Date();\n      const seconds = Math.floor((now - date) / 1000);\n      \n      if (seconds < 60) return '刚刚';\n      \n      const minutes = Math.floor(seconds / 60);\n      if (minutes < 60) return `${minutes}分钟前`;\n      \n      const hours = Math.floor(minutes / 60);\n      if (hours < 24) return `${hours}小时前`;\n      \n      const days = Math.floor(hours / 24);\n      if (days < 7) return `${days}天前`;\n      \n      return this.$tools.rTime(timestamp).split(' ')[0];\n    },\n\n    getRandomColor(str) {\n      if (!str) return '#909399';\n      \n      if (this.colorCache[str]) {\n        return this.colorCache[str];\n      }\n      \n      // 生成一个基于字符串的伪随机颜色\n      let hash = 0;\n      for (let i = 0; i < str.length; i++) {\n        hash = str.charCodeAt(i) + ((hash << 5) - hash);\n      }\n      \n      const colors = [\n        '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',\n        '#3963BC', '#4EC9B0', '#FF9F43', '#9C27B0', '#00BCD4',\n        '#795548', '#607D8B', '#FF5722', '#9E9E9E', '#4CAF50'\n      ];\n      \n      const index = Math.abs(hash) % colors.length;\n      this.colorCache[str] = colors[index];\n      return this.colorCache[str];\n    },\n    \n    handleCommand(command, row) {\n      switch (command) {\n        case 'copy':\n          this.clickCopy(row);\n          break;\n        case 'delete':\n          this.delTask(row.id);\n          break;\n      }\n    }\n  },\n  created() {\n    this.allTask(1)\n  }\n}\n</script>\n\n<style scoped>\n/* 主容器样式 */\n.performance-container {\n  padding: 20px;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  background-color: #f5f7fa;\n  overflow: hidden;\n}\n\n/* 头部区域样式 */\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 16px 24px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.header-title h2 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.header-tag {\n  font-weight: normal;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.search-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.search-input {\n  width: 280px;\n}\n\n.search-input :deep(.el-input__wrapper) {\n  border-radius: 6px;\n  box-shadow: 0 0 0 1px #dcdfe6 inset;\n}\n\n.search-icon {\n  color: #909399;\n}\n\n.search-btn {\n  height: 32px;\n  border-radius: 6px;\n}\n\n.create-btn {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  border-radius: 6px;\n  padding: 8px 16px;\n  font-weight: 500;\n  transition: all 0.3s;\n}\n\n.create-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n}\n\n/* 统计卡片样式 */\n.task-stats {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 16px;\n  flex-shrink: 0;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  position: relative;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n  transition: all 0.3s;\n  overflow: hidden;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: #303133;\n}\n\n.stat-label {\n  color: #909399;\n  font-size: 14px;\n}\n\n.stat-icon {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  color: white;\n}\n\n.stat-card:nth-child(1) {\n  border-top: 3px solid #409EFF;\n}\n.stat-card:nth-child(2) {\n  border-top: 3px solid #E6A23C;\n}\n.stat-card:nth-child(3) {\n  border-top: 3px solid #67C23A;\n}\n.stat-card:nth-child(4) {\n  border-top: 3px solid #F56C6C;\n}\n\n.running-icon {\n  background-color: #E6A23C;\n}\n.running-icon1 {\n  background-color: #409EFF;\n}\n.completed-icon {\n  background-color: #67C23A;\n}\n\n.error-icon {\n  background-color: #F56C6C;\n}\n\n/* 滚动容器样式 */\n.table-scrollbar {\n  flex: 1;\n  overflow: hidden;\n}\n\n.table-scrollbar :deep(.el-scrollbar__wrap) {\n  overflow-x: hidden;\n}\n\n/* 表格卡片样式 */\n.task-table-card {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 表格内容样式 */\n:deep(.el-table) {\n  --el-table-header-bg-color: #f5f7fa;\n  --el-table-row-hover-bg-color: #f0f5ff;\n  border-radius: 6px;\n  overflow: hidden;\n  flex: 1;\n}\n\n:deep(.el-table__header) {\n  font-weight: 600;\n}\n\n:deep(.el-table__row) {\n  transition: all 0.2s;\n}\n\n:deep(.el-table__row:hover) {\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  transform: translateY(-2px);\n}\n\n:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {\n  background-color: #fafafa;\n}\n\n/* 空状态样式 */\n.empty-data {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 0;\n}\n\n.empty-data p {\n  margin-top: 0;\n  margin-bottom: 20px;\n  color: #909399;\n}\n\n/* 任务信息样式 */\n.task-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.task-avatar-container {\n  flex-shrink: 0;\n}\n\n.task-avatar {\n  font-weight: 500;\n  border: 2px solid white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.task-detail {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.task-name {\n  font-weight: 500;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.task-name-link {\n  color: #303133;\n  text-decoration: none;\n  transition: all 0.3s;\n  position: relative;\n}\n\n.task-name-link:hover {\n  color: var(--el-color-primary);\n}\n\n.task-name-link:hover::after {\n  content: '';\n  position: absolute;\n  width: 100%;\n  height: 2px;\n  background-color: var(--el-color-primary);\n  bottom: -2px;\n  left: 0;\n  border-radius: 1px;\n}\n\n.task-meta {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.meta-tag {\n  padding: 0 6px;\n  height: 22px;\n  line-height: 20px;\n  font-size: 12px;\n  border-radius: 4px;\n}\n\n.meta-info {\n  color: #909399;\n  font-size: 12px;\n}\n\n/* 状态样式 */\n.status-cell {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n.status-indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n}\n\n.status-running {\n  background-color: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);\n}\n\n.status-completed {\n  background-color: #67C23A;\n  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);\n}\n\n.status-error {\n  background-color: #F56C6C;\n  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);\n}\n\n.status-text {\n  font-size: 13px;\n}\n\n/* 模式标签 */\n.mode-tag {\n  min-width: 60px;\n}\n\n/* 用户信息 */\n.user-info {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.user-avatar {\n  flex-shrink: 0;\n}\n\n.user-name {\n  font-size: 13px;\n  font-weight: 500;\n  max-width: 80px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n/* 更新信息 */\n.update-info {\n  text-align: center;\n}\n\n.update-by {\n  font-size: 13px;\n  font-weight: 500;\n  color: #606266;\n  margin-bottom: 2px;\n}\n\n.update-time {\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 备注样式 */\n.desc-text {\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  font-size: 13px;\n  color: #606266;\n}\n\n.no-data {\n  color: #c0c4cc;\n  font-size: 13px;\n}\n\n/* 操作区域 */\n.action-buttons {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  height: 32px;\n  border-radius: 4px;\n  transition: all 0.3s;\n}\n\n.action-btn:hover {\n  transform: translateY(-2px);\n}\n\n.more-btn {\n  width: 32px;\n  height: 32px;\n  padding: 0;\n  border-radius: 4px;\n}\n\n:deep(.danger-item) {\n  color: #F56C6C;\n}\n\n/* 分页样式 */\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  flex-shrink: 0;\n}\n\n/* 对话框样式 */\n:deep(.task-dialog .el-dialog) {\n  border-radius: 12px;\n  overflow: hidden;\n  max-width: 90%;\n}\n\n:deep(.task-dialog .el-dialog__header) {\n  padding: 20px;\n  margin-right: 0;\n  background: linear-gradient(135deg, #409EFF, #2a62ff);\n}\n\n:deep(.task-dialog .el-dialog__title) {\n  color: white;\n  font-weight: 600;\n  font-size: 18px;\n}\n\n:deep(.task-dialog .el-dialog__headerbtn .el-dialog__close) {\n  color: white;\n}\n\n.dialog-content {\n  padding: 20px;\n}\n\n/* 表单布局样式 */\n.form-row {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 10px;\n}\n\n.form-item-half {\n  flex: 1;\n  min-width: 0;\n}\n\n.form-item-full {\n  width: 100%;\n}\n\n:deep(.task-dialog .el-form-item__label) {\n  padding-bottom: 8px;\n  font-weight: 500;\n}\n\n:deep(.task-dialog .el-input__wrapper),\n:deep(.task-dialog .el-textarea__inner) {\n  border-radius: 6px;\n  box-shadow: 0 0 0 1px #dcdfe6 inset;\n  transition: all 0.3s;\n}\n\n:deep(.task-dialog .el-input__wrapper:hover),\n:deep(.task-dialog .el-textarea__inner:hover) {\n  box-shadow: 0 0 0 1px var(--el-color-primary) inset;\n}\n\n:deep(.task-dialog .el-form-item:last-child) {\n  margin-bottom: 0;\n}\n\n/* 任务类型和运行模式选择样式 */\n.task-type-radio,\n.run-mode-radio {\n  width: 100%;\n  display: flex;\n  gap: 10px;\n}\n\n.task-type-radio .el-radio,\n.run-mode-radio .el-radio {\n  margin-right: 0;\n  margin-bottom: 10px;\n  padding: 10px;\n  border: 2px solid #e4e7ed;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  width: calc(50% - 5px);\n}\n\n.task-type-radio .el-radio:last-child,\n.run-mode-radio .el-radio:last-child {\n  margin-bottom: 0;\n}\n\n.task-type-radio .el-radio:hover,\n.run-mode-radio .el-radio:hover {\n  border-color: #409eff;\n  background-color: #f0f9ff;\n}\n\n.task-type-radio .el-radio.is-checked,\n.run-mode-radio .el-radio.is-checked {\n  border-color: #409eff;\n  background-color: #f0f9ff;\n}\n\n.radio-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n}\n\n.radio-icon {\n  font-size: 18px;\n  color: #409eff;\n}\n\n/* 分布式配置区域样式 */\n.distributed-config {\n  margin-top: 10px;\n  margin-bottom: 20px;\n  padding: 20px;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n/* 服务器和端口选项样式 */\n.server-option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  padding: 0 5px;\n}\n\n.server-name {\n  font-weight: 600;\n  color: #303133;\n  flex: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.server-address {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding-left: 5px;\n}\n\n/* 端口选项样式 */\n.port-option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.port-number {\n  font-weight: 600;\n  color: #303133;\n}\n\n/* 工作进程数信息 */\n.worker-info {\n  margin-top: 8px;\n  display: flex;\n  gap: 20px;\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 提示框样式 */\n.tip-box {\n  margin-top: 8px;\n  padding: 8px 12px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 13px;\n}\n\n.tip-box.warning {\n  background-color: #fdf6ec;\n  border: 1px solid #f5dab1;\n  color: #e6a23c;\n}\n\n.tip-box.info {\n  background-color: #f0f9ff;\n  border: 1px solid #b3d8ff;\n  color: #409eff;\n}\n\n/* 下拉选项样式 */\n:deep(.el-select-dropdown__item) {\n  padding: 8px 10px;\n  height: auto;\n}\n\n:deep(.el-select-dropdown__item .server-option) {\n  padding: 0;\n}\n\n:deep(.el-tag) {\n  margin: 0;\n}\n</style>", "import { render } from \"./PerformanceTask.vue?vue&type=template&id=0ebe0c80&scoped=true\"\nimport script from \"./PerformanceTask.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceTask.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceTask.vue?vue&type=style&index=0&id=0ebe0c80&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0ebe0c80\"]])\n\nexport default __exports__"], "names": ["class", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_tag", "type", "effect", "_cache", "_hoisted_4", "_hoisted_5", "_component_el_input", "$data", "filterText", "$event", "placeholder", "clearable", "prefix", "_withCtx", "_component_el_icon", "_component_Search", "_component_el_button", "onClick", "$options", "searchClick", "popup", "_component_Plus", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "taskList", "length", "_hoisted_9", "_component_List", "_hoisted_10", "_hoisted_11", "getRunningCount", "_hoisted_12", "_component_Loading", "_hoisted_13", "_hoisted_14", "getCompletedCount", "_hoisted_15", "_component_SuccessFilled", "_hoisted_16", "_hoisted_17", "getErrorCount", "_hoisted_18", "_component_WarningFilled", "_component_el_scrollbar", "height", "_hoisted_19", "_createBlock", "_component_el_table", "data", "style", "border", "stripe", "tableHeight", "background", "color", "fontWeight", "empty", "_hoisted_40", "_component_el_empty", "description", "_component_el_table_column", "width", "align", "label", "default", "row", "_hoisted_20", "_hoisted_21", "_component_el_avatar", "size", "_normalizeStyle", "backgroundColor", "getRandomColor", "taskName", "substr", "toUpperCase", "_hoisted_22", "_hoisted_23", "_component_router_link", "to", "clickTaskManage", "_hoisted_24", "taskType", "taskTypeMap", "toString", "_hoisted_25", "formatTimeAgo", "create_time", "_hoisted_26", "_normalizeClass", "getStatusClass", "status", "_hoisted_27", "status_display", "getStatusText", "getRunPatternType", "runPattern", "taskType_display", "getDistributedModeType", "distributed_mode", "getDistributedModeText", "_hoisted_28", "_hoisted_29", "creator", "_hoisted_30", "modifier", "update_time", "_createElementBlock", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "desc", "_component_el_tooltip", "content", "placement", "_hoisted_37", "_hoisted_38", "fixed", "_hoisted_39", "runTask", "id", "plain", "_component_Promotion", "_component_Menu", "_component_el_dropdown", "trigger", "onCommand", "handleCommand", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "_component_CopyDocument", "divided", "_component_Delete", "_component_More", "tableLoading", "_hoisted_41", "_component_el_pagination", "pages", "current", "layout", "total", "count", "onCurrentChange", "currentPages", "_component_el_dialog", "dialogVisible", "title", "dialogTitle", "clearValidation", "footer", "_hoisted_64", "addTask", "_hoisted_42", "_component_el_form", "model", "form", "rules", "rulesPerf", "ref", "_hoisted_43", "_component_el_form_item", "prop", "maxlength", "proName", "disabled", "_hoisted_44", "_component_el_radio_group", "selectTaskType", "_component_el_radio", "_hoisted_45", "_component_Tickets", "_hoisted_46", "_component_Timer", "_hoisted_47", "_component_InfoFilled", "_hoisted_48", "_component_Monitor", "_hoisted_49", "_component_Connection", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_component_el_select", "master_server", "onChange", "handleMasterServerChange", "_Fragment", "_renderList", "availableServers", "server", "_component_el_option", "key", "name", "host_ip", "host_port", "value", "_hoisted_53", "_hoisted_54", "_hoisted_55", "gui_port", "gui<PERSON><PERSON><PERSON>", "port", "occupied", "_hoisted_56", "_hoisted_57", "_hoisted_58", "worker_servers", "multiple", "availableWorkerServers", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_component_el_input_number", "total_workers", "min", "max", "maxWorkers", "_hoisted_63", "recommendedWorkers", "rows", "components", "Plus", "Promotion", "CopyDocument", "<PERSON><PERSON>", "Delete", "InfoFilled", "Search", "List", "Loading", "SuccessFilled", "WarningFilled", "More", "User", "Edit", "Tickets", "Timer", "Monitor", "Connection", "addDlg", "project", "required", "message", "validator", "rule", "callback", "this", "Error", "statusMap", "text", "colorCache", "resizeObserver", "portCheckTimer", "mounted", "calculateTableHeight", "loadServers", "startPortCheck", "ResizeObserver", "container", "document", "querySelector", "observe", "window", "addEventListener", "beforeUnmount", "removeEventListener", "disconnect", "clearInterval", "computed", "mapState", "pro", "state", "envId", "username", "sessionStorage", "getItem", "get", "set", "filter", "task", "minTableHeight", "undefined", "selectedServers", "includes", "reduce", "max_workers", "Math", "ceil", "methods", "mapMutations", "$nextTick", "headerHeight", "offsetHeight", "statsHeight", "paginationHeight", "availableHeight", "innerHeight", "allTask", "page", "query", "response", "$api", "getPerformanceTask", "result", "error", "ElMessage", "duration", "currentPage", "$router", "push", "checkedTask", "getServers", "console", "$message", "checkPortStatus", "checkServerPorts", "occupiedPorts", "occupied_ports", "for<PERSON>ach", "setInterval", "$refs", "perfRef", "clearValidate", "validate", "async", "vaild", "params", "createPerformanceTask", "delTask", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "closeOnClickModal", "then", "delPerformanceTask", "catch", "clickCopy", "taskId", "env", "ElNotification", "showClose", "position", "refreshTaskList", "setTimeout", "typeMap", "mode", "modeMap", "timestamp", "date", "Date", "now", "seconds", "floor", "minutes", "hours", "days", "$tools", "rTime", "split", "str", "hash", "i", "charCodeAt", "colors", "index", "abs", "created", "__exports__", "render"], "sourceRoot": ""}