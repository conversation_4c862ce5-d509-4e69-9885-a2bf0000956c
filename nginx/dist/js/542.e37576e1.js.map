{"version": 3, "file": "js/542.e37576e1.js", "mappings": "0NACMA,MAAM,oB,SACSA,MAAM,uB,GAEnBA,MAAM,c,GACLA,MAAM,e,GAILA,MAAM,iB,GACLA,MAAM,gB,GACNA,MAAM,e,GAGRA,MAAM,gB,GAGHA,MAAM,e,GAMLA,MAAM,qB,GAEJA,MAAM,kB,GACJA,MAAM,mB,GAEJA,MAAM,a,GACJA,MAAM,a,GACNA,MAAM,W,GAKVA,MAAM,c,GACJA,MAAM,mB,GACJA,MAAM,a,GACNA,MAAM,a,GACJA,MAAM,c,GAKVA,MAAM,qB,GACJA,MAAM,a,GACNA,MAAM,a,GACJA,MAAM,c,GAKVA,MAAM,kB,GACJA,MAAM,a,GACNA,MAAM,a,GACJA,MAAM,c,GAKVA,MAAM,mB,GACJA,MAAM,a,GACNA,MAAM,a,GACJA,MAAM,c,GAQZA,MAAM,iB,GACJA,MAAM,c,GASJA,MAAM,gBAAgBC,IAAI,U,GAE5BD,MAAM,c,GAIJA,MAAM,gBAAgBC,IAAI,U,GAMhCD,MAAM,gB,GACJA,MAAM,iB,GAEJA,MAAM,e,iBAUDA,MAAM,a,iBAcLA,MAAM,gB,GAGNA,MAAM,c,GACJA,MAAM,c,GAKRA,MAAM,gB,GAKRA,MAAM,gB,IAQEA,MAAM,e,IAONA,MAAM,a,kWApJAE,GAAAC,S,WAApCC,EAAAA,EAAAA,IAoMM,MApMNC,EAoMM,CAnMMH,GAAAI,S,WAAXF,EAAAA,EAAAA,IAkMM,MAlMNG,EAkMM,EAhMLC,EAAAA,EAAAA,IAgBM,MAhBNC,EAgBM,EAfLD,EAAAA,EAAAA,IAQM,MARNE,EAQM,EAPLC,EAAAA,EAAAA,IAEYC,GAAA,CAFDC,KAAK,OAAOb,MAAM,WAAYc,QAAOC,GAAAC,Q,kBAC/C,IAA2B,EAA3BL,EAAAA,EAAAA,IAA2BM,GAAA,M,iBAAlB,IAAQ,EAARN,EAAAA,EAAAA,IAAQO,M,6BAElBV,EAAAA,EAAAA,IAGM,MAHNW,EAGM,EAFLX,EAAAA,EAAAA,IAA4D,MAA5DY,EAA0B,UAAMC,EAAAA,EAAAA,IAAGC,EAAAC,OAAOC,OAAOC,IAAE,IACnDjB,EAAAA,EAAAA,IAAqE,MAArEkB,GAAqEL,EAAAA,EAAAA,IAAzCC,EAAAK,OAAOC,MAAM1B,GAAAC,OAAO0B,cAAW,QAG7DrB,EAAAA,EAAAA,IAKM,MALNsB,EAKM,EAJLtB,EAAAA,EAAAA,IAGM,OAHDR,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,eAAuBhB,GAAAiB,oB,cACjCxB,EAAAA,EAAAA,IAAoC,QAA9BR,MAAM,eAAc,OAAG,KAC7BQ,EAAAA,EAAAA,IAA6D,OAA7DyB,GAA6DZ,EAAAA,EAAAA,IAAhCnB,GAAAC,OAAO+B,WAAa,GAAI,IAAC,I,QAItDvB,EAAAA,EAAAA,IA8KewB,GAAA,CA9KAC,MAAO,CAAAC,OAAA,qBAAAC,UAAA,UAAkD,C,iBAEtE,IA2KM,EA3KN9B,EAAAA,EAAAA,IA2KM,MA3KN+B,EA2KM,EAzKJ/B,EAAAA,EAAAA,IAiEM,MAjENgC,EAiEM,EAhEJhC,EAAAA,EAAAA,IAMM,MANNiC,EAMM,C,aALJjC,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAGM,MAHNkC,EAGM,EAFJlC,EAAAA,EAAAA,IAA8D,MAA9DmC,GAA8DtB,EAAAA,EAAAA,IAApCnB,GAAAC,OAAOyC,WAAa,SAAJ,IAC1CpC,EAAAA,EAAAA,IAA0D,MAA1DqC,GAA0DxB,EAAAA,EAAAA,IAAlCnB,GAAAC,OAAO2C,UAAY,QAAJ,QAK3CtC,EAAAA,EAAAA,IAiCM,MAjCNuC,EAiCM,EAhCJvC,EAAAA,EAAAA,IAOM,MAPNwC,EAOM,EANJxC,EAAAA,EAAAA,IAAgE,MAAhEyC,EAAgE,EAAzCtC,EAAAA,EAAAA,IAAmCM,GAAA,M,iBAA1B,IAAgB,EAAhBN,EAAAA,EAAAA,IAAgBuC,M,SAChD1C,EAAAA,EAAAA,IAGM,MAHN2C,EAGM,EAFJ3C,EAAAA,EAAAA,IAAgF,MAAhF4C,GAAgF/B,EAAAA,EAAAA,IAArDnB,GAAAI,OAAO+C,SAAWnD,GAAAI,OAAO+C,QAAQC,QAAU,GAAJ,G,aAClE9C,EAAAA,EAAAA,IAAkC,OAA7BR,MAAM,cAAa,QAAI,M,aAE9BQ,EAAAA,EAAAA,IAA6B,OAAxBR,MAAM,aAAW,aAExBQ,EAAAA,EAAAA,IAOM,MAPN+C,EAOM,EANJ/C,EAAAA,EAAAA,IAAiE,MAAjEgD,EAAiE,EAA1C7C,EAAAA,EAAAA,IAAoCM,GAAA,M,iBAA3B,IAAiB,EAAjBN,EAAAA,EAAAA,IAAiB8C,M,SACjDjD,EAAAA,EAAAA,IAGM,MAHNkD,EAGM,EAFJlD,EAAAA,EAAAA,IAAuD,MAAvDmD,GAAuDtC,EAAAA,EAAAA,IAA5BN,GAAA6C,aAAaN,QAAM,G,aAC9C9C,EAAAA,EAAAA,IAAkC,OAA7BR,MAAM,cAAa,QAAI,M,aAE9BQ,EAAAA,EAAAA,IAA6B,OAAxBR,MAAM,aAAW,aAExBQ,EAAAA,EAAAA,IAOM,MAPNqD,EAOM,EANJrD,EAAAA,EAAAA,IAA2D,MAA3DsD,EAA2D,EAApCnD,EAAAA,EAAAA,IAA8BM,GAAA,M,iBAArB,IAAW,EAAXN,EAAAA,EAAAA,IAAWoD,M,SAC3CvD,EAAAA,EAAAA,IAGM,MAHNwD,EAGM,EAFJxD,EAAAA,EAAAA,IAAoD,MAApDyD,GAAoD5C,EAAAA,EAAAA,IAAzBN,GAAAmD,UAAUZ,QAAM,G,aAC3C9C,EAAAA,EAAAA,IAAkC,OAA7BR,MAAM,cAAa,QAAI,M,aAE9BQ,EAAAA,EAAAA,IAA6B,OAAxBR,MAAM,aAAW,aAExBQ,EAAAA,EAAAA,IAOM,MAPN2D,EAOM,EANJ3D,EAAAA,EAAAA,IAAqE,MAArE4D,EAAqE,EAA9CzD,EAAAA,EAAAA,IAAwCM,GAAA,M,iBAA/B,IAAqB,EAArBN,EAAAA,EAAAA,IAAqB0D,M,SACrD7D,EAAAA,EAAAA,IAGM,MAHN8D,EAGM,EAFJ9D,EAAAA,EAAAA,IAAqD,MAArD+D,GAAqDlD,EAAAA,EAAAA,IAA1BN,GAAAyD,WAAWlB,QAAM,G,aAC5C9C,EAAAA,EAAAA,IAAkC,OAA7BR,MAAM,cAAa,QAAI,M,aAE9BQ,EAAAA,EAAAA,IAA6B,OAAxBR,MAAM,aAAW,eAK1BQ,EAAAA,EAAAA,IAkBM,MAlBNiE,EAkBM,EAjBJjE,EAAAA,EAAAA,IAUM,MAVNkE,EAUM,C,eATJlE,EAAAA,EAAAA,IAOM,OAPDR,MAAM,gBAAc,EACvBQ,EAAAA,EAAAA,IAAa,UAAT,SACJA,EAAAA,EAAAA,IAIM,OAJDR,MAAM,gBAAc,EACvBQ,EAAAA,EAAAA,IAA2C,QAArCR,MAAM,uBAAsB,OAClCQ,EAAAA,EAAAA,IAAwC,QAAlCR,MAAM,oBAAmB,OAC/BQ,EAAAA,EAAAA,IAAyC,QAAnCR,MAAM,qBAAoB,U,KAGpCQ,EAAAA,EAAAA,IAA8C,MAA9CmE,EAA8C,aAEhDnE,EAAAA,EAAAA,IAKM,MALNoE,EAKM,C,eAJJpE,EAAAA,EAAAA,IAEM,OAFDR,MAAM,gBAAc,EACvBQ,EAAAA,EAAAA,IAAa,UAAT,U,KAENA,EAAAA,EAAAA,IAA8C,MAA9CqE,EAA8C,iBAMpDrE,EAAAA,EAAAA,IAoGM,MApGNsE,EAoGM,EAnGJtE,EAAAA,EAAAA,IAeM,MAfNuE,EAeM,C,eAdJvE,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAYM,MAZNwE,EAYM,G,aAXJ5E,EAAAA,EAAAA,IAUM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IATUhF,GAAAiF,WAAPC,K,WADThF,EAAAA,EAAAA,IAUM,OARHiF,IAAKD,EAAIE,MACVtF,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,aAAY,CAAAwD,OACArF,GAAAsF,gBAAkBJ,EAAIE,MAAK,CAAGF,EAAIE,QAAQ,KAC3DxE,QAAK2E,GAAE1E,GAAA2E,mBAAmBN,EAAIE,Q,EAE/B3E,EAAAA,EAAAA,IAAgEM,GAAA,CAAvDjB,MAAM,YAAU,C,iBAAC,IAA4B,G,WAA5B2F,EAAAA,EAAAA,KAA4BC,EAAAA,EAAAA,IAAZR,EAAIS,U,YAC9CrF,EAAAA,EAAAA,IAA4B,aAAAa,EAAAA,EAAAA,IAAnB+D,EAAIU,OAAK,IAClBtF,EAAAA,EAAAA,IAA2D,OAA3DuF,GAA2D1E,EAAAA,EAAAA,IAAhCN,GAAAiF,YAAYZ,EAAIE,QAAK,I,mBAKtD3E,EAAAA,EAAAA,IAiFewB,GAAA,CAjFDnC,MAAM,cAAY,C,iBAC9B,IA+EmB,EA/EnBW,EAAAA,EAAAA,IA+EmBsF,EAAAA,EAAA,CA/EDC,KAAK,cAAY,C,iBAE/B,IAAwC,G,aAD1C9F,EAAAA,EAAAA,IA6EM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA5EqBhF,GAAAiG,eAAc,CAA/BC,EAAOC,M,WADjBjG,EAAAA,EAAAA,IA6EM,OA3EHiF,IAAKe,EAAMF,KAAOG,EACnBrG,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,aAAY,CAAAuE,SACEpG,GAAAqG,eAAeC,SAASH,O,EAE5C7F,EAAAA,EAAAA,IAaM,OAbDR,MAAM,eAAgBc,QAAK2E,GAAE1E,GAAA0F,YAAYJ,I,EAC5C7F,EAAAA,EAAAA,IAEM,MAFNkG,EAEM,EADJ/F,EAAAA,EAAAA,IAAgEM,GAAA,M,iBAAvD,IAA6C,G,WAA7C0E,EAAAA,EAAAA,KAA6CC,EAAAA,EAAAA,IAA7B7E,GAAA4F,aAAaP,EAAMQ,Y,cAE9CpG,EAAAA,EAAAA,IAKM,MALNqG,EAKM,EAJJrG,EAAAA,EAAAA,IAAyD,MAAzDsG,GAAyDzF,EAAAA,EAAAA,IAA9B+E,EAAMF,MAAQ,SAAJ,IACrC1F,EAAAA,EAAAA,IAEM,OAFDR,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,cAAsBhB,GAAAgG,mBAAmBX,EAAMQ,W,QACrD7F,GAAAiG,kBAAkBZ,EAAMQ,QAAK,MAGpCpG,EAAAA,EAAAA,IAEM,MAFNyG,EAEM,EADJtG,EAAAA,EAAAA,IAAqFM,GAAA,CAA3EjB,OAAK+B,EAAAA,EAAAA,IAAA,CAAAmF,OAAYhH,GAAAqG,eAAeC,SAASH,M,kBAAU,IAAc,EAAd1F,EAAAA,EAAAA,IAAcwG,M,uCAI/E3G,EAAAA,EAAAA,IAuDM,MAvDN4G,EAuDM,EAtDJzG,EAAAA,EAAAA,IAqDW0G,GAAA,CApDRC,KAAMlB,EAAMmB,OAAS,GACtBvH,MAAM,aACNwH,KAAK,S,kBAEL,IAMkB,EANlB7G,EAAAA,EAAAA,IAMkB8G,GAAA,CAND5G,KAAK,UAAQ,CACjB6G,SAAOC,EAAAA,EAAAA,IAGVC,GAHiB,EACvBpH,EAAAA,EAAAA,IAEM,MAFNqH,GAEM,EADJlH,EAAAA,EAAAA,IAAuCmH,GAAA,CAA7BC,OAAQH,EAAMI,K,8BAI9BrH,EAAAA,EAAAA,IAOkB8G,GAAA,CAPD3B,MAAM,OAAO,YAAU,O,CAC3B4B,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpH,EAAAA,EAAAA,IAGM,MAHNyH,GAGM,EAFJtH,EAAAA,EAAAA,IAAiEM,GAAA,M,iBAAxD,IAA8C,G,WAA9C0E,EAAAA,EAAAA,KAA8CC,EAAAA,EAAAA,IAA9B7E,GAAAmH,gBAAgBN,EAAMI,U,YAC/CxH,EAAAA,EAAAA,IAA4C,aAAAa,EAAAA,EAAAA,IAAnCuG,EAAMI,IAAI9B,MAAQ,SAAJ,O,OAI7BvF,EAAAA,EAAAA,IAUkB8G,GAAA,CAVD3B,MAAM,OAAOqC,MAAM,MAAMC,MAAM,U,CACnCV,SAAOC,EAAAA,EAAAA,IAOVC,GAPiB,CAEI,QAAnBA,EAAMI,IAAInH,MAAkB+G,EAAMI,IAAIK,S,WAD9CjI,EAAAA,EAAAA,IAMM,O,MAJJJ,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,eACE6F,EAAMI,IAAIK,OAAOC,kB,QAEtBV,EAAMI,IAAIK,QAAM,K,wBAIzB1H,EAAAA,EAAAA,IAUkB8G,GAAA,CAVD3B,MAAM,MAAMqC,MAAM,MAAMC,MAAM,U,CAClCV,SAAOC,EAAAA,EAAAA,IAOVC,GAPiB,CAEI,QAAnBA,EAAMI,IAAInH,WAA4C0H,IAA1BX,EAAMI,IAAIQ,c,WAD9CpI,EAAAA,EAAAA,IAMM,O,MAJJJ,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,cACEhB,GAAA0H,mBAAmBb,EAAMI,IAAIQ,iB,QAElCZ,EAAMI,IAAIQ,aAAW,K,wBAI9B7H,EAAAA,EAAAA,IAUkB8G,GAAA,CAVD3B,MAAM,KAAKqC,MAAM,MAAMC,MAAM,U,CACjCV,SAAOC,EAAAA,EAAAA,IAOVC,GAPiB,CAEfA,EAAMI,IAAIpB,Q,WADlBxG,EAAAA,EAAAA,IAMM,O,MAJJJ,OAAK+B,EAAAA,EAAAA,IAAA,CAAC,eACEhB,GAAA2H,mBAAmBd,EAAMI,IAAIpB,W,QAElCgB,EAAMI,IAAIpB,OAAK,K,0DAlDM1G,GAAAqG,eAAeC,SAASH,O,4JAiF5E,IACCH,KAAM,aACNyC,WAAY,CACTC,QAAO,KACPC,KAAI,QACJC,aAAY,gBACZC,cAAa,iBACbC,QAAO,WACPC,kBAAiB,qBACjBC,WAAU,cACVC,KAAI,QACJC,WAAU,cACVC,IAAG,OACHC,YAAWA,GAAAA,IAEdhC,IAAAA,GACC,MAAO,CACNnH,OAAQ,KACRG,OAAQ,KACR6F,eAAgB,GAChBX,cAAe,MACfe,eAAgB,GAChBpB,WAAY,CACX,CAAEW,MAAO,OAAQR,MAAO,MAAOO,KAAM,QACrC,CAAEC,MAAO,OAAQR,MAAO,UAAWO,KAAM,iBACzC,CAAEC,MAAO,OAAQR,MAAO,OAAQO,KAAM,WACtC,CAAEC,MAAO,OAAQR,MAAO,QAASO,KAAM,sBAG1C,EACA0D,SAAU,CACT3F,YAAAA,GACC,OAAK4F,KAAKlJ,QAAWkJ,KAAKlJ,OAAO+C,QAC1BmG,KAAKlJ,OAAO+C,QAAQoG,OAAOC,GAAqB,YAAdA,EAAI9C,OADI,EAElD,EACA1C,SAAAA,GACC,OAAKsF,KAAKlJ,QAAWkJ,KAAKlJ,OAAO+C,QAC1BmG,KAAKlJ,OAAO+C,QAAQoG,OAAOC,GAAqB,SAAdA,EAAI9C,OADI,EAElD,EACApC,UAAAA,GACC,OAAKgF,KAAKlJ,QAAWkJ,KAAKlJ,OAAO+C,QAC1BmG,KAAKlJ,OAAO+C,QAAQoG,OAAOC,GAAqB,UAAdA,EAAI9C,OADI,EAElD,EACA5E,gBAAAA,GACC,IAAKwH,KAAKrJ,SAAWqJ,KAAKrJ,OAAO+B,UAAW,MAAO,OACnD,MAAMyH,EAAOC,WAAWJ,KAAKrJ,OAAO+B,WACpC,OAAIyH,GAAQ,GAAW,YACnBA,GAAQ,GAAW,OACnBA,GAAQ,GAAW,OAChB,MACR,GAEDE,QAAS,CACR7I,MAAAA,GACC8I,OAAOC,QAAQC,MAChB,EACA,mBAAMC,CAAcxI,GACnB,IACCyI,QAAQC,IAAI,cAAe1I,GAC3B,MAAM2I,QAAiBZ,KAAKa,KAAKC,cAAc7I,GAC/CyI,QAAQC,IAAI,WAAYC,GAEA,MAApBA,EAASG,SAERH,EAAS9C,MAAQ8C,EAAS9C,KAAKjE,QAClCmG,KAAKlJ,OAAS8J,EAAS9C,KAGf8C,EAAS9C,MAAQ8C,EAAS9C,KAAKkD,OACvChB,KAAKlJ,OAAS8J,EAAS9C,KAAKkD,MAG7BN,QAAQC,IAAI,YAAaX,KAAKlJ,QAE1BkJ,KAAKlJ,QAAUkJ,KAAKlJ,OAAO+C,SAC9BmG,KAAK9D,mBAAmB,OACxBwE,QAAQC,IAAI,YAAaX,KAAKrD,kBAE9B+D,QAAQO,MAAM,aACdC,GAAAA,GAAUD,MAAM,cAGnB,CAAE,MAAOA,GACRP,QAAQO,MAAM,YAAaA,GAC3BC,GAAAA,GAAUD,MAAM,WACjB,CACD,EACA,mBAAME,CAAclJ,GACnB,IACCyI,QAAQC,IAAI,cAAe1I,GAC3B,MAAM2I,QAAiBZ,KAAKa,KAAKM,cAAclJ,GAC/CyI,QAAQC,IAAI,WAAYC,GAEA,MAApBA,EAASG,QAAkBH,EAAS9C,OACvCkC,KAAKrJ,OAASiK,EAAS9C,KACvB4C,QAAQC,IAAI,YAAaX,KAAKrJ,QAEhC,CAAE,MAAOsK,GACRP,QAAQO,MAAM,YAAaA,GAC3BC,GAAAA,GAAUD,MAAM,WACjB,CACD,EACAG,MAAAA,GACC,IAAKpB,KAAKlJ,SAAWkJ,KAAKqB,MAAMD,OAE/B,YADAV,QAAQC,IAAI,uBAIb,MAAM7E,EAAQ,CACbkE,KAAKlJ,OAAOwK,KAAO,EACnBtB,KAAKlJ,OAAOyK,SAAW,EACvBvB,KAAKlJ,OAAO0K,MAAQ,EACpBxB,KAAKlJ,OAAOmK,OAAS,GAEhB3E,EAAQ,CAAC,OAAQ,OAAQ,OAAQ,QACvCoE,QAAQC,IAAI,SAAU,CAAC7E,QAAOQ,UAC9B0D,KAAKyB,OAAOL,OAAOpB,KAAKqB,MAAMD,OAAQtF,EAAOQ,EAC9C,EACAoF,MAAAA,GACC,IAAK1B,KAAKlJ,SAAWkJ,KAAKqB,MAAMK,OAE/B,YADAhB,QAAQC,IAAI,uBAIb,MAAMgB,EAAQ,CACb,CAAE7F,MAAOkE,KAAKlJ,OAAOyK,SAAW,EAAG7E,KAAM,MACzC,CAAEZ,MAAOkE,KAAKlJ,OAAO0K,MAAQ,EAAG9E,KAAM,MACtC,CAAEZ,MAAOkE,KAAKlJ,OAAOmK,OAAS,EAAGvE,KAAM,OAExCgE,QAAQC,IAAI,SAAUgB,GACtB3B,KAAKyB,OAAOC,OAAO1B,KAAKqB,MAAMK,OAAQC,EACvC,EACAzF,kBAAAA,CAAmB7E,GAClB,IAAK2I,KAAKlJ,SAAWkJ,KAAKlJ,OAAO+C,UAAY+H,MAAMC,QAAQ7B,KAAKlJ,OAAO+C,SAGtE,OAFA6G,QAAQO,MAAM,uBACdjB,KAAKrD,eAAiB,IAIvBqD,KAAKhE,cAAgB3E,EACrBqJ,QAAQC,IAAI,UAAWtJ,GAEV,QAATA,EACH2I,KAAKrD,eAAiB,IAAIqD,KAAKlJ,OAAO+C,SACnB,YAATxC,EACV2I,KAAKrD,eAAiB,IAAIqD,KAAK5F,cACZ,SAAT/C,EACV2I,KAAKrD,eAAiB,IAAIqD,KAAKtF,WACZ,UAATrD,IACV2I,KAAKrD,eAAiB,IAAIqD,KAAKhF,aAGhC0F,QAAQC,IAAI,WAAYX,KAAKrD,eAAe7C,QAC5CkG,KAAKjD,eAAiB,EACvB,EACAE,WAAAA,CAAYJ,GACX,MAAMiF,EAAW9B,KAAKjD,eAAegF,QAAQlF,IAC3B,IAAdiF,EACH9B,KAAKjD,eAAeiF,KAAKnF,GAEzBmD,KAAKjD,eAAekF,OAAOH,EAAU,EAEvC,EACAtF,WAAAA,CAAYnF,GACX,OAAK2I,KAAKlJ,QAAWkJ,KAAKlJ,OAAO+C,QACpB,QAATxC,EAAuB2I,KAAKlJ,OAAO+C,QAAQC,OAClC,YAATzC,EAA2B2I,KAAK5F,aAAaN,OACpC,SAATzC,EAAwB2I,KAAKtF,UAAUZ,OACpCkG,KAAKhF,WAAWlB,OAJ0B,CAKlD,EACAqD,YAAAA,CAAaC,GACZ,MAAc,YAAVA,EAA4B,gBAClB,SAAVA,EAAyB,UACtB,mBACR,EACAG,kBAAAA,CAAmBH,GAClB,MAAO,SAASA,GACjB,EACAI,iBAAAA,CAAkBJ,GACjB,MAAM8E,EAAM,CACXX,QAAS,KACTC,KAAM,KACNP,MAAO,MAER,OAAOiB,EAAI9E,IAAUA,CACtB,EACAsB,eAAAA,CAAgBF,GACf,OAAKA,GAAQA,EAAInH,MACG,QAAbmH,EAAInH,KAAiB,aADE,KAE/B,EACA4H,kBAAAA,CAAmBkD,GAElB,OADAA,EAAOC,SAASD,GAAQ,GACpBA,GAAQ,KAAOA,EAAO,IAAY,UAClCA,GAAQ,KAAOA,EAAO,IAAY,WAClCA,GAAQ,KAAOA,EAAO,IAAY,eAC/B,cACR,EACAjD,kBAAAA,CAAmB9B,GAClB,OAAKA,EACS,OAAVA,EAAuB,UACb,OAAVA,EAAuB,OACpB,QAHY,OAIpB,GAED,aAAMiF,GACL,IACC3B,QAAQC,IAAI,QACZ,MAAM1I,EAAK+H,KAAKjI,OAAOC,OAAOC,GAC1BA,GACHyI,QAAQC,IAAI,QAAS1I,SACf+H,KAAKS,cAAcxI,SACnB+H,KAAKmB,cAAclJ,KAEzByI,QAAQO,MAAM,YACdC,GAAAA,GAAUD,MAAM,YAElB,CAAE,MAAOA,GACRP,QAAQO,MAAM,WAAYA,GAC1BC,GAAAA,GAAUD,MAAM,UACjB,CACD,EACAqB,OAAAA,GACKtC,KAAKqB,MAAMD,QAAQpB,KAAKoB,SACxBpB,KAAKqB,MAAMK,QAAQ1B,KAAK0B,QAC7B,EACAa,OAAAA,GACKvC,KAAKqB,MAAMD,QAAQpB,KAAKoB,SACxBpB,KAAKqB,MAAMK,QAAQ1B,KAAK0B,QAC7B,G,YCpbD,MAAMc,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASC,IAAQ,CAAC,YAAY,qBAEzF,S,0ICQUjM,MAAM,e,GAGPoC,MAAA,mB,aAsCApC,MAAM,e,SAM6BoC,MAAA,mB,SAOLA,MAAA,mB,SACKA,MAAA,mB,SAC3BA,MAAA,mB,SAK0BA,MAAA,mB,SAC1BA,MAAA,mB,SASRA,MAAA,0D,GAcIpC,MAAM,iB,0ZArGhBW,EAAAA,EAAAA,IAsFQuL,EAAA,CAtFC,cAAY,KAAK9J,MAAA,uBAA2BvB,KAAK,cAAcyE,MAAM,KAAKkC,KAAK,Q,kBACzF,IAYgB,CAZkB,OAAf2E,EAAApE,OAAOlH,O,WAA1B8E,EAAAA,EAAAA,IAYgByG,EAAA,C,MAZyBtG,MAAM,MAAMI,KAAK,M,kBACtD,IAUM,CAVKiG,EAAApE,OAAOsE,kB,WAAlBjM,EAAAA,EAAAA,IAUM,MAAAC,EAAA,CATO8L,EAAApE,OAAOsE,gBAAgB,gBAAgB7F,SAAS,sB,WAA3DpG,EAAAA,EAAAA,IAGM,MAAAG,EAAA,EADJI,EAAAA,EAAAA,IAA4F2L,EAAA,CAAnFC,UAAU,E,WAAeJ,EAAApE,OAAOyE,c,qCAAPL,EAAApE,OAAOyE,cAAa/G,GAAEgH,KAAK,OAAOC,MAAM,U,uCAE5EtM,EAAAA,EAAAA,IAIM,MAAAK,EAAA,EAHJE,EAAAA,EAAAA,IAEewB,EAAA,CAFDE,OAAO,QAAUsK,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAA0G,EAA1GlM,EAAAA,EAAAA,IAA0G2L,EAAA,CAAjGC,UAAU,EAAMO,UAAQX,EAAApE,OAAOyE,cAAeC,KAAK,OAAOC,MAAM,SAASrK,OAAO,S,6EAKjE,OAAf8J,EAAApE,OAAOlH,O,WAA1B8E,EAAAA,EAAAA,IAWcyG,EAAA,C,MAX2BtG,MAAM,MAAMI,KAAK,M,kBACtD,IASe,EATfvF,EAAAA,EAAAA,IASewB,EAAA,CATDE,OAAO,QAASsK,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAOI,CAP2BV,EAAApE,OAAOsE,kB,WAAtCjM,EAAAA,EAAAA,IAOI,MAPJM,EAOI,G,aANLN,EAAAA,EAAAA,IAKM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IALsBiH,EAAApE,OAAOsE,gBAAe,CAArC/G,EAAOD,M,WAApBjF,EAAAA,EAAAA,IAKM,aAJLO,EAAAA,EAAAA,IAGSoM,EAAA,CAHD3K,MAAA,qBAAyBvB,KAAK,Q,kBACrC,IAAgD,EAAhDL,EAAAA,EAAAA,IAAgD,IAAhDW,GAAgDE,EAAAA,EAAAA,IAAlBgE,EAAM,OAAH,IACjC7E,EAAAA,EAAAA,IAAwB,aAAAa,EAAAA,EAAAA,IAAfiE,GAAK,K,yEAMgB,OAAf6G,EAAApE,OAAOlH,O,WAA1B8E,EAAAA,EAAAA,IA4BcyG,EAAA,C,MA5B2BtG,MAAM,OAAOI,KAAK,M,kBACvD,IA0Be,EA1BfvF,EAAAA,EAAAA,IA0BewB,EAAA,CA1BDE,OAAO,QAASsK,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAwBI,CAxBOV,EAAApE,OAAOiF,gB,WAAlB5M,EAAAA,EAAAA,IAwBI,MAAAgB,EAAA,EAvBLT,EAAAA,EAAAA,IAsBcsM,EAAA,C,WAtBQ/M,EAAAgN,Y,qCAAAhN,EAAAgN,YAAWzH,GAAEzF,MAAM,e,kBACxC,IAMmB,EANnBW,EAAAA,EAAAA,IAMmBwM,EAAA,CANDjH,KAAK,KAAG,CACdkH,OAAKzF,EAAAA,EAAAA,IACf,IAAciF,EAAA,KAAAA,EAAA,KAAdpM,EAAAA,EAAAA,IAAc,SAAX,WAAO,M,iBAEX,IAA+C,EAA/CA,EAAAA,EAAAA,IAA+C,WAA1C,qBAAiBa,EAAAA,EAAAA,IAAG8K,EAAApE,OAAOM,QAAM,IACtC7H,EAAAA,EAAAA,IAAyC,WAApC,kBAAca,EAAAA,EAAAA,IAAG8K,EAAApE,OAAOsF,KAAG,K,OAEjC1M,EAAAA,EAAAA,IAOmBwM,EAAA,CAPDjH,KAAK,KAAG,CACdkH,OAAKzF,EAAAA,EAAAA,IACf,IAAsBiF,EAAA,MAAAA,EAAA,MAAtBpM,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEd,IAA8C,G,aAAnDJ,EAAAA,EAAAA,IAEM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFsBiH,EAAApE,OAAOuF,gBAAe,CAArChI,EAAOD,M,WAApBjF,EAAAA,EAAAA,IAEM,aADLI,EAAAA,EAAAA,IAAsC,aAAAa,EAAAA,EAAAA,IAA7BgE,EAAM,MAAQC,GAAK,O,eAG9B3E,EAAAA,EAAAA,IAKmBwM,EAAA,CALDjH,KAAK,KAAG,CACdkH,OAAKzF,EAAAA,EAAAA,IACf,IAAsBiF,EAAA,MAAAA,EAAA,MAAtBpM,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEnB,IAAuC,EAAvCA,EAAAA,EAAAA,IAAuC,aAAAa,EAAAA,EAAAA,IAA9B8K,EAAApE,OAAOiF,eAAa,K,oFAMjCrM,EAAAA,EAAAA,IAYcyL,EAAA,CAZDtG,MAAM,MAAI,C,iBACtB,IAUe,EAVfnF,EAAAA,EAAAA,IAUewB,EAAA,CAVDE,OAAO,QAASsK,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAQM,EARNrM,EAAAA,EAAAA,IAQM,MARNkB,EAQM,G,aAPLtB,EAAAA,EAAAA,IAMM6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANuBiH,EAAApE,OAAOwF,SAAQ,CAA/BC,EAAMnH,M,WAAnBjG,EAAAA,EAAAA,IAMM,YAL8C,UAAZoN,EAAK,K,WAA5C7H,EAAAA,EAAAA,IAAmFoH,EAAA,C,MAA3E3K,MAAA,sB,kBAAqD,IAAa,E,iBAAVoL,EAAK,IAAD,K,YACZ,YAAZA,EAAK,K,WAAjD7H,EAAAA,EAAAA,IAAwGoH,EAAA,C,MAAhG3K,MAAA,qBAA2DvB,KAAK,W,kBAAU,IAAa,E,iBAAV2M,EAAK,IAAD,K,YACjC,UAAZA,EAAK,K,WAAjD7H,EAAAA,EAAAA,IAAqGoH,EAAA,C,MAA7F3K,MAAA,qBAAyDvB,KAAK,U,kBAAS,IAAa,E,iBAAV2M,EAAK,IAAD,K,YAC9B,SAAZA,EAAK,K,WAAjD7H,EAAAA,EAAAA,IAAqGoH,EAAA,C,MAA7F3K,MAAA,qBAAwDvB,KAAK,W,kBAAU,IAAa,E,iBAAV2M,EAAK,IAAD,K,YAC1D,WAAZA,EAAK,K,WAArBpN,EAAAA,EAAAA,IAAiF,MAAjF0B,GAAiFT,EAAAA,EAAAA,IAAhBmM,EAAK,IAAD,K,4CAKzE7M,EAAAA,EAAAA,IAMcyL,EAAA,CANDqB,SAAA,IAAQ,CACT3H,OAAK6B,EAAAA,EAAAA,IACf,IAAkG,CAArE,OAAjBwE,EAAApE,OAAOnB,Q,WAAnBxG,EAAAA,EAAAA,IAAkG,OAAlG6B,GAAkGZ,EAAAA,EAAAA,IAAA,YAAtB8K,EAAApE,OAAOnB,OAAK,IACtD,OAAjBuF,EAAApE,OAAOnB,Q,WAAxBxG,EAAAA,EAAAA,IAAuG,OAAvGmC,GAAuGlB,EAAAA,EAAAA,IAAA,YAAtB8K,EAAApE,OAAOnB,OAAK,M,WAC7FxG,EAAAA,EAAAA,IAA8D,OAA9DoC,GAA8DnB,EAAAA,EAAAA,IAAtB8K,EAAApE,OAAOnB,OAAK,M,MAGpB,OAAfuF,EAAApE,OAAOlH,O,WAA1B8E,EAAAA,EAAAA,IAKcyG,EAAA,C,MAL2BqB,SAAA,I,CAC7B3H,OAAK6B,EAAAA,EAAAA,IACf,IAA4G,CAAhGwE,EAAApE,OAAOS,aAAe,M,WAAlCpI,EAAAA,EAAAA,IAA4G,OAA5GqC,GAA4GpB,EAAAA,EAAAA,IAAA,YAA5B8K,EAAApE,OAAOS,aAAW,M,WAClGpI,EAAAA,EAAAA,IAAkF,OAAlFsC,GAAkFrB,EAAAA,EAAAA,IAAA,YAA5B8K,EAAApE,OAAOS,aAAW,M,wBAG1E7H,EAAAA,EAAAA,IAIcyL,EAAA,CAJDqB,SAAA,IAAQ,CACT3H,OAAK6B,EAAAA,EAAAA,IACf,IAAiC,E,2BAAlBwE,EAAApE,OAAO2F,UAAQ,K,cAIuD,OAAjBvB,EAAApE,OAAOnB,OAAkBuF,EAAAwB,U,WAA7FvN,EAAAA,EAAAA,IAEM,MAFNuC,EAEM,EADJhC,EAAAA,EAAAA,IAAqFC,EAAA,CAAxEE,QAAOC,EAAA6M,cAAe/M,KAAK,UAAUgN,MAAA,GAAMrG,KAAK,Q,kBAAO,IAAKoF,EAAA,MAAAA,EAAA,M,QAAL,Y,gDAGtEjM,EAAAA,EAAAA,IAeYmN,EAAA,CAfDV,MAAM,Q,WAAiBlN,EAAA6N,U,qCAAA7N,EAAA6N,UAAStI,GAAE0C,MAAM,MAAO,eAAcpH,EAAAiN,mB,CAS3DC,QAAMtG,EAAAA,EAAAA,IACf,IAGM,EAHNnH,EAAAA,EAAAA,IAGM,MAHNqC,EAGM,EAFJlC,EAAAA,EAAAA,IAAqDC,EAAA,CAAzCE,QAAOC,EAAAiN,mBAAiB,C,iBAAE,IAAGpB,EAAA,MAAAA,EAAA,M,QAAH,U,6BACtCjM,EAAAA,EAAAA,IAA0DC,EAAA,CAA/CC,KAAK,UAAWC,QAAOC,EAAAmN,S,kBAAS,IAAGtB,EAAA,MAAAA,EAAA,M,QAAH,U,iDAX/C,IAOU,EAPVjM,EAAAA,EAAAA,IAOUwN,EAAA,CAPAC,MAAOlO,EAAAmO,SAAO,C,iBACtB,IAIe,EAJf1N,EAAAA,EAAAA,IAIe2N,EAAA,CAJDxI,MAAM,QAAM,C,iBACxB,IAEY,EAFZnF,EAAAA,EAAAA,IAEY4N,EAAA,CAFD/G,KAAK,Q,WAAiBtH,EAAAmO,QAAQG,U,qCAARtO,EAAAmO,QAAQG,UAAS/I,GAAEgJ,YAAY,WAAWrM,MAAA,gB,kBACT,IAA0B,G,aAA1FhC,EAAAA,EAAAA,IAAsH6E,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAAvChF,EAAAwO,WAARC,K,WAAvEhJ,EAAAA,EAAAA,IAAsHiJ,EAAA,CAA1G9I,MAAO6I,EAAKzI,KAAO,IAAMyI,EAAKtB,IAAM/H,MAAOqJ,EAAKlN,GAAgC4D,IAAKsJ,EAAKlN,I,oEAG1Gd,EAAAA,EAAAA,IAAiK2N,EAAA,CAAnJxI,MAAM,SAAO,C,iBAAC,IAAsH,EAAtHnF,EAAAA,EAAAA,IAAsHkO,EAAA,CAA3GC,SAAU,CAAAC,QAAA,EAAAC,QAAA,G,WAAqC9O,EAAAmO,QAAQY,K,qCAAR/O,EAAAmO,QAAQY,KAAIxJ,GAAE5E,KAAK,WAAWqO,aAAa,O,0HAczI,GACCC,MAAO,CACNpH,OAAQ,CACPL,QAAS,CAAC,GAEXiG,QAAS,CACRjG,SAAS,IAGX6B,SAAU,KACN6F,EAAAA,EAAAA,IAAS,CAAC,SAEdzG,WAAY,CACX0G,OAAMA,EAAAA,GAEP/H,IAAAA,GACC,MAAO,CACN4F,YAAa,CAAC,IAAK,IAAK,KAExBa,WAAW,EAEXM,QAAS,CACRG,UAAW,KACXS,KAAM,GACNzE,KAAM,GACND,OAAQ,OAENmE,WAAW,GAEhB,EACA7E,QAAS,CACR,aAAMqE,GACL1E,KAAK6E,QAAQiB,QAAU9F,KAAK+F,IAAI9N,GAChC+H,KAAK6E,QAAQ7D,KAAOhB,KAAKzB,OACzB,MAAMqC,QAAiBZ,KAAKa,KAAKmF,WAAWhG,KAAK6E,SACzB,MAApBjE,EAASG,SACZf,KAAKiG,SAAS,CACb5O,KAAM,UACN6O,QAAS,UACTC,SAAU,MAEXnG,KAAKuE,WAAY,EACjBvE,KAAK6E,QAAU,CACdG,UAAW,KACXS,KAAM,GACNzE,KAAM,GACND,OAAQ,OAGX,EAEEyD,iBAAAA,GACExE,KAAKuE,WAAY,EACjBvE,KAAK6E,QAAU,CAChBG,UAAW,KACXS,KAAM,GACNzE,KAAM,GACND,OAAQ,MAEP,EAGF,mBAAMqD,GACJ,MAAMxD,QAAiBZ,KAAKa,KAAKuF,mBACT,MAApBxF,EAASG,SACXf,KAAKkF,WAAatE,EAAS9C,KAC3BkC,KAAKuE,WAAY,EAErB,I,WChLJ,MAAM/B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,O", "sources": ["webpack://frontend-web/./src/views/Reports/Report.vue", "webpack://frontend-web/./src/views/Reports/Report.vue?4281", "webpack://frontend-web/./src/components/common/caseResult.vue", "webpack://frontend-web/./src/components/common/caseResult.vue?0f1a"], "sourcesContent": ["  <template>\r\n\t<div class=\"report-dashboard\" v-if=\"record\">\r\n\t\t<div v-if=\"report\" class=\"dashboard-container\">\r\n\t\t\t<!-- 顶部状态栏 -->\r\n\t\t\t<div class=\"status-bar\">\r\n\t\t\t\t<div class=\"status-left\">\r\n\t\t\t\t\t<el-button type=\"text\" class=\"back-btn\" @click=\"goBack\">\r\n\t\t\t\t\t\t<el-icon><Back /></el-icon>\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<div class=\"report-status\">\r\n\t\t\t\t\t\t<div class=\"status-title\">测试报告 #{{ $route.params.id }}</div>\r\n\t\t\t\t\t\t<div class=\"status-time\">{{ $tools.rTime(record.create_time) }}</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"status-right\">\r\n\t\t\t\t\t<div class=\"status-badge\" :class=\"getPassRateClass\">\r\n\t\t\t\t\t\t<span class=\"badge-label\">通过率</span>\r\n\t\t\t\t\t\t<span class=\"badge-value\">{{ record.pass_rate || 0 }}%</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n      <el-scrollbar :style=\"{ height: 'calc(100vh - 21vh)',minHeight: '700px'}\">\r\n        <!-- 主要内容区 -->\r\n        <div class=\"dashboard-content\">\r\n          <!-- 左侧数据概览 -->\r\n          <div class=\"overview-panel\">\r\n            <div class=\"overview-header\">\r\n              <h2>测试概览</h2>\r\n              <div class=\"task-info\">\r\n                <div class=\"task-name\">{{ record.plan_name || '未命名计划' }}</div>\r\n                <div class=\"env-tag\">{{ record.env_name || '未知环境' }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 统计卡片 -->\r\n            <div class=\"stat-cards\">\r\n              <div class=\"stat-card total\">\r\n                <div class=\"stat-icon\"><el-icon><DataAnalysis /></el-icon></div>\r\n                <div class=\"stat-info\">\r\n                  <div class=\"stat-value\">{{ report.results && report.results.length || 0 }}</div>\r\n                  <div class=\"stat-label\">场景总数</div>\r\n                </div>\r\n                <div class=\"stat-wave\"></div>\r\n              </div>\r\n              <div class=\"stat-card success\">\r\n                <div class=\"stat-icon\"><el-icon><SuccessFilled /></el-icon></div>\r\n                <div class=\"stat-info\">\r\n                  <div class=\"stat-value\">{{ successscent.length }}</div>\r\n                  <div class=\"stat-label\">通过场景</div>\r\n                </div>\r\n                <div class=\"stat-wave\"></div>\r\n              </div>\r\n              <div class=\"stat-card fail\">\r\n                <div class=\"stat-icon\"><el-icon><Warning /></el-icon></div>\r\n                <div class=\"stat-info\">\r\n                  <div class=\"stat-value\">{{ failscent.length }}</div>\r\n                  <div class=\"stat-label\">失败场景</div>\r\n                </div>\r\n                <div class=\"stat-wave\"></div>\r\n              </div>\r\n              <div class=\"stat-card error\">\r\n                <div class=\"stat-icon\"><el-icon><CircleCloseFilled /></el-icon></div>\r\n                <div class=\"stat-info\">\r\n                  <div class=\"stat-value\">{{ errorscent.length }}</div>\r\n                  <div class=\"stat-label\">错误场景</div>\r\n                </div>\r\n                <div class=\"stat-wave\"></div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 图表区域 -->\r\n            <div class=\"chart-section\">\r\n              <div class=\"chart-card\">\r\n                <div class=\"chart-header\">\r\n                  <h3>执行统计</h3>\r\n                  <div class=\"chart-legend\">\r\n                    <span class=\"legend-item success\">通过</span>\r\n                    <span class=\"legend-item fail\">失败</span>\r\n                    <span class=\"legend-item error\">错误</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"chart-content\" ref=\"chart1\"></div>\r\n              </div>\r\n              <div class=\"chart-card\">\r\n                <div class=\"chart-header\">\r\n                  <h3>结果分布</h3>\r\n                </div>\r\n                <div class=\"chart-content\" ref=\"chart2\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 右侧场景详情 -->\r\n          <div class=\"detail-panel\">\r\n            <div class=\"detail-header\">\r\n              <h2>测试场景</h2>\r\n              <div class=\"filter-tabs\">\r\n                <div\r\n                  v-for=\"tab in filterTabs\"\r\n                  :key=\"tab.value\"\r\n                  class=\"filter-tab\"\r\n                  :class=\"{ active: currentFilter === tab.value, [tab.value]: true }\"\r\n                  @click=\"handleFilterChange(tab.value)\"\r\n                >\r\n                  <el-icon class=\"tab-icon\"><component :is=\"tab.icon\" /></el-icon>\r\n                  <span>{{ tab.label }}</span>\r\n                  <span class=\"tab-count\">{{ getTabCount(tab.value) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <el-scrollbar class=\"scene-list\">\r\n              <transition-group name=\"scene-fade\">\r\n                <div\r\n                  v-for=\"(scent, index) in showScentDatas\"\r\n                  :key=\"scent.name + index\"\r\n                  class=\"scene-card\"\r\n                  :class=\"{ expanded: expandedScenes.includes(index) }\"\r\n                >\r\n                  <div class=\"scene-header\" @click=\"toggleScene(index)\">\r\n                    <div class=\"scene-status\">\r\n                      <el-icon><component :is=\"getSceneIcon(scent.state)\" /></el-icon>\r\n                    </div>\r\n                    <div class=\"scene-info\">\r\n                      <div class=\"scene-name\">{{ scent.name || '未命名场景' }}</div>\r\n                      <div class=\"scene-state\" :class=\"getSceneStateClass(scent.state)\">\r\n                        {{ getSceneStateText(scent.state) }}\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"scene-action\">\r\n                      <el-icon :class=\"{ rotate: expandedScenes.includes(index) }\"><ArrowRight /></el-icon>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"scene-detail\" v-show=\"expandedScenes.includes(index)\">\r\n                    <el-table\r\n                      :data=\"scent.cases || []\"\r\n                      class=\"case-table\"\r\n                      size=\"small\"\r\n                    >\r\n                      <el-table-column type=\"expand\">\r\n                        <template #default=\"scope\">\r\n                          <div class=\"case-detail\">\r\n                            <caseRes :result=\"scope.row\"></caseRes>\r\n                          </div>\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column label=\"用例名称\" min-width=\"200\">\r\n                        <template #default=\"scope\">\r\n                          <div class=\"case-name\">\r\n                            <el-icon><component :is=\"getCaseTypeIcon(scope.row)\" /></el-icon>\r\n                            <span>{{ scope.row.name || '未命名用例' }}</span>\r\n                          </div>\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column label=\"请求方法\" width=\"100\" align=\"center\">\r\n                        <template #default=\"scope\">\r\n                          <div\r\n                            v-if=\"scope.row.type === 'api' && scope.row.method\"\r\n                            class=\"method-badge\"\r\n                            :class=\"scope.row.method.toLowerCase()\"\r\n                          >\r\n                            {{ scope.row.method }}\r\n                          </div>\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column label=\"状态码\" width=\"100\" align=\"center\">\r\n                        <template #default=\"scope\">\r\n                          <div\r\n                            v-if=\"scope.row.type === 'api' && scope.row.status_cede !== undefined\"\r\n                            class=\"status-code\"\r\n                            :class=\"getStatusCodeClass(scope.row.status_cede)\"\r\n                          >\r\n                            {{ scope.row.status_cede }}\r\n                          </div>\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column label=\"结果\" width=\"100\" align=\"center\">\r\n                        <template #default=\"scope\">\r\n                          <div\r\n                            v-if=\"scope.row.state\"\r\n                            class=\"result-badge\"\r\n                            :class=\"getCaseResultClass(scope.row.state)\"\r\n                          >\r\n                            {{ scope.row.state }}\r\n                          </div>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                  </div>\r\n                </div>\r\n              </transition-group>\r\n            </el-scrollbar>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport caseRes from '../../components/common/caseResult.vue';\r\nimport { \r\n  Back, \r\n  DataAnalysis, \r\n  SuccessFilled, \r\n  Warning, \r\n  CircleCloseFilled, \r\n  ArrowRight, \r\n  Grid, \r\n  Connection, \r\n  Cpu\r\n} from '@element-plus/icons-vue';\r\nimport { ElMessage, ElScrollbar } from 'element-plus';\r\n\r\nexport default {\r\n\tname: 'TestReport',\r\n\tcomponents: { \r\n    caseRes,\r\n    Back,\r\n    DataAnalysis,\r\n    SuccessFilled,\r\n    Warning,\r\n    CircleCloseFilled,\r\n    ArrowRight,\r\n    Grid,\r\n    Connection,\r\n    Cpu,\r\n    ElScrollbar\r\n  },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\trecord: null,\r\n\t\t\treport: null,\r\n\t\t\tshowScentDatas: [],\r\n\t\t\tcurrentFilter: 'all',\r\n\t\t\texpandedScenes: [],\r\n\t\t\tfilterTabs: [\r\n\t\t\t\t{ label: '全部场景', value: 'all', icon: 'Grid' },\r\n\t\t\t\t{ label: '通过场景', value: 'success', icon: 'SuccessFilled' },\r\n\t\t\t\t{ label: '失败场景', value: 'fail', icon: 'Warning' },\r\n\t\t\t\t{ label: '错误场景', value: 'error', icon: 'CircleCloseFilled' }\r\n\t\t\t]\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\tsuccessscent() {\r\n\t\t\tif (!this.report || !this.report.results) return [];\r\n\t\t\treturn this.report.results.filter(val => val.state === 'success');\r\n\t\t},\r\n\t\tfailscent() {\r\n\t\t\tif (!this.report || !this.report.results) return [];\r\n\t\t\treturn this.report.results.filter(val => val.state === 'fail');\r\n\t\t},\r\n\t\terrorscent() {\r\n\t\t\tif (!this.report || !this.report.results) return [];\r\n\t\t\treturn this.report.results.filter(val => val.state === 'error');\r\n\t\t},\r\n\t\tgetPassRateClass() {\r\n\t\t\tif (!this.record || !this.record.pass_rate) return 'poor';\r\n\t\t\tconst rate = parseFloat(this.record.pass_rate);\r\n\t\t\tif (rate >= 90) return 'excellent';\r\n\t\t\tif (rate >= 70) return 'good';\r\n\t\t\tif (rate >= 50) return 'fair';\r\n\t\t\treturn 'poor';\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgoBack() {\r\n\t\t\twindow.history.back();\r\n\t\t},\r\n\t\tasync getReportInfo(id) {\r\n\t\t\ttry {\r\n\t\t\t\tconsole.log('获取报告信息, ID:', id);\r\n\t\t\t\tconst response = await this.$api.getTestReport(id);\r\n\t\t\t\tconsole.log('报告API响应:', response);\r\n\t\t\t\t\r\n\t\t\t\tif (response.status === 200) {\r\n\t\t\t\t\t// 如果数据是直接在response.data，而不是response.data.info\r\n\t\t\t\t\tif (response.data && response.data.results) {\r\n\t\t\t\t\t\tthis.report = response.data;\r\n\t\t\t\t\t} \r\n\t\t\t\t\t// 或者数据在response.data.info\r\n\t\t\t\t\telse if (response.data && response.data.info) {\r\n\t\t\t\t\t\tthis.report = response.data.info;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('处理后的报告数据:', this.report);\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (this.report && this.report.results) {\r\n\t\t\t\t\t\tthis.handleFilterChange('all');\r\n\t\t\t\t\t\tconsole.log('过滤后的场景数据:', this.showScentDatas);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('报告数据结构不正确');\r\n\t\t\t\t\t\tElMessage.error('报告数据结构不正确');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取报告信息失败:', error);\r\n\t\t\t\tElMessage.error('获取报告信息失败');\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync getRecordInfo(id) {\r\n\t\t\ttry {\r\n\t\t\t\tconsole.log('获取记录信息, ID:', id);\r\n\t\t\t\tconst response = await this.$api.getRecordInfo(id);\r\n\t\t\t\tconsole.log('记录API响应:', response);\r\n\t\t\t\t\r\n\t\t\t\tif (response.status === 200 && response.data) {\r\n\t\t\t\t\tthis.record = response.data;\r\n\t\t\t\t\tconsole.log('处理后的记录数据:', this.record);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取记录信息失败:', error);\r\n\t\t\t\tElMessage.error('获取记录信息失败');\r\n\t\t\t}\r\n\t\t},\r\n\t\tchart1() {\r\n\t\t\tif (!this.report || !this.$refs.chart1) {\r\n\t\t\t\tconsole.log('无法渲染图表1 - 数据或DOM不存在');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst value = [\r\n\t\t\t\tthis.report.all || 0, \r\n\t\t\t\tthis.report.success || 0, \r\n\t\t\t\tthis.report.fail || 0, \r\n\t\t\t\tthis.report.error || 0\r\n\t\t\t];\r\n\t\t\tconst label = ['用例总数', '通过用例', '失败用例', '错误用例'];\r\n\t\t\tconsole.log('图表1数据:', {value, label});\r\n\t\t\tthis.$chart.chart1(this.$refs.chart1, value, label);\r\n\t\t},\r\n\t\tchart2() {\r\n\t\t\tif (!this.report || !this.$refs.chart2) {\r\n\t\t\t\tconsole.log('无法渲染图表2 - 数据或DOM不存在');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst datas = [\r\n\t\t\t\t{ value: this.report.success || 0, name: '通过' },\r\n\t\t\t\t{ value: this.report.fail || 0, name: '失败' },\r\n\t\t\t\t{ value: this.report.error || 0, name: '错误' }\r\n\t\t\t];\r\n\t\t\tconsole.log('图表2数据:', datas);\r\n\t\t\tthis.$chart.chart2(this.$refs.chart2, datas);\r\n\t\t},\r\n\t\thandleFilterChange(type) {\r\n\t\t\tif (!this.report || !this.report.results || !Array.isArray(this.report.results)) {\r\n\t\t\t\tconsole.error('没有场景数据或数据格式不正确');\r\n\t\t\t\tthis.showScentDatas = [];\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.currentFilter = type;\r\n\t\t\tconsole.log('切换场景过滤:', type);\r\n\t\t\t\r\n\t\t\tif (type === 'all') {\r\n\t\t\t\tthis.showScentDatas = [...this.report.results];\r\n\t\t\t} else if (type === 'success') {\r\n\t\t\t\tthis.showScentDatas = [...this.successscent];\r\n\t\t\t} else if (type === 'fail') {\r\n\t\t\t\tthis.showScentDatas = [...this.failscent];\r\n\t\t\t} else if (type === 'error') {\r\n\t\t\t\tthis.showScentDatas = [...this.errorscent];\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconsole.log('过滤后场景数量:', this.showScentDatas.length);\r\n\t\t\tthis.expandedScenes = [];\r\n\t\t},\r\n\t\ttoggleScene(index) {\r\n\t\t\tconst position = this.expandedScenes.indexOf(index);\r\n\t\t\tif (position === -1) {\r\n\t\t\t\tthis.expandedScenes.push(index);\r\n\t\t\t} else {\r\n\t\t\t\tthis.expandedScenes.splice(position, 1);\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetTabCount(type) {\r\n\t\t\tif (!this.report || !this.report.results) return 0;\r\n\t\t\tif (type === 'all') return this.report.results.length;\r\n\t\t\tif (type === 'success') return this.successscent.length;\r\n\t\t\tif (type === 'fail') return this.failscent.length;\r\n\t\t\treturn this.errorscent.length;\r\n\t\t},\r\n\t\tgetSceneIcon(state) {\r\n\t\t\tif (state === 'success') return 'SuccessFilled';\r\n\t\t\tif (state === 'fail') return 'Warning';\r\n\t\t\treturn 'CircleCloseFilled';\r\n\t\t},\r\n\t\tgetSceneStateClass(state) {\r\n\t\t\treturn `state-${state}`;\r\n\t\t},\r\n\t\tgetSceneStateText(state) {\r\n\t\t\tconst map = {\r\n\t\t\t\tsuccess: '通过',\r\n\t\t\t\tfail: '失败',\r\n\t\t\t\terror: '错误'\r\n\t\t\t};\r\n\t\t\treturn map[state] || state;\r\n\t\t},\r\n\t\tgetCaseTypeIcon(row) {\r\n\t\t\tif (!row || !row.type) return 'Cpu';\r\n\t\t\treturn row.type === 'api' ? 'Connection' : 'Cpu';\r\n\t\t},\r\n\t\tgetStatusCodeClass(code) {\r\n\t\t\tcode = parseInt(code || 0);\r\n\t\t\tif (code >= 200 && code < 300) return 'success';\r\n\t\t\tif (code >= 300 && code < 400) return 'redirect';\r\n\t\t\tif (code >= 400 && code < 500) return 'client-error';\r\n\t\t\treturn 'server-error';\r\n\t\t},\r\n\t\tgetCaseResultClass(state) {\r\n\t\t\tif (!state) return 'error';\r\n\t\t\tif (state === '成功') return 'success';\r\n\t\t\tif (state === '失败') return 'fail';\r\n\t\t\treturn 'error';\r\n\t\t}\r\n\t},\r\n\tasync created() {\r\n\t\ttry {\r\n\t\t\tconsole.log('组件创建');\r\n\t\t\tconst id = this.$route.params.id;\r\n\t\t\tif (id) {\r\n\t\t\t\tconsole.log('报告ID:', id);\r\n\t\t\t\tawait this.getReportInfo(id);\r\n\t\t\t\tawait this.getRecordInfo(id);\r\n\t\t\t} else {\r\n\t\t\t\tconsole.error('没有找到报告ID');\r\n\t\t\t\tElMessage.error('没有找到报告ID');\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('初始化报告失败:', error);\r\n\t\t\tElMessage.error('初始化报告失败');\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tif (this.$refs.chart1) this.chart1();\r\n\t\tif (this.$refs.chart2) this.chart2();\r\n\t},\r\n\tupdated() {\r\n\t\tif (this.$refs.chart1) this.chart1();\r\n\t\tif (this.$refs.chart2) this.chart2();\r\n\t}\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.report-dashboard {\r\n\tmin-height: 100vh;\r\n\tbackground: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);\r\n\tpadding: 20px;\r\n}\r\n\r\n.dashboard-container {\r\n\tmax-width: 1800px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n/* 顶部状态栏 */\r\n.status-bar {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tbackground: rgba(255, 255, 255, 0.9);\r\n\tpadding: 16px 24px;\r\n\tborder-radius: 12px;\r\n\tmargin-bottom: 24px;\r\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\r\n\tbackdrop-filter: blur(10px);\r\n\tborder: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.status-left {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n}\r\n\r\n.back-btn {\r\n\tcolor: #606266;\r\n\tfont-size: 20px;\r\n\ttransition: all 0.3s;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 8px;\r\n}\r\n\r\n.back-btn:hover {\r\n\tcolor: #409eff;\r\n\ttransform: translateX(-2px);\r\n}\r\n\r\n.status-title {\r\n\tfont-size: 20px;\r\n\tfont-weight: 600;\r\n\tbackground: linear-gradient(45deg, #409eff, #36cfc9);\r\n\t-webkit-background-clip: text;\r\n\t-webkit-text-fill-color: transparent;\r\n}\r\n\r\n.status-time {\r\n\tfont-size: 14px;\r\n\tcolor: #909399;\r\n\tmargin-top: 4px;\r\n}\r\n\r\n.status-badge {\r\n\tpadding: 8px 16px;\r\n\tborder-radius: 20px;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.status-badge:hover {\r\n\ttransform: translateY(-2px);\r\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.status-badge.excellent { background: linear-gradient(45deg, #67c23a, #95d475); color: #fff; }\r\n.status-badge.good { background: linear-gradient(45deg, #409eff, #79bbff); color: #fff; }\r\n.status-badge.fair { background: linear-gradient(45deg, #e6a23c, #ebb563); color: #fff; }\r\n.status-badge.poor { background: linear-gradient(45deg, #f56c6c, #f78989); color: #fff; }\r\n\r\n.badge-label {\r\n\tfont-size: 12px;\r\n\topacity: 0.9;\r\n}\r\n\r\n.badge-value {\r\n\tfont-size: 20px;\r\n\tfont-weight: 600;\r\n\tmargin-top: 2px;\r\n}\r\n\r\n/* 主内容区布局 */\r\n.dashboard-content {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: 400px 1fr;\r\n\tgap: 24px;\r\n}\r\n\r\n/* 左侧概览面板 */\r\n.overview-panel {\r\n\tbackground: rgba(255, 255, 255, 0.9);\r\n\tborder-radius: 12px;\r\n\tpadding: 16px;\r\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\r\n\tbackdrop-filter: blur(10px);\r\n\tborder: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.overview-header {\r\n\tmargin-bottom: 16px;\r\n}\r\n\r\n.overview-header h2 {\r\n\tmargin: 0 0 12px 0;\r\n\tfont-size: 18px;\r\n\tcolor: #303133;\r\n}\r\n\r\n.task-info {\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\tpadding: 8px 12px;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.task-name {\r\n\tfont-size: 14px;\r\n\tfont-weight: 500;\r\n\tmargin-bottom: 6px;\r\n\tcolor: #303133;\r\n}\r\n\r\n.env-tag {\r\n\tdisplay: inline-block;\r\n\tpadding: 4px 8px;\r\n\tbackground: linear-gradient(45deg, #409eff, #36cfc9);\r\n\tcolor: #fff;\r\n\tborder-radius: 4px;\r\n\tfont-size: 12px;\r\n}\r\n\r\n/* 统计卡片 */\r\n.stat-cards {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(2, 1fr);\r\n\tgap: 12px;\r\n\tmargin-bottom: 16px;\r\n}\r\n\r\n.stat-card {\r\n\tposition: relative;\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\tborder-radius: 10px;\r\n\tpadding: 12px;\r\n\toverflow: hidden;\r\n\ttransition: all 0.3s;\r\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.stat-card:hover {\r\n\ttransform: translateY(-2px);\r\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stat-icon {\r\n\tfont-size: 20px;\r\n\tmargin-bottom: 8px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.stat-icon .el-icon {\r\n\tfont-size: 20px;\r\n}\r\n\r\n.stat-value {\r\n\tfont-size: 24px;\r\n\tfont-weight: 600;\r\n\tmargin-bottom: 2px;\r\n}\r\n\r\n.stat-label {\r\n\tfont-size: 12px;\r\n\tcolor: #909399;\r\n}\r\n\r\n.stat-wave {\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 4px;\r\n\tbackground: linear-gradient(90deg, transparent, currentColor, transparent);\r\n\tanimation: wave 2s infinite;\r\n}\r\n\r\n@keyframes wave {\r\n\t0% { transform: translateX(-100%); }\r\n\t100% { transform: translateX(100%); }\r\n}\r\n\r\n.stat-card.total { color: #409eff; }\r\n.stat-card.success { color: #67c23a; }\r\n.stat-card.fail { color: #e6a23c; }\r\n.stat-card.error { color: #f56c6c; }\r\n\r\n/* 图表区域 */\r\n.chart-card {\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\tborder-radius: 10px;\r\n\tpadding: 12px;\r\n\tmargin-bottom: 12px;\r\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.chart-card:hover {\r\n\ttransform: translateY(-2px);\r\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chart-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 12px;\r\n}\r\n\r\n.chart-header h3 {\r\n\tmargin: 0;\r\n\tfont-size: 14px;\r\n\tcolor: #303133;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.chart-legend {\r\n\tdisplay: flex;\r\n\tgap: 8px;\r\n}\r\n\r\n.legend-item {\r\n\tfont-size: 11px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tcolor: #606266;\r\n}\r\n\r\n.legend-item::before {\r\n\tcontent: '';\r\n\tdisplay: inline-block;\r\n\twidth: 6px;\r\n\theight: 6px;\r\n\tborder-radius: 50%;\r\n\tmargin-right: 3px;\r\n}\r\n\r\n.legend-item.success::before { background: #67c23a; }\r\n.legend-item.fail::before { background: #e6a23c; }\r\n.legend-item.error::before { background: #f56c6c; }\r\n\r\n.chart-content {\r\n\theight: 200px;\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n/* 右侧详情面板 */\r\n.detail-panel {\r\n\tbackground: rgba(255, 255, 255, 0.9);\r\n\tborder-radius: 12px;\r\n\tpadding: 20px;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\r\n\tbackdrop-filter: blur(10px);\r\n\tborder: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.detail-header {\r\n\tmargin-bottom: 24px;\r\n}\r\n\r\n.detail-header h2 {\r\n\tmargin: 0 0 16px 0;\r\n\tfont-size: 18px;\r\n\tcolor: #303133;\r\n}\r\n\r\n/* 过滤标签 */\r\n.filter-tabs {\r\n\tdisplay: flex;\r\n\tgap: 12px;\r\n\tmargin-bottom: 20px;\r\n}\r\n\r\n.filter-tab {\r\n\tflex: 1;\r\n\tpadding: 12px;\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\tborder-radius: 8px;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8px;\r\n\tcolor: #606266;\r\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.tab-icon {\r\n\tfont-size: 16px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.filter-tab::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 4px;\r\n\theight: 100%;\r\n\ttransition: all 0.3s;\r\n\topacity: 0.7;\r\n}\r\n\r\n.filter-tab.all::before {\r\n\tbackground: linear-gradient(to bottom, #409eff, #36cfc9);\r\n}\r\n\r\n.filter-tab.success::before {\r\n\tbackground: linear-gradient(to bottom, #67c23a, #95d475);\r\n}\r\n\r\n.filter-tab.fail::before {\r\n\tbackground: linear-gradient(to bottom, #e6a23c, #ebb563);\r\n}\r\n\r\n.filter-tab.error::before {\r\n\tbackground: linear-gradient(to bottom, #f56c6c, #f78989);\r\n}\r\n\r\n.filter-tab:hover {\r\n\tbackground: rgba(255, 255, 255, 0.9);\r\n\ttransform: translateY(-2px);\r\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.filter-tab:hover::before {\r\n\twidth: 6px;\r\n}\r\n\r\n.filter-tab.active {\r\n\tbackground: #fff;\r\n\tpadding-left: 16px;\r\n}\r\n\r\n.filter-tab.active::before {\r\n\twidth: 8px;\r\n}\r\n\r\n.filter-tab.active.all {\r\n\tcolor: #409eff;\r\n\tborder: 1px solid rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n.filter-tab.active.success {\r\n\tcolor: #67c23a;\r\n\tborder: 1px solid rgba(103, 194, 58, 0.2);\r\n}\r\n\r\n.filter-tab.active.fail {\r\n\tcolor: #e6a23c;\r\n\tborder: 1px solid rgba(230, 162, 60, 0.2);\r\n}\r\n\r\n.filter-tab.active.error {\r\n\tcolor: #f56c6c;\r\n\tborder: 1px solid rgba(245, 108, 108, 0.2);\r\n}\r\n\r\n.filter-tab i {\r\n\tfont-size: 16px;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.filter-tab:hover i {\r\n\ttransform: scale(1.1);\r\n}\r\n\r\n.filter-tab.active i {\r\n\ttransform: scale(1.2);\r\n}\r\n\r\n.tab-count {\r\n\tmargin-left: auto;\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\tpadding: 4px 10px;\r\n\tborder-radius: 12px;\r\n\tfont-size: 12px;\r\n\tfont-weight: bold;\r\n\ttransition: all 0.3s;\r\n\tmin-width: 24px;\r\n\ttext-align: center;\r\n}\r\n\r\n.filter-tab.active .tab-count {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.filter-tab.active.all .tab-count {\r\n\tbackground: rgba(64, 158, 255, 0.1);\r\n\tcolor: #409eff;\r\n}\r\n\r\n.filter-tab.active.success .tab-count {\r\n\tbackground: rgba(103, 194, 58, 0.1);\r\n\tcolor: #67c23a;\r\n}\r\n\r\n.filter-tab.active.fail .tab-count {\r\n\tbackground: rgba(230, 162, 60, 0.1);\r\n\tcolor: #e6a23c;\r\n}\r\n\r\n.filter-tab.active.error .tab-count {\r\n\tbackground: rgba(245, 108, 108, 0.1);\r\n\tcolor: #f56c6c;\r\n}\r\n\r\n/* 场景列表 */\r\n.scene-list {\r\n\theight: calc(100vh - 26vh);\r\n\tmin-height: 400px;\r\n\tpadding-right: 8px;\r\n}\r\n\r\n.scene-card {\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\tborder-radius: 8px;\r\n\tmargin-bottom: 12px;\r\n\ttransition: all 0.3s, max-height 0.5s ease;\r\n\tbox-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n\toverflow: hidden;\r\n}\r\n\r\n.scene-card:hover {\r\n\ttransform: translateX(4px);\r\n\tbox-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.scene-header {\r\n\tpadding: 16px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 16px;\r\n\tcursor: pointer;\r\n}\r\n\r\n.scene-status {\r\n\tfont-size: 20px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.scene-status .el-icon {\r\n\tfont-size: 20px;\r\n}\r\n\r\n.scene-info {\r\n\tflex: 1;\r\n}\r\n\r\n.scene-name {\r\n\tfont-weight: 500;\r\n\tmargin-bottom: 4px;\r\n\tcolor: #303133;\r\n}\r\n\r\n.scene-state {\r\n\tfont-size: 12px;\r\n\tpadding: 2px 8px;\r\n\tborder-radius: 4px;\r\n\tdisplay: inline-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.state-success { background: rgba(103, 194, 58, 0.1); color: #67c23a; }\r\n.state-fail { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }\r\n.state-error { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }\r\n\r\n.scene-action {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.scene-action .el-icon {\r\n\ttransition: transform 0.3s;\r\n\tcolor: #909399;\r\n}\r\n\r\n.scene-action .el-icon.rotate {\r\n\ttransform: rotate(90deg);\r\n}\r\n\r\n.scene-detail {\r\n\tpadding: 0 16px 16px;\r\n\toverflow: hidden;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n/* 用例表格样式 */\r\n.case-table {\r\n\tbackground: transparent !important;\r\n}\r\n\r\n.case-table :deep(.el-table__expanded-cell) {\r\n\tpadding: 10px 20px;\r\n\tbackground: rgba(250, 250, 250, 0.5);\r\n}\r\n\r\n.case-name {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8px;\r\n\tcolor: #303133;\r\n}\r\n\r\n.case-name .el-icon {\r\n\tfont-size: 16px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.method-badge {\r\n\tpadding: 4px 8px;\r\n\tborder-radius: 4px;\r\n\tfont-size: 12px;\r\n\tfont-weight: 500;\r\n\tdisplay: inline-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.method-badge.get { background: rgba(103, 194, 58, 0.1); color: #67c23a; }\r\n.method-badge.post { background: rgba(64, 158, 255, 0.1); color: #409eff; }\r\n.method-badge.put { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }\r\n.method-badge.delete { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }\r\n\r\n.status-code {\r\n\tpadding: 4px 8px;\r\n\tborder-radius: 4px;\r\n\tfont-size: 12px;\r\n\tdisplay: inline-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.status-code.success { background: rgba(103, 194, 58, 0.1); color: #67c23a; }\r\n.status-code.redirect { background: rgba(64, 158, 255, 0.1); color: #409eff; }\r\n.status-code.client-error { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }\r\n.status-code.server-error { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }\r\n\r\n.result-badge {\r\n\tpadding: 4px 8px;\r\n\tborder-radius: 4px;\r\n\tfont-size: 12px;\r\n\tdisplay: inline-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.result-badge.success { background: rgba(103, 194, 58, 0.1); color: #67c23a; }\r\n.result-badge.fail { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }\r\n.result-badge.error { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }\r\n\r\n/* 动画效果 */\r\n.scene-fade-enter-active, .scene-fade-leave-active {\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.scene-fade-enter-from, .scene-fade-leave-to {\r\n\topacity: 0;\r\n\ttransform: translateY(10px);\r\n}\r\n\r\n/* 展开/收起效果 */\r\n.scene-detail {\r\n  overflow: hidden;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.scene-card {\r\n  transition: all 0.3s, max-height 0.5s ease;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 滚动条样式 */\r\n::-webkit-scrollbar {\r\n\twidth: 6px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n\tbackground: rgba(255, 255, 255, 0.1);\r\n\tborder-radius: 3px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n\tbackground: rgba(144, 147, 153, 0.3);\r\n\tborder-radius: 3px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n\tbackground: rgba(144, 147, 153, 0.5);\r\n}\r\n\r\n/* 自定义 Element Plus 表格样式 */\r\n:deep(.el-table) {\r\n  --el-table-header-bg-color: rgba(255, 255, 255, 0.6);\r\n  --el-table-row-hover-bg-color: rgba(255, 255, 255, 0.8);\r\n  background: transparent;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n:deep(.el-table__header-wrapper),\r\n:deep(.el-table__body-wrapper) {\r\n  background: transparent;\r\n}\r\n\r\n:deep(.el-table th) {\r\n  background-color: var(--el-table-header-bg-color);\r\n  font-weight: 600;\r\n  color: #303133;\r\n  padding: 10px 0;\r\n}\r\n\r\n:deep(.el-table td) {\r\n  padding: 10px 0;\r\n}\r\n\r\n:deep(.el-table .el-table__cell) {\r\n  vertical-align: middle;\r\n}\r\n</style>\r\n", "import { render } from \"./Report.vue?vue&type=template&id=cf72507a&scoped=true\"\nimport script from \"./Report.vue?vue&type=script&lang=js\"\nexport * from \"./Report.vue?vue&type=script&lang=js\"\n\nimport \"./Report.vue?vue&type=style&index=0&id=cf72507a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-cf72507a\"]])\n\nexport default __exports__", "<template>\n\t  <el-tabs model-value=\"rb\" style=\"min-height: 300px;\" type=\"border-card\" value=\"rb\" size=\"mini\">\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应体\" name=\"rb\">\n      <div v-if=\"result.response_header\">\n        <div v-if=\"result.response_header['Content-Type'].includes('application/json')\">\n          <!-- 如果 Content-Type 是 application/json，渲染 JSON 格式的 Editor -->\n          <Editor :readOnly=\"true\" v-model=\"result.response_body\" lang=\"json\" theme=\"chrome\"></Editor>\n        </div>\n        <div v-else>\n          <el-scrollbar height=\"400px\"  @wheel.stop>\n            <Editor :readOnly=\"true\" v-html=\"result.response_body\" lang=\"html\" theme=\"chrome\" height=\"400px\"></Editor>\n          </el-scrollbar>\n        </div>\n      </div>\n    </el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应头\" name=\"rh\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div class=\"tab-box-sli\" v-if=\"result.response_header\">\n\t\t\t\t<div v-for=\"(value, key) in result.response_header\">\n\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" type=\"info\">\n\t\t\t\t\t\t<b style=\"color: #747474;\">{{ key + ' : ' }}</b>\n\t\t\t\t\t\t<span>{{ value }}</span>\n\t\t\t\t\t</el-tag>\n\t\t\t\t</div>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"请求信息\" name=\"rq\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div v-if=\"result.requests_body\">\n\t\t\t\t<el-collapse v-model=\"activeNames\" class=\"tab-box-sli\">\n\t\t\t\t\t<el-collapse-item name=\"1\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>General</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div>Request Method : {{ result.method }}</div>\n\t\t\t\t\t\t<div>Request URL : {{ result.url }}</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"2\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Headers</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div v-for=\"(value, key) in result.requests_header\">\n\t\t\t\t\t\t\t<span>{{ key + ' : ' + value }}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"3\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Payload</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<span>{{ result.requests_body }}</span>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t</el-collapse>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane label=\"日志\">\n\t\t\t<el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t\t<div class=\"tab-box-sli\">\n\t\t\t\t\t<div v-for=\"(item, index) in result.log_data\">\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-if=\"item[0] === 'DEBUG'\" >{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'WARNING'\" type=\"warning\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'ERROR'\" type=\"danger\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'INFO'\" type=\"success\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<pre v-else-if=\"item[0] === 'EXCEPT'\" style=\"color: #d60000;\">{{ item[1] }}</pre>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.state === '成功'\" style=\"color: #00AA7F;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else-if=\"result.state === '失败'\" style=\"color: #d18d17;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else style=\"color: #ff0000;\">{{ result.state }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.status_cede <= 300\" style=\"color: #00AA7F;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t\t<span v-else style=\"color: #ff5500;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t{{ 'Time : ' + result.run_time }}\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t</el-tabs>\n    <div style=\"margin-top: 10px;width: 100%;text-align: center;\" v-if=\"result.state === '失败' && showbtn\">\n      <el-button  @click=\"getInterfaces\" type=\"success\" plain size=\"mini\">提交bug</el-button>\n    </div>\n    <!-- 添加bug的弹框 -->\n    <el-dialog title=\"提交bug\" v-model=\"addBugDlg\" width=\"40%\" :before-close=\"closeDialogResult\">\n      <el-form :model=\"bugForm\">\n        <el-form-item label=\"所属接口\">\n          <el-select size=\"small\" v-model=\"bugForm.interface\" placeholder=\"bug对应的接口\" style=\"width: 100%;\">\n            <el-option :label=\"iter.name + ' ' + iter.url\" :value=\"iter.id\" v-for=\"iter in interfaces\" :key=\"iter.id\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"bug描述\"><el-input :autosize=\"{ minRows: 3, maxRows: 4 }\" v-model=\"bugForm.desc\" type=\"textarea\" autocomplete=\"off\"></el-input></el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"closeDialogResult\">取 消</el-button>\n          <el-button type=\"success\" @click=\"saveBug\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n</template>\n\n<script>\nimport Editor from './Editor.vue';\nimport { mapState } from 'vuex';\nexport default {\n\tprops: {\n\t\tresult: {\n\t\t\tdefault: {}\n\t\t},\n\t\tshowbtn: {\n\t\t\tdefault: true\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState(['pro'])\n\t},\n\tcomponents: {\n\t\tEditor\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveNames: ['1', '2', '3'],\n\t\t\t// 提交bug的显示窗口\n\t\t\taddBugDlg: false,\n\t\t\t// 添加bug的表单\n\t\t\tbugForm: {\n\t\t\t\tinterface: null,\n\t\t\t\tdesc: '',\n\t\t\t\tinfo: '',\n\t\t\t\tstatus: '待处理'\n\t\t\t},\n      interfaces:[]\n\t\t};\n\t},\n\tmethods: {\n\t\tasync saveBug() {\n\t\t\tthis.bugForm.project = this.pro.id;\n\t\t\tthis.bugForm.info = this.result;\n\t\t\tconst response = await this.$api.createBugs(this.bugForm);\n\t\t\tif (response.status === 201) {\n\t\t\t\tthis.$message({\n\t\t\t\t\ttype: 'success',\n\t\t\t\t\tmessage: 'bug提交成功',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\tthis.addBugDlg = false;\n\t\t\t\tthis.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n\t\t\t}\n\t\t},\n    // 取消按钮时重置输入信息\n    closeDialogResult() {\n      this.addBugDlg = false;\n      this.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n      },\n\n    // 获取接口列表\n    async getInterfaces() {\n      const response = await this.$api.getNewInterfaces();\n      if (response.status === 200) {\n        this.interfaces = response.data\n        this.addBugDlg = true\n      }\n    }\n\t}\n};\n</script>\n\n<style></style>\n", "import { render } from \"./caseResult.vue?vue&type=template&id=3a14eb2a\"\nimport script from \"./caseResult.vue?vue&type=script&lang=js\"\nexport * from \"./caseResult.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__"], "names": ["class", "ref", "$data", "record", "_createElementBlock", "_hoisted_1", "report", "_hoisted_2", "_createElementVNode", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_button", "type", "onClick", "$options", "goBack", "_component_el_icon", "_component_Back", "_hoisted_5", "_hoisted_6", "_toDisplayString", "_ctx", "$route", "params", "id", "_hoisted_7", "$tools", "rTime", "create_time", "_hoisted_8", "_normalizeClass", "getPassRateClass", "_hoisted_9", "pass_rate", "_component_el_scrollbar", "style", "height", "minHeight", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "plan_name", "_hoisted_15", "env_name", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_component_DataAnalysis", "_hoisted_19", "_hoisted_20", "results", "length", "_hoisted_21", "_hoisted_22", "_component_SuccessFilled", "_hoisted_23", "_hoisted_24", "successscent", "_hoisted_25", "_hoisted_26", "_component_Warning", "_hoisted_27", "_hoisted_28", "failscent", "_hoisted_29", "_hoisted_30", "_component_CircleCloseFilled", "_hoisted_31", "_hoisted_32", "errorscent", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_Fragment", "_renderList", "filterTabs", "tab", "key", "value", "active", "currentFilter", "$event", "handleFilterChange", "_createBlock", "_resolveDynamicComponent", "icon", "label", "_hoisted_42", "getTabCount", "_TransitionGroup", "name", "showScentDatas", "scent", "index", "expanded", "expandedScenes", "includes", "toggleScene", "_hoisted_44", "getSceneIcon", "state", "_hoisted_45", "_hoisted_46", "getSceneStateClass", "getSceneStateText", "_hoisted_47", "rotate", "_component_ArrowRight", "_hoisted_48", "_component_el_table", "data", "cases", "size", "_component_el_table_column", "default", "_withCtx", "scope", "_hoisted_49", "_component_caseRes", "result", "row", "_hoisted_50", "getCaseTypeIcon", "width", "align", "method", "toLowerCase", "undefined", "status_cede", "getStatusCodeClass", "getCaseResultClass", "components", "caseRes", "Back", "DataAnalysis", "SuccessFilled", "Warning", "CircleCloseFilled", "ArrowRight", "Grid", "Connection", "Cpu", "ElScrollbar", "computed", "this", "filter", "val", "rate", "parseFloat", "methods", "window", "history", "back", "getReportInfo", "console", "log", "response", "$api", "getTestReport", "status", "info", "error", "ElMessage", "getRecordInfo", "chart1", "$refs", "all", "success", "fail", "$chart", "chart2", "datas", "Array", "isArray", "position", "indexOf", "push", "splice", "map", "code", "parseInt", "created", "mounted", "updated", "__exports__", "render", "_component_el_tabs", "$props", "_component_el_tab_pane", "response_header", "_component_Editor", "readOnly", "response_body", "lang", "theme", "onWheel", "_cache", "_withModifiers", "innerHTML", "_component_el_tag", "requests_body", "_component_el_collapse", "activeNames", "_component_el_collapse_item", "title", "url", "requests_header", "log_data", "item", "disabled", "run_time", "showbtn", "getInterfaces", "plain", "_component_el_dialog", "addBugDlg", "closeDialogResult", "footer", "saveBug", "_component_el_form", "model", "bugForm", "_component_el_form_item", "_component_el_select", "interface", "placeholder", "interfaces", "iter", "_component_el_option", "_component_el_input", "autosize", "minRows", "maxRows", "desc", "autocomplete", "props", "mapState", "Editor", "project", "pro", "createBugs", "$message", "message", "duration", "getNewInterfaces"], "sourceRoot": ""}