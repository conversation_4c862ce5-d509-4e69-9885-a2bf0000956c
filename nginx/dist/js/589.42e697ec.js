"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[589],{56589:function(e,t,a){a.r(t),a.d(t,{default:function(){return Z}});a(44114);var s=a(56768),l=a(24232);const n={class:"dashboard-container"},r={class:"stat-card primary-gradient"},i={class:"stat-icon"},o={class:"stat-content"},d={class:"stat-value"},c={class:"stat-card success-gradient"},u={class:"stat-icon"},g={class:"stat-content"},k={class:"stat-value"},h={class:"stat-card warning-gradient"},b={class:"stat-icon"},p={class:"stat-content"},_={class:"stat-value"},f={class:"stat-card info-gradient"},m={class:"stat-icon"},v={class:"stat-content"},w={class:"stat-value"},L={class:"chart-filter-container"},F={class:"chart-card"},D={class:"card-header"},C={class:"time-selector"},y={class:"chart-wrapper",ref:"chart_box"},T={class:"filter-card"},W={class:"filter-content"},x={class:"filter-buttons"},S={class:"table-card"},R={class:"card-header with-border"},$={class:"header-actions"},H={class:"time-cell"},A={class:"user-cell"},V={class:"plan-cell"},Y={key:0,class:"running-status"},z={key:1,class:"result-container"},X={class:"progress-bar"},M={class:"result-stats"},P={class:"stat-item pass"},E={class:"stat-item total"},U={class:"action-buttons"},N={key:1,class:"running-badge"};function Q(e,t,a,Q,G,I){const O=(0,s.g2)("DataLine"),j=(0,s.g2)("el-icon"),q=(0,s.g2)("Check"),B=(0,s.g2)("Loading"),J=(0,s.g2)("PieChart"),K=(0,s.g2)("el-radio-button"),Z=(0,s.g2)("el-radio-group"),ee=(0,s.g2)("el-date-picker"),te=(0,s.g2)("el-form-item"),ae=(0,s.g2)("Refresh"),se=(0,s.g2)("el-button"),le=(0,s.g2)("Search"),ne=(0,s.g2)("el-form"),re=(0,s.g2)("Download"),ie=(0,s.g2)("el-table-column"),oe=(0,s.g2)("Clock"),de=(0,s.g2)("el-avatar"),ce=(0,s.g2)("el-tag"),ue=(0,s.g2)("User"),ge=(0,s.g2)("AlarmClock"),ke=(0,s.g2)("QuestionFilled"),he=(0,s.g2)("Tickets"),be=(0,s.g2)("el-progress"),pe=(0,s.g2)("Document"),_e=(0,s.g2)("View"),fe=(0,s.g2)("el-table"),me=(0,s.g2)("el-scrollbar"),ve=(0,s.gN)("loading");return(0,s.uX)(),(0,s.Wv)(me,{height:"97vh"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",n,[(0,s.Lk)("div",{class:(0,l.C4)(["stats-container",{"stats-loaded":!G.isLoading}])},[(0,s.Lk)("div",r,[(0,s.Lk)("div",i,[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(O)]),_:1})]),(0,s.Lk)("div",o,[(0,s.Lk)("div",d,(0,l.v_)(I.recordsCount),1),t[2]||(t[2]=(0,s.Lk)("div",{class:"stat-title"},"总执行次数",-1))])]),(0,s.Lk)("div",c,[(0,s.Lk)("div",u,[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(q)]),_:1})]),(0,s.Lk)("div",g,[(0,s.Lk)("div",k,(0,l.v_)(I.totalSuccess),1),t[3]||(t[3]=(0,s.Lk)("div",{class:"stat-title"},"通过用例",-1))])]),(0,s.Lk)("div",h,[(0,s.Lk)("div",b,[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(B)]),_:1})]),(0,s.Lk)("div",p,[(0,s.Lk)("div",_,(0,l.v_)(I.runningCount),1),t[4]||(t[4]=(0,s.Lk)("div",{class:"stat-title"},"执行中",-1))])]),(0,s.Lk)("div",f,[(0,s.Lk)("div",m,[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(J)]),_:1})]),(0,s.Lk)("div",v,[(0,s.Lk)("div",w,(0,l.v_)(I.averagePassRate)+"%",1),t[5]||(t[5]=(0,s.Lk)("div",{class:"stat-title"},"平均通过率",-1))])])],2),(0,s.Lk)("div",L,[(0,s.Lk)("div",F,[(0,s.Lk)("div",D,[t[11]||(t[11]=(0,s.Lk)("h3",null,"测试通过率趋势",-1)),(0,s.Lk)("div",C,[(0,s.bF)(Z,{modelValue:G.timeRange,"onUpdate:modelValue":t[0]||(t[0]=e=>G.timeRange=e),size:"small",onChange:I.handleTimeRangeChange},{default:(0,s.k6)(()=>[(0,s.bF)(K,{label:"day"},{default:(0,s.k6)(()=>t[6]||(t[6]=[(0,s.eW)("当天")])),_:1,__:[6]}),(0,s.bF)(K,{label:"day3"},{default:(0,s.k6)(()=>t[7]||(t[7]=[(0,s.eW)("近3天")])),_:1,__:[7]}),(0,s.bF)(K,{label:"week"},{default:(0,s.k6)(()=>t[8]||(t[8]=[(0,s.eW)("近7天")])),_:1,__:[8]}),(0,s.bF)(K,{label:"month"},{default:(0,s.k6)(()=>t[9]||(t[9]=[(0,s.eW)("近30天")])),_:1,__:[9]}),(0,s.bF)(K,{label:"all"},{default:(0,s.k6)(()=>t[10]||(t[10]=[(0,s.eW)("全部")])),_:1,__:[10]})]),_:1},8,["modelValue","onChange"])])]),(0,s.Lk)("div",y,null,512)]),(0,s.Lk)("div",T,[t[14]||(t[14]=(0,s.Lk)("div",{class:"card-header"},[(0,s.Lk)("h3",null,"筛选条件")],-1)),(0,s.Lk)("div",W,[(0,s.bF)(ne,{"label-position":"top",size:"small"},{default:(0,s.k6)(()=>[(0,s.bF)(te,{label:"执行时间",class:"date-range-item"},{default:(0,s.k6)(()=>[(0,s.bF)(ee,{modelValue:G.dataTime,"onUpdate:modelValue":t[1]||(t[1]=e=>G.dataTime=e),type:"datetimerange","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss","default-time":G.defaultTimeOptions,shortcuts:G.shortcuts,"range-separator":"至",clearable:!1,class:"date-picker",style:{maxWidth:"100%"}},null,8,["modelValue","default-time","shortcuts"])]),_:1}),(0,s.Lk)("div",x,[(0,s.bF)(se,{plain:"",onClick:I.clearData},{default:(0,s.k6)(()=>[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(ae)]),_:1}),t[12]||(t[12]=(0,s.eW)("重置 "))]),_:1,__:[12]},8,["onClick"]),(0,s.bF)(se,{type:"primary",onClick:I.submitForm},{default:(0,s.k6)(()=>[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(le)]),_:1}),t[13]||(t[13]=(0,s.eW)("查询 "))]),_:1,__:[13]},8,["onClick"])])]),_:1})])])]),(0,s.Lk)("div",S,[(0,s.Lk)("div",R,[t[16]||(t[16]=(0,s.Lk)("h3",null,"执行记录",-1)),(0,s.Lk)("div",$,[(0,s.bF)(se,{size:"small",type:"primary",plain:"",onClick:I.exportData},{default:(0,s.k6)(()=>[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(re)]),_:1}),t[15]||(t[15]=(0,s.eW)("导出数据 "))]),_:1,__:[15]},8,["onClick"])])]),(0,s.bo)(((0,s.uX)(),(0,s.Wv)(fe,{data:G.records,"element-loading-text":"正在加载数据...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(255, 255, 255, 0.8)",class:"custom-table","header-cell-style":{background:"#f8faff",color:"#606266",fontWeight:"600"},border:""},{default:(0,s.k6)(()=>[(0,s.bF)(ie,{type:"index",label:"序号",width:"60",align:"center"}),(0,s.bF)(ie,{label:"执行时间",align:"center","min-width":"160"},{default:(0,s.k6)(t=>[(0,s.Lk)("div",H,[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(oe)]),_:1}),(0,s.Lk)("span",null,(0,l.v_)(e.$tools.rTime(t.row.create_time)),1)])]),_:1}),(0,s.bF)(ie,{label:"执行人",align:"center","min-width":"110"},{default:(0,s.k6)(e=>[(0,s.Lk)("div",A,[(0,s.bF)(de,{size:24,class:"user-avatar"},{default:(0,s.k6)(()=>[(0,s.eW)((0,l.v_)(e.row.tester.substring(0,1)),1)]),_:2},1024),(0,s.Lk)("span",null,(0,l.v_)(e.row.tester),1)])]),_:1}),(0,s.bF)(ie,{label:"环境",align:"center","min-width":"110"},{default:(0,s.k6)(e=>[(0,s.bF)(ce,{effect:"dark",type:I.getEnvType(e.row.env_name),class:"env-tag"},{default:(0,s.k6)(()=>[(0,s.eW)((0,l.v_)(e.row.env_name),1)]),_:2},1032,["type"])]),_:1}),(0,s.bF)(ie,{label:"执行类型",align:"center","min-width":"120"},{default:(0,s.k6)(e=>["手动执行"===e.row.execute_type?((0,s.uX)(),(0,s.Wv)(ce,{key:0,effect:"plain",type:"primary",class:"type-tag"},{default:(0,s.k6)(()=>[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(ue)]),_:1}),(0,s.eW)(" "+(0,l.v_)(e.row.execute_type),1)]),_:2},1024)):"定时执行"===e.row.execute_type?((0,s.uX)(),(0,s.Wv)(ce,{key:1,effect:"plain",type:"success",class:"type-tag"},{default:(0,s.k6)(()=>[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(ge)]),_:1}),(0,s.eW)(" "+(0,l.v_)(e.row.execute_type),1)]),_:2},1024)):((0,s.uX)(),(0,s.Wv)(ce,{key:2,effect:"plain",type:"info",class:"type-tag"},{default:(0,s.k6)(()=>[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(ke)]),_:1}),t[17]||(t[17]=(0,s.eW)(" 未知 "))]),_:1,__:[17]}))]),_:1}),(0,s.bF)(ie,{label:"测试计划",align:"center","min-width":"120"},{default:(0,s.k6)(e=>[(0,s.Lk)("div",V,[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(he)]),_:1}),(0,s.Lk)("span",null,(0,l.v_)(e.row.plan_name),1)])]),_:1}),(0,s.bF)(ie,{label:"执行情况",align:"center","min-width":"220"},{default:(0,s.k6)(e=>["执行中"===e.row.status?((0,s.uX)(),(0,s.CE)("div",Y,t[18]||(t[18]=[(0,s.Lk)("div",{class:"pulse-dot"},null,-1),(0,s.Lk)("span",null,"正在执行中...",-1)]))):((0,s.uX)(),(0,s.CE)("div",z,[(0,s.Lk)("div",X,[(0,s.bF)(be,{percentage:Number(e.row.pass_rate),color:I.getStatusColorGradient(e.row.pass_rate),"stroke-width":8,"show-text":!1},null,8,["percentage","color"])]),(0,s.Lk)("div",M,[(0,s.Lk)("div",P,[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(q)]),_:1}),(0,s.Lk)("span",null,(0,l.v_)(e.row.success),1)]),(0,s.Lk)("div",E,[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(pe)]),_:1}),(0,s.Lk)("span",null,(0,l.v_)(e.row.all),1)]),(0,s.Lk)("div",{class:"stat-item rate",style:(0,l.Tr)({color:I.getStatusColor(e.row.pass_rate)})},(0,l.v_)(e.row.pass_rate)+"% ",5)])]))]),_:1}),(0,s.bF)(ie,{label:"操作",align:"center",width:"160"},{default:(0,s.k6)(a=>[(0,s.Lk)("div",U,["执行中"!==a.row.status?((0,s.uX)(),(0,s.Wv)(se,{key:0,type:"primary",size:"small",onClick:t=>e.$router.push({name:"report",params:{id:a.row.id}})},{default:(0,s.k6)(()=>[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(_e)]),_:1}),t[19]||(t[19]=(0,s.eW)("查看 "))]),_:2,__:[19]},1032,["onClick"])):((0,s.uX)(),(0,s.CE)("span",N,[(0,s.bF)(j,null,{default:(0,s.k6)(()=>[(0,s.bF)(B)]),_:1}),t[20]||(t[20]=(0,s.eW)(" 执行中 "))]))])]),_:1})]),_:1},8,["data"])),[[ve,G.isLoading]])])])]),_:1})}a(18111),a(22489),a(18237);var G=a(60782),I=a(57477);const O=a(2830);function j(e,t){const a=O.tz(e,t);return a.format("YYYY-MM-DD HH:mm:ss")}function q(e,t=!1){const a=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),l=String(e.getDate()).padStart(2,"0");let n,r,i;return t?(n="23",r="59",i="59"):(n="00",r="00",i="00"),`${a}-${s}-${l} ${n}:${r}:${i}`}var B={components:{DataLine:I.DataLine,Check:I.Check,Loading:I.Loading,PieChart:I.PieChart,Refresh:I.Refresh,Search:I.Search,Download:I.Download,Clock:I.Clock,User:I.User,AlarmClock:I.AlarmClock,QuestionFilled:I.QuestionFilled,Tickets:I.Tickets,Document:I.Document,View:I.View},data(){return{isLoading:!1,records:[],timeRange:"day3",dataTime:[q(new Date((new Date).getTime()-1728e5)),q(new Date,!0)],defaultTimeOptions:["00:00:00","23:59:59"],shortcuts:[{text:"今天",value:()=>{const e=new Date,t=new Date;return t.setHours(0,0,0),e.setHours(23,59,59),[t,e]}},{text:"近三天",value:()=>{const e=new Date,t=new Date;return t.setDate(e.getDate()-2),t.setHours(0,0,0),e.setHours(23,59,59),[t,e]}},{text:"近七天",value:()=>{const e=new Date,t=new Date;return t.setDate(e.getDate()-6),t.setHours(0,0,0),e.setHours(23,59,59),[t,e]}},{text:"近一个月",value:()=>{const e=new Date,t=new Date;return t.setMonth(e.getMonth()-1),t.setHours(0,0,0),e.setHours(23,59,59),[t,e]}}],currentPage:1,pageSize:10}},methods:{submitForm(){this.getAllRecord()},clearData(){this.dataTime=[q(new Date((new Date).getTime()-1728e5)),q(new Date,!0)]},handleTimeRangeChange(e){const t=new Date;let a;"day"===e?(a=new Date,a.setDate(t.getDate())):"day3"===e?(a=new Date,a.setDate(t.getDate()-2)):"week"===e?(a=new Date,a.setDate(t.getDate()-6)):"month"===e?(a=new Date,a.setDate(t.getDate()-29)):"all"===e&&(a=new Date,console.log(a),a.setFullYear(t.getFullYear()-10)),this.dataTime=[q(a),q(t,!0)],this.getAllRecord()},async getAllRecord(){this.isLoading=!0,await new Promise(e=>setTimeout(e,100));const e=j(this.dataTime[0],"Asia/Shanghai"),t=j(this.dataTime[1],"Asia/Shanghai"),a=await this.$api.getTestRecord({project:this.pro.id,start_time:e,end_time:t});200==a.status?(this.records=a.data,this.chartView(),this.$nextTick(()=>{this.isLoading=!1})):this.isLoading=!1},chartView(){this.$chart.chart3(this.$refs.chart_box,this.pateData.value,this.pateData.label)},getStatusColor(e){return e=Number(e),e>=90?"#67C23A":e>=70?"#E6A23C":"#F56C6C"},getStatusColorGradient(e){return e=Number(e),e>=90?[{color:"#95de64",position:0},{color:"#52c41a",position:1}]:e>=70?[{color:"#ffd666",position:0},{color:"#faad14",position:1}]:[{color:"#ff7875",position:0},{color:"#f5222d",position:1}]},getEnvType(e){const t={"生产环境":"danger","预发布环境":"warning","测试环境":"success","开发环境":"info"};return t[e]||"primary"},handleSizeChange(e){this.pageSize=e},handleCurrentChange(e){this.currentPage=e},exportData(){this.$message({message:"导出功能尚未实现，敬请期待！",type:"warning",duration:3e3})}},computed:{...(0,G.aH)(["pro"]),pateData(){let e=[],t=[];for(let a of this.records)e.push(this.$tools.rTime(a.create_time)),t.push(a.pass_rate);return{label:e,value:t}},totalSuccess(){return this.records.reduce((e,t)=>e+("执行中"!==t.status&&parseInt(t.success)||0),0)},runningCount(){return this.records.filter(e=>"执行中"===e.status).length},averagePassRate(){const e=this.records.filter(e=>"执行中"!==e.status&&e.pass_rate);if(0===e.length)return 0;const t=e.reduce((e,t)=>e+parseFloat(t.pass_rate),0);return Math.round(t/e.length)},recordsCount(){return this.records.length}},watch:{timeRange(e){this.handleTimeRangeChange(e)}},created(){this.isLoading=!0,this.getAllRecord()},mounted(){this.$nextTick(()=>{!this.isLoading&&this.records.length>0&&this.$forceUpdate()})}},J=a(71241);const K=(0,J.A)(B,[["render",Q],["__scopeId","data-v-0ffee73a"]]);var Z=K}}]);
//# sourceMappingURL=589.42e697ec.js.map