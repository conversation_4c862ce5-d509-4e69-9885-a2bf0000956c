{"version": 3, "file": "js/34.77f5864d.js", "mappings": "4LACOA,MAAM,kC,8EAAXC,EAAAA,EAAAA,IAOM,MAPNC,EAOM,EANJC,EAAAA,EAAAA,IAKEC,EAAA,CAJC,aAAYC,EAAAC,IAAIC,GAChBC,kBAAkBC,EAAAC,sBAClBC,kBAAkBF,EAAAG,sBAClBC,kBAAkBJ,EAAAK,uB,wHCLlBd,MAAM,oB,GAGAA,MAAM,e,GACJA,MAAM,e,GAYVA,MAAM,e,GAwCAA,MAAM,iB,GAEHA,MAAM,sB,GASTA,MAAM,a,GASNA,MAAM,mB,GAyBNA,MAAM,gB,GASNA,MAAM,mB,GAoBNA,MAAM,kB,GAsBZA,MAAM,sB,SAmDFA,MAAM,kB,GAONe,MAAA,oD,GA2FDf,MAAM,iB,SAgBeA,MAAM,mB,GAGxBA,MAAM,gB,GAONA,MAAM,e,GAGNA,MAAM,e,GACJA,MAAM,a,GAINA,MAAM,a,GAUZA,MAAM,iB,GAIEA,MAAM,gB,GAINA,MAAM,gB,GAKNA,MAAM,gB,GAINA,MAAM,gB,GAKNA,MAAM,gB,GAINA,MAAM,gB,GAQNA,MAAM,gB,GAINA,MAAM,gB,GAKNA,MAAM,gB,GAINA,MAAM,gB,q4BAxYzBC,EAAAA,EAAAA,IA+YM,MA/YNC,EA+YM,EA9YJC,EAAAA,EAAAA,IAmKUa,GAAA,CAnKDhB,MAAM,WAAWiB,OAAO,S,CACpBC,QAAMC,EAAAA,EAAAA,IACf,IASM,EATNC,EAAAA,EAAAA,IASM,MATNC,EASM,EARJD,EAAAA,EAAAA,IAGM,MAHNE,EAGM,EAFJnB,EAAAA,EAAAA,IAAsDoB,EAAA,CAA7CvB,MAAM,eAAa,C,iBAAC,IAAe,EAAfG,EAAAA,EAAAA,IAAeqB,K,qBAC5CJ,EAAAA,EAAAA,IAAuC,QAAjCpB,MAAM,gBAAe,SAAK,OAElCG,EAAAA,EAAAA,IAGYsB,EAAA,CAHDC,KAAK,UAAUC,KAAK,QAASC,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAEC,EAAAC,kBAAmB,I,kBAChE,IAA2B,EAA3B7B,EAAAA,EAAAA,IAA2BoB,EAAA,M,iBAAlB,IAAQ,EAARpB,EAAAA,EAAAA,IAAQ8B,K,6BAAU,c,mCAOjC,IAuBM,EAvBNb,EAAAA,EAAAA,IAuBM,MAvBNc,EAuBM,EAtBJ/B,EAAAA,EAAAA,IAqBUgC,EAAA,CArBAC,QAAQ,EAAOC,MAAON,EAAAO,WAAYtC,MAAM,e,kBAChD,IASe,EATfG,EAAAA,EAAAA,IASeoC,EAAA,CATDC,MAAM,SAAO,C,iBACzB,IAOW,EAPXrC,EAAAA,EAAAA,IAOWsC,EAAA,C,WANAV,EAAAO,WAAWI,K,qCAAXX,EAAAO,WAAWI,KAAIZ,GACxBa,YAAY,WACZC,UAAA,GACAjB,KAAK,QACL,cAAY,SACZZ,MAAA,iB,gCAGJZ,EAAAA,EAAAA,IASeoC,EAAA,M,iBARb,IAGY,EAHZpC,EAAAA,EAAAA,IAGYsB,EAAA,CAHDC,KAAK,UAAUC,KAAK,QAASC,QAAOnB,EAAAoC,e,kBAC7C,IAA6B,EAA7B1C,EAAAA,EAAAA,IAA6BoB,EAAA,M,iBAApB,IAAU,EAAVpB,EAAAA,EAAAA,IAAU2C,K,6BAAU,W,6BAG/B3C,EAAAA,EAAAA,IAGYsB,EAAA,CAHDE,KAAK,QAASC,QAAOnB,EAAAsC,a,kBAC9B,IAA8B,EAA9B5C,EAAAA,EAAAA,IAA8BoB,EAAA,M,iBAArB,IAAW,EAAXpB,EAAAA,EAAAA,IAAW6C,K,6BAAU,W,gFAQtCC,EAAAA,EAAAA,IA2GWC,GAAA,CA1GRC,KAAMpB,EAAAqB,aAEP,aAAW,UACVC,kBAAkB5C,EAAA6C,sBACnBC,OAAA,GACAC,OAAA,GACA,2BACAxD,MAAM,kB,kBAEN,IAA+C,EAA/CG,EAAAA,EAAAA,IAA+CsD,EAAA,CAA9B/B,KAAK,YAAYgC,MAAM,QAExCvD,EAAAA,EAAAA,IAOkBsD,EAAA,CAPDjB,MAAM,QAAQmB,KAAK,OAAO,YAAU,O,CACxCC,SAAOzC,EAAAA,EAAAA,IAIV0C,GAJiB,EACvBzC,EAAAA,EAAAA,IAGM,MAHN0C,EAGM,EAFJ3D,EAAAA,EAAAA,IAAkCoB,EAAA,M,iBAAzB,IAAe,EAAfpB,EAAAA,EAAAA,IAAeqB,K,OACxBJ,EAAAA,EAAAA,IAA4D,OAA5D2C,GAA4DC,EAAAA,EAAAA,IAAxBH,EAAMI,IAAIvB,MAAI,O,OAKxDvC,EAAAA,EAAAA,IAAuFsD,EAAA,CAAtEjB,MAAM,KAAKmB,KAAK,cAAc,YAAU,MAAM,8BAE/DxD,EAAAA,EAAAA,IAOkBsD,EAAA,CAPDjB,MAAM,OAAOmB,KAAK,YAAY,YAAU,O,CAC5CC,SAAOzC,EAAAA,EAAAA,IAIV0C,GAJiB,EACvBzC,EAAAA,EAAAA,IAGM,MAHN8C,EAGM,EAFJ/D,EAAAA,EAAAA,IAA+BoB,EAAA,M,iBAAtB,IAAY,EAAZpB,EAAAA,EAAAA,IAAYgE,K,OACrB/C,EAAAA,EAAAA,IAAsC,aAAA4C,EAAAA,EAAAA,IAA7BH,EAAMI,IAAIG,WAAS,O,OAKlCjE,EAAAA,EAAAA,IAuBkBsD,EAAA,CAvBDjB,MAAM,OAAO,YAAU,O,CAC3BoB,SAAOzC,EAAAA,EAAAA,IAoBV0C,GApBiB,EACvBzC,EAAAA,EAAAA,IAmBM,MAnBNiD,EAmBM,EAlBJlE,EAAAA,EAAAA,IAKamE,EAAA,CALDC,QAAQ,SAASC,UAAU,MAAMC,OAAO,S,kBAClD,IAGS,EAHTtE,EAAAA,EAAAA,IAGSuE,EAAA,CAHD/C,KAAK,QAAQ3B,MAAM,aAAayE,OAAO,S,kBAC7C,IAA4B,EAA5BtE,EAAAA,EAAAA,IAA4BoB,EAAA,M,iBAAnB,IAAS,EAATpB,EAAAA,EAAAA,IAASwE,K,eAAU,KAC5BX,EAAAA,EAAAA,IAAGH,EAAMI,IAAIW,mBAAoB,MACnC,K,yBAEFzE,EAAAA,EAAAA,IAKamE,EAAA,CALDC,QAAQ,QAAQC,UAAU,MAAMC,OAAO,S,kBACjD,IAGS,EAHTtE,EAAAA,EAAAA,IAGSuE,EAAA,CAHD/C,KAAK,QAAQD,KAAK,UAAU1B,MAAM,aAAayE,OAAO,S,kBAC5D,IAA+B,EAA/BtE,EAAAA,EAAAA,IAA+BoB,EAAA,M,iBAAtB,IAAY,EAAZpB,EAAAA,EAAAA,IAAY0E,M,eAAU,KAC/Bb,EAAAA,EAAAA,IAAGH,EAAMI,IAAIa,SAAU,QACzB,K,yBAEF3E,EAAAA,EAAAA,IAKamE,EAAA,CALDC,QAAQ,MAAMC,UAAU,MAAMC,OAAO,S,kBAC/C,IAGS,EAHTtE,EAAAA,EAAAA,IAGSuE,EAAA,CAHD/C,KAAK,QAAQD,KAAK,UAAU1B,MAAM,aAAayE,OAAO,S,kBAC5D,IAAwC,EAAxCtE,EAAAA,EAAAA,IAAwCoB,EAAA,M,iBAA/B,IAAqB,EAArBpB,EAAAA,EAAAA,IAAqB4E,M,eAAU,KACxCf,EAAAA,EAAAA,IAAGH,EAAMI,IAAIe,cAAe,KAC9B,K,mCAMR7E,EAAAA,EAAAA,IAOkBsD,EAAA,CAPDjB,MAAM,OAAOmB,KAAK,cAAcD,MAAM,O,CAC1CE,SAAOzC,EAAAA,EAAAA,IAIV0C,GAJiB,EACvBzC,EAAAA,EAAAA,IAGM,MAHN6D,EAGM,EAFJ9E,EAAAA,EAAAA,IAA+BoB,EAAA,M,iBAAtB,IAAY,EAAZpB,EAAAA,EAAAA,IAAY+E,M,OACrB9D,EAAAA,EAAAA,IAAoD,aAAA4C,EAAAA,EAAAA,IAA3CvD,EAAA0E,WAAWtB,EAAMI,IAAImB,cAAW,O,OAK/CjF,EAAAA,EAAAA,IAOkBsD,EAAA,CAPDjB,MAAM,MAAMmB,KAAK,UAAUD,MAAM,O,CACrCE,SAAOzC,EAAAA,EAAAA,IAIV0C,GAJiB,EACvBzC,EAAAA,EAAAA,IAGM,MAHNiE,EAGM,EAFJlF,EAAAA,EAAAA,IAA2BoB,EAAA,M,iBAAlB,IAAQ,EAARpB,EAAAA,EAAAA,IAAQmF,M,OACjBlE,EAAAA,EAAAA,IAAoC,aAAA4C,EAAAA,EAAAA,IAA3BH,EAAMI,IAAIsB,SAAO,O,OAKhCpF,EAAAA,EAAAA,IASkBsD,EAAA,CATDjB,MAAM,KAAKkB,MAAM,O,CACrBE,SAAOzC,EAAAA,EAAAA,IAMP0C,GANc,EACvB1D,EAAAA,EAAAA,IAKSuE,EAAA,CAJNhD,KAAMmC,EAAMI,IAAIuB,UAAY,UAAY,OACzC7D,KAAK,QACL8C,OAAO,Q,kBACP,IAAwC,E,iBAArCZ,EAAMI,IAAIuB,UAAY,KAAO,OAAV,K,6BAK5BrF,EAAAA,EAAAA,IAoBkBsD,EAAA,CApBDjB,MAAM,KAAKkB,MAAM,MAAM+B,MAAM,S,CACjC7B,SAAOzC,EAAAA,EAAAA,IAiBV0C,GAjBiB,EACvBzC,EAAAA,EAAAA,IAgBM,MAhBNsE,EAgBM,EAfJvF,EAAAA,EAAAA,IAIamE,EAAA,CAJDC,QAAQ,OAAOC,UAAU,O,kBACnC,IAEU,EAFVrE,EAAAA,EAAAA,IAEUsB,EAAA,CAFCC,KAAK,UAAUC,KAAK,QAAQgE,OAAA,GAAQ/D,QAAKE,GAAErB,EAAAmF,aAAa/B,EAAMI,M,kBACzE,IAA2B,EAA3B9D,EAAAA,EAAAA,IAA2BoB,EAAA,M,iBAAlB,IAAQ,EAARpB,EAAAA,EAAAA,IAAQ0F,M,6CAGnB1F,EAAAA,EAAAA,IAIamE,EAAA,CAJDC,QAAQ,QAAQC,UAAU,O,kBACpC,IAEU,EAFVrE,EAAAA,EAAAA,IAEUsB,EAAA,CAFCC,KAAK,UAAUC,KAAK,QAAQgE,OAAA,GAAQ/D,QAAKE,GAAErB,EAAAqF,aAAajC,EAAMI,M,kBACzE,IAA2B,EAA3B9D,EAAAA,EAAAA,IAA2BoB,EAAA,M,iBAAlB,IAAQ,EAARpB,EAAAA,EAAAA,IAAQ4F,M,6CAGnB5F,EAAAA,EAAAA,IAIamE,EAAA,CAJDC,QAAQ,QAAQC,UAAU,O,kBACpC,IAEU,EAFVrE,EAAAA,EAAAA,IAEUsB,EAAA,CAFCC,KAAK,SAASC,KAAK,QAAQgE,OAAA,GAAQ/D,QAAKE,GAAErB,EAAAuF,eAAenC,EAAMI,M,kBAC1E,IAA6B,EAA7B9D,EAAAA,EAAAA,IAA6BoB,EAAA,M,iBAApB,IAAU,EAAVpB,EAAAA,EAAAA,IAAU8F,M,mGAnGhBlE,EAAAmE,iBA4Gb9E,EAAAA,EAAAA,IAWM,MAXN+E,EAWM,EAVJhG,EAAAA,EAAAA,IASEiG,GAAA,CARAC,WAAA,GACAC,OAAO,0CACC,eAAcvE,EAAAwE,WAAWC,Q,sCAAXzE,EAAAwE,WAAWC,QAAO1E,GAChC,YAAWC,EAAAwE,WAAW5E,K,mCAAXI,EAAAwE,WAAW5E,KAAIG,GACjC,aAAY,CAAC,GAAI,GAAI,GAAI,KACzB2E,MAAO1E,EAAAwE,WAAWE,MAClBC,aAAajG,EAAAoC,cACb8D,gBAAgBlG,EAAAoC,e,yFAMvB1C,EAAAA,EAAAA,IA6IYyG,GAAA,C,WA5ID7E,EAAAC,iB,uCAAAD,EAAAC,iBAAgBF,GACxB+E,MAAO9E,EAAA+E,gBAAkB,QAAU,QACpCpD,MAAM,QACLqD,QAAOtG,EAAAuG,UACR,uB,CAgIWC,QAAM9F,EAAAA,EAAAA,IACf,IAKO,EALPC,EAAAA,EAAAA,IAKO,OALP8F,EAKO,EAJL/G,EAAAA,EAAAA,IAA2DsB,EAAA,CAA/CG,QAAKC,EAAA,MAAAA,EAAA,IAAAC,GAAEC,EAAAC,kBAAmB,I,kBAAO,IAAEH,EAAA,MAAAA,EAAA,M,QAAF,S,eAC7C1B,EAAAA,EAAAA,IAEYsB,EAAA,CAFDC,KAAK,UAAWE,QAAOnB,EAAA0G,WAAaC,QAASrF,EAAAsF,e,kBACtD,IAAmC,E,iBAAhCtF,EAAA+E,gBAAkB,KAAO,MAAV,K,mDAlIxB,IA4HU,EA5HV3G,EAAAA,EAAAA,IA4HUgC,EAAA,CA5HAE,MAAON,EAAAuF,aAAeC,MAAOxF,EAAAyF,UAAWC,IAAI,kBAAkB,cAAY,QAAQzH,MAAM,iB,kBAChG,IAKe,EALfG,EAAAA,EAAAA,IAKeoC,EAAA,CALDC,MAAM,QAAQmB,KAAK,Q,kBAC/B,IAG2B,EAH3BxD,EAAAA,EAAAA,IAG2BsC,EAAA,C,WAFhBV,EAAAuF,aAAa5E,K,qCAAbX,EAAAuF,aAAa5E,KAAIZ,GAC1Ba,YAAY,WACZ,cAAY,Y,gCAGhBxC,EAAAA,EAAAA,IAMeoC,EAAA,CANDC,MAAM,KAAKmB,KAAK,e,kBAC5B,IAI2B,EAJ3BxD,EAAAA,EAAAA,IAI2BsC,EAAA,C,WAHhBV,EAAAuF,aAAaI,Y,qCAAb3F,EAAAuF,aAAaI,YAAW5F,GACjCJ,KAAK,WACJiG,KAAM,EACPhF,YAAY,Y,+BAGiCZ,EAAA+E,iB,4BAAjD7D,EAAAA,EAAAA,IAiBeV,EAAA,C,MAjBDC,MAAM,OAAOmB,KAAK,W,kBAC9B,IAWY,EAXZxD,EAAAA,EAAAA,IAWYyH,GAAA,C,WAVD7F,EAAAuF,aAAaO,Q,qCAAb9F,EAAAuF,aAAaO,QAAO/F,GAC7Ba,YAAY,aACZmF,WAAA,GACA/G,MAAA,eACCgH,SAAQtH,EAAAuH,c,kBAEP,IAAwB,G,aAD1B/H,EAAAA,EAAAA,IAIqBgI,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAHJnG,EAAAoG,SAARC,K,WADTnF,EAAAA,EAAAA,IAIqBoF,GAAA,CAFlBC,IAAKF,EAAK7H,GACViC,MAAO4F,EAAKG,SACZC,MAAOJ,EAAK7H,I,sEAEiBwB,EAAAuF,aAAaO,U,WAA/C5H,EAAAA,EAAAA,IAGM,MAHNwI,EAGM,EAFJtI,EAAAA,EAAAA,IAAiCoB,EAAA,M,iBAAxB,IAAc,EAAdpB,EAAAA,EAAAA,IAAcuI,M,qBACvBtH,EAAAA,EAAAA,IAA2C,YAArC,kCAA8B,Q,yBAIxCjB,EAAAA,EAAAA,IAOawI,GAAA,CAPD,mBAAiB,UAAQ,C,iBACnC,IAKM,EALNvH,EAAAA,EAAAA,IAKM,MALNwH,EAKM,C,eAJJxH,EAAAA,EAAAA,IAAiB,YAAX,QAAI,IACOW,EAAA8G,iBAAmB9G,EAAA+E,kB,WAApC7D,EAAAA,EAAAA,IAEYxB,EAAA,C,MAFyCC,KAAK,OAAQE,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAErB,EAAAqI,sBAAsB/G,EAAA8G,kB,kBAC7F,IAA4B,EAA5B1I,EAAAA,EAAAA,IAA4BoB,EAAA,M,iBAAnB,IAAS,EAATpB,EAAAA,EAAAA,IAASwE,K,6BAAU,gB,0CAKlCxE,EAAAA,EAAAA,IAwBS4I,GAAA,CAxBAC,OAAQ,IAAE,C,iBACjB,IAWS,EAXT7I,EAAAA,EAAAA,IAWS8I,GAAA,CAXAC,KAAM,IAAE,C,iBACf,IASe,EATf/I,EAAAA,EAAAA,IASeoC,EAAA,CATDC,MAAM,SAASmB,KAAK,qB,kBAChC,IAOkB,EAPlBxD,EAAAA,EAAAA,IAOkBgJ,GAAA,C,WANPpH,EAAAuF,aAAa1C,kB,qCAAb7C,EAAAuF,aAAa1C,kBAAiB9C,GACtCsH,IAAK,EACLC,UAAW,EACZ,oBAAkB,QAClBtI,MAAA,gB,CACWuI,QAAMnI,EAAAA,EAAAA,IAAC,IAAEU,EAAA,MAAAA,EAAA,M,QAAF,S,wCAIxB1B,EAAAA,EAAAA,IAUS8I,GAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQe,EARf/I,EAAAA,EAAAA,IAQeoC,EAAA,CARDC,MAAM,QAAQmB,KAAK,W,kBAC/B,IAMkB,EANlBxD,EAAAA,EAAAA,IAMkBgJ,GAAA,C,WALPpH,EAAAuF,aAAaxC,Q,qCAAb/C,EAAAuF,aAAaxC,QAAOhD,GAC5BsH,IAAK,EACLC,UAAW,EACZ,oBAAkB,QAClBtI,MAAA,gB,gDAMRZ,EAAAA,EAAAA,IA2BS4I,GAAA,CA3BAC,OAAQ,IAAE,C,iBACjB,IAYS,EAZT7I,EAAAA,EAAAA,IAYS8I,GAAA,CAZAC,KAAM,IAAE,C,iBACf,IAUe,EAVf/I,EAAAA,EAAAA,IAUeoC,EAAA,CAVDC,MAAM,MAAMmB,KAAK,gB,kBAC7B,IAQkB,EARlBxD,EAAAA,EAAAA,IAQkBgJ,GAAA,C,WAPPpH,EAAAuF,aAAatC,a,uCAAbjD,EAAAuF,aAAatC,aAAYlD,GACjCsH,IAAK,EACLG,IAAK,IACLF,UAAW,EACZ,oBAAkB,QAClBtI,MAAA,gB,CACWuI,QAAMnI,EAAAA,EAAAA,IAAC,IAACU,EAAA,MAAAA,EAAA,M,QAAD,Q,wCAIxB1B,EAAAA,EAAAA,IAYS8I,GAAA,CAZAC,KAAM,IAAE,C,iBACf,IAUe,EAVf/I,EAAAA,EAAAA,IAUeoC,EAAA,CAVDC,MAAM,SAASmB,KAAK,W,kBAChC,IAQkB,EARlBxD,EAAAA,EAAAA,IAQkBgJ,GAAA,C,WAPPpH,EAAAuF,aAAakC,Q,uCAAbzH,EAAAuF,aAAakC,QAAO1H,GAC5BsH,IAAK,EACLG,IAAK,IACLF,UAAW,EACZ,oBAAkB,QAClBtI,MAAA,gB,CACWuI,QAAMnI,EAAAA,EAAAA,IAAC,IAACU,EAAA,MAAAA,EAAA,M,QAAD,Q,gDAM1B1B,EAAAA,EAAAA,IAwBS4I,GAAA,CAxBAC,OAAQ,IAAE,C,iBACjB,IAYS,EAZT7I,EAAAA,EAAAA,IAYS8I,GAAA,CAZAC,KAAM,IAAE,C,iBACf,IAUe,EAVf/I,EAAAA,EAAAA,IAUeoC,EAAA,CAVDC,MAAM,QAAQmB,KAAK,c,kBAC/B,IAQkB,EARlBxD,EAAAA,EAAAA,IAQkBgJ,GAAA,C,WAPPpH,EAAAuF,aAAamC,W,uCAAb1H,EAAAuF,aAAamC,WAAU3H,GAC/BsH,IAAK,EACLG,IAAK,IACLF,UAAW,EACZ,oBAAkB,QAClBtI,MAAA,gB,CACWuI,QAAMnI,EAAAA,EAAAA,IAAC,IAACU,EAAA,MAAAA,EAAA,M,QAAD,Q,wCAIxB1B,EAAAA,EAAAA,IASS8I,GAAA,CATAC,KAAM,IAAE,C,iBACf,IAOe,EAPf/I,EAAAA,EAAAA,IAOeoC,EAAA,CAPDC,MAAM,QAAM,C,iBACxB,IAKY,EALZrC,EAAAA,EAAAA,IAKYuJ,GAAA,C,WAJD3H,EAAAuF,aAAa9B,U,uCAAbzD,EAAAuF,aAAa9B,UAAS1D,GAC/B,cAAY,KACZ,gBAAc,MACd,oB,uHAkBZ3B,EAAAA,EAAAA,IAuFYyG,GAAA,C,WAtFD7E,EAAA4H,iB,uCAAA5H,EAAA4H,iBAAgB7H,GACzB+E,MAAM,QACNnD,MAAM,QACN,uB,kBAEA,IAgFM,CAhFK3B,EAAA6H,mB,WAAX3J,EAAAA,EAAAA,IAgFM,MAhFN4J,EAgFM,EA/EJ1J,EAAAA,EAAAA,IAuBS4I,GAAA,CAvBAC,OAAQ,GAAIhJ,MAAM,iB,kBACzB,IASS,EATTG,EAAAA,EAAAA,IASS8I,GAAA,CATAC,KAAM,IAAE,C,iBACf,IAMM,EANN9H,EAAAA,EAAAA,IAMM,MANN0I,EAMM,EALJ3J,EAAAA,EAAAA,IAAkCoB,EAAA,M,iBAAzB,IAAe,EAAfpB,EAAAA,EAAAA,IAAeqB,K,OACxBJ,EAAAA,EAAAA,IAAoC,WAAA4C,EAAAA,EAAAA,IAA7BjC,EAAA6H,iBAAiBlH,MAAI,IAC5BvC,EAAAA,EAAAA,IAESuE,EAAA,CAFAhD,KAAMK,EAAA6H,iBAAiBpE,UAAY,UAAY,OAAQxF,MAAM,c,kBACpE,IAA+C,E,iBAA5C+B,EAAA6H,iBAAiBpE,UAAY,KAAO,OAAV,K,oBAGjCpE,EAAAA,EAAAA,IAAiE,MAAjE2I,GAAiE/F,EAAAA,EAAAA,IAArCjC,EAAA6H,iBAAiBlC,aAAW,K,OAE1DvH,EAAAA,EAAAA,IAWS8I,GAAA,CAXAC,KAAM,GAAC,C,iBACd,IASM,EATN9H,EAAAA,EAAAA,IASM,MATN4I,EASM,EARJ5I,EAAAA,EAAAA,IAGM,MAHN6I,EAGM,EAFJ9J,EAAAA,EAAAA,IAA2BoB,EAAA,M,iBAAlB,IAAQ,EAARpB,EAAAA,EAAAA,IAAQmF,M,OACjBlE,EAAAA,EAAAA,IAAgD,YAA1C,SAAK4C,EAAAA,EAAAA,IAAGjC,EAAA6H,iBAAiBrE,SAAO,MAExCnE,EAAAA,EAAAA,IAGM,MAHN8I,EAGM,EAFJ/J,EAAAA,EAAAA,IAA+BoB,EAAA,M,iBAAtB,IAAY,EAAZpB,EAAAA,EAAAA,IAAY+E,M,OACrB9D,EAAAA,EAAAA,IAAiE,YAA3D,UAAM4C,EAAAA,EAAAA,IAAGvD,EAAA0E,WAAWpD,EAAA6H,iBAAiBxE,cAAW,S,eAM9DjF,EAAAA,EAAAA,IAAuDwI,GAAA,CAA3C,mBAAiB,UAAQ,C,iBAAC,IAAI9G,EAAA,MAAAA,EAAA,M,QAAJ,W,eAEtCT,EAAAA,EAAAA,IAmDM,MAnDN+I,EAmDM,EAlDJhK,EAAAA,EAAAA,IA4BS4I,GAAA,CA5BAC,OAAQ,IAAE,C,iBACjB,IAQS,EART7I,EAAAA,EAAAA,IAQS8I,GAAA,CARAC,KAAM,GAAC,C,iBACd,IAMU,EANV/I,EAAAA,EAAAA,IAMUa,GAAA,CANDC,OAAO,QAAQjB,MAAM,6B,kBAC5B,IAGM,EAHNoB,EAAAA,EAAAA,IAGM,MAHNgJ,EAGM,EAFJjK,EAAAA,EAAAA,IAA4BoB,EAAA,M,iBAAnB,IAAS,EAATpB,EAAAA,EAAAA,IAASwE,K,qBAClBvD,EAAAA,EAAAA,IAAmB,YAAb,UAAM,OAEdA,EAAAA,EAAAA,IAAqG,MAArGiJ,EAAqG,E,iBAAxEtI,EAAA6H,iBAAiBhF,mBAAoB,IAAC,G,eAAAxD,EAAAA,EAAAA,IAA4B,QAAtBpB,MAAM,QAAO,MAAE,Q,eAG5FG,EAAAA,EAAAA,IAQS8I,GAAA,CARAC,KAAM,GAAC,C,iBACd,IAMU,EANV/I,EAAAA,EAAAA,IAMUa,GAAA,CANDC,OAAO,QAAQjB,MAAM,mB,kBAC5B,IAGM,EAHNoB,EAAAA,EAAAA,IAGM,MAHNkJ,EAGM,EAFJnK,EAAAA,EAAAA,IAA+BoB,EAAA,M,iBAAtB,IAAY,EAAZpB,EAAAA,EAAAA,IAAY0E,M,qBACrBzD,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,OAEbA,EAAAA,EAAAA,IAA8D,MAA9DmJ,GAA8DvG,EAAAA,EAAAA,IAAjCjC,EAAA6H,iBAAiB9E,SAAO,K,eAGzD3E,EAAAA,EAAAA,IAQS8I,GAAA,CARAC,KAAM,GAAC,C,iBACd,IAMU,EANV/I,EAAAA,EAAAA,IAMUa,GAAA,CANDC,OAAO,QAAQjB,MAAM,4B,kBAC5B,IAGM,EAHNoB,EAAAA,EAAAA,IAGM,MAHNoJ,EAGM,EAFJrK,EAAAA,EAAAA,IAAwCoB,EAAA,M,iBAA/B,IAAqB,EAArBpB,EAAAA,EAAAA,IAAqB4E,M,qBAC9B3D,EAAAA,EAAAA,IAAgB,YAAV,OAAG,OAEXA,EAAAA,EAAAA,IAA8F,MAA9FqJ,EAA8F,E,iBAAjE1I,EAAA6H,iBAAiB5E,cAAY,G,eAAG5D,EAAAA,EAAAA,IAA2B,QAArBpB,MAAM,QAAO,KAAC,Q,uBAKvFG,EAAAA,EAAAA,IAmBS4I,GAAA,CAnBAC,OAAQ,GAAIjI,MAAA,uB,kBACnB,IAQS,EARTZ,EAAAA,EAAAA,IAQS8I,GAAA,CARAC,KAAM,IAAE,C,iBACf,IAMU,EANV/I,EAAAA,EAAAA,IAMUa,GAAA,CANDC,OAAO,QAAQjB,MAAM,mB,kBAC5B,IAGM,EAHNoB,EAAAA,EAAAA,IAGM,MAHNsJ,EAGM,EAFJvK,EAAAA,EAAAA,IAA0BoB,EAAA,M,iBAAjB,IAAO,EAAPpB,EAAAA,EAAAA,IAAOwK,M,qBAChBvJ,EAAAA,EAAAA,IAAmB,YAAb,UAAM,OAEdA,EAAAA,EAAAA,IAAyF,MAAzFwJ,EAAyF,E,iBAA5D7I,EAAA6H,iBAAiBJ,SAAO,G,eAAGpI,EAAAA,EAAAA,IAA2B,QAArBpB,MAAM,QAAO,KAAC,Q,eAGhFG,EAAAA,EAAAA,IAQS8I,GAAA,CARAC,KAAM,IAAE,C,iBACf,IAMU,EANV/I,EAAAA,EAAAA,IAMUa,GAAA,CANDC,OAAO,QAAQjB,MAAM,sB,kBAC5B,IAGM,EAHNoB,EAAAA,EAAAA,IAGM,MAHNyJ,EAGM,EAFJ1K,EAAAA,EAAAA,IAA2BoB,EAAA,M,iBAAlB,IAAQ,EAARpB,EAAAA,EAAAA,IAAQ2K,M,qBACjB1J,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,OAEbA,EAAAA,EAAAA,IAA4F,MAA5F2J,EAA4F,E,iBAA/DhJ,EAAA6H,iBAAiBH,YAAU,G,eAAGrI,EAAAA,EAAAA,IAA2B,QAArBpB,MAAM,QAAO,KAAC,Q,2GAkB/F,GACE0C,KAAM,kBACNsI,WAAY,CACVC,KAAI,OAAEC,OAAM,SAAEC,QAAO,UAAEC,YAAW,cAAEC,KAAI,OAAEC,KAAI,OAAEC,OAAM,SACtDC,MAAK,QAAEC,SAAQ,WAAEC,kBAAiB,oBAAEC,SAAQ,WAAEC,KAAI,OAAGC,KAAI,OAAEC,SAAQ,WACnEC,WAAUA,EAAAA,YAEZC,MAAO,CACLC,UAAW,CACTvK,KAAM,CAACwK,OAAQC,QACfC,UAAU,IAGdC,MAAO,CAELrK,gBAAAA,CAAiBsK,GACXA,IAAWC,KAAKzF,iBAClByF,KAAKC,WAET,GAEFrJ,IAAAA,GACE,MAAO,CACLC,aAAc,GACd8C,cAAc,EACdmB,eAAe,EACfrF,kBAAkB,EAClB2H,kBAAkB,EAClB7C,gBAAiB,KACjB8C,iBAAkB,KAClB6C,aAAc,GACdtE,SAAU,GACVU,eAAgB,KAEhBvG,WAAY,CACVI,KAAM,IAGR6D,WAAY,CACVC,QAAS,EACT7E,KAAM,GACN8E,MAAO,GAGTa,aAAc,CACZ5E,KAAM,GACNgF,YAAa,GACbG,QAAS,KACTjD,kBAAmB,EACnBE,QAAS,EACTE,aAAc,EACdwE,QAAS,EACTC,WAAY,EACZjE,WAAW,GAGbgC,UAAW,CACT9E,KAAM,CACJ,CAAE0J,UAAU,EAAMM,QAAS,WAAYC,QAAS,QAChD,CAAEvD,IAAK,EAAGG,IAAK,IAAKmD,QAAS,kBAAmBC,QAAS,SAE3D9E,QAAS,CACP,CAAEuE,UAAU,EAAMM,QAAS,UAAWC,QAAS,WAEjD/H,kBAAmB,CACjB,CAAEwH,UAAU,EAAMM,QAAS,YAAaC,QAAS,QACjD,CAAEjL,KAAM,SAAUgL,QAAS,cAAeC,QAAS,SAErD7H,QAAS,CACP,CAAEsH,UAAU,EAAMM,QAAS,WAAYC,QAAS,QAChD,CAAEjL,KAAM,SAAUgL,QAAS,aAAcC,QAAS,SAEpD3H,aAAc,CACZ,CAAEoH,UAAU,EAAMM,QAAS,SAAUC,QAAS,QAC9C,CAAEjL,KAAM,SAAUgL,QAAS,WAAYC,QAAS,UAIxD,EACAC,OAAAA,GACEL,KAAK1J,eACP,EACAgK,QAAS,CAEP,eAAML,GACJ,IACE,MAAMM,QAAiBP,KAAKQ,KAAKC,oBAAoB,CACnDC,WAAYV,KAAKN,UACjBiB,SAAS,IAGa,MAApBJ,EAASK,SACXZ,KAAKpE,SAAW2E,EAAS3J,MAAQ,GAErC,CAAE,MAAOiK,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,cAAgBA,EAAMV,SAAW,SACjDH,KAAKpE,SAAW,EAClB,CACF,EAGA,mBAAMtF,GACJ0J,KAAKrG,cAAe,EACpB,IACE,MAAMqH,EAAS,CACbN,WAAYV,KAAKN,UACjBuB,KAAMjB,KAAKhG,WAAWC,QACtBiH,UAAWlB,KAAKhG,WAAW5E,KAC3Be,KAAM6J,KAAKjK,WAAWI,WAAQgL,GAG1BZ,QAAiBP,KAAKQ,KAAKY,aAAaJ,GAE9C,GAAwB,MAApBT,EAASK,OAAgB,CAC3B,IAAIhK,EAAO2J,EAAS3J,MAAQ,CAAC,EAGzBA,EAAKyK,SAAWC,MAAMC,QAAQ3K,EAAKyK,UACrCrB,KAAKnJ,aAAeD,EAAKyK,QACzBrB,KAAKhG,WAAWE,MAAQtD,EAAK4K,OAAS5K,EAAKyK,QAAQI,QAG5CH,MAAMC,QAAQ3K,IACrBoJ,KAAKnJ,aAAeD,EACpBoJ,KAAKhG,WAAWE,MAAQtD,EAAK6K,QACpB7K,EAAK8K,WAAaJ,MAAMC,QAAQ3K,EAAK8K,YAC9C1B,KAAKnJ,aAAeD,EAAK8K,UACzB1B,KAAKhG,WAAWE,MAAQtD,EAAKsD,OAAStD,EAAK8K,UAAUD,SAGrDzB,KAAKnJ,aAAe,GACpBmJ,KAAKhG,WAAWE,MAAQ,GAI1B4G,QAAQa,IAAI,aAAc3B,KAAKnJ,aACjC,CACF,CAAE,MAAOgK,GACPC,QAAQD,MAAM,WAAYA,GAC1BE,EAAAA,GAAUF,MAAM,aAAeA,EAAMN,UAAU3J,MAAMuJ,SAAWU,EAAMV,SAAW,SAEjFH,KAAKnJ,aAAe,GACpBmJ,KAAKhG,WAAWE,MAAQ,CAC1B,CAAE,QACA8F,KAAKrG,cAAe,CACtB,CACF,EAGAnD,WAAAA,GACEwJ,KAAKjK,WAAWI,KAAO,GACvB6J,KAAKhG,WAAWC,QAAU,EAC1B+F,KAAK1J,eACP,EAGAS,qBAAAA,CAAsB6K,GACpB5B,KAAKE,aAAe0B,CACtB,EAGAvI,YAAAA,CAAawI,GACX7B,KAAK3C,iBAAmBwE,EACxB7B,KAAK5C,kBAAmB,CAC1B,EAGA7D,YAAAA,CAAasI,GACX7B,KAAKzF,gBAAkBsH,EACvB7B,KAAKjF,aAAe,IAAK8G,GACzB7B,KAAKvK,kBAAmB,CAC1B,EAGA,oBAAMgE,CAAeoI,GACnB,UACQC,EAAAA,EAAaC,QACjB,aAAaF,EAAS1L,WACtB,OACA,CACE6L,kBAAmB,KACnBC,iBAAkB,KAClB9M,KAAM,YAIV,MAAMoL,QAAiBP,KAAKQ,KAAK/G,eAAeoI,EAAS7N,IAEjC,MAApBuM,EAASK,QAAsC,MAApBL,EAASK,SACtCG,EAAAA,GAAUmB,QAAQ,QAClBlC,KAAK1J,gBAET,CAAE,MAAOuK,GACO,WAAVA,IACFC,QAAQD,MAAM,WAAYA,GAC1BE,EAAAA,GAAUF,MAAM,UAAYA,EAAMV,SAAW,SAEjD,CACF,EAGA,kBAAM1E,CAAa0G,GACjB,GAAKA,EAKL,IAEE,MAAM5B,QAAiBP,KAAKQ,KAAK4B,eAAe,CAC9C9G,QAAS6G,EACTlB,KAAM,EACNC,UAAW,IAGW,MAApBX,EAASK,QAAkBL,EAAS3J,KAAKyK,SAAWd,EAAS3J,KAAKyK,QAAQI,OAAS,GACrFzB,KAAK1D,eAAiBiE,EAAS3J,KAAKyK,QAAQ,GAIzCrB,KAAKjF,aAAa1C,mBAClB2H,KAAKjF,aAAaxC,SAClByH,KAAKjF,aAAatC,cAClBuH,KAAKjF,aAAakC,SAClB+C,KAAKjF,aAAamC,YAGnB4E,EAAAA,EAAaC,QACX,qBACA,SACA,CACEC,kBAAmB,IACnBC,iBAAkB,IAClB9M,KAAM,SAERkN,KAAK,KAELrC,KAAKzD,sBAAsByD,KAAK1D,kBAC/BgG,MAAM,UAKXtC,KAAK1D,eAAiB,KACtByE,EAAAA,GAAUwB,QAAQ,eAEtB,CAAE,MAAO1B,GACPC,QAAQD,MAAM,YAAaA,GAC3Bb,KAAK1D,eAAiB,IACxB,MA9CE0D,KAAK1D,eAAiB,IA+C1B,EAGAC,qBAAAA,CAAsBiG,GACfA,IAELxC,KAAKjF,aAAa1C,kBAAoBmK,EAAOC,iBAAmB,EAChEzC,KAAKjF,aAAaxC,QAAUiK,EAAOE,QAAU,EAC7C1C,KAAKjF,aAAatC,aAAe+J,EAAOG,UAAa,IAAMH,EAAOG,UAAa,IAC/E3C,KAAKjF,aAAakC,QAAUuF,EAAOI,QAAU,EAC7C5C,KAAKjF,aAAamC,WAAasF,EAAOK,WAAa,EAEnD9B,EAAAA,GAAUmB,QAAQ,iBACpB,EAGA,gBAAMtH,GACJ,IACE,MAAMkI,QAAc9C,KAAK+C,MAAMC,gBAAgBC,WAC/C,IAAKH,EAAO,OAEZ9C,KAAKlF,eAAgB,EAErB,MAAMkG,EAAS,IACVhB,KAAKjF,aACR2F,WAAYV,KAAKN,WAInB,IAAKM,KAAKzF,gBAAiB,CAEzB,IAAKyF,KAAKjF,aAAaO,QAGrB,OAFAyF,EAAAA,GAAUF,MAAM,mBAChBb,KAAKlF,eAAgB,GAKvB,MAAMoI,EACoC,IAAxClD,KAAKjF,aAAa1C,mBACY,IAA9B2H,KAAKjF,aAAaxC,SACY,IAA9ByH,KAAKjF,aAAakC,SACe,IAAjC+C,KAAKjF,aAAamC,WAEpB,GAAIgG,EAAS,CACX,MAAMC,QAAerB,EAAAA,EAAaC,QAChC,4CACA,OACA,CACEC,kBAAmB,KACnBC,iBAAkB,KAClB9M,KAAM,YAERmN,MAAM,IAAM,UAEd,GAAe,WAAXa,EAEF,YADAnD,KAAKlF,eAAgB,EAGzB,CACF,CAKA,IAAIyF,EAFJO,QAAQa,IAAI,WAAYX,GAItBT,EADEP,KAAKzF,sBACUyF,KAAKQ,KAAK4C,eAAepD,KAAKzF,gBAAgBvG,GAAIgN,SAElDhB,KAAKQ,KAAK6C,eAAerC,GAGpB,MAApBT,EAASK,QAAsC,MAApBL,EAASK,SACtCG,EAAAA,GAAUmB,QAAQlC,KAAKzF,gBAAkB,OAAS,QAClDyF,KAAKvK,kBAAmB,EACxBuK,KAAK1J,gBAET,CAAE,MAAOuK,GACPC,QAAQD,MAAM,QAASA,GACvBE,EAAAA,GAAUF,MAAM,UAAYA,EAAMN,UAAU3J,MAAMuJ,SAAWU,EAAMV,SAAW,QAChF,CAAE,QACAH,KAAKlF,eAAgB,CACvB,CACF,EAGAL,SAAAA,GACEuF,KAAKzF,gBAAkB,KACvByF,KAAKjF,aAAe,CAClB5E,KAAM,GACNgF,YAAa,GACbG,QAAS,KACTjD,kBAAmB,EACnBE,QAAS,EACTE,aAAc,EACdwE,QAAS,EACTC,WAAY,EACZjE,WAAW,GAEb+G,KAAK+C,MAAMC,iBAAiBM,aAC9B,EAGA1K,UAAAA,CAAW2K,GACT,OAAKA,EACE,IAAIC,KAAKD,GAAME,iBADJ,GAEpB,I,WCzvBJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,QFMA,GACEvN,KAAM,sBACNsI,WAAY,CACVkF,gBAAeA,GAEjBC,SAAU,KACLC,EAAAA,EAAAA,IAAS,CACV9P,IAAK+P,GAASA,EAAM/P,OAGxBuM,QAAS,CACPnM,qBAAAA,CAAsB4P,GAEpB/D,KAAKgE,SAAS,CACZ7O,KAAM,UACNgL,QAAS,YAAY4D,EAAa5N,OAClC8N,SAAU,KAEd,EAEA5P,qBAAAA,CAAsB0P,GAEpB/D,KAAKgE,SAAS,CACZ7O,KAAM,UACNgL,QAAS,YAAY4D,EAAa5N,OAClC8N,SAAU,KAEd,EAEA1P,qBAAAA,CAAsBwP,GAEpB/D,KAAKgE,SAAS,CACZ7O,KAAM,UACNgL,QAAS,YAAY4D,EAAa5N,OAClC8N,SAAU,KAEd,IG5CJ,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceBaseline.vue", "webpack://frontend-web/./src/views/PerformanceTest/BaselineManager.vue", "webpack://frontend-web/./src/views/PerformanceTest/BaselineManager.vue?eb0e", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceBaseline.vue?1f0f"], "sourcesContent": ["<template>\r\n  <div class=\"performance-baseline-container\">\r\n    <BaselineManager \r\n      :project-id=\"pro.id\"\r\n      @baseline-created=\"handleBaselineCreated\"\r\n      @baseline-updated=\"handleBaselineUpdated\"\r\n      @baseline-deleted=\"handleBaselineDeleted\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport BaselineManager from '@/views/PerformanceTest/BaselineManager.vue'\r\n\r\nexport default {\r\n  name: 'PerformanceBaseline',\r\n  components: {\r\n    BaselineManager\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      pro: state => state.pro\r\n    })\r\n  },\r\n  methods: {\r\n    handleBaselineCreated(baselineData) {\r\n      // 处理基准线创建事件\r\n      this.$message({\r\n        type: 'success',\r\n        message: `基准线创建成功: ${baselineData.name}`,\r\n        duration: 3000\r\n      })\r\n    },\r\n    \r\n    handleBaselineUpdated(baselineData) {\r\n      // 处理基准线更新事件\r\n      this.$message({\r\n        type: 'success',\r\n        message: `基准线更新成功: ${baselineData.name}`,\r\n        duration: 3000\r\n      })\r\n    },\r\n    \r\n    handleBaselineDeleted(baselineData) {\r\n      // 处理基准线删除事件\r\n      this.$message({\r\n        type: 'success',\r\n        message: `基准线删除成功: ${baselineData.name}`,\r\n        duration: 3000\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.performance-baseline-container {\r\n  padding: 20px;\r\n  height: 100%;\r\n}\r\n</style>", "<template>\n  <div class=\"baseline-manager\">\n    <el-card class=\"box-card\" shadow=\"hover\">\n      <template #header>\n        <div class=\"card-header\">\n          <div class=\"header-left\">\n            <el-icon class=\"header-icon\"><TrendCharts /></el-icon>\n            <span class=\"header-title\">基准线管理</span>\n          </div>\n          <el-button type=\"primary\" size=\"small\" @click=\"showCreateDialog = true\">\n            <el-icon><Plus /></el-icon>\n            创建基准线\n          </el-button>\n        </div>\n      </template>\n      \n      <!-- 搜索区域 -->\n      <div class=\"search-area\">\n        <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\n          <el-form-item label=\"基准线名称\">\n            <el-input \n              v-model=\"searchForm.name\" \n              placeholder=\"请输入基准线名称\" \n              clearable \n              size=\"small\"\n              prefix-icon=\"Search\"\n              style=\"width: 220px;\">\n            </el-input>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" size=\"small\" @click=\"loadBaselines\">\n              <el-icon><Search /></el-icon>\n              搜索\n            </el-button>\n            <el-button size=\"small\" @click=\"resetSearch\">\n              <el-icon><Refresh /></el-icon>\n              重置\n            </el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <!-- 基准线列表 -->\n      <el-table\n        :data=\"baselineList\"\n        v-loading=\"tableLoading\"\n        empty-text=\"暂无基准线数据\"\n        @selection-change=\"handleSelectionChange\"\n        border\n        stripe\n        highlight-current-row\n        class=\"baseline-table\">\n        \n        <el-table-column type=\"selection\" width=\"55\" />\n        \n        <el-table-column label=\"基准线名称\" prop=\"name\" min-width=\"200\">\n          <template #default=\"scope\">\n            <div class=\"baseline-name\">\n              <el-icon><TrendCharts /></el-icon>\n              <span class=\"baseline-name-text\">{{ scope.row.name }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"描述\" prop=\"description\" min-width=\"200\" show-overflow-tooltip />\n        \n        <el-table-column label=\"关联任务\" prop=\"task_name\" min-width=\"150\">\n          <template #default=\"scope\">\n            <div class=\"task-name\">\n              <el-icon><Document /></el-icon>\n              <span>{{ scope.row.task_name }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"基准指标\" min-width=\"300\">\n          <template #default=\"scope\">\n            <div class=\"metrics-display\">\n              <el-tooltip content=\"平均响应时间\" placement=\"top\" effect=\"light\">\n                <el-tag size=\"small\" class=\"metric-tag\" effect=\"plain\">\n                  <el-icon><Timer /></el-icon>\n                  {{ scope.row.avg_response_time }}ms\n                </el-tag>\n              </el-tooltip>\n              <el-tooltip content=\"每秒事务数\" placement=\"top\" effect=\"light\">\n                <el-tag size=\"small\" type=\"success\" class=\"metric-tag\" effect=\"plain\">\n                  <el-icon><PieChart /></el-icon>\n                  {{ scope.row.avg_tps }} TPS\n                </el-tag>\n              </el-tooltip>\n              <el-tooltip content=\"成功率\" placement=\"top\" effect=\"light\">\n                <el-tag size=\"small\" type=\"warning\" class=\"metric-tag\" effect=\"plain\">\n                  <el-icon><CircleCheckFilled /></el-icon>\n                  {{ scope.row.success_rate }}%\n                </el-tag>\n              </el-tooltip>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"创建时间\" prop=\"create_time\" width=\"180\">\n          <template #default=\"scope\">\n            <div class=\"time-display\">\n              <el-icon><Calendar /></el-icon>\n              <span>{{ formatTime(scope.row.create_time) }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"创建人\" prop=\"creator\" width=\"100\">\n          <template #default=\"scope\">\n            <div class=\"creator-display\">\n              <el-icon><User /></el-icon>\n              <span>{{ scope.row.creator }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag \n              :type=\"scope.row.is_active ? 'success' : 'info'\" \n              size=\"small\"\n              effect=\"dark\">\n              {{ scope.row.is_active ? '活跃' : '非活跃' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"250\" fixed=\"right\">\n          <template #default=\"scope\">\n            <div class=\"action-buttons\">\n              <el-tooltip content=\"查看详情\" placement=\"top\">\n                <el-button type=\"primary\" size=\"small\" circle @click=\"viewBaseline(scope.row)\">\n                <el-icon><View /></el-icon>\n              </el-button>\n              </el-tooltip>\n              <el-tooltip content=\"编辑基准线\" placement=\"top\">\n                <el-button type=\"warning\" size=\"small\" circle @click=\"editBaseline(scope.row)\">\n                <el-icon><Edit /></el-icon>\n              </el-button>\n              </el-tooltip>\n              <el-tooltip content=\"删除基准线\" placement=\"top\">\n                <el-button type=\"danger\" size=\"small\" circle @click=\"deleteBaseline(scope.row)\">\n                <el-icon><Delete /></el-icon>\n              </el-button>\n              </el-tooltip>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          background\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          v-model:current-page=\"pagination.current\"\n          v-model:page-size=\"pagination.size\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :total=\"pagination.total\"\n          @size-change=\"loadBaselines\"\n          @current-change=\"loadBaselines\"\n        />\n      </div>\n    </el-card>\n\n    <!-- 创建/编辑基准线对话框 -->\n    <el-dialog\n      v-model=\"showCreateDialog\"\n      :title=\"editingBaseline ? '编辑基准线' : '创建基准线'\"\n      width=\"650px\"\n      @close=\"resetForm\"\n      destroy-on-close>\n      \n      <el-form :model=\"baselineForm\" :rules=\"formRules\" ref=\"baselineFormRef\" label-width=\"120px\" class=\"baseline-form\">\n        <el-form-item label=\"基准线名称\" prop=\"name\">\n          <el-input \n            v-model=\"baselineForm.name\" \n            placeholder=\"请输入基准线名称\"\n            prefix-icon=\"Document\" />\n        </el-form-item>\n        \n        <el-form-item label=\"描述\" prop=\"description\">\n          <el-input \n            v-model=\"baselineForm.description\" \n            type=\"textarea\" \n            :rows=\"3\"\n            placeholder=\"请输入基准线描述\" />\n        </el-form-item>\n        \n        <el-form-item label=\"关联任务\" prop=\"task_id\" v-if=\"!editingBaseline\">\n          <el-select \n            v-model=\"baselineForm.task_id\" \n            placeholder=\"请选择关联的性能任务\"\n            filterable\n            style=\"width: 100%\"\n            @change=\"onTaskChange\">\n            <el-option \n              v-for=\"task in taskList\" \n              :key=\"task.id\" \n              :label=\"task.taskName\" \n              :value=\"task.id\" />\n          </el-select>\n          <div class=\"form-help-text\" v-if=\"baselineForm.task_id\">\n            <el-icon><InfoFilled /></el-icon> \n            <span>如不填写指标值，将自动使用该任务的最近一次报告数据作为基准线</span>\n          </div>\n        </el-form-item>\n        \n        <el-divider content-position=\"center\">\n          <div style=\"display: flex; align-items: center; gap: 10px;\">\n            <span>性能指标</span>\n            <el-button v-if=\"lastTaskReport && !editingBaseline\" type=\"text\" @click=\"fillMetricsFromReport(lastTaskReport)\">\n              <el-icon><Timer /></el-icon> 从最新报告填充\n            </el-button>\n          </div>\n        </el-divider>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"平均响应时间\" prop=\"avg_response_time\">\n              <el-input-number \n                v-model=\"baselineForm.avg_response_time\" \n                :min=\"0\"\n                :precision=\"2\"\n                controls-position=\"right\"\n                style=\"width: 100%\">\n                <template #append>ms</template>\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"平均TPS\" prop=\"avg_tps\">\n              <el-input-number \n                v-model=\"baselineForm.avg_tps\" \n                :min=\"0\"\n                :precision=\"2\"\n                controls-position=\"right\"\n                style=\"width: 100%\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"成功率\" prop=\"success_rate\">\n              <el-input-number \n                v-model=\"baselineForm.success_rate\" \n                :min=\"0\"\n                :max=\"100\"\n                :precision=\"2\"\n                controls-position=\"right\"\n                style=\"width: 100%\">\n                <template #append>%</template>\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"CPU使用率\" prop=\"avg_cpu\">\n              <el-input-number \n                v-model=\"baselineForm.avg_cpu\" \n                :min=\"0\"\n                :max=\"100\"\n                :precision=\"2\"\n                controls-position=\"right\"\n                style=\"width: 100%\">\n                <template #append>%</template>\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"内存使用率\" prop=\"avg_memory\">\n              <el-input-number \n                v-model=\"baselineForm.avg_memory\" \n                :min=\"0\"\n                :max=\"100\"\n                :precision=\"2\"\n                controls-position=\"right\"\n                style=\"width: 100%\">\n                <template #append>%</template>\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否激活\">\n              <el-switch \n                v-model=\"baselineForm.is_active\"\n                active-text=\"激活\"\n                inactive-text=\"非激活\"\n                inline-prompt>\n              </el-switch>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showCreateDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitLoading\">\n            {{ editingBaseline ? '更新' : '创建' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 基准线详情对话框 -->\n    <el-dialog\n      v-model=\"showDetailDialog\"\n      title=\"基准线详情\"\n      width=\"750px\"\n      destroy-on-close>\n      \n      <div v-if=\"selectedBaseline\" class=\"baseline-detail\">\n        <el-row :gutter=\"20\" class=\"detail-header\">\n          <el-col :span=\"16\">\n            <div class=\"detail-title\">\n              <el-icon><TrendCharts /></el-icon>\n              <h3>{{ selectedBaseline.name }}</h3>\n              <el-tag :type=\"selectedBaseline.is_active ? 'success' : 'info'\" class=\"status-tag\">\n                {{ selectedBaseline.is_active ? '活跃' : '非活跃' }}\n              </el-tag>\n            </div>\n            <div class=\"detail-desc\">{{ selectedBaseline.description }}</div>\n          </el-col>\n          <el-col :span=\"8\">\n            <div class=\"detail-info\">\n              <div class=\"info-item\">\n                <el-icon><User /></el-icon>\n                <span>创建人: {{ selectedBaseline.creator }}</span>\n              </div>\n              <div class=\"info-item\">\n                <el-icon><Calendar /></el-icon>\n                <span>创建时间: {{ formatTime(selectedBaseline.create_time) }}</span>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-divider content-position=\"center\">性能指标</el-divider>\n        \n        <div class=\"metrics-cards\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"8\">\n              <el-card shadow=\"hover\" class=\"metric-card response-time\">\n                <div class=\"metric-title\">\n                  <el-icon><Timer /></el-icon>\n                  <span>平均响应时间</span>\n                </div>\n                <div class=\"metric-value\">{{ selectedBaseline.avg_response_time }} <span class=\"unit\">ms</span></div>\n              </el-card>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-card shadow=\"hover\" class=\"metric-card tps\">\n                <div class=\"metric-title\">\n                  <el-icon><PieChart /></el-icon>\n                  <span>平均TPS</span>\n                </div>\n                <div class=\"metric-value\">{{ selectedBaseline.avg_tps }}</div>\n              </el-card>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-card shadow=\"hover\" class=\"metric-card success-rate\">\n                <div class=\"metric-title\">\n                  <el-icon><CircleCheckFilled /></el-icon>\n                  <span>成功率</span>\n                </div>\n                <div class=\"metric-value\">{{ selectedBaseline.success_rate }}<span class=\"unit\">%</span></div>\n              </el-card>\n            </el-col>\n          </el-row>\n          \n          <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n            <el-col :span=\"12\">\n              <el-card shadow=\"hover\" class=\"metric-card cpu\">\n                <div class=\"metric-title\">\n                  <el-icon><CPU /></el-icon>\n                  <span>CPU使用率</span>\n                </div>\n                <div class=\"metric-value\">{{ selectedBaseline.avg_cpu }}<span class=\"unit\">%</span></div>\n              </el-card>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-card shadow=\"hover\" class=\"metric-card memory\">\n                <div class=\"metric-title\">\n                  <el-icon><Coin /></el-icon>\n                  <span>内存使用率</span>\n                </div>\n                <div class=\"metric-value\">{{ selectedBaseline.avg_memory }}<span class=\"unit\">%</span></div>\n              </el-card>\n            </el-col>\n          </el-row>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { \n  Plus, Search, Refresh, TrendCharts, View, Edit, Delete, \n  Timer, PieChart, CircleCheckFilled, Calendar, User, CPU, Coin, Document,\n  InfoFilled\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'BaselineManager',\n  components: {\n    Plus, Search, Refresh, TrendCharts, View, Edit, Delete, \n    Timer, PieChart, CircleCheckFilled, Calendar, User,  Coin, Document,\n    InfoFilled\n  },\n  props: {\n    projectId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  watch: {\n    // 监听对话框打开状态，加载任务列表\n    showCreateDialog(newVal) {\n      if (newVal && !this.editingBaseline) {\n        this.loadTasks()\n      }\n    }\n  },\n  data() {\n    return {\n      baselineList: [],\n      tableLoading: false,\n      submitLoading: false,\n      showCreateDialog: false,\n      showDetailDialog: false,\n      editingBaseline: null,\n      selectedBaseline: null,\n      selectedRows: [],\n      taskList: [], // 任务列表\n      lastTaskReport: null, // 任务的最新报告数据\n      \n      searchForm: {\n        name: ''\n      },\n      \n      pagination: {\n        current: 1,\n        size: 20,\n        total: 0\n      },\n      \n      baselineForm: {\n        name: '',\n        description: '',\n        task_id: null, // 添加任务ID字段\n        avg_response_time: 0,\n        avg_tps: 0,\n        success_rate: 0,\n        avg_cpu: 0,\n        avg_memory: 0,\n        is_active: true\n      },\n      \n      formRules: {\n        name: [\n          { required: true, message: '请输入基准线名称', trigger: 'blur' },\n          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }\n        ],\n        task_id: [\n          { required: true, message: '请选择关联任务', trigger: 'change' }\n        ],\n        avg_response_time: [\n          { required: true, message: '请输入平均响应时间', trigger: 'blur' },\n          { type: 'number', message: '平均响应时间必须是数字', trigger: 'blur' }\n        ],\n        avg_tps: [\n          { required: true, message: '请输入平均TPS', trigger: 'blur' },\n          { type: 'number', message: '平均TPS必须是数字', trigger: 'blur' }\n        ],\n        success_rate: [\n          { required: true, message: '请输入成功率', trigger: 'blur' },\n          { type: 'number', message: '成功率必须是数字', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.loadBaselines()\n  },\n  methods: {\n    // 加载性能任务列表\n    async loadTasks() {\n      try {\n        const response = await this.$api.getPerformanceTasks({\n          project_id: this.projectId,\n          no_page: true // 不分页，获取所有任务\n        })\n        \n        if (response.status === 200) {\n          this.taskList = response.data || []\n        }\n      } catch (error) {\n        console.error('加载任务列表失败:', error)\n        ElMessage.error('加载任务列表失败: ' + (error.message || '网络错误'))\n        this.taskList = []\n      }\n    },\n    \n    // 加载基准线列表\n    async loadBaselines() {\n      this.tableLoading = true\n      try {\n        const params = {\n          project_id: this.projectId,\n          page: this.pagination.current,\n          page_size: this.pagination.size,\n          name: this.searchForm.name || undefined\n        }\n        \n        const response = await this.$api.getBaselines(params)\n        \n        if (response.status === 200) {\n          let data = response.data || {}\n          \n          // 处理后端标准分页格式\n          if (data.results && Array.isArray(data.results)) {\n            this.baselineList = data.results\n            this.pagination.total = data.count || data.results.length\n          } \n          // 处理不同的数据结构 - 为了向后兼容\n          else if (Array.isArray(data)) {\n            this.baselineList = data\n            this.pagination.total = data.length\n          } else if (data.baselines && Array.isArray(data.baselines)) {\n            this.baselineList = data.baselines\n            this.pagination.total = data.total || data.baselines.length\n          } else {\n            // 其他情况，设置为空数组\n            this.baselineList = []\n            this.pagination.total = 0\n          }\n          \n          // 调试日志\n          console.log('获取到的基准线数据:', this.baselineList)\n        }\n      } catch (error) {\n        console.error('加载基准线失败:', error)\n        ElMessage.error('加载基准线失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n        // 设置默认值\n        this.baselineList = []\n        this.pagination.total = 0\n      } finally {\n        this.tableLoading = false\n      }\n    },\n    \n    // 重置搜索\n    resetSearch() {\n      this.searchForm.name = ''\n      this.pagination.current = 1\n      this.loadBaselines()\n    },\n    \n    // 处理选择变化\n    handleSelectionChange(selection) {\n      this.selectedRows = selection\n    },\n    \n    // 查看基准线\n    viewBaseline(baseline) {\n      this.selectedBaseline = baseline\n      this.showDetailDialog = true\n    },\n    \n    // 编辑基准线\n    editBaseline(baseline) {\n      this.editingBaseline = baseline\n      this.baselineForm = { ...baseline }\n      this.showCreateDialog = true\n    },\n    \n    // 删除基准线\n    async deleteBaseline(baseline) {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除基准线 \"${baseline.name}\" 吗？`,\n          '删除确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n        \n        const response = await this.$api.deleteBaseline(baseline.id)\n        \n        if (response.status === 204 || response.status === 200) {\n          ElMessage.success('删除成功')\n          this.loadBaselines()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除基准线失败:', error)\n          ElMessage.error('删除失败: ' + (error.message || '未知错误'))\n        }\n      }\n    },\n    \n    // 当任务选择变更时获取该任务最近的报告\n    async onTaskChange(taskId) {\n      if (!taskId) {\n        this.lastTaskReport = null\n        return\n      }\n      \n      try {\n        // 获取任务的最新报告\n        const response = await this.$api.getTaskReports({\n          task_id: taskId,\n          page: 1,\n          page_size: 1\n        })\n        \n        if (response.status === 200 && response.data.results && response.data.results.length > 0) {\n          this.lastTaskReport = response.data.results[0]\n          \n          // 如果性能指标都是0或空，可以自动填充最新报告的数据\n          if (\n            !this.baselineForm.avg_response_time && \n            !this.baselineForm.avg_tps && \n            !this.baselineForm.success_rate && \n            !this.baselineForm.avg_cpu && \n            !this.baselineForm.avg_memory\n          ) {\n            // 用户可能希望从报告中获取指标\n            ElMessageBox.confirm(\n              '是否要从最新报告中填充性能指标数据？',\n              '填充指标数据',\n              {\n                confirmButtonText: '是',\n                cancelButtonText: '否',\n                type: 'info'\n              }\n            ).then(() => {\n              // 用户确认后，填充指标数据\n              this.fillMetricsFromReport(this.lastTaskReport)\n            }).catch(() => {\n              // 用户取消，不做操作\n            })\n          }\n        } else {\n          this.lastTaskReport = null\n          ElMessage.warning('该任务没有测试报告数据')\n        }\n      } catch (error) {\n        console.error('获取任务报告失败:', error)\n        this.lastTaskReport = null\n      }\n    },\n    \n    // 从报告中填充性能指标\n    fillMetricsFromReport(report) {\n      if (!report) return\n      \n      this.baselineForm.avg_response_time = report.avgResponseTime || 0\n      this.baselineForm.avg_tps = report.avgTps || 0\n      this.baselineForm.success_rate = report.errorRate ? (100 - report.errorRate) : 100\n      this.baselineForm.avg_cpu = report.avgCpu || 0\n      this.baselineForm.avg_memory = report.avgMemory || 0\n      \n      ElMessage.success('已从最新报告中填充指标数据')\n    },\n    \n    // 提交表单\n    async submitForm() {\n      try {\n        const valid = await this.$refs.baselineFormRef.validate()\n        if (!valid) return\n        \n        this.submitLoading = true\n        \n        const params = {\n          ...this.baselineForm,\n          project_id: this.projectId\n        }\n        \n        // 添加task_id参数，这是必需的\n        if (!this.editingBaseline) {\n          // 如果没有选择任务，提示用户\n          if (!this.baselineForm.task_id) {\n            ElMessage.error('请选择关联的性能任务')\n            this.submitLoading = false\n            return\n          }\n          \n          // 如果所有性能指标都为0，提示用户\n          const allZero = \n            this.baselineForm.avg_response_time === 0 && \n            this.baselineForm.avg_tps === 0 && \n            this.baselineForm.avg_cpu === 0 && \n            this.baselineForm.avg_memory === 0\n            \n          if (allZero) {\n            const result = await ElMessageBox.confirm(\n              '您未填写任何性能指标值，系统将自动使用关联任务的最新报告数据作为基准线。是否继续？',\n              '确认创建',\n              {\n                confirmButtonText: '创建',\n                cancelButtonText: '取消',\n                type: 'warning'\n              }\n            ).catch(() => 'cancel')\n            \n            if (result === 'cancel') {\n              this.submitLoading = false\n              return\n            }\n          }\n        }\n        \n        // 调试日志：查看发送的数据\n        console.log('提交基准线数据:', params)\n        \n        let response\n        if (this.editingBaseline) {\n          response = await this.$api.updateBaseline(this.editingBaseline.id, params)\n        } else {\n          response = await this.$api.createBaseline(params)\n        }\n        \n        if (response.status === 200 || response.status === 201) {\n          ElMessage.success(this.editingBaseline ? '更新成功' : '创建成功')\n          this.showCreateDialog = false\n          this.loadBaselines()\n        }\n      } catch (error) {\n        console.error('提交失败:', error)\n        ElMessage.error('提交失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n      } finally {\n        this.submitLoading = false\n      }\n    },\n    \n    // 重置表单\n    resetForm() {\n      this.editingBaseline = null\n      this.baselineForm = {\n        name: '',\n        description: '',\n        task_id: null,\n        avg_response_time: 0,\n        avg_tps: 0,\n        success_rate: 0,\n        avg_cpu: 0,\n        avg_memory: 0,\n        is_active: true\n      }\n      this.$refs.baselineFormRef?.resetFields()\n    },\n    \n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '-'\n      return new Date(time).toLocaleString()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.baseline-manager {\n  padding: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.header-icon {\n  font-size: 20px;\n  color: var(--el-color-primary);\n}\n\n.header-title {\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.search-area {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #f9fafc;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.search-form {\n  margin: 0;\n}\n\n.baseline-table {\n  border-radius: 6px;\n  overflow: hidden;\n  margin-bottom: 20px;\n}\n\n.baseline-name, .task-name {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.baseline-name-text {\n  font-weight: 500;\n}\n\n.metrics-display {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.metric-tag {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n\n.time-display, .creator-display {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n}\n\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: right;\n}\n\n/* 表单样式 */\n.baseline-form {\n  padding: 10px 20px;\n}\n\n/* 详情样式 */\n.baseline-detail {\n  padding: 10px 0;\n}\n\n.detail-header {\n  margin-bottom: 20px;\n}\n\n.detail-title {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n}\n\n.detail-title h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.status-tag {\n  padding: 4px 8px;\n}\n\n.detail-desc {\n  color: #606266;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.detail-info {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n  justify-content: center;\n  height: 100%;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #606266;\n}\n\n.metrics-cards {\n  margin-top: 20px;\n}\n\n.metric-card {\n  border-radius: 8px;\n  height: 120px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding: 15px;\n}\n\n.metric-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #606266;\n  margin-bottom: 15px;\n}\n\n.metric-value {\n  font-size: 28px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.unit {\n  font-size: 16px;\n  font-weight: normal;\n  margin-left: 2px;\n  color: #606266;\n}\n\n.response-time {\n  border-left: 4px solid #409EFF;\n}\n\n.tps {\n  border-left: 4px solid #67C23A;\n}\n\n.success-rate {\n  border-left: 4px solid #E6A23C;\n}\n\n.cpu {\n  border-left: 4px solid #F56C6C;\n}\n\n.memory {\n  border-left: 4px solid #909399;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n\n.form-help-text {\n  margin-top: 5px;\n  color: #909399;\n  font-size: 12px;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  \n  .el-icon {\n    color: #409EFF;\n  }\n}\n</style>", "import { render } from \"./BaselineManager.vue?vue&type=template&id=40ec0df8&scoped=true\"\nimport script from \"./BaselineManager.vue?vue&type=script&lang=js\"\nexport * from \"./BaselineManager.vue?vue&type=script&lang=js\"\n\nimport \"./BaselineManager.vue?vue&type=style&index=0&id=40ec0df8&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-40ec0df8\"]])\n\nexport default __exports__", "import { render } from \"./PerformanceBaseline.vue?vue&type=template&id=104bba96&scoped=true\"\nimport script from \"./PerformanceBaseline.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceBaseline.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceBaseline.vue?vue&type=style&index=0&id=104bba96&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-104bba96\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_BaselineManager", "_ctx", "pro", "id", "onBaselineCreated", "$options", "handleBaselineCreated", "onBaselineUpdated", "handleBaselineUpdated", "onBaselineDeleted", "handleBaselineDeleted", "style", "_component_el_card", "shadow", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_component_el_icon", "_component_Trend<PERSON><PERSON>s", "_component_el_button", "type", "size", "onClick", "_cache", "$event", "$data", "showCreateDialog", "_component_Plus", "_hoisted_4", "_component_el_form", "inline", "model", "searchForm", "_component_el_form_item", "label", "_component_el_input", "name", "placeholder", "clearable", "loadBaselines", "_component_Search", "resetSearch", "_component_Refresh", "_createBlock", "_component_el_table", "data", "baselineList", "onSelectionChange", "handleSelectionChange", "border", "stripe", "_component_el_table_column", "width", "prop", "default", "scope", "_hoisted_5", "_hoisted_6", "_toDisplayString", "row", "_hoisted_7", "_component_Document", "task_name", "_hoisted_8", "_component_el_tooltip", "content", "placement", "effect", "_component_el_tag", "_component_Timer", "avg_response_time", "_component_<PERSON><PERSON><PERSON>", "avg_tps", "_component_CircleCheckFilled", "success_rate", "_hoisted_9", "_component_Calendar", "formatTime", "create_time", "_hoisted_10", "_component_User", "creator", "is_active", "fixed", "_hoisted_11", "circle", "viewBaseline", "_component_View", "editBaseline", "_component_Edit", "deleteBaseline", "_component_Delete", "tableLoading", "_hoisted_12", "_component_el_pagination", "background", "layout", "pagination", "current", "total", "onSizeChange", "onCurrentChange", "_component_el_dialog", "title", "editingBaseline", "onClose", "resetForm", "footer", "_hoisted_15", "submitForm", "loading", "submitLoading", "baselineForm", "rules", "formRules", "ref", "description", "rows", "_component_el_select", "task_id", "filterable", "onChange", "onTaskChange", "_Fragment", "_renderList", "taskList", "task", "_component_el_option", "key", "taskName", "value", "_hoisted_13", "_component_InfoFilled", "_component_el_divider", "_hoisted_14", "lastTaskReport", "fillMetricsFromReport", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_input_number", "min", "precision", "append", "max", "avg_cpu", "avg_memory", "_component_el_switch", "showDetailDialog", "selectedBaseline", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_component_CPU", "_hoisted_30", "_hoisted_31", "_component_Coin", "_hoisted_32", "components", "Plus", "Search", "Refresh", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View", "Edit", "Delete", "Timer", "<PERSON><PERSON><PERSON>", "CircleCheckFilled", "Calendar", "User", "Coin", "Document", "InfoFilled", "props", "projectId", "String", "Number", "required", "watch", "newVal", "this", "loadTasks", "selectedRows", "message", "trigger", "mounted", "methods", "response", "$api", "getPerformanceTasks", "project_id", "no_page", "status", "error", "console", "ElMessage", "params", "page", "page_size", "undefined", "getBaselines", "results", "Array", "isArray", "count", "length", "baselines", "log", "selection", "baseline", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "success", "taskId", "getTaskReports", "then", "catch", "warning", "report", "avgResponseTime", "avgTps", "errorRate", "avgCpu", "avgMemory", "valid", "$refs", "baselineFormRef", "validate", "allZero", "result", "updateBaseline", "createBaseline", "resetFields", "time", "Date", "toLocaleString", "__exports__", "BaselineManager", "computed", "mapState", "state", "baselineData", "$message", "duration", "render"], "sourceRoot": ""}