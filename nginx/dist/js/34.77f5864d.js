"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[34],{29034:function(e,a,t){t.r(a),t.d(a,{default:function(){return J}});var l=t(56768);const s={class:"performance-baseline-container"};function i(e,a,t,i,n,r){const o=(0,l.g2)("BaselineManager");return(0,l.uX)(),(0,l.CE)("div",s,[(0,l.bF)(o,{"project-id":e.pro.id,onBaselineCreated:r.handleBaselineCreated,onBaselineUpdated:r.handleBaselineUpdated,onBaselineDeleted:r.handleBaselineDeleted},null,8,["project-id","onBaselineCreated","onBaselineUpdated","onBaselineDeleted"])])}var n=t(60782),r=t(24232);const o={class:"baseline-manager"},d={class:"card-header"},c={class:"header-left"},u={class:"search-area"},m={class:"baseline-name"},p={class:"baseline-name-text"},b={class:"task-name"},g={class:"metrics-display"},_={class:"time-display"},k={class:"creator-display"},h={class:"action-buttons"},F={class:"pagination-wrapper"},f={key:0,class:"form-help-text"},v={style:{display:"flex","align-items":"center",gap:"10px"}},w={class:"dialog-footer"},y={key:0,class:"baseline-detail"},C={class:"detail-title"},L={class:"detail-desc"},B={class:"detail-info"},V={class:"info-item"},T={class:"info-item"},x={class:"metrics-cards"},D={class:"metric-title"},R={class:"metric-value"},z={class:"metric-title"},U={class:"metric-value"},W={class:"metric-title"},S={class:"metric-value"},P={class:"metric-title"},$={class:"metric-value"},j={class:"metric-title"},I={class:"metric-value"};function X(e,a,t,s,i,n){const X=(0,l.g2)("TrendCharts"),A=(0,l.g2)("el-icon"),E=(0,l.g2)("Plus"),M=(0,l.g2)("el-button"),q=(0,l.g2)("el-input"),Q=(0,l.g2)("el-form-item"),N=(0,l.g2)("Search"),H=(0,l.g2)("Refresh"),K=(0,l.g2)("el-form"),G=(0,l.g2)("el-table-column"),J=(0,l.g2)("Document"),O=(0,l.g2)("Timer"),Y=(0,l.g2)("el-tag"),Z=(0,l.g2)("el-tooltip"),ee=(0,l.g2)("PieChart"),ae=(0,l.g2)("CircleCheckFilled"),te=(0,l.g2)("Calendar"),le=(0,l.g2)("User"),se=(0,l.g2)("View"),ie=(0,l.g2)("Edit"),ne=(0,l.g2)("Delete"),re=(0,l.g2)("el-table"),oe=(0,l.g2)("el-pagination"),de=(0,l.g2)("el-card"),ce=(0,l.g2)("el-option"),ue=(0,l.g2)("el-select"),me=(0,l.g2)("InfoFilled"),pe=(0,l.g2)("el-divider"),be=(0,l.g2)("el-input-number"),ge=(0,l.g2)("el-col"),_e=(0,l.g2)("el-row"),ke=(0,l.g2)("el-switch"),he=(0,l.g2)("el-dialog"),Fe=(0,l.g2)("CPU"),fe=(0,l.g2)("Coin"),ve=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",o,[(0,l.bF)(de,{class:"box-card",shadow:"hover"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",d,[(0,l.Lk)("div",c,[(0,l.bF)(A,{class:"header-icon"},{default:(0,l.k6)(()=>[(0,l.bF)(X)]),_:1}),a[17]||(a[17]=(0,l.Lk)("span",{class:"header-title"},"基准线管理",-1))]),(0,l.bF)(M,{type:"primary",size:"small",onClick:a[0]||(a[0]=e=>i.showCreateDialog=!0)},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(E)]),_:1}),a[18]||(a[18]=(0,l.eW)(" 创建基准线 "))]),_:1,__:[18]})])]),default:(0,l.k6)(()=>[(0,l.Lk)("div",u,[(0,l.bF)(K,{inline:!0,model:i.searchForm,class:"search-form"},{default:(0,l.k6)(()=>[(0,l.bF)(Q,{label:"基准线名称"},{default:(0,l.k6)(()=>[(0,l.bF)(q,{modelValue:i.searchForm.name,"onUpdate:modelValue":a[1]||(a[1]=e=>i.searchForm.name=e),placeholder:"请输入基准线名称",clearable:"",size:"small","prefix-icon":"Search",style:{width:"220px"}},null,8,["modelValue"])]),_:1}),(0,l.bF)(Q,null,{default:(0,l.k6)(()=>[(0,l.bF)(M,{type:"primary",size:"small",onClick:n.loadBaselines},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(N)]),_:1}),a[19]||(a[19]=(0,l.eW)(" 搜索 "))]),_:1,__:[19]},8,["onClick"]),(0,l.bF)(M,{size:"small",onClick:n.resetSearch},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(H)]),_:1}),a[20]||(a[20]=(0,l.eW)(" 重置 "))]),_:1,__:[20]},8,["onClick"])]),_:1})]),_:1},8,["model"])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(re,{data:i.baselineList,"empty-text":"暂无基准线数据",onSelectionChange:n.handleSelectionChange,border:"",stripe:"","highlight-current-row":"",class:"baseline-table"},{default:(0,l.k6)(()=>[(0,l.bF)(G,{type:"selection",width:"55"}),(0,l.bF)(G,{label:"基准线名称",prop:"name","min-width":"200"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",m,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(X)]),_:1}),(0,l.Lk)("span",p,(0,r.v_)(e.row.name),1)])]),_:1}),(0,l.bF)(G,{label:"描述",prop:"description","min-width":"200","show-overflow-tooltip":""}),(0,l.bF)(G,{label:"关联任务",prop:"task_name","min-width":"150"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",b,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(J)]),_:1}),(0,l.Lk)("span",null,(0,r.v_)(e.row.task_name),1)])]),_:1}),(0,l.bF)(G,{label:"基准指标","min-width":"300"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",g,[(0,l.bF)(Z,{content:"平均响应时间",placement:"top",effect:"light"},{default:(0,l.k6)(()=>[(0,l.bF)(Y,{size:"small",class:"metric-tag",effect:"plain"},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(O)]),_:1}),(0,l.eW)(" "+(0,r.v_)(e.row.avg_response_time)+"ms ",1)]),_:2},1024)]),_:2},1024),(0,l.bF)(Z,{content:"每秒事务数",placement:"top",effect:"light"},{default:(0,l.k6)(()=>[(0,l.bF)(Y,{size:"small",type:"success",class:"metric-tag",effect:"plain"},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(ee)]),_:1}),(0,l.eW)(" "+(0,r.v_)(e.row.avg_tps)+" TPS ",1)]),_:2},1024)]),_:2},1024),(0,l.bF)(Z,{content:"成功率",placement:"top",effect:"light"},{default:(0,l.k6)(()=>[(0,l.bF)(Y,{size:"small",type:"warning",class:"metric-tag",effect:"plain"},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(ae)]),_:1}),(0,l.eW)(" "+(0,r.v_)(e.row.success_rate)+"% ",1)]),_:2},1024)]),_:2},1024)])]),_:1}),(0,l.bF)(G,{label:"创建时间",prop:"create_time",width:"180"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",_,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(te)]),_:1}),(0,l.Lk)("span",null,(0,r.v_)(n.formatTime(e.row.create_time)),1)])]),_:1}),(0,l.bF)(G,{label:"创建人",prop:"creator",width:"100"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",k,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(le)]),_:1}),(0,l.Lk)("span",null,(0,r.v_)(e.row.creator),1)])]),_:1}),(0,l.bF)(G,{label:"状态",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(Y,{type:e.row.is_active?"success":"info",size:"small",effect:"dark"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(e.row.is_active?"活跃":"非活跃"),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(G,{label:"操作",width:"250",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",h,[(0,l.bF)(Z,{content:"查看详情",placement:"top"},{default:(0,l.k6)(()=>[(0,l.bF)(M,{type:"primary",size:"small",circle:"",onClick:a=>n.viewBaseline(e.row)},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(se)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),(0,l.bF)(Z,{content:"编辑基准线",placement:"top"},{default:(0,l.k6)(()=>[(0,l.bF)(M,{type:"warning",size:"small",circle:"",onClick:a=>n.editBaseline(e.row)},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(ie)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),(0,l.bF)(Z,{content:"删除基准线",placement:"top"},{default:(0,l.k6)(()=>[(0,l.bF)(M,{type:"danger",size:"small",circle:"",onClick:a=>n.deleteBaseline(e.row)},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(ne)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[ve,i.tableLoading]]),(0,l.Lk)("div",F,[(0,l.bF)(oe,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":i.pagination.current,"onUpdate:currentPage":a[2]||(a[2]=e=>i.pagination.current=e),"page-size":i.pagination.size,"onUpdate:pageSize":a[3]||(a[3]=e=>i.pagination.size=e),"page-sizes":[10,20,50,100],total:i.pagination.total,onSizeChange:n.loadBaselines,onCurrentChange:n.loadBaselines},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),(0,l.bF)(he,{modelValue:i.showCreateDialog,"onUpdate:modelValue":a[15]||(a[15]=e=>i.showCreateDialog=e),title:i.editingBaseline?"编辑基准线":"创建基准线",width:"650px",onClose:n.resetForm,"destroy-on-close":""},{footer:(0,l.k6)(()=>[(0,l.Lk)("span",w,[(0,l.bF)(M,{onClick:a[14]||(a[14]=e=>i.showCreateDialog=!1)},{default:(0,l.k6)(()=>a[28]||(a[28]=[(0,l.eW)("取消")])),_:1,__:[28]}),(0,l.bF)(M,{type:"primary",onClick:n.submitForm,loading:i.submitLoading},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(i.editingBaseline?"更新":"创建"),1)]),_:1},8,["onClick","loading"])])]),default:(0,l.k6)(()=>[(0,l.bF)(K,{model:i.baselineForm,rules:i.formRules,ref:"baselineFormRef","label-width":"120px",class:"baseline-form"},{default:(0,l.k6)(()=>[(0,l.bF)(Q,{label:"基准线名称",prop:"name"},{default:(0,l.k6)(()=>[(0,l.bF)(q,{modelValue:i.baselineForm.name,"onUpdate:modelValue":a[4]||(a[4]=e=>i.baselineForm.name=e),placeholder:"请输入基准线名称","prefix-icon":"Document"},null,8,["modelValue"])]),_:1}),(0,l.bF)(Q,{label:"描述",prop:"description"},{default:(0,l.k6)(()=>[(0,l.bF)(q,{modelValue:i.baselineForm.description,"onUpdate:modelValue":a[5]||(a[5]=e=>i.baselineForm.description=e),type:"textarea",rows:3,placeholder:"请输入基准线描述"},null,8,["modelValue"])]),_:1}),i.editingBaseline?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.Wv)(Q,{key:0,label:"关联任务",prop:"task_id"},{default:(0,l.k6)(()=>[(0,l.bF)(ue,{modelValue:i.baselineForm.task_id,"onUpdate:modelValue":a[6]||(a[6]=e=>i.baselineForm.task_id=e),placeholder:"请选择关联的性能任务",filterable:"",style:{width:"100%"},onChange:n.onTaskChange},{default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(i.taskList,e=>((0,l.uX)(),(0,l.Wv)(ce,{key:e.id,label:e.taskName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),i.baselineForm.task_id?((0,l.uX)(),(0,l.CE)("div",f,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(me)]),_:1}),a[21]||(a[21]=(0,l.Lk)("span",null,"如不填写指标值，将自动使用该任务的最近一次报告数据作为基准线",-1))])):(0,l.Q3)("",!0)]),_:1})),(0,l.bF)(pe,{"content-position":"center"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",v,[a[23]||(a[23]=(0,l.Lk)("span",null,"性能指标",-1)),i.lastTaskReport&&!i.editingBaseline?((0,l.uX)(),(0,l.Wv)(M,{key:0,type:"text",onClick:a[7]||(a[7]=e=>n.fillMetricsFromReport(i.lastTaskReport))},{default:(0,l.k6)(()=>[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(O)]),_:1}),a[22]||(a[22]=(0,l.eW)(" 从最新报告填充 "))]),_:1,__:[22]})):(0,l.Q3)("",!0)])]),_:1}),(0,l.bF)(_e,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(ge,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(Q,{label:"平均响应时间",prop:"avg_response_time"},{default:(0,l.k6)(()=>[(0,l.bF)(be,{modelValue:i.baselineForm.avg_response_time,"onUpdate:modelValue":a[8]||(a[8]=e=>i.baselineForm.avg_response_time=e),min:0,precision:2,"controls-position":"right",style:{width:"100%"}},{append:(0,l.k6)(()=>a[24]||(a[24]=[(0,l.eW)("ms")])),_:1},8,["modelValue"])]),_:1})]),_:1}),(0,l.bF)(ge,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(Q,{label:"平均TPS",prop:"avg_tps"},{default:(0,l.k6)(()=>[(0,l.bF)(be,{modelValue:i.baselineForm.avg_tps,"onUpdate:modelValue":a[9]||(a[9]=e=>i.baselineForm.avg_tps=e),min:0,precision:2,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),(0,l.bF)(_e,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(ge,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(Q,{label:"成功率",prop:"success_rate"},{default:(0,l.k6)(()=>[(0,l.bF)(be,{modelValue:i.baselineForm.success_rate,"onUpdate:modelValue":a[10]||(a[10]=e=>i.baselineForm.success_rate=e),min:0,max:100,precision:2,"controls-position":"right",style:{width:"100%"}},{append:(0,l.k6)(()=>a[25]||(a[25]=[(0,l.eW)("%")])),_:1},8,["modelValue"])]),_:1})]),_:1}),(0,l.bF)(ge,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(Q,{label:"CPU使用率",prop:"avg_cpu"},{default:(0,l.k6)(()=>[(0,l.bF)(be,{modelValue:i.baselineForm.avg_cpu,"onUpdate:modelValue":a[11]||(a[11]=e=>i.baselineForm.avg_cpu=e),min:0,max:100,precision:2,"controls-position":"right",style:{width:"100%"}},{append:(0,l.k6)(()=>a[26]||(a[26]=[(0,l.eW)("%")])),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),(0,l.bF)(_e,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(ge,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(Q,{label:"内存使用率",prop:"avg_memory"},{default:(0,l.k6)(()=>[(0,l.bF)(be,{modelValue:i.baselineForm.avg_memory,"onUpdate:modelValue":a[12]||(a[12]=e=>i.baselineForm.avg_memory=e),min:0,max:100,precision:2,"controls-position":"right",style:{width:"100%"}},{append:(0,l.k6)(()=>a[27]||(a[27]=[(0,l.eW)("%")])),_:1},8,["modelValue"])]),_:1})]),_:1}),(0,l.bF)(ge,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(Q,{label:"是否激活"},{default:(0,l.k6)(()=>[(0,l.bF)(ke,{modelValue:i.baselineForm.is_active,"onUpdate:modelValue":a[13]||(a[13]=e=>i.baselineForm.is_active=e),"active-text":"激活","inactive-text":"非激活","inline-prompt":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title","onClose"]),(0,l.bF)(he,{modelValue:i.showDetailDialog,"onUpdate:modelValue":a[16]||(a[16]=e=>i.showDetailDialog=e),title:"基准线详情",width:"750px","destroy-on-close":""},{default:(0,l.k6)(()=>[i.selectedBaseline?((0,l.uX)(),(0,l.CE)("div",y,[(0,l.bF)(_e,{gutter:20,class:"detail-header"},{default:(0,l.k6)(()=>[(0,l.bF)(ge,{span:16},{default:(0,l.k6)(()=>[(0,l.Lk)("div",C,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(X)]),_:1}),(0,l.Lk)("h3",null,(0,r.v_)(i.selectedBaseline.name),1),(0,l.bF)(Y,{type:i.selectedBaseline.is_active?"success":"info",class:"status-tag"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(i.selectedBaseline.is_active?"活跃":"非活跃"),1)]),_:1},8,["type"])]),(0,l.Lk)("div",L,(0,r.v_)(i.selectedBaseline.description),1)]),_:1}),(0,l.bF)(ge,{span:8},{default:(0,l.k6)(()=>[(0,l.Lk)("div",B,[(0,l.Lk)("div",V,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(le)]),_:1}),(0,l.Lk)("span",null,"创建人: "+(0,r.v_)(i.selectedBaseline.creator),1)]),(0,l.Lk)("div",T,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(te)]),_:1}),(0,l.Lk)("span",null,"创建时间: "+(0,r.v_)(n.formatTime(i.selectedBaseline.create_time)),1)])])]),_:1})]),_:1}),(0,l.bF)(pe,{"content-position":"center"},{default:(0,l.k6)(()=>a[29]||(a[29]=[(0,l.eW)("性能指标")])),_:1,__:[29]}),(0,l.Lk)("div",x,[(0,l.bF)(_e,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(ge,{span:8},{default:(0,l.k6)(()=>[(0,l.bF)(de,{shadow:"hover",class:"metric-card response-time"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",D,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(O)]),_:1}),a[30]||(a[30]=(0,l.Lk)("span",null,"平均响应时间",-1))]),(0,l.Lk)("div",R,[(0,l.eW)((0,r.v_)(i.selectedBaseline.avg_response_time)+" ",1),a[31]||(a[31]=(0,l.Lk)("span",{class:"unit"},"ms",-1))])]),_:1})]),_:1}),(0,l.bF)(ge,{span:8},{default:(0,l.k6)(()=>[(0,l.bF)(de,{shadow:"hover",class:"metric-card tps"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",z,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(ee)]),_:1}),a[32]||(a[32]=(0,l.Lk)("span",null,"平均TPS",-1))]),(0,l.Lk)("div",U,(0,r.v_)(i.selectedBaseline.avg_tps),1)]),_:1})]),_:1}),(0,l.bF)(ge,{span:8},{default:(0,l.k6)(()=>[(0,l.bF)(de,{shadow:"hover",class:"metric-card success-rate"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",W,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(ae)]),_:1}),a[33]||(a[33]=(0,l.Lk)("span",null,"成功率",-1))]),(0,l.Lk)("div",S,[(0,l.eW)((0,r.v_)(i.selectedBaseline.success_rate),1),a[34]||(a[34]=(0,l.Lk)("span",{class:"unit"},"%",-1))])]),_:1})]),_:1})]),_:1}),(0,l.bF)(_e,{gutter:20,style:{"margin-top":"20px"}},{default:(0,l.k6)(()=>[(0,l.bF)(ge,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(de,{shadow:"hover",class:"metric-card cpu"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",P,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(Fe)]),_:1}),a[35]||(a[35]=(0,l.Lk)("span",null,"CPU使用率",-1))]),(0,l.Lk)("div",$,[(0,l.eW)((0,r.v_)(i.selectedBaseline.avg_cpu),1),a[36]||(a[36]=(0,l.Lk)("span",{class:"unit"},"%",-1))])]),_:1})]),_:1}),(0,l.bF)(ge,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(de,{shadow:"hover",class:"metric-card memory"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",j,[(0,l.bF)(A,null,{default:(0,l.k6)(()=>[(0,l.bF)(fe)]),_:1}),a[37]||(a[37]=(0,l.Lk)("span",null,"内存使用率",-1))]),(0,l.Lk)("div",I,[(0,l.eW)((0,r.v_)(i.selectedBaseline.avg_memory),1),a[38]||(a[38]=(0,l.Lk)("span",{class:"unit"},"%",-1))])]),_:1})]),_:1})]),_:1})])])):(0,l.Q3)("",!0)]),_:1},8,["modelValue"])])}var A=t(51219),E=t(12933),M=t(57477),q={name:"BaselineManager",components:{Plus:M.Plus,Search:M.Search,Refresh:M.Refresh,TrendCharts:M.TrendCharts,View:M.View,Edit:M.Edit,Delete:M.Delete,Timer:M.Timer,PieChart:M.PieChart,CircleCheckFilled:M.CircleCheckFilled,Calendar:M.Calendar,User:M.User,Coin:M.Coin,Document:M.Document,InfoFilled:M.InfoFilled},props:{projectId:{type:[String,Number],required:!0}},watch:{showCreateDialog(e){e&&!this.editingBaseline&&this.loadTasks()}},data(){return{baselineList:[],tableLoading:!1,submitLoading:!1,showCreateDialog:!1,showDetailDialog:!1,editingBaseline:null,selectedBaseline:null,selectedRows:[],taskList:[],lastTaskReport:null,searchForm:{name:""},pagination:{current:1,size:20,total:0},baselineForm:{name:"",description:"",task_id:null,avg_response_time:0,avg_tps:0,success_rate:0,avg_cpu:0,avg_memory:0,is_active:!0},formRules:{name:[{required:!0,message:"请输入基准线名称",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],task_id:[{required:!0,message:"请选择关联任务",trigger:"change"}],avg_response_time:[{required:!0,message:"请输入平均响应时间",trigger:"blur"},{type:"number",message:"平均响应时间必须是数字",trigger:"blur"}],avg_tps:[{required:!0,message:"请输入平均TPS",trigger:"blur"},{type:"number",message:"平均TPS必须是数字",trigger:"blur"}],success_rate:[{required:!0,message:"请输入成功率",trigger:"blur"},{type:"number",message:"成功率必须是数字",trigger:"blur"}]}}},mounted(){this.loadBaselines()},methods:{async loadTasks(){try{const e=await this.$api.getPerformanceTasks({project_id:this.projectId,no_page:!0});200===e.status&&(this.taskList=e.data||[])}catch(e){console.error("加载任务列表失败:",e),A.nk.error("加载任务列表失败: "+(e.message||"网络错误")),this.taskList=[]}},async loadBaselines(){this.tableLoading=!0;try{const e={project_id:this.projectId,page:this.pagination.current,page_size:this.pagination.size,name:this.searchForm.name||void 0},a=await this.$api.getBaselines(e);if(200===a.status){let e=a.data||{};e.results&&Array.isArray(e.results)?(this.baselineList=e.results,this.pagination.total=e.count||e.results.length):Array.isArray(e)?(this.baselineList=e,this.pagination.total=e.length):e.baselines&&Array.isArray(e.baselines)?(this.baselineList=e.baselines,this.pagination.total=e.total||e.baselines.length):(this.baselineList=[],this.pagination.total=0),console.log("获取到的基准线数据:",this.baselineList)}}catch(e){console.error("加载基准线失败:",e),A.nk.error("加载基准线失败: "+(e.response?.data?.message||e.message||"网络错误")),this.baselineList=[],this.pagination.total=0}finally{this.tableLoading=!1}},resetSearch(){this.searchForm.name="",this.pagination.current=1,this.loadBaselines()},handleSelectionChange(e){this.selectedRows=e},viewBaseline(e){this.selectedBaseline=e,this.showDetailDialog=!0},editBaseline(e){this.editingBaseline=e,this.baselineForm={...e},this.showCreateDialog=!0},async deleteBaseline(e){try{await E.s.confirm(`确定要删除基准线 "${e.name}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=await this.$api.deleteBaseline(e.id);204!==a.status&&200!==a.status||(A.nk.success("删除成功"),this.loadBaselines())}catch(a){"cancel"!==a&&(console.error("删除基准线失败:",a),A.nk.error("删除失败: "+(a.message||"未知错误")))}},async onTaskChange(e){if(e)try{const a=await this.$api.getTaskReports({task_id:e,page:1,page_size:1});200===a.status&&a.data.results&&a.data.results.length>0?(this.lastTaskReport=a.data.results[0],this.baselineForm.avg_response_time||this.baselineForm.avg_tps||this.baselineForm.success_rate||this.baselineForm.avg_cpu||this.baselineForm.avg_memory||E.s.confirm("是否要从最新报告中填充性能指标数据？","填充指标数据",{confirmButtonText:"是",cancelButtonText:"否",type:"info"}).then(()=>{this.fillMetricsFromReport(this.lastTaskReport)}).catch(()=>{})):(this.lastTaskReport=null,A.nk.warning("该任务没有测试报告数据"))}catch(a){console.error("获取任务报告失败:",a),this.lastTaskReport=null}else this.lastTaskReport=null},fillMetricsFromReport(e){e&&(this.baselineForm.avg_response_time=e.avgResponseTime||0,this.baselineForm.avg_tps=e.avgTps||0,this.baselineForm.success_rate=e.errorRate?100-e.errorRate:100,this.baselineForm.avg_cpu=e.avgCpu||0,this.baselineForm.avg_memory=e.avgMemory||0,A.nk.success("已从最新报告中填充指标数据"))},async submitForm(){try{const e=await this.$refs.baselineFormRef.validate();if(!e)return;this.submitLoading=!0;const a={...this.baselineForm,project_id:this.projectId};if(!this.editingBaseline){if(!this.baselineForm.task_id)return A.nk.error("请选择关联的性能任务"),void(this.submitLoading=!1);const e=0===this.baselineForm.avg_response_time&&0===this.baselineForm.avg_tps&&0===this.baselineForm.avg_cpu&&0===this.baselineForm.avg_memory;if(e){const e=await E.s.confirm("您未填写任何性能指标值，系统将自动使用关联任务的最新报告数据作为基准线。是否继续？","确认创建",{confirmButtonText:"创建",cancelButtonText:"取消",type:"warning"}).catch(()=>"cancel");if("cancel"===e)return void(this.submitLoading=!1)}}let t;console.log("提交基准线数据:",a),t=this.editingBaseline?await this.$api.updateBaseline(this.editingBaseline.id,a):await this.$api.createBaseline(a),200!==t.status&&201!==t.status||(A.nk.success(this.editingBaseline?"更新成功":"创建成功"),this.showCreateDialog=!1,this.loadBaselines())}catch(e){console.error("提交失败:",e),A.nk.error("提交失败: "+(e.response?.data?.message||e.message||"网络错误"))}finally{this.submitLoading=!1}},resetForm(){this.editingBaseline=null,this.baselineForm={name:"",description:"",task_id:null,avg_response_time:0,avg_tps:0,success_rate:0,avg_cpu:0,avg_memory:0,is_active:!0},this.$refs.baselineFormRef?.resetFields()},formatTime(e){return e?new Date(e).toLocaleString():"-"}}},Q=t(71241);const N=(0,Q.A)(q,[["render",X],["__scopeId","data-v-40ec0df8"]]);var H=N,K={name:"PerformanceBaseline",components:{BaselineManager:H},computed:{...(0,n.aH)({pro:e=>e.pro})},methods:{handleBaselineCreated(e){this.$message({type:"success",message:`基准线创建成功: ${e.name}`,duration:3e3})},handleBaselineUpdated(e){this.$message({type:"success",message:`基准线更新成功: ${e.name}`,duration:3e3})},handleBaselineDeleted(e){this.$message({type:"success",message:`基准线删除成功: ${e.name}`,duration:3e3})}}};const G=(0,Q.A)(K,[["render",i],["__scopeId","data-v-104bba96"]]);var J=G}}]);
//# sourceMappingURL=34.77f5864d.js.map