"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[911],{32911:function(e,l,a){a.r(l),a.d(l,{default:function(){return Q}});var t=a(56768),o=a(24232);const r={class:"user-management"},s={class:"page-header"},d={class:"action-buttons"},i={class:"card-header"},n={class:"header-title"},u={class:"search-inputs"},m={class:"button-group"},c={class:"card-header"},p={class:"header-title"},b={key:0,class:"header-count"},h={class:"username-cell"},k={class:"username-text"},g={key:1},_={class:"mobile-cell"},F={class:"email-cell"},f={class:"project-tags"},w={class:"action-column"},y={class:"pagination-container"},C={class:"dialog-content"},U={class:"dialog-footer"},V={class:"dialog-content"},x={class:"dialog-footer"},v={class:"dialog-content"},L={class:"user-option"},P={class:"dialog-footer"};function j(e,l,a,j,W,z){const R=(0,t.g2)("el-button"),$=(0,t.g2)("Search"),A=(0,t.g2)("el-icon"),D=(0,t.g2)("User",!0),E=(0,t.g2)("el-input"),Q=(0,t.g2)("el-form-item"),I=(0,t.g2)("Iphone"),X=(0,t.g2)("Message"),S=(0,t.g2)("Folder"),q=(0,t.g2)("el-form"),K=(0,t.g2)("el-card"),M=(0,t.g2)("List"),B=(0,t.g2)("el-tag"),T=(0,t.g2)("el-table-column"),H=(0,t.g2)("el-avatar"),N=(0,t.g2)("el-tooltip"),O=(0,t.g2)("el-table"),G=(0,t.g2)("el-pagination"),J=(0,t.g2)("Stamp"),Y=(0,t.g2)("Lock"),Z=(0,t.g2)("el-dialog"),ee=(0,t.g2)("el-option"),le=(0,t.g2)("el-select"),ae=(0,t.g2)("el-scrollbar"),te=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",r,[(0,t.Lk)("div",s,[(0,t.Lk)("div",d,[(0,t.bF)(R,{onClick:z.clickAdd,type:"primary",icon:j.Plus},{default:(0,t.k6)(()=>l[21]||(l[21]=[(0,t.Lk)("span",null,"新增用户",-1)])),_:1,__:[21]},8,["onClick","icon"]),(0,t.bF)(R,{onClick:z.clickAddPro,type:"success",icon:j.UserFilled},{default:(0,t.k6)(()=>l[22]||(l[22]=[(0,t.Lk)("span",null,"添加项目成员",-1)])),_:1,__:[22]},8,["onClick","icon"])])]),(0,t.bF)(ae,{height:"calc(100vh - 125px)"},{default:(0,t.k6)(()=>[(0,t.bF)(K,{class:"search-card"},{header:(0,t.k6)(()=>[(0,t.Lk)("div",i,[(0,t.Lk)("span",n,[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)($)]),_:1}),l[23]||(l[23]=(0,t.eW)(" 搜索条件 "))])])]),default:(0,t.k6)(()=>[(0,t.bF)(q,{model:W.QueryCondition,"label-width":"80px","label-position":"left",inline:"",class:"search-form"},{default:(0,t.k6)(()=>[(0,t.Lk)("div",u,[(0,t.bF)(Q,{label:"用户名"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.QueryCondition.username,"onUpdate:modelValue":l[0]||(l[0]=e=>W.QueryCondition.username=e),placeholder:"请输入用户名",clearable:""},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(D)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"手机号"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.QueryCondition.mobile,"onUpdate:modelValue":l[1]||(l[1]=e=>W.QueryCondition.mobile=e),placeholder:"请输入手机号码",clearable:""},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(I)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"邮箱"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.QueryCondition.email,"onUpdate:modelValue":l[2]||(l[2]=e=>W.QueryCondition.email=e),placeholder:"请输入邮箱",clearable:""},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(X)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"所属项目"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.QueryCondition.project_name,"onUpdate:modelValue":l[3]||(l[3]=e=>W.QueryCondition.project_name=e),placeholder:"请输入项目名称",clearable:""},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(S)]),_:1})]),_:1},8,["modelValue"])]),_:1})]),(0,t.bF)(Q,{class:"query-buttons"},{default:(0,t.k6)(()=>[(0,t.Lk)("div",m,[(0,t.bF)(R,{onClick:z.resetForm,icon:j.Refresh},{default:(0,t.k6)(()=>l[24]||(l[24]=[(0,t.eW)("重置")])),_:1,__:[24]},8,["onClick","icon"]),(0,t.bF)(R,{type:"primary",onClick:z.submitForm,icon:j.Search},{default:(0,t.k6)(()=>l[25]||(l[25]=[(0,t.eW)("查询")])),_:1,__:[25]},8,["onClick","icon"])])]),_:1})]),_:1},8,["model"])]),_:1}),(0,t.bF)(K,{class:"table-card"},{header:(0,t.k6)(()=>[(0,t.Lk)("div",c,[(0,t.Lk)("span",p,[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(M)]),_:1}),l[26]||(l[26]=(0,t.eW)(" 用户列表 "))]),W.Pager.count?((0,t.uX)(),(0,t.CE)("span",b,[l[27]||(l[27]=(0,t.eW)(" 共 ")),(0,t.bF)(B,{type:"info",effect:"plain"},{default:(0,t.k6)(()=>[(0,t.eW)((0,o.v_)(W.Pager.count),1)]),_:1}),l[28]||(l[28]=(0,t.eW)(" 条记录 "))])):(0,t.Q3)("",!0)])]),default:(0,t.k6)(()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(O,{data:W.UserLsit,stripe:"",border:"","element-loading-text":"加载中...","element-loading-background":"rgba(255, 255, 255, 0.8)","empty-text":"暂无数据","row-key":"id",class:"user-table","header-cell-style":{background:"#f6f9fc",color:"#2c3e50"}},{default:(0,t.k6)(()=>[(0,t.bF)(T,{label:"序号",align:"center",width:"80"},{default:(0,t.k6)(e=>[(0,t.bF)(B,{type:"info",effect:"plain",class:"index-tag"},{default:(0,t.k6)(()=>[(0,t.eW)((0,o.v_)(e.$index+1),1)]),_:2},1024)]),_:1}),(0,t.bF)(T,{label:"用户名",prop:"username",align:"center"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",h,[(0,t.bF)(H,{size:28,icon:j.User,class:"user-avatar"},null,8,["icon"]),(0,t.Lk)("span",k,(0,o.v_)(e.row.username),1)])]),_:1}),(0,t.bF)(T,{label:"用户标签",prop:"weChat_name",align:"center"},{default:(0,t.k6)(e=>[e.row.weChat_name?((0,t.uX)(),(0,t.Wv)(B,{key:0,effect:"light",class:"wechat-tag"},{default:(0,t.k6)(()=>[(0,t.eW)((0,o.v_)(e.row.weChat_name),1)]),_:2},1024)):((0,t.uX)(),(0,t.CE)("span",g,"-"))]),_:1}),(0,t.bF)(T,{label:"手机号码",prop:"mobile",align:"center"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",_,[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(I)]),_:1}),(0,t.Lk)("span",null,(0,o.v_)(e.row.mobile||"-"),1)])]),_:1}),(0,t.bF)(T,{label:"邮箱",prop:"email",align:"center","show-overflow-tooltip":""},{default:(0,t.k6)(e=>[(0,t.Lk)("div",F,[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(X)]),_:1}),(0,t.Lk)("span",null,(0,o.v_)(e.row.email||"-"),1)])]),_:1}),(0,t.bF)(T,{label:"所属项目","show-overflow-tooltip":"",align:"center"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",f,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.row.project,(e,l)=>((0,t.uX)(),(0,t.Wv)(B,{key:l,size:"small",effect:"plain",class:"project-tag"},{default:(0,t.k6)(()=>[(0,t.Lk)("span",null,(0,o.v_)(e.name),1)]),_:2},1024))),128)),e.row.project&&0!==e.row.project.length?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.Wv)(B,{key:0,type:"info",size:"small",effect:"plain"},{default:(0,t.k6)(()=>l[29]||(l[29]=[(0,t.eW)(" 暂无项目 ")])),_:1,__:[29]}))])]),_:1}),(0,t.bF)(T,{label:"操作",width:"200",align:"center",fixed:"right"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",w,[(0,t.bF)(N,{content:"编辑用户",placement:"top","show-after":500},{default:(0,t.k6)(()=>[(0,t.bF)(R,{onClick:l=>z.clickEdit(e.row),size:"small",type:"primary",circle:"",plain:"",class:"action-btn"},{default:(0,t.k6)(()=>l[30]||(l[30]=[(0,t.eW)("编辑")])),_:2,__:[30]},1032,["onClick"])]),_:2},1024),(0,t.bF)(N,{content:"删除用户",placement:"top","show-after":500},{default:(0,t.k6)(()=>[(0,t.bF)(R,{onClick:l=>z.delUser(e.row.id),size:"small",type:"danger",circle:"",plain:"",class:"action-btn"},{default:(0,t.k6)(()=>l[31]||(l[31]=[(0,t.eW)("删除")])),_:2,__:[31]},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[te,W.tableLoading]]),(0,t.Lk)("div",y,[(0,t.bF)(G,{background:"",layout:"total, sizes, prev, pager, next, jumper","page-sizes":[10,30,50,100],onSizeChange:z.sizes,onCurrentChange:z.currentPages,total:W.Pager.count,"current-page":W.Pager.current,"page-size":W.Pager.size||10},null,8,["onSizeChange","onCurrentChange","total","current-page","page-size"])])]),_:1}),(0,t.bF)(Z,{modelValue:W.addDlg,"onUpdate:modelValue":l[10]||(l[10]=e=>W.addDlg=e),title:"新增用户",width:"500px","destroy-on-close":"","close-on-click-modal":!1,onClosed:z.clearValidation},{footer:(0,t.k6)(()=>[(0,t.Lk)("div",U,[(0,t.bF)(R,{onClick:z.clearValidation},{default:(0,t.k6)(()=>l[32]||(l[32]=[(0,t.eW)("取消")])),_:1,__:[32]},8,["onClick"]),(0,t.bF)(R,{type:"primary",onClick:z.AddInter,loading:W.submitLoading},{default:(0,t.k6)(()=>l[33]||(l[33]=[(0,t.eW)("确定")])),_:1,__:[33]},8,["onClick","loading"])])]),default:(0,t.k6)(()=>[(0,t.Lk)("div",C,[(0,t.bF)(q,{model:W.addForm,rules:W.rulesUser,ref:"UserRef","label-width":"100px"},{default:(0,t.k6)(()=>[(0,t.bF)(Q,{prop:"username",label:"用户名"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.addForm.username,"onUpdate:modelValue":l[4]||(l[4]=e=>W.addForm.username=e),maxlength:"18",minlength:"3",placeholder:"请输入用户名","show-word-limit":""},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(D)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"用户标签"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.addForm.weChat_name,"onUpdate:modelValue":l[5]||(l[5]=e=>W.addForm.weChat_name=e),maxlength:"50",minlength:"1",placeholder:"请输入用户标签名称"},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(J)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"手机号码",prop:"mobile"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.addForm.mobile,"onUpdate:modelValue":l[6]||(l[6]=e=>W.addForm.mobile=e),maxlength:"11",minlength:"11",placeholder:"请输入手机号"},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(I)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"邮箱地址"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.addForm.email,"onUpdate:modelValue":l[7]||(l[7]=e=>W.addForm.email=e),placeholder:"请输入邮箱地址",readonly:"",onfocus:"this.removeAttribute('readonly');"},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(X)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"所属项目",required:""},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.addForm.project_name,"onUpdate:modelValue":l[8]||(l[8]=e=>W.addForm.project_name=e),disabled:""},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(S)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"密码",prop:"password"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.addForm.password,"onUpdate:modelValue":l[9]||(l[9]=e=>W.addForm.password=e),type:"password","show-password":"",maxlength:"18",minlength:"3",placeholder:"请输入密码"},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(Y)]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue","onClosed"]),(0,t.bF)(Z,{modelValue:W.editDlg,"onUpdate:modelValue":l[17]||(l[17]=e=>W.editDlg=e),title:"修改用户",width:"500px","destroy-on-close":"","close-on-click-modal":!1,onClosed:z.clearValidation},{footer:(0,t.k6)(()=>[(0,t.Lk)("div",x,[(0,t.bF)(R,{type:"warning",onClick:z.resetPassword,icon:j.Key},{default:(0,t.k6)(()=>[(0,t.eW)((0,o.v_)(W.showResetPassword?"取消修改密码":"重置密码"),1)]),_:1},8,["onClick","icon"]),(0,t.bF)(R,{type:"primary",onClick:z.UpdateInter,loading:W.submitLoading},{default:(0,t.k6)(()=>l[34]||(l[34]=[(0,t.eW)("确定")])),_:1,__:[34]},8,["onClick","loading"]),(0,t.bF)(R,{onClick:z.clearValidation},{default:(0,t.k6)(()=>l[35]||(l[35]=[(0,t.eW)("取消")])),_:1,__:[35]},8,["onClick"])])]),default:(0,t.k6)(()=>[(0,t.Lk)("div",V,[(0,t.bF)(q,{model:W.editForm,rules:W.rulesUser,ref:"UserRef","label-width":"100px"},{default:(0,t.k6)(()=>[(0,t.bF)(Q,{prop:"username",label:"用户名"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.editForm.username,"onUpdate:modelValue":l[11]||(l[11]=e=>W.editForm.username=e),maxlength:"18",minlength:"3",placeholder:"请输入用户名",disabled:""},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(D)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"用户标签"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.editForm.weChat_name,"onUpdate:modelValue":l[12]||(l[12]=e=>W.editForm.weChat_name=e),maxlength:"50",minlength:"1",placeholder:"请输入用户标签名称"},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(J)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"手机号码",prop:"mobile"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.editForm.mobile,"onUpdate:modelValue":l[13]||(l[13]=e=>W.editForm.mobile=e),maxlength:"11",minlength:"11",placeholder:"请输入手机号"},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(I)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"邮箱地址"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.editForm.email,"onUpdate:modelValue":l[14]||(l[14]=e=>W.editForm.email=e),placeholder:"请输入邮箱地址",maxlength:"30",readonly:"",onfocus:"this.removeAttribute('readonly');"},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(X)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"所属项目",required:""},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.editForm.project_name,"onUpdate:modelValue":l[15]||(l[15]=e=>W.editForm.project_name=e),disabled:""},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(S)]),_:1})]),_:1},8,["modelValue"])]),_:1}),W.showResetPassword?((0,t.uX)(),(0,t.Wv)(Q,{key:0,label:"新密码",prop:"password"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.editForm.password,"onUpdate:modelValue":l[16]||(l[16]=e=>W.editForm.password=e),type:"password","show-password":"",maxlength:"18",minlength:"3",placeholder:"请输入密码",readonly:"",onfocus:"this.removeAttribute('readonly');"},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(Y)]),_:1})]),_:1},8,["modelValue"])]),_:1})):(0,t.Q3)("",!0)]),_:1},8,["model","rules"])])]),_:1},8,["modelValue","onClosed"]),(0,t.bF)(Z,{modelValue:W.addProDlg,"onUpdate:modelValue":l[20]||(l[20]=e=>W.addProDlg=e),title:"添加其他项目成员",width:"500px","destroy-on-close":"","close-on-click-modal":!1,onClosed:z.clearValidation},{footer:(0,t.k6)(()=>[(0,t.Lk)("div",P,[(0,t.bF)(R,{onClick:z.clearValidation},{default:(0,t.k6)(()=>l[36]||(l[36]=[(0,t.eW)("取消")])),_:1,__:[36]},8,["onClick"]),(0,t.bF)(R,{type:"primary",onClick:z.clickExcludeUser,loading:W.submitLoading},{default:(0,t.k6)(()=>l[37]||(l[37]=[(0,t.eW)("确定")])),_:1,__:[37]},8,["onClick","loading"])])]),default:(0,t.k6)(()=>[(0,t.Lk)("div",v,[(0,t.bF)(q,{model:W.addProForm,ref:"UserRef","label-width":"100px"},{default:(0,t.k6)(()=>[(0,t.bF)(Q,{label:"所属项目",required:""},{default:(0,t.k6)(()=>[(0,t.bF)(E,{modelValue:W.addProForm.project_name,"onUpdate:modelValue":l[18]||(l[18]=e=>W.addProForm.project_name=e),disabled:""},{prefix:(0,t.k6)(()=>[(0,t.bF)(A,null,{default:(0,t.k6)(()=>[(0,t.bF)(S)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(Q,{label:"选择用户"},{default:(0,t.k6)(()=>[(0,t.bF)(le,{modelValue:W.addProForm.users,"onUpdate:modelValue":l[19]||(l[19]=e=>W.addProForm.users=e),multiple:"",filterable:"",placeholder:"请选择用户",style:{width:"100%"},"collapse-tags":"","collapse-tags-tooltip":""},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(W.usersExclude,e=>((0,t.uX)(),(0,t.Wv)(ee,{key:e.id,value:e.id,label:e.username},{default:(0,t.k6)(()=>[(0,t.Lk)("div",L,[(0,t.bF)(H,{size:24,icon:j.User,class:"user-avatar"},null,8,["icon"]),(0,t.Lk)("span",null,(0,o.v_)(e.username),1)])]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue","onClosed"])]),_:1})])}a(44114),a(18111),a(61701);var W=a(60782),z=a(57477),R=a(51219),$=a(12933),A={name:"UserManagement",components:{},setup(){return{Plus:z.Plus,UserFilled:z.UserFilled,Search:z.Search,Refresh:z.Refresh,Edit:z.Edit,Delete:z.Delete,Key:z.Key,User:z.User,Iphone:z.Iphone,Message:z.Message,Folder:z.Folder,Lock:z.Lock,List:z.List,Stamp:z.Stamp}},data(){return{UserLsit:[],QueryCondition:{username:"",mobile:"",email:"",project_name:""},Pager:{},addDlg:!1,editDlg:!1,addProDlg:!1,tableLoading:!1,submitLoading:!1,showResetPassword:!1,addForm:{username:"",mobile:"",email:"",project_id:"",project_name:"",password:"",weChat_name:""},editForm:{username:"",mobile:"",email:"",project_id:"",project_name:"",password:"",weChat_name:""},addProForm:{project_id:"",project_name:"",users:[]},usersExclude:[],rulesUser:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:18,message:"用户名长度在3到18个字符之间",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:3,max:18,message:"密码长度在3到18个字符之间",trigger:"blur"}],mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}]},readonlyInput:!0}},computed:{...(0,W.aH)(["pro","interfaces"]),...(0,W.L8)(["interfaces1","interfaces2"])},methods:{async getAllUser(e,l){const a=this.QueryCondition.username.trim(),t=this.QueryCondition.mobile.trim(),o=this.QueryCondition.email.trim(),r=this.QueryCondition.project_name.trim();let s=[];a&&s.push(`&username=${encodeURIComponent(a)}`),t&&s.push(`&mobile=${encodeURIComponent(t)}`),o&&s.push(`&email=${encodeURIComponent(o)}`),r&&s.push(`&project_name=${encodeURIComponent(r)}`);let d="/users/user/";e&&l||e?d+=`?page=${e}&size=${l}${s.join("")}`:l&&(d+=`?size=${l}${s.join("")}`);try{const e=await this.$api.getAllUsers(d,this.pro.id);200===e.status&&(this.UserLsit=e.data.result,this.Pager=e.data)}catch(i){R.nk.error("获取用户列表失败"),console.error(i)}},async getExcludeUser(){try{const e=await this.$api.getExcludeUsers(this.pro.id);if(200===e.status){const l=e.data;this.usersExclude=l.map(e=>({id:e.id,username:e.username})),0===this.usersExclude.length&&(0,R.nk)({type:"info",message:"没有可添加的用户",duration:1500})}}catch(e){R.nk.error("获取可选用户列表失败"),console.error(e)}},async clickExcludeUser(){if(this.addProForm.users&&0!==this.addProForm.users.length){this.submitLoading=!0;try{const e={...this.addProForm},l=await this.$api.addExcludeUser(e);200===l.status&&((0,R.nk)({type:"success",message:`成功添加 ${e.users.length} 名用户到项目`,duration:1500}),this.addProDlg=!1,this.getAllUser(1,this.Pager.size))}catch(e){R.nk.error("添加用户失败"),console.error(e)}finally{this.submitLoading=!1}}else R.nk.warning("请至少选择一名用户")},resetForm(){this.QueryCondition={username:"",mobile:"",email:"",project_name:""},(0,R.nk)({type:"info",message:"已重置搜索条件",duration:1e3}),this.getAllUser(1,this.Pager.size)},submitForm(){this.getAllUser(1,this.Pager.size)},clickAdd(){this.addDlg=!0,this.addForm={username:"",mobile:"",email:"",password:"",project_id:this.pro.id,project_name:this.pro.name,weChat_name:""}},clickAddPro(){this.addProDlg=!0,this.addProForm={project_id:this.pro.id,project_name:this.pro.name,users:[]},this.getExcludeUser()},clearValidation(){this.addDlg=!1,this.editDlg=!1,this.addProDlg=!1,this.showResetPassword=!1,this.$refs.UserRef&&this.$refs.UserRef.clearValidate()},AddInter(){this.$refs.UserRef.validate(async e=>{if(e){this.submitLoading=!0;try{const e={...this.addForm};""===e.weChat_name&&(e.weChat_name=e.username);const l=await this.$api.createUser(e);201===l.status&&((0,R.nk)({type:"success",message:"用户添加成功",duration:1500}),this.addForm={username:"",mobile:"",email:"",password:"",project_id:"",project_name:"",weChat_name:""},this.addDlg=!1,this.showResetPassword=!1,this.getAllUser(1,this.Pager.size))}catch(l){R.nk.error("添加用户失败"),console.error(l)}finally{this.submitLoading=!1}}else R.nk.warning("请正确填写表单")})},UpdateInter(){this.$refs.UserRef.validate(async e=>{if(e){this.submitLoading=!0;try{const e=this.editForm,l=await this.$api.updateUser(e.id,e);200===l.status&&((0,R.nk)({type:"success",message:"用户修改成功",duration:1500}),this.addForm={username:"",mobile:"",email:"",password:"",project_id:"",project_name:"",weChat_name:""},this.editDlg=!1,this.showResetPassword=!1,this.getAllUser(1,this.Pager.size))}catch(l){R.nk.error("修改用户失败"),console.error(l)}finally{this.submitLoading=!1}}else R.nk.warning("请正确填写表单")})},resetPassword(){this.showResetPassword=!this.showResetPassword,this.showResetPassword?(0,R.nk)({type:"info",message:"请输入新密码",duration:1500}):this.editForm.password=""},clickEdit(e){this.editDlg=!0,this.editForm={...e},this.editForm.project_id=this.pro.id,this.editForm.project_name=this.pro.name},delUser(e){$.s.confirm("此操作将永久删除该用户, 是否继续?","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",draggable:!0,closeOnClickModal:!1}).then(async()=>{try{const l=await this.$api.deleteUser(e);204===l.status&&((0,R.nk)({type:"success",message:"删除成功!",duration:1500}),this.getAllUser(1,this.Pager.size))}catch(l){R.nk.error("删除用户失败"),console.error(l)}}).catch(()=>{(0,R.nk)({type:"info",message:"已取消删除",duration:1500})})},currentPages(e){this.getAllUser(e,this.Pager.size),this.Pager.page=e},sizes(e){this.getAllUser(1,e)}},created(){this.getAllUser(1,10)}},D=a(71241);const E=(0,D.A)(A,[["render",j],["__scopeId","data-v-ba4dbb8a"]]);var Q=E}}]);
//# sourceMappingURL=911.c19fce86.js.map