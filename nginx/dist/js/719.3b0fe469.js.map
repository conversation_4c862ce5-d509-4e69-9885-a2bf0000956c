{"version": 3, "file": "js/719.3b0fe469.js", "mappings": "85BA4EMA,EAAc,sG,mCAtEpB,MAAMC,GAAQC,EAAAA,EAAAA,MAORC,IANSC,EAAAA,EAAAA,OAGGC,EAAAA,EAAAA,IAAS,IAAMJ,EAAMK,MAAMC,YAGvBC,EAAAA,EAAAA,IAAI,CACxBC,OAAQ,GACRC,MAAO,EACPC,QAAS,EACTC,KAAM,MAIFC,GAAYL,EAAAA,EAAAA,KAAI,GAGhBM,GAAiBC,EAAAA,EAAAA,IAAS,CAC9BC,KAAM,GACNC,OAAQ,GACRC,IAAK,GACLC,OAAQ,KAOJC,IAHoBZ,EAAAA,EAAAA,IAAI,KAGDA,EAAAA,EAAAA,IAAI,OAG3Ba,GAAUb,EAAAA,EAAAA,KAAI,GACdc,GAAYd,EAAAA,EAAAA,KAAI,GAChBe,GAAmBf,EAAAA,EAAAA,KAAI,GAGvBgB,GAAiBT,EAAAA,EAAAA,IAAS,CAC9BU,gBAAiB,CAAC,CAAEC,UAAU,EAAMC,QAAS,UAAWC,QAAS,WAI7DC,GAAWd,EAAAA,EAAAA,IAAS,CACxBe,aAAc,GACdC,OAAQ,CAAC,EACTC,QAAS,CAAC,EACVP,gBAAiB,GACjBQ,cAAe,GACfd,OAAQ,YAIJe,EAAgB,CACpB,CAAEC,MAAO,KAAMC,MAAO,IACtB,CAAED,MAAO,MAAOC,MAAO,OACvB,CAAED,MAAO,OAAQC,MAAO,QACxB,CAAED,MAAO,MAAOC,MAAO,OACvB,CAAED,MAAO,QAASC,MAAO,SACzB,CAAED,MAAO,SAAUC,MAAO,WAItBC,EAAgB,CACpB,CAAEF,MAAO,KAAMC,MAAO,IACtB,CAAED,MAAO,MAAOC,MAAO,WACvB,CAAED,MAAO,KAAMC,MAAO,QACtB,CAAED,MAAO,KAAMC,MAAO,SAqBlBE,IAPgB9B,EAAAA,EAAAA,IAAI,CACxB+B,MAAO,CAAEC,KAAM,EAAG5B,KAAM,IACxB6B,KAAM,CAAEzB,KAAM,OAAQoB,MAAO,KAC7BM,KAAM,CAAEC,GAAI,OAIS,CACrB,CACEA,GAAI,EACJ3B,KAAM,SACNC,OAAQ,MACRC,IAAK,aACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,UACRM,gBAAiB,KACjBQ,cAAe,MAEjB,CACEU,GAAI,EACJ3B,KAAM,QACNC,OAAQ,OACRC,IAAK,aACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,OACRM,gBAAiB,qDACjBQ,cAAe,sDAEjB,CACEU,GAAI,EACJ3B,KAAM,SACNC,OAAQ,MACRC,IAAK,iBACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,OACRM,gBAAiB,gCACjBQ,cAAe,iCAEjB,CACEU,GAAI,EACJ3B,KAAM,OACNC,OAAQ,SACRC,IAAK,iBACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,UACRM,gBAAiB,KACjBQ,cAAe,MAEjB,CACEU,GAAI,EACJ3B,KAAM,OACNC,OAAQ,OACRC,IAAK,aACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,OACRM,gBAAiB,iEACjBQ,cAAe,oEAKbc,EAAoBA,KACxBlC,EAAUuB,OAAQ,EAGlBY,WAAW,KACT,MAAMC,EAAeX,EAAeY,OAAOC,IACzC,MAAMC,GAAatC,EAAeE,MAAQmC,EAAKnC,KAAKqC,SAASvC,EAAeE,MACtEsC,GAAexC,EAAeG,QAAUkC,EAAKlC,SAAWH,EAAeG,OACvEsC,GAAYzC,EAAeI,KAAOiC,EAAKjC,IAAImC,SAASvC,EAAeI,KACnEsC,GAAe1C,EAAeK,QAAUgC,EAAKhC,SAAWL,EAAeK,OAC7E,OAAOiC,GAAaE,GAAeC,GAAYC,IAGjDrD,EAAciC,MAAQ,CACpB3B,OAAQwC,EACRvC,MAAOuC,EAAaQ,OACpB9C,QAAS,EACTC,KAAM,IAERC,EAAUuB,OAAQ,GACjB,MAiBCsB,EAAYA,KAChB5C,EAAeE,KAAO,GACtBF,EAAeG,OAAS,GACxBH,EAAeI,IAAM,GACrBJ,EAAeK,OAAS,GACxB4B,KAIIY,EAAaA,KACjBZ,KAIIa,GAASC,IACb1D,EAAciC,MAAMxB,KAAOiD,EAC3Bd,KAIIe,GAAgBD,IACpB1D,EAAciC,MAAMzB,QAAUkD,EAC9Bd,KAIIgB,GAAkBC,IACtB3C,EAAQe,OAAQ,EAChBhB,EAAqBgB,MAAQ4B,EAC7BnC,EAASC,aAAekC,EAAIrB,GAGxBqB,EAAIvC,gBACNI,EAASJ,gBAAkBuC,EAAIvC,gBAE/BI,EAASJ,gBAAkBzB,EAI7B6B,EAASI,cAAgB,GACzBJ,EAASV,OAAS,WAId8C,GAAcA,KAClBpD,EAAUuB,OAAQ,EAGlBY,WAAW,KAET,MAAMkB,EAAOC,KAAKC,SAAW,GAEzBF,GACFrC,EAASI,cAAgBJ,EAASJ,gBAClCI,EAASV,OAAS,OAClBkD,EAAAA,GAAUC,QAAQ,UAGlBzC,EAASI,cAAgB,wGAKzBJ,EAASV,OAAS,OAClBkD,EAAAA,GAAUE,MAAM,SAIlB,MAAMC,EAAQrE,EAAciC,MAAM3B,OAAOgE,UAAUtB,GAAQA,EAAKR,KAAOvB,EAAqBgB,MAAMO,KACnF,IAAX6B,IACFrE,EAAciC,MAAM3B,OAAO+D,GAAO/C,gBAAkBI,EAASJ,gBAC7DtB,EAAciC,MAAM3B,OAAO+D,GAAOvC,cAAgBJ,EAASI,cAC3D9B,EAAciC,MAAM3B,OAAO+D,GAAOrD,OAASU,EAASV,QAGtDN,EAAUuB,OAAQ,EAClBd,EAAUc,OAAQ,EAClBf,EAAQe,OAAQ,GACf,MAoDCsC,GAAkBV,IACjBA,EAAI/B,eAKTb,EAAqBgB,MAAQ4B,EAC7BnC,EAASJ,gBAAkBuC,EAAIvC,gBAC/BI,EAASI,cAAgB+B,EAAI/B,cAC7BX,EAAUc,OAAQ,GAPhBiC,EAAAA,GAAUM,QAAQ,YAWhBC,GAAcA,KAClBvD,EAAQe,OAAQ,EAChBd,EAAUc,OAAQ,EAClBb,EAAiBa,OAAQ,GAIrByC,GAAmBA,KACvBR,EAAAA,GAAUC,QAAQ,aAIdQ,GAAYA,KAChBjE,EAAUuB,OAAQ,EAElBY,WAAW,KACTqB,EAAAA,GAAUC,QAAQ,WAClBvB,IACAlC,EAAUuB,OAAQ,GACjB,M,OAIL2C,EAAAA,EAAAA,IAAU,KACRhC,M,wmBAKAiC,EAAAA,EAAAA,IA0FM,MA1FNC,EA0FM,EAzFJD,EAAAA,EAAAA,IAwFM,MAxFNE,EAwFM,C,eAvFJF,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAsBM,MAtBNG,EAsBM,EArBJH,EAAAA,EAAAA,IAEO,a,qBAFD,WACJI,EAAAA,EAAAA,IAAgJC,EAAA,C,WAA7HvE,EAAeE,K,qCAAfF,EAAeE,KAAIsE,GAAEC,YAAY,UAAUC,aAAa,MAAMC,UAAU,KAAMC,WAAW,EAAOC,MAAO,CAAAC,MAAA,U,0BAE5HZ,EAAAA,EAAAA,IAIO,a,qBAJD,WACJI,EAAAA,EAAAA,IAEYS,EAAA,C,WAFQ/E,EAAeG,O,qCAAfH,EAAeG,OAAMqE,GAAEC,YAAY,UAAWG,WAAW,EAAOC,MAAO,CAAAC,MAAA,U,kBAC9E,IAA6B,G,WAAxCE,EAAAA,EAAAA,IAAqGC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAA3E9D,EAARiB,IAAlBiC,EAAAA,EAAAA,IAAqGa,EAAA,CAA3DC,IAAK/C,EAAKf,MAAQD,MAAOgB,EAAKhB,MAAQC,MAAOe,EAAKf,O,4DAGhG4C,EAAAA,EAAAA,IAEO,a,uBAFD,WACJI,EAAAA,EAAAA,IAA+IC,EAAA,C,WAA5HvE,EAAeI,I,qCAAfJ,EAAeI,IAAGoE,GAAEC,YAAY,UAAUC,aAAa,MAAMC,UAAU,KAAMC,WAAW,EAAOC,MAAO,CAAAC,MAAA,U,0BAE3HZ,EAAAA,EAAAA,IAIO,a,uBAJD,WACJI,EAAAA,EAAAA,IAEYS,EAAA,C,WAFQ/E,EAAeK,O,qCAAfL,EAAeK,OAAMmE,GAAEC,YAAY,UAAWG,WAAW,EAAOC,MAAO,CAAAC,MAAA,U,kBAC9E,IAA6B,G,WAAxCE,EAAAA,EAAAA,IAAqGC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAA3E3D,EAARc,IAAlBiC,EAAAA,EAAAA,IAAqGa,EAAA,CAA3DC,IAAK/C,EAAKf,MAAQD,MAAOgB,EAAKhB,MAAQC,MAAOe,EAAKf,O,4DAIhG4C,EAAAA,EAAAA,IAGO,OAHPmB,EAGO,EAFLf,EAAAA,EAAAA,IAA2DgB,EAAA,CAA/CC,QAAO3C,EAAW4C,KAAK,W,kBAAU,IAAEC,EAAA,MAAAA,EAAA,M,QAAF,S,eAC7CnB,EAAAA,EAAAA,IAA0EgB,EAAA,CAA/DI,KAAK,UAAWH,QAAO1C,EAAY2C,KAAK,U,kBAAS,IAAEC,EAAA,MAAAA,EAAA,M,QAAF,S,mBAIhEvB,EAAAA,EAAAA,IAGM,MAHNyB,EAGM,EAFJrB,EAAAA,EAAAA,IAA8EgB,EAAA,CAAnEI,KAAK,UAAWH,QAAOvB,GAAWwB,KAAK,a,kBAAY,IAAIC,EAAA,MAAAA,EAAA,M,QAAJ,W,eAC9DnB,EAAAA,EAAAA,IAA0FgB,EAAA,CAA/EI,KAAK,UAAWH,QAAOxB,GAAkByB,KAAK,gB,kBAAe,IAAMC,EAAA,MAAAA,EAAA,M,QAAN,a,sCAI1EG,EAAAA,EAAAA,IA+CWC,EAAA,CA/CAC,KAAMzG,EAAAiC,MAAc3B,OAAQ,aAAW,OAA6BoG,OAAA,GAAOC,OAAA,I,kBACpF,IAIkB,EAJlB1B,EAAAA,EAAAA,IAIkB2B,EAAA,CAJD5E,MAAM,KAAK6E,MAAM,SAASpB,MAAM,M,CACpCqB,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,EACvBnC,EAAAA,EAAAA,IAAmC,aAAAoC,EAAAA,EAAAA,IAA1BD,EAAME,OAAS,GAAH,K,OAGzBjC,EAAAA,EAAAA,IAAiF2B,EAAA,CAAhE5E,MAAM,OAAOmF,KAAK,OAAON,MAAM,SAAS,8BACzD5B,EAAAA,EAAAA,IAoBkB2B,EAAA,CApBD5E,MAAM,OAAO6E,MAAM,SAASpB,MAAM,O,CACtCqB,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,EACvBnC,EAAAA,EAAAA,IAgBM,MAhBNuC,EAgBM,CAf6B,SAArBJ,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAA0B,EAAA,EADLpC,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,8BAEzB,QAArBkG,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAA6B,EAAA,EADLvC,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,8BAEzB,QAArBkG,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAA8B,EAAA,EADLxC,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,8BAEzB,UAArBkG,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAA+B,EAAA,EADLzC,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,8BAEzB,WAArBkG,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAAgC,EAAA,EADL1C,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,yCAKhEmE,EAAAA,EAAAA,IAAgF2B,EAAA,CAA/D5E,MAAM,OAAOmF,KAAK,MAAM,2BAAsBN,MAAM,YACrE5B,EAAAA,EAAAA,IAMkB2B,EAAA,CAND5E,MAAM,OAAO6E,MAAM,SAASpB,MAAM,O,CACtCqB,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,CACY,YAArBA,EAAMnD,IAAI7C,S,WAAxBuF,EAAAA,EAAAA,IAAsEe,EAAA,C,MAAxBjB,KAAK,Q,kBAAO,IAAGD,EAAA,MAAAA,EAAA,M,QAAH,U,eAClB,SAArBY,EAAMnD,IAAI7C,S,WAA7BuF,EAAAA,EAAAA,IAA0Ee,EAAA,C,MAA1BjB,KAAK,W,kBAAU,IAAED,EAAA,MAAAA,EAAA,M,QAAF,S,eACvB,SAArBY,EAAMnD,IAAI7C,S,WAA7BuF,EAAAA,EAAAA,IAAyEe,EAAA,C,MAAzBjB,KAAK,U,kBAAS,IAAED,EAAA,MAAAA,EAAA,M,QAAF,S,wCAGlEnB,EAAAA,EAAAA,IAIkB2B,EAAA,CAJD5E,MAAM,OAAO6E,MAAM,U,CACvBC,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,E,qBAChBtE,KAAKsE,EAAMnD,IAAIpB,aAAamF,kBAAc,K,OAGrD3C,EAAAA,EAAAA,IAKkB2B,EAAA,CALD5E,MAAM,KAAK6E,MAAM,SAASpB,MAAM,O,CACpCqB,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,EACvB/B,EAAAA,EAAAA,IAAgGgB,EAAA,CAApFC,QAAKf,GAAEvB,GAAeoD,EAAMnD,KAAMpD,KAAK,QAAQ4F,KAAK,UAAUwB,MAAA,I,kBAAM,IAAIzB,EAAA,MAAAA,EAAA,M,QAAJ,W,gCAChFnB,EAAAA,EAAAA,IAAgGgB,EAAA,CAApFC,QAAKf,GAAEZ,GAAeyC,EAAMnD,KAAMpD,KAAK,QAAQ4F,KAAK,UAAUwB,MAAA,I,kBAAM,IAAIzB,EAAA,MAAAA,EAAA,M,QAAJ,W,gEA5ClB1F,EAAAuB,UAkDpE4C,EAAAA,EAAAA,IAKM,MALNiD,EAKM,EAJJ7C,EAAAA,EAAAA,IAGgB8C,EAAA,CAHDC,WAAA,GAAWC,OAAO,0CAA2C,aAAY,CAAC,GAAI,GAAI,GAAI,KAClGC,aAAazE,GAAQ0E,gBAAgBxE,GAAeyE,MAAOpI,EAAAiC,MAAc1B,MACzE,eAAcP,EAAAiC,MAAczB,QAAS,YAAU,MAAM,YAAU,O,wCAOxEyE,EAAAA,EAAAA,IA6CYoD,GAAA,C,WA7CQnH,EAAAe,M,qCAAAf,EAAOe,MAAAkD,GAAEmD,MAAM,QAAQ7C,MAAM,MAAO,eAAchB,I,CAuCzD8D,QAAMxB,EAAAA,EAAAA,IACf,IAGO,EAHPlC,EAAAA,EAAAA,IAGO,OAHP2D,EAGO,EAFLvD,EAAAA,EAAAA,IAA8CgB,EAAA,CAAlCC,QAAOzB,IAAW,C,iBAAE,IAAE2B,EAAA,MAAAA,EAAA,M,QAAF,S,eAChCnB,EAAAA,EAAAA,IAA+DgB,EAAA,CAApDI,KAAK,UAAWH,QAAOpC,I,kBAAa,IAAIsC,EAAA,MAAAA,EAAA,M,QAAJ,W,mCAzCnD,IAqCM,CArCKnF,EAAAgB,Q,WAAX0D,EAAAA,EAAAA,IAqCM,MArCN8C,EAqCM,EApCJ5D,EAAAA,EAAAA,IAaM,MAbN6D,EAaM,EAZJzD,EAAAA,EAAAA,IAWkB0D,GAAA,CAXDL,MAAM,OAAQM,OAAQ,EAAGlC,OAAA,I,kBACxC,IAAyF,EAAzFzB,EAAAA,EAAAA,IAAyF4D,GAAA,CAAnE7G,MAAM,QAAM,C,iBAAC,IAA+B,E,iBAA5Bf,EAAAgB,MAAqBpB,MAAI,K,OAC/DoE,EAAAA,EAAAA,IAOuB4D,GAAA,CAPD7G,MAAM,QAAM,C,iBAChC,IAKS,EALTiD,EAAAA,EAAAA,IAKSqC,EAAA,CALAjB,KAAsC,QAAhCpF,EAAAgB,MAAqBnB,OAAmB,UAAsD,SAA3BG,EAAAgB,MAAqBnB,OAAM,UAAoE,QAA3BG,EAAAgB,MAAqBnB,OAAM,UAAmE,WAA3BG,EAAAgB,MAAqBnB,OAAM,iB,kBAIlP,IAAiC,E,iBAA9BG,EAAAgB,MAAqBnB,QAAM,K,0BAGlCmE,EAAAA,EAAAA,IAAkG4D,GAAA,CAA5E7G,MAAM,OAAQ8G,KAAM,G,kBAAG,IAA8B,E,iBAA3B7H,EAAAgB,MAAqBlB,KAAG,K,iBAI5E8D,EAAAA,EAAAA,IAoBM,MApBNkE,EAoBM,EAnBJ9D,EAAAA,EAAAA,IAkBU+D,GAAA,CAlBAC,MAAOvH,EAAWwH,MAAO7H,EAAgB,iBAAe,O,kBAChE,IAgBU,EAhBV4D,EAAAA,EAAAA,IAgBUkE,GAAA,CAhBD9C,KAAK,eAAa,C,iBACzB,IAQc,EARdpB,EAAAA,EAAAA,IAQcmE,GAAA,CARDpH,MAAM,QAAM,C,iBACvB,IAEe,EAFfiD,EAAAA,EAAAA,IAEeoE,GAAA,CAFDrH,MAAM,SAIpBiD,EAAAA,EAAAA,IAEeoE,GAAA,CAFDrH,MAAM,U,OAKtBiD,EAAAA,EAAAA,IAIcmE,GAAA,CAJDpH,MAAM,QAAM,C,iBACvB,IAEe,EAFfiD,EAAAA,EAAAA,IAEeoE,GAAA,CAFDrH,MAAM,SAASmF,KAAK,mB,kBAChC,IAAmH,EAAnHlC,EAAAA,EAAAA,IAAmHC,EAAA,C,WAAhGxD,EAASJ,gB,qCAATI,EAASJ,gBAAe6D,GAAEkB,KAAK,WAAYiD,KAAM,GAAIlE,YAAY,qB,0HAgBlGH,EAAAA,EAAAA,IAuFYoD,GAAA,C,WAvFQlH,EAAAc,M,qCAAAd,EAASc,MAAAkD,GAAEmD,MAAM,OAAO7C,MAAM,MAAO,eAAchB,I,CAiF1D8D,QAAMxB,EAAAA,EAAAA,IACf,IAGO,EAHPlC,EAAAA,EAAAA,IAGO,OAHP0E,EAGO,EAFLtE,EAAAA,EAAAA,IAA8CgB,EAAA,CAAlCC,QAAOzB,IAAW,C,iBAAE,IAAE2B,EAAA,MAAAA,EAAA,M,QAAF,S,eAChCnB,EAAAA,EAAAA,IAAwFgB,EAAA,CAA7EI,KAAK,UAAWH,QAAKE,EAAA,KAAAA,EAAA,GAAAjB,GAAEvB,GAAe3C,EAAAgB,S,kBAAuB,IAAImE,EAAA,MAAAA,EAAA,M,QAAJ,W,mCAnF5E,IA+EM,CA/EKnF,EAAAgB,Q,WAAX0D,EAAAA,EAAAA,IA+EM,MA/EN6D,EA+EM,EA9EJ3E,EAAAA,EAAAA,IAKM,MALN4E,EAKM,EAJJxE,EAAAA,EAAAA,IAGqByE,GAAA,CAHVpB,MAA2B,SAApB5G,EAASV,OAAoB,OAAS,OAC9CqF,KAA0B,SAApB3E,EAASV,OAAoB,UAAY,QAC/C2I,UAAU,EACX,gB,4BAGX9E,EAAAA,EAAAA,IAsEM,MAtEN+E,EAsEM,EArEJ3E,EAAAA,EAAAA,IAoEUkE,GAAA,CApED9C,KAAK,eAAa,C,iBACzB,IAec,EAfdpB,EAAAA,EAAAA,IAecmE,GAAA,CAfDpH,MAAM,QAAM,C,iBACvB,IAaS,EAbTiD,EAAAA,EAAAA,IAaS4E,GAAA,CAbAC,OAAQ,IAAE,C,iBACjB,IAKS,EALT7E,EAAAA,EAAAA,IAKS8E,GAAA,CALAjB,KAAM,IAAE,C,iBACf,IAGM,EAHNjE,EAAAA,EAAAA,IAGM,MAHNmF,EAGM,C,eAFJnF,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAA8D,MAA9DoF,GAA8DhD,EAAAA,EAAAA,IAAjCvF,EAASJ,iBAAe,O,OAGzD2D,EAAAA,EAAAA,IAKS8E,GAAA,CALAjB,KAAM,IAAE,C,iBACf,IAGM,EAHNjE,EAAAA,EAAAA,IAGM,MAHNqF,EAGM,C,eAFJrF,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAAmH,OAA9GsF,OAAKC,EAAAA,EAAAA,IAAA,CAAC,eAAc,iBAA+C,SAApB1I,EAASV,Y,QAAuBU,EAASI,eAAa,O,uBAMlHmD,EAAAA,EAAAA,IA4BcmE,GAAA,CA5BDpH,MAAM,QAAM,C,iBACvB,IA0BM,EA1BN6C,EAAAA,EAAAA,IA0BM,MA1BNwF,EA0BM,C,eAzBJxF,EAAAA,EAAAA,IAAe,UAAX,UAAM,IACqB,SAApBnD,EAASV,S,WAApB2E,EAAAA,EAAAA,IAEM,MAAA2E,EAAA,EADJrF,EAAAA,EAAAA,IAAiCsF,GAAA,CAAvBC,YAAY,gB,WAExB7E,EAAAA,EAAAA,IAoBM,MAAA8E,EAAA,EAnBJxF,EAAAA,EAAAA,IAAoEyE,GAAA,CAA1DpB,MAAM,OAAOjC,KAAK,UAAWsD,UAAU,EAAO,kBACxD9E,EAAAA,EAAAA,IAiBM,MAjBN6F,EAiBM,EAhBJzF,EAAAA,EAAAA,IAeWuB,EAAA,CAfAC,KAAM,C,qLAIdC,OAAA,I,kBACD,IAAyC,EAAzCzB,EAAAA,EAAAA,IAAyC2B,EAAA,CAAxB5E,MAAM,KAAKmF,KAAK,SACjClC,EAAAA,EAAAA,IAA+C2B,EAAA,CAA9B5E,MAAM,MAAMmF,KAAK,cAClClC,EAAAA,EAAAA,IAA6C2B,EAAA,CAA5B5E,MAAM,MAAMmF,KAAK,YAClClC,EAAAA,EAAAA,IAMkB2B,EAAA,CAND5E,MAAM,KAAKmF,KAAK,U,CACpBL,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,CACY,cAArBA,EAAMnD,IAAI7C,S,WAAxBuF,EAAAA,EAAAA,IAA0Ee,EAAA,C,MAA1BjB,KAAK,U,kBAAS,IAAGD,EAAA,MAAAA,EAAA,M,QAAH,U,eACtB,YAArBY,EAAMnD,IAAI7C,S,WAA7BuF,EAAAA,EAAAA,IAA6Ee,EAAA,C,MAA1BjB,KAAK,W,kBAAU,IAAED,EAAA,MAAAA,EAAA,M,QAAF,S,4BAClEG,EAAAA,EAAAA,IAAyCe,EAAA,C,MAA1BjB,KAAK,W,kBAAU,IAAED,EAAA,MAAAA,EAAA,M,QAAF,S,+CAS5CnB,EAAAA,EAAAA,IAmBcmE,GAAA,CAnBDpH,MAAM,QAAM,C,iBACvB,IAiBM,EAjBN6C,EAAAA,EAAAA,IAiBM,MAjBN8F,EAiBM,EAhBJ1F,EAAAA,EAAAA,IAec2F,GAAA,M,iBAdZ,IAGmB,EAHnB3F,EAAAA,EAAAA,IAGmB4F,GAAA,CAHDC,UAAU,OAAOC,UAAU,MAAM1E,KAAK,W,kBACtD,IAAgB,C,eAAhBxB,EAAAA,EAAAA,IAAgB,SAAb,aAAS,KACZA,EAAAA,EAAAA,IAAyD,IAAzDmG,GAAyD/D,EAAAA,EAAAA,KAAA,IAA9BvE,MAAOkF,kBAAc,K,eAElD3C,EAAAA,EAAAA,IAImB4F,GAAA,CAJDC,UAAU,OAAOC,UAAU,MAAM1E,KAAK,Q,kBACtD,IAAkB,C,eAAlBxB,EAAAA,EAAAA,IAAkB,SAAf,eAAW,KACdA,EAAAA,EAAAA,IAA0C,SAAvC,SAAKoC,EAAAA,EAAAA,IAAGhG,EAAAgB,MAAqBlB,KAAG,IACnC8D,EAAAA,EAAAA,IAAgD,SAA7C,YAAQoC,EAAAA,EAAAA,IAAGhG,EAAAgB,MAAqBnB,QAAM,K,eAE3CmE,EAAAA,EAAAA,IAImB4F,GAAA,CAJAC,UAA+B,SAApBpJ,EAASV,OAAoB,OAAS,OAAQ+J,UAAU,MACpE1E,KAA0B,SAApB3E,EAASV,OAAoB,UAAY,U,kBAC/D,IAAoE,EAApE6D,EAAAA,EAAAA,IAAoE,UAAAoC,EAAAA,EAAAA,IAA1C,SAApBvF,EAASV,OAAoB,YAAc,cAA5B,IACrB6D,EAAAA,EAAAA,IAAyD,IAAzDoG,GAAyDhE,EAAAA,EAAAA,KAAA,IAA9BvE,MAAOkF,kBAAc,K,wHCpjBlE,MAAMsD,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://frontend-web/./src/views/Interface/BlindTest.vue", "webpack://frontend-web/./src/views/Interface/BlindTest.vue?58dc"], "sourcesContent": ["<script setup>\r\nimport { ref, reactive, onMounted, computed } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { useStore } from 'vuex'\r\nimport { useRouter } from 'vue-router'\r\n\r\nconst store = useStore()\r\nconst router = useRouter()\r\n\r\n// 获取项目ID\r\nconst projectId = computed(() => store.state.projectId)\r\n\r\n// 盲测测试数据\r\nconst blindTestData = ref({\r\n  result: [],\r\n  count: 0,\r\n  current: 1,\r\n  size: 10\r\n})\r\n\r\n// 加载状态\r\nconst isLoading = ref(false)\r\n\r\n// 查询条件\r\nconst queryCondition = reactive({\r\n  name: '',\r\n  method: '',\r\n  url: '',\r\n  status: ''\r\n})\r\n\r\n// 测试结果详情\r\nconst testResultDetails = ref([])\r\n\r\n// 当前测试的接口\r\nconst currentTestInterface = ref(null)\r\n\r\n// 对话框显示状态\r\nconst testDlg = ref(false)\r\nconst resultDlg = ref(false)\r\nconst compareResultDlg = ref(false)\r\n\r\n// 表单校验规则\r\nconst rulesBlindTest = reactive({\r\n  expected_result: [{ required: true, message: '请输入预期结果', trigger: 'blur' }]\r\n})\r\n\r\n// 测试表单\r\nconst testForm = reactive({\r\n  interface_id: '',\r\n  params: {},\r\n  headers: {},\r\n  expected_result: '',\r\n  actual_result: '',\r\n  status: 'pending'\r\n})\r\n\r\n// 筛选方法类型列表\r\nconst methodOptions = [\r\n  { label: '全部', value: '' },\r\n  { label: 'GET', value: 'GET' },\r\n  { label: 'POST', value: 'POST' },\r\n  { label: 'PUT', value: 'PUT' },\r\n  { label: 'PATCH', value: 'PATCH' },\r\n  { label: 'DELETE', value: 'DELETE' }\r\n]\r\n\r\n// 测试状态选项\r\nconst statusOptions = [\r\n  { label: '全部', value: '' },\r\n  { label: '待测试', value: 'pending' },\r\n  { label: '通过', value: 'pass' },\r\n  { label: '失败', value: 'fail' }\r\n]\r\n\r\n// JSON样例数据\r\nconst jsonExample = `{\r\n  \"name\": \"API盲测示例\",\r\n  \"status\": 200,\r\n  \"data\": {\r\n    \"id\": 1001,\r\n    \"value\": \"示例值\"\r\n  }\r\n}`\r\n\r\n// 请求参数示例\r\nconst paramsExample = ref({\r\n  query: { page: 1, size: 10 },\r\n  body: { name: \"测试数据\", value: 123 },\r\n  path: { id: 456 }\r\n})\r\n\r\n// 模拟测试数据(用于演示)\r\nconst demoInterfaces = [\r\n  {\r\n    id: 1,\r\n    name: '获取用户列表',\r\n    method: 'GET',\r\n    url: '/api/users',\r\n    create_time: new Date().toISOString(),\r\n    status: 'pending',\r\n    expected_result: null,\r\n    actual_result: null\r\n  },\r\n  {\r\n    id: 2,\r\n    name: '创建新用户',\r\n    method: 'POST',\r\n    url: '/api/users',\r\n    create_time: new Date().toISOString(),\r\n    status: 'pass',\r\n    expected_result: '{\"code\":200,\"message\":\"用户创建成功\",\"data\":{\"id\":1003}}',\r\n    actual_result: '{\"code\":200,\"message\":\"用户创建成功\",\"data\":{\"id\":1003}}'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: '更新用户信息',\r\n    method: 'PUT',\r\n    url: '/api/users/:id',\r\n    create_time: new Date().toISOString(),\r\n    status: 'fail',\r\n    expected_result: '{\"code\":200,\"message\":\"更新成功\"}',\r\n    actual_result: '{\"code\":400,\"message\":\"参数错误\"}'\r\n  },\r\n  {\r\n    id: 4,\r\n    name: '删除用户',\r\n    method: 'DELETE',\r\n    url: '/api/users/:id',\r\n    create_time: new Date().toISOString(),\r\n    status: 'pending',\r\n    expected_result: null,\r\n    actual_result: null\r\n  },\r\n  {\r\n    id: 5,\r\n    name: '用户登录',\r\n    method: 'POST',\r\n    url: '/api/login',\r\n    create_time: new Date().toISOString(),\r\n    status: 'pass',\r\n    expected_result: '{\"code\":200,\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"}',\r\n    actual_result: '{\"code\":200,\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"}'\r\n  }\r\n]\r\n\r\n// 加载接口数据\r\nconst loadBlindTestData = () => {\r\n  isLoading.value = true\r\n\r\n  // 这里模拟API调用，实际项目中应替换为真实的API调用\r\n  setTimeout(() => {\r\n    const filteredData = demoInterfaces.filter(item => {\r\n      const nameMatch = !queryCondition.name || item.name.includes(queryCondition.name)\r\n      const methodMatch = !queryCondition.method || item.method === queryCondition.method\r\n      const urlMatch = !queryCondition.url || item.url.includes(queryCondition.url)\r\n      const statusMatch = !queryCondition.status || item.status === queryCondition.status\r\n      return nameMatch && methodMatch && urlMatch && statusMatch\r\n    })\r\n\r\n    blindTestData.value = {\r\n      result: filteredData,\r\n      count: filteredData.length,\r\n      current: 1,\r\n      size: 10\r\n    }\r\n    isLoading.value = false\r\n  }, 500)\r\n\r\n  // 实际API调用可以参考下面的注释代码\r\n  /*\r\n  this.$api.getBlindTests(projectId.value, queryCondition).then(res => {\r\n    if (res.status === 200) {\r\n      blindTestData.value = res.data\r\n    }\r\n    isLoading.value = false\r\n  }).catch(err => {\r\n    console.error(err)\r\n    isLoading.value = false\r\n  })\r\n  */\r\n}\r\n\r\n// 重置查询条件\r\nconst resetForm = () => {\r\n  queryCondition.name = ''\r\n  queryCondition.method = ''\r\n  queryCondition.url = ''\r\n  queryCondition.status = ''\r\n  loadBlindTestData()\r\n}\r\n\r\n// 提交查询\r\nconst submitForm = () => {\r\n  loadBlindTestData()\r\n}\r\n\r\n// 分页大小变化\r\nconst sizes = (val) => {\r\n  blindTestData.value.size = val\r\n  loadBlindTestData()\r\n}\r\n\r\n// 分页当前页变化\r\nconst currentPages = (val) => {\r\n  blindTestData.value.current = val\r\n  loadBlindTestData()\r\n}\r\n\r\n// 开始进行API盲测\r\nconst startBlindTest = (row) => {\r\n  testDlg.value = true\r\n  currentTestInterface.value = row\r\n  testForm.interface_id = row.id\r\n\r\n  // 如果已经有测试结果，则加载之前的结果\r\n  if (row.expected_result) {\r\n    testForm.expected_result = row.expected_result\r\n  } else {\r\n    testForm.expected_result = jsonExample\r\n  }\r\n\r\n  // 清空上次的测试结果\r\n  testForm.actual_result = ''\r\n  testForm.status = 'pending'\r\n}\r\n\r\n// 执行API测试\r\nconst executeTest = () => {\r\n  isLoading.value = true\r\n\r\n  // 模拟API调用测试\r\n  setTimeout(() => {\r\n    // 模拟测试结果(50%概率通过，50%概率失败)\r\n    const pass = Math.random() > 0.5\r\n\r\n    if (pass) {\r\n      testForm.actual_result = testForm.expected_result\r\n      testForm.status = 'pass'\r\n      ElMessage.success('测试通过')\r\n    } else {\r\n      // 模拟失败结果\r\n      testForm.actual_result = `{\r\n        \"code\": 400,\r\n        \"message\": \"参数错误\",\r\n        \"errors\": [\"参数格式不正确\", \"缺少必要参数\"]\r\n      }`\r\n      testForm.status = 'fail'\r\n      ElMessage.error('测试失败')\r\n    }\r\n\r\n    // 更新界面数据\r\n    const index = blindTestData.value.result.findIndex(item => item.id === currentTestInterface.value.id)\r\n    if (index !== -1) {\r\n      blindTestData.value.result[index].expected_result = testForm.expected_result\r\n      blindTestData.value.result[index].actual_result = testForm.actual_result\r\n      blindTestData.value.result[index].status = testForm.status\r\n    }\r\n\r\n    isLoading.value = false\r\n    resultDlg.value = true\r\n    testDlg.value = false\r\n  }, 1000)\r\n\r\n  // 实际项目中的API调用可参考下面注释代码\r\n  /*\r\n  this.$api.runBlindTest(testForm).then(res => {\r\n    if (res.status === 200) {\r\n      testForm.actual_result = JSON.stringify(res.data.result, null, 2)\r\n\r\n      // 比较预期结果和实际结果\r\n      try {\r\n        const expected = JSON.parse(testForm.expected_result)\r\n        const actual = res.data.result\r\n\r\n        // 这里可以添加自定义的结果比较逻辑\r\n        const isEqual = JSON.stringify(expected) === JSON.stringify(actual)\r\n        testForm.status = isEqual ? 'pass' : 'fail'\r\n\r\n        if (isEqual) {\r\n          ElMessage.success('测试通过')\r\n        } else {\r\n          ElMessage.error('测试失败')\r\n        }\r\n      } catch (e) {\r\n        testForm.status = 'fail'\r\n        ElMessage.error('预期结果格式错误，无法比较')\r\n      }\r\n\r\n      // 更新界面数据\r\n      const index = blindTestData.value.result.findIndex(item => item.id === currentTestInterface.value.id)\r\n      if (index !== -1) {\r\n        blindTestData.value.result[index].expected_result = testForm.expected_result\r\n        blindTestData.value.result[index].actual_result = testForm.actual_result\r\n        blindTestData.value.result[index].status = testForm.status\r\n      }\r\n\r\n      resultDlg.value = true\r\n      testDlg.value = false\r\n    }\r\n    isLoading.value = false\r\n  }).catch(err => {\r\n    console.error(err)\r\n    testForm.status = 'fail'\r\n    testForm.actual_result = JSON.stringify(err.response.data, null, 2)\r\n    ElMessage.error('测试执行失败')\r\n    isLoading.value = false\r\n    resultDlg.value = true\r\n    testDlg.value = false\r\n  })\r\n  */\r\n}\r\n\r\n// 查看测试结果详情\r\nconst viewTestResult = (row) => {\r\n  if (!row.actual_result) {\r\n    ElMessage.warning('该接口尚未测试')\r\n    return\r\n  }\r\n\r\n  currentTestInterface.value = row\r\n  testForm.expected_result = row.expected_result\r\n  testForm.actual_result = row.actual_result\r\n  resultDlg.value = true\r\n}\r\n\r\n// 关闭对话框\r\nconst closeDialog = () => {\r\n  testDlg.value = false\r\n  resultDlg.value = false\r\n  compareResultDlg.value = false\r\n}\r\n\r\n// 导出测试报告\r\nconst exportTestReport = () => {\r\n  ElMessage.success('测试报告导出成功')\r\n}\r\n\r\n// 批量测试\r\nconst batchTest = () => {\r\n  isLoading.value = true\r\n\r\n  setTimeout(() => {\r\n    ElMessage.success('批量测试已完成')\r\n    loadBlindTestData()\r\n    isLoading.value = false\r\n  }, 2000)\r\n}\r\n\r\n// 页面加载时获取数据\r\nonMounted(() => {\r\n  loadBlindTestData()\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div class=\"blind-test-container\">\r\n    <div class=\"title\">\r\n      <h2>接口盲测</h2>\r\n      <div class=\"query_model\">\r\n        <span>接口名称\r\n          <el-input v-model=\"queryCondition.name\" placeholder=\"请输入接口名称\" autocomplete=\"off\" maxlength=\"30\" :clearable=\"true\" :style=\"{ width: '200px' }\" />\r\n        </span>\r\n        <span>请求类型\r\n          <el-select v-model=\"queryCondition.method\" placeholder=\"请选择请求类型\" :clearable=\"true\" :style=\"{ width: '200px' }\">\r\n            <el-option v-for=\"item in methodOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </span>\r\n        <span>接口地址\r\n          <el-input v-model=\"queryCondition.url\" placeholder=\"请输入接口地址\" autocomplete=\"off\" maxlength=\"30\" :clearable=\"true\" :style=\"{ width: '200px' }\" />\r\n        </span>\r\n        <span>测试状态\r\n          <el-select v-model=\"queryCondition.status\" placeholder=\"请选择测试状态\" :clearable=\"true\" :style=\"{ width: '200px' }\">\r\n            <el-option v-for=\"item in statusOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </span>\r\n\r\n        <span class=\"buttons\">\r\n          <el-button @click=\"resetForm\" icon=\"Refresh\">重置</el-button>\r\n          <el-button type=\"success\" @click=\"submitForm\" icon=\"Search\">查询</el-button>\r\n        </span>\r\n      </div>\r\n\r\n      <div class=\"operation-buttons\">\r\n        <el-button type=\"primary\" @click=\"batchTest\" icon=\"VideoPlay\">批量盲测</el-button>\r\n        <el-button type=\"success\" @click=\"exportTestReport\" icon=\"DocumentCopy\">导出测试报告</el-button>\r\n      </div>\r\n\r\n      <!-- 接口列表 -->\r\n      <el-table :data=\"blindTestData.result\" empty-text=\"暂无数据\" v-loading=\"isLoading\" border stripe>\r\n        <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n          <template #default=\"scope\">\r\n            <span>{{ scope.$index + 1 }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"接口名称\" prop=\"name\" align=\"center\" show-overflow-tooltip />\r\n        <el-table-column label=\"请求类型\" align=\"center\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <div style=\"font-weight: bold\">\r\n              <span v-if=\"scope.row.method === 'POST'\">\r\n                <el-tag color=\"#49cc90\" size=\"large\">{{ scope.row.method }}</el-tag>\r\n              </span>\r\n              <span v-if=\"scope.row.method === 'GET'\">\r\n                <el-tag color=\"#61affe\" size=\"large\">{{ scope.row.method }}</el-tag>\r\n              </span>\r\n              <span v-if=\"scope.row.method === 'PUT'\">\r\n                <el-tag color=\"#fca130\" size=\"large\">{{ scope.row.method }}</el-tag>\r\n              </span>\r\n              <span v-if=\"scope.row.method === 'PATCH'\">\r\n                <el-tag color=\"#50e3c2\" size=\"large\">{{ scope.row.method }}</el-tag>\r\n              </span>\r\n              <span v-if=\"scope.row.method === 'DELETE'\">\r\n                <el-tag color=\"#f93e3e\" size=\"large\">{{ scope.row.method }}</el-tag>\r\n              </span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"接口地址\" prop=\"url\" show-overflow-tooltip align=\"center\" />\r\n        <el-table-column label=\"测试状态\" align=\"center\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag v-if=\"scope.row.status === 'pending'\" type=\"info\">待测试</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status === 'pass'\" type=\"success\">通过</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status === 'fail'\" type=\"danger\">失败</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            {{ new Date(scope.row.create_time).toLocaleString() }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"220\">\r\n          <template #default=\"scope\">\r\n            <el-button @click=\"startBlindTest(scope.row)\" size=\"small\" type=\"primary\" plain>开始盲测</el-button>\r\n            <el-button @click=\"viewTestResult(scope.row)\" size=\"small\" type=\"success\" plain>查看结果</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页组件 -->\r\n      <div class=\"pagination-block\">\r\n        <el-pagination background layout=\"total, sizes, prev, pager, next, jumper\" :page-sizes=\"[10, 20, 50, 100]\"\r\n          @size-change=\"sizes\" @current-change=\"currentPages\" :total=\"blindTestData.count\"\r\n          :current-page=\"blindTestData.current\" next-text=\"下一页\" prev-text=\"上一页\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 盲测对话框 -->\r\n  <el-dialog v-model=\"testDlg\" title=\"API盲测\" width=\"60%\" :before-close=\"closeDialog\">\r\n    <div v-if=\"currentTestInterface\" class=\"blind-test-dialog\">\r\n      <div class=\"interface-info\">\r\n        <el-descriptions title=\"接口信息\" :column=\"2\" border>\r\n          <el-descriptions-item label=\"接口名称\">{{ currentTestInterface.name }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"请求方式\">\r\n            <el-tag :type=\"currentTestInterface.method === 'GET' ? 'primary' :\r\n              currentTestInterface.method === 'POST' ? 'success' :\r\n              currentTestInterface.method === 'PUT' ? 'warning' :\r\n              currentTestInterface.method === 'DELETE' ? 'danger' : 'info'\">\r\n              {{ currentTestInterface.method }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"接口地址\" :span=\"2\">{{ currentTestInterface.url }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <div class=\"interface-test-form\">\r\n        <el-form :model=\"testForm\" :rules=\"rulesBlindTest\" label-position=\"top\">\r\n          <el-tabs type=\"border-card\">\r\n            <el-tab-pane label=\"请求参数\">\r\n              <el-form-item label=\"请求头\">\r\n<!--                <el-input v-model=\"JSON.stringify(paramsExample.value.query, null, 2)\" type=\"textarea\" :rows=\"5\" placeholder=\"请输入请求头参数（JSON格式）\"></el-input>-->\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"请求体\">\r\n<!--                <el-input v-model=\"JSON.stringify(paramsExample.value.body, null, 2)\" type=\"textarea\" :rows=\"5\" placeholder=\"请输入请求体参数（JSON格式）\"></el-input>-->\r\n              </el-form-item>\r\n            </el-tab-pane>\r\n\r\n            <el-tab-pane label=\"预期结果\">\r\n              <el-form-item label=\"预期响应结果\" prop=\"expected_result\">\r\n                <el-input v-model=\"testForm.expected_result\" type=\"textarea\" :rows=\"10\" placeholder=\"请输入预期响应结果（JSON格式）\"></el-input>\r\n              </el-form-item>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n    <template #footer>\r\n      <span class=\"dialog-footer\">\r\n        <el-button @click=\"closeDialog\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"executeTest\">执行测试</el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n\r\n  <!-- 测试结果对话框 -->\r\n  <el-dialog v-model=\"resultDlg\" title=\"测试结果\" width=\"70%\" :before-close=\"closeDialog\">\r\n    <div v-if=\"currentTestInterface\" class=\"test-result-container\">\r\n      <div class=\"result-header\">\r\n        <el-alert :title=\"testForm.status === 'pass' ? '测试通过' : '测试失败'\"\r\n                 :type=\"testForm.status === 'pass' ? 'success' : 'error'\"\r\n                 :closable=\"false\"\r\n                 show-icon />\r\n      </div>\r\n\r\n      <div class=\"result-content\">\r\n        <el-tabs type=\"border-card\">\r\n          <el-tab-pane label=\"结果对比\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <div class=\"result-panel\">\r\n                  <h3>预期结果</h3>\r\n                  <pre class=\"json-content\">{{ testForm.expected_result }}</pre>\r\n                </div>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <div class=\"result-panel\">\r\n                  <h3>实际结果</h3>\r\n                  <pre class=\"json-content\" :class=\"{'error-content': testForm.status === 'fail'}\">{{ testForm.actual_result }}</pre>\r\n                </div>\r\n              </el-col>\r\n            </el-row>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"差异分析\">\r\n            <div class=\"diff-analysis\">\r\n              <h3>差异分析结果</h3>\r\n              <div v-if=\"testForm.status === 'pass'\">\r\n                <el-empty description=\"没有发现差异\" />\r\n              </div>\r\n              <div v-else>\r\n                <el-alert title=\"发现差异\" type=\"warning\" :closable=\"false\" show-icon />\r\n                <div class=\"diff-items\">\r\n                  <el-table :data=\"[\r\n                    { key: 'code', expected: '200', actual: '400', status: 'different' },\r\n                    { key: 'message', expected: '成功', actual: '参数错误', status: 'different' },\r\n                    { key: 'data', expected: '对象', actual: '不存在', status: 'missing' }\r\n                  ]\" border>\r\n                    <el-table-column label=\"字段\" prop=\"key\" />\r\n                    <el-table-column label=\"预期值\" prop=\"expected\" />\r\n                    <el-table-column label=\"实际值\" prop=\"actual\" />\r\n                    <el-table-column label=\"状态\" prop=\"status\">\r\n                      <template #default=\"scope\">\r\n                        <el-tag v-if=\"scope.row.status === 'different'\" type=\"danger\">不一致</el-tag>\r\n                        <el-tag v-else-if=\"scope.row.status === 'missing'\" type=\"warning\">缺失</el-tag>\r\n                        <el-tag v-else type=\"success\">一致</el-tag>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"测试日志\">\r\n            <div class=\"test-logs\">\r\n              <el-timeline>\r\n                <el-timeline-item timestamp=\"测试开始\" placement=\"top\" type=\"primary\">\r\n                  <p>开始执行API盲测</p>\r\n                  <p class=\"log-time\">{{ new Date().toLocaleString() }}</p>\r\n                </el-timeline-item>\r\n                <el-timeline-item timestamp=\"发送请求\" placement=\"top\" type=\"info\">\r\n                  <p>向服务器发送API请求</p>\r\n                  <p>URL: {{ currentTestInterface.url }}</p>\r\n                  <p>Method: {{ currentTestInterface.method }}</p>\r\n                </el-timeline-item>\r\n                <el-timeline-item :timestamp=\"testForm.status === 'pass' ? '测试通过' : '测试失败'\" placement=\"top\"\r\n                                 :type=\"testForm.status === 'pass' ? 'success' : 'danger'\">\r\n                  <p>{{ testForm.status === 'pass' ? '响应结果与预期一致' : '响应结果与预期不一致' }}</p>\r\n                  <p class=\"log-time\">{{ new Date().toLocaleString() }}</p>\r\n                </el-timeline-item>\r\n              </el-timeline>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n    <template #footer>\r\n      <span class=\"dialog-footer\">\r\n        <el-button @click=\"closeDialog\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"startBlindTest(currentTestInterface)\">重新测试</el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style scoped>\r\n.blind-test-container {\r\n  padding: 16px;\r\n  height: calc(100vh - 65px);\r\n  overflow: auto;\r\n}\r\n\r\n.title {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.title h2 {\r\n  margin-bottom: 20px;\r\n  font-weight: 600;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.query_model {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.query_model > span {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.operation-buttons {\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.buttons {\r\n  margin-left: auto;\r\n}\r\n\r\n.pagination-block {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n/* 盲测对话框样式 */\r\n.blind-test-dialog {\r\n  padding: 10px;\r\n}\r\n\r\n.interface-info {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.interface-test-form {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 测试结果对话框样式 */\r\n.test-result-container {\r\n  padding: 10px;\r\n}\r\n\r\n.result-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-content {\r\n  margin-top: 20px;\r\n}\r\n\r\n.result-panel {\r\n  border: 1px solid #e6e6e6;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n  height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n.result-panel h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 10px;\r\n  color: #606266;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.json-content {\r\n  white-space: pre-wrap;\r\n  font-family: 'Courier New', Courier, monospace;\r\n  font-size: 14px;\r\n  background-color: #f9f9f9;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  line-height: 1.5;\r\n  overflow: auto;\r\n}\r\n\r\n.error-content {\r\n  background-color: #fff5f5;\r\n  border-left: 3px solid #f56c6c;\r\n}\r\n\r\n.diff-analysis {\r\n  padding: 10px;\r\n}\r\n\r\n.diff-items {\r\n  margin-top: 15px;\r\n}\r\n\r\n.test-logs {\r\n  padding: 10px;\r\n}\r\n\r\n.log-time {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 添加响应式设计 */\r\n@media screen and (max-width: 992px) {\r\n  .query_model {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .query_model > span {\r\n    width: 100%;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .buttons {\r\n    margin-left: 0;\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n</style>", "import script from \"./BlindTest.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./BlindTest.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./BlindTest.vue?vue&type=style&index=0&id=19d702e0&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-19d702e0\"]])\n\nexport default __exports__"], "names": ["jsonExample", "store", "useStore", "blindTestData", "useRouter", "computed", "state", "projectId", "ref", "result", "count", "current", "size", "isLoading", "queryCondition", "reactive", "name", "method", "url", "status", "currentTestInterface", "testDlg", "resultDlg", "compareResultDlg", "rulesBlindTest", "expected_result", "required", "message", "trigger", "testForm", "interface_id", "params", "headers", "actual_result", "methodOptions", "label", "value", "statusOptions", "demoInterfaces", "query", "page", "body", "path", "id", "create_time", "Date", "toISOString", "loadBlindTestData", "setTimeout", "filteredData", "filter", "item", "nameMatch", "includes", "methodMatch", "urlMatch", "statusMatch", "length", "resetForm", "submitForm", "sizes", "val", "currentPages", "startBlindTest", "row", "executeTest", "pass", "Math", "random", "ElMessage", "success", "error", "index", "findIndex", "viewTestResult", "warning", "closeDialog", "exportTestReport", "batchTest", "onMounted", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_input", "$event", "placeholder", "autocomplete", "maxlength", "clearable", "style", "width", "_component_el_select", "_createElementBlock", "_Fragment", "_renderList", "_component_el_option", "key", "_hoisted_4", "_component_el_button", "onClick", "icon", "_cache", "type", "_hoisted_5", "_createBlock", "_component_el_table", "data", "border", "stripe", "_component_el_table_column", "align", "default", "_withCtx", "scope", "_toDisplayString", "$index", "prop", "_hoisted_6", "_hoisted_7", "_component_el_tag", "color", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "toLocaleString", "plain", "_hoisted_12", "_component_el_pagination", "background", "layout", "onSizeChange", "onCurrentChange", "total", "_component_el_dialog", "title", "footer", "_hoisted_16", "_hoisted_13", "_hoisted_14", "_component_el_descriptions", "column", "_component_el_descriptions_item", "span", "_hoisted_15", "_component_el_form", "model", "rules", "_component_el_tabs", "_component_el_tab_pane", "_component_el_form_item", "rows", "_hoisted_30", "_hoisted_17", "_hoisted_18", "_component_el_alert", "closable", "_hoisted_19", "_component_el_row", "gutter", "_component_el_col", "_hoisted_20", "_hoisted_21", "_hoisted_22", "class", "_normalizeClass", "_hoisted_23", "_hoisted_24", "_component_el_empty", "description", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_component_el_timeline", "_component_el_timeline_item", "timestamp", "placement", "_hoisted_28", "_hoisted_29", "__exports__"], "sourceRoot": ""}