{"version": 3, "file": "js/519.540f0f79.js", "mappings": "wMACOA,MAAM,uB,GAIAA,MAAM,gB,GAoHNA,MAAM,gB,GAEFA,MAAM,kB,GACJA,MAAM,wB,GAGNA,MAAM,e,GACJA,MAAM,gB,GAOVA,MAAM,kB,GACJA,MAAM,6B,GAGNA,MAAM,e,GACJA,MAAM,gB,GAOVA,MAAM,kB,GACJA,MAAM,0B,GAGNA,MAAM,e,GACJA,MAAM,gB,GAOVA,MAAM,kB,GAIJA,MAAM,e,GACJA,MAAM,gB,GAUVA,MAAM,gB,GAURC,IAAI,iBAAiBC,MAAA,kB,GAWrBF,MAAM,e,GAKNA,MAAM,e,GAKNA,MAAM,e,GAKNA,MAAM,e,GAONA,MAAM,oB,GACJA,MAAM,kB,GAEHA,MAAM,oB,GAYXA,MAAM,iB,GAwBNA,MAAM,iB,GAQHA,MAAM,kB,GAGTA,MAAM,iB,GAQHA,MAAM,kB,GAGTA,MAAM,iB,GAEJA,MAAM,c,GA4FTA,MAAM,iB,itBAnXlBG,EAAAA,EAAAA,IAyXM,MAzXNC,EAyXM,EAvXJC,EAAAA,EAAAA,IAgHUC,GAAA,CAhHDN,MAAM,eAAeO,OAAO,S,CACxBC,QAAMC,EAAAA,EAAAA,IACf,IAgBM,EAhBNC,EAAAA,EAAAA,IAgBM,MAhBNC,EAgBM,C,eAfJD,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVL,EAAAA,EAAAA,IAakBO,EAAA,M,iBAZhB,IAOY,EAPZP,EAAAA,EAAAA,IAOYQ,EAAA,CANTC,KAAMC,EAAAC,UAAY,SAAW,UAC7BC,QAASF,EAAAG,WACTC,QAAOC,EAAAC,WACPC,UAAWP,EAAAQ,c,kBACZ,IAAuE,EAAvElB,EAAAA,EAAAA,IAAuEmB,EAAA,M,iBAA9D,IAA+B,CAAbT,EAAAC,Y,WAAaS,EAAAA,EAAAA,IAAqBC,EAAA,CAAAC,IAAA,O,WAApDF,EAAAA,EAAAA,IAA+BG,EAAA,CAAAD,IAAA,O,eAA+B,KACvEE,EAAAA,EAAAA,IAAGd,EAAAC,UAAY,OAAS,QAAZ,K,iDAEdX,EAAAA,EAAAA,IAGYQ,EAAA,CAHAM,QAAKW,EAAA,KAAAA,EAAA,GAAAC,GAAEhB,EAAAiB,kBAAmB,GAAOV,SAAUP,EAAAC,W,kBACrD,IAA8B,EAA9BX,EAAAA,EAAAA,IAA8BmB,EAAA,M,iBAArB,IAAW,EAAXnB,EAAAA,EAAAA,IAAW4B,K,6BAAU,W,0DAOtC,IA0FU,EA1FV5B,EAAAA,EAAAA,IA0FU6B,GAAA,CA1FAC,MAAOpB,EAAAqB,WAAY,cAAY,QAAQC,KAAK,W,kBACpD,IA4CS,EA5CThC,EAAAA,EAAAA,IA4CSiC,EAAA,CA5CAC,OAAQ,IAAE,C,iBACjB,IAeS,EAfTlC,EAAAA,EAAAA,IAeSmC,EAAA,CAfAC,KAAM,GAAC,C,iBACd,IAae,EAbfpC,EAAAA,EAAAA,IAaeqC,EAAA,CAbDC,MAAM,QAAM,C,iBACxB,IAWY,EAXZtC,EAAAA,EAAAA,IAWYuC,EAAA,C,WAVD7B,EAAAQ,a,qCAAAR,EAAAQ,aAAYQ,GACrBc,YAAY,UACXvB,SAAUP,EAAAC,UACXd,MAAA,gB,kBAEE,IAAqB,G,aADvBC,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJKhC,EAAAiC,MAARC,K,WADTxB,EAAAA,EAAAA,IAKYyB,EAAA,CAHTvB,IAAKsB,EAAKE,GACVR,MAAOM,EAAKG,SACZC,MAAOJ,EAAKE,I,uFAKrB9C,EAAAA,EAAAA,IAeSmC,EAAA,CAfAC,KAAM,GAAC,C,iBACd,IAae,EAbfpC,EAAAA,EAAAA,IAaeqC,EAAA,CAbDC,MAAM,QAAM,C,iBACxB,IAWY,EAXZtC,EAAAA,EAAAA,IAWYuC,EAAA,C,WAVD7B,EAAAqB,WAAWkB,O,qCAAXvC,EAAAqB,WAAWkB,OAAMvB,GAC1Bc,YAAY,UACXvB,SAAUP,EAAAC,UACXd,MAAA,gB,kBAEE,IAA2B,G,aAD7BC,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJIhC,EAAAwC,aAAPC,K,WADT/B,EAAAA,EAAAA,IAKYyB,EAAA,CAHTvB,IAAK6B,EAAIL,GACTR,MAAOa,EAAIC,KACXJ,MAAOG,EAAIL,I,uFAKpB9C,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,QAAM,C,iBACxB,IAMY,EANZtC,EAAAA,EAAAA,IAMYuC,EAAA,C,WALD7B,EAAAqB,WAAWsB,K,qCAAX3C,EAAAqB,WAAWsB,KAAI3B,GACvBT,SAAUP,EAAAC,UACXd,MAAA,gB,kBACA,IAAmD,EAAnDG,EAAAA,EAAAA,IAAmD6C,EAAA,CAAxCP,MAAM,OAAOU,MAAM,YAC9BhD,EAAAA,EAAAA,IAAyD6C,EAAA,CAA9CP,MAAM,QAAQU,MAAM,kB,2DAKvChD,EAAAA,EAAAA,IA2CSiC,EAAA,CA3CAC,OAAQ,IAAE,C,iBACjB,IAUS,EAVTlC,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,SAAO,C,iBACzB,IAMkB,EANlBtC,EAAAA,EAAAA,IAMkBsD,GAAA,C,WALP5C,EAAAqB,WAAWwB,M,qCAAX7C,EAAAqB,WAAWwB,MAAK7B,GACxB8B,IAAK,EACLC,IAAK,IACLxC,SAAUP,EAAAC,UACXd,MAAA,gB,mDAING,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,QAAM,C,iBACxB,IAMkB,EANlBtC,EAAAA,EAAAA,IAMkBsD,GAAA,C,WALP5C,EAAAqB,WAAW2B,W,qCAAXhD,EAAAqB,WAAW2B,WAAUhC,GAC7B8B,IAAK,EACLC,IAAK,IACLxC,SAAUP,EAAAC,UACXd,MAAA,gB,mDAING,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,YAAU,C,iBAC5B,IAMkB,EANlBtC,EAAAA,EAAAA,IAMkBsD,GAAA,C,WALP5C,EAAAqB,WAAW4B,S,qCAAXjD,EAAAqB,WAAW4B,SAAQjC,GAC3B8B,IAAK,EACLC,IAAK,KACLxC,SAAUP,EAAAC,UACXd,MAAA,gB,mDAING,EAAAA,EAAAA,IAQSmC,EAAA,CARAC,KAAM,GAAC,C,iBACd,IAMe,EANfpC,EAAAA,EAAAA,IAMeqC,EAAA,CANDC,MAAM,QAAM,C,iBACxB,IAIW,EAJXtC,EAAAA,EAAAA,IAIW4D,GAAA,C,WAHAlD,EAAAqB,WAAW8B,Y,qCAAXnD,EAAAqB,WAAW8B,YAAWnC,GAC/Bc,YAAY,OACXvB,SAAUP,EAAAC,W,uFASvBX,EAAAA,EAAAA,IAyKSiC,EAAA,CAzKAC,OAAQ,GAAIrC,MAAA,uB,kBAEnB,IAkES,EAlETG,EAAAA,EAAAA,IAkESmC,EAAA,CAlEAC,KAAM,IAAE,C,iBACf,IAgDM,EAhDN/B,EAAAA,EAAAA,IAgDM,MAhDNyD,EAgDM,EA/CJ9D,EAAAA,EAAAA,IAUUC,GAAA,CAVDN,MAAM,cAAcO,OAAO,S,kBAClC,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARN0D,EAQM,EAPJ1D,EAAAA,EAAAA,IAEM,MAFN2D,EAEM,EADJhE,EAAAA,EAAAA,IAAkCmB,EAAA,M,iBAAzB,IAAe,EAAfnB,EAAAA,EAAAA,IAAeiE,M,SAE1B5D,EAAAA,EAAAA,IAGM,MAHN6D,EAGM,EAFJ7D,EAAAA,EAAAA,IAA6D,MAA7D8D,GAA6D3C,EAAAA,EAAAA,IAAhCd,EAAA0D,eAAeC,KAAO,GAAJ,G,eAC/ChE,EAAAA,EAAAA,IAAmC,OAA9BV,MAAM,gBAAe,OAAG,U,OAKnCK,EAAAA,EAAAA,IAUUC,GAAA,CAVDN,MAAM,cAAcO,OAAO,S,kBAClC,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARNiE,EAQM,EAPJjE,EAAAA,EAAAA,IAEM,MAFNkE,EAEM,EADJvE,EAAAA,EAAAA,IAA4BmB,EAAA,M,iBAAnB,IAAS,EAATnB,EAAAA,EAAAA,IAASwE,M,SAEpBnE,EAAAA,EAAAA,IAGM,MAHNoE,EAGM,EAFJpE,EAAAA,EAAAA,IAA6E,MAA7EqE,GAA6ElD,EAAAA,EAAAA,IAAhDd,EAAA0D,eAAeO,mBAAqB,GAAI,KAAE,G,eACvEtE,EAAAA,EAAAA,IAAsC,OAAjCV,MAAM,gBAAe,UAAM,U,OAKtCK,EAAAA,EAAAA,IAUUC,GAAA,CAVDN,MAAM,cAAcO,OAAO,S,kBAClC,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARNuE,EAQM,EAPJvE,EAAAA,EAAAA,IAEM,MAFNwE,EAEM,EADJ7E,EAAAA,EAAAA,IAA2BmB,EAAA,M,iBAAlB,IAAQ,EAARnB,EAAAA,EAAAA,IAAQ8E,M,SAEnBzE,EAAAA,EAAAA,IAGM,MAHN0E,EAGM,EAFJ1E,EAAAA,EAAAA,IAA+D,MAA/D2E,GAA+DxD,EAAAA,EAAAA,IAAlCd,EAAA0D,eAAeb,OAAS,GAAJ,G,eACjDlD,EAAAA,EAAAA,IAAoC,OAA/BV,MAAM,gBAAe,QAAI,U,OAKpCK,EAAAA,EAAAA,IAUUC,GAAA,CAVDN,MAAM,cAAcO,OAAO,S,kBAClC,IAQM,EARNG,EAAAA,EAAAA,IAQM,MARN4E,EAQM,EAPJ5E,EAAAA,EAAAA,IAEM,OAFDV,OAAKuF,EAAAA,EAAAA,IAAA,CAAC,yBAAiCnE,EAAAoE,kBAAkBzE,EAAA0D,eAAegB,e,EAC3EpF,EAAAA,EAAAA,IAAoCmB,EAAA,M,iBAA3B,IAAiB,EAAjBnB,EAAAA,EAAAA,IAAiBqF,M,WAE5BhF,EAAAA,EAAAA,IAGM,MAHNiF,EAGM,EAFJjF,EAAAA,EAAAA,IAAkF,MAAlFkF,GAAkF/D,EAAAA,EAAAA,KAApDd,EAAA0D,eAAegB,YAAc,GAAGI,QAAQ,IAAK,IAAC,G,eAC5EnF,EAAAA,EAAAA,IAAmC,OAA9BV,MAAM,gBAAe,OAAG,U,SAOrCK,EAAAA,EAAAA,IAaUC,GAAA,CAbDJ,MAAA,sBAA0BK,OAAO,S,CAC7BC,QAAMC,EAAAA,EAAAA,IACf,IAQM,EARNC,EAAAA,EAAAA,IAQM,MARNoF,EAQM,C,eAPJpF,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVL,EAAAA,EAAAA,IAKiB0F,GAAA,C,WALQhF,EAAAiF,U,qCAAAjF,EAAAiF,UAASjE,GAAEM,KAAK,S,kBACvC,IAAkD,EAAlDhC,EAAAA,EAAAA,IAAkD4F,GAAA,CAAjCtD,MAAM,OAAK,C,iBAAC,IAAGb,EAAA,MAAAA,EAAA,M,QAAH,U,eAC7BzB,EAAAA,EAAAA,IAA6D4F,GAAA,CAA5CtD,MAAM,iBAAe,C,iBAAC,IAAIb,EAAA,MAAAA,EAAA,M,QAAJ,W,eACvCzB,EAAAA,EAAAA,IAAoD4F,GAAA,CAAnCtD,MAAM,SAAO,C,iBAAC,IAAGb,EAAA,MAAAA,EAAA,M,QAAH,U,eAC/BzB,EAAAA,EAAAA,IAAyD4F,GAAA,CAAxCtD,MAAM,cAAY,C,iBAAC,IAAGb,EAAA,MAAAA,EAAA,M,QAAH,U,4DAI1C,IAAuD,EAAvDpB,EAAAA,EAAAA,IAAuD,MAAvDwF,EAAuD,Y,eAK3D7F,EAAAA,EAAAA,IAiGSmC,EAAA,CAjGAC,KAAM,GAAC,C,iBACd,IA0DU,EA1DVpC,EAAAA,EAAAA,IA0DUC,GAAA,CA1DDN,MAAM,eAAeO,OAAO,S,CACxBC,QAAMC,EAAAA,EAAAA,IACf,IAAaqB,EAAA,MAAAA,EAAA,MAAbpB,EAAAA,EAAAA,IAAa,UAAT,QAAI,M,iBAGV,IAGM,EAHNA,EAAAA,EAAAA,IAGM,MAHNyF,EAGM,C,eAFJzF,EAAAA,EAAAA,IAAuC,QAAjCV,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAkF+F,GAAA,CAAzEtF,KAAMM,EAAAiF,cAActF,EAAAuF,a,kBAAa,IAA+B,E,iBAA5BlF,EAAAmF,cAAcxF,EAAAuF,aAAU,K,oBAGvE5F,EAAAA,EAAAA,IAGM,MAHN8F,EAGM,C,eAFJ9F,EAAAA,EAAAA,IAAuC,QAAjCV,MAAM,gBAAe,SAAK,KAChCU,EAAAA,EAAAA,IAA+C,aAAAmB,EAAAA,EAAAA,IAAtCT,EAAAqF,eAAe1F,EAAA2F,eAAY,MAGtChG,EAAAA,EAAAA,IAGM,MAHNiG,EAGM,C,eAFJjG,EAAAA,EAAAA,IAAuC,QAAjCV,MAAM,gBAAe,SAAK,KAChCU,EAAAA,EAAAA,IAA4C,aAAAmB,EAAAA,EAAAA,IAAnCT,EAAAwF,WAAW7F,EAAA8F,gBAAa,MAGnCnG,EAAAA,EAAAA,IAGM,MAHNoG,EAGM,C,eAFJpG,EAAAA,EAAAA,IAAuC,QAAjCV,MAAM,gBAAe,SAAK,KAChCU,EAAAA,EAAAA,IAA+C,aAAAmB,EAAAA,EAAAA,IAAtCT,EAAAwF,WAAWxF,EAAA2F,mBAAgB,MAGtC1G,EAAAA,EAAAA,IAAyB2G,KAEzBtG,EAAAA,EAAAA,IAUM,MAVNuG,EAUM,EATJvG,EAAAA,EAAAA,IAGM,MAHNwG,EAGM,C,uBAHsB,YAE1BxG,EAAAA,EAAAA,IAAyD,OAAzDyG,GAAyDtF,EAAAA,EAAAA,IAAvBd,EAAAqG,cAAe,IAAC,MAEpD/G,EAAAA,EAAAA,IAIcgH,GAAA,CAHXC,WAAYvG,EAAAqG,aACZG,OAAyB,MAAjBxG,EAAAqG,aAAuB,UAAY,UAC3C,eAAc,G,mCAInB/G,EAAAA,EAAAA,IAAyB2G,KAGzBtG,EAAAA,EAAAA,IAeM,MAfN8G,EAeM,EAdJnH,EAAAA,EAAAA,IAMYQ,EAAA,CALVwB,KAAK,QACLvB,KAAK,UACJK,QAAOC,EAAAqG,WACPnG,UAAWP,EAAA2G,iB,kBAAiB,IAE/B5F,EAAA,MAAAA,EAAA,M,QAF+B,a,wCAG/BzB,EAAAA,EAAAA,IAMYQ,EAAA,CALVwB,KAAK,QACLvB,KAAK,OACJK,QAAOC,EAAAuG,cACPrG,UAAWP,EAAA2G,iB,kBAAiB,IAE/B5F,EAAA,MAAAA,EAAA,M,QAF+B,a,kDAOnCzB,EAAAA,EAAAA,IAkCUC,GAAA,CAlCDJ,MAAA,sBAA0BK,OAAO,S,CAC7BC,QAAMC,EAAAA,EAAAA,IACf,IAAaqB,EAAA,MAAAA,EAAA,MAAbpB,EAAAA,EAAAA,IAAa,UAAT,QAAI,M,iBAGV,IASM,EATNA,EAAAA,EAAAA,IASM,MATNkH,EASM,C,eARJlH,EAAAA,EAAAA,IAAwC,OAAnCV,MAAM,kBAAiB,UAAM,KAClCK,EAAAA,EAAAA,IAKcgH,GAAA,CAJXC,WAAYvG,EAAA8G,gBAAgBC,IAC5BP,OAAQnG,EAAA2G,aAAahH,EAAA8G,gBAAgBC,KACrC,aAAW,EACX,eAAc,G,iCAEjBpH,EAAAA,EAAAA,IAA8D,OAA9DsH,GAA8DnG,EAAAA,EAAAA,IAA9Bd,EAAA8G,gBAAgBC,KAAM,IAAC,MAGzDpH,EAAAA,EAAAA,IASM,MATNuH,EASM,C,eARJvH,EAAAA,EAAAA,IAAuC,OAAlCV,MAAM,kBAAiB,SAAK,KACjCK,EAAAA,EAAAA,IAKcgH,GAAA,CAJXC,WAAYvG,EAAA8G,gBAAgBK,OAC5BX,OAAQnG,EAAA+G,gBAAgBpH,EAAA8G,gBAAgBK,QACxC,aAAW,EACX,eAAc,G,iCAEjBxH,EAAAA,EAAAA,IAAiE,OAAjE0H,GAAiEvG,EAAAA,EAAAA,IAAjCd,EAAA8G,gBAAgBK,QAAS,IAAC,MAG5DxH,EAAAA,EAAAA,IAMM,MANN2H,EAMM,C,eALJ3H,EAAAA,EAAAA,IAAsC,OAAjCV,MAAM,kBAAiB,QAAI,KAChCU,EAAAA,EAAAA,IAGM,MAHN4H,EAGM,EAFJ5H,EAAAA,EAAAA,IAA8D,YAAxD,MAAEmB,EAAAA,EAAAA,IAAGT,EAAAmH,YAAYxH,EAAA8G,gBAAgBW,eAAY,IACnD9H,EAAAA,EAAAA,IAA8D,YAAxD,MAAEmB,EAAAA,EAAAA,IAAGT,EAAAmH,YAAYxH,EAAA8G,gBAAgBY,eAAY,S,uBAQ7DpI,EAAAA,EAAAA,IAuFYqI,GAAA,C,WAvFQ3H,EAAAiB,iB,uCAAAjB,EAAAiB,iBAAgBD,GAAE4G,MAAM,OAAOC,MAAM,O,CAiF5CC,QAAMpI,EAAAA,EAAAA,IACf,IAGO,EAHPC,EAAAA,EAAAA,IAGO,OAHPoI,EAGO,EAFLzI,EAAAA,EAAAA,IAA2DQ,EAAA,CAA/CM,QAAKW,EAAA,MAAAA,EAAA,IAAAC,GAAEhB,EAAAiB,kBAAmB,I,kBAAO,IAAEF,EAAA,MAAAA,EAAA,M,QAAF,S,eAC7CzB,EAAAA,EAAAA,IAAsEQ,EAAA,CAA3DC,KAAK,UAAWK,QAAOC,EAAA2H,oB,kBAAoB,IAAIjH,EAAA,MAAAA,EAAA,M,QAAJ,W,iDAnF1D,IA8EU,EA9EVzB,EAAAA,EAAAA,IA8EU2I,GAAA,C,WA9EQjI,EAAAkI,U,uCAAAlI,EAAAkI,UAASlH,I,kBACzB,IAiCc,EAjCd1B,EAAAA,EAAAA,IAiCc6I,GAAA,CAjCDvG,MAAM,OAAOc,KAAK,S,kBAC7B,IA+BU,EA/BVpD,EAAAA,EAAAA,IA+BU6B,GAAA,CA/BAC,MAAOpB,EAAAoI,eAAgB,cAAY,S,kBAC3C,IAWS,EAXT9I,EAAAA,EAAAA,IAWSiC,EAAA,CAXAC,OAAQ,IAAE,C,iBACjB,IAIS,EAJTlC,EAAAA,EAAAA,IAISmC,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFfpC,EAAAA,EAAAA,IAEeqC,EAAA,CAFDC,MAAM,WAAS,C,iBAC3B,IAA0F,EAA1FtC,EAAAA,EAAAA,IAA0FsD,GAAA,C,WAAhE5C,EAAAoI,eAAeC,W,qCAAfrI,EAAAoI,eAAeC,WAAUrH,GAAG8B,IAAK,EAAIC,IAAK,I,wCAGxEzD,EAAAA,EAAAA,IAISmC,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFfpC,EAAAA,EAAAA,IAEeqC,EAAA,CAFDC,MAAM,WAAS,C,iBAC3B,IAAwF,EAAxFtC,EAAAA,EAAAA,IAAwFsD,GAAA,C,WAA9D5C,EAAAoI,eAAeE,Q,uCAAftI,EAAAoI,eAAeE,QAAOtH,GAAG8B,IAAK,EAAIC,IAAK,K,gDAIvEzD,EAAAA,EAAAA,IAiBSiC,EAAA,CAjBAC,OAAQ,IAAE,C,iBACjB,IAIS,EAJTlC,EAAAA,EAAAA,IAISmC,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFfpC,EAAAA,EAAAA,IAEeqC,EAAA,CAFDC,MAAM,QAAM,C,iBACxB,IAA2F,EAA3FtC,EAAAA,EAAAA,IAA2FsD,GAAA,C,WAAjE5C,EAAAoI,eAAeG,Y,uCAAfvI,EAAAoI,eAAeG,YAAWvH,GAAG8B,IAAK,EAAIC,IAAK,I,wCAGzEzD,EAAAA,EAAAA,IAUSmC,EAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQe,EARfpC,EAAAA,EAAAA,IAQeqC,EAAA,CARDC,MAAM,QAAM,C,iBACxB,IAMY,EANZtC,EAAAA,EAAAA,IAMYuC,EAAA,C,WANQ7B,EAAAoI,eAAeI,U,uCAAfxI,EAAAoI,eAAeI,UAASxH,GAAE7B,MAAA,gB,kBAC5C,IAA8C,EAA9CG,EAAAA,EAAAA,IAA8C6C,EAAA,CAAnCP,MAAM,KAAKU,MAAM,SAC5BhD,EAAAA,EAAAA,IAAgD6C,EAAA,CAArCP,MAAM,KAAKU,MAAM,WAC5BhD,EAAAA,EAAAA,IAAkD6C,EAAA,CAAvCP,MAAM,KAAKU,MAAM,aAC5BhD,EAAAA,EAAAA,IAA+C6C,EAAA,CAApCP,MAAM,KAAKU,MAAM,UAC5BhD,EAAAA,EAAAA,IAAgD6C,EAAA,CAArCP,MAAM,KAAKU,MAAM,Y,4EAOxChD,EAAAA,EAAAA,IAuBc6I,GAAA,CAvBDvG,MAAM,QAAQc,KAAK,e,kBAC9B,IAqBU,EArBVpD,EAAAA,EAAAA,IAqBU6B,GAAA,CArBAC,MAAOpB,EAAAoI,eAAgB,cAAY,S,kBAC3C,IASe,EATf9I,EAAAA,EAAAA,IASeqC,EAAA,CATDC,MAAM,OAAK,C,iBACvB,IAOY,EAPZtC,EAAAA,EAAAA,IAOYuC,EAAA,C,WAPQ7B,EAAAoI,eAAeK,c,uCAAfzI,EAAAoI,eAAeK,cAAazH,GAAE7B,MAAA,gB,kBAE9C,IAAyB,G,aAD3BC,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJOhC,EAAA0I,QAAVC,K,WADTjI,EAAAA,EAAAA,IAKYyB,EAAA,CAHTvB,IAAK+H,EAAOvG,GACZR,MAAK,GAAK+G,EAAOjG,SAASiG,EAAOC,WACjCtG,MAAOqG,EAAOvG,I,oEAIrB9C,EAAAA,EAAAA,IASeqC,EAAA,CATDC,MAAM,QAAM,C,iBACxB,IAOY,EAPZtC,EAAAA,EAAAA,IAOYuC,EAAA,C,WAPQ7B,EAAAoI,eAAeS,e,uCAAf7I,EAAAoI,eAAeS,eAAc7H,GAAE8H,SAAA,GAAS3J,MAAA,gB,kBAExD,IAAyB,G,aAD3BC,EAAAA,EAAAA,IAKY2C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJOhC,EAAA0I,QAAVC,K,WADTjI,EAAAA,EAAAA,IAKYyB,EAAA,CAHTvB,IAAK+H,EAAOvG,GACZR,MAAK,GAAK+G,EAAOjG,SAASiG,EAAOC,WACjCtG,MAAOqG,EAAOvG,I,gGAMzB9C,EAAAA,EAAAA,IAkBc6I,GAAA,CAlBDvG,MAAM,OAAOc,KAAK,U,kBAC7B,IAgBU,EAhBVpD,EAAAA,EAAAA,IAgBU6B,GAAA,CAhBAC,MAAOpB,EAAAoI,eAAgB,cAAY,S,kBAC3C,IAIe,EAJf9I,EAAAA,EAAAA,IAIeqC,EAAA,CAJDC,MAAM,UAAQ,C,iBAC1B,IAEkB,EAFlBtC,EAAAA,EAAAA,IAEkBsD,GAAA,C,WAFQ5C,EAAAoI,eAAeW,wB,uCAAf/I,EAAAoI,eAAeW,wBAAuB/H,GAAG8B,IAAK,G,CAC3DkG,QAAMtJ,EAAAA,EAAAA,IAAC,IAAEqB,EAAA,MAAAA,EAAA,M,QAAF,S,gCAGtBzB,EAAAA,EAAAA,IAIeqC,EAAA,CAJDC,MAAM,SAAO,C,iBACzB,IAEkB,EAFlBtC,EAAAA,EAAAA,IAEkBsD,GAAA,C,WAFQ5C,EAAAoI,eAAea,qB,uCAAfjJ,EAAAoI,eAAea,qBAAoBjI,GAAG8B,IAAK,EAAIC,IAAK,K,CACjEiG,QAAMtJ,EAAAA,EAAAA,IAAC,IAACqB,EAAA,MAAAA,EAAA,M,QAAD,Q,gCAGtBzB,EAAAA,EAAAA,IAIeqC,EAAA,CAJDC,MAAM,SAAO,C,iBACzB,IAEkB,EAFlBtC,EAAAA,EAAAA,IAEkBsD,GAAA,C,WAFQ5C,EAAAoI,eAAec,c,uCAAflJ,EAAAoI,eAAec,cAAalI,GAAG8B,IAAK,G,CACjDkG,QAAMtJ,EAAAA,EAAAA,IAAC,IAAGqB,EAAA,MAAAA,EAAA,M,QAAH,U,mKAqBlC,GACE2B,KAAM,gCACNyG,WAAY,CACVC,UAAS,YACTC,WAAU,aACVC,QAAO,UACPC,YAAW,cACXC,MAAK,QACLC,KAAI,OACJC,cAAaA,EAAAA,eAEfC,IAAAA,GACE,MAAO,CAELnJ,aAAc,KACda,WAAY,CACVkB,OAAQ,KACRI,KAAM,SACNE,MAAO,GACPG,WAAY,EACZC,SAAU,EACVE,YAAa,IAIfiF,eAAgB,CACdC,WAAY,EACZC,QAAS,GACTC,YAAa,EACbC,UAAW,OACXC,cAAe,KACfI,eAAgB,GAChBE,wBAAyB,IACzBE,qBAAsB,EACtBC,cAAe,KAIjBjJ,WAAW,EACXE,YAAY,EACZoF,WAAY,OACZO,cAAe,KACfH,aAAc,EACdU,aAAc,EACdM,gBAAiB,KAGjBjD,eAAgB,CACdC,IAAK,EACLM,kBAAmB,EACnBpB,MAAO,EACP6B,WAAY,GAEdoC,gBAAiB,CACfC,IAAK,EACLI,OAAQ,EACRM,aAAc,EACdC,aAAc,GAIhBkC,MAAO,KACP3E,UAAW,MACX4E,UAAW,CACTC,MAAO,GACPnG,IAAK,GACLoG,cAAe,GACflH,MAAO,GACP6B,WAAY,IAIdzC,MAAO,GACPO,aAAc,GACdkG,QAAS,GAGTzH,kBAAkB,EAClBiH,UAAW,QAGX8B,aAAc,KACdC,YAAa,KAGbC,OAAQ,GACRC,QAAS,KAEb,EACAC,SAAU,CACRpE,gBAAAA,GACE,IAAKqE,KAAKvE,gBAAkBuE,KAAKhJ,WAAW4B,SAAU,OAAO,KAC7D,MAAMqH,EAAY,IAAIC,KAAKF,KAAKvE,eAChC,OAAO,IAAIyE,KAAKD,EAAUE,UAAuC,GAA3BH,KAAKhJ,WAAW4B,SAAgB,IACxE,GAEF,aAAMwH,SACEJ,KAAKK,kBACXL,KAAKM,YACLN,KAAKO,kBACP,EACAC,aAAAA,GACER,KAAKS,SACP,EACAC,QAAS,CACP,qBAAML,GACJ,IAEE,MAAMM,QAAsBX,KAAKY,KAAKC,kCACtCb,KAAKpI,MAAQ+I,EAAcrB,KAAKwB,SAAW,GAG3C,MAAMC,QAAoBf,KAAKY,KAAKI,sBACpChB,KAAK7H,aAAe4I,EAAYzB,KAAKwB,SAAW,GAGhD,MAAMG,QAAwBjB,KAAKY,KAAKM,yBACxClB,KAAK3B,QAAU4C,EAAgB3B,KAAKwB,SAAW,EAEjD,CAAE,MAAOK,GACPC,QAAQD,MAAM,YAAaA,GAC3BnB,KAAKqB,SAASF,MAAM,SACtB,CACF,EAEA,gBAAMlL,GACA+J,KAAKpK,gBACDoK,KAAKsB,iBAELtB,KAAKuB,WAEf,EAEA,eAAMA,GACJ,GAAKvB,KAAK7J,aAAV,CAKA6J,KAAKlK,YAAa,EAElB,IACE,MAAM0L,QAAiBxB,KAAKY,KAAKa,4BAA4BzB,KAAK7J,aAAc,IAC3E6J,KAAKhJ,cACLgJ,KAAKjC,iBAGViC,KAAKpK,WAAY,EACjBoK,KAAK9E,WAAa,UAClB8E,KAAKvE,cAAgB,IAAIyE,KACzBF,KAAK1D,gBAAkBkF,EAASlC,KAAKoC,UAGjCF,EAASlC,KAAKqC,UAChB3B,KAAKH,OAAS2B,EAASlC,KAAKqC,QAC5B3B,KAAKF,QAAU0B,EAASlC,KAAKsC,SAC7B5B,KAAKqB,SAASQ,QAAQ,iBAAiBL,EAASlC,KAAKqC,YAIvD3B,KAAK8B,mBAEL9B,KAAKqB,SAASQ,QAAQ,UAExB,CAAE,MAAOV,GACPC,QAAQD,MAAM,UAAWA,GACzBnB,KAAKqB,SAASF,MAAM,YAAcA,EAAMK,UAAUlC,MAAMyC,SAAWZ,EAAMY,SAC3E,CAAE,QACA/B,KAAKlK,YAAa,CACpB,CAhCA,MAFEkK,KAAKqB,SAASW,QAAQ,YAmC1B,EAEA,cAAMV,GACJ,IACMtB,KAAK7J,oBACD6J,KAAKY,KAAKqB,6BAA6BjC,KAAK7J,cAGpD6J,KAAKpK,WAAY,EACjBoK,KAAK9E,WAAa,UAClB8E,KAAKkC,sBAELlC,KAAKqB,SAASQ,QAAQ,UAExB,CAAE,MAAOV,GACPC,QAAQD,MAAM,UAAWA,GACzBnB,KAAKqB,SAASF,MAAM,SACtB,CACF,EAEAW,gBAAAA,GACM9B,KAAKL,cACPK,KAAKL,aAAawC,QAGpB,MAAMC,EAAwC,WAA7BC,OAAOC,SAASF,SAAwB,OAAS,MAC5DG,EAAQ,GAAGH,MAAaC,OAAOC,SAASE,+BAA+BxC,KAAK7J,gBAElF6J,KAAKL,aAAe,IAAI8C,UAAUF,GAElCvC,KAAKL,aAAa+C,OAAS,KACzBtB,QAAQuB,IAAI,kBACZ3C,KAAKL,aAAaiD,KAAKC,KAAKC,UAAU,CAAEpN,KAAM,uBAGhDsK,KAAKL,aAAaoD,UAAaC,IAC7B,MAAM1D,EAAOuD,KAAKI,MAAMD,EAAM1D,MAC9BU,KAAKkD,uBAAuB5D,IAG9BU,KAAKL,aAAawD,QAAWhC,IAC3BC,QAAQD,MAAM,iBAAkBA,IAGlCnB,KAAKL,aAAayD,QAAU,KAC1BhC,QAAQuB,IAAI,kBACR3C,KAAKpK,WAEPyN,WAAW,KACTrD,KAAK8B,oBACJ,KAGT,EAEAI,mBAAAA,GACMlC,KAAKL,eACPK,KAAKL,aAAawC,QAClBnC,KAAKL,aAAe,KAExB,EAEAuD,sBAAAA,CAAuB5D,GACrB,OAAQA,EAAK5J,MACX,IAAK,qBACHsK,KAAKsD,cAAchE,EAAKA,MACxB,MACF,IAAK,iBACHU,KAAKuD,oBAAoBjE,EAAKA,MAC9B,MACF,IAAK,cACHU,KAAKwD,iBAAiBlE,EAAK6B,OAC3B,MACF,IAAK,iBACHnB,KAAKyD,oBAAoBnE,EAAKA,MAC9B,MAEN,EAEAgE,aAAAA,CAAchE,GACZU,KAAK3G,eAAiB,CACpBC,IAAKgG,EAAKoE,SAAW,EACrB9J,kBAAmB0F,EAAK1F,mBAAqB,EAC7CpB,MAAO8G,EAAKqE,eAAiB,EAC7BtJ,WAAYiF,EAAKjF,YAAc,GAIjC,MAAMuJ,GAAM,IAAI1D,MAAO2D,qBACvB7D,KAAKR,UAAUC,MAAMqE,KAAKF,GAC1B5D,KAAKR,UAAUlG,IAAIwK,KAAKxE,EAAKoE,SAAW,GACxC1D,KAAKR,UAAUE,cAAcoE,KAAKxE,EAAK1F,mBAAqB,GAC5DoG,KAAKR,UAAUhH,MAAMsL,KAAKxE,EAAKqE,eAAiB,GAChD3D,KAAKR,UAAUnF,WAAWyJ,KAAKxE,EAAKjF,YAAc,GAGlD0J,OAAOC,KAAKhE,KAAKR,WAAWyE,QAAQ1N,IAC9ByJ,KAAKR,UAAUjJ,GAAK2N,OAAS,IAC/BlE,KAAKR,UAAUjJ,GAAK4N,UAIxBnE,KAAKoE,aACP,EAEAX,mBAAAA,CAAoBnE,GACdA,IACFU,KAAK3G,eAAiB,CACpBC,IAAKgG,EAAKoE,SAAW,EACrB9J,kBAAmB0F,EAAK1F,mBAAqB,EAC7CpB,MAAO8G,EAAKqE,eAAiB,EAC7BtJ,WAAYiF,EAAKjF,YAAc,GAGrC,EAEAkJ,mBAAAA,CAAoBjE,GAClBU,KAAKpK,WAAY,EACjBoK,KAAK9E,WAAa,YAClB8E,KAAKhE,aAAe,IACpBgE,KAAKqB,SAASQ,QAAQ,WACtB7B,KAAKkC,qBACP,EAEAsB,gBAAAA,CAAiBrC,GACfnB,KAAKpK,WAAY,EACjBoK,KAAK9E,WAAa,SAClB8E,KAAKqB,SAASF,MAAM,WAAaA,GACjCnB,KAAKkC,qBACP,EAEA5B,SAAAA,GACEN,KAAKqE,UAAU,KACTrE,KAAKsE,MAAMC,iBACbvE,KAAKT,MAAQiF,EAAAA,GAAaxE,KAAKsE,MAAMC,gBACrCvE,KAAKoE,gBAGX,EAEAA,WAAAA,GACE,IAAKpE,KAAKT,MAAO,OAEjB,MAAMkF,EAAS,CACblH,MAAO,CACLmH,KAAM1E,KAAK2E,gBACXC,KAAM,SACNC,UAAW,CACTC,SAAU,KAGdC,QAAS,CACPC,QAAS,QAEXC,MAAO,CACLvP,KAAM,WACN4J,KAAMU,KAAKR,UAAUC,MACrByF,UAAW,CACTC,SAAU,OACVC,OAAQ,KAGZC,MAAO,CACL3P,KAAM,QACN2C,KAAM2H,KAAKsF,gBAEbC,OAAQ,CAAC,CACPjG,KAAMU,KAAKR,UAAUQ,KAAKpF,WAC1BlF,KAAM,OACN8P,QAAQ,EACRC,UAAW,CACTC,QAAS,IAEXC,UAAW,CACTC,MAAO5F,KAAK6F,mBAGhBC,KAAM,CACJlB,KAAM,KACNmB,MAAO,KACPC,OAAQ,MACRC,cAAc,IAIlBjG,KAAKT,MAAM2G,UAAUzB,EACvB,EAEAE,aAAAA,GACE,MAAMwB,EAAS,CACb7M,IAAK,QACLoG,cAAe,SACflH,MAAO,QACP6B,WAAY,SAEd,OAAO8L,EAAOnG,KAAKpF,YAAc,EACnC,EAEA0K,YAAAA,GACE,MAAMc,EAAQ,CACZ9M,IAAK,MACLoG,cAAe,SACflH,MAAO,MACP6B,WAAY,UAEd,OAAO+L,EAAMpG,KAAKpF,YAAc,EAClC,EAEAiL,aAAAA,GACE,MAAMQ,EAAS,CACb/M,IAAK,UACLoG,cAAe,UACflH,MAAO,UACP6B,WAAY,WAEd,OAAOgM,EAAOrG,KAAKpF,YAAc,SACnC,EAEA2F,gBAAAA,GACEP,KAAKJ,YAAc0G,YAAY,KAC7B,GAAItG,KAAKpK,WAAaoK,KAAKvE,cAAe,CAIxC,GAHAuE,KAAK1E,aAAeiL,KAAKC,OAAOtG,KAAK0D,MAAQ,IAAI1D,KAAKF,KAAKvE,eAAe0E,WAAa,KAGnFH,KAAKhJ,WAAW4B,SAAU,CAC5B,MAAM6N,EAA0C,GAA3BzG,KAAKhJ,WAAW4B,SACrCoH,KAAKhE,aAAeuK,KAAK9N,IAAI,IAAMuH,KAAK1E,aAAemL,EAAgB,IACzE,CAGIzG,KAAKhE,cAAgB,KAAOgE,KAAKpK,WACnCoK,KAAKsB,UAET,CAGAtB,KAAK0G,yBACJ,IACL,EAEAA,qBAAAA,GAEE1G,KAAKvD,gBAAkB,CACrBC,IAAK6J,KAAK7N,IAAI,EAAG6N,KAAK9N,IAAI,IAAKuH,KAAKvD,gBAAgBC,IAA8B,IAAvB6J,KAAKI,SAAW,MAC3E7J,OAAQyJ,KAAK7N,IAAI,EAAG6N,KAAK9N,IAAI,IAAKuH,KAAKvD,gBAAgBK,OAAiC,GAAvByJ,KAAKI,SAAW,MACjFvJ,aAAc4C,KAAKvD,gBAAgBW,aAA+B,IAAhBmJ,KAAKI,SACvDtJ,aAAc2C,KAAKvD,gBAAgBY,aAA+B,IAAhBkJ,KAAKI,SAE3D,EAEAlG,OAAAA,GACET,KAAKkC,sBACDlC,KAAKJ,aACPgH,cAAc5G,KAAKJ,aAEjBI,KAAKT,OACPS,KAAKT,MAAMsH,SAEf,EAEAlJ,kBAAAA,GACEqC,KAAKpJ,kBAAmB,EACxBoJ,KAAKqB,SAASQ,QAAQ,QACxB,EAEAxF,UAAAA,GACM2D,KAAK1D,iBACP0D,KAAK8G,QAAQhD,KAAK,CAChBzL,KAAM,2BACN0O,OAAQ,CAAEhP,GAAIiI,KAAK1D,kBAGzB,EAEAC,aAAAA,GACMyD,KAAK1D,iBACP0D,KAAKY,KAAKoG,iBAAiBhH,KAAK1D,gBAEpC,EAGArB,aAAAA,CAAckB,GACZ,MAAM8K,EAAQ,CACZC,KAAM,OACNC,QAAS,UACTC,UAAW,UACXC,OAAQ,SACRC,QAAS,WAEX,OAAOL,EAAM9K,IAAW,MAC1B,EAEAhB,aAAAA,CAAcgB,GACZ,MAAMoL,EAAQ,CACZL,KAAM,MACNC,QAAS,MACTC,UAAW,MACXC,OAAQ,KACRC,QAAS,OAEX,OAAOC,EAAMpL,IAAW,IAC1B,EAEA/B,iBAAAA,CAAkBoN,GAChB,OAAIA,EAAO,EAAU,aACjBA,EAAO,EAAU,eACd,WACT,EAEA7K,YAAAA,CAAa8K,GACX,OAAIA,EAAU,GAAW,YACrBA,EAAU,GAAW,UAClB,SACT,EAEA1K,eAAAA,CAAgB0K,GACd,OAAIA,EAAU,GAAW,YACrBA,EAAU,GAAW,UAClB,SACT,EAEApM,cAAAA,CAAeqM,GACb,IAAKA,EAAS,MAAO,KACrB,MAAMC,EAAQpB,KAAKC,MAAMkB,EAAU,MAC7BE,EAAUrB,KAAKC,MAAOkB,EAAU,KAAQ,IACxCG,EAAOH,EAAU,GACvB,MAAO,GAAGC,MAAUC,MAAYC,IAClC,EAEArM,UAAAA,CAAWsM,GACT,OAAKA,EACE,IAAI5H,KAAK4H,GAAMC,iBADJ,IAEpB,EAEA5K,WAAAA,CAAY6K,GACV,IAAKA,EAAO,MAAO,KACnB,MAAMC,EAAI,KACJC,EAAQ,CAAC,IAAK,KAAM,KAAM,MAC1BC,EAAI5B,KAAKC,MAAMD,KAAK5D,IAAIqF,GAASzB,KAAK5D,IAAIsF,IAChD,OAAOG,YAAYJ,EAAQzB,KAAK8B,IAAIJ,EAAGE,IAAI1N,QAAQ,IAAMyN,EAAMC,EACjE,GAGFG,MAAO,CACL1N,SAAAA,GACEoF,KAAKoE,aACP,I,WC93BJ,MAAMmE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceExecutionOptimized.vue", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceExecutionOptimized.vue?66b5"], "sourcesContent": ["<template>\r\n  <div class=\"execution-container\">\r\n    <!-- 测试配置面板 -->\r\n    <el-card class=\"config-panel\" shadow=\"hover\">\r\n      <template #header>\r\n        <div class=\"panel-header\">\r\n          <h3>性能测试执行</h3>\r\n          <el-button-group>\r\n            <el-button \r\n              :type=\"isRunning ? 'danger' : 'primary'\" \r\n              :loading=\"isStarting\"\r\n              @click=\"toggleTest\"\r\n              :disabled=\"!selectedTask\">\r\n              <el-icon><VideoPlay v-if=\"!isRunning\" /><VideoPause v-else /></el-icon>\r\n              {{ isRunning ? '停止测试' : '开始测试' }}\r\n            </el-button>\r\n            <el-button @click=\"showConfigDialog = true\" :disabled=\"isRunning\">\r\n              <el-icon><Setting /></el-icon>\r\n              配置\r\n            </el-button>\r\n          </el-button-group>\r\n        </div>\r\n      </template>\r\n\r\n      <el-form :model=\"testConfig\" label-width=\"120px\" size=\"default\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"选择任务\">\r\n              <el-select \r\n                v-model=\"selectedTask\" \r\n                placeholder=\"请选择性能任务\"\r\n                :disabled=\"isRunning\"\r\n                style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"task in tasks\"\r\n                  :key=\"task.id\"\r\n                  :label=\"task.taskName\"\r\n                  :value=\"task.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"测试环境\">\r\n              <el-select \r\n                v-model=\"testConfig.env_id\" \r\n                placeholder=\"请选择测试环境\"\r\n                :disabled=\"isRunning\"\r\n                style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"env in environments\"\r\n                  :key=\"env.id\"\r\n                  :label=\"env.name\"\r\n                  :value=\"env.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"执行模式\">\r\n              <el-select \r\n                v-model=\"testConfig.mode\" \r\n                :disabled=\"isRunning\"\r\n                style=\"width: 100%\">\r\n                <el-option label=\"单机模式\" value=\"single\"></el-option>\r\n                <el-option label=\"分布式模式\" value=\"distributed\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"并发用户数\">\r\n              <el-input-number \r\n                v-model=\"testConfig.users\" \r\n                :min=\"1\" \r\n                :max=\"10000\"\r\n                :disabled=\"isRunning\"\r\n                style=\"width: 100%\">\r\n              </el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"启动速率\">\r\n              <el-input-number \r\n                v-model=\"testConfig.spawn_rate\" \r\n                :min=\"1\" \r\n                :max=\"100\"\r\n                :disabled=\"isRunning\"\r\n                style=\"width: 100%\">\r\n              </el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"持续时间(分钟)\">\r\n              <el-input-number \r\n                v-model=\"testConfig.duration\" \r\n                :min=\"1\" \r\n                :max=\"1440\"\r\n                :disabled=\"isRunning\"\r\n                style=\"width: 100%\">\r\n              </el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"报告名称\">\r\n              <el-input \r\n                v-model=\"testConfig.report_name\" \r\n                placeholder=\"自动生成\"\r\n                :disabled=\"isRunning\">\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n    </el-card>\r\n\r\n    <!-- 实时监控面板 -->\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n      <!-- 左侧指标卡片 -->\r\n      <el-col :span=\"18\">\r\n        <div class=\"metrics-grid\">\r\n          <el-card class=\"metric-card\" shadow=\"hover\">\r\n            <div class=\"metric-content\">\r\n              <div class=\"metric-icon tps-icon\">\r\n                <el-icon><TrendCharts /></el-icon>\r\n              </div>\r\n              <div class=\"metric-info\">\r\n                <div class=\"metric-value\">{{ currentMetrics.tps || 0 }}</div>\r\n                <div class=\"metric-label\">TPS</div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n\r\n          <el-card class=\"metric-card\" shadow=\"hover\">\r\n            <div class=\"metric-content\">\r\n              <div class=\"metric-icon response-icon\">\r\n                <el-icon><Timer /></el-icon>\r\n              </div>\r\n              <div class=\"metric-info\">\r\n                <div class=\"metric-value\">{{ currentMetrics.avg_response_time || 0 }}ms</div>\r\n                <div class=\"metric-label\">平均响应时间</div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n\r\n          <el-card class=\"metric-card\" shadow=\"hover\">\r\n            <div class=\"metric-content\">\r\n              <div class=\"metric-icon users-icon\">\r\n                <el-icon><User /></el-icon>\r\n              </div>\r\n              <div class=\"metric-info\">\r\n                <div class=\"metric-value\">{{ currentMetrics.users || 0 }}</div>\r\n                <div class=\"metric-label\">活跃用户</div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n\r\n          <el-card class=\"metric-card\" shadow=\"hover\">\r\n            <div class=\"metric-content\">\r\n              <div class=\"metric-icon error-icon\" :class=\"getErrorRateClass(currentMetrics.error_rate)\">\r\n                <el-icon><WarningFilled /></el-icon>\r\n              </div>\r\n              <div class=\"metric-info\">\r\n                <div class=\"metric-value\">{{ (currentMetrics.error_rate || 0).toFixed(2) }}%</div>\r\n                <div class=\"metric-label\">错误率</div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 实时图表 -->\r\n        <el-card style=\"margin-top: 20px;\" shadow=\"hover\">\r\n          <template #header>\r\n            <div class=\"chart-header\">\r\n              <h4>实时性能趋势</h4>\r\n              <el-radio-group v-model=\"chartType\" size=\"small\">\r\n                <el-radio-button label=\"tps\">TPS</el-radio-button>\r\n                <el-radio-button label=\"response_time\">响应时间</el-radio-button>\r\n                <el-radio-button label=\"users\">用户数</el-radio-button>\r\n                <el-radio-button label=\"error_rate\">错误率</el-radio-button>\r\n              </el-radio-group>\r\n            </div>\r\n          </template>\r\n          <div ref=\"chartContainer\" style=\"height: 300px;\"></div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 右侧状态面板 -->\r\n      <el-col :span=\"6\">\r\n        <el-card class=\"status-panel\" shadow=\"hover\">\r\n          <template #header>\r\n            <h4>测试状态</h4>\r\n          </template>\r\n\r\n          <div class=\"status-item\">\r\n            <span class=\"status-label\">测试状态:</span>\r\n            <el-tag :type=\"getStatusType(testStatus)\">{{ getStatusText(testStatus) }}</el-tag>\r\n          </div>\r\n\r\n          <div class=\"status-item\">\r\n            <span class=\"status-label\">运行时长:</span>\r\n            <span>{{ formatDuration(testDuration) }}</span>\r\n          </div>\r\n\r\n          <div class=\"status-item\">\r\n            <span class=\"status-label\">开始时间:</span>\r\n            <span>{{ formatTime(testStartTime) }}</span>\r\n          </div>\r\n\r\n          <div class=\"status-item\">\r\n            <span class=\"status-label\">预计结束:</span>\r\n            <span>{{ formatTime(estimatedEndTime) }}</span>\r\n          </div>\r\n\r\n          <el-divider></el-divider>\r\n\r\n          <div class=\"progress-section\">\r\n            <div class=\"progress-label\">\r\n              测试进度\r\n              <span class=\"progress-percent\">{{ testProgress }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"testProgress\" \r\n              :status=\"testProgress === 100 ? 'success' : 'primary'\"\r\n              :stroke-width=\"8\">\r\n            </el-progress>\r\n          </div>\r\n\r\n          <el-divider></el-divider>\r\n\r\n          <!-- 快捷操作 -->\r\n          <div class=\"quick-actions\">\r\n            <el-button \r\n              size=\"small\" \r\n              type=\"primary\" \r\n              @click=\"viewReport\"\r\n              :disabled=\"!currentReportId\">\r\n              查看报告\r\n            </el-button>\r\n            <el-button \r\n              size=\"small\" \r\n              type=\"info\" \r\n              @click=\"exportResults\"\r\n              :disabled=\"!currentReportId\">\r\n              导出结果\r\n            </el-button>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 系统资源监控 -->\r\n        <el-card style=\"margin-top: 20px;\" shadow=\"hover\">\r\n          <template #header>\r\n            <h4>系统资源</h4>\r\n          </template>\r\n\r\n          <div class=\"resource-item\">\r\n            <div class=\"resource-label\">CPU使用率</div>\r\n            <el-progress \r\n              :percentage=\"systemResources.cpu\" \r\n              :status=\"getCpuStatus(systemResources.cpu)\"\r\n              :show-text=\"false\"\r\n              :stroke-width=\"6\">\r\n            </el-progress>\r\n            <span class=\"resource-value\">{{ systemResources.cpu }}%</span>\r\n          </div>\r\n\r\n          <div class=\"resource-item\">\r\n            <div class=\"resource-label\">内存使用率</div>\r\n            <el-progress \r\n              :percentage=\"systemResources.memory\" \r\n              :status=\"getMemoryStatus(systemResources.memory)\"\r\n              :show-text=\"false\"\r\n              :stroke-width=\"6\">\r\n            </el-progress>\r\n            <span class=\"resource-value\">{{ systemResources.memory }}%</span>\r\n          </div>\r\n\r\n          <div class=\"resource-item\">\r\n            <div class=\"resource-label\">网络IO</div>\r\n            <div class=\"network-io\">\r\n              <span>↑ {{ formatBytes(systemResources.network_sent) }}</span>\r\n              <span>↓ {{ formatBytes(systemResources.network_recv) }}</span>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 配置对话框 -->\r\n    <el-dialog v-model=\"showConfigDialog\" title=\"高级配置\" width=\"60%\">\r\n      <el-tabs v-model=\"configTab\">\r\n        <el-tab-pane label=\"基础配置\" name=\"basic\">\r\n          <el-form :model=\"advancedConfig\" label-width=\"120px\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"思考时间(秒)\">\r\n                  <el-input-number v-model=\"advancedConfig.think_time\" :min=\"0\" :max=\"60\"></el-input-number>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"超时时间(秒)\">\r\n                  <el-input-number v-model=\"advancedConfig.timeout\" :min=\"1\" :max=\"300\"></el-input-number>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"重试次数\">\r\n                  <el-input-number v-model=\"advancedConfig.retry_count\" :min=\"0\" :max=\"10\"></el-input-number>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"日志级别\">\r\n                  <el-select v-model=\"advancedConfig.log_level\" style=\"width: 100%\">\r\n                    <el-option label=\"关闭\" value=\"off\"></el-option>\r\n                    <el-option label=\"错误\" value=\"error\"></el-option>\r\n                    <el-option label=\"警告\" value=\"warning\"></el-option>\r\n                    <el-option label=\"信息\" value=\"info\"></el-option>\r\n                    <el-option label=\"调试\" value=\"debug\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"分布式配置\" name=\"distributed\">\r\n          <el-form :model=\"advancedConfig\" label-width=\"120px\">\r\n            <el-form-item label=\"主节点\">\r\n              <el-select v-model=\"advancedConfig.master_server\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"server in servers\"\r\n                  :key=\"server.id\"\r\n                  :label=\"`${server.name} (${server.host_ip})`\"\r\n                  :value=\"server.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"工作节点\">\r\n              <el-select v-model=\"advancedConfig.worker_servers\" multiple style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"server in servers\"\r\n                  :key=\"server.id\"\r\n                  :label=\"`${server.name} (${server.host_ip})`\"\r\n                  :value=\"server.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"告警设置\" name=\"alerts\">\r\n          <el-form :model=\"advancedConfig\" label-width=\"120px\">\r\n            <el-form-item label=\"响应时间告警\">\r\n              <el-input-number v-model=\"advancedConfig.response_time_threshold\" :min=\"0\">\r\n                <template #append>ms</template>\r\n              </el-input-number>\r\n            </el-form-item>\r\n            <el-form-item label=\"错误率告警\">\r\n              <el-input-number v-model=\"advancedConfig.error_rate_threshold\" :min=\"0\" :max=\"100\">\r\n                <template #append>%</template>\r\n              </el-input-number>\r\n            </el-form-item>\r\n            <el-form-item label=\"TPS告警\">\r\n              <el-input-number v-model=\"advancedConfig.tps_threshold\" :min=\"0\">\r\n                <template #append>TPS</template>\r\n              </el-input-number>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n      \r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"showConfigDialog = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveAdvancedConfig\">保存配置</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { VideoPlay, VideoPause, Setting, TrendCharts, Timer, User, WarningFilled } from '@element-plus/icons-vue'\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'PerformanceExecutionOptimized',\r\n  components: {\r\n    VideoPlay,\r\n    VideoPause, \r\n    Setting,\r\n    TrendCharts,\r\n    Timer,\r\n    User,\r\n    WarningFilled\r\n  },\r\n  data() {\r\n    return {\r\n      // 测试配置\r\n      selectedTask: null,\r\n      testConfig: {\r\n        env_id: null,\r\n        mode: 'single',\r\n        users: 10,\r\n        spawn_rate: 2,\r\n        duration: 5,\r\n        report_name: ''\r\n      },\r\n      \r\n      // 高级配置\r\n      advancedConfig: {\r\n        think_time: 1,\r\n        timeout: 30,\r\n        retry_count: 0,\r\n        log_level: 'info',\r\n        master_server: null,\r\n        worker_servers: [],\r\n        response_time_threshold: 1000,\r\n        error_rate_threshold: 5,\r\n        tps_threshold: 100\r\n      },\r\n      \r\n      // 状态管理\r\n      isRunning: false,\r\n      isStarting: false,\r\n      testStatus: 'idle',\r\n      testStartTime: null,\r\n      testDuration: 0,\r\n      testProgress: 0,\r\n      currentReportId: null,\r\n      \r\n      // 实时数据\r\n      currentMetrics: {\r\n        tps: 0,\r\n        avg_response_time: 0,\r\n        users: 0,\r\n        error_rate: 0\r\n      },\r\n      systemResources: {\r\n        cpu: 0,\r\n        memory: 0,\r\n        network_sent: 0,\r\n        network_recv: 0\r\n      },\r\n      \r\n      // 图表\r\n      chart: null,\r\n      chartType: 'tps',\r\n      chartData: {\r\n        times: [],\r\n        tps: [],\r\n        response_time: [],\r\n        users: [],\r\n        error_rate: []\r\n      },\r\n      \r\n      // 数据源\r\n      tasks: [],\r\n      environments: [],\r\n      servers: [],\r\n      \r\n      // UI状态\r\n      showConfigDialog: false,\r\n      configTab: 'basic',\r\n      \r\n      // WebSocket\r\n      wsConnection: null,\r\n      updateTimer: null,\r\n      \r\n      // GUI相关\r\n      guiUrl: '',\r\n      webPort: 8089\r\n    }\r\n  },\r\n  computed: {\r\n    estimatedEndTime() {\r\n      if (!this.testStartTime || !this.testConfig.duration) return null\r\n      const startTime = new Date(this.testStartTime)\r\n      return new Date(startTime.getTime() + this.testConfig.duration * 60 * 1000)\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadInitialData()\r\n    this.initChart()\r\n    this.startUpdateTimer()\r\n  },\r\n  beforeUnmount() {\r\n    this.cleanup()\r\n  },\r\n  methods: {\r\n    async loadInitialData() {\r\n      try {\r\n        // 加载任务列表\r\n        const tasksResponse = await this.$api.getPerformanceTasksForExecution()\r\n        this.tasks = tasksResponse.data.results || []\r\n        \r\n        // 加载环境列表\r\n        const envResponse = await this.$api.getTestEnvironments()\r\n        this.environments = envResponse.data.results || []\r\n        \r\n        // 加载服务器列表\r\n        const serversResponse = await this.$api.getServersForExecution()\r\n        this.servers = serversResponse.data.results || []\r\n        \r\n      } catch (error) {\r\n        console.error('加载初始数据失败:', error)\r\n        this.$message.error('加载数据失败')\r\n      }\r\n    },\r\n    \r\n    async toggleTest() {\r\n      if (this.isRunning) {\r\n        await this.stopTest()\r\n      } else {\r\n        await this.startTest()\r\n      }\r\n    },\r\n    \r\n    async startTest() {\r\n      if (!this.selectedTask) {\r\n        this.$message.warning('请选择要执行的任务')\r\n        return\r\n      }\r\n      \r\n      this.isStarting = true\r\n      \r\n      try {\r\n        const response = await this.$api.runPerformanceTestOptimized(this.selectedTask, {\r\n          ...this.testConfig,\r\n          ...this.advancedConfig\r\n        })\r\n        \r\n        this.isRunning = true\r\n        this.testStatus = 'running'\r\n        this.testStartTime = new Date()\r\n        this.currentReportId = response.data.report_id\r\n        \r\n        // 保存GUI信息\r\n        if (response.data.gui_url) {\r\n          this.guiUrl = response.data.gui_url\r\n          this.webPort = response.data.web_port\r\n          this.$message.success(`测试启动成功！GUI地址: ${response.data.gui_url}`)\r\n        }\r\n        \r\n        // 连接WebSocket\r\n        this.connectWebSocket()\r\n        \r\n        this.$message.success('性能测试已开始')\r\n        \r\n      } catch (error) {\r\n        console.error('启动测试失败:', error)\r\n        this.$message.error('启动测试失败: ' + (error.response?.data?.message || error.message))\r\n      } finally {\r\n        this.isStarting = false\r\n      }\r\n    },\r\n    \r\n    async stopTest() {\r\n      try {\r\n        if (this.selectedTask) {\r\n          await this.$api.stopPerformanceTestOptimized(this.selectedTask)\r\n        }\r\n        \r\n        this.isRunning = false\r\n        this.testStatus = 'stopped'\r\n        this.disconnectWebSocket()\r\n        \r\n        this.$message.success('性能测试已停止')\r\n        \r\n      } catch (error) {\r\n        console.error('停止测试失败:', error)\r\n        this.$message.error('停止测试失败')\r\n      }\r\n    },\r\n    \r\n    connectWebSocket() {\r\n      if (this.wsConnection) {\r\n        this.wsConnection.close()\r\n      }\r\n      \r\n      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'\r\n      const wsUrl = `${protocol}//${window.location.host}/ws/performance/monitor/${this.selectedTask}/`\r\n      \r\n      this.wsConnection = new WebSocket(wsUrl)\r\n      \r\n      this.wsConnection.onopen = () => {\r\n        console.log('WebSocket连接已建立')\r\n        this.wsConnection.send(JSON.stringify({ type: 'start_monitoring' }))\r\n      }\r\n      \r\n      this.wsConnection.onmessage = (event) => {\r\n        const data = JSON.parse(event.data)\r\n        this.handleWebSocketMessage(data)\r\n      }\r\n      \r\n      this.wsConnection.onerror = (error) => {\r\n        console.error('WebSocket连接错误:', error)\r\n      }\r\n      \r\n      this.wsConnection.onclose = () => {\r\n        console.log('WebSocket连接已关闭')\r\n        if (this.isRunning) {\r\n          // 如果测试还在运行，尝试重连\r\n          setTimeout(() => {\r\n            this.connectWebSocket()\r\n          }, 5000)\r\n        }\r\n      }\r\n    },\r\n    \r\n    disconnectWebSocket() {\r\n      if (this.wsConnection) {\r\n        this.wsConnection.close()\r\n        this.wsConnection = null\r\n      }\r\n    },\r\n    \r\n    handleWebSocketMessage(data) {\r\n      switch (data.type) {\r\n        case 'performance_update':\r\n          this.updateMetrics(data.data)\r\n          break\r\n        case 'test_completed':\r\n          this.handleTestCompleted(data.data)\r\n          break\r\n        case 'test_failed':\r\n          this.handleTestFailed(data.error)\r\n          break\r\n        case 'current_status':\r\n          this.updateCurrentStatus(data.data)\r\n          break\r\n      }\r\n    },\r\n    \r\n    updateMetrics(data) {\r\n      this.currentMetrics = {\r\n        tps: data.avg_tps || 0,\r\n        avg_response_time: data.avg_response_time || 0,\r\n        users: data.current_users || 0,\r\n        error_rate: data.error_rate || 0\r\n      }\r\n      \r\n      // 更新图表数据\r\n      const now = new Date().toLocaleTimeString()\r\n      this.chartData.times.push(now)\r\n      this.chartData.tps.push(data.avg_tps || 0)\r\n      this.chartData.response_time.push(data.avg_response_time || 0)\r\n      this.chartData.users.push(data.current_users || 0)\r\n      this.chartData.error_rate.push(data.error_rate || 0)\r\n      \r\n      // 保持最新50个数据点\r\n      Object.keys(this.chartData).forEach(key => {\r\n        if (this.chartData[key].length > 50) {\r\n          this.chartData[key].shift()\r\n        }\r\n      })\r\n      \r\n      this.updateChart()\r\n    },\r\n    \r\n    updateCurrentStatus(data) {\r\n      if (data) {\r\n        this.currentMetrics = {\r\n          tps: data.avg_tps || 0,\r\n          avg_response_time: data.avg_response_time || 0,\r\n          users: data.current_users || 0,\r\n          error_rate: data.error_rate || 0\r\n        }\r\n      }\r\n    },\r\n    \r\n    handleTestCompleted(data) {\r\n      this.isRunning = false\r\n      this.testStatus = 'completed'\r\n      this.testProgress = 100\r\n      this.$message.success('性能测试已完成')\r\n      this.disconnectWebSocket()\r\n    },\r\n    \r\n    handleTestFailed(error) {\r\n      this.isRunning = false\r\n      this.testStatus = 'failed'\r\n      this.$message.error('性能测试失败: ' + error)\r\n      this.disconnectWebSocket()\r\n    },\r\n    \r\n    initChart() {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.chartContainer) {\r\n          this.chart = echarts.init(this.$refs.chartContainer)\r\n          this.updateChart()\r\n        }\r\n      })\r\n    },\r\n    \r\n    updateChart() {\r\n      if (!this.chart) return\r\n      \r\n      const option = {\r\n        title: {\r\n          text: this.getChartTitle(),\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.chartData.times,\r\n          axisLabel: {\r\n            interval: 'auto',\r\n            rotate: 45\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: this.getChartUnit()\r\n        },\r\n        series: [{\r\n          data: this.chartData[this.chartType],\r\n          type: 'line',\r\n          smooth: true,\r\n          areaStyle: {\r\n            opacity: 0.3\r\n          },\r\n          itemStyle: {\r\n            color: this.getChartColor()\r\n          }\r\n        }],\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '15%',\r\n          containLabel: true\r\n        }\r\n      }\r\n      \r\n      this.chart.setOption(option)\r\n    },\r\n    \r\n    getChartTitle() {\r\n      const titles = {\r\n        tps: 'TPS趋势',\r\n        response_time: '响应时间趋势', \r\n        users: '用户数趋势',\r\n        error_rate: '错误率趋势'\r\n      }\r\n      return titles[this.chartType] || ''\r\n    },\r\n    \r\n    getChartUnit() {\r\n      const units = {\r\n        tps: 'TPS',\r\n        response_time: '毫秒(ms)',\r\n        users: '用户数',\r\n        error_rate: '百分比(%)'\r\n      }\r\n      return units[this.chartType] || ''\r\n    },\r\n    \r\n    getChartColor() {\r\n      const colors = {\r\n        tps: '#409eff',\r\n        response_time: '#67c23a',\r\n        users: '#e6a23c',\r\n        error_rate: '#f56c6c'\r\n      }\r\n      return colors[this.chartType] || '#409eff'\r\n    },\r\n    \r\n    startUpdateTimer() {\r\n      this.updateTimer = setInterval(() => {\r\n        if (this.isRunning && this.testStartTime) {\r\n          this.testDuration = Math.floor((Date.now() - new Date(this.testStartTime).getTime()) / 1000)\r\n          \r\n          // 计算进度\r\n          if (this.testConfig.duration) {\r\n            const totalSeconds = this.testConfig.duration * 60\r\n            this.testProgress = Math.min(100, (this.testDuration / totalSeconds) * 100)\r\n          }\r\n          \r\n          // 如果超过预定时间，自动停止\r\n          if (this.testProgress >= 100 && this.isRunning) {\r\n            this.stopTest()\r\n          }\r\n        }\r\n        \r\n        // 更新系统资源（模拟数据）\r\n        this.updateSystemResources()\r\n      }, 1000)\r\n    },\r\n    \r\n    updateSystemResources() {\r\n      // 模拟系统资源数据更新\r\n      this.systemResources = {\r\n        cpu: Math.max(0, Math.min(100, this.systemResources.cpu + (Math.random() - 0.5) * 10)),\r\n        memory: Math.max(0, Math.min(100, this.systemResources.memory + (Math.random() - 0.5) * 5)),\r\n        network_sent: this.systemResources.network_sent + Math.random() * 1000000,\r\n        network_recv: this.systemResources.network_recv + Math.random() * 1000000\r\n      }\r\n    },\r\n    \r\n    cleanup() {\r\n      this.disconnectWebSocket()\r\n      if (this.updateTimer) {\r\n        clearInterval(this.updateTimer)\r\n      }\r\n      if (this.chart) {\r\n        this.chart.dispose()\r\n      }\r\n    },\r\n    \r\n    saveAdvancedConfig() {\r\n      this.showConfigDialog = false\r\n      this.$message.success('配置已保存')\r\n    },\r\n    \r\n    viewReport() {\r\n      if (this.currentReportId) {\r\n        this.$router.push({ \r\n          name: 'PerformanceResult-Detail', \r\n          params: { id: this.currentReportId }\r\n        })\r\n      }\r\n    },\r\n    \r\n    exportResults() {\r\n      if (this.currentReportId) {\r\n        this.$api.exportTaskReport(this.currentReportId)\r\n      }\r\n    },\r\n    \r\n    // 工具方法\r\n    getStatusType(status) {\r\n      const types = {\r\n        idle: 'info',\r\n        running: 'primary', \r\n        completed: 'success',\r\n        failed: 'danger',\r\n        stopped: 'warning'\r\n      }\r\n      return types[status] || 'info'\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        idle: '待运行',\r\n        running: '运行中',\r\n        completed: '已完成',\r\n        failed: '失败',\r\n        stopped: '已停止'\r\n      }\r\n      return texts[status] || '未知'\r\n    },\r\n    \r\n    getErrorRateClass(rate) {\r\n      if (rate > 5) return 'error-high'\r\n      if (rate > 1) return 'error-medium'\r\n      return 'error-low'\r\n    },\r\n    \r\n    getCpuStatus(percent) {\r\n      if (percent > 80) return 'exception'\r\n      if (percent > 60) return 'warning'\r\n      return 'success'\r\n    },\r\n    \r\n    getMemoryStatus(percent) {\r\n      if (percent > 85) return 'exception'\r\n      if (percent > 70) return 'warning'\r\n      return 'success'\r\n    },\r\n    \r\n    formatDuration(seconds) {\r\n      if (!seconds) return '0s'\r\n      const hours = Math.floor(seconds / 3600)\r\n      const minutes = Math.floor((seconds % 3600) / 60)\r\n      const secs = seconds % 60\r\n      return `${hours}h ${minutes}m ${secs}s`\r\n    },\r\n    \r\n    formatTime(time) {\r\n      if (!time) return '--'\r\n      return new Date(time).toLocaleString()\r\n    },\r\n    \r\n    formatBytes(bytes) {\r\n      if (!bytes) return '0B'\r\n      const k = 1024\r\n      const sizes = ['B', 'KB', 'MB', 'GB']\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    chartType() {\r\n      this.updateChart()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.execution-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: calc(100vh - 60px);\r\n}\r\n\r\n.config-panel {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.panel-header h3 {\r\n  margin: 0;\r\n  color: #409eff;\r\n}\r\n\r\n.metrics-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.metric-card {\r\n  border: none;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.metric-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n}\r\n\r\n.metric-icon {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 15px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.tps-icon {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.response-icon {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.users-icon {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.error-icon {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.error-icon.error-medium {\r\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\r\n}\r\n\r\n.error-icon.error-high {\r\n  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);\r\n}\r\n\r\n.metric-info {\r\n  flex: 1;\r\n}\r\n\r\n.metric-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #409eff;\r\n  line-height: 1;\r\n}\r\n\r\n.metric-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n\r\n.status-panel {\r\n  height: fit-content;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.status-label {\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.progress-section {\r\n  margin: 20px 0;\r\n}\r\n\r\n.progress-label {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.progress-percent {\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n\r\n.quick-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.quick-actions .el-button {\r\n  flex: 1;\r\n}\r\n\r\n.resource-item {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.resource-label {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.resource-value {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-left: 10px;\r\n}\r\n\r\n.network-io {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n\r\n.chart-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.chart-header h4 {\r\n  margin: 0;\r\n  color: #409eff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .metrics-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .metrics-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .panel-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n}\r\n</style>", "import { render } from \"./PerformanceExecutionOptimized.vue?vue&type=template&id=1cb4d7ec&scoped=true\"\nimport script from \"./PerformanceExecutionOptimized.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceExecutionOptimized.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceExecutionOptimized.vue?vue&type=style&index=0&id=1cb4d7ec&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-1cb4d7ec\"]])\n\nexport default __exports__"], "names": ["class", "ref", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "shadow", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button_group", "_component_el_button", "type", "$data", "isRunning", "loading", "isStarting", "onClick", "$options", "toggleTest", "disabled", "selectedTask", "_component_el_icon", "_createBlock", "_component_VideoPause", "key", "_component_VideoPlay", "_toDisplayString", "_cache", "$event", "showConfigDialog", "_component_Setting", "_component_el_form", "model", "testConfig", "size", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_form_item", "label", "_component_el_select", "placeholder", "_Fragment", "_renderList", "tasks", "task", "_component_el_option", "id", "taskName", "value", "env_id", "environments", "env", "name", "mode", "_component_el_input_number", "users", "min", "max", "spawn_rate", "duration", "_component_el_input", "report_name", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_component_Trend<PERSON><PERSON>s", "_hoisted_6", "_hoisted_7", "currentMetrics", "tps", "_hoisted_8", "_hoisted_9", "_component_Timer", "_hoisted_10", "_hoisted_11", "avg_response_time", "_hoisted_12", "_hoisted_13", "_component_User", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_normalizeClass", "getErrorRateClass", "error_rate", "_component_WarningFilled", "_hoisted_17", "_hoisted_18", "toFixed", "_hoisted_19", "_component_el_radio_group", "chartType", "_component_el_radio_button", "_hoisted_20", "_hoisted_21", "_component_el_tag", "getStatusType", "testStatus", "getStatusText", "_hoisted_22", "formatDuration", "testDuration", "_hoisted_23", "formatTime", "testStartTime", "_hoisted_24", "estimatedEndTime", "_component_el_divider", "_hoisted_25", "_hoisted_26", "_hoisted_27", "testProgress", "_component_el_progress", "percentage", "status", "_hoisted_28", "viewReport", "currentReportId", "exportResults", "_hoisted_29", "systemResources", "cpu", "getCpuStatus", "_hoisted_30", "_hoisted_31", "memory", "getMemoryStatus", "_hoisted_32", "_hoisted_33", "_hoisted_34", "formatBytes", "network_sent", "network_recv", "_component_el_dialog", "title", "width", "footer", "_hoisted_35", "saveAdvancedConfig", "_component_el_tabs", "configTab", "_component_el_tab_pane", "advancedConfig", "think_time", "timeout", "retry_count", "log_level", "master_server", "servers", "server", "host_ip", "worker_servers", "multiple", "response_time_threshold", "append", "error_rate_threshold", "tps_threshold", "components", "VideoPlay", "VideoPause", "Setting", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Timer", "User", "WarningFilled", "data", "chart", "chartData", "times", "response_time", "wsConnection", "updateTimer", "guiUrl", "webPort", "computed", "this", "startTime", "Date", "getTime", "mounted", "loadInitialData", "initChart", "startUpdateTimer", "beforeUnmount", "cleanup", "methods", "tasksResponse", "$api", "getPerformanceTasksForExecution", "results", "envResponse", "getTestEnvironments", "serversResponse", "getServersForExecution", "error", "console", "$message", "stopTest", "startTest", "response", "runPerformanceTestOptimized", "report_id", "gui_url", "web_port", "success", "connectWebSocket", "message", "warning", "stopPerformanceTestOptimized", "disconnectWebSocket", "close", "protocol", "window", "location", "wsUrl", "host", "WebSocket", "onopen", "log", "send", "JSON", "stringify", "onmessage", "event", "parse", "handleWebSocketMessage", "onerror", "onclose", "setTimeout", "updateMetrics", "handleTestCompleted", "handleTestFailed", "updateCurrentStatus", "avg_tps", "current_users", "now", "toLocaleTimeString", "push", "Object", "keys", "for<PERSON>ach", "length", "shift", "updateChart", "$nextTick", "$refs", "chartContainer", "echarts", "option", "text", "getChartTitle", "left", "textStyle", "fontSize", "tooltip", "trigger", "xAxis", "axisLabel", "interval", "rotate", "yAxis", "getChartUnit", "series", "smooth", "areaStyle", "opacity", "itemStyle", "color", "getChartColor", "grid", "right", "bottom", "containLabel", "setOption", "titles", "units", "colors", "setInterval", "Math", "floor", "totalSeconds", "updateSystemResources", "random", "clearInterval", "dispose", "$router", "params", "exportTaskReport", "types", "idle", "running", "completed", "failed", "stopped", "texts", "rate", "percent", "seconds", "hours", "minutes", "secs", "time", "toLocaleString", "bytes", "k", "sizes", "i", "parseFloat", "pow", "watch", "__exports__", "render"], "sourceRoot": ""}