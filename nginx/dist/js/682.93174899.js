"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[682],{57890:function(e,t,l){l.d(t,{A:function(){return p}});var o=l(56768),r=l(45130),a=l(24232);const i={key:0,class:"time-preview"};function n(e,t,l,n,s,d){const u=(0,o.g2)("el-radio-button"),c=(0,o.g2)("el-radio-group"),p=(0,o.g2)("el-col"),m=(0,o.g2)("el-time-picker"),g=(0,o.g2)("el-row"),h=(0,o.g2)("el-radio"),k=(0,o.g2)("el-button");return(0,o.uX)(),(0,o.CE)("div",{style:{display:"inline-block"},class:"cron-wrapper",onClick:t[18]||(t[18]=(0,r.D$)(()=>{},["stop"]))},[(0,o.Lk)("div",{class:"form",onClick:t[17]||(t[17]=(0,r.D$)(()=>{},["stop"]))},[(0,o.bF)(g,null,{default:(0,o.k6)(()=>[(0,o.bF)(p,{span:60},{default:(0,o.k6)(()=>[(0,o.bF)(c,{modelValue:s.type,"onUpdate:modelValue":t[0]||(t[0]=e=>s.type=e),size:"large",style:{"margin-bottom":"20px",width:"500px"},onClick:t[1]||(t[1]=(0,r.D$)(()=>{},["stop"]))},{default:(0,o.k6)(()=>[(0,o.bF)(u,{label:"每天"}),(0,o.bF)(u,{label:"每周"}),(0,o.bF)(u,{label:"每月"})]),_:1},8,["modelValue"])]),_:1}),(0,o.bF)(p,{span:5,style:{"margin-left":"20px"}},{default:(0,o.k6)(()=>[(0,o.Lk)("div",{onClick:t[7]||(t[7]=(0,r.D$)(()=>{},["stop"]))},[(0,o.bF)(m,{modelValue:s.time,"onUpdate:modelValue":t[2]||(t[2]=e=>s.time=e),placeholder:"选择时间",size:"large",style:{width:"140px"},"value-format":"H:m","popper-options":{strategy:"fixed",modifiers:[{name:"eventListeners",options:{scroll:!1,resize:!1}}]},"popper-class":"time-picker-popper",teleported:!1,onClick:t[3]||(t[3]=(0,r.D$)(()=>{},["stop"])),onFocus:t[4]||(t[4]=(0,r.D$)(()=>{},["stop"])),onBlur:t[5]||(t[5]=(0,r.D$)(()=>{},["stop"])),onChange:t[6]||(t[6]=(0,r.D$)(()=>{},["stop"]))},null,8,["modelValue"])])]),_:1})]),_:1}),(0,o.bF)(g,null,{default:(0,o.k6)(()=>[s.weekRadio?((0,o.uX)(),(0,o.CE)("div",{key:0,class:"radio-container",onClick:t[11]||(t[11]=(0,r.D$)(()=>{},["stop"]))},[(0,o.bF)(c,{modelValue:s.week,"onUpdate:modelValue":t[9]||(t[9]=e=>s.week=e),onClick:t[10]||(t[10]=(0,r.D$)(()=>{},["stop"]))},{default:(0,o.k6)(()=>[((0,o.uX)(!0),(0,o.CE)(o.FK,null,(0,o.pI)(s.weekOption,e=>((0,o.uX)(),(0,o.Wv)(h,{key:e.cron,label:e.cron,onClick:t[8]||(t[8]=(0,r.D$)(()=>{},["stop"]))},{default:(0,o.k6)(()=>[(0,o.eW)((0,a.v_)(e.value),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])])):(0,o.Q3)("",!0),s.monthRadio?((0,o.uX)(),(0,o.CE)("div",{key:1,class:"radio-container",onClick:t[15]||(t[15]=(0,r.D$)(()=>{},["stop"]))},[(0,o.bF)(c,{modelValue:s.month,"onUpdate:modelValue":t[13]||(t[13]=e=>s.month=e),onClick:t[14]||(t[14]=(0,r.D$)(()=>{},["stop"]))},{default:(0,o.k6)(()=>[((0,o.uX)(!0),(0,o.CE)(o.FK,null,(0,o.pI)(s.monthOption,e=>((0,o.uX)(),(0,o.Wv)(h,{key:e.cron,label:e.cron,onClick:t[12]||(t[12]=(0,r.D$)(()=>{},["stop"]))},{default:(0,o.k6)(()=>[(0,o.eW)((0,a.v_)(e.value),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])])):(0,o.Q3)("",!0)]),_:1}),(0,o.Lk)("div",{class:"footer",onClick:t[16]||(t[16]=(0,r.D$)(()=>{},["stop"]))},[s.time?((0,o.uX)(),(0,o.CE)("p",i,[t[19]||(t[19]=(0,o.eW)(" 当前设置: ")),(0,o.Lk)("span",null,(0,a.v_)(d.timePreview),1)])):(0,o.Q3)("",!0),(0,o.bF)(k,{size:"default",onClick:(0,r.D$)(d.closeCron,["stop"])},{default:(0,o.k6)(()=>t[20]||(t[20]=[(0,o.eW)("取消")])),_:1,__:[20]},8,["onClick"]),(0,o.bF)(k,{size:"default",type:"primary",onClick:(0,r.D$)(d.handleSummit,["stop"]),disabled:!s.time},{default:(0,o.k6)(()=>t[21]||(t[21]=[(0,o.eW)("确定")])),_:1,__:[21]},8,["onClick","disabled"])])])])}l(44114),l(18111),l(20116);var s=l(90144),d={name:"timerTaskCron",props:{runTimeStr:(0,s.KR)(),timeCronStr:{type:String,default:""}},data(){return{visible:!1,weekRadio:!1,monthRadio:!1,value:"",type:"每天",week:1,month:1,time:"",weekOption:[{title:"星期一",value:"星期一",cron:1},{title:"星期二",value:"星期二",cron:2},{title:"星期三",value:"星期三",cron:3},{title:"星期四",value:"星期四",cron:4},{title:"星期五",value:"星期五",cron:5},{title:"星期六",value:"星期六",cron:6},{title:"星期日",value:"星期日",cron:7}],monthOption:[]}},computed:{timePreview(){if(!this.time)return"";let e=this.time;if("每天"===this.type)e=`每天 ${this.time}`;else if("每周"===this.type){const t=this.weekOption.find(e=>e.cron===this.week)?.value||"";e=`每周${t} ${this.time}`}else if("每月"===this.type){const t=this.month<10?`${this.month}  号`:`${this.month} 号`;e=`每月${t} ${this.time}`}return e}},watch:{type(e,t){"每天"===this.type&&(this.weekRadio=!1,this.monthRadio=!1),"每周"===this.type&&(this.weekRadio=!0,this.monthRadio=!1),"每月"===this.type&&(this.weekRadio=!1,this.monthRadio=!0)},week(e,t){},month(e,t){}},created(){this.initData(),this.runTimeStr&&this.parseRunTimeStr(this.runTimeStr)},mounted(){document.addEventListener("click",this.handleGlobalClick)},unmounted(){document.removeEventListener("click",this.handleGlobalClick)},methods:{handleGlobalClick(e){(e.target.closest(".el-time-panel")||e.target.closest(".el-picker-panel")||e.target.closest(".time-picker-popper"))&&e.stopPropagation()},initData(){let e=[];var t="";for(let l=1;l<32;l++)t=l<10?"  号":"号",e.push({title:l+t,value:l+t,cron:l});this.monthOption=e},parseRunTimeStr(e){if(e)try{const t=e.split(" ");t.length>=5&&("*"!==t[0]&&"*"!==t[1]&&(this.time=`${t[1]}:${t[0]}`),"*"!==t[2]&&"*"===t[3]&&"*"===t[4]?(this.type="每月",this.month=parseInt(t[2]),this.monthRadio=!0):"*"===t[2]&&"*"!==t[3]&&"*"===t[4]?(this.type="每周",this.week=parseInt(t[3]),this.weekRadio=!0):this.type="每天")}catch(t){console.error("解析cron表达式失败",t)}},closeCron(){this.$emit("closeTime",!0),this.type="每天",this.week=1,this.month=1,this.time=""},handleSummit(){if(!this.time)return void(this.$message?this.$message({message:"请选择时间!",type:"warning"}):window.ElMessage?window.ElMessage.warning("请选择时间!"):alert("请选择时间!"));let e,t=this.time.split(":").reverse();"每天"===this.type&&(e=t.join(" ")+" * * *"),"每月"===this.type&&(e=t.join(" ")+" "+this.month+" * *"),"每周"===this.type&&(e=t.join(" ")+" * "+this.week+" *"),this.$emit("runTime",e),this.$emit("closeTime",!0),this.type="每天",this.week=1,this.month=1,this.time=""}}},u=l(71241);const c=(0,u.A)(d,[["render",n],["__scopeId","data-v-021b989a"]]);var p=c},70682:function(e,t,l){l.r(t),l.d(t,{default:function(){return T}});var o=l(56768),r=l(24232);const a={style:{"margin-top":"20px","margin-left":"15px"}},i={style:{margin:"15px"}},n={class:"pagination-container"},s={class:"system-icon-content"},d={class:"form-container"},u={class:"form-column"},c={key:0,style:{"margin-top":"20px","margin-left":"-10px"}},p={key:1},m={style:{color:"#606266",display:"flex","align-items":"center","justify-content":"space-between"}},g={class:"form-column"},h={slot:"footer",class:"dialog-footer",style:{"text-align":"center"}};function k(e,t,l,k,f,b){const y=(0,o.g2)("el-button"),F=(0,o.g2)("el-input"),_=(0,o.g2)("Close"),C=(0,o.g2)("el-icon"),v=(0,o.g2)("Check"),w=(0,o.g2)("Plus"),T=(0,o.g2)("el-table-column"),V=(0,o.g2)("el-tag"),S=(0,o.g2)("CopyDocument"),L=(0,o.g2)("EditPen"),x=(0,o.g2)("Delete"),D=(0,o.g2)("el-table"),M=(0,o.g2)("el-pagination"),$=(0,o.g2)("el-scrollbar"),W=(0,o.g2)("el-radio"),U=(0,o.g2)("el-radio-group"),P=(0,o.g2)("el-form-item"),R=(0,o.g2)("timerTaskCron"),X=(0,o.g2)("el-popover"),E=(0,o.g2)("el-option"),N=(0,o.g2)("el-select"),z=(0,o.g2)("el-input-number"),Q=(0,o.g2)("el-form"),j=(0,o.g2)("el-card"),A=(0,o.g2)("QuestionFilled"),I=(0,o.g2)("el-tooltip"),B=(0,o.g2)("el-dialog");return(0,o.uX)(),(0,o.CE)(o.FK,null,[(0,o.Lk)("div",a,[(0,o.bF)(F,{style:{width:"330px"},modelValue:f.filterText,"onUpdate:modelValue":t[0]||(t[0]=e=>f.filterText=e),placeholder:"请输入配置名称进行搜索",clearable:""},{append:(0,o.k6)(()=>[(0,o.bF)(y,{type:"primary",onClick:b.searchClick},{default:(0,o.k6)(()=>t[19]||(t[19]=[(0,o.eW)("查询")])),_:1,__:[19]},8,["onClick"])]),_:1},8,["modelValue"]),l.setButton?((0,o.uX)(),(0,o.Wv)(y,{key:0,type:"warning",style:{float:"right","margin-right":"15px"},onClick:b.handleClose},{default:(0,o.k6)(()=>[(0,o.bF)(C,null,{default:(0,o.k6)(()=>[(0,o.bF)(_)]),_:1}),t[20]||(t[20]=(0,o.eW)("关闭窗口 "))]),_:1,__:[20]},8,["onClick"])):(0,o.Q3)("",!0),l.setButton?((0,o.uX)(),(0,o.Wv)(y,{key:1,type:"primary",style:{float:"right","margin-right":"5px"},onClick:b.handelSettingDlg},{default:(0,o.k6)(()=>[(0,o.bF)(C,null,{default:(0,o.k6)(()=>[(0,o.bF)(v)]),_:1}),t[21]||(t[21]=(0,o.eW)("确认选择 "))]),_:1,__:[21]},8,["onClick"])):((0,o.uX)(),(0,o.Wv)(y,{key:2,type:"primary",style:{float:"right","margin-right":"15px"},onClick:t[1]||(t[1]=e=>b.popup("add"))},{default:(0,o.k6)(()=>[(0,o.bF)(C,null,{default:(0,o.k6)(()=>[(0,o.bF)(w)]),_:1}),t[22]||(t[22]=(0,o.eW)("新增预设 "))]),_:1,__:[22]}))]),(0,o.bF)($,{height:"calc(100vh - 150px)"},{default:(0,o.k6)(()=>[(0,o.Lk)("div",i,[(0,o.bF)(D,{ref:"table","highlight-current-row":"",data:f.presettingList,style:{width:"100%"},size:"small",border:"","empty-text":"暂无数据",onSelectionChange:b.selectSetChange},{default:(0,o.k6)(()=>[(0,o.bF)(T,{type:"selection"}),(0,o.bF)(T,{label:"序号",align:"center",width:"60"},{default:(0,o.k6)(e=>[(0,o.Lk)("span",null,(0,r.v_)(e.$index+1),1)]),_:1}),(0,o.bF)(T,{prop:"name",label:"配置名称",align:"center",width:"250"}),(0,o.bF)(T,{prop:"taskType",label:"任务类型",width:"120",align:"center"},{default:(0,o.k6)(e=>["10"===e.row.taskType?((0,o.uX)(),(0,o.Wv)(V,{key:0,effect:"dark"},{default:(0,o.k6)(()=>[(0,o.eW)((0,r.v_)(f.taskTypeMap[e.row.taskType]||e.row.taskType),1)]),_:2},1024)):((0,o.uX)(),(0,o.Wv)(V,{key:1,type:"success",effect:"dark"},{default:(0,o.k6)(()=>[(0,o.eW)((0,r.v_)(f.taskTypeMap[e.row.taskType]||e.row.taskType),1)]),_:2},1024))]),_:1}),(0,o.bF)(T,{prop:"logMode",label:"日志模式",align:"center",width:"200"},{default:(0,o.k6)(e=>[(0,o.Lk)("span",null,(0,r.v_)(f.logModeMap[e.row.logMode]||e.row.logMode),1)]),_:1}),(0,o.bF)(T,{prop:"control",label:"控制模式",align:"center",width:"150"},{default:(0,o.k6)(e=>[(0,o.Lk)("span",null,(0,r.v_)(f.controlMap[e.row.control]||e.row.control),1)]),_:1}),(0,o.bF)(T,{prop:"pressureMode",label:"压测模式",align:"center",width:"150"},{default:(0,o.k6)(e=>[(0,o.Lk)("span",null,(0,r.v_)(f.pressureModeMap[e.row.pressureMode]||e.row.pressureMode),1)]),_:1}),(0,o.bF)(T,{prop:"serverNames",label:"运行机器",align:"center",width:"300"},{default:(0,o.k6)(e=>[((0,o.uX)(!0),(0,o.CE)(o.FK,null,(0,o.pI)(e.row.serverNames,e=>((0,o.uX)(),(0,o.Wv)(V,{key:e,type:"info",style:{"margin-right":"5px"}},{default:(0,o.k6)(()=>[(0,o.eW)((0,r.v_)(e),1)]),_:2},1024))),128))]),_:1}),(0,o.bF)(T,{prop:"timeUnit",label:"时长单位",align:"center"},{default:(0,o.k6)(e=>[(0,o.Lk)("span",null,(0,r.v_)(f.timeUnitMap[e.row.timeUnit]||e.row.timeUnit),1)]),_:1}),(0,o.bF)(T,{prop:"creator",label:"创建人",align:"center",width:"150"}),(0,o.bF)(T,{label:"创建时间",align:"center",width:"170"},{default:(0,o.k6)(t=>[(0,o.eW)((0,r.v_)(e.$tools.rTime(t.row.create_time)),1)]),_:1}),!1===l.setButton?((0,o.uX)(),(0,o.Wv)(T,{key:0,label:"操作",align:"center",width:"260px",fixed:"right"},{default:(0,o.k6)(e=>[(0,o.bF)(y,{onClick:t=>b.copyPresetting(e.row),type:"warning",size:"small",plain:""},{default:(0,o.k6)(()=>[(0,o.bF)(C,null,{default:(0,o.k6)(()=>[(0,o.bF)(S)]),_:1}),t[23]||(t[23]=(0,o.eW)("复制 "))]),_:2,__:[23]},1032,["onClick"]),(0,o.bF)(y,{onClick:t=>b.popup("edit",e.row),type:"primary",size:"small",plain:""},{default:(0,o.k6)(()=>[(0,o.bF)(C,null,{default:(0,o.k6)(()=>[(0,o.bF)(L)]),_:1}),t[24]||(t[24]=(0,o.eW)("编辑 "))]),_:2,__:[24]},1032,["onClick"]),(0,o.bF)(y,{onClick:t=>b.delPresetting(e.row.id),size:"small"},{default:(0,o.k6)(()=>[(0,o.bF)(C,null,{default:(0,o.k6)(()=>[(0,o.bF)(x)]),_:1}),t[25]||(t[25]=(0,o.eW)("删除 "))]),_:2,__:[25]},1032,["onClick"])]),_:1})):(0,o.Q3)("",!0)]),_:1},8,["data","onSelectionChange"])]),(0,o.Lk)("div",n,[(0,o.bF)(M,{background:"",layout:"total, prev, pager, next, jumper",onCurrentChange:b.currentPages,"default-page-size":100,total:f.pages.count,"current-page":f.pages.current,"next-text":"下一页","prev-text":"上一页"},null,8,["onCurrentChange","total","current-page"])])]),_:1}),(0,o.bF)(B,{title:f.dialogTitle,modelValue:f.dialogVisible,"onUpdate:modelValue":t[18]||(t[18]=e=>f.dialogVisible=e),"before-close":b.closeDialog,top:"40px","destroy-on-close":"","custom-class":"class_dialog"},{default:(0,o.k6)(()=>[(0,o.Lk)("div",s,[(0,o.bF)(Q,{model:f.configForm,rules:f.rulesConfig,ref:"ConfigRef","label-width":"95px"},{default:(0,o.k6)(()=>[(0,o.Lk)("div",d,[(0,o.bF)($,{height:"calc(100vh - 250px)"},{default:(0,o.k6)(()=>[(0,o.Lk)("div",u,[(0,o.bF)(P,{label:"任务类型：",prop:"taskType"},{default:(0,o.k6)(()=>[(0,o.bF)(U,{modelValue:b.selectTaskType,"onUpdate:modelValue":t[2]||(t[2]=e=>b.selectTaskType=e)},{default:(0,o.k6)(()=>[(0,o.bF)(W,{label:"10"},{default:(0,o.k6)(()=>t[26]||(t[26]=[(0,o.eW)(" 普通任务 ")])),_:1,__:[26]}),(0,o.bF)(W,{label:"20"},{default:(0,o.k6)(()=>t[27]||(t[27]=[(0,o.eW)(" 定时任务 ")])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1}),(0,o.bF)(P,{prop:"name",label:"配置名称："},{default:(0,o.k6)(()=>[(0,o.bF)(F,{modelValue:f.configForm.name,"onUpdate:modelValue":t[3]||(t[3]=e=>f.configForm.name=e),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),"20"===f.configForm.taskType?((0,o.uX)(),(0,o.Wv)(P,{key:0,label:"时间配置：",prop:"rule"},{default:(0,o.k6)(()=>[(0,o.bF)(X,{visible:f.cronVisible,"onUpdate:visible":t[5]||(t[5]=e=>f.cronVisible=e),placement:"bottom-start",width:"650",teleported:!0,trigger:"manual"},{reference:(0,o.k6)(()=>[(0,o.bF)(F,{modelValue:f.configForm.rule,"onUpdate:modelValue":t[4]||(t[4]=e=>f.configForm.rule=e),clearable:"",readonly:"",placeholder:"请选择定时任务时间配置",onClick:b.cronFun},null,8,["modelValue","onClick"])]),default:(0,o.k6)(()=>[(0,o.bF)(R,{runTimeStr:f.configForm.rule,onCloseTime:b.closeRunTimeCron,onRunTime:b.runTimeCron},null,8,["runTimeStr","onCloseTime","onRunTime"])]),_:1},8,["visible"])]),_:1})):(0,o.Q3)("",!0),(0,o.bF)(P,{prop:"logMode",label:"日志模式："},{default:(0,o.k6)(()=>[(0,o.bF)(N,{modelValue:b.selectedLogMode,"onUpdate:modelValue":t[6]||(t[6]=e=>b.selectedLogMode=e),placeholder:"请选择日志模式",style:{width:"100%"}},{default:(0,o.k6)(()=>[(0,o.bF)(E,{label:"关闭",value:"0"}),(0,o.bF)(E,{label:"开启-全部日志",value:"10"}),(0,o.bF)(E,{label:"开启-仅成功日志",value:"20"}),(0,o.bF)(E,{label:"开启-仅失败日志",value:"30"})]),_:1},8,["modelValue"])]),_:1}),(0,o.bF)(P,{label:"控制模式：",prop:"control"},{default:(0,o.k6)(()=>[(0,o.bF)(N,{modelValue:b.selectControlMode,"onUpdate:modelValue":t[7]||(t[7]=e=>b.selectControlMode=e),placeholder:"请选择控制模式",style:{width:"100%"}},{default:(0,o.k6)(()=>[(0,o.bF)(E,{label:"集合模式",value:"10"}),(0,o.bF)(E,{label:"单独模式",value:"20"})]),_:1},8,["modelValue"])]),_:1}),(0,o.bF)(P,{label:"压测模式：",prop:"pressureMode"},{default:(0,o.k6)(()=>[(0,o.bF)(N,{modelValue:b.selectPressureMode,"onUpdate:modelValue":t[8]||(t[8]=e=>b.selectPressureMode=e),placeholder:"请选择压测模式",style:{width:"100%"}},{default:(0,o.k6)(()=>[(0,o.bF)(E,{label:"并发模式",value:"10"}),(0,o.bF)(E,{label:"阶梯模式",value:"20"})]),_:1},8,["modelValue"])]),_:1}),(0,o.bF)(P,{label:"时长单位：",prop:"pressureMode"},{default:(0,o.k6)(()=>[(0,o.bF)(N,{modelValue:f.configForm.timeUnit,"onUpdate:modelValue":t[9]||(t[9]=e=>f.configForm.timeUnit=e),placeholder:"请选择时长单位",style:{width:"100%"}},{default:(0,o.k6)(()=>[(0,o.bF)(E,{label:"s",value:"s"}),(0,o.bF)(E,{label:"m",value:"m"}),(0,o.bF)(E,{label:"h",value:"h"})]),_:1},8,["modelValue"])]),_:1}),(0,o.bF)(P,{label:"思考时间：",prop:"thinkTime"},{default:(0,o.k6)(()=>[(0,o.bF)(N,{modelValue:b.selectTimeType,"onUpdate:modelValue":t[10]||(t[10]=e=>b.selectTimeType=e),placeholder:"请选择时间类型",style:{width:"30%"}},{default:(0,o.k6)(()=>[(0,o.bF)(E,{label:"固定",value:"10"}),(0,o.bF)(E,{label:"随机",value:"20"})]),_:1},8,["modelValue"]),"20"===f.configForm.thinkTimeType?((0,o.uX)(),(0,o.CE)("span",c,[(0,o.bF)(z,{modelValue:f.configForm.thinkTime[0],"onUpdate:modelValue":t[11]||(t[11]=e=>f.configForm.thinkTime[0]=e),min:0,max:999,size:"small","controls-position":"right",onChange:e.handleChange,style:{width:"90px","margin-left":"10px"}},null,8,["modelValue","onChange"]),t[28]||(t[28]=(0,o.Lk)("span",{style:{"margin-right":"5px","margin-left":"5px"}},"-",-1)),(0,o.bF)(z,{modelValue:f.configForm.thinkTime[1],"onUpdate:modelValue":t[12]||(t[12]=e=>f.configForm.thinkTime[1]=e),min:0,max:999,size:"small","controls-position":"right",onChange:e.handleChange,style:{width:"90px"}},null,8,["modelValue","onChange"])])):((0,o.uX)(),(0,o.CE)("span",p,[(0,o.bF)(z,{modelValue:f.configForm.thinkTime[0],"onUpdate:modelValue":t[13]||(t[13]=e=>f.configForm.thinkTime[0]=e),min:0,max:999,size:"small","controls-position":"right",onChange:e.handleChange,style:{width:"90px","margin-left":"10px"}},null,8,["modelValue","onChange"])]))]),_:1}),"10"===f.configForm.pressureMode?((0,o.uX)(),(0,o.Wv)(j,{key:1,style:{"background-color":"#f5f7f9"},class:"card",shadow:"always"},{default:(0,o.k6)(()=>[(0,o.bF)(Q,{"label-width":"120px",model:f.FormConcurrency,rules:f.rulesConcurrencyMode,ref:"CaseRef"},{default:(0,o.k6)(()=>[(0,o.bF)(P,{label:"并发用户数：",prop:"concurrencyNumber"},{default:(0,o.k6)(()=>[(0,o.bF)(F,{modelValue:f.FormConcurrency.concurrencyNumber,"onUpdate:modelValue":t[14]||(t[14]=e=>f.FormConcurrency.concurrencyNumber=e)},null,8,["modelValue"])]),_:1}),(0,o.bF)(P,{label:"并发数步长：",prop:"concurrencyStep"},{default:(0,o.k6)(()=>[(0,o.bF)(F,{modelValue:f.FormConcurrency.concurrencyStep,"onUpdate:modelValue":t[15]||(t[15]=e=>f.FormConcurrency.concurrencyStep=e)},null,8,["modelValue"])]),_:1}),(0,o.bF)(P,{label:"持续时长：",prop:"lastLong"},{default:(0,o.k6)(()=>[(0,o.bF)(F,{modelValue:f.FormConcurrency.lastLong,"onUpdate:modelValue":t[16]||(t[16]=e=>f.FormConcurrency.lastLong=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1})):(0,o.Q3)("",!0),"20"===f.configForm.pressureMode?((0,o.uX)(),(0,o.Wv)(j,{key:2,style:{"margin-left":"7px","margin-right":"4px","background-color":"#f5f7f9"},class:"card",shadow:"always"},{default:(0,o.k6)(()=>[(0,o.bF)(Q,{"label-width":"125px",model:f.FormLadder,rules:f.rulesLadderMode,ref:"CaseRef"},{default:(0,o.k6)(()=>[((0,o.uX)(!0),(0,o.CE)(o.FK,null,(0,o.pI)(f.FormLadder.ladders,(e,l)=>((0,o.uX)(),(0,o.CE)("div",{key:l},[(0,o.Lk)("div",m,[(0,o.Lk)("span",null,"阶梯"+(0,r.v_)(l+1),1),(0,o.bF)(y,{disabled:l<1,size:"mini",type:"text",onClick:e=>b.removeLadder(l)},{default:(0,o.k6)(()=>t[29]||(t[29]=[(0,o.eW)(" 删除 ")])),_:2,__:[29]},1032,["disabled","onClick"])]),(0,o.bF)(P,{label:"并发用户数：",prop:"ladders."+l+".concurrencyNumber"},{default:(0,o.k6)(()=>[(0,o.bF)(F,{modelValue:e.concurrencyNumber,"onUpdate:modelValue":t=>e.concurrencyNumber=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),(0,o.bF)(P,{label:"并发数步长：",prop:"ladders."+l+".concurrencyStep"},{default:(0,o.k6)(()=>[(0,o.bF)(F,{modelValue:e.concurrencyStep,"onUpdate:modelValue":t=>e.concurrencyStep=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),(0,o.bF)(P,{label:"阶梯持续时长：",prop:"ladders."+l+".lastLong"},{default:(0,o.k6)(()=>[(0,o.bF)(F,{modelValue:e.lastLong,"onUpdate:modelValue":t=>e.lastLong=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]))),128))]),_:1},8,["model","rules"]),(0,o.bF)(y,{style:{width:"100%","margin-top":"20px","background-color":"#ecf5ff",color:"#409eff"},onClick:b.addLadder},{default:(0,o.k6)(()=>t[30]||(t[30]=[(0,o.eW)(" add Data ")])),_:1,__:[30]},8,["onClick"])]),_:1})):(0,o.Q3)("",!0)])]),_:1}),(0,o.Lk)("div",g,[(0,o.bF)(P,{label:"运行机器：",prop:"resource"},{default:(0,o.k6)(()=>[(0,o.bF)(U,{modelValue:f.configForm.resource,"onUpdate:modelValue":t[17]||(t[17]=e=>f.configForm.resource=e)},{default:(0,o.k6)(()=>[(0,o.bF)(W,{label:"10",onClick:b.defaultServer},{default:(0,o.k6)(()=>[t[31]||(t[31]=(0,o.eW)("默认 ")),(0,o.bF)(I,{content:"使用机器管理中默认机器运行",enterable:!1,placement:"top"},{default:(0,o.k6)(()=>[(0,o.bF)(C,null,{default:(0,o.k6)(()=>[(0,o.bF)(A)]),_:1})]),_:1})]),_:1,__:[31]},8,["onClick"]),(0,o.bF)(W,{label:"20"},{default:(0,o.k6)(()=>[t[32]||(t[32]=(0,o.eW)(" 自定义 ")),(0,o.bF)(I,{content:"支持选择多机器分布式运行",enterable:!1,placement:"top"},{default:(0,o.k6)(()=>[(0,o.bF)(C,null,{default:(0,o.k6)(()=>[(0,o.bF)(A)]),_:1})]),_:1})]),_:1,__:[32]})]),_:1},8,["modelValue"])]),_:1}),"20"===f.configForm.resource?((0,o.uX)(),(0,o.Wv)(D,{key:0,height:"200",data:f.serverData,style:{width:"100%","margin-top":"15px","margin-bottom":"15px"},border:"true",onSelectionChange:b.handleSelectionChange,ref:"serverTable"},{default:(0,o.k6)(()=>[(0,o.bF)(T,{type:"selection",width:"40px"}),(0,o.bF)(T,{align:"center",prop:"name",label:"机器名称"}),(0,o.bF)(T,{align:"center",prop:"host_ip",label:"IP",width:"130px"})]),_:1},8,["data","onSelectionChange"])):(0,o.Q3)("",!0)])])]),_:1},8,["model","rules"]),(0,o.Lk)("div",h,[(0,o.bF)(y,{onClick:b.closeDialog},{default:(0,o.k6)(()=>t[33]||(t[33]=[(0,o.eW)("取 消")])),_:1,__:[33]},8,["onClick"]),"添加预设"===f.dialogTitle?((0,o.uX)(),(0,o.Wv)(y,{key:0,type:"primary",onClick:b.clickAdd},{default:(0,o.k6)(()=>t[34]||(t[34]=[(0,o.eW)("保 存")])),_:1,__:[34]},8,["onClick"])):(0,o.Q3)("",!0),"编辑预设"===f.dialogTitle?((0,o.uX)(),(0,o.Wv)(y,{key:1,type:"primary",onClick:b.clickUpdate},{default:(0,o.k6)(()=>t[35]||(t[35]=[(0,o.eW)("保 存")])),_:1,__:[35]},8,["onClick"])):(0,o.Q3)("",!0)])])]),_:1},8,["title","modelValue","before-close"])],64)}l(44114),l(18111),l(22489),l(7588),l(61701);var f=l(57890),b=l(60782),y=l(57477),F=l(12933),_=l(51219),C={props:{setButton:{type:Boolean,default:!1},taskType:{type:String}},computed:{...(0,b.aH)({server:e=>e.server,pro:e=>e.pro}),username(){return window.sessionStorage.getItem("username")},selectedLogMode:{get(){return this.configForm.logMode.toString()},set(e){this.configForm.logMode=Number(e)}},selectPressureMode:{get(){return this.configForm.pressureMode.toString()},set(e){this.configForm.pressureMode=e}},selectControlMode:{get(){return this.configForm.control.toString()},set(e){this.configForm.control=Number(e)}},selectTaskType:{get(){return this.configForm.taskType.toString()},set(e){this.configForm.taskType=e}},selectTimeType:{get(){return this.configForm.thinkTimeType.toString()},set(e){this.configForm.thinkTimeType=e}}},components:{timerTaskCron:f.A,Close:y.Close,Check:y.Check,Plus:y.Plus,CopyDocument:y.CopyDocument,EditPen:y.EditPen,Delete:y.Delete,QuestionFilled:y.QuestionFilled},watch:{setButton(e){!0===e&&this.getPresetting(1)},"configForm.thinkTimeType"(e){this.configForm.thinkTime="20"===e?[this.configForm.thinkTime[0],this.configForm.thinkTime[1]]:[this.configForm.thinkTime[0]]}},data(){return{taskTypeMap:{10:"普通任务",20:"定时任务"},logModeMap:{0:"关闭",10:"开启-全部日志",20:"开启-仅成功日志",30:"开启-仅失败日志"},pressureModeMap:{10:"并发模式",20:"阶梯模式"},timeUnitMap:{s:"秒",m:"分钟",h:"小时"},controlMap:{10:"集合模式",20:"单独模式"},SettingDlg:!1,cronVisible:!1,dialogVisible:!1,importSetData:"",filterText:"",dialogType:"",dialogTitle:"",presettingList:[],serverData:[],defaultSelection:[],Selection:[],configForm:{name:"",rule:"",taskType:"10",logMode:"0",pressureMode:"10",timeUnit:"s",control:"20",resource:"10",pressureConfig:{},serverArray:[],project:"",creator:"",thinkTimeType:"10",thinkTime:[0]},FormConcurrency:{lastLong:"",concurrencyNumber:"",concurrencyStep:""},FormLadder:{ladders:[{concurrencyNumber:"",concurrencyStep:"",lastLong:""}]},pages:{count:0,current:1},rulesConfig:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],thinkTime:[{required:!0,message:"请输入思考时间",trigger:"blur"}]},rulesConcurrencyMode:{lastLong:[{required:!0,message:"请输入持续时长",trigger:"blur"}],concurrencyNumber:[{required:!0,message:"请输入并发数",trigger:"blur"}],concurrencyStep:[{required:!0,message:"请输入步长",trigger:"blur"}]},rulesLadderMode:{}}},methods:{currentPages(e){this.getPresetting(1),this.pages.current=e},cronFun(){this.cronVisible=!0},closeRunTimeCron(e){this.cronVisible=e},runTimeCron(e){this.configForm.rule=e},async getPresetting(e,t){const l=await this.$api.getPresetting({project_id:this.pro.id,isSetting:!1,page:e,name:t,taskType:this.taskType});200===l.status&&(this.presettingList=l.data.result,this.pages=l.data)},handleSelectionChange(e){this.configForm.serverArray=e.map(e=>e.id)},handelSettingDlg(){this.importSetData?(this.$emit("set-dlg",this.SettingDlg),this.$emit("set-data",this.importSetData),this.taskType=""):this.handleClose()},handleClose(){this.$emit("set-dlg",this.SettingDlg),this.taskType=""},selectSetChange(e){e.length>1&&(this.$refs.table.clearSelection(),this.$refs.table.toggleRowSelection(e[e.length-1],!0)),1===e.length?(this.importSetData=e[0]||null,console.log("importSetData",this.importSetData)):(this.importSetData=e[1]||null,console.log("importSetData",this.importSetData))},delPresetting(e){F.s.confirm("此操作将永久删除该设置, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const t=await this.$api.delPresetting(e);204===t.status&&((0,_.nk)({type:"success",message:"删除成功!"}),this.getPresetting(1))}).catch(()=>{(0,_.nk)({type:"info",message:"已取消删除"})})},dataSubmit(){const e={...this.configForm};if("10"===e.taskType&&delete e.rule,"10"===e.pressureMode){e.pressureConfig=this.FormConcurrency;const{ladders:t,...l}=e.pressureConfig;e.pressureConfig=l}else if("20"===e.pressureMode){e.pressureConfig=this.FormLadder;const{...t}=e.pressureConfig;e.pressureConfig=t}return e},async clickUpdate(){this.$refs.ConfigRef.validate(async e=>{if(!e)return;const t=this.dataSubmit(),l=await this.$api.updatePresetting(t.id,t);200===l.status&&((0,_.nk)({type:"success",message:"修改成功",duration:1e3}),this.closeDialog())})},async clickAdd(){this.$refs.ConfigRef.validate(async e=>{if(!e)return;const t=this.dataSubmit(),l=await this.$api.createPresetting(t);201===l.status&&((0,_.nk)({type:"success",message:"添加成功",duration:1e3}),this.closeDialog())})},async popup(e,t){switch(this.dialogType=e,this.dialogVisible=!0,e){case"add":this.dialogTitle="添加预设",this.configForm.creator=this.username,this.configForm.project=this.pro.id,await this.getServerData(e);break;case"edit":this.dialogTitle="编辑预设",this.configForm={...t},"10"===t.pressureMode?this.FormConcurrency=t.pressureConfig:this.FormLadder=t.pressureConfig,await this.getServerData(e),this.$nextTick(()=>{this.$refs.serverTable?this.Selection.forEach(e=>{this.$refs.serverTable.toggleRowSelection(e,!0)}):console.error("serverTable is undefined")}),this.setRules();break;default:this.dialogTitle="";break}},defaultServer(){this.defaultSelection=this.serverData.filter(e=>!0===e.default_code),this.handleSelectionChange(this.defaultSelection)},async getServerData(e){const t=await this.$api.getServers(this.pro.id,1);if(200===t.status)if(this.serverData=t.data.result,"add"===e)this.defaultSelection=this.serverData.filter(e=>!0===e.default_code),this.handleSelectionChange(this.defaultSelection);else if("edit"===e){const e=this.configForm.serverArray;this.Selection=this.serverData.filter(t=>e.includes(t.id))}},searchClick(){this.getPresetting(1,this.filterText)},closeDialog(){this.dialogVisible=!1,this.configForm={name:"",rule:"",taskType:"10",logMode:"0",pressureMode:"10",timeUnit:"s",control:"20",resource:"10",pressureConfig:{},serverArray:[],project:"",creator:"",thinkTimeType:"10",thinkTime:[0]},this.FormConcurrency={lastLong:"",concurrencyNumber:"",concurrencyStep:""},this.FormLadder={ladders:[{concurrencyNumber:"",concurrencyStep:"",lastLong:""}]},this.getPresetting(1)},async copyPresetting(e){const t={...e};t.name=t.name+"_副本",t.creator=this.username,t.project=this.pro.id,delete t.id,delete t.update_time,delete t.create_time;const l=await this.$api.createPresetting(t);201===l.status&&((0,_.nk)({type:"success",message:"复制成功",duration:1e3}),this.closeDialog())},addLadder(){this.FormLadder.ladders.push({concurrencyNumber:"",concurrencyStep:"",lastLong:""}),this.setRules()},setRules(){const e={};this.FormLadder.ladders.forEach((t,l)=>{e[`ladders.${l}.concurrencyNumber`]=[{required:!0,message:"并发用户数不能为空",trigger:"blur"}],e[`ladders.${l}.concurrencyStep`]=[{required:!0,message:"并发数步长不能为空",trigger:"blur"}],e[`ladders.${l}.lastLong`]=[{required:!0,message:"阶梯持续时长不能为空",trigger:"blur"}]}),this.rulesLadderMode=e},removeLadder(e){this.FormLadder.ladders.length>1&&(this.FormLadder.ladders.splice(e,1),this.setRules())}},mounted(){this.setRules()},created(){this.getPresetting(1)}},v=l(71241);const w=(0,v.A)(C,[["render",k],["__scopeId","data-v-0aaa9d90"]]);var T=w}}]);
//# sourceMappingURL=682.93174899.js.map