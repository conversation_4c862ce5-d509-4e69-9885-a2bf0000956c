{"version": 3, "file": "js/61.ea577b80.js", "mappings": "yOAiBUA,MAAM,e,GAGPC,MAAA,mB,aAsCAD,MAAM,e,SAM6BC,MAAA,mB,SAOLA,MAAA,mB,SACKA,MAAA,mB,SAC3BA,MAAA,mB,SAK0BA,MAAA,mB,SAC1BA,MAAA,mB,SASRA,MAAA,0D,GAcID,MAAM,iB,0ZArGhBE,EAAAA,EAAAA,IAsFQC,EAAA,CAtFC,cAAY,KAAKF,MAAA,uBAA2BG,KAAK,cAAcC,MAAM,KAAKC,KAAK,Q,kBACzF,IAYgB,CAZkB,OAAfC,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IAYgBC,EAAA,C,MAZyBC,MAAM,MAAMC,KAAK,M,kBACtD,IAUM,CAVKL,EAAAC,OAAOK,kB,WAAlBC,EAAAA,EAAAA,IAUM,MAAAC,EAAA,CATOR,EAAAC,OAAOK,gBAAgB,gBAAgBG,SAAS,sB,WAA3DF,EAAAA,EAAAA,IAGM,MAAAG,EAAA,EADJf,EAAAA,EAAAA,IAA4FgB,EAAA,CAAnFC,UAAU,E,WAAeZ,EAAAC,OAAOY,c,qCAAPb,EAAAC,OAAOY,cAAaC,GAAEC,KAAK,OAAOC,MAAM,U,uCAE5ET,EAAAA,EAAAA,IAIM,MAAAU,EAAA,EAHJtB,EAAAA,EAAAA,IAEeuB,EAAA,CAFDC,OAAO,QAAUC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAA0G,EAA1G3B,EAAAA,EAAAA,IAA0GgB,EAAA,CAAjGC,UAAU,EAAMW,UAAQvB,EAAAC,OAAOY,cAAeE,KAAK,OAAOC,MAAM,SAASG,OAAO,S,6EAKjE,OAAfnB,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IAWcC,EAAA,C,MAX2BC,MAAM,MAAMC,KAAK,M,kBACtD,IASe,EATfV,EAAAA,EAAAA,IASeuB,EAAA,CATDC,OAAO,QAASC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAOI,CAP2BtB,EAAAC,OAAOK,kB,WAAtCC,EAAAA,EAAAA,IAOI,MAPJiB,EAOI,G,aANLjB,EAAAA,EAAAA,IAKMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IALsB1B,EAAAC,OAAOK,gBAAe,CAArCR,EAAO6B,M,WAApBpB,EAAAA,EAAAA,IAKM,aAJLZ,EAAAA,EAAAA,IAGSiC,EAAA,CAHDlC,MAAA,qBAAyBG,KAAK,Q,kBACrC,IAAgD,EAAhDgC,EAAAA,EAAAA,IAAgD,IAAhDC,GAAgDC,EAAAA,EAAAA,IAAlBJ,EAAM,OAAH,IACjCE,EAAAA,EAAAA,IAAwB,aAAAE,EAAAA,EAAAA,IAAfjC,GAAK,K,yEAMgB,OAAfE,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IA4BcC,EAAA,C,MA5B2BC,MAAM,OAAOC,KAAK,M,kBACvD,IA0Be,EA1BfV,EAAAA,EAAAA,IA0BeuB,EAAA,CA1BDC,OAAO,QAASC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAwBI,CAxBOtB,EAAAC,OAAO+B,gB,WAAlBzB,EAAAA,EAAAA,IAwBI,MAAA0B,EAAA,EAvBLtC,EAAAA,EAAAA,IAsBcuC,EAAA,C,WAtBQC,EAAAC,Y,qCAAAD,EAAAC,YAAWtB,GAAErB,MAAM,e,kBACxC,IAMmB,EANnBE,EAAAA,EAAAA,IAMmB0C,EAAA,CANDhC,KAAK,KAAG,CACdiC,OAAKC,EAAAA,EAAAA,IACf,IAAclB,EAAA,KAAAA,EAAA,KAAdQ,EAAAA,EAAAA,IAAc,SAAX,WAAO,M,iBAEX,IAA+C,EAA/CA,EAAAA,EAAAA,IAA+C,WAA1C,qBAAiBE,EAAAA,EAAAA,IAAG/B,EAAAC,OAAOuC,QAAM,IACtCX,EAAAA,EAAAA,IAAyC,WAApC,kBAAcE,EAAAA,EAAAA,IAAG/B,EAAAC,OAAOwC,KAAG,K,OAEjC9C,EAAAA,EAAAA,IAOmB0C,EAAA,CAPDhC,KAAK,KAAG,CACdiC,OAAKC,EAAAA,EAAAA,IACf,IAAsBlB,EAAA,MAAAA,EAAA,MAAtBQ,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEd,IAA8C,G,aAAnDtB,EAAAA,EAAAA,IAEMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFsB1B,EAAAC,OAAOyC,gBAAe,CAArC5C,EAAO6B,M,WAApBpB,EAAAA,EAAAA,IAEM,aADLsB,EAAAA,EAAAA,IAAsC,aAAAE,EAAAA,EAAAA,IAA7BJ,EAAM,MAAQ7B,GAAK,O,eAG9BH,EAAAA,EAAAA,IAKmB0C,EAAA,CALDhC,KAAK,KAAG,CACdiC,OAAKC,EAAAA,EAAAA,IACf,IAAsBlB,EAAA,MAAAA,EAAA,MAAtBQ,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEnB,IAAuC,EAAvCA,EAAAA,EAAAA,IAAuC,aAAAE,EAAAA,EAAAA,IAA9B/B,EAAAC,OAAO+B,eAAa,K,oFAMjCrC,EAAAA,EAAAA,IAYcQ,EAAA,CAZDC,MAAM,MAAI,C,iBACtB,IAUe,EAVfT,EAAAA,EAAAA,IAUeuB,EAAA,CAVDC,OAAO,QAASC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAQM,EARNO,EAAAA,EAAAA,IAQM,MARNc,EAQM,G,aAPLpC,EAAAA,EAAAA,IAMMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANuB1B,EAAAC,OAAO2C,SAAQ,CAA/BC,EAAMC,M,WAAnBvC,EAAAA,EAAAA,IAMM,YAL8C,UAAZsC,EAAK,K,WAA5C3C,EAAAA,EAAAA,IAAmF0B,EAAA,C,MAA3ElC,MAAA,sB,kBAAqD,IAAa,E,iBAAVmD,EAAK,IAAD,K,YACZ,YAAZA,EAAK,K,WAAjD3C,EAAAA,EAAAA,IAAwG0B,EAAA,C,MAAhGlC,MAAA,qBAA2DG,KAAK,W,kBAAU,IAAa,E,iBAAVgD,EAAK,IAAD,K,YACjC,UAAZA,EAAK,K,WAAjD3C,EAAAA,EAAAA,IAAqG0B,EAAA,C,MAA7FlC,MAAA,qBAAyDG,KAAK,U,kBAAS,IAAa,E,iBAAVgD,EAAK,IAAD,K,YAC9B,SAAZA,EAAK,K,WAAjD3C,EAAAA,EAAAA,IAAqG0B,EAAA,C,MAA7FlC,MAAA,qBAAwDG,KAAK,W,kBAAU,IAAa,E,iBAAVgD,EAAK,IAAD,K,YAC1D,WAAZA,EAAK,K,WAArBtC,EAAAA,EAAAA,IAAiF,MAAjFwC,GAAiFhB,EAAAA,EAAAA,IAAhBc,EAAK,IAAD,K,4CAKzElD,EAAAA,EAAAA,IAMcQ,EAAA,CAND6C,SAAA,IAAQ,CACT5C,OAAKmC,EAAAA,EAAAA,IACf,IAAkG,CAArE,OAAjBvC,EAAAC,OAAOgD,Q,WAAnB1C,EAAAA,EAAAA,IAAkG,OAAlG2C,GAAkGnB,EAAAA,EAAAA,IAAA,YAAtB/B,EAAAC,OAAOgD,OAAK,IACtD,OAAjBjD,EAAAC,OAAOgD,Q,WAAxB1C,EAAAA,EAAAA,IAAuG,OAAvG4C,GAAuGpB,EAAAA,EAAAA,IAAA,YAAtB/B,EAAAC,OAAOgD,OAAK,M,WAC7F1C,EAAAA,EAAAA,IAA8D,OAA9D6C,GAA8DrB,EAAAA,EAAAA,IAAtB/B,EAAAC,OAAOgD,OAAK,M,MAGpB,OAAfjD,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IAKcC,EAAA,C,MAL2B6C,SAAA,I,CAC7B5C,OAAKmC,EAAAA,EAAAA,IACf,IAA4G,CAAhGvC,EAAAC,OAAOoD,aAAe,M,WAAlC9C,EAAAA,EAAAA,IAA4G,OAA5G+C,GAA4GvB,EAAAA,EAAAA,IAAA,YAA5B/B,EAAAC,OAAOoD,aAAW,M,WAClG9C,EAAAA,EAAAA,IAAkF,OAAlFgD,GAAkFxB,EAAAA,EAAAA,IAAA,YAA5B/B,EAAAC,OAAOoD,aAAW,M,wBAG1E1D,EAAAA,EAAAA,IAIcQ,EAAA,CAJD6C,SAAA,IAAQ,CACT5C,OAAKmC,EAAAA,EAAAA,IACf,IAAiC,E,2BAAlBvC,EAAAC,OAAOuD,UAAQ,K,cAIuD,OAAjBxD,EAAAC,OAAOgD,OAAkBjD,EAAAyD,U,WAA7FlD,EAAAA,EAAAA,IAEM,MAFNmD,EAEM,EADJ/D,EAAAA,EAAAA,IAAqFgE,EAAA,CAAxEC,QAAOC,EAAAC,cAAejE,KAAK,UAAUkE,MAAA,GAAMhE,KAAK,Q,kBAAO,IAAKsB,EAAA,MAAAA,EAAA,M,QAAL,Y,gDAGtE1B,EAAAA,EAAAA,IAeYqE,EAAA,CAfD1B,MAAM,Q,WAAiBH,EAAA8B,U,qCAAA9B,EAAA8B,UAASnD,GAAEoD,MAAM,MAAO,eAAcL,EAAAM,mB,CAS3DC,QAAM7B,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHNwC,EAGM,EAFJ1E,EAAAA,EAAAA,IAAqDgE,EAAA,CAAzCC,QAAOC,EAAAM,mBAAiB,C,iBAAE,IAAG9C,EAAA,MAAAA,EAAA,M,QAAH,U,6BACtC1B,EAAAA,EAAAA,IAA0DgE,EAAA,CAA/C9D,KAAK,UAAW+D,QAAOC,EAAAS,S,kBAAS,IAAGjD,EAAA,MAAAA,EAAA,M,QAAH,U,iDAX/C,IAOU,EAPV1B,EAAAA,EAAAA,IAOU4E,EAAA,CAPAC,MAAOrC,EAAAsC,SAAO,C,iBACtB,IAIe,EAJf9E,EAAAA,EAAAA,IAIe+E,EAAA,CAJDtE,MAAM,QAAM,C,iBACxB,IAEY,EAFZT,EAAAA,EAAAA,IAEYgF,EAAA,CAFD5E,KAAK,Q,WAAiBoC,EAAAsC,QAAQG,U,qCAARzC,EAAAsC,QAAQG,UAAS9D,GAAE+D,YAAY,WAAWnF,MAAA,gB,kBACT,IAA0B,G,aAA1Fa,EAAAA,EAAAA,IAAsHkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAAvCS,EAAA2C,WAARC,K,WAAvE7E,EAAAA,EAAAA,IAAsH8E,EAAA,CAA1G5E,MAAO2E,EAAK1E,KAAO,IAAM0E,EAAKtC,IAAM3C,MAAOiF,EAAKE,GAAgCtD,IAAKoD,EAAKE,I,oEAG1GtF,EAAAA,EAAAA,IAAiK+E,EAAA,CAAnJtE,MAAM,SAAO,C,iBAAC,IAAsH,EAAtHT,EAAAA,EAAAA,IAAsHuF,EAAA,CAA3GC,SAAU,CAAAC,QAAA,EAAAC,QAAA,G,WAAqClD,EAAAsC,QAAQa,K,qCAARnD,EAAAsC,QAAQa,KAAIxE,GAAEjB,KAAK,WAAW0F,aAAa,O,0HAczI,GACCC,MAAO,CACNvF,OAAQ,CACPwF,QAAS,CAAC,GAEXhC,QAAS,CACRgC,SAAS,IAGXC,SAAU,KACNC,EAAAA,EAAAA,IAAS,CAAC,SAEdC,WAAY,CACXC,OAAMA,EAAAA,GAEPC,IAAAA,GACC,MAAO,CACN1D,YAAa,CAAC,IAAK,IAAK,KAExB6B,WAAW,EAEXQ,QAAS,CACRG,UAAW,KACXU,KAAM,GACNS,KAAM,GACNC,OAAQ,OAENlB,WAAW,GAEhB,EACAmB,QAAS,CACR,aAAM3B,GACL4B,KAAKzB,QAAQ0B,QAAUD,KAAKE,IAAInB,GAChCiB,KAAKzB,QAAQsB,KAAOG,KAAKjG,OACzB,MAAMoG,QAAiBH,KAAKI,KAAKC,WAAWL,KAAKzB,SACzB,MAApB4B,EAASL,SACZE,KAAKM,SAAS,CACb3G,KAAM,UACN4G,QAAS,UACTC,SAAU,MAEXR,KAAKjC,WAAY,EACjBiC,KAAKzB,QAAU,CACdG,UAAW,KACXU,KAAM,GACNS,KAAM,GACNC,OAAQ,OAGX,EAEE7B,iBAAAA,GACE+B,KAAKjC,WAAY,EACjBiC,KAAKzB,QAAU,CAChBG,UAAW,KACXU,KAAM,GACNS,KAAM,GACNC,OAAQ,MAEP,EAGF,mBAAMlC,GACJ,MAAMuC,QAAiBH,KAAKI,KAAKK,mBACT,MAApBN,EAASL,SACXE,KAAKpB,WAAauB,EAASP,KAC3BI,KAAKjC,WAAY,EAErB,I,WChLJ,MAAM2C,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,O,6HCNOpH,MAAM,uB,GAEJA,MAAM,oB,GAIJA,MAAM,qB,GA2BRA,MAAM,kB,GAEJA,MAAM,iB,GACJA,MAAM,kB,GAmBJA,MAAM,c,iBAOFA,MAAM,a,GAEHA,MAAM,a,GAETA,MAAM,gB,GAuBdA,MAAM,gB,SAkBGA,MAAM,kB,GAEXA,MAAM,oB,GACJA,MAAM,kB,uBAeNA,MAAM,e,iBAMFA,MAAM,gB,qBAMNA,MAAM,sB,GACJA,MAAM,qB,GAGNA,MAAM,0B,GAGNA,MAAM,qB,GACJA,MAAM,e,GAINA,MAAM,iB,GACJA,MAAM,gB,GAkBlBA,MAAM,kB,GACJA,MAAM,iB,GACJA,MAAM,uB,GACJA,MAAM,c,GAGRA,MAAM,wB,GACJA,MAAM,c,GAGRA,MAAM,0B,GACJA,MAAM,c,GAMVA,MAAM,iB,GAUJA,MAAM,2B,GACJqH,IAAI,aAAarH,MAAM,mB,GAK3BA,MAAM,mB,GACJA,MAAM,kB,GAINA,MAAM,qB,GAWEA,MAAM,a,GAeNA,MAAM,e,SAC8BA,MAAM,wB,SAIjCA,MAAM,0B,SAWbA,MAAM,oB,GACJA,MAAM,mB,IACHA,MAAM,iB,IACNA,MAAM,e,IACNA,MAAM,uB,UAUXA,MAAM,oB,IAiDrBA,MAAM,iB,gkBAvTlBc,EAAAA,EAAAA,IAgUM,MAhUNC,EAgUM,EA9TJqB,EAAAA,EAAAA,IA4BM,MA5BNnB,EA4BM,C,aA3BJmB,EAAAA,EAAAA,IAEM,OAFDpC,MAAM,mBAAiB,EAC1BoC,EAAAA,EAAAA,IAAe,UAAX,Y,KAENA,EAAAA,EAAAA,IAuBM,MAvBNZ,EAuBM,EAtBJtB,EAAAA,EAAAA,IAaWuF,GAAA,C,WAZA/C,GAAA4E,W,qCAAA5E,GAAA4E,WAAUjG,GACnB+D,YAAY,OACZ,cAAY,iBACZmC,UAAA,GACAvH,MAAM,eACLwH,SAAKC,EAAAA,EAAAA,IAAQrD,GAAAsD,gBAAe,Y,CAElBC,QAAM7E,EAAAA,EAAAA,IACf,IAEY,EAFZ5C,EAAAA,EAAAA,IAEYgE,GAAA,CAFAC,QAAOC,GAAAsD,iBAAe,C,iBAChC,IAA6B,EAA7BxH,EAAAA,EAAAA,IAA6B0H,GAAA,M,iBAApB,IAAU,EAAV1H,EAAAA,EAAAA,IAAU2H,M,gEAIzB3H,EAAAA,EAAAA,IAOYgE,GAAA,CANV9D,KAAK,UACJmD,UAAWb,GAAAoF,YAAoC,IAAtBpF,GAAAoF,WAAWC,SAAiBrF,GAAAsF,OACrD7D,QAAOC,GAAA6D,QACRjI,MAAM,c,kBACP,IAED4B,EAAA,KAAAA,EAAA,K,QAFC,a,gEAOLd,EAAAA,EAAAA,IA+PM,MA/PNiB,EA+PM,EA7PJK,EAAAA,EAAAA,IAmDM,MAnDNC,EAmDM,EAlDJD,EAAAA,EAAAA,IAUM,MAVNI,EAUM,C,aATJJ,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVlC,EAAAA,EAAAA,IAOYgE,GAAA,CANV9D,KAAK,UACLE,KAAK,QACJ6D,QAAOC,GAAA8D,QACRlI,MAAM,gB,kBACP,IAED4B,EAAA,KAAAA,EAAA,K,QAFC,a,6BAMyB,IAApBc,GAAAyF,SAASJ,S,WADjBtH,EAAAA,EAAAA,IAMW2H,GAAA,C,MAJTC,YAAY,SACX,aAAY,K,kBAEb,IAA2D,EAA3DnI,EAAAA,EAAAA,IAA2DgE,GAAA,CAAhD9D,KAAK,UAAW+D,QAAOC,GAAA8D,S,kBAAS,IAAItG,EAAA,KAAAA,EAAA,K,QAAJ,W,qDAE3CQ,EAAAA,EAAAA,IA8BM,MA9BNc,EA8BM,G,aA7BJpC,EAAAA,EAAAA,IA4BMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA3BWS,GAAAyF,SAAR/E,K,WADTtC,EAAAA,EAAAA,IA4BM,OA1BHoB,IAAKkB,EAAKoC,GACVxF,OAAKsI,EAAAA,EAAAA,IAAA,cAAAC,OAA0B7F,GAAAsF,SAAW5E,EAAKoC,MAC/CrB,QAAK9C,GAAE+C,GAAAoE,WAAWpF,EAAKoC,K,EAExBpD,EAAAA,EAAAA,IAGM,MAHNqB,EAGM,EAFJvD,EAAAA,EAAAA,IAA6B0H,GAAA,M,iBAApB,IAAU,EAAV1H,EAAAA,EAAAA,IAAUuI,M,OACnBrG,EAAAA,EAAAA,IAA8C,OAA9CsB,GAA8CpB,EAAAA,EAAAA,IAAnBc,EAAKxC,MAAI,MAEtCwB,EAAAA,EAAAA,IAiBM,MAjBNuB,EAiBM,EAhBJzD,EAAAA,EAAAA,IAOawI,GAAA,CAPDC,QAAQ,KAAKC,UAAU,O,kBACjC,IAKU,EALV1I,EAAAA,EAAAA,IAKU0H,GAAA,CAJPzD,SAAKtC,EAAAA,EAAAA,IAAAR,GAAO+C,GAAAyE,SAASzF,GAAI,UAC1BpD,MAAM,e,kBAEN,IAAQ,EAARE,EAAAA,EAAAA,IAAQ4I,M,qCAGZ5I,EAAAA,EAAAA,IAOawI,GAAA,CAPDC,QAAQ,KAAKC,UAAU,O,kBACjC,IAKU,EALV1I,EAAAA,EAAAA,IAKU0H,GAAA,CAJPzD,SAAKtC,EAAAA,EAAAA,IAAAR,GAAO+C,GAAA2E,QAAQ3F,EAAKoC,IAAE,UAC5BxF,MAAM,2B,kBAEN,IAAU,EAAVE,EAAAA,EAAAA,IAAU8I,M,0DASxB5G,EAAAA,EAAAA,IAsMM,MAtMNyB,EAsMM,CAnMwB,IAApBnB,GAAAyF,SAASJ,S,WADjBtH,EAAAA,EAAAA,IAKW2H,GAAA,C,MAHTC,YAAY,kBACZrI,MAAM,gB,kBACN,IAA6D,EAA7DE,EAAAA,EAAAA,IAA6DgE,GAAA,CAAlD9D,KAAK,UAAW+D,QAAOC,GAAA8D,S,kBAAS,IAAMtG,EAAA,KAAAA,EAAA,K,QAAN,a,oCAK/Bc,GAAAoF,YAAoC,IAAtBpF,GAAAoF,WAAWC,S,WAOvCjH,EAAAA,EAAAA,IAmLM,MAnLNgD,EAmLM,EAjLJ1B,EAAAA,EAAAA,IAwDM,MAxDN6B,EAwDM,EAvDJ7B,EAAAA,EAAAA,IAaM,MAbNwC,EAaM,EAZJxC,EAAAA,EAAAA,IAGK,WAFSM,GAAAuG,kB,WAAZnI,EAAAA,EAAAA,IAAyD,OAAAoI,GAAA5G,EAAAA,EAAAA,IAAzBI,GAAAuG,iBAAe,M,WAC/CnI,EAAAA,EAAAA,IAAwB,OAAAqI,EAAX,YAEfjJ,EAAAA,EAAAA,IAOYgE,GAAA,CANV9D,KAAK,UACLE,KAAK,QACJ6D,QAAOC,GAAAgF,cACRpJ,MAAM,iB,kBACP,IAED4B,EAAA,MAAAA,EAAA,M,QAFC,a,+BAKHQ,EAAAA,EAAAA,IAuCM,MAvCNiH,EAuCM,G,aAtCJvI,EAAAA,EAAAA,IAqCMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IApCoBS,GAAAoF,WAAU,CAA1B1E,EAAMC,M,WADhBvC,EAAAA,EAAAA,IAqCM,OAnCHoB,IAAKkB,EAAKoC,GACXxF,MAAM,aACLmE,QAAK9C,GAAE+C,GAAAkF,UAAUlG,I,EAClBhB,EAAAA,EAAAA,IAKM,MALNmH,EAKM,EAJJnH,EAAAA,EAAAA,IAGO,OAFJpC,OAAKsI,EAAAA,EAAAA,IAAA,oBAAuBlE,GAAAoF,oBAAoBpG,KAChD,cAAagB,GAAAqF,mBAAmBrG,I,cAGrChB,EAAAA,EAAAA,IAyBM,MAzBNsH,EAyBM,EAxBJtH,EAAAA,EAAAA,IAEM,MAFNuH,EAEM,EADJvH,EAAAA,EAAAA,IAAwB,WAAAE,EAAAA,EAAAA,IAAjBc,EAAKxC,MAAI,MAElBwB,EAAAA,EAAAA,IAEM,MAFNwH,GAEMtH,EAAAA,EAAAA,IADDc,EAAKyC,MAAQ,QAAJ,IAEdzD,EAAAA,EAAAA,IAiBM,MAjBNyH,EAiBM,EAhBJzH,EAAAA,EAAAA,IAGM,MAHN0H,EAGM,EAFJ5J,EAAAA,EAAAA,IAA+B0H,GAAA,M,iBAAtB,IAAY,EAAZ1H,EAAAA,EAAAA,IAAY6J,M,OACrB3H,EAAAA,EAAAA,IAAyC,aAAAE,EAAAA,EAAAA,IAAhCc,EAAK4G,WAAa,GAAI,MAAG,MAEpC5H,EAAAA,EAAAA,IAWM,MAXN6H,EAWM,EAVJ7H,EAAAA,EAAAA,IAAkD,MAAlD8H,EAA0B,OAAG5H,EAAAA,EAAAA,IAAGe,EAAQ,GAAH,IACrCnD,EAAAA,EAAAA,IAQYgE,GAAA,CAPV9D,KAAK,SACL+J,OAAA,GACAnK,MAAM,mBACLmE,SAAKtC,EAAAA,EAAAA,IAAAR,GAAO+C,GAAAgG,SAAShH,EAAKoC,IAAE,UAC7B3C,MAAM,Q,kBAEN,IAA6B,EAA7B3C,EAAAA,EAAAA,IAA6B0H,GAAA,M,iBAApB,IAAU,EAAV1H,EAAAA,EAAAA,IAAU8I,M,wDAUjC5G,EAAAA,EAAAA,IAqHM,MArHNiI,EAqHM,EApHJjI,EAAAA,EAAAA,IAaM,MAbNkI,EAaM,EAZJlI,EAAAA,EAAAA,IAGM,MAHNmI,EAGM,EAFJnI,EAAAA,EAAAA,IAAyD,MAAzDoI,GAAyDlI,EAAAA,EAAAA,IAA9B8B,GAAAqG,sBAAuB,IAAC,G,eACnDrI,EAAAA,EAAAA,IAAmC,OAA9BpC,MAAM,cAAa,SAAK,OAE/BoC,EAAAA,EAAAA,IAGM,MAHNsI,EAGM,EAFJtI,EAAAA,EAAAA,IAAkD,MAAlDuI,GAAkDrI,EAAAA,EAAAA,IAAvBI,GAAAkI,QAAQ7C,QAAM,G,eACzC3F,EAAAA,EAAAA,IAAmC,OAA9BpC,MAAM,cAAa,SAAK,OAE/BoC,EAAAA,EAAAA,IAGM,MAHNyI,EAGM,EAFJzI,EAAAA,EAAAA,IAAuD,MAAvD0I,GAAuDxI,EAAAA,EAAAA,IAA5B8B,GAAA2G,qBAAiB,G,eAC5C3I,EAAAA,EAAAA,IAAkC,OAA7BpC,MAAM,cAAa,QAAI,SAKhCoC,EAAAA,EAAAA,IAaM,MAbN4I,EAaM,C,wTAHJ5I,EAAAA,EAAAA,IAEM,MAFN6I,EAEM,EADJ7I,EAAAA,EAAAA,IAAoD,MAApD8I,EAAoD,eAKxD9I,EAAAA,EAAAA,IAmFM,MAnFN+I,EAmFM,EAlFJ/I,EAAAA,EAAAA,IAGM,MAHNgJ,EAGM,C,eAFJhJ,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRlC,EAAAA,EAAAA,IAA6CiC,GAAA,CAArC/B,KAAK,OAAOE,KAAK,S,kBAAQ,IAAGsB,EAAA,MAAAA,EAAA,M,QAAH,U,iBAEnCQ,EAAAA,EAAAA,IA6EM,MA7ENiJ,EA6EM,EA5EJnL,EAAAA,EAAAA,IA2EWoL,GAAA,CA1ERjF,KAAM3D,GAAAkI,QACPW,OAAA,GACAtL,MAAA,eACAK,KAAK,QACJ,aAAY,SACbN,MAAM,iB,kBAEN,IAOkB,EAPlBE,EAAAA,EAAAA,IAOkBsL,GAAA,CAPD7K,MAAM,OAAO,YAAU,O,CAC3BqF,SAAOlD,EAAAA,EAAAA,IAIV2I,GAJiB,EACvBrJ,EAAAA,EAAAA,IAGM,MAHNsJ,EAGM,EAFJxL,EAAAA,EAAAA,IAA4B0H,GAAA,M,iBAAnB,IAAS,EAAT1H,EAAAA,EAAAA,IAASyL,M,OAClBvJ,EAAAA,EAAAA,IAAsD,aAAAE,EAAAA,EAAAA,IAA7CsJ,EAAAC,OAAOC,MAAML,EAAMM,IAAIC,cAAW,O,OAKjD9L,EAAAA,EAAAA,IAIkBsL,GAAA,CAJD7K,MAAM,KAAK8D,MAAM,O,CACrBuB,SAAOlD,EAAAA,EAAAA,IACqD2I,GAD9C,EACvBvL,EAAAA,EAAAA,IAAqEiC,GAAA,CAA7D7B,KAAK,QAAQ2L,OAAO,S,kBAAQ,IAAwB,E,iBAArBR,EAAMM,IAAIG,UAAQ,K,oBAI7DhM,EAAAA,EAAAA,IAakBsL,GAAA,CAbD7K,MAAM,KAAK8D,MAAM,O,CACrBuB,SAAOlD,EAAAA,EAAAA,IAUV2I,GAViB,EACvBrJ,EAAAA,EAAAA,IASM,MATN+J,EASM,CAR4B,QAArBV,EAAMM,IAAIxF,S,WAArBzF,EAAAA,EAAAA,IAGM,MAHNsL,EAGMxK,EAAA,MAAAA,EAAA,MAFJQ,EAAAA,EAAAA,IAAwC,OAAnCpC,MAAM,wBAAsB,UACjCoC,EAAAA,EAAAA,IAAgB,YAAV,OAAG,S,WAEXtB,EAAAA,EAAAA,IAGM,MAHNuL,EAGM,EAFJnM,EAAAA,EAAAA,IAA4B0H,GAAA,M,iBAAnB,IAAS,EAAT1H,EAAAA,EAAAA,IAASoM,M,qBAClBlK,EAAAA,EAAAA,IAAgB,YAAV,OAAG,W,OAMjBlC,EAAAA,EAAAA,IAuBkBsL,GAAA,CAvBD7K,MAAM,KAAK,YAAU,O,CACzBqF,SAAOlD,EAAAA,EAAAA,IAcL2I,GAdY,CACc,QAArBA,EAAMM,IAAIxF,S,WACxBzF,EAAAA,EAAAA,IAWM,MAXNyL,EAWM,EAVJnK,EAAAA,EAAAA,IAIM,MAJNoK,EAIM,EAHJpK,EAAAA,EAAAA,IAA0D,OAA1DqK,IAA0DnK,EAAAA,EAAAA,IAA3BmJ,EAAMM,IAAIW,SAAO,IAChDtK,EAAAA,EAAAA,IAAsD,OAAtDuK,GAA0B,MAAErK,EAAAA,EAAAA,IAAGmJ,EAAMM,IAAIa,KAAG,IAC5CxK,EAAAA,EAAAA,IAAmE,OAAnEyK,IAAmEvK,EAAAA,EAAAA,IAA9BmJ,EAAMM,IAAIe,WAAY,IAAC,MAE9D5M,EAAAA,EAAAA,IAIe6M,GAAA,CAHZC,WAAYC,WAAWxB,EAAMM,IAAIe,WACjC,eAAc,EACdI,MAAO9I,GAAA+I,iBAAiB1B,EAAMM,IAAIe,Y,+CAKvChM,EAAAA,EAAAA,IAGM,MAHNsM,GAGM,EAFJlN,EAAAA,EAAAA,IAA8B0H,GAAA,M,iBAArB,IAAW,EAAX1H,EAAAA,EAAAA,IAAWmN,M,qBACpBjL,EAAAA,EAAAA,IAAmB,YAAb,UAAM,S,OAMpBlC,EAAAA,EAAAA,IAWkBsL,GAAA,CAXD7K,MAAM,KAAK8D,MAAM,KAAK6I,MAAM,S,CAChCtH,SAAOlD,EAAAA,EAAAA,IAQJ2I,GARW,CAEM,QAArBA,EAAMM,IAAIxF,S,WADlB9F,EAAAA,EAAAA,IAOYyD,GAAA,C,MALV9D,KAAK,UACLmN,KAAA,GACCpJ,QAAK9C,GAAEuK,EAAA4B,QAAQC,KAAK,CAAD7M,KAAA,SAAA8M,OAAA,CAAAlI,GAAiCiG,EAAMM,IAAIvG,O,kBAChE,IAED5D,EAAA,MAAAA,EAAA,M,QAFC,W,iGAlLfnB,EAAAA,EAAAA,IAKW2H,GAAA,C,MAHTC,YAAY,aACZrI,MAAM,gB,kBACN,IAAmE,EAAnEE,EAAAA,EAAAA,IAAmEgE,GAAA,CAAxD9D,KAAK,UAAW+D,QAAOC,GAAAgF,e,kBAAe,IAAMxH,EAAA,KAAAA,EAAA,K,QAAN,a,8CAtEhBc,GAAAiL,YAkQvCzN,EAAAA,EAAAA,IAyBYqE,GAAA,C,WAxBD7B,GAAAkL,Q,qCAAAlL,GAAAkL,QAAOvM,GACfwB,MAAOH,GAAAmL,SAASrI,GAAK,SAAW,SACjCf,MAAM,MACL,eAAcL,GAAA0J,WACf,sBACAC,IAAI,OACJ/N,MAAM,e,CAYK2E,QAAM7B,EAAAA,EAAAA,IACf,IAGO,EAHPV,EAAAA,EAAAA,IAGO,OAHP4L,GAGO,EAFL9N,EAAAA,EAAAA,IAA6CgE,GAAA,CAAjCC,QAAOC,GAAA0J,YAAU,C,iBAAE,IAAElM,EAAA,MAAAA,EAAA,M,QAAF,S,6BAC/B1B,EAAAA,EAAAA,IAA0DgE,GAAA,CAA/C9D,KAAK,UAAW+D,QAAOC,GAAA6J,U,kBAAU,IAAErM,EAAA,MAAAA,EAAA,M,QAAF,S,iDAbhD,IASU,EATV1B,EAAAA,EAAAA,IASU4E,GAAA,CATAC,MAAOrC,GAAAmL,SAAUxG,IAAI,UAAU,iBAAe,O,kBACtD,IAOe,EAPfnH,EAAAA,EAAAA,IAOe+E,GAAA,CAPDtE,MAAM,OAAOuN,KAAK,OAAQC,MAAO,CAAC,CAADC,UAAA,EAAApH,QAAA,UAAAqH,QAAA,U,kBAC7C,IAKE,EALFnO,EAAAA,EAAAA,IAKEuF,GAAA,C,WAJS/C,GAAAmL,SAASjN,K,qCAAT8B,GAAAmL,SAASjN,KAAIS,GACtB+D,YAAY,UACZmC,UAAA,GACA+G,UAAA,I,mGAaQ5L,GAAA6L,W,WAAhB9N,EAAAA,EAAAA,IAAsF+N,GAAA,C,MAA3DC,aAAarK,GAAAsK,iBAAmB1G,OAAQtF,GAAAsF,Q,mKC9ThE/H,MAAA,wB,IA2BEA,MAAA,6C,qCA4CAD,MAAM,wB,IAcRC,MAAA,kB,IAEkCA,MAAA,mB,IACDA,MAAA,mB,IACAA,MAAA,qB,IACAA,MAAA,mB,gCAuBIA,MAAA,mB,UACYA,MAAA,mB,UACpCA,MAAA,mB,6ZApHlBC,EAAAA,EAAAA,IAmFYqE,EAAA,C,WAnFQ7B,EAAA6L,S,qCAAA7L,EAAA6L,SAAQlN,GAAEwB,MAAM,OAAO4B,MAAM,MAAO,eAAcL,EAAA0J,WAAYC,IAAI,K,kBACtF,IAiFM,EAjFN3L,EAAAA,EAAAA,IAiFM,MAjFNrB,GAiFM,EA/EJqB,EAAAA,EAAAA,IAsBM,aArBJlC,EAAAA,EAAAA,IAIWuF,EAAA,CAJDxF,MAAA,gB,WAA8ByC,EAAA4E,W,qCAAA5E,EAAA4E,WAAUjG,GAAE+D,YAAY,cAAcmC,UAAA,I,CACjEI,QAAM7E,EAAAA,EAAAA,IACf,IAA6D,EAA7D5C,EAAAA,EAAAA,IAA6DgE,EAAA,CAAlD9D,KAAK,UAAW+D,QAAOC,EAAAuK,a,kBAAa,IAAE/M,EAAA,KAAAA,EAAA,K,QAAF,S,qDAGnDQ,EAAAA,EAAAA,IAeO,cAdLlC,EAAAA,EAAAA,IAMYgE,EAAA,CALV9D,KAAK,UACLH,MAAA,sCACCkE,QAAOC,EAAA0J,WACPc,KAAMlM,EAAAmM,O,kBACJ,IACLjN,EAAA,KAAAA,EAAA,K,QADK,Y,mCAEL1B,EAAAA,EAAAA,IAMYgE,EAAA,CALV9D,KAAK,UACLH,MAAA,sCACCkE,QAAOC,EAAA0K,eACPF,KAAMlM,EAAAqM,O,kBACJ,IACLnN,EAAA,KAAAA,EAAA,K,QADK,Y,uCAKT1B,EAAAA,EAAAA,IAsDeuB,EAAA,CAtDDC,OAAO,uBAAqB,C,iBAC1C,IA2CM,EA3CNU,EAAAA,EAAAA,IA2CM,MA3CNnB,GA2CM,EA1CJf,EAAAA,EAAAA,IAyCSoL,EAAA,CAzCEjF,KAAM3D,EAAAsM,SAAWzD,OAAA,GAAO,aAAW,OAAO0D,OAAA,GAAQC,kBAAkB9K,EAAA+K,uB,kBACzE,IAA8E,EAA9EjP,EAAAA,EAAAA,IAA8EsL,EAAA,CAA7DpL,KAAK,YAAYgP,MAAM,SAAS3K,MAAM,QACvDvE,EAAAA,EAAAA,IAIkBsL,EAAA,CAJD7K,MAAM,KAAKyO,MAAM,SAAS3K,MAAM,M,CACpCuB,SAAOlD,EAAAA,EAAAA,IACmB2I,GADZ,EACvBrJ,EAAAA,EAAAA,IAAmC,aAAAE,EAAAA,EAAAA,IAA1BmJ,EAAM4D,OAAS,GAAH,K,OAGzBnP,EAAAA,EAAAA,IAIkBsL,EAAA,CAJD7K,MAAM,OAAOyO,MAAM,U,CACvBpJ,SAAOlD,EAAAA,EAAAA,IACkI2I,GAD3H,EACvBvL,EAAAA,EAAAA,IAAkJoP,EAAA,CAArItP,MAAM,eAAgBuP,GAAI,mBAAoBtP,MAAA,kBAAwBkE,QAAK9C,GAAE+C,EAAAoL,UAAU/D,EAAMM,M,kBAAM,IAAoB,E,iBAAjBN,EAAMM,IAAInL,MAAI,K,gCAGrIV,EAAAA,EAAAA,IAAoEsL,EAAA,CAAnD7K,MAAM,OAAOuN,KAAK,eAAgBkB,MAAM,YACzDlP,EAAAA,EAAAA,IAAyEsL,EAAA,CAAxD7K,MAAM,MAAM8D,MAAM,KAAKyJ,KAAK,YAAYkB,MAAM,YAC/DlP,EAAAA,EAAAA,IAOkBsL,EAAA,CAPD7K,MAAM,OAAOuN,KAAK,OAAOkB,MAAM,U,CACnCpJ,SAAOlD,EAAAA,EAAAA,IAIL2I,GAJY,EACzBvL,EAAAA,EAAAA,IAGawI,EAAA,CAHD1I,MAAM,OAAOiM,OAAO,OAAQtD,QAAS8C,EAAMM,IAAIlG,KAAM+C,UAAU,O,kBACzE,IAAiF,CAAtE6C,EAAMM,IAAIlG,KAAKkC,OAAM,K,WAAhCjH,EAAAA,EAAAA,IAAiF,MAAAU,IAAAc,EAAAA,EAAAA,IAAvCmJ,EAAMM,IAAIlG,KAAK4J,MAAM,EAAG,KAAM,MAAG,M,WAC3E3O,EAAAA,EAAAA,IAAsC,MAAAiB,IAAAO,EAAAA,EAAAA,IAAvBmJ,EAAMM,IAAIlG,MAAI,M,gCAIjC3F,EAAAA,EAAAA,IAAyEsL,EAAA,CAAxD7K,MAAM,MAAMuN,KAAK,UAAUkB,MAAM,SAAU3K,MAAM,SAClEvE,EAAAA,EAAAA,IAIkBsL,EAAA,CAJD7K,MAAM,OAAOyO,MAAM,SAAS3K,MAAM,O,CACtCuB,SAAOlD,EAAAA,EAAAA,IACyB2I,GADlB,E,iBACpBG,EAAAC,OAAOC,MAAML,EAAMM,IAAIC,cAAW,K,OAGzC9L,EAAAA,EAAAA,IAAyEsL,EAAA,CAAxD7K,MAAM,MAAMuN,KAAK,WAAWkB,MAAM,SAAS3K,MAAM,SAClEvE,EAAAA,EAAAA,IAIkBsL,EAAA,CAJD7K,MAAM,OAAOyO,MAAM,SAAS3K,MAAM,O,CACtCuB,SAAOlD,EAAAA,EAAAA,IAC2D2I,GADpD,CACdA,EAAMM,IAAI2D,c,WAAnB5O,EAAAA,EAAAA,IAA2E,IAAAuB,IAAAC,EAAAA,EAAAA,IAAzCsJ,EAAAC,OAAOC,MAAML,EAAMM,IAAI2D,cAAW,K,wBAGxExP,EAAAA,EAAAA,IAMkBsL,EAAA,CAND7K,MAAM,KAAK8D,MAAM,MAAM2K,MAAM,U,CACjCpJ,SAAOlD,EAAAA,EAAAA,IACsF2I,GAD/E,EACvBvL,EAAAA,EAAAA,IAAsGgE,EAAA,CAA1FC,QAAK9C,GAAE+C,EAAAuL,QAAQlE,EAAMM,KAAOzL,KAAM,QAASF,KAAK,UAAWwO,KAAMlM,EAAAkN,W,kBAAW,IAAEhO,EAAA,KAAAA,EAAA,K,QAAF,S,sCACxF1B,EAAAA,EAAAA,IAAuGgE,EAAA,CAA3FC,QAAK9C,GAAE+C,EAAAoL,UAAU/D,EAAMM,KAAOzL,KAAM,QAASF,KAAK,UAAWwO,KAAMlM,EAAAmN,M,kBAAM,IAAMjO,EAAA,KAAAA,EAAA,K,QAAN,a,sCACrF1B,EAAAA,EAAAA,IAA2GgE,EAAA,CAA/FC,QAAK9C,GAAE+C,EAAA0L,QAAQrE,EAAMM,IAAIvG,IAAMlF,KAAM,QAASF,KAAK,SAASkE,MAAA,GAAOsK,KAAMlM,EAAAqN,Q,kBAAQ,IAAEnO,EAAA,KAAAA,EAAA,K,QAAF,S,uFAKzGQ,EAAAA,EAAAA,IAQM,MARNI,GAQM,EAPJtC,EAAAA,EAAAA,IAMgB8P,EAAA,CANAC,WAAA,GAAWC,OAAO,mCACnBC,gBAAgB/L,EAAAgM,aAChB,oBAAmB,IACnBC,MAAO3N,EAAA4N,MAAMC,MACb,eAAc7N,EAAA4N,MAAME,QACtB,YAAU,MAAM,YAAU,O,wGAO3CtQ,EAAAA,EAAAA,IAqCWuQ,EAAA,C,WArCS/N,EAAAgO,U,qCAAAhO,EAAAgO,UAASrP,GAAG,eAAa,EAAOf,KAAK,O,kBACzD,IAmCM,EAnCN8B,EAAAA,EAAAA,IAmCM,MAnCNc,GAmCM,EAlCLhD,EAAAA,EAAAA,IAKkByQ,EAAA,CALD9N,MAAM,OAAOoM,OAAA,GAAQ2B,OAAQ,EAAG3Q,MAAA,yB,kBAChD,IAA+G,EAA/GC,EAAAA,EAAAA,IAA+G2Q,EAAA,CAAzFlQ,MAAM,MAAI,C,iBAAE,IAAsD,EAAtDyB,EAAAA,EAAAA,IAAsD,IAAtDkB,IAAsDhB,EAAAA,EAAAA,IAAzBI,EAAAoO,eAAelE,KAAG,K,OACjF1M,EAAAA,EAAAA,IAAkH2Q,EAAA,CAA5FlQ,MAAM,MAAI,C,iBAAC,IAA0D,EAA1DyB,EAAAA,EAAAA,IAA0D,IAA1DqB,IAA0DnB,EAAAA,EAAAA,IAA7BI,EAAAoO,eAAepE,SAAO,K,OACpFxM,EAAAA,EAAAA,IAAiH2Q,EAAA,CAA3FlQ,MAAM,MAAI,C,iBAAC,IAAyD,EAAzDyB,EAAAA,EAAAA,IAAyD,IAAzDsB,IAAyDpB,EAAAA,EAAAA,IAA1BI,EAAAoO,eAAeC,MAAI,K,OACnF7Q,EAAAA,EAAAA,IAAgH2Q,EAAA,CAA1FlQ,MAAM,MAAI,C,iBAAC,IAAwD,EAAxDyB,EAAAA,EAAAA,IAAwD,IAAxDuB,IAAwDrB,EAAAA,EAAAA,IAA3BI,EAAAoO,eAAeE,OAAK,K,2BAEnF5O,EAAAA,EAAAA,IAA8D,OAAzDnC,MAAA,sCAAuC,EAACmC,EAAAA,EAAAA,IAAW,SAAR,U,KAChDlC,EAAAA,EAAAA,IA0BeuB,EAAA,CA1BDC,OAAO,uBAAqB,C,iBACzC,IAwBW,EAxBXxB,EAAAA,EAAAA,IAwBWoL,EAAA,CAxBAjF,KAAM3D,EAAAoO,eAAeG,MAAOhR,MAAA,eAAoB,aAAW,Q,kBACrE,IAIkB,EAJlBC,EAAAA,EAAAA,IAIkBsL,EAAA,CAJDpL,KAAK,UAAQ,CAClB4F,SAAOlD,EAAAA,EAAAA,IAC4BiD,GADrB,EACxB7F,EAAAA,EAAAA,IAA6CgR,EAAA,CAAhC1Q,OAAQuF,EAAMgG,K,4BAG7B7L,EAAAA,EAAAA,IAA2CsL,EAAA,CAA1B7K,MAAM,MAAMuN,KAAK,UAClChO,EAAAA,EAAAA,IAIuBsL,EAAA,CAJN7K,MAAM,OAAOuN,KAAK,U,CACjBlI,SAAOlD,EAAAA,EAAAA,IACoDiD,GAD7C,CACS,QAAnBA,EAAMgG,IAAI3L,O,WAAtBU,EAAAA,EAAAA,IAAmE,OAAA+C,IAAAvB,EAAAA,EAAAA,IAA1ByD,EAAMgG,IAAIhJ,QAAM,K,wBAGnE7C,EAAAA,EAAAA,IAIuBsL,EAAA,CAJN7K,MAAM,QAAQuN,KAAK,e,CAClBlI,SAAOlD,EAAAA,EAAAA,IACyDiD,GADlD,CACS,QAAnBA,EAAMgG,IAAI3L,O,WAAtBU,EAAAA,EAAAA,IAAwE,OAAAgD,IAAAxB,EAAAA,EAAAA,IAA/ByD,EAAMgG,IAAInI,aAAW,K,wBAGxE1D,EAAAA,EAAAA,IAMkBsL,EAAA,CAND7K,MAAM,OAAOuN,KAAK,QAAQ,YAAU,Q,CACzClI,SAAOlD,EAAAA,EAAAA,IACwEiD,GADjE,CACO,MAAnBA,EAAMgG,IAAIvI,Q,WAAtB1C,EAAAA,EAAAA,IAAyF,OAAzFmD,IAAyF3B,EAAAA,EAAAA,IAAzByD,EAAMgG,IAAIvI,OAAK,IACpC,MAAnBuC,EAAMgG,IAAIvI,Q,WAA3B1C,EAAAA,EAAAA,IAA8F,OAA9F8D,IAA8FtC,EAAAA,EAAAA,IAAzByD,EAAMgG,IAAIvI,OAAK,M,WAC3F1C,EAAAA,EAAAA,IAA+D,OAA/DoI,IAA+D5G,EAAAA,EAAAA,IAAzByD,EAAMgG,IAAIvI,OAAK,M,sGAe5D,IACEuC,MAAO,CACLiC,OAAQmJ,QAEVlL,SAAU,KACLC,EAAAA,GAAAA,IAAS,CAAC,MAAM,WAErBC,WAAW,CACTiL,WAAUA,GAAAA,GAEZ/K,IAAAA,GACE,MAAO,CACLiB,WAAW,GACXgJ,MAAO,GACPtB,SAAU,GACV0B,WAAU,EACVI,eAAe,GACfvC,UAAS,EACT8C,aAAc,GACdtC,MAAK,SACLF,MAAK,SACLkB,OAAM,UACNF,KAAI,QACJD,UAASA,GAAAA,UAEb,EACDpJ,QAAS,KACH8K,EAAAA,GAAAA,IAAa,CAAC,aAElB3C,WAAAA,GACElI,KAAK8K,YAAY9K,KAAKE,IAAInB,GAAGiB,KAAK6J,MAAME,QAAQ/J,KAAKa,WACvD,EAGAkI,SAAAA,CAAUhK,GACViB,KAAK+G,QAAQC,KAAK,CAAE7M,KAAM,mBAC1B6F,KAAK+K,SAAShM,EACd,EAGA,iBAAM+L,CAAY7K,EAAQ+K,EAAK7Q,GAC7B,MAAMgG,QAAgBH,KAAKI,KAAK6K,YAAYhL,EAAQ+K,EAAK7Q,GACzD,GAAuB,MAAnBgG,EAASL,OAAc,CAE5B,MAAMoL,EAAU/K,EAASP,KAAK7F,OAC9BiG,KAAK6J,MAAQ1J,EAASP,KAElB,MAAMuL,QAAgCnL,KAAKI,KAAKgL,aAAa,CACjEC,SAAUrL,KAAKuB,SAEX,GAAsC,MAAlC4J,EAAuBrL,OAAgB,CACrC,MAAMwL,EAAiBH,EAAuBvL,KAAK7F,OAEnDiG,KAAKuI,SAAW2C,EAAQK,OAAO5O,IAAS2O,EAAeE,IAAIC,GAASA,EAAM1M,IAAIxE,SAASoC,EAAKoC,KAE5FiB,KAAKuI,SAAWvI,KAAKuI,SAASgD,OAAO5O,GAA2B,IAAnBA,EAAK4G,WAClDvD,KAAK6J,MAAMC,MAAS9J,KAAKuI,SAASjH,MACxC,CACL,CACA,EAEAqI,YAAAA,CAAa+B,GACV1L,KAAK8K,YAAY9K,KAAKE,IAAInB,GAAG2M,GAC7B1L,KAAK6J,MAAME,QAAU2B,CAEzB,EAGCrC,OAAAA,CAAQtK,GACP4M,GAAAA,EAAaC,QAAQ,qBAAsB,KAAM,CAC/CC,kBAAmB,KACnBC,iBAAkB,KAClBnS,KAAM,YAELoS,KAAKC,UACJ,MAAM7L,QAAiBH,KAAKI,KAAK6L,YAAYlN,GACvB,MAAnBoB,EAASL,UACVoM,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,UAGXP,KAAK8K,YAAY9K,KAAKE,IAAInB,IAC1BiB,KAAKa,WAAa,MAGrBsL,MAAM,MACLD,EAAAA,GAAAA,IAAU,CACRvS,KAAM,OACN4G,QAAS,WAGjB,EAGA,aAAM2I,CAAQtJ,GACV,MAAM2D,EAAY6I,SAASxM,EAAK2D,WAEhC,GADA8I,QAAQC,IAAI/I,GACTA,EAAY,EAClB,GAAIvD,KAAKuM,MAAO,CACf,MAAMtF,EAAS,CACduF,IAAKxM,KAAKuM,MACVd,MAAO7L,EAAKb,KAEP0N,EAAAA,GAAAA,IAAe,CACbrQ,MAAO,OACPmE,QAAS,cACT5G,KAAM,UACN6G,SAAS,MAEjB,MAAML,QAAiBH,KAAKI,KAAKsM,SAAS9M,EAAKb,GAAIkI,GAC5B,KAAnB9G,EAASL,SAEZE,KAAKqK,eAAiBlK,EAASP,KAC/BI,KAAKiK,WAAY,EAEnB,MACCiC,EAAAA,GAAAA,IAAU,CACTvS,KAAM,UACN4G,QAAS,aACTC,SAAU,WAGP0L,EAAAA,GAAAA,IAAU,CACbvS,KAAM,UACN4G,QAAS,YACTC,SAAU,KAEX,EAEFmM,UAAAA,GACE3M,KAAK4M,MAAM,cACb,EAEAvF,UAAAA,GACErH,KAAK2M,YACP,EAEA,oBAAMtE,GACF,GAAiC,IAA7BrI,KAAK4K,aAAatJ,OAMtB,YALE4K,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,cACTC,SAAU,MAIjB,IAAIyG,EAAS,CAAC,EACXA,EAAO4F,UAAY,IAAI7M,KAAK4K,cAC/B,MAAMzK,QAAiBH,KAAKI,KAAK0M,oBAAoB9M,KAAKuB,OAAO0F,GAC5C,MAAlB9G,EAASL,SACXoM,EAAAA,GAAAA,IAAU,CACTvS,KAAM,UACN4G,QAAS,OACTC,SAAU,MAGTR,KAAK2M,YACT,EAGAjE,qBAAAA,CAAsBqE,GAClB/M,KAAK4K,aAAemC,EAAIvB,IAAIlG,GAAOA,EAAIvG,IACvCsN,QAAQC,IAAItM,KAAK4K,aACnB,GAGHoC,OAAAA,GACChN,KAAK8K,YAAY9K,KAAKE,IAAInB,GAC5B,G,YCtSA,MAAM2B,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,UFgUA,IACEhB,WAAY,CACVuN,KAAI,MACJC,SAAQA,IAEVtN,IAAAA,GACE,MAAO,CAER8B,SAAU,GAEVH,OAAQ,GAER4C,QAAS,GAENiD,SAAS,CACPjN,KAAM,GACN4E,GAAI,MAGN8B,WAAY,GAEZsG,SAAS,EAET9F,WAAY,GAEZyG,UAAU,EAEVZ,SAAS,EAET1E,gBAAiB,GAEjB2K,YAAa,CACXlH,QAAS,UACTmH,QAAS,UACTC,OAAQ,UACRxN,KAAM,WAGZ,EACAL,SAAU,KACLC,EAAAA,GAAAA,IAAS,CAAC,MAAO,UACpB6N,YAAAA,GACE,MAAO,CACLC,SAAU,WACVrT,MAAO,OAEX,EACAsT,UAAW,WACZ,IAAIC,EAAU,GACVC,EAAW,GAEf,IAAK,IAAI/Q,KAAQqD,KAAKmE,QACrBsJ,EAAQzG,KAAKhH,KAAKoF,OAAOC,MAAM1I,EAAK4I,cACpCmI,EAAS1G,KAAKR,WAAW7J,EAAK0J,WAAWsH,QAAQ,IAElD,MAAO,CACNzT,MAAOuT,EAAQG,UACfhU,MAAO8T,EAET,GAEA3N,QAAS,KACJ8K,EAAAA,GAAAA,IAAa,CAAC,aAGjB,gBAAMgD,CAAW1T,GACf6F,KAAKkH,SAAU,EACf,IACE,IAAI/G,EAEFA,EADChG,QACgB6F,KAAKI,KAAK0N,aAAa9N,KAAKE,IAAInB,GAAI5E,SAEpC6F,KAAKI,KAAK0N,aAAa9N,KAAKE,IAAInB,IAG3B,MAApBoB,EAASL,SACXE,KAAK0B,SAAWvB,EAASP,KAGrBI,KAAK0B,SAASJ,OAAS,EACpBtB,KAAKuB,QAAWvB,KAAK0B,SAASqM,KAAKC,GAAQA,EAAKjP,KAAOiB,KAAKuB,QAI/DvB,KAAKiO,wBAHLjO,KAAK+B,WAAW/B,KAAK0B,SAAS,GAAG3C,KAMnCiB,KAAKuB,OAAS,GACdvB,KAAKwC,gBAAkB,GACvBxC,KAAKqB,WAAa,GAClBrB,KAAKmE,QAAU,IAGrB,CAAE,MAAOoG,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,WACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,WAAYA,EAC5B,CAAE,QACAvK,KAAKkH,SAAU,CACjB,CACF,EAGA+G,qBAAAA,GACE,GAAIjO,KAAKuB,OAAQ,CACf,MAAM2M,EAAclO,KAAK0B,SAASqM,KAAKC,GAAQA,EAAKjP,KAAOiB,KAAKuB,QAC5D2M,IACFlO,KAAKwC,gBAAkB0L,EAAY/T,KAEvC,MACE6F,KAAKwC,gBAAkB,EAE3B,EAGA,kBAAM2L,GACJ,IACE,MAAMhO,QAAiBH,KAAKI,KAAKgO,cAAc,CAAEJ,KAAMhO,KAAKuB,OAAQtB,QAASD,KAAKE,IAAInB,KAC9D,MAApBoB,EAASL,SACXE,KAAKmE,QAAUhE,EAASP,KAE5B,CAAE,MAAO2K,GACP8B,QAAQ9B,MAAM,WAAYA,EAC5B,CACF,EAGAxI,UAAAA,CAAWR,GACLvB,KAAKuB,SAAWA,IAClBvB,KAAKuB,OAASA,EACdvB,KAAKiO,wBACLjO,KAAKqO,UAAU9M,GAEnB,EAGA,aAAME,GACJzB,KAAKoH,SAAW,CACdjN,KAAM,QACN4E,GAAI,MAENiB,KAAKmH,SAAU,CACjB,EAGA/E,QAAAA,CAAS4L,GACPhO,KAAKoH,SAAW,IAAI4G,GACpBhO,KAAKmH,SAAU,CACjB,EAGAmH,eAAAA,CAAgB1O,GACdI,KAAK+B,WAAWnC,EAAKb,GACvB,EAGA,eAAMsP,CAAU9M,GACdvB,KAAKkH,SAAU,EACf,IACE,MAAM/G,QAAiBH,KAAKI,KAAKgL,aAAa,CAC5CC,SAAU9J,IAEY,MAApBpB,EAASL,SACXE,KAAKqB,WAAalB,EAASP,KAAK7F,QAElCiG,KAAKmO,cACP,CAAE,MAAO5D,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,WACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,WAAYA,EAC5B,CAAE,QACAvK,KAAKkH,SAAU,CACjB,CACF,EAGAjG,eAAAA,GACEjB,KAAK6N,WAAW7N,KAAKa,WACvB,EAGAwG,UAAAA,GACErH,KAAKmH,SAAU,CACjB,EAGA,cAAMK,GACJ,GAAKxH,KAAKoH,SAASjN,KASnB,IACE,IAAIgG,EACJ,GAAIH,KAAKoH,SAASrI,GAEhBoB,QAAiBH,KAAKI,KAAKmO,eAAevO,KAAKoH,SAASrI,GAAIiB,KAAKoH,cAC5D,CAEL,MAAMH,EAAS,CACbhH,QAASD,KAAKE,IAAInB,GAClB5E,KAAM6F,KAAKoH,SAASjN,MAEtBgG,QAAiBH,KAAKI,KAAKoO,eAAevH,EAC5C,CAEwB,MAApB9G,EAASL,QAAsC,MAApBK,EAASL,UACtCoM,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAASP,KAAKoH,SAASrI,GAAK,OAAS,OACrCyB,SAAU,OAEZR,KAAKmH,SAAU,QACTnH,KAAK6N,cAGN7N,KAAKoH,SAASrI,IAAMoB,EAASP,MAAQO,EAASP,KAAKb,GACtDiB,KAAK+B,WAAW5B,EAASP,KAAKb,IACrBiB,KAAKoH,SAASrI,KAAOiB,KAAKuB,QAEnCvB,KAAKiO,wBAGX,CAAE,MAAO1D,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,OACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,SAAUA,EAC1B,MA9CE2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,UACTC,SAAU,MA4ChB,EAGA,aAAMgB,GACJ,GAAKxB,KAAKuB,OASV,GAAIvB,KAAKuM,MAAO,CACd,MAAMtF,EAAS,CACbuF,IAAKxM,KAAKuM,MACVyB,KAAMhO,KAAKuB,OACXkN,MAAO,IAGThC,EAAAA,GAAAA,IAAe,CACbrQ,MAAO,OACPmE,QAAS,mBACT5G,KAAM,UACN6G,SAAU,MAGZ,IACE,MAAML,QAAiBH,KAAKI,KAAKoB,QAAQxB,KAAKuB,OAAQ0F,GAC9B,MAApB9G,EAASL,QACXE,KAAKmO,cAET,CAAE,MAAO5D,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,SACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,SAAUA,EAC1B,CACF,MACE2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,aACTC,SAAU,YAvCZ0L,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,WACTC,SAAU,MAuChB,EAGA8B,OAAAA,CAAQvD,GACN4M,GAAAA,EAAaC,QAAQ,eAAgB,KAAM,CACzCC,kBAAmB,KACnBC,iBAAkB,KAClBnS,KAAM,YAELoS,KAAKC,UACJ,IACE,MAAM7L,QAAiBH,KAAKI,KAAKsO,eAAe3P,GACxB,MAApBoB,EAASL,UACXoM,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,OACTC,SAAU,aAINR,KAAK6N,aAGP9O,IAAOiB,KAAKuB,SACdvB,KAAKuB,OAASvB,KAAK0B,SAASJ,OAAS,EAAItB,KAAK0B,SAAS,GAAG3C,GAAK,GAC3DiB,KAAKuB,OACPvB,KAAKqO,UAAUrO,KAAKuB,SAEpBvB,KAAKqB,WAAa,GAClBrB,KAAKmE,QAAU,KAIvB,CAAE,MAAOoG,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,OACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,SAAUA,EAC1B,IAED4B,MAAM,MACLD,EAAAA,GAAAA,IAAU,CACRvS,KAAM,OACN4G,QAAS,QACTC,SAAU,QAGlB,EAGAqC,SAAAA,CAAUlG,GACRqD,KAAK+G,QAAQC,KAAK,CAAE7M,KAAM,mBAC1BwC,EAAKgS,UAAY,OACjB3O,KAAK+K,SAASpO,EAChB,EAGAiS,kBAAAA,CAAmBC,GACI,WAAjBA,EAAQlV,MACVqG,KAAK2D,SAASkL,EAAQ9P,GAE1B,EAGA,cAAM4E,CAAS5E,GACb,IACE,IAAIkI,EAAS,CAAE6H,SAAU/P,GACzB,MAAMoB,QAAiBH,KAAKI,KAAK2O,oBAAoB/O,KAAKuB,OAAQ0F,GAC1C,MAApB9G,EAASL,UACXoM,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,OACTC,SAAU,OAGZR,KAAKqO,UAAUrO,KAAKuB,QAExB,CAAE,MAAOgJ,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,OACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,SAAUA,EAC1B,CACF,EAGA5H,aAAAA,GACE3C,KAAK8H,UAAW,CAClB,EAGAG,gBAAAA,GACEjI,KAAK8H,UAAW,EAChB9H,KAAKqO,UAAUrO,KAAKuB,OACtB,EAGAyC,kBAAAA,GACE,IAAKhE,KAAKmE,SAAmC,IAAxBnE,KAAKmE,QAAQ7C,OAChC,OAAO,EAGT,MAAM0N,EAAmBhP,KAAKmE,QAAQoH,OAAO0D,GAA4B,QAAlBA,EAAOnP,QAC9D,GAAgC,IAA5BkP,EAAiB1N,OACnB,OAAO,EAGT,MAAM4N,EAAgBF,EAAiBG,OAAO,CAACC,EAAKH,IAAWG,EAAM5I,WAAWyI,EAAO5I,WAAY,GACnG,OAAQ6I,EAAgBF,EAAiB1N,QAAQqM,QAAQ,EAC3D,EAGArJ,iBAAAA,GACE,OAAKtE,KAAKmE,QACHnE,KAAKmE,QAAQoH,OAAO0D,GAA4B,QAAlBA,EAAOnP,QAAoB0G,WAAWyI,EAAO5I,YAAc,IAAI/E,OAD1E,CAE5B,EAGAoF,gBAAAA,CAAiB2I,GACf,MAAM9I,EAAaC,WAAW6I,GAC9B,OAAI9I,GAAc,GACTvG,KAAKmN,YAAYlH,QACfM,GAAc,GAChBvG,KAAKmN,YAAYC,QAEjBpN,KAAKmN,YAAYE,MAE5B,EAGAtK,mBAAAA,CAAoB0I,GAElB,MAAqB,YAAjBA,EAAM3L,OACD,UAIJ2L,EAAMlI,WAAiC,IAApBkI,EAAMlI,UAK1BkI,EAAM6D,WACD7D,EAAM6D,WAAWrJ,QAAU,UAAY,UAIzC,QATE,gBAUX,EAGAjD,kBAAAA,CAAmByI,GAEjB,MAAqB,YAAjBA,EAAM3L,OACD,MAIJ2L,EAAMlI,WAAiC,IAApBkI,EAAMlI,UAK1BkI,EAAM6D,WACD7D,EAAM6D,WAAWrJ,QAAU,OAAS,OAItC,OATE,OAUX,GAEF+G,OAAAA,GACEhN,KAAK6N,YACP,EACA0B,MAAO,CACLpL,OAAAA,GAEEnE,KAAKwP,UAAU,KACTxP,KAAKyP,MAAMC,YACb1P,KAAK2P,OAAOC,OAAO5P,KAAKyP,MAAMC,WAAY1P,KAAKwN,UAAU5T,MAAOoG,KAAKwN,UAAUtT,QAGrF,IG5xBJ,MAAM,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASyG,IAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/components/common/caseResult.vue", "webpack://frontend-web/./src/components/common/caseResult.vue?0f1a", "webpack://frontend-web/./src/views/TestPlan/TestPlanNew.vue", "webpack://frontend-web/./src/views/TestPlan/TestCaseDlg.vue", "webpack://frontend-web/./src/views/TestPlan/TestCaseDlg.vue?2d58", "webpack://frontend-web/./src/views/TestPlan/TestPlanNew.vue?3ad9"], "sourcesContent": ["<template>\n\t  <el-tabs model-value=\"rb\" style=\"min-height: 300px;\" type=\"border-card\" value=\"rb\" size=\"mini\">\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应体\" name=\"rb\">\n      <div v-if=\"result.response_header\">\n        <div v-if=\"result.response_header['Content-Type'].includes('application/json')\">\n          <!-- 如果 Content-Type 是 application/json，渲染 JSON 格式的 Editor -->\n          <Editor :readOnly=\"true\" v-model=\"result.response_body\" lang=\"json\" theme=\"chrome\"></Editor>\n        </div>\n        <div v-else>\n          <el-scrollbar height=\"400px\"  @wheel.stop>\n            <Editor :readOnly=\"true\" v-html=\"result.response_body\" lang=\"html\" theme=\"chrome\" height=\"400px\"></Editor>\n          </el-scrollbar>\n        </div>\n      </div>\n    </el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应头\" name=\"rh\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div class=\"tab-box-sli\" v-if=\"result.response_header\">\n\t\t\t\t<div v-for=\"(value, key) in result.response_header\">\n\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" type=\"info\">\n\t\t\t\t\t\t<b style=\"color: #747474;\">{{ key + ' : ' }}</b>\n\t\t\t\t\t\t<span>{{ value }}</span>\n\t\t\t\t\t</el-tag>\n\t\t\t\t</div>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"请求信息\" name=\"rq\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div v-if=\"result.requests_body\">\n\t\t\t\t<el-collapse v-model=\"activeNames\" class=\"tab-box-sli\">\n\t\t\t\t\t<el-collapse-item name=\"1\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>General</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div>Request Method : {{ result.method }}</div>\n\t\t\t\t\t\t<div>Request URL : {{ result.url }}</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"2\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Headers</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div v-for=\"(value, key) in result.requests_header\">\n\t\t\t\t\t\t\t<span>{{ key + ' : ' + value }}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"3\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Payload</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<span>{{ result.requests_body }}</span>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t</el-collapse>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane label=\"日志\">\n\t\t\t<el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t\t<div class=\"tab-box-sli\">\n\t\t\t\t\t<div v-for=\"(item, index) in result.log_data\">\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-if=\"item[0] === 'DEBUG'\" >{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'WARNING'\" type=\"warning\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'ERROR'\" type=\"danger\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'INFO'\" type=\"success\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<pre v-else-if=\"item[0] === 'EXCEPT'\" style=\"color: #d60000;\">{{ item[1] }}</pre>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.state === '成功'\" style=\"color: #00AA7F;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else-if=\"result.state === '失败'\" style=\"color: #d18d17;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else style=\"color: #ff0000;\">{{ result.state }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.status_cede <= 300\" style=\"color: #00AA7F;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t\t<span v-else style=\"color: #ff5500;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t{{ 'Time : ' + result.run_time }}\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t</el-tabs>\n    <div style=\"margin-top: 10px;width: 100%;text-align: center;\" v-if=\"result.state === '失败' && showbtn\">\n      <el-button  @click=\"getInterfaces\" type=\"success\" plain size=\"mini\">提交bug</el-button>\n    </div>\n    <!-- 添加bug的弹框 -->\n    <el-dialog title=\"提交bug\" v-model=\"addBugDlg\" width=\"40%\" :before-close=\"closeDialogResult\">\n      <el-form :model=\"bugForm\">\n        <el-form-item label=\"所属接口\">\n          <el-select size=\"small\" v-model=\"bugForm.interface\" placeholder=\"bug对应的接口\" style=\"width: 100%;\">\n            <el-option :label=\"iter.name + ' ' + iter.url\" :value=\"iter.id\" v-for=\"iter in interfaces\" :key=\"iter.id\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"bug描述\"><el-input :autosize=\"{ minRows: 3, maxRows: 4 }\" v-model=\"bugForm.desc\" type=\"textarea\" autocomplete=\"off\"></el-input></el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"closeDialogResult\">取 消</el-button>\n          <el-button type=\"success\" @click=\"saveBug\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n</template>\n\n<script>\nimport Editor from './Editor.vue';\nimport { mapState } from 'vuex';\nexport default {\n\tprops: {\n\t\tresult: {\n\t\t\tdefault: {}\n\t\t},\n\t\tshowbtn: {\n\t\t\tdefault: true\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState(['pro'])\n\t},\n\tcomponents: {\n\t\tEditor\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveNames: ['1', '2', '3'],\n\t\t\t// 提交bug的显示窗口\n\t\t\taddBugDlg: false,\n\t\t\t// 添加bug的表单\n\t\t\tbugForm: {\n\t\t\t\tinterface: null,\n\t\t\t\tdesc: '',\n\t\t\t\tinfo: '',\n\t\t\t\tstatus: '待处理'\n\t\t\t},\n      interfaces:[]\n\t\t};\n\t},\n\tmethods: {\n\t\tasync saveBug() {\n\t\t\tthis.bugForm.project = this.pro.id;\n\t\t\tthis.bugForm.info = this.result;\n\t\t\tconst response = await this.$api.createBugs(this.bugForm);\n\t\t\tif (response.status === 201) {\n\t\t\t\tthis.$message({\n\t\t\t\t\ttype: 'success',\n\t\t\t\t\tmessage: 'bug提交成功',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\tthis.addBugDlg = false;\n\t\t\t\tthis.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n\t\t\t}\n\t\t},\n    // 取消按钮时重置输入信息\n    closeDialogResult() {\n      this.addBugDlg = false;\n      this.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n      },\n\n    // 获取接口列表\n    async getInterfaces() {\n      const response = await this.$api.getNewInterfaces();\n      if (response.status === 200) {\n        this.interfaces = response.data\n        this.addBugDlg = true\n      }\n    }\n\t}\n};\n</script>\n\n<style></style>\n", "import { render } from \"./caseResult.vue?vue&type=template&id=3a14eb2a\"\nimport script from \"./caseResult.vue?vue&type=script&lang=js\"\nexport * from \"./caseResult.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\r\n  <div class=\"test-plan-dashboard\">\r\n    <!-- 顶部操作区 -->\r\n    <div class=\"dashboard-header\">\r\n      <div class=\"dashboard-title\">\r\n        <h2>测试计划管理</h2>\r\n      </div>\r\n      <div class=\"dashboard-actions\">\r\n        <el-input \r\n          v-model=\"filterText\" \r\n          placeholder=\"搜索计划\" \r\n          prefix-icon=\"el-icon-search\"\r\n          clearable\r\n          class=\"search-input\"\r\n          @keyup.enter=\"handletreeClick\"\r\n        >\r\n          <template #append>\r\n            <el-button @click=\"handletreeClick\">\r\n              <el-icon><Search /></el-icon>\r\n            </el-button>\r\n          </template>\r\n        </el-input>\r\n        <el-button \r\n          type=\"success\" \r\n          :disabled=\"!scene_list || scene_list.length === 0 || !planId\" \r\n          @click=\"runPlan\"\r\n          class=\"run-button\"\r\n        >\r\n          运行计划\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主体内容区 -->\r\n    <div class=\"dashboard-main\" v-loading=\"loading\">\r\n      <!-- 左侧测试计划列表 -->\r\n      <div class=\"plans-sidebar\">\r\n        <div class=\"sidebar-header\">\r\n          <h3>测试计划列表</h3>\r\n          <el-button \r\n            type=\"primary\" \r\n            size=\"small\" \r\n            @click=\"addPlan\"\r\n            class=\"add-plan-btn\"\r\n          >\r\n            新建计划\r\n          </el-button>\r\n        </div>\r\n        \r\n        <el-empty \r\n          v-if=\"planList.length === 0\" \r\n          description=\"暂无测试计划\" \r\n          :image-size=\"100\"\r\n        >\r\n          <el-button type=\"primary\" @click=\"addPlan\">创建计划</el-button>\r\n        </el-empty>\r\n          <div class=\"plans-list\">\r\n            <div \r\n              v-for=\"item in planList\" \r\n              :key=\"item.id\" \r\n              :class=\"['plan-item', { active: planId === item.id }]\"\r\n              @click=\"selectPlan(item.id)\"\r\n            >\r\n              <div class=\"plan-info\">\r\n                <el-icon><Folder /></el-icon>\r\n                <span class=\"plan-name\">{{ item.name }}</span>\r\n              </div>\r\n              <div class=\"plan-actions\">\r\n                <el-tooltip content=\"编辑\" placement=\"top\">\r\n                  <el-icon \r\n                    @click.stop=\"editPlan(item)\" \r\n                    class=\"action-icon\"\r\n                  >\r\n                    <Edit />\r\n                  </el-icon>\r\n                </el-tooltip>\r\n                <el-tooltip content=\"删除\" placement=\"top\">\r\n                  <el-icon \r\n                    @click.stop=\"delPlan(item.id)\" \r\n                    class=\"action-icon delete-icon\"\r\n                  >\r\n                    <Delete />\r\n                  </el-icon>\r\n                </el-tooltip>\r\n              </div>\r\n            </div>\r\n          </div>\r\n      </div>\r\n      \r\n      <!-- 右侧主要内容区 -->\r\n      <div class=\"content-main\">\r\n        <!-- 没有测试计划时显示 -->\r\n        <el-empty \r\n          v-if=\"planList.length === 0\" \r\n          description=\"暂无测试计划，请先创建测试计划\" \r\n          class=\"center-empty\">\r\n          <el-button type=\"primary\" @click=\"addPlan\">创建测试计划</el-button>\r\n        </el-empty>\r\n        \r\n        <!-- 有测试计划但没有场景时显示 -->\r\n        <el-empty \r\n          v-else-if=\"!scene_list || scene_list.length === 0\" \r\n          description=\"当前计划暂无测试场景\" \r\n          class=\"center-empty\">\r\n          <el-button type=\"primary\" @click=\"clickAddScene\">添加测试场景</el-button>\r\n        </el-empty>\r\n        \r\n        <!-- 有测试场景时显示 -->\r\n        <div v-else class=\"content-layout\">\r\n          <!-- 测试场景区域 -->\r\n          <div class=\"scenes-container\">\r\n            <div class=\"section-header\">\r\n              <h3>\r\n                <span v-if=\"currentPlanName\">{{ currentPlanName }}</span>\r\n                <span v-else>测试场景</span>\r\n              </h3>\r\n              <el-button \r\n                type=\"primary\" \r\n                size=\"small\" \r\n                @click=\"clickAddScene\"\r\n                class=\"add-scene-btn\"\r\n              >\r\n                添加场景\r\n              </el-button>\r\n            </div>\r\n            \r\n            <div class=\"scenes-grid\">\r\n              <div \r\n                v-for=\"(item, index) in scene_list\" \r\n                :key=\"item.id\" \r\n                class=\"scene-card\" \r\n                @click=\"clickView(item)\">\r\n                <div class=\"scene-status\">\r\n                  <div \r\n                    :class=\"['status-indicator', getSceneStatusClass(item)]\" \r\n                    :data-status=\"getSceneStatusText(item)\"\r\n                  ></div>\r\n                </div>\r\n                <div class=\"scene-card-content\">\r\n                  <div class=\"scene-card-header\">\r\n                    <h4>{{ item.name }}</h4>\r\n                  </div>\r\n                  <div class=\"scene-card-description\">\r\n                    {{ item.desc || '暂无描述' }}\r\n                  </div>\r\n                  <div class=\"scene-card-footer\">\r\n                    <div class=\"scene-steps\">\r\n                      <el-icon><Document /></el-icon>\r\n                      <span>{{ item.stepCount || 0 }} 步骤</span>\r\n                    </div>\r\n                    <div class=\"scene-actions\">\r\n                      <div class=\"scene-number\">场景 {{ index + 1 }}</div>\r\n                      <el-button\r\n                        type=\"danger\" \r\n                        circle \r\n                        class=\"delete-scene-btn\"\r\n                        @click.stop=\"delScene(item.id)\"\r\n                        title=\"删除场景\"\r\n                      >\r\n                        <el-icon><Delete /></el-icon>\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 右侧测试数据区域 -->\r\n          <div class=\"data-container\">\r\n            <div class=\"data-overview\">\r\n              <div class=\"data-card pass-rate\">\r\n                <div class=\"data-value\">{{ getAveragePassRate() }}%</div>\r\n                <div class=\"data-label\">平均通过率</div>\r\n              </div>\r\n              <div class=\"data-card total-runs\">\r\n                <div class=\"data-value\">{{ records.length }}</div>\r\n                <div class=\"data-label\">总执行次数</div>\r\n              </div>\r\n              <div class=\"data-card success-runs\">\r\n                <div class=\"data-value\">{{ getSuccessfulRuns() }}</div>\r\n                <div class=\"data-label\">成功执行</div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 通过率趋势图 -->\r\n            <div class=\"chart-section\">\r\n              <div class=\"section-header\">\r\n                <h3>通过率趋势</h3>\r\n                <div class=\"chart-legend\">\r\n                  <div class=\"legend-item\">\r\n                    <div class=\"legend-color success\"></div>\r\n                    <div class=\"legend-text\">通过率</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-container-wrapper\">\r\n                <div ref=\"chartTable\" class=\"chart-container\"></div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 执行记录表格 -->\r\n            <div class=\"records-section\">\r\n              <div class=\"section-header\">\r\n                <h3>执行记录</h3>\r\n                <el-tag type=\"info\" size=\"small\">近三天</el-tag>\r\n              </div>\r\n              <div class=\"records-container\">\r\n                <el-table \r\n                  :data=\"records\" \r\n                  stripe \r\n                  style=\"width: 100%\" \r\n                  size=\"small\"\r\n                  :empty-text=\"'暂无执行记录'\"\r\n                  class=\"records-table\"\r\n                >\r\n                  <el-table-column label=\"执行时间\" min-width=\"120\">\r\n                    <template #default=\"scope\">\r\n                      <div class=\"time-cell\">\r\n                        <el-icon><Timer /></el-icon>\r\n                        <span>{{ $tools.rTime(scope.row.create_time) }}</span>\r\n                      </div>\r\n                    </template>\r\n                  </el-table-column>\r\n                  \r\n                  <el-table-column label=\"环境\" width=\"100\">\r\n                    <template #default=\"scope\">\r\n                      <el-tag size=\"small\" effect=\"plain\">{{ scope.row.env_name }}</el-tag>\r\n                    </template>\r\n                  </el-table-column>\r\n                  \r\n                  <el-table-column label=\"状态\" width=\"100\">\r\n                    <template #default=\"scope\">\r\n                      <div class=\"status-cell\">\r\n                        <div v-if=\"scope.row.status === '执行中'\" class=\"status-badge running\">\r\n                          <div class=\"status-indicator-dot\"></div>\r\n                          <span>执行中</span>\r\n                        </div>\r\n                        <div v-else class=\"status-badge completed\">\r\n                          <el-icon><Check /></el-icon>\r\n                          <span>已完成</span>\r\n                        </div>\r\n                      </div>\r\n                    </template>\r\n                  </el-table-column>\r\n                  \r\n                  <el-table-column label=\"结果\" min-width=\"120\">\r\n                    <template #default=\"scope\">\r\n                      <template v-if=\"scope.row.status !== '执行中'\">\r\n                        <div class=\"progress-wrapper\">\r\n                          <div class=\"progress-counts\">\r\n                            <span class=\"success-count\">{{ scope.row.success }}</span>\r\n                            <span class=\"total-count\">/ {{ scope.row.all }}</span>\r\n                            <span class=\"progress-percentage\">{{ scope.row.pass_rate }}%</span>\r\n                          </div>\r\n                          <el-progress \r\n                            :percentage=\"parseFloat(scope.row.pass_rate)\" \r\n                            :stroke-width=\"6\" \r\n                            :color=\"getProgressColor(scope.row.pass_rate)\"\r\n                          ></el-progress>\r\n                        </div>\r\n                      </template>\r\n                      <template v-else>\r\n                        <div class=\"progress-pending\">\r\n                          <el-icon><Loading /></el-icon>\r\n                          <span>进行中...</span>\r\n                        </div>\r\n                      </template>\r\n                    </template>\r\n                  </el-table-column>\r\n                  \r\n                  <el-table-column label=\"报告\" width=\"80\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                      <el-button \r\n                        v-if=\"scope.row.status !== '执行中'\" \r\n                        type=\"primary\" \r\n                        link\r\n                        @click=\"$router.push({ name: 'report', params: { id: scope.row.id } })\"\r\n                      >\r\n                        查看\r\n                      </el-button>\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 修改计划窗口 -->\r\n    <el-dialog \r\n      v-model=\"editDlg\" \r\n      :title=\"planForm.id ? '编辑测试计划' : '新建测试计划'\" \r\n      width=\"30%\" \r\n      :before-close=\"clickClear\"\r\n      destroy-on-close\r\n      top=\"20vh\"\r\n      class=\"plan-dialog\"\r\n    >\r\n      <el-form :model=\"planForm\" ref=\"treeRef\" label-position=\"top\">\r\n        <el-form-item label=\"计划名称\" prop=\"name\" :rules=\"[{ required: true, message: '请输入计划名称', trigger: 'blur' }]\">\r\n          <el-input \r\n            v-model=\"planForm.name\" \r\n            placeholder=\"请输入计划名称\" \r\n            clearable\r\n            autofocus\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"clickClear\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"savePlan\">保存</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 添加测试场景窗口 -->\r\n    <TestCase v-if=\"sceneDlg\" @close-modal=\"handleCloseModal\" :planId=\"planId\"></TestCase>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {ElMessage, ElMessageBox, ElNotification} from \"element-plus\";\r\nimport {mapMutations, mapState} from \"vuex\";\r\nimport { Icon } from '@iconify/vue'\r\nimport TestCase from '../../views/TestPlan/TestCaseDlg.vue';\r\nexport default {\r\n  components: {\r\n    Icon,\r\n    TestCase\r\n  },\r\n  data() {\r\n    return {\r\n      // 测试计划列表\r\n\t\t\tplanList: [],\r\n\t\t\t// 当前选中的测试计划ID\r\n\t\t\tplanId: '',\r\n\t\t\t// 测试计划中所有运行记录\r\n\t\t\trecords: [],\r\n      // 表单数据\r\n      planForm:{\r\n        name: '',\r\n        id: null\r\n      },\r\n      // 搜索关键词\r\n      filterText: '',\r\n      // 对话框控制\r\n      editDlg: false,\r\n      // 测试场景列表\r\n      scene_list: [],\r\n      // 场景对话框控制\r\n      sceneDlg: false,\r\n      // 加载状态\r\n      loading: false,\r\n      // 当前选中的计划名称\r\n      currentPlanName: '',\r\n      // 颜色配置\r\n      themeColors: {\r\n        success: '#67C23A',\r\n        warning: '#E6A23C',\r\n        danger: '#F56C6C',\r\n        info: '#909399'\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['pro', 'envId']),\r\n    defaultProps() {\r\n      return {\r\n        children: 'children',\r\n        label: 'name',\r\n      }\r\n    },\r\n    chartData: function() {\r\n\t\t\tlet runDate = [];\r\n\t\t\tlet passRate = [];\r\n\r\n\t\t\tfor (let item of this.records) {\r\n\t\t\t\trunDate.push(this.$tools.rTime(item.create_time));\r\n\t\t\t\tpassRate.push(parseFloat(item.pass_rate).toFixed(2));\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\tlabel: runDate.reverse(),\r\n\t\t\t\tvalue: passRate\r\n\t\t\t};\r\n\t\t},\r\n  },\r\n  methods: {\r\n    ...mapMutations(['CaseInfo']),\r\n    \r\n    // 获取测试计划列表\r\n    async getAllPlan(name) {\r\n      this.loading = true;\r\n      try {\r\n        let response;\r\n        if(name) {\r\n          response = await this.$api.getTestPlans(this.pro.id, name);\r\n        } else {\r\n          response = await this.$api.getTestPlans(this.pro.id);\r\n        }\r\n        \r\n        if (response.status === 200) {\r\n          this.planList = response.data;\r\n          \r\n          // 设置默认激活的测试计划并获取数据\r\n          if (this.planList.length > 0) {\r\n            if (!this.planId || !this.planList.find(plan => plan.id === this.planId)) {\r\n              this.selectPlan(this.planList[0].id);\r\n            } else {\r\n              // 更新当前计划名称\r\n              this.updateCurrentPlanName();\r\n            }\r\n          } else {\r\n            this.planId = '';\r\n            this.currentPlanName = '';\r\n            this.scene_list = [];\r\n            this.records = [];\r\n          }\r\n        }\r\n      } catch (error) {\r\n        ElMessage({\r\n          type: 'error',\r\n          message: '获取测试计划失败',\r\n          duration: 1500\r\n        });\r\n        console.error('获取测试计划失败', error);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 更新当前计划名称\r\n    updateCurrentPlanName() {\r\n      if (this.planId) {\r\n        const currentPlan = this.planList.find(plan => plan.id === this.planId);\r\n        if (currentPlan) {\r\n          this.currentPlanName = currentPlan.name;\r\n        }\r\n      } else {\r\n        this.currentPlanName = '';\r\n      }\r\n    },\r\n\r\n    // 获取测试计划所有的执行记录\r\n    async getAllRecord() {\r\n      try {\r\n        const response = await this.$api.getTestRecord({ plan: this.planId, project: this.pro.id });\r\n        if (response.status === 200) {\r\n          this.records = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('获取执行记录失败', error);\r\n      }\r\n    },\r\n    \r\n    // 选择测试计划\r\n    selectPlan(planId) {\r\n      if (this.planId !== planId) {\r\n        this.planId = planId;\r\n        this.updateCurrentPlanName();\r\n        this.getScenes(planId);\r\n      }\r\n    },\r\n    \r\n    // 添加测试计划\r\n    async addPlan() {\r\n      this.planForm = {\r\n        name: '新测试计划',\r\n        id: null\r\n      };\r\n      this.editDlg = true;\r\n    },\r\n    \r\n    // 编辑测试计划\r\n    editPlan(plan) {\r\n      this.planForm = {...plan};\r\n      this.editDlg = true;\r\n    },\r\n    \r\n    // 处理节点点击\r\n    handleNodeClick(data) {\r\n      this.selectPlan(data.id);\r\n    },\r\n    \r\n    // 获取测试场景\r\n    async getScenes(planId) {\r\n      this.loading = true;\r\n      try {\r\n        const response = await this.$api.getTestCase_({\r\n          testplan: planId\r\n        });\r\n        if (response.status === 200) {\r\n          this.scene_list = response.data.result;\r\n        }\r\n        this.getAllRecord();\r\n      } catch (error) {\r\n        ElMessage({\r\n          type: 'error',\r\n          message: '获取测试场景失败',\r\n          duration: 1500\r\n        });\r\n        console.error('获取测试场景失败', error);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    // 点击查询\r\n    handletreeClick() {\r\n      this.getAllPlan(this.filterText);\r\n    },\r\n\r\n    // 点击取消\r\n    clickClear() {\r\n      this.editDlg = false;\r\n    },\r\n    \r\n    // 保存测试计划\r\n    async savePlan() {\r\n      if (!this.planForm.name) {\r\n        ElMessage({\r\n          type: 'warning',\r\n          message: '请输入计划名称',\r\n          duration: 1500\r\n        });\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        let response;\r\n        if (this.planForm.id) {\r\n          // 更新现有计划\r\n          response = await this.$api.updateTestPlan(this.planForm.id, this.planForm);\r\n        } else {\r\n          // 创建新计划\r\n          const params = {\r\n            project: this.pro.id,\r\n            name: this.planForm.name\r\n          };\r\n          response = await this.$api.createTestPlan(params);\r\n        }\r\n        \r\n        if (response.status === 200 || response.status === 201) {\r\n          ElMessage({\r\n            type: 'success',\r\n            message: this.planForm.id ? '保存成功' : '创建成功',\r\n            duration: 1500\r\n          });\r\n          this.editDlg = false;\r\n          await this.getAllPlan();\r\n          \r\n          // 如果是新建的计划，选中它\r\n          if (!this.planForm.id && response.data && response.data.id) {\r\n            this.selectPlan(response.data.id);\r\n          } else if (this.planForm.id === this.planId) {\r\n            // 如果修改的是当前计划，更新名称\r\n            this.updateCurrentPlanName();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        ElMessage({\r\n          type: 'error',\r\n          message: '操作失败',\r\n          duration: 1500\r\n        });\r\n        console.error('保存计划失败', error);\r\n      }\r\n    },\r\n    \r\n    // 运行测试计划\r\n    async runPlan() {\r\n      if (!this.planId) {\r\n        ElMessage({\r\n          type: 'warning',\r\n          message: '请先选择测试计划',\r\n          duration: 1500\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (this.envId) {\r\n        const params = {\r\n          env: this.envId,\r\n          plan: this.planId,\r\n          types: 2\r\n        };\r\n        \r\n        ElNotification({\r\n          title: '开始运行',\r\n          message: '测试计划正在执行中，请稍候...',\r\n          type: 'success',\r\n          duration: 3000\r\n        });\r\n        \r\n        try {\r\n          const response = await this.$api.runPlan(this.planId, params);\r\n          if (response.status === 200) {\r\n            this.getAllRecord();\r\n          }\r\n        } catch (error) {\r\n          ElMessage({\r\n            type: 'error',\r\n            message: '运行计划失败',\r\n            duration: 1500\r\n          });\r\n          console.error('运行计划失败', error);\r\n        }\r\n      } else {\r\n        ElMessage({\r\n          type: 'warning',\r\n          message: '当前未选中执行环境!',\r\n          duration: 1500\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 删除测试计划\r\n    delPlan(id) {\r\n      ElMessageBox.confirm('确定要删除该测试计划吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n        .then(async () => {\r\n          try {\r\n            const response = await this.$api.deleteTestPlan(id);\r\n            if (response.status === 204) {\r\n              ElMessage({\r\n                type: 'success',\r\n                message: '删除成功',\r\n                duration: 1500\r\n              });\r\n              \r\n              // 重新获取计划列表\r\n              await this.getAllPlan();\r\n              \r\n              // 如果删除的是当前选中的计划，重置选择\r\n              if (id === this.planId) {\r\n                this.planId = this.planList.length > 0 ? this.planList[0].id : '';\r\n                if (this.planId) {\r\n                  this.getScenes(this.planId);\r\n                } else {\r\n                  this.scene_list = [];\r\n                  this.records = [];\r\n                }\r\n              }\r\n            }\r\n          } catch (error) {\r\n            ElMessage({\r\n              type: 'error',\r\n              message: '删除失败',\r\n              duration: 1500\r\n            });\r\n            console.error('删除计划失败', error);\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage({\r\n            type: 'info',\r\n            message: '已取消删除',\r\n            duration: 1500\r\n          });\r\n        });\r\n    },\r\n\r\n    // 点击查看测试场景详情\r\n    clickView(item) {\r\n      this.$router.push({ name: 'TestCaseDetail' });\r\n      item.back_type = 'plan';\r\n      this.CaseInfo(item);\r\n    },\r\n\r\n    // 处理场景命令\r\n    handleSceneCommand(command) {\r\n      if (command.type === 'delete') {\r\n        this.delScene(command.id);\r\n      }\r\n    },\r\n\r\n    // 删除测试场景\r\n    async delScene(id) {\r\n      try {\r\n        let params = { scene_id: id };\r\n        const response = await this.$api.deleteTestPlanScene(this.planId, params);\r\n        if (response.status === 200) {\r\n          ElMessage({\r\n            type: 'success',\r\n            message: '删除成功',\r\n            duration: 1500\r\n          });\r\n          // 更新页面中当前任务的数据\r\n          this.getScenes(this.planId);\r\n        }\r\n      } catch (error) {\r\n        ElMessage({\r\n          type: 'error',\r\n          message: '删除失败',\r\n          duration: 1500\r\n        });\r\n        console.error('删除场景失败', error);\r\n      }\r\n    },\r\n    \r\n    // 点击添加场景\r\n    clickAddScene() {\r\n      this.sceneDlg = true;\r\n    },\r\n\r\n    // 处理关闭模态框\r\n    handleCloseModal() {\r\n      this.sceneDlg = false; // 关闭弹窗\r\n      this.getScenes(this.planId);\r\n    },\r\n    \r\n    // 获取平均通过率\r\n    getAveragePassRate() {\r\n      if (!this.records || this.records.length === 0) {\r\n        return 0;\r\n      }\r\n      \r\n      const completedRecords = this.records.filter(record => record.status !== '执行中');\r\n      if (completedRecords.length === 0) {\r\n        return 0;\r\n      }\r\n      \r\n      const totalPassRate = completedRecords.reduce((sum, record) => sum + parseFloat(record.pass_rate), 0);\r\n      return (totalPassRate / completedRecords.length).toFixed(2);\r\n    },\r\n    \r\n    // 获取成功执行次数\r\n    getSuccessfulRuns() {\r\n      if (!this.records) return 0;\r\n      return this.records.filter(record => record.status !== '执行中' && parseFloat(record.pass_rate) >= 80).length;\r\n    },\r\n    \r\n    // 获取进度条颜色\r\n    getProgressColor(rate) {\r\n      const percentage = parseFloat(rate);\r\n      if (percentage >= 80) {\r\n        return this.themeColors.success;\r\n      } else if (percentage >= 60) {\r\n        return this.themeColors.warning;\r\n      } else {\r\n        return this.themeColors.danger;\r\n      }\r\n    },\r\n    \r\n    // 获取场景状态的CSS类名\r\n    getSceneStatusClass(scene) {\r\n      // 如果场景正在执行中\r\n      if (scene.status === 'running') {\r\n        return 'running';\r\n      }\r\n      \r\n      // 如果场景没有步骤\r\n      if (!scene.stepCount || scene.stepCount === 0) {\r\n        return 'not-configured';\r\n      }\r\n      \r\n      // 如果场景有执行结果\r\n      if (scene.lastResult) {\r\n        return scene.lastResult.success ? 'success' : 'failure';\r\n      }\r\n      \r\n      // 如果场景有步骤但未执行过\r\n      return 'ready';\r\n    },\r\n    \r\n    // 获取场景状态的文本描述\r\n    getSceneStatusText(scene) {\r\n      // 如果场景正在执行中\r\n      if (scene.status === 'running') {\r\n        return '执行中';\r\n      }\r\n      \r\n      // 如果场景没有步骤\r\n      if (!scene.stepCount || scene.stepCount === 0) {\r\n        return '未配置步骤';\r\n      }\r\n      \r\n      // 如果场景有执行结果\r\n      if (scene.lastResult) {\r\n        return scene.lastResult.success ? '执行成功' : '执行失败';\r\n      }\r\n      \r\n      // 如果场景有步骤但未执行过\r\n      return '准备就绪';\r\n    },\r\n  },\r\n  created() {\r\n    this.getAllPlan();\r\n  },\r\n  watch: {\r\n    records() {\r\n      // 渲染通过率趋势图\r\n      this.$nextTick(() => {\r\n        if (this.$refs.chartTable) {\r\n          this.$chart.chart3(this.$refs.chartTable, this.chartData.value, this.chartData.label);\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器样式 */\r\n.test-plan-dashboard {\r\n  height: 100vh;\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n  color: #333;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  background-image: linear-gradient(rgba(255, 255, 255, 0.7) 1px, transparent 1px),\r\n                    linear-gradient(90deg, rgba(255, 255, 255, 0.7) 1px, transparent 1px);\r\n  background-size: 20px 20px;\r\n  background-position: center center;\r\n  overflow: hidden; /* 防止整体出现滚动条 */\r\n}\r\n\r\n/* 头部样式 */\r\n.dashboard-header {\r\n  padding: 20px 28px;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  z-index: 10;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.03);\r\n  flex-shrink: 0; /* 防止头部被压缩 */\r\n}\r\n\r\n.dashboard-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 24px;\r\n}\r\n\r\n.dashboard-title h2 {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #1f2329;\r\n  position: relative;\r\n  padding-left: 16px;\r\n}\r\n\r\n.dashboard-title h2:before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 4px;\r\n  height: 20px;\r\n  background: linear-gradient(to bottom, #409EFF, #53a8ff);\r\n  border-radius: 2px;\r\n}\r\n\r\n.dashboard-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.search-input {\r\n  width: 260px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-input:hover, .search-input:focus-within {\r\n  box-shadow: 0 0 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.run-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 10px 16px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  border-radius: 8px;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.run-button:not(:disabled):hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(103, 194, 58, 0.2);\r\n}\r\n\r\n/* 主内容区域 */\r\n.dashboard-main {\r\n  flex: 1;\r\n  display: flex;\r\n  gap: 20px;\r\n  padding: 20px;\r\n  overflow: auto; /* 允许主区域滚动 */\r\n  position: relative;\r\n}\r\n\r\n/* 计划列表侧边栏 */\r\n.plans-sidebar {\r\n  width: 280px;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f9fbfd 100%);\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  flex-shrink: 0; /* 防止侧边栏被压缩 */\r\n  max-height: calc(100vh - 120px); /* 限制最大高度 */\r\n}\r\n\r\n.plans-sidebar:hover {\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.plans-sidebar:before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, #409EFF, #79bbff);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.plans-sidebar:hover:before {\r\n  opacity: 1;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-bottom: 1px solid #f0f2f5;\r\n  background-color: rgba(64, 158, 255, 0.03);\r\n}\r\n\r\n.sidebar-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #1f2329;\r\n  position: relative;\r\n  padding-left: 12px;\r\n}\r\n\r\n.sidebar-header h3:before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 3px;\r\n  height: 14px;\r\n  background: linear-gradient(to bottom, #409EFF, #53a8ff);\r\n  border-radius: 2px;\r\n}\r\n\r\n.add-plan-btn {\r\n  padding: 8px 14px;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-plan-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.15);\r\n}\r\n\r\n\r\n.plans-list {\r\n  padding: 12px;\r\n  overflow-y: auto; /* 允许计划列表滚动 */\r\n  flex: 1; /* 使列表可以占满剩余空间 */\r\n}\r\n\r\n.plan-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 14px;\r\n  margin-bottom: 6px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid transparent;\r\n  background-color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n.plan-item:hover {\r\n  background-color: rgba(64, 158, 255, 0.05);\r\n  border-color: rgba(64, 158, 255, 0.1);\r\n  transform: translateX(4px);\r\n}\r\n\r\n.plan-item.active {\r\n  background-color: rgba(64, 158, 255, 0.1);\r\n  color: #409eff;\r\n  border-left: 3px solid #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.plan-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  overflow: hidden;\r\n}\r\n\r\n.plan-info .el-icon {\r\n  font-size: 18px;\r\n  color: #409eff;\r\n  opacity: 0.8;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.plan-item:hover .plan-info .el-icon {\r\n  opacity: 1;\r\n  transform: scale(1.1);\r\n}\r\n\r\n.plan-name {\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.plan-item.active .plan-name {\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.plan-actions {\r\n  display: none;\r\n  gap: 8px;\r\n}\r\n\r\n.plan-item:hover .plan-actions {\r\n  display: flex;\r\n}\r\n\r\n.action-icon {\r\n  font-size: 16px;\r\n  color: #909399;\r\n  cursor: pointer;\r\n  padding: 4px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.action-icon:hover {\r\n  color: #409eff;\r\n  background-color: rgba(64, 158, 255, 0.1);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.delete-icon:hover {\r\n  color: #f56c6c;\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n}\r\n\r\n/* 内容主区域 */\r\n.content-main {\r\n  flex: 1;\r\n  overflow: auto; /* 允许右侧主要内容区域滚动 */\r\n  position: relative;\r\n  display: flex; /* 使其内容可以灵活排布 */\r\n  flex-direction: column; /* 垂直排列内容 */\r\n}\r\n\r\n.center-empty {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  text-align: center;\r\n  width: 100%;\r\n  max-width: 400px;\r\n}\r\n\r\n.center-empty .el-button {\r\n  margin-top: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.center-empty .el-button:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n/* 布局 */\r\n.content-layout {\r\n  display: grid;\r\n  grid-template-columns: 1fr 380px;\r\n  gap: 20px;\r\n  height: auto; /* 改为自适应高度 */\r\n  min-height: 100%; /* 至少占满容器高度 */\r\n  overflow: visible; /* 不隐藏溢出内容 */\r\n}\r\n\r\n/* 场景区域 */\r\n.scenes-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\r\n  padding: 16px;\r\n  overflow: hidden;\r\n  height: auto; /* 改为自适应高度 */\r\n  min-height: 400px; /* 设置最小高度 */\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #1f2329;\r\n}\r\n\r\n.scenes-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));\r\n  gap: 16px;\r\n  overflow-y: auto; /* 允许场景网格滚动 */\r\n  padding-right: 8px;\r\n  flex: 1; /* 占满剩余空间 */\r\n  max-height: calc(100vh - 250px); /* 限制最大高度 */\r\n  grid-auto-rows: 160px; /* 确保每行的高度一致 */\r\n}\r\n\r\n.scene-card {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  height: 160px; /* 设置固定高度 */\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  border: 1px solid #ebeef5;\r\n  position: relative;\r\n}\r\n\r\n.scene-card:hover {\r\n  transform: translateY(-6px);\r\n  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);\r\n  border-color: #d0e2ff;\r\n}\r\n\r\n.scene-card:before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, #67C23A, #95D475);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.scene-card:hover:before {\r\n  opacity: 1;\r\n}\r\n\r\n.scene-status {\r\n  width: 20px; /* 增加宽度确保圆圈完全显示 */\r\n  background: linear-gradient(to bottom, #f0f2f5, #e6e6e6);\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  padding-top: 16px; /* 使用内边距代替margin */\r\n}\r\n\r\n.status-indicator {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n/* 使用伪元素显示提示文本 */\r\n.status-indicator::after {\r\n  content: attr(data-status);\r\n  position: absolute;\r\n  left: 20px;\r\n  top: -4px;\r\n  background-color: rgba(0, 0, 0, 0.8);\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  z-index: 10;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: opacity 0.3s ease, visibility 0.3s ease;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 鼠标悬停时显示提示 */\r\n.status-indicator:hover::after {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n/* 状态指示器颜色 */\r\n.status-indicator.ready {\r\n  background-color: #67C23A;\r\n  box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.2);\r\n}\r\n\r\n.status-indicator.pending {\r\n  background-color: #E6A23C;\r\n  box-shadow: 0 0 0 4px rgba(230, 162, 60, 0.2);\r\n}\r\n\r\n.status-indicator.success {\r\n  background-color: #67C23A;\r\n  box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.2);\r\n}\r\n\r\n.status-indicator.failure {\r\n  background-color: #F56C6C;\r\n  box-shadow: 0 0 0 4px rgba(245, 108, 108, 0.2);\r\n}\r\n\r\n.status-indicator.running {\r\n  background-color: #409EFF;\r\n  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.status-indicator.not-configured {\r\n  background-color: #909399;\r\n  box-shadow: 0 0 0 4px rgba(144, 147, 153, 0.2);\r\n}\r\n\r\n/* 悬停效果 */\r\n.scene-card:hover .status-indicator.ready,\r\n.scene-card:hover .status-indicator.success {\r\n  box-shadow: 0 0 0 6px rgba(103, 194, 58, 0.25);\r\n}\r\n\r\n.scene-card:hover .status-indicator.pending {\r\n  box-shadow: 0 0 0 6px rgba(230, 162, 60, 0.25);\r\n}\r\n\r\n.scene-card:hover .status-indicator.failure {\r\n  box-shadow: 0 0 0 6px rgba(245, 108, 108, 0.25);\r\n}\r\n\r\n.scene-card:hover .status-indicator.running {\r\n  box-shadow: 0 0 0 6px rgba(64, 158, 255, 0.25);\r\n}\r\n\r\n.scene-card:hover .status-indicator.not-configured {\r\n  box-shadow: 0 0 0 6px rgba(144, 147, 153, 0.25);\r\n}\r\n\r\n.scene-card-content {\r\n  flex: 1;\r\n  padding: 18px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f9fbfd 100%);\r\n}\r\n\r\n.scene-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.scene-card-header h4 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #1f2329;\r\n  line-height: 24px;\r\n  max-width: 85%;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  position: relative;\r\n  padding-bottom: 6px;\r\n}\r\n\r\n.scene-card-header h4:after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 30px;\r\n  height: 2px;\r\n  background-color: #409EFF;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.scene-card:hover .scene-card-header h4:after {\r\n  width: 100%;\r\n}\r\n\r\n.scene-more {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 28px;\r\n  height: 28px;\r\n  border-radius: 6px;\r\n  color: #606266;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  background-color: transparent;\r\n}\r\n\r\n.scene-more:hover {\r\n  background-color: rgba(64, 158, 255, 0.1);\r\n  color: #409EFF;\r\n}\r\n\r\n.scene-card-description {\r\n  flex: 1;\r\n  margin-bottom: 12px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 3; /* 限制最多显示3行 */\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n  line-height: 1.5;\r\n  position: relative;\r\n  background: linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 75%, rgba(249,251,253,1) 100%);\r\n  padding-bottom: 5px;\r\n  max-height: 63px; /* 设置最大高度，约等于3行文字高度 */\r\n}\r\n\r\n.scene-card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-size: 12px;\r\n  color: #909399;\r\n  padding-top: 8px;\r\n  border-top: 1px dashed rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.scene-steps {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  background-color: rgba(64, 158, 255, 0.05);\r\n  padding: 3px 8px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.scene-card:hover .scene-steps {\r\n  background-color: rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.scene-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.scene-number {\r\n  font-size: 12px;\r\n  color: #409EFF;\r\n  padding: 3px 10px;\r\n  border-radius: 12px;\r\n  background-color: rgba(64, 158, 255, 0.1);\r\n  font-weight: 500;\r\n  letter-spacing: 0.5px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.scene-card:hover .scene-number {\r\n  background-color: rgba(64, 158, 255, 0.2);\r\n  box-shadow: 0 2px 5px rgba(64, 158, 255, 0.15);\r\n}\r\n\r\n.delete-scene-btn {\r\n  opacity: 0;\r\n  transform: scale(0.8);\r\n  transition: all 0.3s ease;\r\n  border: none;\r\n  padding: 4px;\r\n  background: rgba(245, 108, 108, 0.1);\r\n  color: #F56C6C;\r\n}\r\n\r\n.scene-card:hover .delete-scene-btn {\r\n  opacity: 1;\r\n  transform: scale(1);\r\n}\r\n\r\n.delete-scene-btn:hover {\r\n  background: rgba(245, 108, 108, 0.2);\r\n  color: #F56C6C;\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 右侧数据区域 */\r\n.data-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n  overflow-y: visible; /* 避免整体滚动 */\r\n  padding-right: 8px;\r\n  height: auto; /* 自适应高度 */\r\n  flex: 1; /* 占用可用空间 */\r\n}\r\n\r\n.data-overview {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 16px;\r\n}\r\n\r\n.data-card {\r\n  background: linear-gradient(135deg, #fff 0%, #f9fbfd 100%);\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  text-align: center;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 120px;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.data-card:before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 4px;\r\n  top: 0;\r\n  left: 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.pass-rate:before {\r\n  background: linear-gradient(90deg, #67C23A, #95D475);\r\n}\r\n\r\n.total-runs:before {\r\n  background: linear-gradient(90deg, #409EFF, #79bbff);\r\n}\r\n\r\n.success-runs:before {\r\n  background: linear-gradient(90deg, #67C23A, #95D475);\r\n}\r\n\r\n.data-value {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.data-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  position: relative;\r\n  padding-bottom: 4px;\r\n}\r\n\r\n.data-label:after {\r\n  content: '';\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 2px;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.pass-rate .data-label:after {\r\n  background-color: #67C23A;\r\n}\r\n\r\n.total-runs .data-label:after {\r\n  background-color: #409EFF;\r\n}\r\n\r\n.success-runs .data-label:after {\r\n  background-color: #67C23A;\r\n}\r\n\r\n.data-card:hover .data-label:after {\r\n  width: 40px;\r\n}\r\n\r\n.chart-section {\r\n  background: linear-gradient(135deg, #fff 0%, #f9fbfd 100%);\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 350px; /* 保持固定高度 */\r\n  flex-shrink: 0; /* 防止被压缩 */\r\n}\r\n\r\n.chart-section:hover {\r\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.chart-section:before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 4px;\r\n  top: 0;\r\n  left: 0;\r\n  background: linear-gradient(90deg, #409EFF, #79bbff);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.chart-section:hover:before {\r\n  opacity: 1;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px dashed rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #1f2329;\r\n  position: relative;\r\n  padding-left: 12px;\r\n}\r\n\r\n.section-header h3:before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 3px;\r\n  height: 14px;\r\n  background: linear-gradient(to bottom, #409EFF, #53a8ff);\r\n  border-radius: 2px;\r\n}\r\n\r\n.chart-legend {\r\n  display: flex;\r\n  gap: 12px;\r\n  background-color: rgba(64, 158, 255, 0.05);\r\n  padding: 4px 12px;\r\n  border-radius: 20px;\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n\r\n.legend-color {\r\n  width: 14px;\r\n  height: 6px;\r\n  border-radius: 3px;\r\n}\r\n\r\n.legend-color.success {\r\n  background: linear-gradient(90deg, #67C23A, #95D475);\r\n}\r\n\r\n.chart-container-wrapper {\r\n  height: 220px;\r\n  margin-top: 12px;\r\n  padding: 8px;\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n  border-radius: 8px;\r\n  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.02);\r\n}\r\n\r\n.chart-container {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.records-section {\r\n  background: linear-gradient(135deg, #fff 0%, #f9fbfd 100%);\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1; /* 占用剩余空间 */\r\n}\r\n\r\n.records-container {\r\n  flex: 1;\r\n  overflow: auto; /* 允许所有方向滚动 */\r\n  position: relative;\r\n  /* 增加内边距防止内容贴边 */\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.records-table {\r\n  margin-top: 12px;\r\n  border-radius: 8px;\r\n  width: 100%;\r\n  min-width: 100%; /* 确保表格至少占满容器宽度 */\r\n}\r\n\r\n/* 表格容器样式优化 */\r\n.el-table {\r\n  --el-table-header-background-color: rgba(64, 158, 255, 0.05);\r\n  --el-table-border-color: rgba(0, 0, 0, 0.03);\r\n  overflow: visible !important; /* 允许表格内容溢出 */\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table::before {\r\n  display: none;\r\n}\r\n\r\n.el-table th.el-table__cell {\r\n  background-color: rgba(64, 158, 255, 0.05);\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table .el-table__row:hover > td.el-table__cell {\r\n  background-color: rgba(64, 158, 255, 0.05);\r\n}\r\n\r\n/* 确保表格内容可以横向滚动 */\r\n.records-container .el-table__body-wrapper {\r\n  overflow-x: auto !important; /* 强制横向滚动 */\r\n}\r\n\r\n/* 状态和时间单元格样式 */\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  justify-content: flex-start;\r\n  color: #606266;\r\n}\r\n\r\n.status-cell {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.status-badge {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  padding: 3px 10px;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.status-badge.running {\r\n  color: #E6A23C;\r\n  background-color: rgba(230, 162, 60, 0.15);\r\n}\r\n\r\n.status-badge.completed {\r\n  color: #67C23A;\r\n  background-color: rgba(103, 194, 58, 0.15);\r\n}\r\n\r\n.status-indicator-dot {\r\n  width: 6px;\r\n  height: 6px;\r\n  border-radius: 50%;\r\n  background-color: currentColor;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    opacity: 0.5;\r\n    transform: scale(1.1);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.progress-wrapper {\r\n  padding: 0 8px;\r\n}\r\n\r\n.progress-counts {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  margin-bottom: 4px;\r\n  font-size: 12px;\r\n  gap: 4px;\r\n  align-items: baseline;\r\n}\r\n\r\n.success-count {\r\n  color: #67C23A;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n}\r\n\r\n.total-count {\r\n  color: #909399;\r\n}\r\n\r\n.progress-percentage {\r\n  color: #606266;\r\n  margin-left: auto;\r\n}\r\n\r\n.progress-pending {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #909399;\r\n}\r\n\r\n/* 对话框样式 */\r\n.plan-dialog :deep(.el-dialog__body) {\r\n  padding-top: 0;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media screen and (max-width: 1400px) {\r\n  .content-layout {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .data-container {\r\n    flex-direction: row;\r\n    flex-wrap: wrap;\r\n    overflow-y: visible;\r\n    overflow-x: hidden;\r\n  }\r\n  \r\n  .data-overview {\r\n    flex-basis: 100%;\r\n  }\r\n  \r\n  .chart-section {\r\n    flex-basis: calc(50% - 10px);\r\n    height: 350px; /* 保持固定高度 */\r\n  }\r\n  \r\n  .records-section {\r\n    flex-basis: calc(50% - 10px);\r\n    min-height: 350px;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .dashboard-main {\r\n    flex-direction: column;\r\n    overflow-y: auto; /* 确保主内容区域可滚动 */\r\n  }\r\n  \r\n  .plans-sidebar {\r\n    width: 100%;\r\n    height: auto;\r\n    max-height: 300px;\r\n    overflow-y: auto; /* 确保侧边栏可滚动 */\r\n  }\r\n  \r\n  .plans-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 8px;\r\n    max-height: none; /* 移除最大高度限制 */\r\n    overflow-y: visible; /* 不再需要单独滚动 */\r\n  }\r\n  \r\n  .plan-actions {\r\n    display: flex;\r\n  }\r\n  \r\n  .chart-section {\r\n    height: 350px; /* 保持固定高度 */\r\n  }\r\n  \r\n  .records-section {\r\n    min-height: 350px;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 768px) {\r\n  .dashboard-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .dashboard-actions {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n  \r\n  .scenes-grid {\r\n    grid-template-columns: 1fr;\r\n    max-height: none; /* 移除最大高度限制 */\r\n  }\r\n  \r\n  .data-overview {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .data-container {\r\n    flex-direction: column;\r\n    overflow-y: visible;\r\n  }\r\n  \r\n  .chart-section {\r\n    flex-basis: 100%;\r\n    height: 350px; /* 保持固定高度 */\r\n  }\r\n  \r\n  .records-section {\r\n    flex-basis: 100%;\r\n    min-height: 350px;\r\n  }\r\n}\r\n\r\n/* 按钮图标与文字对齐修复 */\r\n.el-button {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.el-button .el-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 添加场景按钮和新建计划按钮样式 */\r\n.add-scene-btn, .add-plan-btn {\r\n  padding: 8px 14px;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-scene-btn:hover, .add-plan-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.15);\r\n}\r\n\r\n/* 表格容器样式优化 */\r\n.el-table {\r\n  --el-table-header-background-color: rgba(64, 158, 255, 0.05);\r\n  --el-table-border-color: rgba(0, 0, 0, 0.03);\r\n  overflow: hidden;\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table::before {\r\n  display: none;\r\n}\r\n\r\n.el-table th.el-table__cell {\r\n  background-color: rgba(64, 158, 255, 0.05);\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table .el-table__row:hover > td.el-table__cell {\r\n  background-color: rgba(64, 158, 255, 0.05);\r\n}\r\n\r\n/* 状态徽章增强 */\r\n.status-badge {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  padding: 3px 10px;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.status-badge.running {\r\n  color: #E6A23C;\r\n  background-color: rgba(230, 162, 60, 0.15);\r\n}\r\n\r\n.status-badge.completed {\r\n  color: #67C23A;\r\n  background-color: rgba(103, 194, 58, 0.15);\r\n}\r\n\r\n/* 修复进度条样式 */\r\n.el-progress-bar__outer {\r\n  border-radius: 4px;\r\n  background-color: rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.el-progress-bar__inner {\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 修正按钮图标对齐 */\r\n.run-button, .add-plan-btn, .add-scene-btn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n}\r\n\r\n.run-button .el-icon,\r\n.add-plan-btn .el-icon,\r\n.add-scene-btn .el-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 确保表格可以水平滚动的样式 */\r\n:deep(.el-table__body-wrapper) {\r\n  overflow-x: auto !important;\r\n}\r\n\r\n:deep(.el-table) {\r\n  width: 100%;\r\n  overflow: visible !important;\r\n}\r\n\r\n:deep(.el-table__inner-wrapper) {\r\n  overflow: visible !important;\r\n}\r\n</style>\r\n", "<template>\r\n  <el-dialog v-model=\"sceneDlg\" title=\"添加场景\" width=\"75%\" :before-close=\"clickClear\" top=\"0\">\r\n  <div style=\"margin-left: 20px\">\r\n    <!--    顶部功能-->\r\n    <div >\r\n      <el-input style=\"width: 330px\" v-model=\"filterText\" placeholder=\"请输入用例名称进行搜索\" clearable>\r\n        <template #append>\r\n          <el-button type=\"primary\" @click=\"searchClick\">查询</el-button>\r\n        </template>\r\n      </el-input>\r\n      <span>\r\n        <el-button\r\n          type=\"warning\"\r\n          style=\"float: right;margin-right: 19px\"\r\n          @click=\"clickClear\"\r\n          :icon=\"Close\"\r\n            >关闭窗口\r\n        </el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          style=\"float: right;margin-right: 15px\"\r\n          @click=\"addScentToPlan\"\r\n          :icon=\"Check\"\r\n            >确认选择\r\n        </el-button>\r\n      </span>\r\n    </div>\r\n    <!--    表格功能-->\r\n    <el-scrollbar height=\"calc(100vh - 110px)\">\r\n    <div style=\"margin-top: 15px;margin-right: 20px\">\r\n      <el-table :data=\"caseList\"  stripe empty-text=\"暂无数据\" border @selection-change=\"handleSelectionChange\">\r\n            <el-table-column type=\"selection\" align=\"center\" width=\"50\"></el-table-column>\r\n            <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n              <template #default=\"scope\">\r\n                <span>{{ scope.$index + 1 }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"用例名称\" align=\"center\" >\r\n              <template #default=\"scope\">\r\n                <router-link class=\"no-underline\" :to=\"`/TestCaseDetail/`\" style=\"color: #409eff\" @click=\"clickEdit(scope.row)\">{{ scope.row.name }}</router-link>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"所属项目\" prop=\"project.name\"  align=\"center\" />\r\n            <el-table-column label=\"步骤数\" width=\"70\" prop=\"stepCount\" align=\"center\"/>\r\n            <el-table-column label=\"用例描述\" prop=\"desc\" align=\"center\" >\r\n              <template #default=\"scope\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" :content=\"scope.row.desc\" placement=\"top\">\r\n                <div v-if=\"scope.row.desc.length >16\" >{{ scope.row.desc.slice(0, 16) }}...</div>\r\n                <div v-else>{{ scope.row.desc }}</div>\r\n              </el-tooltip>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"创建人\" prop=\"creator\" align=\"center\"  width=\"120\"/>\r\n            <el-table-column label=\"创建时间\" align=\"center\" width=\"160\">\r\n              <template #default=\"scope\">\r\n                {{ $tools.rTime(scope.row.create_time) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"更新人\" prop=\"modifier\" align=\"center\" width=\"120\"/>\r\n            <el-table-column label=\"更新时间\" align=\"center\" width=\"160\">\r\n              <template #default=\"scope\">\r\n                <a v-if=\"scope.row.update_time\">{{$tools.rTime(scope.row.update_time)}}</a>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"330\" align=\"center\">\r\n              <template #default=\"scope\">\r\n                <el-button @click=\"runCase(scope.row)\" :size=\"'small'\" type=\"primary\" :icon=\"Promotion\">运行</el-button>\r\n                <el-button @click=\"clickEdit(scope.row)\" :size=\"'small'\" type=\"warning\" :icon=\"Menu\">添加管理步骤</el-button>\r\n                <el-button @click=\"delCase(scope.row.id)\" :size=\"'small'\" type=\"danger\" plain :icon=\"Delete\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n    </el-table>\r\n    </div>\r\n    <div class=\"pagination-container\">\r\n      <el-pagination  background layout=\"total, prev, pager, next, jumper\"\r\n                    @current-change=\"currentPages\"\r\n                    :default-page-size=\"100\"\r\n                    :total=\"pages.count\"\r\n                    :current-page=\"pages.current\"\r\n                   next-text=\"下一页\" prev-text=\"上一页\">\r\n      </el-pagination>\r\n    </div>\r\n    </el-scrollbar>\r\n  </div>\r\n  </el-dialog>\r\n  <!-- 显示运行结果 -->\r\n  <el-drawer v-model=\"ResultDlg\" :with-header=\"false\" size=\"50%\">\r\n\t\t<div style=\"padding:20px;\">\r\n\t\t\t<el-descriptions title=\"执行结果\" border :column=\"4\" style=\"text-align: center;\">\r\n\t\t\t\t<el-descriptions-item label=\"总数\" ><b style=\"color: #00aaff\">{{ runScentResult.all }}</b></el-descriptions-item>\r\n\t\t\t\t<el-descriptions-item label=\"通过\"><b style=\"color: #00aa7f\">{{ runScentResult.success }}</b></el-descriptions-item>\r\n\t\t\t\t<el-descriptions-item label=\"失败\"><b style=\"color: orangered\">{{ runScentResult.fail }}</b></el-descriptions-item>\r\n\t\t\t\t<el-descriptions-item label=\"错误\"><b style=\"color: #fca130\">{{ runScentResult.error }}</b></el-descriptions-item>\r\n\t\t\t</el-descriptions>\r\n\t\t\t<div style=\"height: 40px;line-height: 40px;\"><b>执行详情</b></div>\r\n\t\t\t<el-scrollbar height=\"calc(100vh - 180px)\">\r\n\t\t\t\t<el-table :data=\"runScentResult.cases\" style=\"width: 100%\" empty-text=\"暂无数据\">\r\n\t\t\t\t\t<el-table-column type=\"expand\">\r\n\t\t\t\t\t\t<template #default=\"props\">\r\n\t\t\t\t\t\t\t<caseResult :result=\"props.row\"></caseResult>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t<el-table-column label=\"步骤名\" prop=\"name\" />\r\n\t\t\t\t\t<el-table-column label=\"请求方法\" prop=\"method\">\r\n            <template #default=\"props\">\r\n               <span v-if=\"props.row.type === 'api'\">{{ props.row.method }}</span>\r\n\t\t\t\t\t\t</template>\r\n          </el-table-column>\r\n\t\t\t\t\t<el-table-column label=\"响应状态码\" prop=\"status_cede\">\r\n            <template #default=\"props\">\r\n               <span v-if=\"props.row.type === 'api'\">{{ props.row.status_cede }}</span>\r\n\t\t\t\t\t\t</template>\r\n          </el-table-column>\r\n\t\t\t\t\t<el-table-column label=\"执行结果\" prop=\"state\" min-width=\"40px\">\r\n\t\t\t\t\t\t<template #default=\"props\">\r\n\t\t\t\t\t\t\t<span v-if=\"props.row.state == '成功'\" style=\"color: #00AA7F;\">{{ props.row.state }}</span>\r\n              <span v-else-if=\"props.row.state == '错误'\" style=\"color: #fca130;\">{{ props.row.state }}</span>\r\n\t\t\t\t\t\t\t<span v-else style=\"color:#F56C6C\">{{ props.row.state }}</span>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</el-table-column>\r\n\t\t\t\t</el-table>\r\n\t\t\t</el-scrollbar>\r\n\t\t</div>\r\n\t</el-drawer>\r\n</template>\r\n\r\n<script>\r\nimport {mapMutations, mapState} from \"vuex\";\r\nimport caseResult from '../../components/common/caseResult.vue';\r\nimport { ElNotification, ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { Check, Close, Delete, Menu, Promotion } from '@element-plus/icons-vue';\r\n\r\nexport default {\r\n  props: {\r\n    planId: String\r\n  },\r\n  computed: {\r\n    ...mapState(['pro','envId']),\r\n  },\r\n  components:{\r\n    caseResult\r\n  },\r\n  data(){\r\n    return {\r\n      filterText:'',\r\n      pages: [],\r\n      caseList: [],\r\n      ResultDlg:false,\r\n      runScentResult:'',\r\n      sceneDlg:true,\r\n      selectedRows: [],\r\n      Check,\r\n      Close,\r\n      Delete,\r\n      Menu,\r\n      Promotion\r\n    }\r\n  },\r\n methods: {\r\n    ...mapMutations(['CaseInfo']),\r\n    // 查询\r\n   searchClick() {\r\n     this.allTestCase(this.pro.id,this.pages.current,this.filterText)\r\n   },\r\n\r\n   // 点击编辑用例\r\n   clickEdit(id) {\r\n   this.$router.push({ name: 'TestCaseDetail' });\r\n   this.CaseInfo(id)\r\n   },\r\n\r\n   // 用例列表\r\n   async allTestCase(project,page,name) {\r\n     const response =await this.$api.getTestCase(project,page,name)\r\n     if (response.status ===200){\r\n        // 将获取到的测试用例数据存储在 caseList 中\r\n\t\t\t\tconst allCase = response.data.result;\r\n\t\t\t\tthis.pages = response.data\r\n        // 调用接口获取已存在于计划中的场景列表\r\n        const existingScenesResponse  = await this.$api.getTestCase_({\r\n\t\t\t\ttestplan: this.planId\r\n\t\t\t});\r\n        if (existingScenesResponse.status === 200) {\r\n              const existingScenes = existingScenesResponse.data.result;\r\n              // 过滤掉已存在于计划中的场景\r\n              this.caseList = allCase.filter(item => !existingScenes.map(scene => scene.id).includes(item.id));\r\n              // 过滤掉 stepCount 为 0 的数据\r\n              this.caseList = this.caseList.filter(item => item.stepCount !== 0);\r\n              this.pages.count =  this.caseList.length\r\n        }\r\n\t\t\t}\r\n   },\r\n\r\n   currentPages(currentPage) {\r\n      this.allTestCase(this.pro.id,currentPage)\r\n      this.pages.current = currentPage\r\n\r\n  },\r\n\r\n   // 点击删除\r\n   delCase(id) {\r\n    ElMessageBox.confirm('此操作将永久删除该用例, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(async () => {\r\n        const response = await this.$api.delTestCase(id)\r\n        if(response.status ===204){\r\n          ElMessage({\r\n            type: 'success',\r\n            message: '删除成功!'\r\n          });\r\n          // 刷新页面\r\n          this.allTestCase(this.pro.id);\r\n          this.filterText = ''\r\n        }\r\n      })\r\n      .catch(() => {\r\n        ElMessage({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        });\r\n      });\r\n  },\r\n\r\n  // 运行测试用例\r\n  async runCase(data) {\r\n      const stepCount = parseInt(data.stepCount);\r\n      console.log(stepCount)\r\n      if(stepCount > 0){\r\n\t\t\tif (this.envId) {\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\tenv: this.envId,\r\n\t\t\t\t\tscene: data.id\r\n\t\t\t\t};\r\n          ElNotification({\r\n            title: '开始运行',\r\n            message: '运行过程中请稍等片刻噢',\r\n            type: 'success',\r\n            duration:1000\r\n          });\r\n\t\t\t\tconst response = await this.$api.runCases(data.id, params);\r\n\t\t\t\tif (response.status == 200) {\r\n\t\t\t\t\t// 显示执行结果到窗口页面\r\n\t\t\t\t\tthis.runScentResult = response.data;\r\n\t\t\t\t\tthis.ResultDlg = true;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tElMessage({\r\n\t\t\t\t\ttype: 'warning',\r\n\t\t\t\t\tmessage: '当前未选中执行环境!',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}else ElMessage({\r\n\t\t\t\t\ttype: 'warning',\r\n\t\t\t\t\tmessage: '请添加步骤后再运行',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\t\t  },\r\n\r\n  closeModal() {\r\n    this.$emit('close-modal');\r\n  },\r\n  // 点击取消\r\n  clickClear(){\r\n    this.closeModal()\r\n  },\r\n  // 添加选中的测试场景到测试计划中\r\n  async addScentToPlan(){\r\n      if (this.selectedRows.length === 0) {\r\n        ElMessage({\r\n          type: 'warning',\r\n          message: '请勾选要添加的测试场景',\r\n          duration: 1000\r\n        });\r\n      return; // 如果没有勾选场景，则不执行后续操作\r\n      }\r\n\t\t\tlet params = {}\r\n      params.scene_ids = [...this.selectedRows]\r\n\t\t\tconst response = await this.$api.createTestPlanScene(this.planId,params)\r\n\t\t\tif(response.status===200){\r\n\t\t\t\tElMessage({\r\n\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\tmessage: '添加成功',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\t\t\t}\r\n      this.closeModal()\r\n\t\t},\r\n\r\n\r\n  handleSelectionChange(val) {\r\n      this.selectedRows = val.map(row => row.id);\r\n      console.log(this.selectedRows)\r\n    },\r\n\r\n },\r\n created() {\r\n  this.allTestCase(this.pro.id);\r\n},\r\n\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n.no-underline {\r\n  text-decoration: none;\r\n}\r\n</style>\r\n", "import { render } from \"./TestCaseDlg.vue?vue&type=template&id=2d1b3e38&scoped=true\"\nimport script from \"./TestCaseDlg.vue?vue&type=script&lang=js\"\nexport * from \"./TestCaseDlg.vue?vue&type=script&lang=js\"\n\nimport \"./TestCaseDlg.vue?vue&type=style&index=0&id=2d1b3e38&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-2d1b3e38\"]])\n\nexport default __exports__", "import { render } from \"./TestPlanNew.vue?vue&type=template&id=32196f23&scoped=true\"\nimport script from \"./TestPlanNew.vue?vue&type=script&lang=js\"\nexport * from \"./TestPlanNew.vue?vue&type=script&lang=js\"\n\nimport \"./TestPlanNew.vue?vue&type=style&index=0&id=32196f23&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-32196f23\"]])\n\nexport default __exports__"], "names": ["class", "style", "_createVNode", "_component_el_tabs", "type", "value", "size", "$props", "result", "_createBlock", "_component_el_tab_pane", "label", "name", "response_header", "_createElementBlock", "_hoisted_1", "includes", "_hoisted_2", "_component_Editor", "readOnly", "response_body", "$event", "lang", "theme", "_hoisted_3", "_component_el_scrollbar", "height", "onWheel", "_cache", "_withModifiers", "innerHTML", "_hoisted_4", "_Fragment", "_renderList", "key", "_component_el_tag", "_createElementVNode", "_hoisted_5", "_toDisplayString", "requests_body", "_hoisted_6", "_component_el_collapse", "$data", "activeNames", "_component_el_collapse_item", "title", "_withCtx", "method", "url", "requests_header", "_hoisted_7", "log_data", "item", "index", "_hoisted_8", "disabled", "state", "_hoisted_9", "_hoisted_10", "_hoisted_11", "status_cede", "_hoisted_12", "_hoisted_13", "run_time", "showbtn", "_hoisted_14", "_component_el_button", "onClick", "$options", "getInterfaces", "plain", "_component_el_dialog", "addBugDlg", "width", "closeDialogResult", "footer", "_hoisted_15", "saveBug", "_component_el_form", "model", "bugForm", "_component_el_form_item", "_component_el_select", "interface", "placeholder", "interfaces", "iter", "_component_el_option", "id", "_component_el_input", "autosize", "minRows", "maxRows", "desc", "autocomplete", "props", "default", "computed", "mapState", "components", "Editor", "data", "info", "status", "methods", "this", "project", "pro", "response", "$api", "createBugs", "$message", "message", "duration", "getNewInterfaces", "__exports__", "render", "ref", "filterText", "clearable", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handletreeClick", "append", "_component_el_icon", "_component_Search", "scene_list", "length", "planId", "runPlan", "addPlan", "planList", "_component_el_empty", "description", "_normalizeClass", "active", "selectPlan", "_component_Folder", "_component_el_tooltip", "content", "placement", "editPlan", "_component_Edit", "delPlan", "_component_Delete", "currentPlanName", "_hoisted_16", "_hoisted_17", "clickAddScene", "_hoisted_18", "clickView", "_hoisted_20", "getSceneStatusClass", "getSceneStatusText", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_component_Document", "stepCount", "_hoisted_27", "_hoisted_28", "circle", "delScene", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "getAveragePassRate", "_hoisted_33", "_hoisted_34", "records", "_hoisted_35", "_hoisted_36", "getSuccessfulRuns", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_component_el_table", "stripe", "_component_el_table_column", "scope", "_hoisted_43", "_component_Timer", "_ctx", "$tools", "rTime", "row", "create_time", "effect", "env_name", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_component_Check", "_hoisted_47", "_hoisted_48", "_hoisted_49", "success", "_hoisted_50", "all", "_hoisted_51", "pass_rate", "_component_el_progress", "percentage", "parseFloat", "color", "getProgressColor", "_hoisted_52", "_component_Loading", "fixed", "link", "$router", "push", "params", "loading", "editDlg", "planForm", "clickClear", "top", "_hoisted_53", "savePlan", "prop", "rules", "required", "trigger", "autofocus", "sceneDlg", "_component_TestCase", "onCloseModal", "handleCloseModal", "searchClick", "icon", "Close", "addScentToPlan", "Check", "caseList", "border", "onSelectionChange", "handleSelectionChange", "align", "$index", "_component_router_link", "to", "clickEdit", "slice", "update_time", "runCase", "Promotion", "<PERSON><PERSON>", "delCase", "Delete", "_component_el_pagination", "background", "layout", "onCurrentChange", "currentPages", "total", "pages", "count", "current", "_component_el_drawer", "ResultDlg", "_component_el_descriptions", "column", "_component_el_descriptions_item", "runScentResult", "fail", "error", "cases", "_component_caseResult", "String", "caseResult", "selectedRows", "mapMutations", "allTestCase", "CaseInfo", "page", "getTestCase", "allCase", "existingScenesResponse", "getTestCase_", "testplan", "existingScenes", "filter", "map", "scene", "currentPage", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "async", "delTestCase", "ElMessage", "catch", "parseInt", "console", "log", "envId", "env", "ElNotification", "runCases", "closeModal", "$emit", "scene_ids", "createTestPlanScene", "val", "created", "Icon", "TestCase", "themeColors", "warning", "danger", "defaultProps", "children", "chartData", "runDate", "passRate", "toFixed", "reverse", "getAllPlan", "getTestPlans", "find", "plan", "updateCurrentPlanName", "currentPlan", "getAllRecord", "getTestRecord", "getScenes", "handleNodeClick", "updateTestPlan", "createTestPlan", "types", "deleteTestPlan", "back_type", "handleSceneCommand", "command", "scene_id", "deleteTestPlanScene", "completedRecords", "record", "totalPassRate", "reduce", "sum", "rate", "lastResult", "watch", "$nextTick", "$refs", "chartTable", "$chart", "chart3"], "sourceRoot": ""}