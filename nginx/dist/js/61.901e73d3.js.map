{"version": 3, "file": "js/61.901e73d3.js", "mappings": "yOAiBUA,MAAM,e,GAGPC,MAAA,mB,aAsCAD,MAAM,e,SAM6BC,MAAA,mB,SAOLA,MAAA,mB,SACKA,MAAA,mB,SAC3BA,MAAA,mB,SAK0BA,MAAA,mB,SAC1BA,MAAA,mB,SASRA,MAAA,0D,GAcID,MAAM,iB,0ZArGhBE,EAAAA,EAAAA,IAsFQC,EAAA,CAtFC,cAAY,KAAKF,MAAA,uBAA2BG,KAAK,cAAcC,MAAM,KAAKC,KAAK,Q,kBACzF,IAYgB,CAZkB,OAAfC,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IAYgBC,EAAA,C,MAZyBC,MAAM,MAAMC,KAAK,M,kBACtD,IAUM,CAVKL,EAAAC,OAAOK,kB,WAAlBC,EAAAA,EAAAA,IAUM,MAAAC,EAAA,CATOR,EAAAC,OAAOK,gBAAgB,gBAAgBG,SAAS,sB,WAA3DF,EAAAA,EAAAA,IAGM,MAAAG,EAAA,EADJf,EAAAA,EAAAA,IAA4FgB,EAAA,CAAnFC,UAAU,E,WAAeZ,EAAAC,OAAOY,c,qCAAPb,EAAAC,OAAOY,cAAaC,GAAEC,KAAK,OAAOC,MAAM,U,uCAE5ET,EAAAA,EAAAA,IAIM,MAAAU,EAAA,EAHJtB,EAAAA,EAAAA,IAEeuB,EAAA,CAFDC,OAAO,QAAUC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAA0G,EAA1G3B,EAAAA,EAAAA,IAA0GgB,EAAA,CAAjGC,UAAU,EAAMW,UAAQvB,EAAAC,OAAOY,cAAeE,KAAK,OAAOC,MAAM,SAASG,OAAO,S,6EAKjE,OAAfnB,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IAWcC,EAAA,C,MAX2BC,MAAM,MAAMC,KAAK,M,kBACtD,IASe,EATfV,EAAAA,EAAAA,IASeuB,EAAA,CATDC,OAAO,QAASC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAOI,CAP2BtB,EAAAC,OAAOK,kB,WAAtCC,EAAAA,EAAAA,IAOI,MAPJiB,EAOI,G,aANLjB,EAAAA,EAAAA,IAKMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IALsB1B,EAAAC,OAAOK,gBAAe,CAArCR,EAAO6B,M,WAApBpB,EAAAA,EAAAA,IAKM,aAJLZ,EAAAA,EAAAA,IAGSiC,EAAA,CAHDlC,MAAA,qBAAyBG,KAAK,Q,kBACrC,IAAgD,EAAhDgC,EAAAA,EAAAA,IAAgD,IAAhDC,GAAgDC,EAAAA,EAAAA,IAAlBJ,EAAM,OAAH,IACjCE,EAAAA,EAAAA,IAAwB,aAAAE,EAAAA,EAAAA,IAAfjC,GAAK,K,yEAMgB,OAAfE,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IA4BcC,EAAA,C,MA5B2BC,MAAM,OAAOC,KAAK,M,kBACvD,IA0Be,EA1BfV,EAAAA,EAAAA,IA0BeuB,EAAA,CA1BDC,OAAO,QAASC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAwBI,CAxBOtB,EAAAC,OAAO+B,gB,WAAlBzB,EAAAA,EAAAA,IAwBI,MAAA0B,EAAA,EAvBLtC,EAAAA,EAAAA,IAsBcuC,EAAA,C,WAtBQC,EAAAC,Y,qCAAAD,EAAAC,YAAWtB,GAAErB,MAAM,e,kBACxC,IAMmB,EANnBE,EAAAA,EAAAA,IAMmB0C,EAAA,CANDhC,KAAK,KAAG,CACdiC,OAAKC,EAAAA,EAAAA,IACf,IAAclB,EAAA,KAAAA,EAAA,KAAdQ,EAAAA,EAAAA,IAAc,SAAX,WAAO,M,iBAEX,IAA+C,EAA/CA,EAAAA,EAAAA,IAA+C,WAA1C,qBAAiBE,EAAAA,EAAAA,IAAG/B,EAAAC,OAAOuC,QAAM,IACtCX,EAAAA,EAAAA,IAAyC,WAApC,kBAAcE,EAAAA,EAAAA,IAAG/B,EAAAC,OAAOwC,KAAG,K,OAEjC9C,EAAAA,EAAAA,IAOmB0C,EAAA,CAPDhC,KAAK,KAAG,CACdiC,OAAKC,EAAAA,EAAAA,IACf,IAAsBlB,EAAA,MAAAA,EAAA,MAAtBQ,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEd,IAA8C,G,aAAnDtB,EAAAA,EAAAA,IAEMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFsB1B,EAAAC,OAAOyC,gBAAe,CAArC5C,EAAO6B,M,WAApBpB,EAAAA,EAAAA,IAEM,aADLsB,EAAAA,EAAAA,IAAsC,aAAAE,EAAAA,EAAAA,IAA7BJ,EAAM,MAAQ7B,GAAK,O,eAG9BH,EAAAA,EAAAA,IAKmB0C,EAAA,CALDhC,KAAK,KAAG,CACdiC,OAAKC,EAAAA,EAAAA,IACf,IAAsBlB,EAAA,MAAAA,EAAA,MAAtBQ,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEnB,IAAuC,EAAvCA,EAAAA,EAAAA,IAAuC,aAAAE,EAAAA,EAAAA,IAA9B/B,EAAAC,OAAO+B,eAAa,K,oFAMjCrC,EAAAA,EAAAA,IAYcQ,EAAA,CAZDC,MAAM,MAAI,C,iBACtB,IAUe,EAVfT,EAAAA,EAAAA,IAUeuB,EAAA,CAVDC,OAAO,QAASC,QAAKC,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAQM,EARNO,EAAAA,EAAAA,IAQM,MARNc,EAQM,G,aAPLpC,EAAAA,EAAAA,IAMMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANuB1B,EAAAC,OAAO2C,SAAQ,CAA/BC,EAAMC,M,WAAnBvC,EAAAA,EAAAA,IAMM,YAL8C,UAAZsC,EAAK,K,WAA5C3C,EAAAA,EAAAA,IAAmF0B,EAAA,C,MAA3ElC,MAAA,sB,kBAAqD,IAAa,E,iBAAVmD,EAAK,IAAD,K,YACZ,YAAZA,EAAK,K,WAAjD3C,EAAAA,EAAAA,IAAwG0B,EAAA,C,MAAhGlC,MAAA,qBAA2DG,KAAK,W,kBAAU,IAAa,E,iBAAVgD,EAAK,IAAD,K,YACjC,UAAZA,EAAK,K,WAAjD3C,EAAAA,EAAAA,IAAqG0B,EAAA,C,MAA7FlC,MAAA,qBAAyDG,KAAK,U,kBAAS,IAAa,E,iBAAVgD,EAAK,IAAD,K,YAC9B,SAAZA,EAAK,K,WAAjD3C,EAAAA,EAAAA,IAAqG0B,EAAA,C,MAA7FlC,MAAA,qBAAwDG,KAAK,W,kBAAU,IAAa,E,iBAAVgD,EAAK,IAAD,K,YAC1D,WAAZA,EAAK,K,WAArBtC,EAAAA,EAAAA,IAAiF,MAAjFwC,GAAiFhB,EAAAA,EAAAA,IAAhBc,EAAK,IAAD,K,4CAKzElD,EAAAA,EAAAA,IAMcQ,EAAA,CAND6C,SAAA,IAAQ,CACT5C,OAAKmC,EAAAA,EAAAA,IACf,IAAkG,CAArE,OAAjBvC,EAAAC,OAAOgD,Q,WAAnB1C,EAAAA,EAAAA,IAAkG,OAAlG2C,GAAkGnB,EAAAA,EAAAA,IAAA,YAAtB/B,EAAAC,OAAOgD,OAAK,IACtD,OAAjBjD,EAAAC,OAAOgD,Q,WAAxB1C,EAAAA,EAAAA,IAAuG,OAAvG4C,GAAuGpB,EAAAA,EAAAA,IAAA,YAAtB/B,EAAAC,OAAOgD,OAAK,M,WAC7F1C,EAAAA,EAAAA,IAA8D,OAA9D6C,GAA8DrB,EAAAA,EAAAA,IAAtB/B,EAAAC,OAAOgD,OAAK,M,MAGpB,OAAfjD,EAAAC,OAAOJ,O,WAA1BK,EAAAA,EAAAA,IAKcC,EAAA,C,MAL2B6C,SAAA,I,CAC7B5C,OAAKmC,EAAAA,EAAAA,IACf,IAA4G,CAAhGvC,EAAAC,OAAOoD,aAAe,M,WAAlC9C,EAAAA,EAAAA,IAA4G,OAA5G+C,GAA4GvB,EAAAA,EAAAA,IAAA,YAA5B/B,EAAAC,OAAOoD,aAAW,M,WAClG9C,EAAAA,EAAAA,IAAkF,OAAlFgD,GAAkFxB,EAAAA,EAAAA,IAAA,YAA5B/B,EAAAC,OAAOoD,aAAW,M,wBAG1E1D,EAAAA,EAAAA,IAIcQ,EAAA,CAJD6C,SAAA,IAAQ,CACT5C,OAAKmC,EAAAA,EAAAA,IACf,IAAiC,E,2BAAlBvC,EAAAC,OAAOuD,UAAQ,K,cAIuD,OAAjBxD,EAAAC,OAAOgD,OAAkBjD,EAAAyD,U,WAA7FlD,EAAAA,EAAAA,IAEM,MAFNmD,EAEM,EADJ/D,EAAAA,EAAAA,IAAqFgE,EAAA,CAAxEC,QAAOC,EAAAC,cAAejE,KAAK,UAAUkE,MAAA,GAAMhE,KAAK,Q,kBAAO,IAAKsB,EAAA,MAAAA,EAAA,M,QAAL,Y,gDAGtE1B,EAAAA,EAAAA,IAeYqE,EAAA,CAfD1B,MAAM,Q,WAAiBH,EAAA8B,U,qCAAA9B,EAAA8B,UAASnD,GAAEoD,MAAM,MAAO,eAAcL,EAAAM,mB,CAS3DC,QAAM7B,EAAAA,EAAAA,IACf,IAGM,EAHNV,EAAAA,EAAAA,IAGM,MAHNwC,EAGM,EAFJ1E,EAAAA,EAAAA,IAAqDgE,EAAA,CAAzCC,QAAOC,EAAAM,mBAAiB,C,iBAAE,IAAG9C,EAAA,MAAAA,EAAA,M,QAAH,U,6BACtC1B,EAAAA,EAAAA,IAA0DgE,EAAA,CAA/C9D,KAAK,UAAW+D,QAAOC,EAAAS,S,kBAAS,IAAGjD,EAAA,MAAAA,EAAA,M,QAAH,U,iDAX/C,IAOU,EAPV1B,EAAAA,EAAAA,IAOU4E,EAAA,CAPAC,MAAOrC,EAAAsC,SAAO,C,iBACtB,IAIe,EAJf9E,EAAAA,EAAAA,IAIe+E,EAAA,CAJDtE,MAAM,QAAM,C,iBACxB,IAEY,EAFZT,EAAAA,EAAAA,IAEYgF,EAAA,CAFD5E,KAAK,Q,WAAiBoC,EAAAsC,QAAQG,U,qCAARzC,EAAAsC,QAAQG,UAAS9D,GAAE+D,YAAY,WAAWnF,MAAA,gB,kBACT,IAA0B,G,aAA1Fa,EAAAA,EAAAA,IAAsHkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAAvCS,EAAA2C,WAARC,K,WAAvE7E,EAAAA,EAAAA,IAAsH8E,EAAA,CAA1G5E,MAAO2E,EAAK1E,KAAO,IAAM0E,EAAKtC,IAAM3C,MAAOiF,EAAKE,GAAgCtD,IAAKoD,EAAKE,I,oEAG1GtF,EAAAA,EAAAA,IAAiK+E,EAAA,CAAnJtE,MAAM,SAAO,C,iBAAC,IAAsH,EAAtHT,EAAAA,EAAAA,IAAsHuF,EAAA,CAA3GC,SAAU,CAAAC,QAAA,EAAAC,QAAA,G,WAAqClD,EAAAsC,QAAQa,K,qCAARnD,EAAAsC,QAAQa,KAAIxE,GAAEjB,KAAK,WAAW0F,aAAa,O,0HAczI,GACCC,MAAO,CACNvF,OAAQ,CACPwF,QAAS,CAAC,GAEXhC,QAAS,CACRgC,SAAS,IAGXC,SAAU,KACNC,EAAAA,EAAAA,IAAS,CAAC,SAEdC,WAAY,CACXC,OAAMA,EAAAA,GAEPC,IAAAA,GACC,MAAO,CACN1D,YAAa,CAAC,IAAK,IAAK,KAExB6B,WAAW,EAEXQ,QAAS,CACRG,UAAW,KACXU,KAAM,GACNS,KAAM,GACNC,OAAQ,OAENlB,WAAW,GAEhB,EACAmB,QAAS,CACR,aAAM3B,GACL4B,KAAKzB,QAAQ0B,QAAUD,KAAKE,IAAInB,GAChCiB,KAAKzB,QAAQsB,KAAOG,KAAKjG,OACzB,MAAMoG,QAAiBH,KAAKI,KAAKC,WAAWL,KAAKzB,SACzB,MAApB4B,EAASL,SACZE,KAAKM,SAAS,CACb3G,KAAM,UACN4G,QAAS,UACTC,SAAU,MAEXR,KAAKjC,WAAY,EACjBiC,KAAKzB,QAAU,CACdG,UAAW,KACXU,KAAM,GACNS,KAAM,GACNC,OAAQ,OAGX,EAEE7B,iBAAAA,GACE+B,KAAKjC,WAAY,EACjBiC,KAAKzB,QAAU,CAChBG,UAAW,KACXU,KAAM,GACNS,KAAM,GACNC,OAAQ,MAEP,EAGF,mBAAMlC,GACJ,MAAMuC,QAAiBH,KAAKI,KAAKK,mBACT,MAApBN,EAASL,SACXE,KAAKpB,WAAauB,EAASP,KAC3BI,KAAKjC,WAAY,EAErB,I,WChLJ,MAAM2C,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,O,6HCNOpH,MAAM,uB,GAEJA,MAAM,oB,GAIJA,MAAM,qB,GA2BRA,MAAM,kB,GAEJA,MAAM,iB,GACJA,MAAM,kB,GAmBJA,MAAM,c,iBAOFA,MAAM,a,GAEHA,MAAM,a,GAETA,MAAM,gB,GAuBdA,MAAM,gB,SAkBGA,MAAM,kB,GAEXA,MAAM,oB,GACJA,MAAM,kB,uBAeNA,MAAM,e,iBAMFA,MAAM,gB,qBAMNA,MAAM,sB,GACJA,MAAM,qB,GAGNA,MAAM,0B,GAGNA,MAAM,qB,GACJA,MAAM,e,GAINA,MAAM,iB,GACJA,MAAM,gB,GAkBlBA,MAAM,kB,GACJA,MAAM,iB,GACJA,MAAM,uB,GACJA,MAAM,c,GAGRA,MAAM,wB,GACJA,MAAM,c,GAGRA,MAAM,0B,GACJA,MAAM,c,GAMVA,MAAM,iB,GAUJA,MAAM,2B,GACJqH,IAAI,aAAarH,MAAM,mB,GAK3BA,MAAM,mB,GACJA,MAAM,kB,GAINA,MAAM,qB,GAWEA,MAAM,a,GAeNA,MAAM,e,SAC8BA,MAAM,wB,SAIjCA,MAAM,0B,SAWbA,MAAM,oB,GACJA,MAAM,mB,IACHA,MAAM,iB,IACNA,MAAM,e,IACNA,MAAM,uB,UAUXA,MAAM,oB,IAiDrBA,MAAM,iB,gkBAvTlBc,EAAAA,EAAAA,IAgUM,MAhUNC,EAgUM,EA9TJqB,EAAAA,EAAAA,IA4BM,MA5BNnB,EA4BM,C,aA3BJmB,EAAAA,EAAAA,IAEM,OAFDpC,MAAM,mBAAiB,EAC1BoC,EAAAA,EAAAA,IAAe,UAAX,Y,KAENA,EAAAA,EAAAA,IAuBM,MAvBNZ,EAuBM,EAtBJtB,EAAAA,EAAAA,IAaWuF,GAAA,C,WAZA/C,GAAA4E,W,qCAAA5E,GAAA4E,WAAUjG,GACnB+D,YAAY,OACZ,cAAY,iBACZmC,UAAA,GACAvH,MAAM,eACLwH,SAAKC,EAAAA,EAAAA,IAAQrD,GAAAsD,gBAAe,Y,CAElBC,QAAM7E,EAAAA,EAAAA,IACf,IAEY,EAFZ5C,EAAAA,EAAAA,IAEYgE,GAAA,CAFAC,QAAOC,GAAAsD,iBAAe,C,iBAChC,IAA6B,EAA7BxH,EAAAA,EAAAA,IAA6B0H,GAAA,M,iBAApB,IAAU,EAAV1H,EAAAA,EAAAA,IAAU2H,M,gEAIzB3H,EAAAA,EAAAA,IAOYgE,GAAA,CANV9D,KAAK,UACJmD,UAAWb,GAAAoF,YAAoC,IAAtBpF,GAAAoF,WAAWC,SAAiBrF,GAAAsF,OACrD7D,QAAOC,GAAA6D,QACRjI,MAAM,c,kBACP,IAED4B,EAAA,KAAAA,EAAA,K,QAFC,a,gEAOLd,EAAAA,EAAAA,IA+PM,MA/PNiB,EA+PM,EA7PJK,EAAAA,EAAAA,IAmDM,MAnDNC,EAmDM,EAlDJD,EAAAA,EAAAA,IAUM,MAVNI,EAUM,C,aATJJ,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVlC,EAAAA,EAAAA,IAOYgE,GAAA,CANV9D,KAAK,UACLE,KAAK,QACJ6D,QAAOC,GAAA8D,QACRlI,MAAM,gB,kBACP,IAED4B,EAAA,KAAAA,EAAA,K,QAFC,a,6BAMyB,IAApBc,GAAAyF,SAASJ,S,WADjBtH,EAAAA,EAAAA,IAMW2H,GAAA,C,MAJTC,YAAY,SACX,aAAY,K,kBAEb,IAA2D,EAA3DnI,EAAAA,EAAAA,IAA2DgE,GAAA,CAAhD9D,KAAK,UAAW+D,QAAOC,GAAA8D,S,kBAAS,IAAItG,EAAA,KAAAA,EAAA,K,QAAJ,W,qDAE3CQ,EAAAA,EAAAA,IA8BM,MA9BNc,EA8BM,G,aA7BJpC,EAAAA,EAAAA,IA4BMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA3BWS,GAAAyF,SAAR/E,K,WADTtC,EAAAA,EAAAA,IA4BM,OA1BHoB,IAAKkB,EAAKoC,GACVxF,OAAKsI,EAAAA,EAAAA,IAAA,cAAAC,OAA0B7F,GAAAsF,SAAW5E,EAAKoC,MAC/CrB,QAAK9C,GAAE+C,GAAAoE,WAAWpF,EAAKoC,K,EAExBpD,EAAAA,EAAAA,IAGM,MAHNqB,EAGM,EAFJvD,EAAAA,EAAAA,IAA6B0H,GAAA,M,iBAApB,IAAU,EAAV1H,EAAAA,EAAAA,IAAUuI,M,OACnBrG,EAAAA,EAAAA,IAA8C,OAA9CsB,GAA8CpB,EAAAA,EAAAA,IAAnBc,EAAKxC,MAAI,MAEtCwB,EAAAA,EAAAA,IAiBM,MAjBNuB,EAiBM,EAhBJzD,EAAAA,EAAAA,IAOawI,GAAA,CAPDC,QAAQ,KAAKC,UAAU,O,kBACjC,IAKU,EALV1I,EAAAA,EAAAA,IAKU0H,GAAA,CAJPzD,SAAKtC,EAAAA,EAAAA,IAAAR,GAAO+C,GAAAyE,SAASzF,GAAI,UAC1BpD,MAAM,e,kBAEN,IAAQ,EAARE,EAAAA,EAAAA,IAAQ4I,M,qCAGZ5I,EAAAA,EAAAA,IAOawI,GAAA,CAPDC,QAAQ,KAAKC,UAAU,O,kBACjC,IAKU,EALV1I,EAAAA,EAAAA,IAKU0H,GAAA,CAJPzD,SAAKtC,EAAAA,EAAAA,IAAAR,GAAO+C,GAAA2E,QAAQ3F,EAAKoC,IAAE,UAC5BxF,MAAM,2B,kBAEN,IAAU,EAAVE,EAAAA,EAAAA,IAAU8I,M,0DASxB5G,EAAAA,EAAAA,IAsMM,MAtMNyB,EAsMM,CAnMwB,IAApBnB,GAAAyF,SAASJ,S,WADjBtH,EAAAA,EAAAA,IAKW2H,GAAA,C,MAHTC,YAAY,kBACZrI,MAAM,gB,kBACN,IAA6D,EAA7DE,EAAAA,EAAAA,IAA6DgE,GAAA,CAAlD9D,KAAK,UAAW+D,QAAOC,GAAA8D,S,kBAAS,IAAMtG,EAAA,KAAAA,EAAA,K,QAAN,a,oCAK/Bc,GAAAoF,YAAoC,IAAtBpF,GAAAoF,WAAWC,S,WAOvCjH,EAAAA,EAAAA,IAmLM,MAnLNgD,EAmLM,EAjLJ1B,EAAAA,EAAAA,IAwDM,MAxDN6B,EAwDM,EAvDJ7B,EAAAA,EAAAA,IAaM,MAbNwC,EAaM,EAZJxC,EAAAA,EAAAA,IAGK,WAFSM,GAAAuG,kB,WAAZnI,EAAAA,EAAAA,IAAyD,OAAAoI,GAAA5G,EAAAA,EAAAA,IAAzBI,GAAAuG,iBAAe,M,WAC/CnI,EAAAA,EAAAA,IAAwB,OAAAqI,EAAX,YAEfjJ,EAAAA,EAAAA,IAOYgE,GAAA,CANV9D,KAAK,UACLE,KAAK,QACJ6D,QAAOC,GAAAgF,cACRpJ,MAAM,iB,kBACP,IAED4B,EAAA,MAAAA,EAAA,M,QAFC,a,+BAKHQ,EAAAA,EAAAA,IAuCM,MAvCNiH,EAuCM,G,aAtCJvI,EAAAA,EAAAA,IAqCMkB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IApCoBS,GAAAoF,WAAU,CAA1B1E,EAAMC,M,WADhBvC,EAAAA,EAAAA,IAqCM,OAnCHoB,IAAKkB,EAAKoC,GACXxF,MAAM,aACLmE,QAAK9C,GAAE+C,GAAAkF,UAAUlG,I,EAClBhB,EAAAA,EAAAA,IAKM,MALNmH,EAKM,EAJJnH,EAAAA,EAAAA,IAGO,OAFJpC,OAAKsI,EAAAA,EAAAA,IAAA,oBAAuBlE,GAAAoF,oBAAoBpG,KAChD,cAAagB,GAAAqF,mBAAmBrG,I,cAGrChB,EAAAA,EAAAA,IAyBM,MAzBNsH,EAyBM,EAxBJtH,EAAAA,EAAAA,IAEM,MAFNuH,EAEM,EADJvH,EAAAA,EAAAA,IAAwB,WAAAE,EAAAA,EAAAA,IAAjBc,EAAKxC,MAAI,MAElBwB,EAAAA,EAAAA,IAEM,MAFNwH,GAEMtH,EAAAA,EAAAA,IADDc,EAAKyC,MAAQ,QAAJ,IAEdzD,EAAAA,EAAAA,IAiBM,MAjBNyH,EAiBM,EAhBJzH,EAAAA,EAAAA,IAGM,MAHN0H,EAGM,EAFJ5J,EAAAA,EAAAA,IAA+B0H,GAAA,M,iBAAtB,IAAY,EAAZ1H,EAAAA,EAAAA,IAAY6J,M,OACrB3H,EAAAA,EAAAA,IAAyC,aAAAE,EAAAA,EAAAA,IAAhCc,EAAK4G,WAAa,GAAI,MAAG,MAEpC5H,EAAAA,EAAAA,IAWM,MAXN6H,EAWM,EAVJ7H,EAAAA,EAAAA,IAAkD,MAAlD8H,EAA0B,OAAG5H,EAAAA,EAAAA,IAAGe,EAAQ,GAAH,IACrCnD,EAAAA,EAAAA,IAQYgE,GAAA,CAPV9D,KAAK,SACL+J,OAAA,GACAnK,MAAM,mBACLmE,SAAKtC,EAAAA,EAAAA,IAAAR,GAAO+C,GAAAgG,SAAShH,EAAKoC,IAAE,UAC7B3C,MAAM,Q,kBAEN,IAA6B,EAA7B3C,EAAAA,EAAAA,IAA6B0H,GAAA,M,iBAApB,IAAU,EAAV1H,EAAAA,EAAAA,IAAU8I,M,wDAUjC5G,EAAAA,EAAAA,IAqHM,MArHNiI,EAqHM,EApHJjI,EAAAA,EAAAA,IAaM,MAbNkI,EAaM,EAZJlI,EAAAA,EAAAA,IAGM,MAHNmI,EAGM,EAFJnI,EAAAA,EAAAA,IAAyD,MAAzDoI,GAAyDlI,EAAAA,EAAAA,IAA9B8B,GAAAqG,sBAAuB,IAAC,G,eACnDrI,EAAAA,EAAAA,IAAmC,OAA9BpC,MAAM,cAAa,SAAK,OAE/BoC,EAAAA,EAAAA,IAGM,MAHNsI,EAGM,EAFJtI,EAAAA,EAAAA,IAAkD,MAAlDuI,GAAkDrI,EAAAA,EAAAA,IAAvBI,GAAAkI,QAAQ7C,QAAM,G,eACzC3F,EAAAA,EAAAA,IAAmC,OAA9BpC,MAAM,cAAa,SAAK,OAE/BoC,EAAAA,EAAAA,IAGM,MAHNyI,EAGM,EAFJzI,EAAAA,EAAAA,IAAuD,MAAvD0I,GAAuDxI,EAAAA,EAAAA,IAA5B8B,GAAA2G,qBAAiB,G,eAC5C3I,EAAAA,EAAAA,IAAkC,OAA7BpC,MAAM,cAAa,QAAI,SAKhCoC,EAAAA,EAAAA,IAaM,MAbN4I,EAaM,C,wTAHJ5I,EAAAA,EAAAA,IAEM,MAFN6I,EAEM,EADJ7I,EAAAA,EAAAA,IAAoD,MAApD8I,EAAoD,eAKxD9I,EAAAA,EAAAA,IAmFM,MAnFN+I,EAmFM,EAlFJ/I,EAAAA,EAAAA,IAGM,MAHNgJ,EAGM,C,eAFJhJ,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRlC,EAAAA,EAAAA,IAA6CiC,GAAA,CAArC/B,KAAK,OAAOE,KAAK,S,kBAAQ,IAAGsB,EAAA,MAAAA,EAAA,M,QAAH,U,iBAEnCQ,EAAAA,EAAAA,IA6EM,MA7ENiJ,EA6EM,EA5EJnL,EAAAA,EAAAA,IA2EWoL,GAAA,CA1ERjF,KAAM3D,GAAAkI,QACPW,OAAA,GACAtL,MAAA,eACAK,KAAK,QACJ,aAAY,SACbN,MAAM,iB,kBAEN,IAOkB,EAPlBE,EAAAA,EAAAA,IAOkBsL,GAAA,CAPD7K,MAAM,OAAO,YAAU,O,CAC3BqF,SAAOlD,EAAAA,EAAAA,IAIV2I,GAJiB,EACvBrJ,EAAAA,EAAAA,IAGM,MAHNsJ,EAGM,EAFJxL,EAAAA,EAAAA,IAA4B0H,GAAA,M,iBAAnB,IAAS,EAAT1H,EAAAA,EAAAA,IAASyL,M,OAClBvJ,EAAAA,EAAAA,IAAsD,aAAAE,EAAAA,EAAAA,IAA7CsJ,EAAAC,OAAOC,MAAML,EAAMM,IAAIC,cAAW,O,OAKjD9L,EAAAA,EAAAA,IAIkBsL,GAAA,CAJD7K,MAAM,KAAK8D,MAAM,O,CACrBuB,SAAOlD,EAAAA,EAAAA,IACqD2I,GAD9C,EACvBvL,EAAAA,EAAAA,IAAqEiC,GAAA,CAA7D7B,KAAK,QAAQ2L,OAAO,S,kBAAQ,IAAwB,E,iBAArBR,EAAMM,IAAIG,UAAQ,K,oBAI7DhM,EAAAA,EAAAA,IAakBsL,GAAA,CAbD7K,MAAM,KAAK8D,MAAM,O,CACrBuB,SAAOlD,EAAAA,EAAAA,IAUV2I,GAViB,EACvBrJ,EAAAA,EAAAA,IASM,MATN+J,EASM,CAR4B,QAArBV,EAAMM,IAAIxF,S,WAArBzF,EAAAA,EAAAA,IAGM,MAHNsL,EAGMxK,EAAA,MAAAA,EAAA,MAFJQ,EAAAA,EAAAA,IAAwC,OAAnCpC,MAAM,wBAAsB,UACjCoC,EAAAA,EAAAA,IAAgB,YAAV,OAAG,S,WAEXtB,EAAAA,EAAAA,IAGM,MAHNuL,EAGM,EAFJnM,EAAAA,EAAAA,IAA4B0H,GAAA,M,iBAAnB,IAAS,EAAT1H,EAAAA,EAAAA,IAASoM,M,qBAClBlK,EAAAA,EAAAA,IAAgB,YAAV,OAAG,W,OAMjBlC,EAAAA,EAAAA,IAuBkBsL,GAAA,CAvBD7K,MAAM,KAAK,YAAU,O,CACzBqF,SAAOlD,EAAAA,EAAAA,IAcL2I,GAdY,CACc,QAArBA,EAAMM,IAAIxF,S,WACxBzF,EAAAA,EAAAA,IAWM,MAXNyL,EAWM,EAVJnK,EAAAA,EAAAA,IAIM,MAJNoK,EAIM,EAHJpK,EAAAA,EAAAA,IAA0D,OAA1DqK,IAA0DnK,EAAAA,EAAAA,IAA3BmJ,EAAMM,IAAIW,SAAO,IAChDtK,EAAAA,EAAAA,IAAsD,OAAtDuK,GAA0B,MAAErK,EAAAA,EAAAA,IAAGmJ,EAAMM,IAAIa,KAAG,IAC5CxK,EAAAA,EAAAA,IAAmE,OAAnEyK,IAAmEvK,EAAAA,EAAAA,IAA9BmJ,EAAMM,IAAIe,WAAY,IAAC,MAE9D5M,EAAAA,EAAAA,IAIe6M,GAAA,CAHZC,WAAYC,WAAWxB,EAAMM,IAAIe,WACjC,eAAc,EACdI,MAAO9I,GAAA+I,iBAAiB1B,EAAMM,IAAIe,Y,+CAKvChM,EAAAA,EAAAA,IAGM,MAHNsM,GAGM,EAFJlN,EAAAA,EAAAA,IAA8B0H,GAAA,M,iBAArB,IAAW,EAAX1H,EAAAA,EAAAA,IAAWmN,M,qBACpBjL,EAAAA,EAAAA,IAAmB,YAAb,UAAM,S,OAMpBlC,EAAAA,EAAAA,IAWkBsL,GAAA,CAXD7K,MAAM,KAAK8D,MAAM,KAAK6I,MAAM,S,CAChCtH,SAAOlD,EAAAA,EAAAA,IAQJ2I,GARW,CAEM,QAArBA,EAAMM,IAAIxF,S,WADlB9F,EAAAA,EAAAA,IAOYyD,GAAA,C,MALV9D,KAAK,UACLmN,KAAA,GACCpJ,QAAK9C,GAAEuK,EAAA4B,QAAQC,KAAK,CAAD7M,KAAA,SAAA8M,OAAA,CAAAlI,GAAiCiG,EAAMM,IAAIvG,O,kBAChE,IAED5D,EAAA,MAAAA,EAAA,M,QAFC,W,iGAlLfnB,EAAAA,EAAAA,IAKW2H,GAAA,C,MAHTC,YAAY,aACZrI,MAAM,gB,kBACN,IAAmE,EAAnEE,EAAAA,EAAAA,IAAmEgE,GAAA,CAAxD9D,KAAK,UAAW+D,QAAOC,GAAAgF,e,kBAAe,IAAMxH,EAAA,KAAAA,EAAA,K,QAAN,a,8CAtEhBc,GAAAiL,YAkQvCzN,EAAAA,EAAAA,IAyBYqE,GAAA,C,WAxBD7B,GAAAkL,Q,qCAAAlL,GAAAkL,QAAOvM,GACfwB,MAAOH,GAAAmL,SAASrI,GAAK,SAAW,SACjCf,MAAM,MACL,eAAcL,GAAA0J,WACf,sBACAC,IAAI,OACJ/N,MAAM,e,CAYK2E,QAAM7B,EAAAA,EAAAA,IACf,IAGO,EAHPV,EAAAA,EAAAA,IAGO,OAHP4L,GAGO,EAFL9N,EAAAA,EAAAA,IAA6CgE,GAAA,CAAjCC,QAAOC,GAAA0J,YAAU,C,iBAAE,IAAElM,EAAA,MAAAA,EAAA,M,QAAF,S,6BAC/B1B,EAAAA,EAAAA,IAA0DgE,GAAA,CAA/C9D,KAAK,UAAW+D,QAAOC,GAAA6J,U,kBAAU,IAAErM,EAAA,MAAAA,EAAA,M,QAAF,S,iDAbhD,IASU,EATV1B,EAAAA,EAAAA,IASU4E,GAAA,CATAC,MAAOrC,GAAAmL,SAAUxG,IAAI,UAAU,iBAAe,O,kBACtD,IAOe,EAPfnH,EAAAA,EAAAA,IAOe+E,GAAA,CAPDtE,MAAM,OAAOuN,KAAK,OAAQC,MAAO,CAAC,CAADC,UAAA,EAAApH,QAAA,UAAAqH,QAAA,U,kBAC7C,IAKE,EALFnO,EAAAA,EAAAA,IAKEuF,GAAA,C,WAJS/C,GAAAmL,SAASjN,K,qCAAT8B,GAAAmL,SAASjN,KAAIS,GACtB+D,YAAY,UACZmC,UAAA,GACA+G,UAAA,I,mGAaQ5L,GAAA6L,W,WAAhB9N,EAAAA,EAAAA,IAAsF+N,GAAA,C,MAA3DC,aAAarK,GAAAsK,iBAAmB1G,OAAQtF,GAAAsF,Q,mKC9ThE/H,MAAA,wB,IA2BEA,MAAA,6C,qCA4CAD,MAAM,wB,IAcRC,MAAA,kB,IAEkCA,MAAA,mB,IACDA,MAAA,mB,IACAA,MAAA,qB,IACAA,MAAA,mB,gCAuBIA,MAAA,mB,UACYA,MAAA,mB,UACpCA,MAAA,mB,6ZApHlBC,EAAAA,EAAAA,IAmFYqE,EAAA,C,WAnFQ7B,EAAA6L,S,qCAAA7L,EAAA6L,SAAQlN,GAAEwB,MAAM,OAAO4B,MAAM,MAAO,eAAcL,EAAA0J,WAAYC,IAAI,K,kBACtF,IAiFM,EAjFN3L,EAAAA,EAAAA,IAiFM,MAjFNrB,GAiFM,EA/EJqB,EAAAA,EAAAA,IAsBM,aArBJlC,EAAAA,EAAAA,IAIWuF,EAAA,CAJDxF,MAAA,gB,WAA8ByC,EAAA4E,W,qCAAA5E,EAAA4E,WAAUjG,GAAE+D,YAAY,cAAcmC,UAAA,I,CACjEI,QAAM7E,EAAAA,EAAAA,IACf,IAA6D,EAA7D5C,EAAAA,EAAAA,IAA6DgE,EAAA,CAAlD9D,KAAK,UAAW+D,QAAOC,EAAAuK,a,kBAAa,IAAE/M,EAAA,KAAAA,EAAA,K,QAAF,S,qDAGnDQ,EAAAA,EAAAA,IAeO,cAdLlC,EAAAA,EAAAA,IAMYgE,EAAA,CALV9D,KAAK,UACLH,MAAA,sCACCkE,QAAOC,EAAA0J,WACPc,KAAMlM,EAAAmM,O,kBACJ,IACLjN,EAAA,KAAAA,EAAA,K,QADK,Y,mCAEL1B,EAAAA,EAAAA,IAMYgE,EAAA,CALV9D,KAAK,UACLH,MAAA,sCACCkE,QAAOC,EAAA0K,eACPF,KAAMlM,EAAAqM,O,kBACJ,IACLnN,EAAA,KAAAA,EAAA,K,QADK,Y,uCAKT1B,EAAAA,EAAAA,IAsDeuB,EAAA,CAtDDC,OAAO,uBAAqB,C,iBAC1C,IA2CM,EA3CNU,EAAAA,EAAAA,IA2CM,MA3CNnB,GA2CM,EA1CJf,EAAAA,EAAAA,IAyCSoL,EAAA,CAzCEjF,KAAM3D,EAAAsM,SAAWzD,OAAA,GAAO,aAAW,OAAO0D,OAAA,GAAQC,kBAAkB9K,EAAA+K,uB,kBACzE,IAA8E,EAA9EjP,EAAAA,EAAAA,IAA8EsL,EAAA,CAA7DpL,KAAK,YAAYgP,MAAM,SAAS3K,MAAM,QACvDvE,EAAAA,EAAAA,IAIkBsL,EAAA,CAJD7K,MAAM,KAAKyO,MAAM,SAAS3K,MAAM,M,CACpCuB,SAAOlD,EAAAA,EAAAA,IACmB2I,GADZ,EACvBrJ,EAAAA,EAAAA,IAAmC,aAAAE,EAAAA,EAAAA,IAA1BmJ,EAAM4D,OAAS,GAAH,K,OAGzBnP,EAAAA,EAAAA,IAIkBsL,EAAA,CAJD7K,MAAM,OAAOyO,MAAM,U,CACvBpJ,SAAOlD,EAAAA,EAAAA,IACkI2I,GAD3H,EACvBvL,EAAAA,EAAAA,IAAkJoP,EAAA,CAArItP,MAAM,eAAgBuP,GAAI,mBAAoBtP,MAAA,kBAAwBkE,QAAK9C,GAAE+C,EAAAoL,UAAU/D,EAAMM,M,kBAAM,IAAoB,E,iBAAjBN,EAAMM,IAAInL,MAAI,K,gCAGrIV,EAAAA,EAAAA,IAAoEsL,EAAA,CAAnD7K,MAAM,OAAOuN,KAAK,eAAgBkB,MAAM,YACzDlP,EAAAA,EAAAA,IAAyEsL,EAAA,CAAxD7K,MAAM,MAAM8D,MAAM,KAAKyJ,KAAK,YAAYkB,MAAM,YAC/DlP,EAAAA,EAAAA,IAOkBsL,EAAA,CAPD7K,MAAM,OAAOuN,KAAK,OAAOkB,MAAM,U,CACnCpJ,SAAOlD,EAAAA,EAAAA,IAIL2I,GAJY,EACzBvL,EAAAA,EAAAA,IAGawI,EAAA,CAHD1I,MAAM,OAAOiM,OAAO,OAAQtD,QAAS8C,EAAMM,IAAIlG,KAAM+C,UAAU,O,kBACzE,IAAiF,CAAtE6C,EAAMM,IAAIlG,KAAKkC,OAAM,K,WAAhCjH,EAAAA,EAAAA,IAAiF,MAAAU,IAAAc,EAAAA,EAAAA,IAAvCmJ,EAAMM,IAAIlG,KAAK4J,MAAM,EAAG,KAAM,MAAG,M,WAC3E3O,EAAAA,EAAAA,IAAsC,MAAAiB,IAAAO,EAAAA,EAAAA,IAAvBmJ,EAAMM,IAAIlG,MAAI,M,gCAIjC3F,EAAAA,EAAAA,IAAyEsL,EAAA,CAAxD7K,MAAM,MAAMuN,KAAK,UAAUkB,MAAM,SAAU3K,MAAM,SAClEvE,EAAAA,EAAAA,IAIkBsL,EAAA,CAJD7K,MAAM,OAAOyO,MAAM,SAAS3K,MAAM,O,CACtCuB,SAAOlD,EAAAA,EAAAA,IACyB2I,GADlB,E,iBACpBG,EAAAC,OAAOC,MAAML,EAAMM,IAAIC,cAAW,K,OAGzC9L,EAAAA,EAAAA,IAAyEsL,EAAA,CAAxD7K,MAAM,MAAMuN,KAAK,WAAWkB,MAAM,SAAS3K,MAAM,SAClEvE,EAAAA,EAAAA,IAIkBsL,EAAA,CAJD7K,MAAM,OAAOyO,MAAM,SAAS3K,MAAM,O,CACtCuB,SAAOlD,EAAAA,EAAAA,IAC2D2I,GADpD,CACdA,EAAMM,IAAI2D,c,WAAnB5O,EAAAA,EAAAA,IAA2E,IAAAuB,IAAAC,EAAAA,EAAAA,IAAzCsJ,EAAAC,OAAOC,MAAML,EAAMM,IAAI2D,cAAW,K,wBAGxExP,EAAAA,EAAAA,IAMkBsL,EAAA,CAND7K,MAAM,KAAK8D,MAAM,MAAM2K,MAAM,U,CACjCpJ,SAAOlD,EAAAA,EAAAA,IACsF2I,GAD/E,EACvBvL,EAAAA,EAAAA,IAAsGgE,EAAA,CAA1FC,QAAK9C,GAAE+C,EAAAuL,QAAQlE,EAAMM,KAAOzL,KAAM,QAASF,KAAK,UAAWwO,KAAMlM,EAAAkN,W,kBAAW,IAAEhO,EAAA,KAAAA,EAAA,K,QAAF,S,sCACxF1B,EAAAA,EAAAA,IAAuGgE,EAAA,CAA3FC,QAAK9C,GAAE+C,EAAAoL,UAAU/D,EAAMM,KAAOzL,KAAM,QAASF,KAAK,UAAWwO,KAAMlM,EAAAmN,M,kBAAM,IAAMjO,EAAA,KAAAA,EAAA,K,QAAN,a,sCACrF1B,EAAAA,EAAAA,IAA2GgE,EAAA,CAA/FC,QAAK9C,GAAE+C,EAAA0L,QAAQrE,EAAMM,IAAIvG,IAAMlF,KAAM,QAASF,KAAK,SAASkE,MAAA,GAAOsK,KAAMlM,EAAAqN,Q,kBAAQ,IAAEnO,EAAA,KAAAA,EAAA,K,QAAF,S,uFAKzGQ,EAAAA,EAAAA,IAQM,MARNI,GAQM,EAPJtC,EAAAA,EAAAA,IAMgB8P,EAAA,CANAC,WAAA,GAAWC,OAAO,mCACnBC,gBAAgB/L,EAAAgM,aAChB,oBAAmB,IACnBC,MAAO3N,EAAA4N,MAAMC,MACb,eAAc7N,EAAA4N,MAAME,QACtB,YAAU,MAAM,YAAU,O,wGAO3CtQ,EAAAA,EAAAA,IAqCWuQ,EAAA,C,WArCS/N,EAAAgO,U,qCAAAhO,EAAAgO,UAASrP,GAAG,eAAa,EAAOf,KAAK,O,kBACzD,IAmCM,EAnCN8B,EAAAA,EAAAA,IAmCM,MAnCNc,GAmCM,EAlCLhD,EAAAA,EAAAA,IAKkByQ,EAAA,CALD9N,MAAM,OAAOoM,OAAA,GAAQ2B,OAAQ,EAAG3Q,MAAA,yB,kBAChD,IAA+G,EAA/GC,EAAAA,EAAAA,IAA+G2Q,EAAA,CAAzFlQ,MAAM,MAAI,C,iBAAE,IAAsD,EAAtDyB,EAAAA,EAAAA,IAAsD,IAAtDkB,IAAsDhB,EAAAA,EAAAA,IAAzBI,EAAAoO,eAAelE,KAAG,K,OACjF1M,EAAAA,EAAAA,IAAkH2Q,EAAA,CAA5FlQ,MAAM,MAAI,C,iBAAC,IAA0D,EAA1DyB,EAAAA,EAAAA,IAA0D,IAA1DqB,IAA0DnB,EAAAA,EAAAA,IAA7BI,EAAAoO,eAAepE,SAAO,K,OACpFxM,EAAAA,EAAAA,IAAiH2Q,EAAA,CAA3FlQ,MAAM,MAAI,C,iBAAC,IAAyD,EAAzDyB,EAAAA,EAAAA,IAAyD,IAAzDsB,IAAyDpB,EAAAA,EAAAA,IAA1BI,EAAAoO,eAAeC,MAAI,K,OACnF7Q,EAAAA,EAAAA,IAAgH2Q,EAAA,CAA1FlQ,MAAM,MAAI,C,iBAAC,IAAwD,EAAxDyB,EAAAA,EAAAA,IAAwD,IAAxDuB,IAAwDrB,EAAAA,EAAAA,IAA3BI,EAAAoO,eAAeE,OAAK,K,2BAEnF5O,EAAAA,EAAAA,IAA8D,OAAzDnC,MAAA,sCAAuC,EAACmC,EAAAA,EAAAA,IAAW,SAAR,U,KAChDlC,EAAAA,EAAAA,IA0BeuB,EAAA,CA1BDC,OAAO,uBAAqB,C,iBACzC,IAwBW,EAxBXxB,EAAAA,EAAAA,IAwBWoL,EAAA,CAxBAjF,KAAM3D,EAAAoO,eAAeG,MAAOhR,MAAA,eAAoB,aAAW,Q,kBACrE,IAIkB,EAJlBC,EAAAA,EAAAA,IAIkBsL,EAAA,CAJDpL,KAAK,UAAQ,CAClB4F,SAAOlD,EAAAA,EAAAA,IAC4BiD,GADrB,EACxB7F,EAAAA,EAAAA,IAA6CgR,EAAA,CAAhC1Q,OAAQuF,EAAMgG,K,4BAG7B7L,EAAAA,EAAAA,IAA2CsL,EAAA,CAA1B7K,MAAM,MAAMuN,KAAK,UAClChO,EAAAA,EAAAA,IAIuBsL,EAAA,CAJN7K,MAAM,OAAOuN,KAAK,U,CACjBlI,SAAOlD,EAAAA,EAAAA,IACoDiD,GAD7C,CACS,QAAnBA,EAAMgG,IAAI3L,O,WAAtBU,EAAAA,EAAAA,IAAmE,OAAA+C,IAAAvB,EAAAA,EAAAA,IAA1ByD,EAAMgG,IAAIhJ,QAAM,K,wBAGnE7C,EAAAA,EAAAA,IAIuBsL,EAAA,CAJN7K,MAAM,QAAQuN,KAAK,e,CAClBlI,SAAOlD,EAAAA,EAAAA,IACyDiD,GADlD,CACS,QAAnBA,EAAMgG,IAAI3L,O,WAAtBU,EAAAA,EAAAA,IAAwE,OAAAgD,IAAAxB,EAAAA,EAAAA,IAA/ByD,EAAMgG,IAAInI,aAAW,K,wBAGxE1D,EAAAA,EAAAA,IAMkBsL,EAAA,CAND7K,MAAM,OAAOuN,KAAK,QAAQ,YAAU,Q,CACzClI,SAAOlD,EAAAA,EAAAA,IACwEiD,GADjE,CACO,MAAnBA,EAAMgG,IAAIvI,Q,WAAtB1C,EAAAA,EAAAA,IAAyF,OAAzFmD,IAAyF3B,EAAAA,EAAAA,IAAzByD,EAAMgG,IAAIvI,OAAK,IACpC,MAAnBuC,EAAMgG,IAAIvI,Q,WAA3B1C,EAAAA,EAAAA,IAA8F,OAA9F8D,IAA8FtC,EAAAA,EAAAA,IAAzByD,EAAMgG,IAAIvI,OAAK,M,WAC3F1C,EAAAA,EAAAA,IAA+D,OAA/DoI,IAA+D5G,EAAAA,EAAAA,IAAzByD,EAAMgG,IAAIvI,OAAK,M,sGAe5D,IACEuC,MAAO,CACLiC,OAAQmJ,QAEVlL,SAAU,KACLC,EAAAA,GAAAA,IAAS,CAAC,MAAM,WAErBC,WAAW,CACTiL,WAAUA,GAAAA,GAEZ/K,IAAAA,GACE,MAAO,CACLiB,WAAW,GACXgJ,MAAO,GACPtB,SAAU,GACV0B,WAAU,EACVI,eAAe,GACfvC,UAAS,EACT8C,aAAc,GACdtC,MAAK,SACLF,MAAK,SACLkB,OAAM,UACNF,KAAI,QACJD,UAASA,GAAAA,UAEb,EACDpJ,QAAS,KACH8K,EAAAA,GAAAA,IAAa,CAAC,aAElB3C,WAAAA,GACElI,KAAK8K,YAAY9K,KAAKE,IAAInB,GAAGiB,KAAK6J,MAAME,QAAQ/J,KAAKa,WACvD,EAGAkI,SAAAA,CAAUhK,GACViB,KAAK+G,QAAQC,KAAK,CAAE7M,KAAM,mBAC1B6F,KAAK+K,SAAShM,EACd,EAGA,iBAAM+L,CAAY7K,EAAQ+K,EAAK7Q,GAC7B,MAAMgG,QAAgBH,KAAKI,KAAK6K,YAAYhL,EAAQ+K,EAAK7Q,GACzD,GAAuB,MAAnBgG,EAASL,OAAc,CAE5B,MAAMoL,EAAU/K,EAASP,KAAK7F,OAC9BiG,KAAK6J,MAAQ1J,EAASP,KAElB,MAAMuL,QAAgCnL,KAAKI,KAAKgL,aAAa,CACjEC,SAAUrL,KAAKuB,SAEX,GAAsC,MAAlC4J,EAAuBrL,OAAgB,CACrC,MAAMwL,EAAiBH,EAAuBvL,KAAK7F,OAEnDiG,KAAKuI,SAAW2C,EAAQK,OAAO5O,IAAS2O,EAAeE,IAAIC,GAASA,EAAM1M,IAAIxE,SAASoC,EAAKoC,KAE5FiB,KAAKuI,SAAWvI,KAAKuI,SAASgD,OAAO5O,GAA2B,IAAnBA,EAAK4G,WAClDvD,KAAK6J,MAAMC,MAAS9J,KAAKuI,SAASjH,MACxC,CACL,CACA,EAEAqI,YAAAA,CAAa+B,GACV1L,KAAK8K,YAAY9K,KAAKE,IAAInB,GAAG2M,GAC7B1L,KAAK6J,MAAME,QAAU2B,CAEzB,EAGCrC,OAAAA,CAAQtK,GACP4M,GAAAA,EAAaC,QAAQ,qBAAsB,KAAM,CAC/CC,kBAAmB,KACnBC,iBAAkB,KAClBnS,KAAM,YAELoS,KAAKC,UACJ,MAAM7L,QAAiBH,KAAKI,KAAK6L,YAAYlN,GACvB,MAAnBoB,EAASL,UACVoM,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,UAGXP,KAAK8K,YAAY9K,KAAKE,IAAInB,IAC1BiB,KAAKa,WAAa,MAGrBsL,MAAM,MACLD,EAAAA,GAAAA,IAAU,CACRvS,KAAM,OACN4G,QAAS,WAGjB,EAGA,aAAM2I,CAAQtJ,GACV,MAAM2D,EAAY6I,SAASxM,EAAK2D,WAEhC,GADA8I,QAAQC,IAAI/I,GACTA,EAAY,EAClB,GAAIvD,KAAKuM,MAAO,CACf,MAAMtF,EAAS,CACduF,IAAKxM,KAAKuM,MACVd,MAAO7L,EAAKb,KAEP0N,EAAAA,GAAAA,IAAe,CACbrQ,MAAO,OACPmE,QAAS,cACT5G,KAAM,UACN6G,SAAS,MAEjB,MAAML,QAAiBH,KAAKI,KAAKsM,SAAS9M,EAAKb,GAAIkI,GAC5B,KAAnB9G,EAASL,SAEZE,KAAKqK,eAAiBlK,EAASP,KAC/BI,KAAKiK,WAAY,EAEnB,MACCiC,EAAAA,GAAAA,IAAU,CACTvS,KAAM,UACN4G,QAAS,aACTC,SAAU,WAGP0L,EAAAA,GAAAA,IAAU,CACbvS,KAAM,UACN4G,QAAS,YACTC,SAAU,KAEX,EAEFmM,UAAAA,GACE3M,KAAK4M,MAAM,cACb,EAEAvF,UAAAA,GACErH,KAAK2M,YACP,EAEA,oBAAMtE,GACF,GAAiC,IAA7BrI,KAAK4K,aAAatJ,OAMtB,YALE4K,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,cACTC,SAAU,MAIjB,IAAIyG,EAAS,CAAC,EACXA,EAAO4F,UAAY,IAAI7M,KAAK4K,cAC/B,MAAMzK,QAAiBH,KAAKI,KAAK0M,oBAAoB9M,KAAKuB,OAAO0F,GAC5C,MAAlB9G,EAASL,SACXoM,EAAAA,GAAAA,IAAU,CACTvS,KAAM,UACN4G,QAAS,OACTC,SAAU,MAGTR,KAAK2M,YACT,EAGAjE,qBAAAA,CAAsBqE,GAClB/M,KAAK4K,aAAemC,EAAIvB,IAAIlG,GAAOA,EAAIvG,IACvCsN,QAAQC,IAAItM,KAAK4K,aACnB,GAGHoC,OAAAA,GACChN,KAAK8K,YAAY9K,KAAKE,IAAInB,GAC5B,G,YCtSA,MAAM2B,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,UFgUA,IACEhB,WAAY,CACVuN,KAAI,MACJC,SAAQA,IAEVtN,IAAAA,GACE,MAAO,CAER8B,SAAU,GAEVH,OAAQ,GAER4C,QAAS,GAENiD,SAAS,CACPjN,KAAM,GACN4E,GAAI,MAGN8B,WAAY,GAEZsG,SAAS,EAET9F,WAAY,GAEZyG,UAAU,EAEVZ,SAAS,EAET1E,gBAAiB,GAEjB2K,YAAa,CACXlH,QAAS,UACTmH,QAAS,UACTC,OAAQ,UACRxN,KAAM,WAGZ,EACAL,SAAU,KACLC,EAAAA,GAAAA,IAAS,CAAC,MAAO,UACpB6N,YAAAA,GACE,MAAO,CACLC,SAAU,WACVrT,MAAO,OAEX,EACAsT,UAAW,WACZ,IAAIC,EAAU,GACVC,EAAW,GAEf,IAAK,IAAI/Q,KAAQqD,KAAKmE,QACrBsJ,EAAQzG,KAAKhH,KAAKoF,OAAOC,MAAM1I,EAAK4I,cACpCmI,EAAS1G,KAAKR,WAAW7J,EAAK0J,WAAWsH,QAAQ,IAElD,MAAO,CACNzT,MAAOuT,EAAQG,UACfhU,MAAO8T,EAET,GAEA3N,QAAS,KACJ8K,EAAAA,GAAAA,IAAa,CAAC,aAGjB,gBAAMgD,CAAW1T,GACf6F,KAAKkH,SAAU,EACf,IACE,IAAI/G,EAEFA,EADChG,QACgB6F,KAAKI,KAAK0N,aAAa9N,KAAKE,IAAInB,GAAI5E,SAEpC6F,KAAKI,KAAK0N,aAAa9N,KAAKE,IAAInB,IAG3B,MAApBoB,EAASL,SACXE,KAAK0B,SAAWvB,EAASP,KAGrBI,KAAK0B,SAASJ,OAAS,EACpBtB,KAAKuB,QAAWvB,KAAK0B,SAASqM,KAAKC,GAAQA,EAAKjP,KAAOiB,KAAKuB,QAI/DvB,KAAKiO,wBAHLjO,KAAK+B,WAAW/B,KAAK0B,SAAS,GAAG3C,KAMnCiB,KAAKuB,OAAS,GACdvB,KAAKwC,gBAAkB,GACvBxC,KAAKqB,WAAa,GAClBrB,KAAKmE,QAAU,IAGrB,CAAE,MAAOoG,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,WACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,WAAYA,EAC5B,CAAE,QACAvK,KAAKkH,SAAU,CACjB,CACF,EAGA+G,qBAAAA,GACE,GAAIjO,KAAKuB,OAAQ,CACf,MAAM2M,EAAclO,KAAK0B,SAASqM,KAAKC,GAAQA,EAAKjP,KAAOiB,KAAKuB,QAC5D2M,IACFlO,KAAKwC,gBAAkB0L,EAAY/T,KAEvC,MACE6F,KAAKwC,gBAAkB,EAE3B,EAGA,kBAAM2L,GACJ,IACE,MAAMhO,QAAiBH,KAAKI,KAAKgO,cAAc,CAAEJ,KAAMhO,KAAKuB,OAAQtB,QAASD,KAAKE,IAAInB,KAC9D,MAApBoB,EAASL,SACXE,KAAKmE,QAAUhE,EAASP,KAE5B,CAAE,MAAO2K,GACP8B,QAAQ9B,MAAM,WAAYA,EAC5B,CACF,EAGAxI,UAAAA,CAAWR,GACLvB,KAAKuB,SAAWA,IAClBvB,KAAKuB,OAASA,EACdvB,KAAKiO,wBACLjO,KAAKqO,UAAU9M,GAEnB,EAGA,aAAME,GACJzB,KAAKoH,SAAW,CACdjN,KAAM,QACN4E,GAAI,MAENiB,KAAKmH,SAAU,CACjB,EAGA/E,QAAAA,CAAS4L,GACPhO,KAAKoH,SAAW,IAAI4G,GACpBhO,KAAKmH,SAAU,CACjB,EAGAmH,eAAAA,CAAgB1O,GACdI,KAAK+B,WAAWnC,EAAKb,GACvB,EAGA,eAAMsP,CAAU9M,GACdvB,KAAKkH,SAAU,EACf,IACE,MAAM/G,QAAiBH,KAAKI,KAAKgL,aAAa,CAC5CC,SAAU9J,IAEY,MAApBpB,EAASL,SACXE,KAAKqB,WAAalB,EAASP,KAAK7F,QAElCiG,KAAKmO,cACP,CAAE,MAAO5D,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,WACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,WAAYA,EAC5B,CAAE,QACAvK,KAAKkH,SAAU,CACjB,CACF,EAGAjG,eAAAA,GACEjB,KAAK6N,WAAW7N,KAAKa,WACvB,EAGAwG,UAAAA,GACErH,KAAKmH,SAAU,CACjB,EAGA,cAAMK,GACJ,GAAKxH,KAAKoH,SAASjN,KASnB,IACE,IAAIgG,EACJ,GAAIH,KAAKoH,SAASrI,GAEhBoB,QAAiBH,KAAKI,KAAKmO,eAAevO,KAAKoH,SAASrI,GAAIiB,KAAKoH,cAC5D,CAEL,MAAMH,EAAS,CACbhH,QAASD,KAAKE,IAAInB,GAClB5E,KAAM6F,KAAKoH,SAASjN,MAEtBgG,QAAiBH,KAAKI,KAAKoO,eAAevH,EAC5C,CAEwB,MAApB9G,EAASL,QAAsC,MAApBK,EAASL,UACtCoM,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAASP,KAAKoH,SAASrI,GAAK,OAAS,OACrCyB,SAAU,OAEZR,KAAKmH,SAAU,QACTnH,KAAK6N,cAGN7N,KAAKoH,SAASrI,IAAMoB,EAASP,MAAQO,EAASP,KAAKb,GACtDiB,KAAK+B,WAAW5B,EAASP,KAAKb,IACrBiB,KAAKoH,SAASrI,KAAOiB,KAAKuB,QAEnCvB,KAAKiO,wBAGX,CAAE,MAAO1D,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,OACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,SAAUA,EAC1B,MA9CE2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,UACTC,SAAU,MA4ChB,EAGA,aAAMgB,GACJ,GAAKxB,KAAKuB,OASV,GAAIvB,KAAKuM,MAAO,CACd,MAAMtF,EAAS,CACbuF,IAAKxM,KAAKuM,MACVyB,KAAMhO,KAAKuB,OACXkN,MAAO,IAGThC,EAAAA,GAAAA,IAAe,CACbrQ,MAAO,OACPmE,QAAS,mBACT5G,KAAM,UACN6G,SAAU,MAGZ,IACE,MAAML,QAAiBH,KAAKI,KAAKoB,QAAQxB,KAAKuB,OAAQ0F,GAC9B,MAApB9G,EAASL,QACXE,KAAKmO,cAET,CAAE,MAAO5D,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,SACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,SAAUA,EAC1B,CACF,MACE2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,aACTC,SAAU,YAvCZ0L,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,WACTC,SAAU,MAuChB,EAGA8B,OAAAA,CAAQvD,GACN4M,GAAAA,EAAaC,QAAQ,eAAgB,KAAM,CACzCC,kBAAmB,KACnBC,iBAAkB,KAClBnS,KAAM,YAELoS,KAAKC,UACJ,IACE,MAAM7L,QAAiBH,KAAKI,KAAKsO,eAAe3P,GACxB,MAApBoB,EAASL,UACXoM,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,OACTC,SAAU,aAINR,KAAK6N,aAGP9O,IAAOiB,KAAKuB,SACdvB,KAAKuB,OAASvB,KAAK0B,SAASJ,OAAS,EAAItB,KAAK0B,SAAS,GAAG3C,GAAK,GAC3DiB,KAAKuB,OACPvB,KAAKqO,UAAUrO,KAAKuB,SAEpBvB,KAAKqB,WAAa,GAClBrB,KAAKmE,QAAU,KAIvB,CAAE,MAAOoG,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,OACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,SAAUA,EAC1B,IAED4B,MAAM,MACLD,EAAAA,GAAAA,IAAU,CACRvS,KAAM,OACN4G,QAAS,QACTC,SAAU,QAGlB,EAGAqC,SAAAA,CAAUlG,GACRqD,KAAK+G,QAAQC,KAAK,CAAE7M,KAAM,mBAC1BwC,EAAKgS,UAAY,OACjB3O,KAAK+K,SAASpO,EAChB,EAGAiS,kBAAAA,CAAmBC,GACI,WAAjBA,EAAQlV,MACVqG,KAAK2D,SAASkL,EAAQ9P,GAE1B,EAGA,cAAM4E,CAAS5E,GACb,IACE,IAAIkI,EAAS,CAAE6H,SAAU/P,GACzB,MAAMoB,QAAiBH,KAAKI,KAAK2O,oBAAoB/O,KAAKuB,OAAQ0F,GAC1C,MAApB9G,EAASL,UACXoM,EAAAA,GAAAA,IAAU,CACRvS,KAAM,UACN4G,QAAS,OACTC,SAAU,OAGZR,KAAKqO,UAAUrO,KAAKuB,QAExB,CAAE,MAAOgJ,IACP2B,EAAAA,GAAAA,IAAU,CACRvS,KAAM,QACN4G,QAAS,OACTC,SAAU,OAEZ6L,QAAQ9B,MAAM,SAAUA,EAC1B,CACF,EAGA5H,aAAAA,GACE3C,KAAK8H,UAAW,CAClB,EAGAG,gBAAAA,GACEjI,KAAK8H,UAAW,EAChB9H,KAAKqO,UAAUrO,KAAKuB,OACtB,EAGAyC,kBAAAA,GACE,IAAKhE,KAAKmE,SAAmC,IAAxBnE,KAAKmE,QAAQ7C,OAChC,OAAO,EAGT,MAAM0N,EAAmBhP,KAAKmE,QAAQoH,OAAO0D,GAA4B,QAAlBA,EAAOnP,QAC9D,GAAgC,IAA5BkP,EAAiB1N,OACnB,OAAO,EAGT,MAAM4N,EAAgBF,EAAiBG,OAAO,CAACC,EAAKH,IAAWG,EAAM5I,WAAWyI,EAAO5I,WAAY,GACnG,OAAQ6I,EAAgBF,EAAiB1N,QAAQqM,QAAQ,EAC3D,EAGArJ,iBAAAA,GACE,OAAKtE,KAAKmE,QACHnE,KAAKmE,QAAQoH,OAAO0D,GAA4B,QAAlBA,EAAOnP,QAAoB0G,WAAWyI,EAAO5I,YAAc,IAAI/E,OAD1E,CAE5B,EAGAoF,gBAAAA,CAAiB2I,GACf,MAAM9I,EAAaC,WAAW6I,GAC9B,OAAI9I,GAAc,GACTvG,KAAKmN,YAAYlH,QACfM,GAAc,GAChBvG,KAAKmN,YAAYC,QAEjBpN,KAAKmN,YAAYE,MAE5B,EAGAtK,mBAAAA,CAAoB0I,GAElB,MAAqB,YAAjBA,EAAM3L,OACD,UAIJ2L,EAAMlI,WAAiC,IAApBkI,EAAMlI,UAK1BkI,EAAM6D,WACD7D,EAAM6D,WAAWrJ,QAAU,UAAY,UAIzC,QATE,gBAUX,EAGAjD,kBAAAA,CAAmByI,GAEjB,MAAqB,YAAjBA,EAAM3L,OACD,MAIJ2L,EAAMlI,WAAiC,IAApBkI,EAAMlI,UAK1BkI,EAAM6D,WACD7D,EAAM6D,WAAWrJ,QAAU,OAAS,OAItC,OATE,OAUX,GAEF+G,OAAAA,GACEhN,KAAK6N,YACP,EACA0B,MAAO,CACLpL,OAAAA,GAEEnE,KAAKwP,UAAU,KACTxP,KAAKyP,MAAMC,YACb1P,KAAK2P,OAAOC,OAAO5P,KAAKyP,MAAMC,WAAY1P,KAAKwN,UAAU5T,MAAOoG,KAAKwN,UAAUtT,QAGrF,IG5xBJ,MAAM,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASyG,IAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/components/common/caseResult.vue", "webpack://frontend-web/./src/components/common/caseResult.vue?0f1a", "webpack://frontend-web/./src/views/TestPlan/TestPlanNew.vue", "webpack://frontend-web/./src/views/TestPlan/TestCaseDlg.vue", "webpack://frontend-web/./src/views/TestPlan/TestCaseDlg.vue?2d58", "webpack://frontend-web/./src/views/TestPlan/TestPlanNew.vue?3ad9"], "sourcesContent": ["<template>\n\t  <el-tabs model-value=\"rb\" style=\"min-height: 300px;\" type=\"border-card\" value=\"rb\" size=\"mini\">\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应体\" name=\"rb\">\n      <div v-if=\"result.response_header\">\n        <div v-if=\"result.response_header['Content-Type'].includes('application/json')\">\n          <!-- 如果 Content-Type 是 application/json，渲染 JSON 格式的 Editor -->\n          <Editor :readOnly=\"true\" v-model=\"result.response_body\" lang=\"json\" theme=\"chrome\"></Editor>\n        </div>\n        <div v-else>\n          <el-scrollbar height=\"400px\"  @wheel.stop>\n            <Editor :readOnly=\"true\" v-html=\"result.response_body\" lang=\"html\" theme=\"chrome\" height=\"400px\"></Editor>\n          </el-scrollbar>\n        </div>\n      </div>\n    </el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应头\" name=\"rh\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div class=\"tab-box-sli\" v-if=\"result.response_header\">\n\t\t\t\t<div v-for=\"(value, key) in result.response_header\">\n\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" type=\"info\">\n\t\t\t\t\t\t<b style=\"color: #747474;\">{{ key + ' : ' }}</b>\n\t\t\t\t\t\t<span>{{ value }}</span>\n\t\t\t\t\t</el-tag>\n\t\t\t\t</div>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"请求信息\" name=\"rq\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div v-if=\"result.requests_body\">\n\t\t\t\t<el-collapse v-model=\"activeNames\" class=\"tab-box-sli\">\n\t\t\t\t\t<el-collapse-item name=\"1\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>General</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div>Request Method : {{ result.method }}</div>\n\t\t\t\t\t\t<div>Request URL : {{ result.url }}</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"2\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Headers</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div v-for=\"(value, key) in result.requests_header\">\n\t\t\t\t\t\t\t<span>{{ key + ' : ' + value }}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"3\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Payload</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<span>{{ result.requests_body }}</span>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t</el-collapse>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane label=\"日志\">\n\t\t\t<el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t\t<div class=\"tab-box-sli\">\n\t\t\t\t\t<div v-for=\"(item, index) in result.log_data\">\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-if=\"item[0] === 'DEBUG'\" >{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'WARNING'\" type=\"warning\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'ERROR'\" type=\"danger\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'INFO'\" type=\"success\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<pre v-else-if=\"item[0] === 'EXCEPT'\" style=\"color: #d60000;\">{{ item[1] }}</pre>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.state === '成功'\" style=\"color: #00AA7F;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else-if=\"result.state === '失败'\" style=\"color: #d18d17;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else style=\"color: #ff0000;\">{{ result.state }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.status_cede <= 300\" style=\"color: #00AA7F;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t\t<span v-else style=\"color: #ff5500;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t{{ 'Time : ' + result.run_time }}\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t</el-tabs>\n    <div style=\"margin-top: 10px;width: 100%;text-align: center;\" v-if=\"result.state === '失败' && showbtn\">\n      <el-button  @click=\"getInterfaces\" type=\"success\" plain size=\"mini\">提交bug</el-button>\n    </div>\n    <!-- 添加bug的弹框 -->\n    <el-dialog title=\"提交bug\" v-model=\"addBugDlg\" width=\"40%\" :before-close=\"closeDialogResult\">\n      <el-form :model=\"bugForm\">\n        <el-form-item label=\"所属接口\">\n          <el-select size=\"small\" v-model=\"bugForm.interface\" placeholder=\"bug对应的接口\" style=\"width: 100%;\">\n            <el-option :label=\"iter.name + ' ' + iter.url\" :value=\"iter.id\" v-for=\"iter in interfaces\" :key=\"iter.id\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"bug描述\"><el-input :autosize=\"{ minRows: 3, maxRows: 4 }\" v-model=\"bugForm.desc\" type=\"textarea\" autocomplete=\"off\"></el-input></el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"closeDialogResult\">取 消</el-button>\n          <el-button type=\"success\" @click=\"saveBug\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n</template>\n\n<script>\nimport Editor from './Editor.vue';\nimport { mapState } from 'vuex';\nexport default {\n\tprops: {\n\t\tresult: {\n\t\t\tdefault: {}\n\t\t},\n\t\tshowbtn: {\n\t\t\tdefault: true\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState(['pro'])\n\t},\n\tcomponents: {\n\t\tEditor\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveNames: ['1', '2', '3'],\n\t\t\t// 提交bug的显示窗口\n\t\t\taddBugDlg: false,\n\t\t\t// 添加bug的表单\n\t\t\tbugForm: {\n\t\t\t\tinterface: null,\n\t\t\t\tdesc: '',\n\t\t\t\tinfo: '',\n\t\t\t\tstatus: '待处理'\n\t\t\t},\n      interfaces:[]\n\t\t};\n\t},\n\tmethods: {\n\t\tasync saveBug() {\n\t\t\tthis.bugForm.project = this.pro.id;\n\t\t\tthis.bugForm.info = this.result;\n\t\t\tconst response = await this.$api.createBugs(this.bugForm);\n\t\t\tif (response.status === 201) {\n\t\t\t\tthis.$message({\n\t\t\t\t\ttype: 'success',\n\t\t\t\t\tmessage: 'bug提交成功',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\tthis.addBugDlg = false;\n\t\t\t\tthis.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n\t\t\t}\n\t\t},\n    // 取消按钮时重置输入信息\n    closeDialogResult() {\n      this.addBugDlg = false;\n      this.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n      },\n\n    // 获取接口列表\n    async getInterfaces() {\n      const response = await this.$api.getNewInterfaces();\n      if (response.status === 200) {\n        this.interfaces = response.data\n        this.addBugDlg = true\n      }\n    }\n\t}\n};\n</script>\n\n<style></style>\n", "import { render } from \"./caseResult.vue?vue&type=template&id=3a14eb2a\"\nimport script from \"./caseResult.vue?vue&type=script&lang=js\"\nexport * from \"./caseResult.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\n  <div class=\"test-plan-dashboard\">\n    <!-- 顶部操作区 -->\n    <div class=\"dashboard-header\">\n      <div class=\"dashboard-title\">\n        <h2>测试计划管理</h2>\n      </div>\n      <div class=\"dashboard-actions\">\n        <el-input \n          v-model=\"filterText\" \n          placeholder=\"搜索计划\" \n          prefix-icon=\"el-icon-search\"\n          clearable\n          class=\"search-input\"\n          @keyup.enter=\"handletreeClick\"\n        >\n          <template #append>\n            <el-button @click=\"handletreeClick\">\n              <el-icon><Search /></el-icon>\n            </el-button>\n          </template>\n        </el-input>\n        <el-button \n          type=\"success\" \n          :disabled=\"!scene_list || scene_list.length === 0 || !planId\" \n          @click=\"runPlan\"\n          class=\"run-button\"\n        >\n          运行计划\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 主体内容区 -->\n    <div class=\"dashboard-main\" v-loading=\"loading\">\n      <!-- 左侧测试计划列表 -->\n      <div class=\"plans-sidebar\">\n        <div class=\"sidebar-header\">\n          <h3>测试计划列表</h3>\n          <el-button \n            type=\"primary\" \n            size=\"small\" \n            @click=\"addPlan\"\n            class=\"add-plan-btn\"\n          >\n            新建计划\n          </el-button>\n        </div>\n        \n        <el-empty \n          v-if=\"planList.length === 0\" \n          description=\"暂无测试计划\" \n          :image-size=\"100\"\n        >\n          <el-button type=\"primary\" @click=\"addPlan\">创建计划</el-button>\n        </el-empty>\n          <div class=\"plans-list\">\n            <div \n              v-for=\"item in planList\" \n              :key=\"item.id\" \n              :class=\"['plan-item', { active: planId === item.id }]\"\n              @click=\"selectPlan(item.id)\"\n            >\n              <div class=\"plan-info\">\n                <el-icon><Folder /></el-icon>\n                <span class=\"plan-name\">{{ item.name }}</span>\n              </div>\n              <div class=\"plan-actions\">\n                <el-tooltip content=\"编辑\" placement=\"top\">\n                  <el-icon \n                    @click.stop=\"editPlan(item)\" \n                    class=\"action-icon\"\n                  >\n                    <Edit />\n                  </el-icon>\n                </el-tooltip>\n                <el-tooltip content=\"删除\" placement=\"top\">\n                  <el-icon \n                    @click.stop=\"delPlan(item.id)\" \n                    class=\"action-icon delete-icon\"\n                  >\n                    <Delete />\n                  </el-icon>\n                </el-tooltip>\n              </div>\n            </div>\n          </div>\n      </div>\n      \n      <!-- 右侧主要内容区 -->\n      <div class=\"content-main\">\n        <!-- 没有测试计划时显示 -->\n        <el-empty \n          v-if=\"planList.length === 0\" \n          description=\"暂无测试计划，请先创建测试计划\" \n          class=\"center-empty\">\n          <el-button type=\"primary\" @click=\"addPlan\">创建测试计划</el-button>\n        </el-empty>\n        \n        <!-- 有测试计划但没有场景时显示 -->\n        <el-empty \n          v-else-if=\"!scene_list || scene_list.length === 0\" \n          description=\"当前计划暂无测试场景\" \n          class=\"center-empty\">\n          <el-button type=\"primary\" @click=\"clickAddScene\">添加测试场景</el-button>\n        </el-empty>\n        \n        <!-- 有测试场景时显示 -->\n        <div v-else class=\"content-layout\">\n          <!-- 测试场景区域 -->\n          <div class=\"scenes-container\">\n            <div class=\"section-header\">\n              <h3>\n                <span v-if=\"currentPlanName\">{{ currentPlanName }}</span>\n                <span v-else>测试场景</span>\n              </h3>\n              <el-button \n                type=\"primary\" \n                size=\"small\" \n                @click=\"clickAddScene\"\n                class=\"add-scene-btn\"\n              >\n                添加场景\n              </el-button>\n            </div>\n            \n            <div class=\"scenes-grid\">\n              <div \n                v-for=\"(item, index) in scene_list\" \n                :key=\"item.id\" \n                class=\"scene-card\" \n                @click=\"clickView(item)\">\n                <div class=\"scene-status\">\n                  <div \n                    :class=\"['status-indicator', getSceneStatusClass(item)]\" \n                    :data-status=\"getSceneStatusText(item)\"\n                  ></div>\n                </div>\n                <div class=\"scene-card-content\">\n                  <div class=\"scene-card-header\">\n                    <h4>{{ item.name }}</h4>\n                  </div>\n                  <div class=\"scene-card-description\">\n                    {{ item.desc || '暂无描述' }}\n                  </div>\n                  <div class=\"scene-card-footer\">\n                    <div class=\"scene-steps\">\n                      <el-icon><Document /></el-icon>\n                      <span>{{ item.stepCount || 0 }} 步骤</span>\n                    </div>\n                    <div class=\"scene-actions\">\n                      <div class=\"scene-number\">场景 {{ index + 1 }}</div>\n                      <el-button\n                        type=\"danger\" \n                        circle \n                        class=\"delete-scene-btn\"\n                        @click.stop=\"delScene(item.id)\"\n                        title=\"删除场景\"\n                      >\n                        <el-icon><Delete /></el-icon>\n                      </el-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          <!-- 右侧测试数据区域 -->\n          <div class=\"data-container\">\n            <div class=\"data-overview\">\n              <div class=\"data-card pass-rate\">\n                <div class=\"data-value\">{{ getAveragePassRate() }}%</div>\n                <div class=\"data-label\">平均通过率</div>\n              </div>\n              <div class=\"data-card total-runs\">\n                <div class=\"data-value\">{{ records.length }}</div>\n                <div class=\"data-label\">总执行次数</div>\n              </div>\n              <div class=\"data-card success-runs\">\n                <div class=\"data-value\">{{ getSuccessfulRuns() }}</div>\n                <div class=\"data-label\">成功执行</div>\n              </div>\n            </div>\n            \n            <!-- 通过率趋势图 -->\n            <div class=\"chart-section\">\n              <div class=\"section-header\">\n                <h3>通过率趋势</h3>\n                <div class=\"chart-legend\">\n                  <div class=\"legend-item\">\n                    <div class=\"legend-color success\"></div>\n                    <div class=\"legend-text\">通过率</div>\n                  </div>\n                </div>\n              </div>\n              <div class=\"chart-container-wrapper\">\n                <div ref=\"chartTable\" class=\"chart-container\"></div>\n              </div>\n            </div>\n            \n            <!-- 执行记录表格 -->\n            <div class=\"records-section\">\n              <div class=\"section-header\">\n                <h3>执行记录</h3>\n                <el-tag type=\"info\" size=\"small\">近三天</el-tag>\n              </div>\n              <div class=\"records-container\">\n                <el-table \n                  :data=\"records\" \n                  stripe \n                  style=\"width: 100%\" \n                  size=\"small\"\n                  :empty-text=\"'暂无执行记录'\"\n                  class=\"records-table\"\n                >\n                  <el-table-column label=\"执行时间\" min-width=\"120\">\n                    <template #default=\"scope\">\n                      <div class=\"time-cell\">\n                        <el-icon><Timer /></el-icon>\n                        <span>{{ $tools.rTime(scope.row.create_time) }}</span>\n                      </div>\n                    </template>\n                  </el-table-column>\n                  \n                  <el-table-column label=\"环境\" width=\"100\">\n                    <template #default=\"scope\">\n                      <el-tag size=\"small\" effect=\"plain\">{{ scope.row.env_name }}</el-tag>\n                    </template>\n                  </el-table-column>\n                  \n                  <el-table-column label=\"状态\" width=\"100\">\n                    <template #default=\"scope\">\n                      <div class=\"status-cell\">\n                        <div v-if=\"scope.row.status === '执行中'\" class=\"status-badge running\">\n                          <div class=\"status-indicator-dot\"></div>\n                          <span>执行中</span>\n                        </div>\n                        <div v-else class=\"status-badge completed\">\n                          <el-icon><Check /></el-icon>\n                          <span>已完成</span>\n                        </div>\n                      </div>\n                    </template>\n                  </el-table-column>\n                  \n                  <el-table-column label=\"结果\" min-width=\"120\">\n                    <template #default=\"scope\">\n                      <template v-if=\"scope.row.status !== '执行中'\">\n                        <div class=\"progress-wrapper\">\n                          <div class=\"progress-counts\">\n                            <span class=\"success-count\">{{ scope.row.success }}</span>\n                            <span class=\"total-count\">/ {{ scope.row.all }}</span>\n                            <span class=\"progress-percentage\">{{ scope.row.pass_rate }}%</span>\n                          </div>\n                          <el-progress \n                            :percentage=\"parseFloat(scope.row.pass_rate)\" \n                            :stroke-width=\"6\" \n                            :color=\"getProgressColor(scope.row.pass_rate)\"\n                          ></el-progress>\n                        </div>\n                      </template>\n                      <template v-else>\n                        <div class=\"progress-pending\">\n                          <el-icon><Loading /></el-icon>\n                          <span>进行中...</span>\n                        </div>\n                      </template>\n                    </template>\n                  </el-table-column>\n                  \n                  <el-table-column label=\"报告\" width=\"80\" fixed=\"right\">\n                    <template #default=\"scope\">\n                      <el-button \n                        v-if=\"scope.row.status !== '执行中'\" \n                        type=\"primary\" \n                        link\n                        @click=\"$router.push({ name: 'report', params: { id: scope.row.id } })\"\n                      >\n                        查看\n                      </el-button>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 修改计划窗口 -->\n    <el-dialog \n      v-model=\"editDlg\" \n      :title=\"planForm.id ? '编辑测试计划' : '新建测试计划'\" \n      width=\"30%\" \n      :before-close=\"clickClear\"\n      destroy-on-close\n      top=\"20vh\"\n      class=\"plan-dialog\"\n    >\n      <el-form :model=\"planForm\" ref=\"treeRef\" label-position=\"top\">\n        <el-form-item label=\"计划名称\" prop=\"name\" :rules=\"[{ required: true, message: '请输入计划名称', trigger: 'blur' }]\">\n          <el-input \n            v-model=\"planForm.name\" \n            placeholder=\"请输入计划名称\" \n            clearable\n            autofocus\n          />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"clickClear\">取消</el-button>\n          <el-button type=\"primary\" @click=\"savePlan\">保存</el-button>\n        </span>\n      </template>\n    </el-dialog>\n    \n    <!-- 添加测试场景窗口 -->\n    <TestCase v-if=\"sceneDlg\" @close-modal=\"handleCloseModal\" :planId=\"planId\"></TestCase>\n  </div>\n</template>\n\n<script>\nimport {ElMessage, ElMessageBox, ElNotification} from \"element-plus\";\nimport {mapMutations, mapState} from \"vuex\";\nimport { Icon } from '@iconify/vue'\nimport TestCase from '../../views/TestPlan/TestCaseDlg.vue';\nexport default {\n  components: {\n    Icon,\n    TestCase\n  },\n  data() {\n    return {\n      // 测试计划列表\n\t\t\tplanList: [],\n\t\t\t// 当前选中的测试计划ID\n\t\t\tplanId: '',\n\t\t\t// 测试计划中所有运行记录\n\t\t\trecords: [],\n      // 表单数据\n      planForm:{\n        name: '',\n        id: null\n      },\n      // 搜索关键词\n      filterText: '',\n      // 对话框控制\n      editDlg: false,\n      // 测试场景列表\n      scene_list: [],\n      // 场景对话框控制\n      sceneDlg: false,\n      // 加载状态\n      loading: false,\n      // 当前选中的计划名称\n      currentPlanName: '',\n      // 颜色配置\n      themeColors: {\n        success: '#67C23A',\n        warning: '#E6A23C',\n        danger: '#F56C6C',\n        info: '#909399'\n      }\n    }\n  },\n  computed: {\n    ...mapState(['pro', 'envId']),\n    defaultProps() {\n      return {\n        children: 'children',\n        label: 'name',\n      }\n    },\n    chartData: function() {\n\t\t\tlet runDate = [];\n\t\t\tlet passRate = [];\n\n\t\t\tfor (let item of this.records) {\n\t\t\t\trunDate.push(this.$tools.rTime(item.create_time));\n\t\t\t\tpassRate.push(parseFloat(item.pass_rate).toFixed(2));\n\t\t\t}\n\t\t\treturn {\n\t\t\t\tlabel: runDate.reverse(),\n\t\t\t\tvalue: passRate\n\t\t\t};\n\t\t},\n  },\n  methods: {\n    ...mapMutations(['CaseInfo']),\n    \n    // 获取测试计划列表\n    async getAllPlan(name) {\n      this.loading = true;\n      try {\n        let response;\n        if(name) {\n          response = await this.$api.getTestPlans(this.pro.id, name);\n        } else {\n          response = await this.$api.getTestPlans(this.pro.id);\n        }\n        \n        if (response.status === 200) {\n          this.planList = response.data;\n          \n          // 设置默认激活的测试计划并获取数据\n          if (this.planList.length > 0) {\n            if (!this.planId || !this.planList.find(plan => plan.id === this.planId)) {\n              this.selectPlan(this.planList[0].id);\n            } else {\n              // 更新当前计划名称\n              this.updateCurrentPlanName();\n            }\n          } else {\n            this.planId = '';\n            this.currentPlanName = '';\n            this.scene_list = [];\n            this.records = [];\n          }\n        }\n      } catch (error) {\n        ElMessage({\n          type: 'error',\n          message: '获取测试计划失败',\n          duration: 1500\n        });\n        console.error('获取测试计划失败', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 更新当前计划名称\n    updateCurrentPlanName() {\n      if (this.planId) {\n        const currentPlan = this.planList.find(plan => plan.id === this.planId);\n        if (currentPlan) {\n          this.currentPlanName = currentPlan.name;\n        }\n      } else {\n        this.currentPlanName = '';\n      }\n    },\n\n    // 获取测试计划所有的执行记录\n    async getAllRecord() {\n      try {\n        const response = await this.$api.getTestRecord({ plan: this.planId, project: this.pro.id });\n        if (response.status === 200) {\n          this.records = response.data;\n        }\n      } catch (error) {\n        console.error('获取执行记录失败', error);\n      }\n    },\n    \n    // 选择测试计划\n    selectPlan(planId) {\n      if (this.planId !== planId) {\n        this.planId = planId;\n        this.updateCurrentPlanName();\n        this.getScenes(planId);\n      }\n    },\n    \n    // 添加测试计划\n    async addPlan() {\n      this.planForm = {\n        name: '新测试计划',\n        id: null\n      };\n      this.editDlg = true;\n    },\n    \n    // 编辑测试计划\n    editPlan(plan) {\n      this.planForm = {...plan};\n      this.editDlg = true;\n    },\n    \n    // 处理节点点击\n    handleNodeClick(data) {\n      this.selectPlan(data.id);\n    },\n    \n    // 获取测试场景\n    async getScenes(planId) {\n      this.loading = true;\n      try {\n        const response = await this.$api.getTestCase_({\n          testplan: planId\n        });\n        if (response.status === 200) {\n          this.scene_list = response.data.result;\n        }\n        this.getAllRecord();\n      } catch (error) {\n        ElMessage({\n          type: 'error',\n          message: '获取测试场景失败',\n          duration: 1500\n        });\n        console.error('获取测试场景失败', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    // 点击查询\n    handletreeClick() {\n      this.getAllPlan(this.filterText);\n    },\n\n    // 点击取消\n    clickClear() {\n      this.editDlg = false;\n    },\n    \n    // 保存测试计划\n    async savePlan() {\n      if (!this.planForm.name) {\n        ElMessage({\n          type: 'warning',\n          message: '请输入计划名称',\n          duration: 1500\n        });\n        return;\n      }\n      \n      try {\n        let response;\n        if (this.planForm.id) {\n          // 更新现有计划\n          response = await this.$api.updateTestPlan(this.planForm.id, this.planForm);\n        } else {\n          // 创建新计划\n          const params = {\n            project: this.pro.id,\n            name: this.planForm.name\n          };\n          response = await this.$api.createTestPlan(params);\n        }\n        \n        if (response.status === 200 || response.status === 201) {\n          ElMessage({\n            type: 'success',\n            message: this.planForm.id ? '保存成功' : '创建成功',\n            duration: 1500\n          });\n          this.editDlg = false;\n          await this.getAllPlan();\n          \n          // 如果是新建的计划，选中它\n          if (!this.planForm.id && response.data && response.data.id) {\n            this.selectPlan(response.data.id);\n          } else if (this.planForm.id === this.planId) {\n            // 如果修改的是当前计划，更新名称\n            this.updateCurrentPlanName();\n          }\n        }\n      } catch (error) {\n        ElMessage({\n          type: 'error',\n          message: '操作失败',\n          duration: 1500\n        });\n        console.error('保存计划失败', error);\n      }\n    },\n    \n    // 运行测试计划\n    async runPlan() {\n      if (!this.planId) {\n        ElMessage({\n          type: 'warning',\n          message: '请先选择测试计划',\n          duration: 1500\n        });\n        return;\n      }\n      \n      if (this.envId) {\n        const params = {\n          env: this.envId,\n          plan: this.planId,\n          types: 2\n        };\n        \n        ElNotification({\n          title: '开始运行',\n          message: '测试计划正在执行中，请稍候...',\n          type: 'success',\n          duration: 3000\n        });\n        \n        try {\n          const response = await this.$api.runPlan(this.planId, params);\n          if (response.status === 200) {\n            this.getAllRecord();\n          }\n        } catch (error) {\n          ElMessage({\n            type: 'error',\n            message: '运行计划失败',\n            duration: 1500\n          });\n          console.error('运行计划失败', error);\n        }\n      } else {\n        ElMessage({\n          type: 'warning',\n          message: '当前未选中执行环境!',\n          duration: 1500\n        });\n      }\n    },\n    \n    // 删除测试计划\n    delPlan(id) {\n      ElMessageBox.confirm('确定要删除该测试计划吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      })\n        .then(async () => {\n          try {\n            const response = await this.$api.deleteTestPlan(id);\n            if (response.status === 204) {\n              ElMessage({\n                type: 'success',\n                message: '删除成功',\n                duration: 1500\n              });\n              \n              // 重新获取计划列表\n              await this.getAllPlan();\n              \n              // 如果删除的是当前选中的计划，重置选择\n              if (id === this.planId) {\n                this.planId = this.planList.length > 0 ? this.planList[0].id : '';\n                if (this.planId) {\n                  this.getScenes(this.planId);\n                } else {\n                  this.scene_list = [];\n                  this.records = [];\n                }\n              }\n            }\n          } catch (error) {\n            ElMessage({\n              type: 'error',\n              message: '删除失败',\n              duration: 1500\n            });\n            console.error('删除计划失败', error);\n          }\n        })\n        .catch(() => {\n          ElMessage({\n            type: 'info',\n            message: '已取消删除',\n            duration: 1500\n          });\n        });\n    },\n\n    // 点击查看测试场景详情\n    clickView(item) {\n      this.$router.push({ name: 'TestCaseDetail' });\n      item.back_type = 'plan';\n      this.CaseInfo(item);\n    },\n\n    // 处理场景命令\n    handleSceneCommand(command) {\n      if (command.type === 'delete') {\n        this.delScene(command.id);\n      }\n    },\n\n    // 删除测试场景\n    async delScene(id) {\n      try {\n        let params = { scene_id: id };\n        const response = await this.$api.deleteTestPlanScene(this.planId, params);\n        if (response.status === 200) {\n          ElMessage({\n            type: 'success',\n            message: '删除成功',\n            duration: 1500\n          });\n          // 更新页面中当前任务的数据\n          this.getScenes(this.planId);\n        }\n      } catch (error) {\n        ElMessage({\n          type: 'error',\n          message: '删除失败',\n          duration: 1500\n        });\n        console.error('删除场景失败', error);\n      }\n    },\n    \n    // 点击添加场景\n    clickAddScene() {\n      this.sceneDlg = true;\n    },\n\n    // 处理关闭模态框\n    handleCloseModal() {\n      this.sceneDlg = false; // 关闭弹窗\n      this.getScenes(this.planId);\n    },\n    \n    // 获取平均通过率\n    getAveragePassRate() {\n      if (!this.records || this.records.length === 0) {\n        return 0;\n      }\n      \n      const completedRecords = this.records.filter(record => record.status !== '执行中');\n      if (completedRecords.length === 0) {\n        return 0;\n      }\n      \n      const totalPassRate = completedRecords.reduce((sum, record) => sum + parseFloat(record.pass_rate), 0);\n      return (totalPassRate / completedRecords.length).toFixed(2);\n    },\n    \n    // 获取成功执行次数\n    getSuccessfulRuns() {\n      if (!this.records) return 0;\n      return this.records.filter(record => record.status !== '执行中' && parseFloat(record.pass_rate) >= 80).length;\n    },\n    \n    // 获取进度条颜色\n    getProgressColor(rate) {\n      const percentage = parseFloat(rate);\n      if (percentage >= 80) {\n        return this.themeColors.success;\n      } else if (percentage >= 60) {\n        return this.themeColors.warning;\n      } else {\n        return this.themeColors.danger;\n      }\n    },\n    \n    // 获取场景状态的CSS类名\n    getSceneStatusClass(scene) {\n      // 如果场景正在执行中\n      if (scene.status === 'running') {\n        return 'running';\n      }\n      \n      // 如果场景没有步骤\n      if (!scene.stepCount || scene.stepCount === 0) {\n        return 'not-configured';\n      }\n      \n      // 如果场景有执行结果\n      if (scene.lastResult) {\n        return scene.lastResult.success ? 'success' : 'failure';\n      }\n      \n      // 如果场景有步骤但未执行过\n      return 'ready';\n    },\n    \n    // 获取场景状态的文本描述\n    getSceneStatusText(scene) {\n      // 如果场景正在执行中\n      if (scene.status === 'running') {\n        return '执行中';\n      }\n      \n      // 如果场景没有步骤\n      if (!scene.stepCount || scene.stepCount === 0) {\n        return '未配置步骤';\n      }\n      \n      // 如果场景有执行结果\n      if (scene.lastResult) {\n        return scene.lastResult.success ? '执行成功' : '执行失败';\n      }\n      \n      // 如果场景有步骤但未执行过\n      return '准备就绪';\n    },\n  },\n  created() {\n    this.getAllPlan();\n  },\n  watch: {\n    records() {\n      // 渲染通过率趋势图\n      this.$nextTick(() => {\n        if (this.$refs.chartTable) {\n          this.$chart.chart3(this.$refs.chartTable, this.chartData.value, this.chartData.label);\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 主容器样式 */\n.test-plan-dashboard {\n  height: 100vh;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: #f5f7fa;\n  color: #333;\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\n  background-image: linear-gradient(rgba(255, 255, 255, 0.7) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(255, 255, 255, 0.7) 1px, transparent 1px);\n  background-size: 20px 20px;\n  background-position: center center;\n  overflow: hidden; /* 防止整体出现滚动条 */\n}\n\n/* 头部样式 */\n.dashboard-header {\n  padding: 20px 28px;\n  background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  z-index: 10;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.03);\n  flex-shrink: 0; /* 防止头部被压缩 */\n}\n\n.dashboard-title {\n  display: flex;\n  align-items: center;\n  gap: 24px;\n}\n\n.dashboard-title h2 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2329;\n  position: relative;\n  padding-left: 16px;\n}\n\n.dashboard-title h2:before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 20px;\n  background: linear-gradient(to bottom, #409EFF, #53a8ff);\n  border-radius: 2px;\n}\n\n.dashboard-actions {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.search-input {\n  width: 260px;\n  transition: all 0.3s ease;\n}\n\n.search-input:hover, .search-input:focus-within {\n  box-shadow: 0 0 8px rgba(64, 158, 255, 0.1);\n}\n\n.run-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 10px 16px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  border-radius: 8px;\n  letter-spacing: 0.5px;\n}\n\n.run-button:not(:disabled):hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(103, 194, 58, 0.2);\n}\n\n/* 主内容区域 */\n.dashboard-main {\n  flex: 1;\n  display: flex;\n  gap: 20px;\n  padding: 20px;\n  overflow: auto; /* 允许主区域滚动 */\n  position: relative;\n}\n\n/* 计划列表侧边栏 */\n.plans-sidebar {\n  width: 280px;\n  background: linear-gradient(135deg, #ffffff 0%, #f9fbfd 100%);\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  position: relative;\n  flex-shrink: 0; /* 防止侧边栏被压缩 */\n  max-height: calc(100vh - 120px); /* 限制最大高度 */\n}\n\n.plans-sidebar:hover {\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);\n}\n\n.plans-sidebar:before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background: linear-gradient(90deg, #409EFF, #79bbff);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.plans-sidebar:hover:before {\n  opacity: 1;\n}\n\n.sidebar-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  border-bottom: 1px solid #f0f2f5;\n  background-color: rgba(64, 158, 255, 0.03);\n}\n\n.sidebar-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2329;\n  position: relative;\n  padding-left: 12px;\n}\n\n.sidebar-header h3:before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 3px;\n  height: 14px;\n  background: linear-gradient(to bottom, #409EFF, #53a8ff);\n  border-radius: 2px;\n}\n\n.add-plan-btn {\n  padding: 8px 14px;\n  display: inline-flex;\n  align-items: center;\n  gap: 6px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.add-plan-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.15);\n}\n\n\n.plans-list {\n  padding: 12px;\n  overflow-y: auto; /* 允许计划列表滚动 */\n  flex: 1; /* 使列表可以占满剩余空间 */\n}\n\n.plan-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 14px;\n  margin-bottom: 6px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 1px solid transparent;\n  background-color: rgba(255, 255, 255, 0.6);\n}\n\n.plan-item:hover {\n  background-color: rgba(64, 158, 255, 0.05);\n  border-color: rgba(64, 158, 255, 0.1);\n  transform: translateX(4px);\n}\n\n.plan-item.active {\n  background-color: rgba(64, 158, 255, 0.1);\n  color: #409eff;\n  border-left: 3px solid #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n.plan-info {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  overflow: hidden;\n}\n\n.plan-info .el-icon {\n  font-size: 18px;\n  color: #409eff;\n  opacity: 0.8;\n  transition: all 0.3s ease;\n}\n\n.plan-item:hover .plan-info .el-icon {\n  opacity: 1;\n  transform: scale(1.1);\n}\n\n.plan-name {\n  font-size: 14px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  transition: all 0.3s ease;\n}\n\n.plan-item.active .plan-name {\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.plan-actions {\n  display: none;\n  gap: 8px;\n}\n\n.plan-item:hover .plan-actions {\n  display: flex;\n}\n\n.action-icon {\n  font-size: 16px;\n  color: #909399;\n  cursor: pointer;\n  padding: 4px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n  background-color: rgba(255, 255, 255, 0.5);\n}\n\n.action-icon:hover {\n  color: #409eff;\n  background-color: rgba(64, 158, 255, 0.1);\n  transform: scale(1.1);\n}\n\n.delete-icon:hover {\n  color: #f56c6c;\n  background-color: rgba(245, 108, 108, 0.1);\n}\n\n/* 内容主区域 */\n.content-main {\n  flex: 1;\n  overflow: auto; /* 允许右侧主要内容区域滚动 */\n  position: relative;\n  display: flex; /* 使其内容可以灵活排布 */\n  flex-direction: column; /* 垂直排列内容 */\n}\n\n.center-empty {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  text-align: center;\n  width: 100%;\n  max-width: 400px;\n}\n\n.center-empty .el-button {\n  margin-top: 16px;\n  transition: all 0.3s ease;\n}\n\n.center-empty .el-button:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.2);\n}\n\n/* 布局 */\n.content-layout {\n  display: grid;\n  grid-template-columns: 1fr 380px;\n  gap: 20px;\n  height: auto; /* 改为自适应高度 */\n  min-height: 100%; /* 至少占满容器高度 */\n  overflow: visible; /* 不隐藏溢出内容 */\n}\n\n/* 场景区域 */\n.scenes-container {\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  padding: 16px;\n  overflow: hidden;\n  height: auto; /* 改为自适应高度 */\n  min-height: 400px; /* 设置最小高度 */\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.section-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2329;\n}\n\n.scenes-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));\n  gap: 16px;\n  overflow-y: auto; /* 允许场景网格滚动 */\n  padding-right: 8px;\n  flex: 1; /* 占满剩余空间 */\n  max-height: calc(100vh - 250px); /* 限制最大高度 */\n  grid-auto-rows: 160px; /* 确保每行的高度一致 */\n}\n\n.scene-card {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  display: flex;\n  height: 160px; /* 设置固定高度 */\n  overflow: hidden;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  border: 1px solid #ebeef5;\n  position: relative;\n}\n\n.scene-card:hover {\n  transform: translateY(-6px);\n  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);\n  border-color: #d0e2ff;\n}\n\n.scene-card:before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #67C23A, #95D475);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.scene-card:hover:before {\n  opacity: 1;\n}\n\n.scene-status {\n  width: 20px; /* 增加宽度确保圆圈完全显示 */\n  background: linear-gradient(to bottom, #f0f2f5, #e6e6e6);\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: flex-start;\n  padding-top: 16px; /* 使用内边距代替margin */\n}\n\n.status-indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n/* 使用伪元素显示提示文本 */\n.status-indicator::after {\n  content: attr(data-status);\n  position: absolute;\n  left: 20px;\n  top: -4px;\n  background-color: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  z-index: 10;\n  opacity: 0;\n  visibility: hidden;\n  transition: opacity 0.3s ease, visibility 0.3s ease;\n  pointer-events: none;\n}\n\n/* 鼠标悬停时显示提示 */\n.status-indicator:hover::after {\n  opacity: 1;\n  visibility: visible;\n}\n\n/* 状态指示器颜色 */\n.status-indicator.ready {\n  background-color: #67C23A;\n  box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.2);\n}\n\n.status-indicator.pending {\n  background-color: #E6A23C;\n  box-shadow: 0 0 0 4px rgba(230, 162, 60, 0.2);\n}\n\n.status-indicator.success {\n  background-color: #67C23A;\n  box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.2);\n}\n\n.status-indicator.failure {\n  background-color: #F56C6C;\n  box-shadow: 0 0 0 4px rgba(245, 108, 108, 0.2);\n}\n\n.status-indicator.running {\n  background-color: #409EFF;\n  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);\n  animation: pulse 1.5s infinite;\n}\n\n.status-indicator.not-configured {\n  background-color: #909399;\n  box-shadow: 0 0 0 4px rgba(144, 147, 153, 0.2);\n}\n\n/* 悬停效果 */\n.scene-card:hover .status-indicator.ready,\n.scene-card:hover .status-indicator.success {\n  box-shadow: 0 0 0 6px rgba(103, 194, 58, 0.25);\n}\n\n.scene-card:hover .status-indicator.pending {\n  box-shadow: 0 0 0 6px rgba(230, 162, 60, 0.25);\n}\n\n.scene-card:hover .status-indicator.failure {\n  box-shadow: 0 0 0 6px rgba(245, 108, 108, 0.25);\n}\n\n.scene-card:hover .status-indicator.running {\n  box-shadow: 0 0 0 6px rgba(64, 158, 255, 0.25);\n}\n\n.scene-card:hover .status-indicator.not-configured {\n  box-shadow: 0 0 0 6px rgba(144, 147, 153, 0.25);\n}\n\n.scene-card-content {\n  flex: 1;\n  padding: 18px;\n  display: flex;\n  flex-direction: column;\n  background: linear-gradient(135deg, #ffffff 0%, #f9fbfd 100%);\n}\n\n.scene-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 10px;\n}\n\n.scene-card-header h4 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2329;\n  line-height: 24px;\n  max-width: 85%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  position: relative;\n  padding-bottom: 6px;\n}\n\n.scene-card-header h4:after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 30px;\n  height: 2px;\n  background-color: #409EFF;\n  transition: width 0.3s ease;\n}\n\n.scene-card:hover .scene-card-header h4:after {\n  width: 100%;\n}\n\n.scene-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 28px;\n  height: 28px;\n  border-radius: 6px;\n  color: #606266;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: transparent;\n}\n\n.scene-more:hover {\n  background-color: rgba(64, 158, 255, 0.1);\n  color: #409EFF;\n}\n\n.scene-card-description {\n  flex: 1;\n  margin-bottom: 12px;\n  font-size: 14px;\n  color: #606266;\n  display: -webkit-box;\n  -webkit-line-clamp: 3; /* 限制最多显示3行 */\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  line-height: 1.5;\n  position: relative;\n  background: linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 75%, rgba(249,251,253,1) 100%);\n  padding-bottom: 5px;\n  max-height: 63px; /* 设置最大高度，约等于3行文字高度 */\n}\n\n.scene-card-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n  color: #909399;\n  padding-top: 8px;\n  border-top: 1px dashed rgba(0, 0, 0, 0.05);\n}\n\n.scene-steps {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  background-color: rgba(64, 158, 255, 0.05);\n  padding: 3px 8px;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n}\n\n.scene-card:hover .scene-steps {\n  background-color: rgba(64, 158, 255, 0.1);\n}\n\n.scene-actions {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.scene-number {\n  font-size: 12px;\n  color: #409EFF;\n  padding: 3px 10px;\n  border-radius: 12px;\n  background-color: rgba(64, 158, 255, 0.1);\n  font-weight: 500;\n  letter-spacing: 0.5px;\n  transition: all 0.3s ease;\n}\n\n.scene-card:hover .scene-number {\n  background-color: rgba(64, 158, 255, 0.2);\n  box-shadow: 0 2px 5px rgba(64, 158, 255, 0.15);\n}\n\n.delete-scene-btn {\n  opacity: 0;\n  transform: scale(0.8);\n  transition: all 0.3s ease;\n  border: none;\n  padding: 4px;\n  background: rgba(245, 108, 108, 0.1);\n  color: #F56C6C;\n}\n\n.scene-card:hover .delete-scene-btn {\n  opacity: 1;\n  transform: scale(1);\n}\n\n.delete-scene-btn:hover {\n  background: rgba(245, 108, 108, 0.2);\n  color: #F56C6C;\n  transform: scale(1.1);\n}\n\n/* 右侧数据区域 */\n.data-container {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  overflow-y: visible; /* 避免整体滚动 */\n  padding-right: 8px;\n  height: auto; /* 自适应高度 */\n  flex: 1; /* 占用可用空间 */\n}\n\n.data-overview {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 16px;\n}\n\n.data-card {\n  background: linear-gradient(135deg, #fff 0%, #f9fbfd 100%);\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  padding: 20px;\n  text-align: center;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 120px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.data-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\n}\n\n.data-card:before {\n  content: '';\n  position: absolute;\n  width: 100%;\n  height: 4px;\n  top: 0;\n  left: 0;\n  transition: all 0.3s ease;\n}\n\n.pass-rate:before {\n  background: linear-gradient(90deg, #67C23A, #95D475);\n}\n\n.total-runs:before {\n  background: linear-gradient(90deg, #409EFF, #79bbff);\n}\n\n.success-runs:before {\n  background: linear-gradient(90deg, #67C23A, #95D475);\n}\n\n.data-value {\n  font-size: 28px;\n  font-weight: 600;\n  margin-bottom: 10px;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.data-label {\n  font-size: 14px;\n  color: #606266;\n  position: relative;\n  padding-bottom: 4px;\n}\n\n.data-label:after {\n  content: '';\n  position: absolute;\n  width: 20px;\n  height: 2px;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  transition: width 0.3s ease;\n}\n\n.pass-rate .data-label:after {\n  background-color: #67C23A;\n}\n\n.total-runs .data-label:after {\n  background-color: #409EFF;\n}\n\n.success-runs .data-label:after {\n  background-color: #67C23A;\n}\n\n.data-card:hover .data-label:after {\n  width: 40px;\n}\n\n.chart-section {\n  background: linear-gradient(135deg, #fff 0%, #f9fbfd 100%);\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  padding: 20px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  height: 350px; /* 保持固定高度 */\n  flex-shrink: 0; /* 防止被压缩 */\n}\n\n.chart-section:hover {\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);\n}\n\n.chart-section:before {\n  content: '';\n  position: absolute;\n  width: 100%;\n  height: 4px;\n  top: 0;\n  left: 0;\n  background: linear-gradient(90deg, #409EFF, #79bbff);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.chart-section:hover:before {\n  opacity: 1;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding-bottom: 12px;\n  border-bottom: 1px dashed rgba(0, 0, 0, 0.05);\n}\n\n.section-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2329;\n  position: relative;\n  padding-left: 12px;\n}\n\n.section-header h3:before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 3px;\n  height: 14px;\n  background: linear-gradient(to bottom, #409EFF, #53a8ff);\n  border-radius: 2px;\n}\n\n.chart-legend {\n  display: flex;\n  gap: 12px;\n  background-color: rgba(64, 158, 255, 0.05);\n  padding: 4px 12px;\n  border-radius: 20px;\n}\n\n.legend-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 12px;\n  color: #606266;\n}\n\n.legend-color {\n  width: 14px;\n  height: 6px;\n  border-radius: 3px;\n}\n\n.legend-color.success {\n  background: linear-gradient(90deg, #67C23A, #95D475);\n}\n\n.chart-container-wrapper {\n  height: 220px;\n  margin-top: 12px;\n  padding: 8px;\n  background-color: rgba(255, 255, 255, 0.5);\n  border-radius: 8px;\n  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.02);\n}\n\n.chart-container {\n  height: 100%;\n  width: 100%;\n}\n\n.records-section {\n  background: linear-gradient(135deg, #fff 0%, #f9fbfd 100%);\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  padding: 20px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  flex: 1; /* 占用剩余空间 */\n}\n\n.records-container {\n  flex: 1;\n  overflow: auto; /* 允许所有方向滚动 */\n  position: relative;\n  /* 增加内边距防止内容贴边 */\n  padding-bottom: 10px;\n}\n\n.records-table {\n  margin-top: 12px;\n  border-radius: 8px;\n  width: 100%;\n  min-width: 100%; /* 确保表格至少占满容器宽度 */\n}\n\n/* 表格容器样式优化 */\n.el-table {\n  --el-table-header-background-color: rgba(64, 158, 255, 0.05);\n  --el-table-border-color: rgba(0, 0, 0, 0.03);\n  overflow: visible !important; /* 允许表格内容溢出 */\n  border-radius: 8px;\n}\n\n.el-table::before {\n  display: none;\n}\n\n.el-table th.el-table__cell {\n  background-color: rgba(64, 158, 255, 0.05);\n  color: #606266;\n  font-weight: 600;\n}\n\n.el-table .el-table__row:hover > td.el-table__cell {\n  background-color: rgba(64, 158, 255, 0.05);\n}\n\n/* 确保表格内容可以横向滚动 */\n.records-container .el-table__body-wrapper {\n  overflow-x: auto !important; /* 强制横向滚动 */\n}\n\n/* 状态和时间单元格样式 */\n.time-cell {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  justify-content: flex-start;\n  color: #606266;\n}\n\n.status-cell {\n  display: flex;\n  justify-content: center;\n}\n\n.status-badge {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 3px 10px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\n}\n\n.status-badge.running {\n  color: #E6A23C;\n  background-color: rgba(230, 162, 60, 0.15);\n}\n\n.status-badge.completed {\n  color: #67C23A;\n  background-color: rgba(103, 194, 58, 0.15);\n}\n\n.status-indicator-dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: currentColor;\n  animation: pulse 1.5s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.5;\n    transform: scale(1.1);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n.progress-wrapper {\n  padding: 0 8px;\n}\n\n.progress-counts {\n  display: flex;\n  justify-content: flex-start;\n  margin-bottom: 4px;\n  font-size: 12px;\n  gap: 4px;\n  align-items: baseline;\n}\n\n.success-count {\n  color: #67C23A;\n  font-weight: bold;\n  font-size: 14px;\n}\n\n.total-count {\n  color: #909399;\n}\n\n.progress-percentage {\n  color: #606266;\n  margin-left: auto;\n}\n\n.progress-pending {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n  color: #909399;\n}\n\n/* 对话框样式 */\n.plan-dialog :deep(.el-dialog__body) {\n  padding-top: 0;\n}\n\n/* 响应式调整 */\n@media screen and (max-width: 1400px) {\n  .content-layout {\n    grid-template-columns: 1fr;\n  }\n  \n  .data-container {\n    flex-direction: row;\n    flex-wrap: wrap;\n    overflow-y: visible;\n    overflow-x: hidden;\n  }\n  \n  .data-overview {\n    flex-basis: 100%;\n  }\n  \n  .chart-section {\n    flex-basis: calc(50% - 10px);\n    height: 350px; /* 保持固定高度 */\n  }\n  \n  .records-section {\n    flex-basis: calc(50% - 10px);\n    min-height: 350px;\n  }\n}\n\n@media screen and (max-width: 1200px) {\n  .dashboard-main {\n    flex-direction: column;\n    overflow-y: auto; /* 确保主内容区域可滚动 */\n  }\n  \n  .plans-sidebar {\n    width: 100%;\n    height: auto;\n    max-height: 300px;\n    overflow-y: auto; /* 确保侧边栏可滚动 */\n  }\n  \n  .plans-list {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n    gap: 8px;\n    max-height: none; /* 移除最大高度限制 */\n    overflow-y: visible; /* 不再需要单独滚动 */\n  }\n  \n  .plan-actions {\n    display: flex;\n  }\n  \n  .chart-section {\n    height: 350px; /* 保持固定高度 */\n  }\n  \n  .records-section {\n    min-height: 350px;\n  }\n}\n\n@media screen and (max-width: 768px) {\n  .dashboard-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 16px;\n  }\n  \n  .dashboard-actions {\n    width: 100%;\n    justify-content: space-between;\n  }\n  \n  .scenes-grid {\n    grid-template-columns: 1fr;\n    max-height: none; /* 移除最大高度限制 */\n  }\n  \n  .data-overview {\n    grid-template-columns: 1fr;\n  }\n  \n  .data-container {\n    flex-direction: column;\n    overflow-y: visible;\n  }\n  \n  .chart-section {\n    flex-basis: 100%;\n    height: 350px; /* 保持固定高度 */\n  }\n  \n  .records-section {\n    flex-basis: 100%;\n    min-height: 350px;\n  }\n}\n\n/* 按钮图标与文字对齐修复 */\n.el-button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.el-button .el-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 添加场景按钮和新建计划按钮样式 */\n.add-scene-btn, .add-plan-btn {\n  padding: 8px 14px;\n  display: inline-flex;\n  align-items: center;\n  gap: 6px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.add-scene-btn:hover, .add-plan-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.15);\n}\n\n/* 表格容器样式优化 */\n.el-table {\n  --el-table-header-background-color: rgba(64, 158, 255, 0.05);\n  --el-table-border-color: rgba(0, 0, 0, 0.03);\n  overflow: hidden;\n  border-radius: 8px;\n}\n\n.el-table::before {\n  display: none;\n}\n\n.el-table th.el-table__cell {\n  background-color: rgba(64, 158, 255, 0.05);\n  color: #606266;\n  font-weight: 600;\n}\n\n.el-table .el-table__row:hover > td.el-table__cell {\n  background-color: rgba(64, 158, 255, 0.05);\n}\n\n/* 状态徽章增强 */\n.status-badge {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 3px 10px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\n}\n\n.status-badge.running {\n  color: #E6A23C;\n  background-color: rgba(230, 162, 60, 0.15);\n}\n\n.status-badge.completed {\n  color: #67C23A;\n  background-color: rgba(103, 194, 58, 0.15);\n}\n\n/* 修复进度条样式 */\n.el-progress-bar__outer {\n  border-radius: 4px;\n  background-color: rgba(0, 0, 0, 0.04);\n}\n\n.el-progress-bar__inner {\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n/* 修正按钮图标对齐 */\n.run-button, .add-plan-btn, .add-scene-btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n.run-button .el-icon,\n.add-plan-btn .el-icon,\n.add-scene-btn .el-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 4px;\n}\n\n/* 确保表格可以水平滚动的样式 */\n:deep(.el-table__body-wrapper) {\n  overflow-x: auto !important;\n}\n\n:deep(.el-table) {\n  width: 100%;\n  overflow: visible !important;\n}\n\n:deep(.el-table__inner-wrapper) {\n  overflow: visible !important;\n}\n</style>\n", "<template>\n  <el-dialog v-model=\"sceneDlg\" title=\"添加场景\" width=\"75%\" :before-close=\"clickClear\" top=\"0\">\n  <div style=\"margin-left: 20px\">\n    <!--    顶部功能-->\n    <div >\n      <el-input style=\"width: 330px\" v-model=\"filterText\" placeholder=\"请输入用例名称进行搜索\" clearable>\n        <template #append>\n          <el-button type=\"primary\" @click=\"searchClick\">查询</el-button>\n        </template>\n      </el-input>\n      <span>\n        <el-button\n          type=\"warning\"\n          style=\"float: right;margin-right: 19px\"\n          @click=\"clickClear\"\n          :icon=\"Close\"\n            >关闭窗口\n        </el-button>\n        <el-button\n          type=\"primary\"\n          style=\"float: right;margin-right: 15px\"\n          @click=\"addScentToPlan\"\n          :icon=\"Check\"\n            >确认选择\n        </el-button>\n      </span>\n    </div>\n    <!--    表格功能-->\n    <el-scrollbar height=\"calc(100vh - 110px)\">\n    <div style=\"margin-top: 15px;margin-right: 20px\">\n      <el-table :data=\"caseList\"  stripe empty-text=\"暂无数据\" border @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" align=\"center\" width=\"50\"></el-table-column>\n            <el-table-column label=\"序号\" align=\"center\" width=\"60\">\n              <template #default=\"scope\">\n                <span>{{ scope.$index + 1 }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"用例名称\" align=\"center\" >\n              <template #default=\"scope\">\n                <router-link class=\"no-underline\" :to=\"`/TestCaseDetail/`\" style=\"color: #409eff\" @click=\"clickEdit(scope.row)\">{{ scope.row.name }}</router-link>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"所属项目\" prop=\"project.name\"  align=\"center\" />\n            <el-table-column label=\"步骤数\" width=\"70\" prop=\"stepCount\" align=\"center\"/>\n            <el-table-column label=\"用例描述\" prop=\"desc\" align=\"center\" >\n              <template #default=\"scope\">\n              <el-tooltip class=\"item\" effect=\"dark\" :content=\"scope.row.desc\" placement=\"top\">\n                <div v-if=\"scope.row.desc.length >16\" >{{ scope.row.desc.slice(0, 16) }}...</div>\n                <div v-else>{{ scope.row.desc }}</div>\n              </el-tooltip>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"创建人\" prop=\"creator\" align=\"center\"  width=\"120\"/>\n            <el-table-column label=\"创建时间\" align=\"center\" width=\"160\">\n              <template #default=\"scope\">\n                {{ $tools.rTime(scope.row.create_time) }}\n              </template>\n            </el-table-column>\n            <el-table-column label=\"更新人\" prop=\"modifier\" align=\"center\" width=\"120\"/>\n            <el-table-column label=\"更新时间\" align=\"center\" width=\"160\">\n              <template #default=\"scope\">\n                <a v-if=\"scope.row.update_time\">{{$tools.rTime(scope.row.update_time)}}</a>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" width=\"330\" align=\"center\">\n              <template #default=\"scope\">\n                <el-button @click=\"runCase(scope.row)\" :size=\"'small'\" type=\"primary\" :icon=\"Promotion\">运行</el-button>\n                <el-button @click=\"clickEdit(scope.row)\" :size=\"'small'\" type=\"warning\" :icon=\"Menu\">添加管理步骤</el-button>\n                <el-button @click=\"delCase(scope.row.id)\" :size=\"'small'\" type=\"danger\" plain :icon=\"Delete\">删除</el-button>\n              </template>\n            </el-table-column>\n    </el-table>\n    </div>\n    <div class=\"pagination-container\">\n      <el-pagination  background layout=\"total, prev, pager, next, jumper\"\n                    @current-change=\"currentPages\"\n                    :default-page-size=\"100\"\n                    :total=\"pages.count\"\n                    :current-page=\"pages.current\"\n                   next-text=\"下一页\" prev-text=\"上一页\">\n      </el-pagination>\n    </div>\n    </el-scrollbar>\n  </div>\n  </el-dialog>\n  <!-- 显示运行结果 -->\n  <el-drawer v-model=\"ResultDlg\" :with-header=\"false\" size=\"50%\">\n\t\t<div style=\"padding:20px;\">\n\t\t\t<el-descriptions title=\"执行结果\" border :column=\"4\" style=\"text-align: center;\">\n\t\t\t\t<el-descriptions-item label=\"总数\" ><b style=\"color: #00aaff\">{{ runScentResult.all }}</b></el-descriptions-item>\n\t\t\t\t<el-descriptions-item label=\"通过\"><b style=\"color: #00aa7f\">{{ runScentResult.success }}</b></el-descriptions-item>\n\t\t\t\t<el-descriptions-item label=\"失败\"><b style=\"color: orangered\">{{ runScentResult.fail }}</b></el-descriptions-item>\n\t\t\t\t<el-descriptions-item label=\"错误\"><b style=\"color: #fca130\">{{ runScentResult.error }}</b></el-descriptions-item>\n\t\t\t</el-descriptions>\n\t\t\t<div style=\"height: 40px;line-height: 40px;\"><b>执行详情</b></div>\n\t\t\t<el-scrollbar height=\"calc(100vh - 180px)\">\n\t\t\t\t<el-table :data=\"runScentResult.cases\" style=\"width: 100%\" empty-text=\"暂无数据\">\n\t\t\t\t\t<el-table-column type=\"expand\">\n\t\t\t\t\t\t<template #default=\"props\">\n\t\t\t\t\t\t\t<caseResult :result=\"props.row\"></caseResult>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column label=\"步骤名\" prop=\"name\" />\n\t\t\t\t\t<el-table-column label=\"请求方法\" prop=\"method\">\n            <template #default=\"props\">\n               <span v-if=\"props.row.type === 'api'\">{{ props.row.method }}</span>\n\t\t\t\t\t\t</template>\n          </el-table-column>\n\t\t\t\t\t<el-table-column label=\"响应状态码\" prop=\"status_cede\">\n            <template #default=\"props\">\n               <span v-if=\"props.row.type === 'api'\">{{ props.row.status_cede }}</span>\n\t\t\t\t\t\t</template>\n          </el-table-column>\n\t\t\t\t\t<el-table-column label=\"执行结果\" prop=\"state\" min-width=\"40px\">\n\t\t\t\t\t\t<template #default=\"props\">\n\t\t\t\t\t\t\t<span v-if=\"props.row.state == '成功'\" style=\"color: #00AA7F;\">{{ props.row.state }}</span>\n              <span v-else-if=\"props.row.state == '错误'\" style=\"color: #fca130;\">{{ props.row.state }}</span>\n\t\t\t\t\t\t\t<span v-else style=\"color:#F56C6C\">{{ props.row.state }}</span>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t</el-table>\n\t\t\t</el-scrollbar>\n\t\t</div>\n\t</el-drawer>\n</template>\n\n<script>\nimport {mapMutations, mapState} from \"vuex\";\nimport caseResult from '../../components/common/caseResult.vue';\nimport { ElNotification, ElMessage, ElMessageBox } from \"element-plus\";\nimport { Check, Close, Delete, Menu, Promotion } from '@element-plus/icons-vue';\n\nexport default {\n  props: {\n    planId: String\n  },\n  computed: {\n    ...mapState(['pro','envId']),\n  },\n  components:{\n    caseResult\n  },\n  data(){\n    return {\n      filterText:'',\n      pages: [],\n      caseList: [],\n      ResultDlg:false,\n      runScentResult:'',\n      sceneDlg:true,\n      selectedRows: [],\n      Check,\n      Close,\n      Delete,\n      Menu,\n      Promotion\n    }\n  },\n methods: {\n    ...mapMutations(['CaseInfo']),\n    // 查询\n   searchClick() {\n     this.allTestCase(this.pro.id,this.pages.current,this.filterText)\n   },\n\n   // 点击编辑用例\n   clickEdit(id) {\n   this.$router.push({ name: 'TestCaseDetail' });\n   this.CaseInfo(id)\n   },\n\n   // 用例列表\n   async allTestCase(project,page,name) {\n     const response =await this.$api.getTestCase(project,page,name)\n     if (response.status ===200){\n        // 将获取到的测试用例数据存储在 caseList 中\n\t\t\t\tconst allCase = response.data.result;\n\t\t\t\tthis.pages = response.data\n        // 调用接口获取已存在于计划中的场景列表\n        const existingScenesResponse  = await this.$api.getTestCase_({\n\t\t\t\ttestplan: this.planId\n\t\t\t});\n        if (existingScenesResponse.status === 200) {\n              const existingScenes = existingScenesResponse.data.result;\n              // 过滤掉已存在于计划中的场景\n              this.caseList = allCase.filter(item => !existingScenes.map(scene => scene.id).includes(item.id));\n              // 过滤掉 stepCount 为 0 的数据\n              this.caseList = this.caseList.filter(item => item.stepCount !== 0);\n              this.pages.count =  this.caseList.length\n        }\n\t\t\t}\n   },\n\n   currentPages(currentPage) {\n      this.allTestCase(this.pro.id,currentPage)\n      this.pages.current = currentPage\n\n  },\n\n   // 点击删除\n   delCase(id) {\n    ElMessageBox.confirm('此操作将永久删除该用例, 是否继续?', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning'\n    })\n      .then(async () => {\n        const response = await this.$api.delTestCase(id)\n        if(response.status ===204){\n          ElMessage({\n            type: 'success',\n            message: '删除成功!'\n          });\n          // 刷新页面\n          this.allTestCase(this.pro.id);\n          this.filterText = ''\n        }\n      })\n      .catch(() => {\n        ElMessage({\n          type: 'info',\n          message: '已取消删除'\n        });\n      });\n  },\n\n  // 运行测试用例\n  async runCase(data) {\n      const stepCount = parseInt(data.stepCount);\n      console.log(stepCount)\n      if(stepCount > 0){\n\t\t\tif (this.envId) {\n\t\t\t\tconst params = {\n\t\t\t\t\tenv: this.envId,\n\t\t\t\t\tscene: data.id\n\t\t\t\t};\n          ElNotification({\n            title: '开始运行',\n            message: '运行过程中请稍等片刻噢',\n            type: 'success',\n            duration:1000\n          });\n\t\t\t\tconst response = await this.$api.runCases(data.id, params);\n\t\t\t\tif (response.status == 200) {\n\t\t\t\t\t// 显示执行结果到窗口页面\n\t\t\t\t\tthis.runScentResult = response.data;\n\t\t\t\t\tthis.ResultDlg = true;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tElMessage({\n\t\t\t\t\ttype: 'warning',\n\t\t\t\t\tmessage: '当前未选中执行环境!',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t}\n\t\t}else ElMessage({\n\t\t\t\t\ttype: 'warning',\n\t\t\t\t\tmessage: '请添加步骤后再运行',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t  },\n\n  closeModal() {\n    this.$emit('close-modal');\n  },\n  // 点击取消\n  clickClear(){\n    this.closeModal()\n  },\n  // 添加选中的测试场景到测试计划中\n  async addScentToPlan(){\n      if (this.selectedRows.length === 0) {\n        ElMessage({\n          type: 'warning',\n          message: '请勾选要添加的测试场景',\n          duration: 1000\n        });\n      return; // 如果没有勾选场景，则不执行后续操作\n      }\n\t\t\tlet params = {}\n      params.scene_ids = [...this.selectedRows]\n\t\t\tconst response = await this.$api.createTestPlanScene(this.planId,params)\n\t\t\tif(response.status===200){\n\t\t\t\tElMessage({\n\t\t\t\t\ttype: 'success',\n\t\t\t\t\tmessage: '添加成功',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t}\n      this.closeModal()\n\t\t},\n\n\n  handleSelectionChange(val) {\n      this.selectedRows = val.map(row => row.id);\n      console.log(this.selectedRows)\n    },\n\n },\n created() {\n  this.allTestCase(this.pro.id);\n},\n\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n}\n.no-underline {\n  text-decoration: none;\n}\n</style>\n", "import { render } from \"./TestCaseDlg.vue?vue&type=template&id=2d1b3e38&scoped=true\"\nimport script from \"./TestCaseDlg.vue?vue&type=script&lang=js\"\nexport * from \"./TestCaseDlg.vue?vue&type=script&lang=js\"\n\nimport \"./TestCaseDlg.vue?vue&type=style&index=0&id=2d1b3e38&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-2d1b3e38\"]])\n\nexport default __exports__", "import { render } from \"./TestPlanNew.vue?vue&type=template&id=32196f23&scoped=true\"\nimport script from \"./TestPlanNew.vue?vue&type=script&lang=js\"\nexport * from \"./TestPlanNew.vue?vue&type=script&lang=js\"\n\nimport \"./TestPlanNew.vue?vue&type=style&index=0&id=32196f23&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-32196f23\"]])\n\nexport default __exports__"], "names": ["class", "style", "_createVNode", "_component_el_tabs", "type", "value", "size", "$props", "result", "_createBlock", "_component_el_tab_pane", "label", "name", "response_header", "_createElementBlock", "_hoisted_1", "includes", "_hoisted_2", "_component_Editor", "readOnly", "response_body", "$event", "lang", "theme", "_hoisted_3", "_component_el_scrollbar", "height", "onWheel", "_cache", "_withModifiers", "innerHTML", "_hoisted_4", "_Fragment", "_renderList", "key", "_component_el_tag", "_createElementVNode", "_hoisted_5", "_toDisplayString", "requests_body", "_hoisted_6", "_component_el_collapse", "$data", "activeNames", "_component_el_collapse_item", "title", "_withCtx", "method", "url", "requests_header", "_hoisted_7", "log_data", "item", "index", "_hoisted_8", "disabled", "state", "_hoisted_9", "_hoisted_10", "_hoisted_11", "status_cede", "_hoisted_12", "_hoisted_13", "run_time", "showbtn", "_hoisted_14", "_component_el_button", "onClick", "$options", "getInterfaces", "plain", "_component_el_dialog", "addBugDlg", "width", "closeDialogResult", "footer", "_hoisted_15", "saveBug", "_component_el_form", "model", "bugForm", "_component_el_form_item", "_component_el_select", "interface", "placeholder", "interfaces", "iter", "_component_el_option", "id", "_component_el_input", "autosize", "minRows", "maxRows", "desc", "autocomplete", "props", "default", "computed", "mapState", "components", "Editor", "data", "info", "status", "methods", "this", "project", "pro", "response", "$api", "createBugs", "$message", "message", "duration", "getNewInterfaces", "__exports__", "render", "ref", "filterText", "clearable", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handletreeClick", "append", "_component_el_icon", "_component_Search", "scene_list", "length", "planId", "runPlan", "addPlan", "planList", "_component_el_empty", "description", "_normalizeClass", "active", "selectPlan", "_component_Folder", "_component_el_tooltip", "content", "placement", "editPlan", "_component_Edit", "delPlan", "_component_Delete", "currentPlanName", "_hoisted_16", "_hoisted_17", "clickAddScene", "_hoisted_18", "clickView", "_hoisted_20", "getSceneStatusClass", "getSceneStatusText", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_component_Document", "stepCount", "_hoisted_27", "_hoisted_28", "circle", "delScene", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "getAveragePassRate", "_hoisted_33", "_hoisted_34", "records", "_hoisted_35", "_hoisted_36", "getSuccessfulRuns", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_component_el_table", "stripe", "_component_el_table_column", "scope", "_hoisted_43", "_component_Timer", "_ctx", "$tools", "rTime", "row", "create_time", "effect", "env_name", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_component_Check", "_hoisted_47", "_hoisted_48", "_hoisted_49", "success", "_hoisted_50", "all", "_hoisted_51", "pass_rate", "_component_el_progress", "percentage", "parseFloat", "color", "getProgressColor", "_hoisted_52", "_component_Loading", "fixed", "link", "$router", "push", "params", "loading", "editDlg", "planForm", "clickClear", "top", "_hoisted_53", "savePlan", "prop", "rules", "required", "trigger", "autofocus", "sceneDlg", "_component_TestCase", "onCloseModal", "handleCloseModal", "searchClick", "icon", "Close", "addScentToPlan", "Check", "caseList", "border", "onSelectionChange", "handleSelectionChange", "align", "$index", "_component_router_link", "to", "clickEdit", "slice", "update_time", "runCase", "Promotion", "<PERSON><PERSON>", "delCase", "Delete", "_component_el_pagination", "background", "layout", "onCurrentChange", "currentPages", "total", "pages", "count", "current", "_component_el_drawer", "ResultDlg", "_component_el_descriptions", "column", "_component_el_descriptions_item", "runScentResult", "fail", "error", "cases", "_component_caseResult", "String", "caseResult", "selectedRows", "mapMutations", "allTestCase", "CaseInfo", "page", "getTestCase", "allCase", "existingScenesResponse", "getTestCase_", "testplan", "existingScenes", "filter", "map", "scene", "currentPage", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "async", "delTestCase", "ElMessage", "catch", "parseInt", "console", "log", "envId", "env", "ElNotification", "runCases", "closeModal", "$emit", "scene_ids", "createTestPlanScene", "val", "created", "Icon", "TestCase", "themeColors", "warning", "danger", "defaultProps", "children", "chartData", "runDate", "passRate", "toFixed", "reverse", "getAllPlan", "getTestPlans", "find", "plan", "updateCurrentPlanName", "currentPlan", "getAllRecord", "getTestRecord", "getScenes", "handleNodeClick", "updateTestPlan", "createTestPlan", "types", "deleteTestPlan", "back_type", "handleSceneCommand", "command", "scene_id", "deleteTestPlanScene", "completedRecords", "record", "totalPassRate", "reduce", "sum", "rate", "lastResult", "watch", "$nextTick", "$refs", "chartTable", "$chart", "chart3"], "sourceRoot": ""}