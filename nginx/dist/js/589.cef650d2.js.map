{"version": 3, "file": "js/589.cef650d2.js", "mappings": "iNAESA,MAAM,uB,GAGJA,MAAM,8B,GACJA,MAAM,a,GAGNA,MAAM,gB,GACJA,MAAM,c,GAKVA,MAAM,8B,GACJA,MAAM,a,GAGNA,MAAM,gB,GACJA,MAAM,c,GAKVA,MAAM,8B,GACJA,MAAM,a,GAGNA,MAAM,gB,GACJA,MAAM,c,GAKVA,MAAM,2B,GACJA,MAAM,a,GAGNA,MAAM,gB,GACJA,MAAM,c,GAMVA,MAAM,0B,GAEJA,MAAM,c,GACJA,MAAM,e,GAEJA,MAAM,iB,GAURA,MAAM,gBAAgBC,IAAI,a,GAI5BD,MAAM,e,GAIJA,MAAM,kB,GAiBFA,MAAM,kB,GAcdA,MAAM,c,GACJA,MAAM,2B,GAEJA,MAAM,kB,GAqBFA,MAAM,a,GASNA,MAAM,a,GAuCNA,MAAM,a,SAS4BA,MAAM,kB,SAIjCA,MAAM,oB,GACXA,MAAM,gB,GAQNA,MAAM,gB,GACJA,MAAM,kB,GAINA,MAAM,mB,GAcVA,MAAM,kB,SASIA,MAAM,iB,guBA7NjCE,EAAAA,EAAAA,IAsOeC,GAAA,CAtODC,OAAO,QAAM,C,iBACzB,IAoOI,EApOJC,EAAAA,EAAAA,IAoOI,MApOJC,EAoOI,EAlOJD,EAAAA,EAAAA,IAwCM,OAxCDL,OAAKO,EAAAA,EAAAA,IAAA,CAAC,kBAAiB,iBAA4BC,EAAAC,c,EACtDJ,EAAAA,EAAAA,IAQM,MARNK,EAQM,EAPJL,EAAAA,EAAAA,IAEM,MAFNM,EAEM,EADJC,EAAAA,EAAAA,IAA+BC,EAAA,M,iBAAtB,IAAY,EAAZD,EAAAA,EAAAA,IAAYE,K,SAEvBT,EAAAA,EAAAA,IAGM,MAHNU,EAGM,EAFJV,EAAAA,EAAAA,IAAgD,MAAhDW,GAAgDC,EAAAA,EAAAA,IAArBC,EAAAC,cAAY,G,aACvCd,EAAAA,EAAAA,IAAmC,OAA9BL,MAAM,cAAa,SAAK,SAIjCK,EAAAA,EAAAA,IAQM,MARNe,EAQM,EAPJf,EAAAA,EAAAA,IAEM,MAFNgB,EAEM,EADJT,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,IAASU,K,SAEpBjB,EAAAA,EAAAA,IAGM,MAHNkB,EAGM,EAFJlB,EAAAA,EAAAA,IAAgD,MAAhDmB,GAAgDP,EAAAA,EAAAA,IAArBC,EAAAO,cAAY,G,aACvCpB,EAAAA,EAAAA,IAAkC,OAA7BL,MAAM,cAAa,QAAI,SAIhCK,EAAAA,EAAAA,IAQM,MARNqB,EAQM,EAPJrB,EAAAA,EAAAA,IAEM,MAFNsB,EAEM,EADJf,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWgB,K,SAEtBvB,EAAAA,EAAAA,IAGM,MAHNwB,EAGM,EAFJxB,EAAAA,EAAAA,IAAgD,MAAhDyB,GAAgDb,EAAAA,EAAAA,IAArBC,EAAAa,cAAY,G,aACvC1B,EAAAA,EAAAA,IAAiC,OAA5BL,MAAM,cAAa,OAAG,SAI/BK,EAAAA,EAAAA,IAQM,MARN2B,EAQM,EAPJ3B,EAAAA,EAAAA,IAEM,MAFN4B,EAEM,EADJrB,EAAAA,EAAAA,IAA+BC,EAAA,M,iBAAtB,IAAY,EAAZD,EAAAA,EAAAA,IAAYsB,K,SAEvB7B,EAAAA,EAAAA,IAGM,MAHN8B,EAGM,EAFJ9B,EAAAA,EAAAA,IAAoD,MAApD+B,GAAoDnB,EAAAA,EAAAA,IAAzBC,EAAAmB,iBAAkB,IAAC,G,aAC9ChC,EAAAA,EAAAA,IAAmC,OAA9BL,MAAM,cAAa,SAAK,S,IAKjCK,EAAAA,EAAAA,IAmDM,MAnDNiC,EAmDM,EAjDJjC,EAAAA,EAAAA,IAcM,MAdNkC,EAcM,EAbJlC,EAAAA,EAAAA,IAWM,MAXNmC,EAWM,C,eAVJnC,EAAAA,EAAAA,IAAgB,UAAZ,WAAO,KACXA,EAAAA,EAAAA,IAQM,MARNoC,EAQM,EAPJ7B,EAAAA,EAAAA,IAMiB8B,EAAA,C,WANQlC,EAAAmC,U,qCAAAnC,EAAAmC,UAASC,GAAEC,KAAK,QAASC,SAAQ5B,EAAA6B,uB,kBACxD,IAAiD,EAAjDnC,EAAAA,EAAAA,IAAiDoC,EAAA,CAAhCC,MAAM,OAAK,C,iBAAC,IAAEC,EAAA,KAAAA,EAAA,K,QAAF,S,cAC7BtC,EAAAA,EAAAA,IAAmDoC,EAAA,CAAlCC,MAAM,QAAM,C,iBAAC,IAAGC,EAAA,KAAAA,EAAA,K,QAAH,U,cAC9BtC,EAAAA,EAAAA,IAAmDoC,EAAA,CAAlCC,MAAM,QAAM,C,iBAAC,IAAGC,EAAA,KAAAA,EAAA,K,QAAH,U,cAC9BtC,EAAAA,EAAAA,IAAqDoC,EAAA,CAApCC,MAAM,SAAO,C,iBAAC,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,cAC/BtC,EAAAA,EAAAA,IAAiDoC,EAAA,CAAhCC,MAAM,OAAK,C,iBAAC,IAAEC,EAAA,MAAAA,EAAA,M,QAAF,S,uDAInC7C,EAAAA,EAAAA,IAAiD,MAAjD8C,EAAiD,aAInD9C,EAAAA,EAAAA,IA+BM,MA/BN+C,EA+BM,C,eA9BJ/C,EAAAA,EAAAA,IAEM,OAFDL,MAAM,eAAa,EACtBK,EAAAA,EAAAA,IAAa,UAAT,U,KAENA,EAAAA,EAAAA,IA0BM,MA1BNgD,EA0BM,EAzBJzC,EAAAA,EAAAA,IAwBU0C,GAAA,CAxBD,iBAAe,MAAMT,KAAK,S,kBACjC,IAce,EAdfjC,EAAAA,EAAAA,IAce2C,GAAA,CAdDN,MAAM,OAAOjD,MAAM,mB,kBAC/B,IAYE,EAZFY,EAAAA,EAAAA,IAYE4C,GAAA,C,WAXShD,EAAAiD,S,qCAAAjD,EAAAiD,SAAQb,GACjBc,KAAK,gBACL,oBAAkB,OAClB,kBAAgB,OAChB,eAAa,sBACZ,eAAclD,EAAAmD,mBACdC,UAAWpD,EAAAoD,UACZ,kBAAgB,IACfC,WAAW,EACZ7D,MAAM,cACL8D,MAAO,CAAAC,SAAA,S,2DAGZ1D,EAAAA,EAAAA,IAOM,MAPN2D,EAOM,EANJpD,EAAAA,EAAAA,IAEYqD,GAAA,CAFDC,MAAA,GAAOC,QAAOjD,EAAAkD,W,kBACvB,IAA8B,EAA9BxD,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWyD,M,6BAAU,U,6BAEhCzD,EAAAA,EAAAA,IAEYqD,GAAA,CAFDP,KAAK,UAAWS,QAAOjD,EAAAoD,Y,kBAChC,IAA6B,EAA7B1D,EAAAA,EAAAA,IAA6BC,EAAA,M,iBAApB,IAAU,EAAVD,EAAAA,EAAAA,IAAU2D,M,6BAAU,U,6CASzClE,EAAAA,EAAAA,IAiIM,MAjINmE,EAiIM,EAhIJnE,EAAAA,EAAAA,IAOM,MAPNoE,EAOM,C,eANJpE,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAIM,MAJNqE,EAIM,EAHJ9D,EAAAA,EAAAA,IAEYqD,GAAA,CAFDpB,KAAK,QAAQa,KAAK,UAAUQ,MAAA,GAAOC,QAAOjD,EAAAyD,Y,kBACnD,IAA+B,EAA/B/D,EAAAA,EAAAA,IAA+BC,EAAA,M,iBAAtB,IAAY,EAAZD,EAAAA,EAAAA,IAAYgE,M,6BAAU,Y,sDAKrC1E,EAAAA,EAAAA,IAsHW2E,GAAA,CArHRC,KAAMtE,EAAAuE,QAEP,uBAAqB,YACrB,0BAAwB,kBACxB,6BAA2B,2BAC3B/E,MAAM,eACL,oBAAmB,CAAAgF,WAAA,UAAAC,MAAA,UAAAC,WAAA,OACpBC,OAAA,I,kBAEA,IAAqF,EAArFvE,EAAAA,EAAAA,IAAqFwE,GAAA,CAApE1B,KAAK,QAAQT,MAAM,KAAKoC,MAAM,KAAKC,MAAM,YAE1D1E,EAAAA,EAAAA,IAOkBwE,GAAA,CAPDnC,MAAM,OAAOqC,MAAM,SAAS,YAAU,O,CAC1CC,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpF,EAAAA,EAAAA,IAGM,MAHNqF,EAGM,EAFJ9E,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,IAAS+E,M,OAClBtF,EAAAA,EAAAA,IAAsD,aAAAY,EAAAA,EAAAA,IAA7C2E,EAAAC,OAAOC,MAAML,EAAMM,IAAIC,cAAW,O,OAKjDpF,EAAAA,EAAAA,IAOkBwE,GAAA,CAPDnC,MAAM,MAAMqC,MAAM,SAAS,YAAU,O,CACzCC,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpF,EAAAA,EAAAA,IAGM,MAHN4F,EAGM,EAFJrF,EAAAA,EAAAA,IAA4FsF,GAAA,CAAhFrD,KAAM,GAAI7C,MAAM,e,kBAAc,IAAsC,E,iBAAnCyF,EAAMM,IAAII,OAAOC,UAAU,EAAG,IAAJ,K,YACvE/F,EAAAA,EAAAA,IAAmC,aAAAY,EAAAA,EAAAA,IAA1BwE,EAAMM,IAAII,QAAM,O,OAK/BvF,EAAAA,EAAAA,IAMkBwE,GAAA,CANDnC,MAAM,KAAKqC,MAAM,SAAS,YAAU,O,CACxCC,SAAOC,EAAAA,EAAAA,IAGPC,GAHc,EACvB7E,EAAAA,EAAAA,IAESyF,GAAA,CAFDC,OAAO,OAAQ5C,KAAMxC,EAAAqF,WAAWd,EAAMM,IAAIS,UAAWxG,MAAM,W,kBACjE,IAAwB,E,iBAArByF,EAAMM,IAAIS,UAAQ,K,6BAK3B5F,EAAAA,EAAAA,IAoBkBwE,GAAA,CApBDnC,MAAM,OAAOqC,MAAM,SAAS,YAAU,O,CAC1CC,SAAOC,EAAAA,EAAAA,IAOPC,GAPc,CAEY,SAA3BA,EAAMM,IAAIU,e,WADlBvG,EAAAA,EAAAA,IAMSmG,GAAA,C,MAJPC,OAAO,QACP5C,KAAK,UACL1D,MAAM,Y,kBACN,IAA2B,EAA3BY,EAAAA,EAAAA,IAA2BC,EAAA,M,iBAAlB,IAAQ,EAARD,EAAAA,EAAAA,IAAQ8F,M,eAAU,KAACzF,EAAAA,EAAAA,IAAGwE,EAAMM,IAAIU,cAAY,K,YAGf,SAA3BhB,EAAMM,IAAIU,e,WADvBvG,EAAAA,EAAAA,IAMSmG,GAAA,C,MAJPC,OAAO,QACP5C,KAAK,UACL1D,MAAM,Y,kBACN,IAAiC,EAAjCY,EAAAA,EAAAA,IAAiCC,EAAA,M,iBAAxB,IAAc,EAAdD,EAAAA,EAAAA,IAAc+F,M,eAAU,KAAC1F,EAAAA,EAAAA,IAAGwE,EAAMM,IAAIU,cAAY,K,yBAE7DvG,EAAAA,EAAAA,IAESmG,GAAA,C,MAFMC,OAAO,QAAQ5C,KAAK,OAAO1D,MAAM,Y,kBAC9C,IAAqC,EAArCY,EAAAA,EAAAA,IAAqCC,EAAA,M,iBAA5B,IAAkB,EAAlBD,EAAAA,EAAAA,IAAkBgG,M,6BAAU,W,wBAK3ChG,EAAAA,EAAAA,IAOkBwE,GAAA,CAPDnC,MAAM,OAAOqC,MAAM,SAAS,YAAU,O,CAC1CC,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,EACvBpF,EAAAA,EAAAA,IAGM,MAHNwG,EAGM,EAFJjG,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWkG,M,OACpBzG,EAAAA,EAAAA,IAAsC,aAAAY,EAAAA,EAAAA,IAA7BwE,EAAMM,IAAIgB,WAAS,O,OAKlCnG,EAAAA,EAAAA,IA8BkBwE,GAAA,CA9BDnC,MAAM,OAAOqC,MAAM,SAAS,YAAU,O,CAC1CC,SAAOC,EAAAA,EAAAA,IAIVC,GAJiB,CACS,QAArBA,EAAMM,IAAIiB,S,WAArBC,EAAAA,EAAAA,IAGM,MAHNC,EAGMhE,EAAA,MAAAA,EAAA,MAFJ7C,EAAAA,EAAAA,IAA6B,OAAxBL,MAAM,aAAW,UACtBK,EAAAA,EAAAA,IAAqB,YAAf,YAAQ,S,WAEhB4G,EAAAA,EAAAA,IAsBM,MAtBNE,EAsBM,EArBJ9G,EAAAA,EAAAA,IAOM,MAPN+G,EAOM,EANJxG,EAAAA,EAAAA,IAKeyG,GAAA,CAJZC,WAAYC,OAAO9B,EAAMM,IAAIyB,WAC7BvC,MAAO/D,EAAAuG,uBAAuBhC,EAAMM,IAAIyB,WACxC,eAAc,EACd,aAAW,G,kCAGhBnH,EAAAA,EAAAA,IAYM,MAZNqH,EAYM,EAXJrH,EAAAA,EAAAA,IAGM,MAHNsH,EAGM,EAFJ/G,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,IAASU,K,OAClBjB,EAAAA,EAAAA,IAAoC,aAAAY,EAAAA,EAAAA,IAA3BwE,EAAMM,IAAI6B,SAAO,MAE5BvH,EAAAA,EAAAA,IAGM,MAHNwH,EAGM,EAFJjH,EAAAA,EAAAA,IAA+BC,EAAA,M,iBAAtB,IAAY,EAAZD,EAAAA,EAAAA,IAAYkH,M,OACrBzH,EAAAA,EAAAA,IAAgC,aAAAY,EAAAA,EAAAA,IAAvBwE,EAAMM,IAAIgC,KAAG,MAExB1H,EAAAA,EAAAA,IAEM,OAFDL,MAAM,iBAAkB8D,OAAKkE,EAAAA,EAAAA,IAAA,CAAA/C,MAAU/D,EAAA+G,eAAexC,EAAMM,IAAIyB,e,QAChE/B,EAAMM,IAAIyB,WAAY,KAC3B,U,OAMR5G,EAAAA,EAAAA,IAgBkBwE,GAAA,CAhBDnC,MAAM,KAAKqC,MAAM,SAASD,MAAM,O,CACpCE,SAAOC,EAAAA,EAAAA,IAaVC,GAbiB,EACvBpF,EAAAA,EAAAA,IAYM,MAZN6H,EAYM,CAVyB,QAArBzC,EAAMM,IAAIiB,S,WADlB9G,EAAAA,EAAAA,IAOY+D,GAAA,C,MALVP,KAAK,UACLb,KAAK,QACJsB,QAAKvB,GAAEgD,EAAAuC,QAAQC,KAAK,CAADC,KAAA,SAAAC,OAAA,CAAAC,GAAiC9C,EAAMM,IAAIwC,O,kBAE/D,IAA2B,EAA3B3H,EAAAA,EAAAA,IAA2BC,EAAA,M,iBAAlB,IAAQ,EAARD,EAAAA,EAAAA,IAAQ4H,M,6BAAU,U,6CAE7BvB,EAAAA,EAAAA,IAEO,OAFPwB,EAEO,EADL7H,EAAAA,EAAAA,IAA8BC,EAAA,M,iBAArB,IAAW,EAAXD,EAAAA,EAAAA,IAAWgB,K,6BAAU,iB,+BA/G3BpB,EAAAC,mB,2DA6IrB,MAAMiI,EAASC,EAAQ,MACvB,SAASC,EAAwBC,EAASC,GACxC,MAAMC,EAAIL,EAAOM,GAAGH,EAASC,GAC7B,OAAOC,EAAEE,OAAO,sBAClB,CAEA,SAASC,EAAiBC,EAAMC,GAAW,GACzC,MAAMC,EAAOF,EAAKG,cACZC,EAAQC,OAAOL,EAAKM,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOL,EAAKS,WAAWF,SAAS,EAAG,KAC/C,IAAIG,EAAOC,EAASC,EAYpB,OAVIX,GACFS,EAAQ,KACRC,EAAU,KACVC,EAAU,OAEVF,EAAQ,KACRC,EAAU,KACVC,EAAU,MAGL,GAAGV,KAAQE,KAASI,KAAOE,KAASC,KAAWC,GACxD,CAEA,OACEC,WAAY,CACVC,SAAQ,WACRC,MAAK,QACLC,QAAO,UACPC,SAAQ,WACRC,QAAO,UACPC,OAAM,SACNC,SAAQ,WACRC,MAAK,QACLC,KAAI,OACJC,WAAU,aACVC,eAAc,iBACdC,QAAO,UACPC,SAAQ,WACRC,KAAIA,EAAAA,MAENhG,IAAAA,GACE,MAAO,CACLrE,WAAW,EACXsE,QAAS,GACTpC,UAAW,OACXc,SAAU,CAACyF,EAAiB,IAAI6B,MAAK,IAAIA,MAAOC,UAAY,SAClD9B,EAAiB,IAAI6B,MAAQ,IACvCpH,mBAAoB,CAAC,WAAY,YACjCC,UAAW,CACT,CACEqH,KAAM,KACNC,MAAQA,KACN,MAAMC,EAAM,IAAIJ,KACVK,EAAQ,IAAIL,KAGlB,OAFAK,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,KAGnB,CACEF,KAAM,MACNC,MAAQA,KACN,MAAMC,EAAM,IAAIJ,KACVK,EAAQ,IAAIL,KAIlB,OAHAK,EAAME,QAAQH,EAAIvB,UAAY,GAC9BwB,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,KAGnB,CACEF,KAAM,MACNC,MAAQA,KACN,MAAMC,EAAM,IAAIJ,KACVK,EAAQ,IAAIL,KAIlB,OAHAK,EAAME,QAAQH,EAAIvB,UAAY,GAC9BwB,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,KAGnB,CACEF,KAAM,OACNC,MAAQA,KACN,MAAMC,EAAM,IAAIJ,KACVK,EAAQ,IAAIL,KAIlB,OAHAK,EAAMG,SAASJ,EAAI1B,WAAa,GAChC2B,EAAMC,SAAS,EAAG,EAAG,GACrBF,EAAIE,SAAS,GAAI,GAAI,IACd,CAACD,EAAOD,MAIrBK,YAAa,EACbC,SAAU,GAEd,EAEAC,QAAS,CACPpH,UAAAA,GACEqH,KAAKC,cACP,EAEAxH,SAAAA,GACEuH,KAAKlI,SAAW,CACdyF,EAAiB,IAAI6B,MAAK,IAAIA,MAAOC,UAAY,SACjD9B,EAAiB,IAAI6B,MAAQ,GAEjC,EAEAhI,qBAAAA,CAAsBmI,GACpB,MAAMW,EAAM,IAAId,KAChB,IAAIe,EACU,QAAVZ,GACFY,EAAY,IAAIf,KAChBe,EAAUR,QAAQO,EAAIjC,YACH,SAAVsB,GACTY,EAAY,IAAIf,KAChBe,EAAUR,QAAQO,EAAIjC,UAAY,IACd,SAAVsB,GACVY,EAAY,IAAIf,KAChBe,EAAUR,QAAQO,EAAIjC,UAAY,IACf,UAAVsB,GACTY,EAAY,IAAIf,KAChBe,EAAUR,QAAQO,EAAIjC,UAAY,KACf,QAAVsB,IAETY,EAAY,IAAIf,KAChBgB,QAAQC,IAAIF,GACZA,EAAUG,YAAYJ,EAAIvC,cAAgB,KAG5CqC,KAAKlI,SAAW,CACdyF,EAAiB4C,GACjB5C,EAAiB2C,GAAK,IAGxBF,KAAKC,cACP,EAEA,kBAAMA,GACJD,KAAKlL,WAAY,QAGX,IAAIyL,QAAQC,GAAWC,WAAWD,EAAS,MAEjD,MAAML,EAAYlD,EAAwB+C,KAAKlI,SAAS,GAAI,iBACtD4I,EAAUzD,EAAwB+C,KAAKlI,SAAS,GAAI,iBACpD6I,QAAiBX,KAAKY,KAAKC,cAAc,CAC7CC,QAASd,KAAKe,IAAInE,GAClBoE,WAAYb,EACZc,SAAUP,IAGW,KAAnBC,EAAStF,QACX2E,KAAK5G,QAAUuH,EAASxH,KACxB6G,KAAKkB,YAGLlB,KAAKmB,UAAU,KACbnB,KAAKlL,WAAY,KAGnBkL,KAAKlL,WAAY,CAErB,EAEAoM,SAAAA,GACElB,KAAKoB,OAAOC,OAAOrB,KAAKsB,MAAMC,UAAWvB,KAAKwB,SAASjC,MAAOS,KAAKwB,SAASlK,MAC9E,EAEAgF,cAAAA,CAAemF,GAEb,OADAA,EAAO7F,OAAO6F,GACVA,GAAQ,GAAW,UACnBA,GAAQ,GAAW,UAChB,SACT,EAEA3F,sBAAAA,CAAuB2F,GAErB,OADAA,EAAO7F,OAAO6F,GACVA,GAAQ,GAAW,CAAC,CAACnI,MAAO,UAAWoI,SAAU,GAAI,CAACpI,MAAO,UAAWoI,SAAU,IAClFD,GAAQ,GAAW,CAAC,CAACnI,MAAO,UAAWoI,SAAU,GAAI,CAACpI,MAAO,UAAWoI,SAAU,IAC/E,CAAC,CAACpI,MAAO,UAAWoI,SAAU,GAAI,CAACpI,MAAO,UAAWoI,SAAU,GACxE,EAEA9G,UAAAA,CAAW+G,GACT,MAAMC,EAAU,CACd,OAAQ,SACR,QAAS,UACT,OAAQ,UACR,OAAQ,QAEV,OAAOA,EAAQD,IAAQ,SACzB,EAEAE,gBAAAA,CAAiBC,GACf9B,KAAKF,SAAWgC,CAClB,EAEAC,mBAAAA,CAAoBD,GAClB9B,KAAKH,YAAciC,CACrB,EAEA9I,UAAAA,GAEGgH,KAAKgC,SAAS,CACbC,QAAS,iBACTlK,KAAM,UACNmK,SAAU,KAEd,GAGFC,SAAU,KACLC,EAAAA,EAAAA,IAAS,CAAC,QAEbZ,QAAAA,GACE,IAAIa,EAAW,GACXxG,EAAY,GAChB,IAAK,IAAIyG,KAAQtC,KAAK5G,QACpBiJ,EAAS5F,KAAKuD,KAAK9F,OAAOC,MAAMmI,EAAKjI,cACrCwB,EAAUY,KAAK6F,EAAKzG,WAEtB,MAAO,CACLvE,MAAO+K,EACP9C,MAAO1D,EAEX,EAEA/F,YAAAA,GACE,OAAOkK,KAAK5G,QAAQmJ,OAAO,CAACC,EAAKC,IACxBD,GAAyB,QAAlBC,EAAOpH,QAAmBqH,SAASD,EAAOxG,UAAgB,GACvE,EACL,EAEA7F,YAAAA,GACE,OAAO4J,KAAK5G,QAAQuJ,OAAOF,GAA4B,QAAlBA,EAAOpH,QAAkBuH,MAChE,EAEAlM,eAAAA,GACE,MAAMmM,EAAe7C,KAAK5G,QAAQuJ,OAAOG,GAAkB,QAAbA,EAAEzH,QAAoByH,EAAEjH,WACtE,GAA4B,IAAxBgH,EAAaD,OAAc,OAAO,EAEtC,MAAMJ,EAAMK,EAAaN,OAAO,CAACC,EAAKC,IAC7BD,EAAMO,WAAWN,EAAO5G,WAC9B,GAEH,OAAOmH,KAAKC,MAAMT,EAAMK,EAAaD,OACvC,EAEApN,YAAAA,GACE,OAAOwK,KAAK5G,QAAQwJ,MACtB,GAGFM,MAAO,CACLlM,SAAAA,CAAUmM,GAERnD,KAAK5I,sBAAsB+L,EAC7B,GAGFC,OAAAA,GAEEpD,KAAKlL,WAAY,EACjBkL,KAAKC,cACP,EAEAoD,OAAAA,GAEErD,KAAKmB,UAAU,MAERnB,KAAKlL,WAAakL,KAAK5G,QAAQwJ,OAAS,GAC3C5C,KAAKsD,gBAGX,G,WC5gBF,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/Reports/Records.vue", "webpack://frontend-web/./src/views/Reports/Records.vue?8faa"], "sourcesContent": ["<template>\n  <el-scrollbar height=\"97vh\">\n    <div class=\"dashboard-container\">\n    <!-- 顶部统计区域 -->\n    <div class=\"stats-container\" :class=\"{ 'stats-loaded': !isLoading }\">\n      <div class=\"stat-card primary-gradient\">\n        <div class=\"stat-icon\">\n          <el-icon><DataLine /></el-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ recordsCount }}</div>\n          <div class=\"stat-title\">总执行次数</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card success-gradient\">\n        <div class=\"stat-icon\">\n          <el-icon><Check /></el-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ totalSuccess }}</div>\n          <div class=\"stat-title\">通过用例</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card warning-gradient\">\n        <div class=\"stat-icon\">\n          <el-icon><Loading /></el-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ runningCount }}</div>\n          <div class=\"stat-title\">执行中</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card info-gradient\">\n        <div class=\"stat-icon\">\n          <el-icon><PieChart /></el-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ averagePassRate }}%</div>\n          <div class=\"stat-title\">平均通过率</div>\n        </div>\n      </div>\n    </div>\n      <!-- 图表和过滤器部分 -->\n      <div class=\"chart-filter-container\">\n        <!-- 图表卡片 -->\n        <div class=\"chart-card\">\n          <div class=\"card-header\">\n            <h3>测试通过率趋势</h3>\n            <div class=\"time-selector\">\n              <el-radio-group v-model=\"timeRange\" size=\"small\" @change=\"handleTimeRangeChange\">\n                <el-radio-button label=\"day\">当天</el-radio-button>\n                <el-radio-button label=\"day3\">近3天</el-radio-button>\n                <el-radio-button label=\"week\">近7天</el-radio-button>\n                <el-radio-button label=\"month\">近30天</el-radio-button>\n                <el-radio-button label=\"all\">全部</el-radio-button>\n              </el-radio-group>\n            </div>\n          </div>\n          <div class=\"chart-wrapper\" ref=\"chart_box\"></div>\n        </div>\n\n        <!-- 筛选卡片 -->\n        <div class=\"filter-card\">\n          <div class=\"card-header\">\n            <h3>筛选条件</h3>\n          </div>\n          <div class=\"filter-content\">\n            <el-form label-position=\"top\" size=\"small\">\n              <el-form-item label=\"执行时间\" class=\"date-range-item\">\n                <el-date-picker\n                  v-model=\"dataTime\"\n                  type=\"datetimerange\"\n                  start-placeholder=\"开始时间\"\n                  end-placeholder=\"结束时间\"\n                  value-format=\"YYYY-MM-DD HH:mm:ss\"\n                  :default-time=\"defaultTimeOptions\"\n                  :shortcuts=\"shortcuts\"\n                  range-separator=\"至\"\n                  :clearable=\"false\"\n                  class=\"date-picker\"\n                  :style=\"{maxWidth: '100%'}\"\n                />\n              </el-form-item>\n              <div class=\"filter-buttons\">\n                <el-button plain @click=\"clearData\">\n                  <el-icon><Refresh /></el-icon>重置\n                </el-button>\n                <el-button type=\"primary\" @click=\"submitForm\">\n                  <el-icon><Search /></el-icon>查询\n                </el-button>\n              </div>\n            </el-form>\n          </div>\n        </div>\n      </div>\n\n      <!-- 数据表格 -->\n      <div class=\"table-card\">\n        <div class=\"card-header with-border\">\n          <h3>执行记录</h3>\n          <div class=\"header-actions\">\n            <el-button size=\"small\" type=\"primary\" plain @click=\"exportData\">\n              <el-icon><Download /></el-icon>导出数据\n            </el-button>\n          </div>\n        </div>\n\n        <el-table\n          :data=\"records\"\n          v-loading=\"isLoading\"\n          element-loading-text=\"正在加载数据...\"\n          element-loading-spinner=\"el-icon-loading\"\n          element-loading-background=\"rgba(255, 255, 255, 0.8)\"\n          class=\"custom-table\"\n          :header-cell-style=\"{ background: '#f8faff', color: '#606266', fontWeight: '600' }\"\n          border\n        >\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\n\n          <el-table-column label=\"执行时间\" align=\"center\" min-width=\"160\">\n            <template #default=\"scope\">\n              <div class=\"time-cell\">\n                <el-icon><Clock /></el-icon>\n                <span>{{ $tools.rTime(scope.row.create_time) }}</span>\n              </div>\n            </template>\n          </el-table-column>\n\n          <el-table-column label=\"执行人\" align=\"center\" min-width=\"110\">\n            <template #default=\"scope\">\n              <div class=\"user-cell\">\n                <el-avatar :size=\"24\" class=\"user-avatar\">{{ scope.row.tester.substring(0, 1) }}</el-avatar>\n                <span>{{ scope.row.tester }}</span>\n              </div>\n            </template>\n          </el-table-column>\n\n          <el-table-column label=\"环境\" align=\"center\" min-width=\"110\">\n            <template #default=\"scope\">\n              <el-tag effect=\"dark\" :type=\"getEnvType(scope.row.env_name)\" class=\"env-tag\">\n                {{ scope.row.env_name }}\n              </el-tag>\n            </template>\n          </el-table-column>\n\n          <el-table-column label=\"执行类型\" align=\"center\" min-width=\"120\">\n            <template #default=\"scope\">\n              <el-tag\n                v-if=\"scope.row.execute_type === '手动执行'\"\n                effect=\"plain\"\n                type=\"primary\"\n                class=\"type-tag\">\n                <el-icon><User /></el-icon> {{ scope.row.execute_type }}\n              </el-tag>\n              <el-tag\n                v-else-if=\"scope.row.execute_type === '定时执行'\"\n                effect=\"plain\"\n                type=\"success\"\n                class=\"type-tag\">\n                <el-icon><AlarmClock /></el-icon> {{ scope.row.execute_type }}\n              </el-tag>\n              <el-tag v-else effect=\"plain\" type=\"info\" class=\"type-tag\">\n                <el-icon><QuestionFilled /></el-icon> 未知\n              </el-tag>\n            </template>\n          </el-table-column>\n\n          <el-table-column label=\"测试计划\" align=\"center\" min-width=\"120\">\n            <template #default=\"scope\">\n              <div class=\"plan-cell\">\n                <el-icon><Tickets /></el-icon>\n                <span>{{ scope.row.plan_name }}</span>\n              </div>\n            </template>\n          </el-table-column>\n\n          <el-table-column label=\"执行情况\" align=\"center\" min-width=\"220\">\n            <template #default=\"scope\">\n              <div v-if=\"scope.row.status === '执行中'\" class=\"running-status\">\n                <div class=\"pulse-dot\"></div>\n                <span>正在执行中...</span>\n              </div>\n              <div v-else class=\"result-container\">\n                <div class=\"progress-bar\">\n                  <el-progress\n                    :percentage=\"Number(scope.row.pass_rate)\"\n                    :color=\"getStatusColorGradient(scope.row.pass_rate)\"\n                    :stroke-width=\"8\"\n                    :show-text=\"false\"\n                  ></el-progress>\n                </div>\n                <div class=\"result-stats\">\n                  <div class=\"stat-item pass\">\n                    <el-icon><Check /></el-icon>\n                    <span>{{ scope.row.success }}</span>\n                  </div>\n                  <div class=\"stat-item total\">\n                    <el-icon><Document /></el-icon>\n                    <span>{{ scope.row.all }}</span>\n                  </div>\n                  <div class=\"stat-item rate\" :style=\"{color: getStatusColor(scope.row.pass_rate)}\">\n                    {{ scope.row.pass_rate }}%\n                  </div>\n                </div>\n              </div>\n            </template>\n          </el-table-column>\n\n          <el-table-column label=\"操作\" align=\"center\" width=\"160\">\n            <template #default=\"scope\">\n              <div class=\"action-buttons\">\n                <el-button\n                  v-if=\"scope.row.status !== '执行中'\"\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"$router.push({ name: 'report', params: { id: scope.row.id } })\"\n                >\n                  <el-icon><View /></el-icon>查看\n                </el-button>\n                <span v-else class=\"running-badge\">\n                  <el-icon><Loading /></el-icon> 执行中\n                </span>\n              </div>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n  </div>\n  </el-scrollbar>\n</template>\n\n<script>\nimport { mapState } from 'vuex';\nimport { \n  DataLine, \n  Check, \n  Loading, \n  PieChart, \n  Refresh, \n  Search, \n  Download, \n  Clock, \n  User, \n  AlarmClock, \n  QuestionFilled, \n  Tickets, \n  Document, \n  View \n} from '@element-plus/icons-vue';\n\nconst moment = require('moment-timezone');\nfunction convertToTimeZoneFormat(dateStr, timeZone) {\n  const m = moment.tz(dateStr, timeZone);\n  return m.format('YYYY-MM-DD HH:mm:ss');\n}\n\nfunction getFormattedDate(date, endOfDay = false) {\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  let hours, minutes, seconds;\n\n  if (endOfDay) {\n    hours = '23';\n    minutes = '59';\n    seconds = '59';\n  } else {\n    hours = '00';\n    minutes = '00';\n    seconds = '00';\n  }\n\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n}\n\nexport default {\n  components: {\n    DataLine,\n    Check,\n    Loading,\n    PieChart,\n    Refresh,\n    Search,\n    Download,\n    Clock,\n    User,\n    AlarmClock,\n    QuestionFilled,\n    Tickets,\n    Document,\n    View\n  },\n  data() {\n    return {\n      isLoading: false,\n      records: [],\n      timeRange: 'day3',\n      dataTime: [getFormattedDate(new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000)),\n                getFormattedDate(new Date(), true)],\n      defaultTimeOptions: ['00:00:00', '23:59:59'],\n      shortcuts: [\n        {\n          text: '今天',\n          value: (() => {\n            const end = new Date();\n            const start = new Date();\n            start.setHours(0, 0, 0);\n            end.setHours(23, 59, 59);\n            return [start, end];\n          })\n        },\n        {\n          text: '近三天',\n          value: (() => {\n            const end = new Date();\n            const start = new Date();\n            start.setDate(end.getDate() - 2);\n            start.setHours(0, 0, 0);\n            end.setHours(23, 59, 59);\n            return [start, end];\n          })\n        },\n        {\n          text: '近七天',\n          value: (() => {\n            const end = new Date();\n            const start = new Date();\n            start.setDate(end.getDate() - 6);\n            start.setHours(0, 0, 0);\n            end.setHours(23, 59, 59);\n            return [start, end];\n          })\n        },\n        {\n          text: '近一个月',\n          value: (() => {\n            const end = new Date();\n            const start = new Date();\n            start.setMonth(end.getMonth() - 1);\n            start.setHours(0, 0, 0);\n            end.setHours(23, 59, 59);\n            return [start, end];\n          })\n        }\n      ],\n      currentPage: 1,\n      pageSize: 10\n    };\n  },\n\n  methods: {\n    submitForm() {\n      this.getAllRecord();\n    },\n\n    clearData() {\n      this.dataTime = [\n        getFormattedDate(new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000)),\n        getFormattedDate(new Date(), true)\n      ];\n    },\n\n    handleTimeRangeChange(value) {\n      const now = new Date();\n      let startDate;\n      if (value === 'day') {\n        startDate = new Date();\n        startDate.setDate(now.getDate());\n      } else if (value === 'day3') {\n        startDate = new Date();\n        startDate.setDate(now.getDate() - 2);\n      }  else if (value === 'week') {\n        startDate = new Date();\n        startDate.setDate(now.getDate() - 6);\n      } else if (value === 'month') {\n        startDate = new Date();\n        startDate.setDate(now.getDate() - 29);\n      } else if (value === 'all') {\n        // 对于 \"全部\"，我们可以设置一个很早的日期或者按项目情况调整\n        startDate = new Date();\n        console.log(startDate)\n        startDate.setFullYear(now.getFullYear() - 10);\n      }\n\n      this.dataTime = [\n        getFormattedDate(startDate),\n        getFormattedDate(now, true)\n      ];\n\n      this.getAllRecord();\n    },\n\n    async getAllRecord() {\n      this.isLoading = true;\n      \n      // 延迟一小段时间确保加载状态显示一致\n      await new Promise(resolve => setTimeout(resolve, 100));\n      \n      const startDate = convertToTimeZoneFormat(this.dataTime[0], 'Asia/Shanghai');\n      const endDate = convertToTimeZoneFormat(this.dataTime[1], 'Asia/Shanghai');\n      const response = await this.$api.getTestRecord({\n        project: this.pro.id,\n        start_time: startDate,\n        end_time: endDate\n      });\n\n      if (response.status == 200) {\n        this.records = response.data;\n        this.chartView();\n        \n        // 使用nextTick确保DOM更新后再关闭loading状态\n        this.$nextTick(() => {\n          this.isLoading = false;\n        });\n      } else {\n        this.isLoading = false;\n      }\n    },\n\n    chartView() {\n      this.$chart.chart3(this.$refs.chart_box, this.pateData.value, this.pateData.label);\n    },\n\n    getStatusColor(rate) {\n      rate = Number(rate);\n      if (rate >= 90) return '#67C23A';\n      if (rate >= 70) return '#E6A23C';\n      return '#F56C6C';\n    },\n\n    getStatusColorGradient(rate) {\n      rate = Number(rate);\n      if (rate >= 90) return [{color: '#95de64', position: 0}, {color: '#52c41a', position: 1}];\n      if (rate >= 70) return [{color: '#ffd666', position: 0}, {color: '#faad14', position: 1}];\n      return [{color: '#ff7875', position: 0}, {color: '#f5222d', position: 1}];\n    },\n\n    getEnvType(env) {\n      const typeMap = {\n        '生产环境': 'danger',\n        '预发布环境': 'warning',\n        '测试环境': 'success',\n        '开发环境': 'info'\n      };\n      return typeMap[env] || 'primary';\n    },\n\n    handleSizeChange(val) {\n      this.pageSize = val;\n    },\n\n    handleCurrentChange(val) {\n      this.currentPage = val;\n    },\n\n    exportData() {\n      // Inform the user that the export feature is not yet implemented\n       this.$message({\n        message: '导出功能尚未实现，敬请期待！',\n        type: 'warning',\n        duration: 3000\n      });\n    }\n  },\n\n  computed: {\n    ...mapState(['pro']),\n\n    pateData() {\n      let run_date = [];\n      let pass_rate = [];\n      for (let item of this.records) {\n        run_date.push(this.$tools.rTime(item.create_time));\n        pass_rate.push(item.pass_rate);\n      }\n      return {\n        label: run_date,\n        value: pass_rate\n      };\n    },\n\n    totalSuccess() {\n      return this.records.reduce((sum, record) => {\n        return sum + (record.status !== '执行中' ? parseInt(record.success) || 0 : 0);\n      }, 0);\n    },\n\n    runningCount() {\n      return this.records.filter(record => record.status === '执行中').length;\n    },\n\n    averagePassRate() {\n      const validRecords = this.records.filter(r => r.status !== '执行中' && r.pass_rate);\n      if (validRecords.length === 0) return 0;\n\n      const sum = validRecords.reduce((sum, record) => {\n        return sum + parseFloat(record.pass_rate);\n      }, 0);\n\n      return Math.round(sum / validRecords.length);\n    },\n\n    recordsCount() {\n      return this.records.length;\n    }\n  },\n\n  watch: {\n    timeRange(newValue) {\n      // Call handleTimeRangeChange when timeRange changes\n      this.handleTimeRangeChange(newValue);\n    }\n  },\n\n  created() {\n    // 先将isLoading设为true，保证显示一致性\n    this.isLoading = true;\n    this.getAllRecord();\n  },\n\n  mounted() {\n    // 确保在挂载后样式已经应用\n    this.$nextTick(() => {\n      // 如果数据已加载完成，强制更新一次视图\n      if (!this.isLoading && this.records.length > 0) {\n        this.$forceUpdate();\n      }\n    });\n  }\n};\n</script>\n\n<style scoped>\n.dashboard-container {\n  padding: 20px;\n  background-color: #f0f2f5;\n  min-height: 100%;\n}\n\n/* 统计卡片样式 */\n.stats-container {\n  display: grid;\n  grid-template-columns: repeat(4, minmax(240px, 1fr)); /* 明确4列 */\n  gap: 24px;\n  margin-bottom: 24px; /* 添加底部间距确保布局稳定 */\n  min-height: 120px; /* 确保即使在加载状态也有最小高度 */\n  width: 100%;\n}\n\n/* 确保在不同状态下保持一致的布局 */\n.stats-container.stats-loaded {\n  opacity: 1;\n  transition: opacity 0.3s ease;\n}\n\n.stat-card {\n  position: relative;\n  border-radius: 10px;\n  padding: 24px;\n  color: white;\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n.stat-card:after {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  background: rgba(255, 255, 255, 0.1);\n  pointer-events: none;\n  opacity: 0;\n  transition: opacity 0.3s;\n  border-radius: 10px;\n}\n\n.stat-card:hover:after {\n  opacity: 1;\n}\n\n.primary-gradient {\n  background: linear-gradient(135deg, #1890ff, #096dd9);\n}\n\n.success-gradient {\n  background: linear-gradient(135deg, #52c41a, #389e0d);\n}\n\n.warning-gradient {\n  background: linear-gradient(135deg, #faad14, #d48806);\n}\n\n.info-gradient {\n  background: linear-gradient(135deg, #722ed1, #531dab);\n}\n\n.stat-icon {\n  height: 56px;\n  width: 56px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28px;\n  margin-right: 20px;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 26px;\n  font-weight: 600;\n  margin-bottom: 4px;\n  line-height: 1.2;\n  white-space: nowrap;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.stat-title {\n  font-size: 14px;\n  opacity: 0.9;\n  white-space: nowrap;\n}\n\n/* 图表和过滤器布局 */\n.chart-filter-container {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 24px;\n  margin-bottom: 24px;\n}\n\n.chart-card, .filter-card, .table-card {\n  background: white;\n  border-radius: 10px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  transition: box-shadow 0.3s ease;\n}\n\n.chart-card:hover, .filter-card:hover, .table-card:hover {\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);\n}\n\n.card-header {\n  padding: 16px 24px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.card-header.with-border {\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #262626;\n}\n\n.chart-wrapper {\n  padding: 16px;\n  height: 320px;\n  min-height: 320px; /* 确保最小高度一致 */\n}\n\n/* 确保内容在加载状态时保持稳定 */\n.el-scrollbar__wrap {\n  overflow-x: hidden;\n}\n\n/* 确保表格在加载时的高度稳定 */\n.table-card {\n  min-height: 200px;\n  margin-top: 24px;\n}\n\n/* 筛选器样式 */\n.filter-content {\n  padding: 20px;\n}\n\n.date-range-item {\n  margin-bottom: 20px;\n}\n\n.date-picker {\n  width: 100%;\n  max-width: 100%;\n}\n\n.filter-buttons {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  margin-top: 10px;\n}\n\n/* 表格样式 */\n.custom-table {\n  margin: 0;\n  font-size: 14px;\n}\n\n.custom-table :deep(td) {\n  padding: 12px 0;\n  height: auto;\n  line-height: 1.5;\n}\n\n.custom-table :deep(th) {\n  padding: 12px 0;\n  height: 50px;\n  font-weight: 600;\n}\n\n.time-cell, .user-cell, .plan-cell {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  line-height: 1.5;\n  text-align: center;\n}\n\n.user-avatar {\n  background-color: #1890ff;\n  color: white;\n}\n\n.env-tag, .type-tag {\n  padding: 4px 8px;\n  border-radius: 4px;\n  line-height: 1.5;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 4px;\n}\n\n.running-status {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  color: #faad14;\n  font-weight: 500;\n}\n\n.pulse-dot {\n  width: 10px;\n  height: 10px;\n  border-radius: 50%;\n  background-color: #faad14;\n  position: relative;\n  animation: pulse 1.5s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(0.8);\n    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0.7);\n  }\n  70% {\n    transform: scale(1);\n    box-shadow: 0 0 0 6px rgba(250, 173, 20, 0);\n  }\n  100% {\n    transform: scale(0.8);\n  }\n}\n\n.result-container {\n  padding: 0 16px;\n  min-width: 220px;\n}\n\n.progress-bar {\n  margin-bottom: 10px;\n}\n\n.result-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: nowrap;\n  margin-top: 8px;\n}\n\n.stat-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 13px;\n  white-space: nowrap;\n  padding: 0 8px;\n}\n\n.stat-item.pass {\n  color: #52c41a;\n}\n\n.stat-item.total {\n  color: #8c8c8c;\n}\n\n.stat-item.rate {\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.running-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 5px;\n  color: #faad14;\n  font-size: 13px;\n}\n\n.pagination-container {\n  padding: 16px 24px;\n  display: flex;\n  justify-content: flex-end;\n  border-top: 1px solid #f0f0f0;\n  flex-wrap: wrap;\n}\n\n/* 响应式调整 */\n@media (max-width: 1200px) {\n  .chart-filter-container {\n    grid-template-columns: 1fr;\n  }\n\n  .stats-container {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 16px;\n  }\n\n  .date-picker:deep(.el-range-editor) {\n    width: 100% !important;\n  }\n}\n\n@media (max-width: 768px) {\n  .dashboard-container {\n    padding: 16px;\n  }\n\n  .stats-container {\n    grid-template-columns: 1fr;\n    gap: 16px;\n    margin-bottom: 16px;\n  }\n\n  .stat-card {\n    padding: 16px;\n  }\n\n  .stat-icon {\n    height: 48px;\n    width: 48px;\n    font-size: 24px;\n  }\n\n  .stat-value {\n    font-size: 24px;\n  }\n\n  .chart-filter-container {\n    gap: 16px;\n    margin-bottom: 16px;\n  }\n\n  .filter-content {\n    padding: 16px;\n  }\n\n  .chart-wrapper {\n    height: 260px;\n  }\n\n  .date-picker:deep(.el-range-editor) {\n    width: 100% !important;\n    flex-direction: column;\n    height: auto;\n  }\n\n  .date-picker:deep(.el-range-separator) {\n    padding: 5px 0;\n  }\n\n  .date-picker:deep(.el-range-input) {\n    width: 100%;\n  }\n}\n\n@media (max-width: 1300px) {\n  .action-buttons {\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .action-buttons .el-button {\n    margin-left: 0;\n    margin-bottom: 8px;\n  }\n}\n\n/* 添加表格行条纹效果 */\n.custom-table :deep(.el-table__row:nth-child(even)) {\n  background-color: #fafafa;\n}\n\n/* 确保表格有表头边界 */\n.custom-table :deep(.el-table__header) {\n  border-bottom: 1px solid #ebeef5;\n}\n\n/* 确保测试计划内容不被截断 */\n.plan-cell span {\n  max-width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: inline-block;\n}\n\n/* 调整操作按钮 */\n.action-buttons .el-button {\n  padding: 6px 12px;\n}\n\n/* 确保内容垂直居中 */\n.custom-table :deep(.cell) {\n  padding-top: 0;\n  padding-bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n</style>\n", "import { render } from \"./Records.vue?vue&type=template&id=0ffee73a&scoped=true\"\nimport script from \"./Records.vue?vue&type=script&lang=js\"\nexport * from \"./Records.vue?vue&type=script&lang=js\"\n\nimport \"./Records.vue?vue&type=style&index=0&id=0ffee73a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0ffee73a\"]])\n\nexport default __exports__"], "names": ["class", "ref", "_createBlock", "_component_el_scrollbar", "height", "_createElementVNode", "_hoisted_1", "_normalizeClass", "$data", "isLoading", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_icon", "_component_DataLine", "_hoisted_4", "_hoisted_5", "_toDisplayString", "$options", "recordsCount", "_hoisted_6", "_hoisted_7", "_component_Check", "_hoisted_8", "_hoisted_9", "totalSuccess", "_hoisted_10", "_hoisted_11", "_component_Loading", "_hoisted_12", "_hoisted_13", "runningCount", "_hoisted_14", "_hoisted_15", "_component_<PERSON><PERSON><PERSON>", "_hoisted_16", "_hoisted_17", "averagePassRate", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_el_radio_group", "timeRange", "$event", "size", "onChange", "handleTimeRangeChange", "_component_el_radio_button", "label", "_cache", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_component_el_form", "_component_el_form_item", "_component_el_date_picker", "dataTime", "type", "defaultTimeOptions", "shortcuts", "clearable", "style", "max<PERSON><PERSON><PERSON>", "_hoisted_25", "_component_el_button", "plain", "onClick", "clearData", "_component_Refresh", "submitForm", "_component_Search", "_hoisted_26", "_hoisted_27", "_hoisted_28", "exportData", "_component_Download", "_component_el_table", "data", "records", "background", "color", "fontWeight", "border", "_component_el_table_column", "width", "align", "default", "_withCtx", "scope", "_hoisted_29", "_component_Clock", "_ctx", "$tools", "rTime", "row", "create_time", "_hoisted_30", "_component_el_avatar", "tester", "substring", "_component_el_tag", "effect", "getEnvType", "env_name", "execute_type", "_component_User", "_component_AlarmClock", "_component_QuestionFilled", "_hoisted_31", "_component_Tickets", "plan_name", "status", "_createElementBlock", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_component_el_progress", "percentage", "Number", "pass_rate", "getStatusColorGradient", "_hoisted_35", "_hoisted_36", "success", "_hoisted_37", "_component_Document", "all", "_normalizeStyle", "getStatusColor", "_hoisted_38", "$router", "push", "name", "params", "id", "_component_View", "_hoisted_39", "moment", "require", "convertToTimeZoneFormat", "dateStr", "timeZone", "m", "tz", "format", "getFormattedDate", "date", "endOfDay", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "minutes", "seconds", "components", "DataLine", "Check", "Loading", "<PERSON><PERSON><PERSON>", "Refresh", "Search", "Download", "Clock", "User", "AlarmClock", "QuestionFilled", "Tickets", "Document", "View", "Date", "getTime", "text", "value", "end", "start", "setHours", "setDate", "setMonth", "currentPage", "pageSize", "methods", "this", "getAllRecord", "now", "startDate", "console", "log", "setFullYear", "Promise", "resolve", "setTimeout", "endDate", "response", "$api", "getTestRecord", "project", "pro", "start_time", "end_time", "chartView", "$nextTick", "$chart", "chart3", "$refs", "chart_box", "pate<PERSON><PERSON>", "rate", "position", "env", "typeMap", "handleSizeChange", "val", "handleCurrentChange", "$message", "message", "duration", "computed", "mapState", "run_date", "item", "reduce", "sum", "record", "parseInt", "filter", "length", "validRecords", "r", "parseFloat", "Math", "round", "watch", "newValue", "created", "mounted", "$forceUpdate", "__exports__", "render"], "sourceRoot": ""}