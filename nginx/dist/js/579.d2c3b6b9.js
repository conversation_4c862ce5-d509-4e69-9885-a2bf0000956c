"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[579],{99128:function(e,t,a){a.r(t),a.d(t,{default:function(){return nt}});var l=a(56768),o=a(24232),s=a(45130);const n={class:"interface-container"},i={class:"search-area"},r={class:"action-buttons"},d={class:"button-group"},c={class:"env-info"},u={style:{"font-size":"14px",color:"#909399","margin-right":"10px","margin-bottom":"10px",display:"inline-block"}},m={slot:"footer",class:"dialog-footer"},p={class:"interface-title"},_={class:"interface-list"},h={class:"interface-checkbox"},g=["onClick"],k={class:"method-section"},f={class:"info-section"},b=["title"],F=["title"],C={style:{"margin-top":"10px"}},y={key:0},v={style:{color:"#409eff","margin-right":"8px"}};function w(e,t,a,w,D,x){const T=(0,l.g2)("treeNode"),E=(0,l.g2)("el-col"),V=(0,l.g2)("el-input"),L=(0,l.g2)("el-option"),I=(0,l.g2)("el-select"),W=(0,l.g2)("el-button"),A=(0,l.g2)("Delete"),M=(0,l.g2)("el-icon"),S=(0,l.g2)("Plus"),O=(0,l.g2)("View"),P=(0,l.g2)("Star"),U=(0,l.g2)("Upload"),j=(0,l.g2)("el-form-item"),R=(0,l.g2)("el-form"),$=(0,l.g2)("el-dialog"),z=(0,l.g2)("el-checkbox"),q=(0,l.g2)("el-tag"),X=(0,l.g2)("Flag"),J=(0,l.g2)("ArrowDown"),B=(0,l.g2)("el-dropdown-item"),K=(0,l.g2)("el-dropdown-menu"),N=(0,l.g2)("el-dropdown"),Y=(0,l.g2)("el-scrollbar"),Q=(0,l.g2)("el-row"),H=(0,l.g2)("addCase"),G=(0,l.g2)("el-drawer"),Z=(0,l.g2)("newEditCase"),ee=(0,l.g2)("el-card"),te=(0,l.g2)("el-timeline-item"),ae=(0,l.g2)("el-timeline"),le=(0,l.g2)("interfaceImport"),oe=(0,l.g2)("mockInterface");return(0,l.uX)(),(0,l.CE)(l.FK,null,[(0,l.Lk)("div",n,[(0,l.bF)(Q,{gutter:10,class:"main-content"},{default:(0,l.k6)(()=>[(0,l.bF)(E,{xs:24,sm:8,md:6,lg:6,xl:5,class:"left-panel"},{default:(0,l.k6)(()=>[(0,l.bF)(T,{onTreeClick:x.handleTreeClick,handleTreeClick:x.handleTreeClick},null,8,["onTreeClick","handleTreeClick"])]),_:1}),(0,l.bF)(E,{xs:24,sm:16,md:18,lg:18,xl:19,class:"right-content"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",i,[(0,l.bF)(V,{style:{width:"100%","max-width":"300px","margin-right":"10px","margin-bottom":"10px"},modelValue:D.filterText.name,"onUpdate:modelValue":t[0]||(t[0]=e=>D.filterText.name=e),placeholder:"请输入接口名称",clearable:""},null,8,["modelValue"]),(0,l.bF)(I,{modelValue:D.filterText.status,"onUpdate:modelValue":t[1]||(t[1]=e=>D.filterText.status=e),placeholder:"选择查询状态",style:{width:"100%","max-width":"150px","margin-right":"10px","margin-bottom":"10px"},clearable:""},{default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(D.options,e=>((0,l.uX)(),(0,l.Wv)(L,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),(0,l.bF)(W,{type:"primary",onClick:x.handlenewInterfacesClick,style:{"margin-bottom":"10px"}},{default:(0,l.k6)(()=>t[13]||(t[13]=[(0,l.eW)("查询")])),_:1,__:[13]},8,["onClick"]),(0,l.bF)(W,{onClick:t[2]||(t[2]=e=>D.filterText={status:"",name:""}),style:{"margin-bottom":"10px"}},{default:(0,l.k6)(()=>t[14]||(t[14]=[(0,l.eW)("重置")])),_:1,__:[14]})]),(0,l.Lk)("div",r,[(0,l.Lk)("div",d,[(0,l.bF)(W,{type:"danger",onClick:x.delAllInterface,style:{"margin-right":"10px","margin-bottom":"10px"}},{default:(0,l.k6)(()=>[(0,l.bF)(M,{style:{"margin-right":"6px"}},{default:(0,l.k6)(()=>[(0,l.bF)(A)]),_:1}),t[15]||(t[15]=(0,l.eW)(" 批量删除 "))]),_:1,__:[15]},8,["onClick"]),(0,l.bF)(W,{type:"primary",onClick:x.clickAdd,style:{"margin-right":"10px","margin-bottom":"10px"}},{default:(0,l.k6)(()=>[(0,l.bF)(M,{style:{"margin-right":"6px"}},{default:(0,l.k6)(()=>[(0,l.bF)(S)]),_:1}),t[16]||(t[16]=(0,l.eW)(" 新增接口 "))]),_:1,__:[16]},8,["onClick"]),(0,l.bF)(W,{type:D.showOnlySelf?"success":"primary",onClick:x.userInterface,style:{"margin-right":"10px","margin-bottom":"10px"}},{default:(0,l.k6)(()=>[(0,l.bF)(M,{style:{"margin-right":"6px"}},{default:(0,l.k6)(()=>[(0,l.bF)(O)]),_:1}),(0,l.eW)(" "+(0,o.v_)(x.buttonText),1)]),_:1},8,["type","onClick"]),(0,l.bF)(W,{type:"primary",onClick:t[3]||(t[3]=e=>D.dialogVisible=!0),style:{"margin-right":"10px","margin-bottom":"10px"}},{default:(0,l.k6)(()=>[(0,l.bF)(M,{style:{"margin-right":"6px"}},{default:(0,l.k6)(()=>[(0,l.bF)(P)]),_:1}),t[17]||(t[17]=(0,l.eW)(" 选择环境 "))]),_:1,__:[17]})]),(0,l.Lk)("div",c,[(0,l.Lk)("span",u,[t[18]||(t[18]=(0,l.eW)("当前环境： ")),(0,l.bF)(W,{type:"info",disabled:"",plain:""},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(x.currentEnv?x.currentEnv.name:"未知环境"),1)]),_:1})]),(0,l.bF)(W,{type:"warning",onClick:x.importClick,style:{"margin-bottom":"10px"}},{default:(0,l.k6)(()=>[(0,l.bF)(M,null,{default:(0,l.k6)(()=>[(0,l.bF)(U)]),_:1}),t[19]||(t[19]=(0,l.eW)(" 导入接口 "))]),_:1,__:[19]},8,["onClick"])])]),(0,l.bF)($,{modelValue:D.dialogVisible,"onUpdate:modelValue":t[6]||(t[6]=e=>D.dialogVisible=e),width:"30%",title:"选择环境"},{footer:(0,l.k6)(()=>[(0,l.Lk)("span",m,[(0,l.bF)(W,{onClick:t[5]||(t[5]=e=>D.dialogVisible=!1)},{default:(0,l.k6)(()=>t[20]||(t[20]=[(0,l.eW)("取消")])),_:1,__:[20]}),(0,l.bF)(W,{type:"primary",onClick:x.confirmSelection},{default:(0,l.k6)(()=>t[21]||(t[21]=[(0,l.eW)("确定")])),_:1,__:[21]},8,["onClick"])])]),default:(0,l.k6)(()=>[(0,l.bF)(R,{rules:e.rulesinterface,ref:"interfaceRef"},{default:(0,l.k6)(()=>[(0,l.bF)(j,{label:"测试环境",prop:"env"},{default:(0,l.k6)(()=>[(0,l.bF)(I,{modelValue:D.selectedEnvironment,"onUpdate:modelValue":t[4]||(t[4]=e=>D.selectedEnvironment=e),modelModifiers:{lazy:!0},placeholder:"请选择环境",style:{width:"70%"},"no-data-text":"暂无数据"},{default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.testEnvs,e=>((0,l.uX)(),(0,l.Wv)(L,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["rules"])]),_:1},8,["modelValue"]),(0,l.Lk)("div",p,"全部接口共 ("+(0,o.v_)(D.interfaceCount)+") 个",1),(0,l.bF)(Y,{class:"interface-scrollbar"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",_,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(D.tableData,e=>((0,l.uX)(),(0,l.CE)("div",{key:e.id,class:(0,o.C4)(["interface-item",[{"interface-item-selected":D.multipleSelection.includes(e.id)},`method-${e.method.toLowerCase()}`]])},[(0,l.Lk)("div",h,[(0,l.bF)(z,{value:D.multipleSelection.includes(e.id),onChange:t=>x.handleSingleSelect(t,e.id)},null,8,["value","onChange"])]),(0,l.Lk)("div",{class:"interface-content",onClick:t=>x.clickCopy(e.id)},[(0,l.Lk)("div",k,["POST"===e.method?((0,l.uX)(),(0,l.Wv)(q,{key:0,color:"#49cc90"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.method),1)]),_:2},1024)):(0,l.Q3)("",!0),"GET"===e.method?((0,l.uX)(),(0,l.Wv)(q,{key:1,color:"#61affe"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.method),1)]),_:2},1024)):(0,l.Q3)("",!0),"PUT"===e.method?((0,l.uX)(),(0,l.Wv)(q,{key:2,color:"#fca130"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.method),1)]),_:2},1024)):(0,l.Q3)("",!0),"PATCH"===e.method?((0,l.uX)(),(0,l.Wv)(q,{key:3,color:"#50e3c2"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.method),1)]),_:2},1024)):(0,l.Q3)("",!0),"DELETE"===e.method?((0,l.uX)(),(0,l.Wv)(q,{key:4,color:"#f93e3e"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.method),1)]),_:2},1024)):(0,l.Q3)("",!0),"DEAD"===e.method?((0,l.uX)(),(0,l.Wv)(q,{key:5,color:"rgb(201, 233, 104)"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.method),1)]),_:2},1024)):(0,l.Q3)("",!0)]),(0,l.Lk)("div",f,[(0,l.Lk)("div",{class:"interface-url",title:e.url},(0,o.v_)(e.url),9,b),(0,l.Lk)("div",{class:"interface-name",title:e.name},(0,o.v_)(e.name),9,F)]),(0,l.Lk)("div",{class:"status-section",onClick:t[7]||(t[7]=(0,s.D$)(()=>{},["stop"]))},[(0,l.bF)(N,{trigger:"click",onCommand:t=>x.statusClick(t,e.id)},{dropdown:(0,l.k6)(()=>[(0,l.bF)(K,null,{default:(0,l.k6)(()=>[(0,l.bF)(B,{command:"已发布",style:{color:"#67C23A"}},{default:(0,l.k6)(()=>[(0,l.bF)(M,null,{default:(0,l.k6)(()=>[(0,l.bF)(X)]),_:1}),t[22]||(t[22]=(0,l.eW)(" 已发布 "))]),_:1,__:[22]}),(0,l.bF)(B,{command:"测试中",style:{color:"#626aef"}},{default:(0,l.k6)(()=>[(0,l.bF)(M,null,{default:(0,l.k6)(()=>[(0,l.bF)(X)]),_:1}),t[23]||(t[23]=(0,l.eW)(" 测试中 "))]),_:1,__:[23]}),(0,l.bF)(B,{command:"开发中",style:{color:"#E6A23C"}},{default:(0,l.k6)(()=>[(0,l.bF)(M,null,{default:(0,l.k6)(()=>[(0,l.bF)(X)]),_:1}),t[24]||(t[24]=(0,l.eW)(" 开发中 "))]),_:1,__:[24]}),(0,l.bF)(B,{command:"已废弃",style:{color:"#909399"}},{default:(0,l.k6)(()=>[(0,l.bF)(M,null,{default:(0,l.k6)(()=>[(0,l.bF)(X)]),_:1}),t[25]||(t[25]=(0,l.eW)(" 已废弃 "))]),_:1,__:[25]})]),_:1})]),default:(0,l.k6)(()=>[(0,l.Lk)("span",{class:"status-text",style:(0,o.Tr)({color:x.buttonColor(e.status)})},["状态"!=e.status?((0,l.uX)(),(0,l.Wv)(M,{key:0,style:(0,o.Tr)({color:x.buttonColor(e.status)})},{default:(0,l.k6)(()=>[(0,l.bF)(X)]),_:2},1032,["style"])):(0,l.Q3)("",!0),(0,l.eW)(" "+(0,o.v_)(e.status)+" ",1),(0,l.bF)(M,null,{default:(0,l.k6)(()=>[(0,l.bF)(J)]),_:1})],4)]),_:2},1032,["onCommand"])]),(0,l.Lk)("div",{class:"action-section",onClick:t[8]||(t[8]=(0,s.D$)(()=>{},["stop"]))},[(0,l.bF)(W,{type:"text",onClick:t=>x.handleChildData(e)},{default:(0,l.k6)(()=>t[26]||(t[26]=[(0,l.eW)("接口Mock")])),_:2,__:[26]},1032,["onClick"]),(0,l.bF)(W,{type:"text",onClick:t=>x.clickEditStep(e.id)},{default:(0,l.k6)(()=>t[27]||(t[27]=[(0,l.eW)("调试")])),_:2,__:[27]},1032,["onClick"]),(0,l.bF)(W,{type:"text",onClick:t=>x.clickCopy(e.id)},{default:(0,l.k6)(()=>t[28]||(t[28]=[(0,l.eW)("复制")])),_:2,__:[28]},1032,["onClick"]),(0,l.bF)(W,{type:"text",onClick:t=>x.clickDel(e.id)},{default:(0,l.k6)(()=>t[29]||(t[29]=[(0,l.eW)("删除")])),_:2,__:[29]},1032,["onClick"]),(0,l.bF)(W,{type:"text",onClick:x.clickLog},{default:(0,l.k6)(()=>t[30]||(t[30]=[(0,l.eW)("操作记录")])),_:1,__:[30]},8,["onClick"])])],8,g)],2))),128))])]),_:1})]),_:1})]),_:1})]),(0,l.bF)(G,{modelValue:D.addCaseDlg,"onUpdate:modelValue":t[9]||(t[9]=e=>D.addCaseDlg=e),"destroy-on-close":!0,"with-header":!1,size:"50%",onClose:x.handleClose},{default:(0,l.k6)(()=>[(0,l.bF)(H,{treeId:D.treeId,style:{padding:"0 10px"},onCloseDialog:x.handleClose},null,8,["treeId","onCloseDialog"])]),_:1},8,["modelValue","onClose"]),(0,l.bF)(G,{modelValue:D.editCaseDlg,"onUpdate:modelValue":t[10]||(t[10]=e=>D.editCaseDlg=e),"destroy-on-close":!0,"with-header":!1,size:"50%",onClose:x.handleClose},{default:(0,l.k6)(()=>[(0,l.bF)(Z,{ref:"childRef",Interface_id:D.Interface_id,copyDlg:D.copyDlg,style:{padding:"0 10px"}},null,8,["Interface_id","copyDlg"])]),_:1},8,["modelValue","onClose"]),(0,l.bF)(G,{modelValue:D.logDlg,"onUpdate:modelValue":t[11]||(t[11]=e=>D.logDlg=e),"with-header":!1,size:"50%"},{default:(0,l.k6)(()=>[(0,l.bF)(ee,null,{default:(0,l.k6)(()=>[t[31]||(t[31]=(0,l.Lk)("b",null,"接口操作记录",-1)),(0,l.Lk)("div",C,[(0,l.bF)(ae,null,{default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(D.bugLogs,(t,a)=>((0,l.uX)(),(0,l.Wv)(te,{key:a,timestamp:e.$tools.rDate(t.create_time),placement:"top",color:"#0bbd87"},{default:(0,l.k6)(()=>[(0,l.bF)(ee,null,{default:(0,l.k6)(()=>[(0,l.Lk)("h4",null,(0,o.v_)(t.handle),1),t.remark?((0,l.uX)(),(0,l.CE)("p",y,"变更记录："+(0,o.v_)(t.remark),1)):(0,l.Q3)("",!0),(0,l.Lk)("span",v,(0,o.v_)(t.update_user),1),(0,l.Lk)("span",null,"操作于 "+(0,o.v_)(e.$tools.rTime(t.create_time)),1)]),_:2},1024)]),_:2},1032,["timestamp"]))),128))]),_:1})])]),_:1,__:[31]})]),_:1},8,["modelValue"]),D.importDlg?((0,l.uX)(),(0,l.Wv)(le,{key:0,importDlg:D.importDlg,onCloseModal:x.handleCloseModal},null,8,["importDlg","onCloseModal"])):(0,l.Q3)("",!0),(0,l.bF)(G,{modelValue:D.mockDlg,"onUpdate:modelValue":t[12]||(t[12]=e=>D.mockDlg=e),"destroy-on-close":!0,"with-header":!1,size:"60%",onClose:x.handleClose},{default:(0,l.k6)(()=>[(0,l.bF)(oe,{interfaceData:e.interfaceData,style:{padding:"0 10px"}},null,8,["interfaceData"])]),_:1},8,["modelValue","onClose"])],64)}a(44114),a(18111),a(22489),a(20116),a(61701);var D=a(31032),x=a(51219),T=a(12933),E=a(80225);const V={class:"interface-container"},L={class:"action-buttons"},I={class:"form-card"},W={class:"tags-container"},A={class:"meta-card"},M={class:"meta-content"},S={class:"meta-item"},O={class:"meta-value"},P={class:"body-type-selector"},U={key:0,class:"editor-container"},j={key:1,class:"editor-container"},R={key:2,class:"form-data-container"},$={class:"templates-container"},z={class:"code-mod"},q={class:"code-mod"},X={class:"code-mod"},J={class:"code-mod"},B={class:"templates-container"},K={class:"code-mod"},N={class:"code-mod"},Y={class:"code-mod"},Q={class:"code-mod"},H={class:"code-mod"},G={class:"code-mod"},Z={class:"code-mod"},ee={class:"code-mod"},te={class:"code-mod"},ae={class:"code-mod"},le={key:0,class:"result-section"};function oe(e,t,a,n,i,r){const d=(0,l.g2)("el-option"),c=(0,l.g2)("el-select"),u=(0,l.g2)("el-input"),m=(0,l.g2)("el-form-item"),p=(0,l.g2)("el-col"),_=(0,l.g2)("Promotion"),h=(0,l.g2)("el-icon"),g=(0,l.g2)("el-button"),k=(0,l.g2)("Plus"),f=(0,l.g2)("el-row"),b=(0,l.g2)("el-cascader"),F=(0,l.g2)("el-tag"),C=(0,l.g2)("el-form"),y=(0,l.g2)("Editor"),v=(0,l.g2)("el-tab-pane"),w=(0,l.g2)("el-radio"),D=(0,l.g2)("el-radio-group"),x=(0,l.g2)("FromData"),T=(0,l.g2)("el-tabs"),E=(0,l.g2)("caseResult"),oe=(0,l.g2)("el-scrollbar");return(0,l.uX)(),(0,l.Wv)(oe,{height:"calc(100vh)",style:{"padding-right":"0"}},{default:(0,l.k6)(()=>[(0,l.Lk)("div",V,[t[53]||(t[53]=(0,l.Lk)("div",{class:"section-header"},[(0,l.Lk)("span",{class:"section-title"},"API信息")],-1)),(0,l.bF)(C,{rules:i.rulesinterface,ref:"interfaceRef",model:i.caseInfo,"label-width":"90px",size:"small",class:"api-form"},{default:(0,l.k6)(()=>[(0,l.bF)(f,{gutter:15,class:"url-row"},{default:(0,l.k6)(()=>[(0,l.bF)(p,{xs:24,sm:24,md:16,lg:16,xl:16},{default:(0,l.k6)(()=>[(0,l.bF)(m,{prop:"url",label:"请求地址",class:"url-form-item"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:i.caseInfo.url,"onUpdate:modelValue":t[1]||(t[1]=e=>i.caseInfo.url=e),placeholder:"请输入接口地址",class:"url-input"},{prepend:(0,l.k6)(()=>[(0,l.bF)(c,{modelValue:i.caseInfo.method,"onUpdate:modelValue":t[0]||(t[0]=e=>i.caseInfo.method=e),placeholder:"请求类型",class:"method-select"},{default:(0,l.k6)(()=>[(0,l.bF)(d,{label:"GET",value:"GET",class:"method-get"}),(0,l.bF)(d,{label:"POST",value:"POST",class:"method-post"}),(0,l.bF)(d,{label:"PUT",value:"PUT",class:"method-put"}),(0,l.bF)(d,{label:"PATCH",value:"PATCH",class:"method-patch"}),(0,l.bF)(d,{label:"DELETE",value:"DELETE",class:"method-delete"}),(0,l.bF)(d,{label:"HEAD",value:"HEAD",class:"method-head"})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1})]),_:1}),(0,l.bF)(p,{xs:24,sm:24,md:8,lg:8,xl:8},{default:(0,l.k6)(()=>[(0,l.Lk)("div",L,[(0,l.bF)(g,{onClick:r.runCase,type:"success",class:"action-button"},{default:(0,l.k6)(()=>[(0,l.bF)(h,null,{default:(0,l.k6)(()=>[(0,l.bF)(_)]),_:1}),t[29]||(t[29]=(0,l.eW)("调试"))]),_:1,__:[29]},8,["onClick"]),(0,l.bF)(g,{onClick:r.addClick,type:"primary",class:"action-button"},{default:(0,l.k6)(()=>[(0,l.bF)(h,null,{default:(0,l.k6)(()=>[(0,l.bF)(k)]),_:1}),t[30]||(t[30]=(0,l.eW)("新建"))]),_:1,__:[30]},8,["onClick"])])]),_:1})]),_:1}),(0,l.Lk)("div",I,[(0,l.bF)(f,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(p,{xs:24,sm:24,md:7,lg:7,xl:7},{default:(0,l.k6)(()=>[(0,l.bF)(m,{label:"节点/模块",class:"form-item"},{default:(0,l.k6)(()=>[(0,l.bF)(b,{modelValue:i.caseInfo.treenode,"onUpdate:modelValue":t[2]||(t[2]=e=>i.caseInfo.treenode=e),options:i.options,props:{label:"name",value:"id",checkStrictly:!0},onChange:r.removeCascaderAriaOwns,onVisibleChange:r.removeCascaderAriaOwns,onExpandChange:r.removeCascaderAriaOwns,clearable:"","change-on-select":"",filterable:"",placeholder:"请选择节点/模块",class:"full-width"},null,8,["modelValue","options","onChange","onVisibleChange","onExpandChange"])]),_:1})]),_:1}),(0,l.bF)(p,{xs:24,sm:24,md:10,lg:10,xl:10},{default:(0,l.k6)(()=>[(0,l.bF)(m,{label:"接口名称",prop:"name",class:"form-item"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:i.caseInfo.name,"onUpdate:modelValue":t[3]||(t[3]=e=>i.caseInfo.name=e),placeholder:"请输入接口名称",clearable:"",class:"full-width"},null,8,["modelValue"])]),_:1})]),_:1}),(0,l.bF)(p,{xs:24,sm:24,md:7,lg:7,xl:7},{default:(0,l.k6)(()=>[(0,l.bF)(m,{label:"数据锁定",class:"form-item"},{default:(0,l.k6)(()=>[(0,l.bF)(c,{modelValue:i.caseInfo.YApi_status,"onUpdate:modelValue":t[4]||(t[4]=e=>i.caseInfo.YApi_status=e),placeholder:"请选择",class:"full-width"},{default:(0,l.k6)(()=>[(0,l.bF)(d,{label:"已锁定",value:"1"}),(0,l.bF)(d,{label:"无需锁定",value:"0"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),(0,l.bF)(f,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(p,{xs:24,sm:24,md:12,lg:12,xl:12},{default:(0,l.k6)(()=>[(0,l.bF)(m,{label:"描述",class:"form-item"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:i.caseInfo.desc,"onUpdate:modelValue":t[5]||(t[5]=e=>i.caseInfo.desc=e),type:"textarea",clearable:"",class:"full-width",rows:2},null,8,["modelValue"])]),_:1})]),_:1}),(0,l.bF)(p,{xs:24,sm:24,md:12,lg:12,xl:12},{default:(0,l.k6)(()=>[(0,l.bF)(m,{label:"接口标签",class:"form-item"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",W,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(i.caseInfo.interface_tag,e=>((0,l.uX)(),(0,l.Wv)(F,{key:e,class:"tag-item",type:r.getRandomType(),closable:"","disable-transitions":!1,onClose:t=>r.removeTag(e),effect:"light",size:"small"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e),1)]),_:2},1032,["type","onClose"]))),128)),i.state.editTag?((0,l.uX)(),(0,l.Wv)(u,{key:0,ref:"caseTagInputRef",modelValue:i.state.tagValue,"onUpdate:modelValue":t[6]||(t[6]=e=>i.state.tagValue=e),size:"small",onKeyup:(0,s.jR)(r.addTag,["enter"]),onBlur:r.addTag,class:"tag-input",maxlength:"30"},null,8,["modelValue","onKeyup","onBlur"])):((0,l.uX)(),(0,l.Wv)(g,{key:1,size:"small",onClick:r.showEditTag,class:"add-tag-btn"},{default:(0,l.k6)(()=>t[31]||(t[31]=[(0,l.eW)("+ 添加")])),_:1,__:[31]},8,["onClick"]))])]),_:1})]),_:1})]),_:1})]),(0,l.Lk)("div",A,[(0,l.Lk)("div",M,[(0,l.Lk)("div",S,[t[32]||(t[32]=(0,l.Lk)("div",{class:"meta-label"},"创建用户",-1)),(0,l.Lk)("div",O,(0,o.v_)(r.username),1)])])])]),_:1},8,["rules","model"]),t[54]||(t[54]=(0,l.Lk)("div",{class:"section-header"},[(0,l.Lk)("span",{class:"section-title"},"请求信息")],-1)),(0,l.bF)(T,{type:"border-card",class:"request-tabs"},{default:(0,l.k6)(()=>[(0,l.bF)(v,{label:"请求头(headers)"},{default:(0,l.k6)(()=>[(0,l.bF)(y,{modelValue:i.headers,"onUpdate:modelValue":t[7]||(t[7]=e=>i.headers=e)},null,8,["modelValue"])]),_:1}),(0,l.bF)(v,{label:"查询参数(Params)"},{default:(0,l.k6)(()=>[(0,l.bF)(y,{modelValue:i.params,"onUpdate:modelValue":t[8]||(t[8]=e=>i.params=e)},null,8,["modelValue"])]),_:1}),(0,l.bF)(v,{label:"请求体(Body)"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",P,[(0,l.bF)(D,{modelValue:i.paramType,"onUpdate:modelValue":t[9]||(t[9]=e=>i.paramType=e),class:"param-type-group"},{default:(0,l.k6)(()=>[(0,l.bF)(w,{label:"json"},{default:(0,l.k6)(()=>t[33]||(t[33]=[(0,l.eW)("application/json")])),_:1,__:[33]}),(0,l.bF)(w,{label:"data"},{default:(0,l.k6)(()=>t[34]||(t[34]=[(0,l.eW)("x-www-form-urlencoded")])),_:1,__:[34]}),(0,l.bF)(w,{label:"formData"},{default:(0,l.k6)(()=>t[35]||(t[35]=[(0,l.eW)("form-data")])),_:1,__:[35]})]),_:1},8,["modelValue"])]),"json"===i.paramType?((0,l.uX)(),(0,l.CE)("div",U,[(0,l.bF)(y,{modelValue:i.json,"onUpdate:modelValue":t[10]||(t[10]=e=>i.json=e)},null,8,["modelValue"])])):"data"===i.paramType?((0,l.uX)(),(0,l.CE)("div",j,[(0,l.bF)(y,{modelValue:i.data,"onUpdate:modelValue":t[11]||(t[11]=e=>i.data=e)},null,8,["modelValue"])])):"formData"===i.paramType?((0,l.uX)(),(0,l.CE)("div",R,[(0,l.bF)(x,{modelValue:i.file,"onUpdate:modelValue":t[12]||(t[12]=e=>i.file=e)},null,8,["modelValue"])])):(0,l.Q3)("",!0)]),_:1}),(0,l.bF)(v,{label:"前置脚本"},{default:(0,l.k6)(()=>[(0,l.bF)(f,{gutter:16},{default:(0,l.k6)(()=>[(0,l.bF)(p,{xs:24,sm:24,md:18,lg:18,xl:18,class:"script-editor"},{default:(0,l.k6)(()=>[(0,l.bF)(y,{modelValue:i.caseInfo.setup_script,"onUpdate:modelValue":t[13]||(t[13]=e=>i.caseInfo.setup_script=e),lang:"python",theme:"monokai"},null,8,["modelValue"])]),_:1}),(0,l.bF)(p,{xs:24,sm:24,md:6,lg:6,xl:6,class:"script-templates"},{default:(0,l.k6)(()=>[t[40]||(t[40]=(0,l.Lk)("div",{class:"templates-header"},"脚本模板",-1)),(0,l.Lk)("div",$,[(0,l.Lk)("div",z,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[14]||(t[14]=e=>r.addSetUptCodeMod("ENV"))},{default:(0,l.k6)(()=>t[36]||(t[36]=[(0,l.eW)("预设全局变量")])),_:1,__:[36]})]),(0,l.Lk)("div",q,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[15]||(t[15]=e=>r.addSetUptCodeMod("env"))},{default:(0,l.k6)(()=>t[37]||(t[37]=[(0,l.eW)("预设局部变量")])),_:1,__:[37]})]),(0,l.Lk)("div",X,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[16]||(t[16]=e=>r.addSetUptCodeMod("func"))},{default:(0,l.k6)(()=>t[38]||(t[38]=[(0,l.eW)("调用全局函数")])),_:1,__:[38]})]),(0,l.Lk)("div",J,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[17]||(t[17]=e=>r.addSetUptCodeMod("sql"))},{default:(0,l.k6)(()=>t[39]||(t[39]=[(0,l.eW)("执行sql查询")])),_:1,__:[39]})])])]),_:1,__:[40]})]),_:1})]),_:1}),(0,l.bF)(v,{label:"后置脚本"},{default:(0,l.k6)(()=>[(0,l.bF)(f,{gutter:16},{default:(0,l.k6)(()=>[(0,l.bF)(p,{xs:24,sm:24,md:18,lg:18,xl:18,class:"script-editor"},{default:(0,l.k6)(()=>[(0,l.bF)(y,{modelValue:i.caseInfo.teardown_script,"onUpdate:modelValue":t[18]||(t[18]=e=>i.caseInfo.teardown_script=e),lang:"python",theme:"monokai"},null,8,["modelValue"])]),_:1}),(0,l.bF)(p,{xs:24,sm:24,md:6,lg:6,xl:6,class:"script-templates"},{default:(0,l.k6)(()=>[t[51]||(t[51]=(0,l.Lk)("div",{class:"templates-header"},"脚本模板",-1)),(0,l.Lk)("div",B,[(0,l.Lk)("div",K,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[19]||(t[19]=e=>r.addTearDownCodeMod("getBody"))},{default:(0,l.k6)(()=>t[41]||(t[41]=[(0,l.eW)("获取响应体")])),_:1,__:[41]})]),(0,l.Lk)("div",N,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[20]||(t[20]=e=>r.addTearDownCodeMod("JSextract"))},{default:(0,l.k6)(()=>t[42]||(t[42]=[(0,l.eW)("jsonpath提取")])),_:1,__:[42]})]),(0,l.Lk)("div",Y,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[21]||(t[21]=e=>r.addTearDownCodeMod("REextract"))},{default:(0,l.k6)(()=>t[43]||(t[43]=[(0,l.eW)("正则提取")])),_:1,__:[43]})]),(0,l.Lk)("div",Q,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[22]||(t[22]=e=>r.addTearDownCodeMod("ENV"))},{default:(0,l.k6)(()=>t[44]||(t[44]=[(0,l.eW)("设置全局变量")])),_:1,__:[44]})]),(0,l.Lk)("div",H,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[23]||(t[23]=e=>r.addTearDownCodeMod("env"))},{default:(0,l.k6)(()=>t[45]||(t[45]=[(0,l.eW)("设置局部变量")])),_:1,__:[45]})]),(0,l.Lk)("div",G,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[24]||(t[24]=e=>r.addTearDownCodeMod("func"))},{default:(0,l.k6)(()=>t[46]||(t[46]=[(0,l.eW)("调用全局函数")])),_:1,__:[46]})]),(0,l.Lk)("div",Z,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[25]||(t[25]=e=>r.addTearDownCodeMod("sql"))},{default:(0,l.k6)(()=>t[47]||(t[47]=[(0,l.eW)("执行sql查询")])),_:1,__:[47]})]),(0,l.Lk)("div",ee,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[26]||(t[26]=e=>r.addTearDownCodeMod("http"))},{default:(0,l.k6)(()=>t[48]||(t[48]=[(0,l.eW)("断言HTTP状态")])),_:1,__:[48]})]),(0,l.Lk)("div",te,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[27]||(t[27]=e=>r.addTearDownCodeMod("eq"))},{default:(0,l.k6)(()=>t[49]||(t[49]=[(0,l.eW)("断言相等")])),_:1,__:[49]})]),(0,l.Lk)("div",ae,[(0,l.bF)(g,{type:"success",size:"small",plain:"",onClick:t[28]||(t[28]=e=>r.addTearDownCodeMod("contain"))},{default:(0,l.k6)(()=>t[50]||(t[50]=[(0,l.eW)("断言包含")])),_:1,__:[50]})])])]),_:1,__:[51]})]),_:1})]),_:1})]),_:1}),i.runResult?((0,l.uX)(),(0,l.CE)("div",le,[t[52]||(t[52]=(0,l.Lk)("div",{class:"section-header"},[(0,l.Lk)("span",{class:"section-title"},"执行结果")],-1)),(0,l.bF)(E,{result:i.runResult},null,8,["result"])])):(0,l.Q3)("",!0)])]),_:1})}var se=a(67638),ne=a(97167),ie=a(53629),re=a(60782),de=a(57477),ce={props:["treeId"],components:{caseResult:se.A,FromData:ne.A,Editor:ie.A,Plus:de.Plus,Promotion:de.Promotion},data(){return{rulesinterface:{name:[{required:!0,message:"请输入接口名称",trigger:"blur"}],url:[{required:!0,message:"请输入接口信息",trigger:"blur"}]},addForm:{},state:{form:{item:[{type:""},{type:"success"},{type:"info"},{type:"danger"},{type:"warning"}]},editTag:!1,tagValue:""},options:[],caseInfo:{method:"POST",interface_tag:[],url:"",name:"",treenode:this.treeId,creator:"",modifier:"",desc:"",headers:{},request:{json:{},data:null,params:{}},file:[],setup_script:"# 前置脚本(python):\n# global_tools:全局工具函数\n# data:用例数据 \n# env: 局部环境\n# ENV: 全局环境\n# db: 数据库操作对象",teardown_script:"# 后置脚本(python):\n# global_tools:全局工具函数\n# data:用例数据 \n# response:响应对象response \n# env: 局部环境\n# ENV: 全局环境\n# db: 数据库操作对象"},paramType:"json",json:"{}",data:"{}",params:"{}",headers:"{}",interfaceparams:"{}",file:[],runResult:""}},computed:{...(0,re.aH)(["pro","envId"]),username(){return window.sessionStorage.getItem("username")}},methods:{focusInput(){this.$nextTick(()=>{this.$refs.caseTagInputRef.focus()})},addTag(){this.state.editTag&&this.state.tagValue&&(this.caseInfo.interface_tag||(this.caseInfo.interface_tag=[]),this.caseInfo.interface_tag.push(this.state.tagValue),this.focusInput()),this.state.editTag=!1,this.state.tagValue=""},removeTag(e){this.caseInfo.interface_tag.splice(this.caseInfo.interface_tag.indexOf(e),1)},showEditTag(){this.state.editTag=!0,this.focusInput()},getRandomType(){const e=Math.floor(Math.random()*this.state.form.item.length);return this.state.form.item[e].type},async allTree(){const e=await this.$api.getTreeNode({project_id:this.pro.id});200===e.status&&(this.options=e.data.result)},removeCascaderAriaOwns(){this.$nextTick(()=>{const e=document.querySelectorAll(".el-cascader-panel .el-cascader-node[aria-owns]");Array.from(e).map(e=>e.removeAttribute("aria-owns"))})},addSetUptCodeMod(e){switch(e){case"ENV":this.caseInfo.setup_script+='\n# 设置全局变量 \ntest.save_global_variable("变量名",变量值)';break;case"env":this.caseInfo.setup_script+='\n# 设置局部变量  \ntest.save_env_variable("变量名",变量值)';break;case"func":this.caseInfo.setup_script+="\n# 调用全局工具函数random_mobile随机生成一个手机号码  \nmobile = global_func.random_mobile()";break;case"sql":this.caseInfo.setup_script+='\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\n# db.连接名.execute_all(sql语句) \nsql = "SELECT count(*) as count FROM futureloan.member"\nres = db.aliyun.execute_all(sql)';break}},addTearDownCodeMod(e){switch(e){case"getBody":this.caseInfo.teardown_script+="\n# Demo:获取响应体(json)  \nbody = response.json()",this.caseInfo.teardown_script+="\n# Demo2:获取响应体(字符串)  \nbody = response.text";break;case"JSextract":this.caseInfo.teardown_script+='\n# Demo:jsonpath提取response中的msg字段  \nmsg = test.json_extract(response.json(),"$..msg")';break;case"REextract":this.caseInfo.teardown_script+='\n# Demo:正则提取响应体中的数据  \nres = test.re_extract(response.text,"正则表达式",)';break;case"ENV":this.caseInfo.teardown_script+='\n# 设置全局变量 \ntest.save_global_variable("变量名",变量值)';break;case"env":this.caseInfo.teardown_script+='\n# 设置局部变量  \ntest.save_env_variable("变量名",变量值)';break;case"func":this.caseInfo.teardown_script+="\n# 调用全局工具函数random_mobile随机生成一个手机号码  \nmobile = global_func.random_mobile()";break;case"sql":this.caseInfo.teardown_script+='\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\n# db.连接名.execute_all(sql语句) \nsql = "SELECT count(*) as count FROM futureloan.member"\nres = db.aliyun.execute_all(sql)';break;case"http":this.caseInfo.teardown_script+='\n# 断言http状态码 \n# Demo:断言http状态码是否为200  \ntest.assertion("相等",200,response.status_code)';break;case"eq":this.caseInfo.teardown_script+='\n# 断言相等 \ntest.assertion("相等","预期结果","实际结果")';break;case"contain":this.caseInfo.teardown_script+='\n# 断言包含:预期结果中的内容在实际结果中是否存在 \ntest.assertion("包含","预期结果","实际结果")';break}},getEditData(){let e={...this.caseInfo};if(e.project=this.pro.id,e.type="api",delete e.status,e.treenode&&e.treenode.length>0){const t=e.treenode[e.treenode.length-1];console.log(t),e.treenode=t}else console.log("列表为空");e.interface_tag={tag:[...e.interface_tag]},e.creator=this.username;try{e.headers=JSON.parse(this.headers)}catch(t){return(0,x.nk)({message:"提交的headers数据 json格式错误，请检查！",type:"warning",duration:1e3}),null}if("json"===this.paramType){const l=a(67160);try{e.request={json:l.parse(this.json)},e.request.data=null,e.file=[]}catch(t){return(0,x.nk)({message:"提交的application/json数据json格式错误，请检查！",type:"warning",duration:1e3}),null}}else if("data"===this.paramType)try{e.request={data:JSON.parse(this.data)},e.request.json=null,e.file=[]}catch(t){return(0,x.nk)({message:"提交的x-www-form-urlencoded数据json格式错误，请检查！",type:"warning",duration:1e3}),null}else"formData"===this.paramType&&(e.file=this.file,e.request={});try{return e.request.params=JSON.parse(this.params),e}catch(t){return(0,x.nk)({message:"提交的Params数据json格式错误，请检查！",type:"warning",duration:1e3}),null}},async addClick(){this.$refs.interfaceRef.validate(async e=>{if(!e)return;const t=this.getEditData();console.log("新增的参数：",t);const a=await this.$api.createNewInterface(t);201===a.status&&((0,x.nk)({type:"success",message:"添加成功",duration:1e3}),this.triggerClose())})},async runCase(){this.envId?this.$refs.interfaceRef.validate(async e=>{if(!e)return;const t=this.getEditData();t.interface={url:this.caseInfo.url,method:this.caseInfo.method};const a={data:t,env:this.envId},l=await this.$api.runNewCase(a);200===l.status&&(this.runResult=l.data,(0,x.nk)({type:"success",message:"执行完毕",duration:1e3}))}):(0,x.nk)({type:"warning",message:"当前未选中执行环境!",duration:1e3})},triggerClose(){this.$emit("close-dialog")}},created(){this.allTree()}},ue=a(71241);const me=(0,ue.A)(ce,[["render",oe],["__scopeId","data-v-29be43b7"]]);var pe=me;const _e={key:0},he={class:"help-box"},ge={class:"curl-support-list"},ke={key:1},fe={class:"help-box"},be={class:"curl-support-list"},Fe={key:2},Ce={class:"help-box"},ye={key:0},ve={key:1},we={key:3},De={class:"help-box"},xe={class:"curl-support-list"},Te={key:4},Ee={class:"help-box"},Ve={class:"curl-support-list"},Le={key:5},Ie={class:"help-box"},We={class:"curl-support-list"},Ae={slot:"footer",class:"dialog-footer"};function Me(e,t,a,s,n,i){const r=(0,l.g2)("icon"),d=(0,l.g2)("el-check-tag"),c=(0,l.g2)("el-tag"),u=(0,l.g2)("upload-filled"),m=(0,l.g2)("el-icon"),p=(0,l.g2)("el-upload"),_=(0,l.g2)("el-form-item"),h=(0,l.g2)("el-cascader"),g=(0,l.g2)("el-form"),k=(0,l.g2)("el-input"),f=(0,l.g2)("el-radio"),b=(0,l.g2)("el-radio-group"),F=(0,l.g2)("el-button"),C=(0,l.g2)("el-dialog");return(0,l.uX)(),(0,l.Wv)(C,{modelValue:n.importDlg,"onUpdate:modelValue":t[14]||(t[14]=e=>n.importDlg=e),title:"导入接口",width:"60%","before-close":i.clickClear,"custom-class":"class_dialog",top:"50px"},{footer:(0,l.k6)(()=>[(0,l.Lk)("span",Ae,[(0,l.bF)(F,{onClick:i.clickClear},{default:(0,l.k6)(()=>t[91]||(t[91]=[(0,l.eW)("取消")])),_:1,__:[91]},8,["onClick"]),(0,l.bF)(F,{type:"primary",loading:n.isLoading,onClick:t[13]||(t[13]=e=>i.importClick(n.selectedOption))},{default:(0,l.k6)(()=>t[92]||(t[92]=[(0,l.eW)("导入")])),_:1,__:[92]},8,["loading"])])]),default:(0,l.k6)(()=>[(0,l.Lk)("div",null,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(n.options,(e,t)=>((0,l.uX)(),(0,l.Wv)(d,{key:t,class:(0,o.C4)([{selected:n.selectedOption===e.value},"option"]),onClick:t=>i.selectOption(e.value)},{default:(0,l.k6)(()=>[(0,l.Lk)("i",null,[(0,l.bF)(r,{icon:e.icon,class:"importIcon"},null,8,["icon"])]),(0,l.eW)((0,o.v_)(e.label),1)]),_:2},1032,["class","onClick"]))),128))]),"Postman"===n.selectedOption?((0,l.uX)(),(0,l.CE)("div",_e,[(0,l.Lk)("div",he,[t[23]||(t[23]=(0,l.eW)(" 支持导入 ")),(0,l.bF)(c,null,{default:(0,l.k6)(()=>t[15]||(t[15]=[(0,l.eW)("Postman")])),_:1,__:[15]}),t[24]||(t[24]=(0,l.eW)(" 集合、环境、全局数据。 ")),(0,l.Lk)("div",ge,[t[22]||(t[22]=(0,l.Lk)("p",null,[(0,l.Lk)("strong",null,"支持的格式：")],-1)),(0,l.Lk)("ul",null,[(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[16]||(t[16]=[(0,l.eW)("Collection v2.0/v2.1")])),_:1,__:[16]}),t[17]||(t[17]=(0,l.eW)(" Postman集合"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[18]||(t[18]=[(0,l.eW)("Environment")])),_:1,__:[18]}),t[19]||(t[19]=(0,l.eW)(" Postman环境变量"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[20]||(t[20]=[(0,l.eW)("Globals")])),_:1,__:[20]}),t[21]||(t[21]=(0,l.eW)(" Postman全局变量"))])])])]),(0,l.bF)(g,{model:n.postmanForm,rules:n.rulesPostman,ref:"postmanRef"},{default:(0,l.k6)(()=>[(0,l.bF)(_,{label:"Postman文件",prop:"file"},{default:(0,l.k6)(()=>[(0,l.bF)(p,{class:"upload-demo",drag:"",action:"#","auto-upload":!1,"on-change":i.handlePostmanFileChange,limit:1},{tip:(0,l.k6)(()=>t[25]||(t[25]=[(0,l.Lk)("div",{class:"el-upload__tip"}," 请上传Postman导出的JSON文件 ",-1)])),default:(0,l.k6)(()=>[(0,l.bF)(m,{class:"el-icon--upload"},{default:(0,l.k6)(()=>[(0,l.bF)(u)]),_:1}),t[26]||(t[26]=(0,l.Lk)("div",{class:"el-upload__text"},[(0,l.eW)(" 拖拽文件到此处或 "),(0,l.Lk)("em",null,"点击上传")],-1))]),_:1,__:[26]},8,["on-change"])]),_:1}),(0,l.bF)(_,{label:"节点/模块",prop:"treenode"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{modelValue:n.postmanForm.treenode,"onUpdate:modelValue":t[0]||(t[0]=e=>n.postmanForm.treenode=e),options:n.treeOptions,props:{label:"name",value:"id",checkStrictly:!0},onChange:i.removeCascaderAriaOwns,onVisibleChange:i.removeCascaderAriaOwns,onExpandChange:i.removeCascaderAriaOwns,clearable:"","change-on-select":"",filterable:"",placeholder:"请选择节点/模块"},null,8,["modelValue","options","onChange","onVisibleChange","onExpandChange"])]),_:1})]),_:1},8,["model","rules"])])):(0,l.Q3)("",!0),"Curl"===n.selectedOption?((0,l.uX)(),(0,l.CE)("div",ke,[(0,l.Lk)("div",fe,[t[51]||(t[51]=(0,l.eW)(" 支持导入 ")),(0,l.bF)(c,null,{default:(0,l.k6)(()=>t[27]||(t[27]=[(0,l.eW)("Curl")])),_:1,__:[27]}),t[52]||(t[52]=(0,l.eW)(" 命令，从外部复制后自动解析为接口内容。 ")),(0,l.Lk)("div",be,[t[50]||(t[50]=(0,l.Lk)("p",null,[(0,l.Lk)("strong",null,"支持的参数：")],-1)),(0,l.Lk)("ul",null,[(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[28]||(t[28]=[(0,l.eW)("-X, --request")])),_:1,__:[28]}),t[29]||(t[29]=(0,l.eW)(" 指定HTTP请求方法"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[30]||(t[30]=[(0,l.eW)("-H, --header")])),_:1,__:[30]}),t[31]||(t[31]=(0,l.eW)(" 指定请求头"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[32]||(t[32]=[(0,l.eW)("-d, --data")])),_:1,__:[32]}),t[33]||(t[33]=(0,l.eW)(" 指定请求体数据"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[34]||(t[34]=[(0,l.eW)("--data-binary")])),_:1,__:[34]}),t[35]||(t[35]=(0,l.eW)(" 二进制数据"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[36]||(t[36]=[(0,l.eW)("--data-urlencode")])),_:1,__:[36]}),t[37]||(t[37]=(0,l.eW)(" URL编码数据"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[38]||(t[38]=[(0,l.eW)("--data-raw")])),_:1,__:[38]}),t[39]||(t[39]=(0,l.eW)(" 原始数据"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[40]||(t[40]=[(0,l.eW)("-F, --form")])),_:1,__:[40]}),t[41]||(t[41]=(0,l.eW)(" 表单数据"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[42]||(t[42]=[(0,l.eW)("-A, --user-agent")])),_:1,__:[42]}),t[43]||(t[43]=(0,l.eW)(" 用户代理"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[44]||(t[44]=[(0,l.eW)("-e, --referer")])),_:1,__:[44]}),t[45]||(t[45]=(0,l.eW)(" 引用页"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[46]||(t[46]=[(0,l.eW)("-b, --cookie")])),_:1,__:[46]}),t[47]||(t[47]=(0,l.eW)(" Cookie"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[48]||(t[48]=[(0,l.eW)("-u, --user")])),_:1,__:[48]}),t[49]||(t[49]=(0,l.eW)(" 基本认证"))])])])]),(0,l.bF)(g,{model:n.curlForm,rules:n.rulesCurl,ref:"curlRef"},{default:(0,l.k6)(()=>[(0,l.bF)(_,{label:"Curl命令",prop:"curlContent"},{default:(0,l.k6)(()=>[(0,l.bF)(k,{modelValue:n.curlForm.curlContent,"onUpdate:modelValue":t[1]||(t[1]=e=>n.curlForm.curlContent=e),type:"textarea",rows:8,placeholder:'请粘贴curl命令，例如：curl -X POST "https://api.example.com/v1/users" -H "Content-Type: application/json" -d {"name":"test"}',clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(_,{label:"节点/模块",prop:"treenode"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{modelValue:n.curlForm.treenode,"onUpdate:modelValue":t[2]||(t[2]=e=>n.curlForm.treenode=e),options:n.treeOptions,props:{label:"name",value:"id",checkStrictly:!0},onChange:i.removeCascaderAriaOwns,onVisibleChange:i.removeCascaderAriaOwns,onExpandChange:i.removeCascaderAriaOwns,clearable:"","change-on-select":"",filterable:"",placeholder:"请选择节点/模块"},null,8,["modelValue","options","onChange","onVisibleChange","onExpandChange"])]),_:1})]),_:1},8,["model","rules"])])):(0,l.Q3)("",!0),"YApi"===n.selectedOption?((0,l.uX)(),(0,l.CE)("div",Fe,[(0,l.Lk)("div",Ce,[t[54]||(t[54]=(0,l.eW)(" 支持手动、自动导入 ")),(0,l.bF)(c,null,{default:(0,l.k6)(()=>t[53]||(t[53]=[(0,l.eW)("YApi")])),_:1,__:[53]}),t[55]||(t[55]=(0,l.eW)(" 平台的接口数据。 "))]),(0,l.Lk)("div",null,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(n.optionYApi,(e,t)=>((0,l.uX)(),(0,l.Wv)(d,{key:t,class:(0,o.C4)([{selectedYApi:n.selectedOptionYApi===e.value},"optionYApi"]),onClick:t=>i.selectOptionYApi(e.value)},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.label),1)]),_:2},1032,["class","onClick"]))),128))]),0===n.selectedOptionYApi?((0,l.uX)(),(0,l.CE)("div",ye,[(0,l.bF)(g,{inline:!0,model:n.formInline,class:"demo-form-inline",rules:n.rulesYApi,ref:"YApiRef"},{default:(0,l.k6)(()=>[(0,l.bF)(_,{label:"平台地址",prop:"url"},{default:(0,l.k6)(()=>[(0,l.bF)(k,{modelValue:n.formInline.url,"onUpdate:modelValue":t[3]||(t[3]=e=>n.formInline.url=e),placeholder:"请输入YApi平台项目地址",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(_,{label:"平台TOKEN",prop:"token"},{default:(0,l.k6)(()=>[(0,l.bF)(k,{modelValue:n.formInline.token,"onUpdate:modelValue":t[4]||(t[4]=e=>n.formInline.token=e),placeholder:"请输入YApi平台项目token",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(_,{label:"平台项目ID",prop:"YApiId"},{default:(0,l.k6)(()=>[(0,l.bF)(k,{modelValue:n.formInline.YApiId,"onUpdate:modelValue":t[5]||(t[5]=e=>n.formInline.YApiId=e),placeholder:"请输入YApi平台项目id",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(_,{label:"节点/模块",prop:"treenode"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{modelValue:n.formInline.treenode,"onUpdate:modelValue":t[6]||(t[6]=e=>n.formInline.treenode=e),options:n.treeOptions,props:{label:"name",value:"id",checkStrictly:!0},onChange:i.removeCascaderAriaOwns,onVisibleChange:i.removeCascaderAriaOwns,onExpandChange:i.removeCascaderAriaOwns,clearable:"","change-on-select":"",filterable:"",placeholder:"请选择节点/模块"},null,8,["modelValue","options","onChange","onVisibleChange","onExpandChange"])]),_:1})]),_:1},8,["model","rules"])])):((0,l.uX)(),(0,l.CE)("div",ve,t[56]||(t[56]=[(0,l.Lk)("div",{class:"help-warning"}," 因与定时任务功能入口重复，请移至定时任务功能入口进行自动同步 ",-1)])))])):(0,l.Q3)("",!0),"Apipost"===n.selectedOption?((0,l.uX)(),(0,l.CE)("div",we,[(0,l.Lk)("div",De,[t[63]||(t[63]=(0,l.eW)(" 支持导入 ")),(0,l.bF)(c,null,{default:(0,l.k6)(()=>t[57]||(t[57]=[(0,l.eW)("Apipost")])),_:1,__:[57]}),t[64]||(t[64]=(0,l.eW)(" 集合数据。 ")),(0,l.Lk)("div",xe,[t[62]||(t[62]=(0,l.Lk)("p",null,[(0,l.Lk)("strong",null,"支持的格式：")],-1)),(0,l.Lk)("ul",null,[(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[58]||(t[58]=[(0,l.eW)("Collection")])),_:1,__:[58]}),t[59]||(t[59]=(0,l.eW)(" Apipost集合"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[60]||(t[60]=[(0,l.eW)("Environment")])),_:1,__:[60]}),t[61]||(t[61]=(0,l.eW)(" Apipost环境变量"))])])])]),(0,l.bF)(g,{model:n.apipostForm,rules:n.rulesApipost,ref:"apipostRef"},{default:(0,l.k6)(()=>[(0,l.bF)(_,{label:"Apipost文件",prop:"file"},{default:(0,l.k6)(()=>[(0,l.bF)(p,{class:"upload-demo",drag:"",action:"#","auto-upload":!1,"on-change":i.handleApipostFileChange,limit:1},{tip:(0,l.k6)(()=>t[65]||(t[65]=[(0,l.Lk)("div",{class:"el-upload__tip"}," 请上传Apipost导出的JSON文件 ",-1)])),default:(0,l.k6)(()=>[(0,l.bF)(m,{class:"el-icon--upload"},{default:(0,l.k6)(()=>[(0,l.bF)(u)]),_:1}),t[66]||(t[66]=(0,l.Lk)("div",{class:"el-upload__text"},[(0,l.eW)(" 拖拽文件到此处或 "),(0,l.Lk)("em",null,"点击上传")],-1))]),_:1,__:[66]},8,["on-change"])]),_:1}),(0,l.bF)(_,{label:"节点/模块",prop:"treenode"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{modelValue:n.apipostForm.treenode,"onUpdate:modelValue":t[7]||(t[7]=e=>n.apipostForm.treenode=e),options:n.treeOptions,props:{label:"name",value:"id",checkStrictly:!0},onChange:i.removeCascaderAriaOwns,onVisibleChange:i.removeCascaderAriaOwns,onExpandChange:i.removeCascaderAriaOwns,clearable:"","change-on-select":"",filterable:"",placeholder:"请选择节点/模块"},null,8,["modelValue","options","onChange","onVisibleChange","onExpandChange"])]),_:1})]),_:1},8,["model","rules"])])):(0,l.Q3)("",!0),"Swagger"===n.selectedOption?((0,l.uX)(),(0,l.CE)("div",Te,[(0,l.Lk)("div",Ee,[t[75]||(t[75]=(0,l.eW)(" 支持导入 ")),(0,l.bF)(c,null,{default:(0,l.k6)(()=>t[67]||(t[67]=[(0,l.eW)("Swagger")])),_:1,__:[67]}),t[76]||(t[76]=(0,l.eW)(" 接口文档。 ")),(0,l.Lk)("div",Ve,[t[74]||(t[74]=(0,l.Lk)("p",null,[(0,l.Lk)("strong",null,"支持的格式：")],-1)),(0,l.Lk)("ul",null,[(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[68]||(t[68]=[(0,l.eW)("OpenAPI 2.0/3.0")])),_:1,__:[68]}),t[69]||(t[69]=(0,l.eW)(" Swagger规范"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[70]||(t[70]=[(0,l.eW)("JSON/YAML")])),_:1,__:[70]}),t[71]||(t[71]=(0,l.eW)(" 文件格式"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[72]||(t[72]=[(0,l.eW)("URL")])),_:1,__:[72]}),t[73]||(t[73]=(0,l.eW)(" Swagger文档地址"))])])])]),(0,l.bF)(g,{model:n.swaggerForm,rules:n.rulesSwagger,ref:"swaggerRef"},{default:(0,l.k6)(()=>[(0,l.bF)(_,{label:"导入方式"},{default:(0,l.k6)(()=>[(0,l.bF)(b,{modelValue:n.swaggerForm.importType,"onUpdate:modelValue":t[8]||(t[8]=e=>n.swaggerForm.importType=e)},{default:(0,l.k6)(()=>[(0,l.bF)(f,{label:"file"},{default:(0,l.k6)(()=>t[77]||(t[77]=[(0,l.eW)("文件导入")])),_:1,__:[77]}),(0,l.bF)(f,{label:"url"},{default:(0,l.k6)(()=>t[78]||(t[78]=[(0,l.eW)("URL导入")])),_:1,__:[78]})]),_:1},8,["modelValue"])]),_:1}),"file"===n.swaggerForm.importType?((0,l.uX)(),(0,l.Wv)(_,{key:0,label:"Swagger文件",prop:"file"},{default:(0,l.k6)(()=>[(0,l.bF)(p,{class:"upload-demo",drag:"",action:"#","auto-upload":!1,"on-change":i.handleSwaggerFileChange,limit:1},{tip:(0,l.k6)(()=>t[79]||(t[79]=[(0,l.Lk)("div",{class:"el-upload__tip"}," 请上传Swagger JSON或YAML文件 ",-1)])),default:(0,l.k6)(()=>[(0,l.bF)(m,{class:"el-icon--upload"},{default:(0,l.k6)(()=>[(0,l.bF)(u)]),_:1}),t[80]||(t[80]=(0,l.Lk)("div",{class:"el-upload__text"},[(0,l.eW)(" 拖拽文件到此处或 "),(0,l.Lk)("em",null,"点击上传")],-1))]),_:1,__:[80]},8,["on-change"])]),_:1})):((0,l.uX)(),(0,l.Wv)(_,{key:1,label:"Swagger URL",prop:"url"},{default:(0,l.k6)(()=>[(0,l.bF)(k,{modelValue:n.swaggerForm.url,"onUpdate:modelValue":t[9]||(t[9]=e=>n.swaggerForm.url=e),placeholder:"请输入Swagger文档URL，例如：https://petstore.swagger.io/v2/swagger.json",clearable:""},null,8,["modelValue"])]),_:1})),(0,l.bF)(_,{label:"节点/模块",prop:"treenode"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{modelValue:n.swaggerForm.treenode,"onUpdate:modelValue":t[10]||(t[10]=e=>n.swaggerForm.treenode=e),options:n.treeOptions,props:{label:"name",value:"id",checkStrictly:!0},onChange:i.removeCascaderAriaOwns,onVisibleChange:i.removeCascaderAriaOwns,onExpandChange:i.removeCascaderAriaOwns,clearable:"","change-on-select":"",filterable:"",placeholder:"请选择节点/模块"},null,8,["modelValue","options","onChange","onVisibleChange","onExpandChange"])]),_:1})]),_:1},8,["model","rules"])])):(0,l.Q3)("",!0),"Js fetch"===n.selectedOption?((0,l.uX)(),(0,l.CE)("div",Le,[(0,l.Lk)("div",Ie,[t[89]||(t[89]=(0,l.eW)(" 支持导入 ")),(0,l.bF)(c,null,{default:(0,l.k6)(()=>t[81]||(t[81]=[(0,l.eW)("JS fetch")])),_:1,__:[81]}),t[90]||(t[90]=(0,l.eW)(" 代码片段。 ")),(0,l.Lk)("div",We,[t[88]||(t[88]=(0,l.Lk)("p",null,[(0,l.Lk)("strong",null,"支持的格式：")],-1)),(0,l.Lk)("ul",null,[(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[82]||(t[82]=[(0,l.eW)("fetch")])),_:1,__:[82]}),t[83]||(t[83]=(0,l.eW)(" 原生fetch API"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[84]||(t[84]=[(0,l.eW)("axios")])),_:1,__:[84]}),t[85]||(t[85]=(0,l.eW)(" axios请求"))]),(0,l.Lk)("li",null,[(0,l.bF)(c,{size:"small"},{default:(0,l.k6)(()=>t[86]||(t[86]=[(0,l.eW)("jQuery")])),_:1,__:[86]}),t[87]||(t[87]=(0,l.eW)(" $.ajax请求"))])])])]),(0,l.bF)(g,{model:n.jsFetchForm,rules:n.rulesJsFetch,ref:"jsFetchRef"},{default:(0,l.k6)(()=>[(0,l.bF)(_,{label:"JS代码",prop:"jsContent"},{default:(0,l.k6)(()=>[(0,l.bF)(k,{modelValue:n.jsFetchForm.jsContent,"onUpdate:modelValue":t[11]||(t[11]=e=>n.jsFetchForm.jsContent=e),type:"textarea",rows:8,placeholder:'请粘贴JS代码，例如：fetch("https://api.example.com/data", { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ name: "test" }) })',clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(_,{label:"节点/模块",prop:"treenode"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{modelValue:n.jsFetchForm.treenode,"onUpdate:modelValue":t[12]||(t[12]=e=>n.jsFetchForm.treenode=e),options:n.treeOptions,props:{label:"name",value:"id",checkStrictly:!0},onChange:i.removeCascaderAriaOwns,onVisibleChange:i.removeCascaderAriaOwns,onExpandChange:i.removeCascaderAriaOwns,clearable:"","change-on-select":"",filterable:"",placeholder:"请选择节点/模块"},null,8,["modelValue","options","onChange","onVisibleChange","onExpandChange"])]),_:1})]),_:1},8,["model","rules"])])):(0,l.Q3)("",!0)]),_:1},8,["modelValue","before-close"])}var Se=a(99181);const Oe=(0,ue.A)(Se.A,[["render",Me],["__scopeId","data-v-7d83888f"]]);var Pe=Oe;const Ue={style:{margin:"15px"}},je={style:{display:"flex","justify-content":"space-between","margin-bottom":"25px"}},Re={style:{color:"#606266","font-size":"14px",display:"flex","align-items":"center"}},$e={style:{"margin-top":"15px"}},ze=["innerHTML"],qe={class:"system-icon-content"},Xe={style:{display:"flex","align-items":"center"}},Je={style:{"margin-bottom":"30px","font-size":"16px"}},Be={class:"menu-container"},Ke={class:"menu-content-wrapper"},Ne={key:0,class:"menu-content"},Ye={style:{"margin-left":"10px"}},Qe={key:1,class:"menu-content"},He={style:{"margin-left":"10px"}},Ge={key:2,class:"menu-content"},Ze={class:"dialog-footer",style:{"text-align":"center"}};function et(e,t,a,n,i,r){const d=(0,l.g2)("el-switch"),c=(0,l.g2)("CircleCheck"),u=(0,l.g2)("el-icon"),m=(0,l.g2)("el-alert"),p=(0,l.g2)("el-option"),_=(0,l.g2)("el-select"),h=(0,l.g2)("el-input"),g=(0,l.g2)("el-form-item"),k=(0,l.g2)("el-col"),f=(0,l.g2)("EditPen"),b=(0,l.g2)("el-button"),F=(0,l.g2)("el-row"),C=(0,l.g2)("el-form"),y=(0,l.g2)("el-table-column"),v=(0,l.g2)("More"),w=(0,l.g2)("el-dropdown-item"),D=(0,l.g2)("el-dropdown-menu"),x=(0,l.g2)("el-dropdown"),T=(0,l.g2)("el-table"),E=(0,l.g2)("Plus"),V=(0,l.g2)("el-scrollbar"),L=(0,l.g2)("QuestionFilled"),I=(0,l.g2)("el-tooltip"),W=(0,l.g2)("el-menu-item"),A=(0,l.g2)("el-menu"),M=(0,l.g2)("el-radio"),S=(0,l.g2)("el-radio-group"),O=(0,l.g2)("Editor"),P=(0,l.g2)("el-divider"),U=(0,l.g2)("el-check-tag"),j=(0,l.g2)("el-input-number"),R=(0,l.g2)("el-dialog");return(0,l.uX)(),(0,l.CE)(l.FK,null,[(0,l.bF)(V,{height:"calc(100vh)",style:{"padding-right":"10px"}},{default:(0,l.k6)(()=>[(0,l.Lk)("div",Ue,[(0,l.Lk)("div",je,[t[17]||(t[17]=(0,l.Lk)("b",{style:{color:"#101828CC","font-size":"15px"}},"请求信息",-1)),(0,l.Lk)("span",Re,[t[16]||(t[16]=(0,l.eW)("开启/禁用Mock ")),(0,l.bF)(d,{modelValue:i.mockData.status,"onUpdate:modelValue":t[0]||(t[0]=e=>i.mockData.status=e),"inline-prompt":"",size:"default",style:{"--el-switch-on-color":"#53a8ff","margin-left":"10px"}},null,8,["modelValue"])])]),i.mockData.mockTitle?((0,l.uX)(),(0,l.Wv)(m,{key:0,style:{"margin-bottom":"10px"},title:i.mockData.mockTitle,type:"success"},{icon:(0,l.k6)(()=>[(0,l.bF)(u,null,{default:(0,l.k6)(()=>[(0,l.bF)(c)]),_:1})]),_:1},8,["title"])):(0,l.Q3)("",!0),(0,l.bF)(C,{rules:i.rulesInterface,ref:"interfaceRef",model:i.mockData},{default:(0,l.k6)(()=>[(0,l.bF)(F,{gutter:24},{default:(0,l.k6)(()=>[(0,l.bF)(k,{span:13},{default:(0,l.k6)(()=>[(0,l.bF)(g,{prop:"url"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{modelValue:i.mockData.url,"onUpdate:modelValue":t[2]||(t[2]=e=>i.mockData.url=e),placeholder:"请输入接口地址",style:{"font-size":"14px"}},{prepend:(0,l.k6)(()=>[(0,l.bF)(_,{modelValue:i.mockData.method,"onUpdate:modelValue":t[1]||(t[1]=e=>i.mockData.method=e),placeholder:"请求类型",size:"small",style:{width:"100px",color:"black"}},{default:(0,l.k6)(()=>[(0,l.bF)(p,{label:"GET",value:"GET",style:{color:"rgba(204,73,145,0.87)"}}),(0,l.bF)(p,{label:"POST",value:"POST",style:{color:"#61affe"}}),(0,l.bF)(p,{label:"PUT",value:"PUT",style:{color:"#fca130"}}),(0,l.bF)(p,{label:"PATCH",value:"PATCH",style:{color:"#50e3c2"}}),(0,l.bF)(p,{label:"DELETE",value:"DELETE",style:{color:"#f93e3e"}}),(0,l.bF)(p,{label:"HEAD",value:"HEAD",style:{color:"rgb(201, 233, 104)"}})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1})]),_:1}),(0,l.bF)(k,{span:8},{default:(0,l.k6)(()=>[(0,l.bF)(g,{prop:"name",label:"接口名称"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{modelValue:i.mockData.name,"onUpdate:modelValue":t[3]||(t[3]=e=>i.mockData.name=e),placeholder:"请输入接口名称"},null,8,["modelValue"])]),_:1})]),_:1}),(0,l.bF)(k,{span:3},{default:(0,l.k6)(()=>[i.mockData.mockTitle?((0,l.uX)(),(0,l.Wv)(b,{key:0,onClick:t[4]||(t[4]=e=>r.editMock()),type:"primary"},{default:(0,l.k6)(()=>[(0,l.bF)(u,null,{default:(0,l.k6)(()=>[(0,l.bF)(f)]),_:1}),t[18]||(t[18]=(0,l.eW)(" 保存 "))]),_:1,__:[18]})):((0,l.uX)(),(0,l.Wv)(b,{key:1,onClick:t[5]||(t[5]=e=>r.addMock()),type:"primary"},{default:(0,l.k6)(()=>[(0,l.bF)(u,null,{default:(0,l.k6)(()=>[(0,l.bF)(f)]),_:1}),t[19]||(t[19]=(0,l.eW)(" 保存 "))]),_:1,__:[19]}))]),_:1})]),_:1})]),_:1},8,["rules","model"]),t[25]||(t[25]=(0,l.Lk)("div",null,[(0,l.Lk)("b",{style:{color:"#101828CC","font-size":"15px"}},"Mock 期望")],-1)),(0,l.Lk)("div",$e,[(0,l.bF)(T,{data:i.mockData.MockDetail,stripe:"","empty-text":"暂无数据",border:""},{default:(0,l.k6)(()=>[(0,l.bF)(y,{label:"名称",width:"180",prop:"name",align:"center"}),(0,l.bF)(y,{label:"条件",prop:"remark",align:"center"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",{style:{color:"#66b1ff"},innerHTML:e.row.remark},null,8,ze)]),_:1}),(0,l.bF)(y,{label:"创建人",width:"140",prop:"creator",align:"center"}),(0,l.bF)(y,{label:"操作",width:"200",align:"center"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",null,[(0,l.bF)(b,{onClick:t=>r.openDialog("view",e.row),size:"small",type:"success",disabled:!i.mockData.status},{default:(0,l.k6)(()=>t[20]||(t[20]=[(0,l.eW)("详情")])),_:2,__:[20]},1032,["onClick","disabled"]),(0,l.bF)(b,{onClick:t=>r.openDialog("edit",e.row),size:"small",type:"primary",disabled:!i.mockData.status},{default:(0,l.k6)(()=>t[21]||(t[21]=[(0,l.eW)("编辑")])),_:2,__:[21]},1032,["onClick","disabled"]),(0,l.bF)(x,{trigger:"click"},{dropdown:(0,l.k6)(()=>[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(w,{command:"复制",style:{color:"#409eff"},onClick:t=>r.copyMockDetail(e.row)},{default:(0,l.k6)(()=>t[22]||(t[22]=[(0,l.eW)(" 复制 ")])),_:2,__:[22]},1032,["onClick"]),(0,l.bF)(w,{command:"删除",style:{color:"#409eff"},onClick:t=>r.clickDel(e.row.id)},{default:(0,l.k6)(()=>t[23]||(t[23]=[(0,l.eW)(" 删除 ")])),_:2,__:[23]},1032,["onClick"])]),_:2},1024)]),default:(0,l.k6)(()=>[(0,l.bF)(b,{style:{"margin-left":"15px"},type:"text",size:"small",disabled:!i.mockData.status},{default:(0,l.k6)(()=>[(0,l.bF)(u,null,{default:(0,l.k6)(()=>[(0,l.bF)(v)]),_:1})]),_:1},8,["disabled"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])]),(0,l.bF)(b,{type:"primary",style:{"margin-bottom":"20px","margin-top":"10px"},onClick:t[6]||(t[6]=e=>r.openDialog("add")),disabled:!i.mockData.status},{default:(0,l.k6)(()=>[(0,l.bF)(u,null,{default:(0,l.k6)(()=>[(0,l.bF)(E)]),_:1}),t[24]||(t[24]=(0,l.eW)(" 新建期望 "))]),_:1,__:[24]},8,["disabled"]),t[26]||(t[26]=(0,l.Lk)("div",{style:{"margin-bottom":"20px"}},[(0,l.Lk)("b",{style:{color:"#101828CC","font-size":"15px","margin-bottom":"15px"}},"调用记录")],-1))])]),_:1}),(0,l.bF)(R,{title:i.dialogTitle,modelValue:i.dialogVisible,"onUpdate:modelValue":t[15]||(t[15]=e=>i.dialogVisible=e),width:"65%","before-close":r.closeDialog,top:"40px","destroy-on-close":"","custom-class":"class_dialog"},{footer:(0,l.k6)(()=>[(0,l.Lk)("div",Ze,[(0,l.bF)(b,{onClick:r.closeDialog},{default:(0,l.k6)(()=>t[41]||(t[41]=[(0,l.eW)("取 消")])),_:1,__:[41]},8,["onClick"]),"新建期望"===i.dialogTitle?((0,l.uX)(),(0,l.Wv)(b,{key:0,type:"primary",onClick:r.addMockDetail},{default:(0,l.k6)(()=>t[42]||(t[42]=[(0,l.eW)("保 存")])),_:1,__:[42]},8,["onClick"])):(0,l.Q3)("",!0),"编辑期望"===i.dialogTitle?((0,l.uX)(),(0,l.Wv)(b,{key:1,type:"primary",onClick:r.editMockDetail},{default:(0,l.k6)(()=>t[43]||(t[43]=[(0,l.eW)("保 存")])),_:1,__:[43]},8,["onClick"])):(0,l.Q3)("",!0)])]),default:(0,l.k6)(()=>[(0,l.bF)(V,{height:"82vh",style:{"padding-right":"20px"}},{default:(0,l.k6)(()=>[(0,l.Lk)("div",qe,[(0,l.bF)(C,{model:i.detailData,rules:i.rulesDetail,ref:"detailRef","label-position":"top"},{default:(0,l.k6)(()=>[(0,l.bF)(g,{label:"期望名称",prop:"name"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{modelValue:i.detailData.name,"onUpdate:modelValue":t[7]||(t[7]=e=>i.detailData.name=e)},null,8,["modelValue"])]),_:1}),(0,l.bF)(g,{label:"参数条件",prop:"conditionForm"},{default:(0,l.k6)(()=>[(0,l.bF)(T,{data:i.detailData.conditionForm,stripe:"","empty-text":"暂无数据",border:""},{default:(0,l.k6)(()=>[(0,l.bF)(y,{label:"参数位置",width:"180",prop:"location",align:"center"},{default:(0,l.k6)(e=>[(0,l.bF)(_,{clearable:"",modelValue:e.row.location,"onUpdate:modelValue":t=>e.row.location=t,placeholder:"请选择参数位置",style:{width:"155px",color:"black",padding:"0px"}},{default:(0,l.k6)(()=>[(0,l.bF)(p,{label:"query",value:"query"}),(0,l.bF)(p,{label:"path",value:"path"}),(0,l.bF)(p,{label:"header",value:"header"}),(0,l.bF)(p,{label:"body",value:"body"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),(0,l.bF)(y,{label:"参数名",prop:"paramName",align:"center"},{default:(0,l.k6)(e=>[(0,l.bF)(h,{modelValue:e.row.paramName,"onUpdate:modelValue":t=>e.row.paramName=t},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),(0,l.bF)(y,{label:"比较",width:"180",prop:"comparison",align:"center"},{default:(0,l.k6)(e=>[(0,l.bF)(_,{clearable:"",modelValue:e.row.comparison,"onUpdate:modelValue":t=>e.row.comparison=t,placeholder:"请选择",style:{width:"155px",color:"black",padding:"0px"}},{default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(i.options,e=>((0,l.uX)(),(0,l.Wv)(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),(0,l.bF)(y,{label:"参数值",prop:"value",align:"center"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",Xe,[(0,l.bF)(h,{style:{width:"240px","margin-right":"10px"},modelValue:e.row.value,"onUpdate:modelValue":t=>e.row.value=t},null,8,["modelValue","onUpdate:modelValue"]),(0,l.bF)(_,{modelValue:e.row.valueType,"onUpdate:modelValue":t=>e.row.valueType=t,placeholder:"String",style:{width:"90px",padding:"0"}},{default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(i.typeOptions,e=>((0,l.uX)(),(0,l.Wv)(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]),_:1}),(0,l.bF)(y,{label:"操作",width:"100",align:"center"},{default:(0,l.k6)(e=>[(0,l.bF)(b,{type:"text",size:"small",disabled:e.$index<1,onClick:(0,s.D$)(t=>r.deleteRow(e.$index),["prevent"])},{default:(0,l.k6)(()=>t[27]||(t[27]=[(0,l.eW)(" 删除 ")])),_:2,__:[27]},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"]),"查看期望"!==i.dialogTitle?((0,l.uX)(),(0,l.Wv)(b,{key:0,style:{width:"100%","margin-top":"20px","background-color":"#ecf5ff",color:"#409eff"},onClick:r.onAddItem},{default:(0,l.k6)(()=>t[28]||(t[28]=[(0,l.eW)(" add Data ")])),_:1,__:[28]},8,["onClick"])):(0,l.Q3)("",!0)]),_:1}),(0,l.Lk)("div",Je,[t[29]||(t[29]=(0,l.eW)("IP 条件 ")),(0,l.bF)(I,{content:"开启后该期望仅对 指定IP 的地址生效; 填写示例：127.0.0.1:8080",enterable:!1,placement:"top",effect:"light"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{style:{color:"#53a8ff","font-size":"16px"}},{default:(0,l.k6)(()=>[(0,l.bF)(L)]),_:1})]),_:1}),(0,l.bF)(d,{modelValue:i.detailData.ipCode,"onUpdate:modelValue":t[8]||(t[8]=e=>i.detailData.ipCode=e),"inline-prompt":"",size:"small",style:{"--el-switch-on-color":"#53a8ff","margin-left":"10px"}},null,8,["modelValue"]),i.detailData.ipCode?((0,l.uX)(),(0,l.Wv)(h,{key:0,style:{"margin-top":"10px"},modelValue:i.detailData.ipInput,"onUpdate:modelValue":t[9]||(t[9]=e=>i.detailData.ipInput=e)},null,8,["modelValue"])):(0,l.Q3)("",!0)]),(0,l.bF)(g,{label:"返回数据"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",Be,[(0,l.bF)(A,{"default-active":i.activeIndex,mode:"horizontal",onSelect:r.handleSelect,class:"response-menu"},{default:(0,l.k6)(()=>[(0,l.bF)(W,{index:"1"},{default:(0,l.k6)(()=>t[30]||(t[30]=[(0,l.eW)("响应体(response)")])),_:1,__:[30]}),(0,l.bF)(W,{index:"2"},{default:(0,l.k6)(()=>t[31]||(t[31]=[(0,l.eW)("响应头(headers)")])),_:1,__:[31]}),(0,l.bF)(W,{index:"3"},{default:(0,l.k6)(()=>t[32]||(t[32]=[(0,l.eW)("更多设置")])),_:1,__:[32]})]),_:1},8,["default-active","onSelect"]),(0,l.Lk)("div",Ke,["1"===i.activeIndex?((0,l.uX)(),(0,l.CE)("div",Ne,[(0,l.bF)(S,{modelValue:i.detailData.response.paramType,"onUpdate:modelValue":t[10]||(t[10]=e=>i.detailData.response.paramType=e)},{default:(0,l.k6)(()=>[(0,l.bF)(M,{label:"json"},{default:(0,l.k6)(()=>t[33]||(t[33]=[(0,l.eW)("application/json")])),_:1,__:[33]}),(0,l.bF)(M,{label:"xml"},{default:(0,l.k6)(()=>t[34]||(t[34]=[(0,l.eW)("application/xml")])),_:1,__:[34]}),(0,l.bF)(M,{label:"html"},{default:(0,l.k6)(()=>t[35]||(t[35]=[(0,l.eW)("html/plain")])),_:1,__:[35]})]),_:1},8,["modelValue"]),(0,l.bF)(I,{content:i.sampleResponse,placement:"top",effect:"light"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{style:{"margin-left":"-6px",color:"#67c23a"}},{default:(0,l.k6)(()=>[(0,l.bF)(L)]),_:1})]),_:1},8,["content"]),t[37]||(t[37]=(0,l.Lk)("span",{style:{"margin-left":"6px",color:"#67c23a"}},"支持MockJS语法",-1)),(0,l.Lk)("div",null,[(0,l.bF)(F,null,{default:(0,l.k6)(()=>[(0,l.bF)(k,{span:18},{default:(0,l.k6)(()=>[(0,l.bF)(O,{modelValue:i.detailData.response.data,"onUpdate:modelValue":t[11]||(t[11]=e=>i.detailData.response.data=e)},null,8,["modelValue"])]),_:1}),(0,l.bF)(k,{style:{"margin-top":"-15px"},span:6},{default:(0,l.k6)(()=>[(0,l.bF)(P,null,{default:(0,l.k6)(()=>t[36]||(t[36]=[(0,l.eW)("脚本模板")])),_:1,__:[36]}),(0,l.bF)(V,{height:"380px"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",Ye,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(i.mockTags,e=>((0,l.uX)(),(0,l.CE)("div",{key:e,style:{"margin-bottom":"7px"}},[(0,l.bF)(I,{content:`${e.txt}`,placement:"top",effect:"light"},{default:(0,l.k6)(()=>[(0,l.bF)(U,{type:"info",onClick:t=>r.copyText(e.mockJS),style:{"margin-right":"10px"}},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.mockJS),1)]),_:2},1032,["onClick"])]),_:2},1032,["content"])]))),128))])]),_:1})]),_:1})]),_:1})])])):(0,l.Q3)("",!0),"2"===i.activeIndex?((0,l.uX)(),(0,l.CE)("div",Qe,[(0,l.bF)(I,{content:i.sampleHeader,placement:"top",effect:"light"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{style:{color:"#67c23a"}},{default:(0,l.k6)(()=>[(0,l.bF)(L)]),_:1})]),_:1},8,["content"]),t[39]||(t[39]=(0,l.Lk)("span",{style:{"margin-left":"6px",color:"#67c23a"}},"示例",-1)),(0,l.bF)(F,null,{default:(0,l.k6)(()=>[(0,l.bF)(k,{span:18},{default:(0,l.k6)(()=>[(0,l.bF)(O,{modelValue:i.detailData.headers,"onUpdate:modelValue":t[12]||(t[12]=e=>i.detailData.headers=e)},null,8,["modelValue"])]),_:1}),(0,l.bF)(k,{style:{"margin-top":"-15px"},span:6},{default:(0,l.k6)(()=>[(0,l.bF)(P,null,{default:(0,l.k6)(()=>t[38]||(t[38]=[(0,l.eW)("脚本模板")])),_:1,__:[38]}),(0,l.bF)(V,{height:"380px"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",He,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(i.mockTags,e=>((0,l.uX)(),(0,l.CE)("div",{key:e,style:{"margin-bottom":"7px"}},[(0,l.bF)(I,{content:`${e.txt}`,placement:"top",effect:"light"},{default:(0,l.k6)(()=>[(0,l.bF)(U,{type:"info",onClick:t=>r.copyText(e.mockJS),style:{"margin-right":"10px"}},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.mockJS),1)]),_:2},1032,["onClick"])]),_:2},1032,["content"])]))),128))])]),_:1})]),_:1})]),_:1})])):(0,l.Q3)("",!0),"3"===i.activeIndex?((0,l.uX)(),(0,l.CE)("div",Ge,[(0,l.bF)(g,{label:"返回 HTTP 状态码",prop:"statusCode","label-position":e.top,"label-width":"100px"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{style:{width:"150px"},modelValue:i.detailData.config.statusCode,"onUpdate:modelValue":t[13]||(t[13]=e=>i.detailData.config.statusCode=e)},null,8,["modelValue"])]),_:1},8,["label-position"]),(0,l.bF)(g,{label:"返回延迟",prop:"time"},{default:(0,l.k6)(()=>[(0,l.bF)(j,{modelValue:i.detailData.config.time,"onUpdate:modelValue":t[14]||(t[14]=e=>i.detailData.config.time=e),min:0,max:999,size:"small","controls-position":"right",placeholder:"秒"},null,8,["modelValue"]),t[40]||(t[40]=(0,l.Lk)("span",{style:{"margin-left":"10px"}},"秒",-1))]),_:1,__:[40]})])):(0,l.Q3)("",!0)])])]),_:1})]),_:1},8,["model","rules"])])]),_:1})]),_:1},8,["title","modelValue","before-close"])],64)}var tt={props:["interfaceData"],components:{caseResult:se.A,Editor:ie.A,CircleCheck:de.CircleCheck,EditPen:de.EditPen,More:de.More,Plus:de.Plus,QuestionFilled:de.QuestionFilled},computed:{username(){return window.sessionStorage.getItem("username")}},data(){return{mockDlg:!0,dialogVisible:!1,dialogType:"",dialogTitle:"",activeIndex:"1",mockData:{id:"",newInterface:"",method:"",name:"",url:"",status:null,mockTitle:"",MockDetail:[]},detailData:{id:"",name:"",conditionForm:[{location:"",paramName:"",comparison:"",value:"",valueType:"String"}],ipCode:!1,ipInput:"",response:{paramType:"json",data:"{}"},headers:"{}",config:{statusCode:"200",time:"0"},creator:"",remark:""},rulesInterface:{url:[{required:!0,message:"请输入接口路径信息",trigger:"blur"}],name:[{required:!0,message:"请输入接口名称",trigger:"blur"}]},rulesDetail:{name:[{required:!0,message:"请输入期望名称",trigger:"blur"}]},mockLog:[{create_time:"2024-02-18T10:30:00",method:"GET",url:"/api/v1/user/login",ip:"***********",status_code:"200",time_consuming:"0.1s"},{create_time:"2024-02-18T10:30:00",method:"GET",url:"/api/v1/user/login",ip:"***********",status_code:"200",time_consuming:"0.1s"},{create_time:"2024-02-18T10:30:00",method:"POST",url:"/api/v1/user/login",ip:"*************",status_code:"400",time_consuming:"0.1s"},{create_time:"2024-02-18T10:30:00",method:"POST",url:"/api/v1/user/login",ip:"***********",status_code:"200",time_consuming:"0.5s"},{create_time:"2024-02-18T10:30:00",method:"POST",url:"/api/v1/user/login",ip:"***********",status_code:"200",time_consuming:"0.5s"},{create_time:"2024-02-18T10:30:00",method:"POST",url:"/api/v1/user/login",ip:"***********",status_code:"200",time_consuming:"0.5s"},{create_time:"2024-02-18T10:30:00",method:"POST",url:"/api/v1/user/login",ip:"***********",status_code:"200",time_consuming:"0.5s"}],options:[{value:"equal",label:"等于"},{value:"notEqual",label:"不等于"},{value:"contains",label:"包含"},{value:"notContains",label:"不包含"},{value:"greaterThan",label:"大于"},{value:"lessThan",label:"小于"},{value:"greaterThanOrEqual",label:"大于等于"},{value:"lessThanOrEqual",label:"小于等于"},{value:"empty",label:"空"},{value:"notEmpty",label:"非空"}],typeOptions:[{value:"String",label:"String"},{value:"Integer",label:"Integer"},{value:"Array",label:"Array"},{value:"Boolean",label:"Boolean"},{value:"Float",label:"Float"}],sampleResponse:'示例\n    {\n        "name": "@name",\n        "age": \'@integer(18, 60)\',\n        "birth": "@date(\'yyyy-MM-dd\')",\n        "email": "@email"\n      }',sampleHeader:'{\n        "Content-Type": "application/json",\n        "Authorization": "Bearer {{token}}"\n      }',mockTags:[{mockJS:"@name()",txt:"随机生成名字"},{mockJS:"@integer(20, 40)",txt:"随机生成20到40之间的值"},{mockJS:"@email()",txt:"随机生成邮箱"},{mockJS:"@telephone()",txt:"返回一个随机的11位手机号码"},{mockJS:"@natural(1,100)",txt:"返回一个随机的1-100的自然数（大于等于 0 的整数）"},{mockJS:"@float( 1, 10, 2, 5 )",txt:"返回一个随机的浮点数，整数1-10，小数部分位数的最小值2，最大值5"},{mockJS:"@character(pool)",txt:"从字符串池返回随机的字符"},{mockJS:"@string( pool, 1, 10 )",txt:"从字符串池返回一个随机字符串，字符数1-10"},{mockJS:"@range( 1, 100, 1 )",txt:"返回一个整型数组，参数分别：start：起始值，stop：结束值，step：步长"},{mockJS:"@now('yyyy-MM-dd HH:mm:ss')",txt:"返回当前日期字符串。例：2014-04-29 20:08:38"},{mockJS:"@date('yyyy-MM-dd')",txt:"返回一个随机的日期字符串。例：1983-01-29"},{mockJS:"@guid()",txt:"随机生成一个 GUID。例：eFD616Bd-e149-c98E-a041-5e12ED0C94Fd"},{mockJS:"@increment(1)",txt:"随机生成主键，从1起，整数自增的步长"},{mockJS:"@url('http')",txt:"随机生成一个http URL"},{mockJS:"@protocol()",txt:"随机生成一个 URL 协议。例：http ftp"},{mockJS:"@domain()",txt:"随机生成一个域名"},{mockJS:"@province()",txt:"随机生成一个（中国）省（或直辖市、自治区、特别行政区）"},{mockJS:"@city()",txt:"随机生成一个（中国）市"},{mockJS:"@county()",txt:"随机生成一个（中国）县"},{mockJS:"@county(true)",txt:"随机生成一个（中国）县（带省市）。例：甘肃省 白银市 会宁县"},{mockJS:"@zip()",txt:"随机生成一个邮政编码"},{mockJS:"@color()",txt:"随机生成颜色，格式为 '#RRGGBB'"},{mockJS:"@paragraph()",txt:"随机生成一段文本"},{mockJS:"@cparagraph()",txt:"随机生成一段中文文本"},{mockJS:"@word()",txt:"随机生成一个单词"}]}},methods:{closeModal(){this.$emit("close-modal")},clickClear(){this.closeModal()},clickAdd(){this.addDlg=!0},closeDialog(){this.dialogVisible=!1,this.getMockData(),this.detailData={name:"",conditionForm:[{location:"",paramName:"",comparison:"",value:"",valueType:"String"}],ipCode:!1,ipInput:"",response:{paramType:"json",data:"{}"},headers:"{}",config:{statusCode:"200",time:"0"}}},onAddItem(){const e={location:"",paramName:"",comparison:"",value:"",valueType:"String"};this.detailData.conditionForm.push(e)},deleteRow(e){this.detailData.conditionForm.splice(e,1)},handleSelect(e){this.activeIndex=e},async getMockData(){const e=await this.$api.getMock(this.interfaceData.id);200===e.status&&(this.mockData.id=e.data.id,this.mockData.name=this.interfaceData.name,this.mockData.url=this.interfaceData.url,this.mockData.method=this.interfaceData.method,this.mockData.newInterface=this.interfaceData.id,this.mockData.status=e.data.status,this.mockData.mockTitle=e.data.MockUrl,this.mockData.MockDetail=e.data.MockDetail)},async editInterface(){const e={method:this.mockData.method,name:this.mockData.name,url:this.mockData.url};await this.$api.updatenewInterface(this.interfaceData.id,e)},async editMock(){const e={...this.mockData};delete e.mockTitle,delete e.MockDetail;const t=await this.$api.updateMock(this.mockData.id,e);200===t.status&&((0,x.nk)({type:"success",message:"保存成功",duration:1e3}),this.editInterface(),this.getMockData())},async addMock(){const e={...this.mockData};e.creator=this.username,delete e.id,delete e.mockTitle,delete e.MockDetail;const t=await this.$api.createMock(e);201===t.status&&(this.editInterface(),this.getMockData())},async addMockDetail(){const e={...this.detailData};delete e.id,e.creator=this.username,e.mock=this.mockData.id,console.log(e);const t=await this.$api.createDetail(e);201===t.status&&((0,x.nk)({type:"success",message:"保存成功",duration:1e3}),this.getMockData(),this.closeDialog())},async editMockDetail(){const e={...this.detailData};delete e.creator,e.modifier=this.username;const t=await this.$api.updateDetail(e.id,e);200===t.status&&((0,x.nk)({type:"success",message:"保存成功",duration:1e3}),this.getMockData(),this.closeDialog())},async copyMockDetail(e){const t=e;delete t.id,t.creator=this.username,t.name=t.name+"_副本";const a=await this.$api.createDetail(t);201===a.status&&((0,x.nk)({type:"success",message:"保存成功",duration:1e3}),this.getMockData())},clickDel(e){T.s.confirm("确定要删除该期望吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.delMockDetail(e)}).catch(()=>{(0,x.nk)({type:"info",message:"取消删除",duration:1e3})})},async delMockDetail(e){const t=await this.$api.delDetail(e);204===t.status&&((0,x.nk)({type:"success",message:"删除成功",duration:1e3}),this.getMockData())},openDialog(e,t){switch(this.dialogType=e,this.dialogVisible=!0,e){case"add":this.dialogTitle="新建期望";break;case"edit":this.dialogTitle="编辑期望",this.detailData=t;break;case"view":this.dialogTitle="查看期望",this.detailData=t;break;default:this.dialogTitle="";break}},copyText(e){const t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select();try{const e=document.execCommand("copy");e?(0,x.nk)({message:"已复制到剪贴板",type:"success"}):x.nk.error("复制失败，请手动复制")}catch(a){console.error("复制失败:",a),x.nk.error("复制失败，请手动复制")}document.body.removeChild(t)}},created(){this.getMockData()}};const at=(0,ue.A)(tt,[["render",et],["__scopeId","data-v-172d417a"]]);var lt=at,ot={components:{treeNode:D.A,newEditCase:E.A,addCase:pe,interfaceImport:Pe,mockInterface:lt,Delete:de.Delete,Plus:de.Plus,View:de.View,Star:de.Star,Upload:de.Upload,Flag:de.Flag,ArrowDown:de.ArrowDown},computed:{buttonText(){return this.showOnlySelf?"取消只看自己创建":"只看自己创建"},...(0,re.aH)(["pro","testEnvs","envId"]),username(){return window.sessionStorage.getItem("username")},currentEnv(){return this.envId&&this.testEnvs&&0!==this.testEnvs.length?this.testEnvs.find(e=>e.id===this.envId)||this.testEnvs[0]:null},env:{get(){return this.envId},set(e){this.selectEnv(e)}}},data(){return{treeId:"",filterText:{name:"",status:""},tableData:[],editCaseDlg:!1,addCaseDlg:!1,logDlg:!1,importDlg:!1,Interface_id:"",copyDlg:!1,showOnlySelf:!1,selectedOption:"",dialogVisible:!1,selectedEnvironment:"",multipleSelection:[],bugLogs:[{create_time:"2024-02-18T10:30:00",handle:"修复了一个bug",remark:"这是修复bug的备注",update_user:"张三"},{create_time:"2024-02-17T14:20:00",handle:"重新测试了bug",remark:"接口名称登录变更为tms登录接口",update_user:"李四"},{create_time:"2024-02-16T09:45:00",handle:"提交了一个新的bug",update_user:"王五"}],interfaceCount:0,options:[{value:"开发中",label:"开发中"},{value:"测试中",label:"测试中"},{value:"已发布",label:"已发布"},{value:"已废弃",label:"已废弃"}],mockDlg:!1}},methods:{...(0,re.PY)(["selectEnv"]),handleSelectionChange(e){this.multipleSelection=e.map(e=>e.id)},handleSingleSelect(e,t){e?this.multipleSelection.includes(t)||this.multipleSelection.push(t):this.multipleSelection=this.multipleSelection.filter(e=>e!==t)},getRowClassName(e){switch(e){case"GET":return"--el-card-border-color:#61affe";case"POST":return"--el-card-border-color:#49cc90";case"PUT":return"--el-card-border-color:#49cc90";case"DELETE":return"--el-card-border-color:#f93e3e";case"PATCH":return"--el-card-border-color:#50e3c2";default:return""}},async handleTreeClick(e,t,a){let l=this.pro.id;if(t){let a=t.name,o=t.status;const s=await this.$api.getNewInterfaces(e,l,a,o);200===s.status&&(this.treeId=e,this.tableData=s.data,this.interfaceCount=s.data.length)}else if(a){let o=t.name,s=t.status;const n=await this.$api.getNewInterfaces(e,l,o,s,a);200===n.status&&(this.treeId=e,this.tableData=n.data,this.interfaceCount=n.data.length)}else{const t=await this.$api.getNewInterfaces(e,l);200===t.status&&(this.treeId=e,this.tableData=t.data,this.interfaceCount=t.data.length)}},async delInterface(e){const t=await this.$api.deleteNewInterface(e);204===t.status&&((0,x.nk)({type:"success",message:"删除成功",duration:1e3}),this.handleTreeClick(this.treeId),this.filterText={status:"",name:""},this.$refs.multipleTable.clearSelection())},async delAllInterface(){if(0===this.multipleSelection.length)return void(0,x.nk)({type:"warning",message:"请勾选数据后再操作！",duration:2e3});const e={item_ids:this.multipleSelection},t=await this.$api.deleteAllNewInterfaces(e);200===t.status&&((0,x.nk)({type:"success",message:"删除成功",duration:1e3}),this.handleTreeClick(this.treeId),this.filterText={status:"",name:""},this.multipleSelection=[],this.$refs.multipleTable.clearSelection())},handlenewInterfacesClick(){this.handleTreeClick(this.treeId,this.filterText)},clickAdd(){this.addCaseDlg=!0},clickDel(e){T.s.confirm("确定要删除该接口吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.delInterface(e)}).catch(()=>{(0,x.nk)({type:"info",message:"取消删除",duration:1e3})})},handleClose(){this.addCaseDlg=!1,this.editCaseDlg=!1,this.mockDlg=!1,this.copyDlg=!1,this.handleTreeClick(this.treeId)},clickEditStep(e){this.Interface_id=e,this.editCaseDlg=!0,this.$nextTick(()=>{this.$refs.childRef.getInterfaceInfo(this.Interface_id)})},clickCopy(e){this.copyDlg=!0,this.clickEditStep(e)},clickLog(){this.logDlg=!0},userInterface(){this.showOnlySelf=!this.showOnlySelf,this.showOnlySelf?this.handleTreeClick(this.treeId,"",this.username):this.handleTreeClick(this.treeId)},confirmSelection(){this.env=this.selectedEnvironment,this.selectedEnvironmentName=this.testEnvs.find(e=>e.id===this.selectedEnvironment).name,this.dialogVisible=!1},importClick(){this.importDlg=!0},handleCloseModal(){this.importDlg=!1,this.handleTreeClick(this.treeId)},buttonColor(e){switch(e){case"已发布":return"#67C23A";case"测试中":return"#626aef";case"开发中":return"#E6A23C";case"已废弃":return"#909399";default:return"#409eff"}},async statusClick(e,t){let a={status:e};const l=await this.$api.updateNewInterface(t,a);200===l.status&&this.handleTreeClick(this.treeId)},handleChildData(e){this.interfaceData=e,this.mockDlg=!0}}};const st=(0,ue.A)(ot,[["render",w],["__scopeId","data-v-19855cc5"]]);var nt=st},99181:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){var core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(18111),core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_iterator_constructor_js__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es_iterator_map_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(61701),core_js_modules_es_iterator_map_js__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es_iterator_map_js__WEBPACK_IMPORTED_MODULE_1__),core_js_modules_web_dom_exception_stack_js__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(64979),core_js_modules_web_dom_exception_stack_js__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_web_dom_exception_stack_js__WEBPACK_IMPORTED_MODULE_2__),_iconify_vue__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(82484),vuex__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(60782),element_plus__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(51219),_element_plus_icons_vue__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(57477);__webpack_exports__.A={components:{Icon:_iconify_vue__WEBPACK_IMPORTED_MODULE_3__.In,Upload:_element_plus_icons_vue__WEBPACK_IMPORTED_MODULE_6__.Upload},props:["importDlg"],data(){return{isLoading:!1,importDlg:this.importDlg,selectedOption:"Postman",selectedOptionYApi:0,rulesYApi:{url:[{required:!0,message:"请输入YApi平台项目地址",trigger:"blur"}],token:[{required:!0,message:"请输入YApi平台项目token",trigger:"blur"}],YApiId:[{required:!0,message:"请输入YApi平台项目id",trigger:"blur"}],treenode:[{required:!0,message:"请选择节点/模块",trigger:"blur"}]},rulesCurl:{curlContent:[{required:!0,message:"请输入Curl命令",trigger:"blur"}],treenode:[{required:!0,message:"请选择节点/模块",trigger:"blur"}]},rulesPostman:{file:[{required:!0,message:"请选择Postman文件",trigger:"change"}],treenode:[{required:!0,message:"请选择节点/模块",trigger:"blur"}]},rulesApipost:{file:[{required:!0,message:"请选择Apipost文件",trigger:"change"}],treenode:[{required:!0,message:"请选择节点/模块",trigger:"blur"}]},rulesSwagger:{file:[{required:!0,message:"请选择Swagger文件",trigger:"change"}],url:[{required:!0,message:"请输入Swagger文档URL",trigger:"blur"}],treenode:[{required:!0,message:"请选择节点/模块",trigger:"blur"}]},rulesJsFetch:{jsContent:[{required:!0,message:"请输入JS代码",trigger:"blur"}],treenode:[{required:!0,message:"请选择节点/模块",trigger:"blur"}]},options:[{value:"Postman",label:"Postman",icon:"devicon:postman"},{value:"Apipost",label:"Apipost",icon:"logos:appcircle-icon"},{value:"Curl",label:"Curl",icon:"logos:codio"},{value:"Swagger",label:"Swagger",icon:"vscode-icons:file-type-swagger"},{value:"Js fetch",label:"Js fetch",icon:"logos:nodejs-icon"},{value:"YApi",label:"YApi",icon:"logos:yii"}],optionYApi:[{value:0,label:"手动同步"},{value:1,label:"自动同步"}],curlForm:{curlContent:"",treenode:""},postmanForm:{file:null,treenode:""},apipostForm:{file:null,treenode:""},swaggerForm:{importType:"file",file:null,url:"",treenode:""},jsFetchForm:{jsContent:"",treenode:""},fileList:[{name:"food.jpeg",url:"https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100"},{name:"food2.jpeg",url:"https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100"}],formInline:{token:"069a46f10333f6923285d9d312c0c912f2db0286b99c3a8ae3544fca38eb1f54",YApiId:17139,treenode:"",format:"list",project:"",url:"http://api.doc.jiyou-tech.com"},treeOptions:[]}},computed:{...(0,vuex__WEBPACK_IMPORTED_MODULE_4__.aH)(["pro"])},methods:{closeModal(){this.$emit("close-modal")},clickClear(){this.closeModal()},importClick(e){"YApi"===e?this.getYApiImport():"Curl"===e?this.getCurlImport():"Postman"===e?this.getPostmanImport():"Apipost"===e?this.getApipostImport():"Swagger"===e?this.getSwaggerImport():"Js fetch"===e&&this.getJsFetchImport()},selectOption(e){this.selectedOption=e},selectOptionYApi(e){this.selectedOptionYApi=e},handleChange(e,t){this.fileList=this.fileList.slice(-3)},handlePostmanFileChange(e){this.postmanForm.file=e.raw},handleApipostFileChange(e){this.apipostForm.file=e.raw},handleSwaggerFileChange(e){this.swaggerForm.file=e.raw},removeCascaderAriaOwns(){this.$nextTick(()=>{const e=document.querySelectorAll(".el-cascader-panel .el-cascader-node[aria-owns]");Array.from(e).map(e=>e.removeAttribute("aria-owns"))})},parseCurlCommand(e){let t={method:"POST",url:"",headers:{},data:null};const a=e.match(/-X\s+([A-Z]+)/i)||e.match(/--request\s+([A-Z]+)/i);a&&(t.method=a[1].toUpperCase());let l=null;const o=e.match(/curl\s+['"]([^'"]+)['"]/i);if(o)l=o[1];else{const t=e.match(/curl\s+(\S+)/i);t&&(l=t[1])}l&&(t.url=l.replace(/^['"]|['"]$/g,""));const s=[...e.matchAll(/-H\s+['"]([^:]+):\s*([^'"]+)['"]/gi),...e.matchAll(/--header\s+['"]([^:]+):\s*([^'"]+)['"]/gi)];for(const g of s)t.headers[g[1].trim()]=g[2].trim();const n=[...e.matchAll(/-b\s+['"]([^'"]+)['"]/gi),...e.matchAll(/--cookie\s+['"]([^'"]+)['"]/gi)];for(const g of n)t.headers["Cookie"]=g[1].trim();let i=this.extractQuotedValue(e,"--data-raw");if(i){try{t.data=JSON.parse(i)}catch(h){t.data=i}return t}let r=this.extractQuotedValue(e,"-d")||this.extractQuotedValue(e,"--data");if(r){try{t.data=JSON.parse(r)}catch(h){t.data=this.parseDataString(r)}return t}let d=this.extractQuotedValue(e,"--data-binary");if(d)return t.data=d,t.headers["Content-Type"]||(t.headers["Content-Type"]="application/octet-stream"),t;let c=this.extractQuotedValue(e,"--data-urlencode");if(c)return t.data=this.parseDataString(c),t.headers["Content-Type"]||(t.headers["Content-Type"]="application/x-www-form-urlencoded"),t;const u=[...e.matchAll(/-F\s+['"]([^=]+)=([^'"]*)['"]/gi),...e.matchAll(/--form\s+['"]([^=]+)=([^'"]*)['"]/gi)];if(u.length>0){const e={};for(const t of u)e[t[1].trim()]=t[2].trim();t.data=e,t.headers["Content-Type"]||(t.headers["Content-Type"]="multipart/form-data")}const m=e.match(/--user-agent\s+['"](.+?)['"]/i)||e.match(/-A\s+['"](.+?)['"]/i);m&&(t.headers["User-Agent"]=m[1]);const p=e.match(/--referer\s+['"](.+?)['"]/i)||e.match(/-e\s+['"](.+?)['"]/i);p&&(t.headers["Referer"]=p[1]);const _=e.match(/--user\s+['"](.+?)['"]/i)||e.match(/-u\s+['"](.+?)['"]/i);if(_){const e=_[1];if(e.includes(":")){const[a,l]=e.split(":"),o=btoa(`${a}:${l}`);t.headers["Authorization"]=`Basic ${o}`}}return t},extractQuotedValue(e,t){const a=new RegExp(`${t}\\s+'([^']*(?:\\\\.[^']*)*)'+`,"i"),l=new RegExp(`${t}\\s+"([^"]*(?:\\\\.[^"]*)*)"`,"i"),o=e.match(a);if(o)return o[1];const s=e.match(l);if(s)return s[1];const n=e.indexOf(t);if(-1===n)return null;let i=e.indexOf("'",n);if(-1===i&&(i=e.indexOf('"',n)),-1===i)return null;const r=e[i];let d=i+1,c=!1;while(d<e.length){if("\\"===e[d])c=!c;else{if(e[d]===r&&!c)break;c=!1}d++}return d>=e.length?null:e.substring(i+1,d)},parseDataString(e){if(e.includes("=")&&e.match(/^[^=]+=.+/)){const t={},a=e.split("&");for(const e of a){const[a,l]=e.split("=");a&&void 0!==l&&(t[decodeURIComponent(a)]=decodeURIComponent(l))}return t}return e},parseJsFetch(jsCode){let result={method:"GET",url:"",headers:{},data:null};if(jsCode.includes("fetch(")){const urlMatch=jsCode.match(/fetch\s*\(\s*["']([^"']+)["']/);urlMatch&&(result.url=urlMatch[1]);const methodMatch=jsCode.match(/method\s*:\s*["']([^"']+)["']/i);methodMatch&&(result.method=methodMatch[1].toUpperCase());const headersMatch=jsCode.match(/headers\s*:\s*(\{[^}]+\})/);if(headersMatch)try{const headersStr=headersMatch[1].replace(/(['"])?([a-zA-Z0-9_]+)(['"])?\s*:/g,'"$2": '),headers=eval(`(${headersStr})`);result.headers=headers}catch(e){console.error("解析headers失败:",e)}const bodyMatch=jsCode.match(/body\s*:\s*JSON\.stringify\s*\(([^)]+)\)/);if(bodyMatch)try{const bodyStr=bodyMatch[1].replace(/(['"])?([a-zA-Z0-9_]+)(['"])?\s*:/g,'"$2": ');result.data=eval(`(${bodyStr})`)}catch(e){console.error("解析body失败:",e)}}else if(jsCode.includes("axios(")||jsCode.includes("axios.")){const methodMatch=jsCode.match(/(?:axios|axios\.(get|post|put|delete|patch))\s*\(/i);if(methodMatch&&methodMatch[1])result.method=methodMatch[1].toUpperCase();else{const e=jsCode.match(/method\s*:\s*["']([^"']+)["']/i);e&&(result.method=e[1].toUpperCase())}const urlMatch=jsCode.match(/(?:axios|axios\.[a-z]+)\s*\(\s*["']([^"']+)["']/i)||jsCode.match(/url\s*:\s*["']([^"']+)["']/i);urlMatch&&(result.url=urlMatch[1]);const headersMatch=jsCode.match(/headers\s*:\s*(\{[^}]+\})/);if(headersMatch)try{const headersStr=headersMatch[1].replace(/(['"])?([a-zA-Z0-9_]+)(['"])?\s*:/g,'"$2": '),headers=eval(`(${headersStr})`);result.headers=headers}catch(e){console.error("解析headers失败:",e)}const dataMatch=jsCode.match(/data\s*:\s*(\{[^}]+\})/);if(dataMatch)try{const dataStr=dataMatch[1].replace(/(['"])?([a-zA-Z0-9_]+)(['"])?\s*:/g,'"$2": ');result.data=eval(`(${dataStr})`)}catch(e){console.error("解析data失败:",e)}}else if(jsCode.includes("$.ajax")||jsCode.includes("jQuery.ajax")){const urlMatch=jsCode.match(/url\s*:\s*["']([^"']+)["']/i);urlMatch&&(result.url=urlMatch[1]);const methodMatch=jsCode.match(/type\s*:\s*["']([^"']+)["']/i)||jsCode.match(/method\s*:\s*["']([^"']+)["']/i);methodMatch&&(result.method=methodMatch[1].toUpperCase());const headersMatch=jsCode.match(/headers\s*:\s*(\{[^}]+\})/);if(headersMatch)try{const headersStr=headersMatch[1].replace(/(['"])?([a-zA-Z0-9_]+)(['"])?\s*:/g,'"$2": '),headers=eval(`(${headersStr})`);result.headers=headers}catch(e){console.error("解析headers失败:",e)}const dataMatch=jsCode.match(/data\s*:\s*(\{[^}]+\})/);if(dataMatch)try{const dataStr=dataMatch[1].replace(/(['"])?([a-zA-Z0-9_]+)(['"])?\s*:/g,'"$2": ');result.data=eval(`(${dataStr})`)}catch(e){console.error("解析data失败:",e)}}return result},async getCurlImport(){this.$refs.curlRef.validate(async e=>{if(e){this.isLoading=!0;try{const e=this.parseCurlCommand(this.curlForm.curlContent);let t={method:e.method,url:e.url,headers:e.headers,body:e.data||{},project:this.pro.id};if(this.curlForm.treenode&&this.curlForm.treenode.length>0){const e=this.curlForm.treenode[this.curlForm.treenode.length-1];t.treenode=e}const a=await this.$api.getCurlImport(t);201===a.status&&((0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"success",message:"Curl导入成功",duration:1e3}),this.closeModal())}catch(t){(0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"error",message:"Curl解析失败，请检查格式是否正确",duration:2e3}),console.error("Curl解析错误:",t)}finally{this.isLoading=!1}}})},async getYApiImport(){this.$refs.YApiRef.validate(async e=>{if(!e)return;this.isLoading=!0;let t={...this.formInline};if(t.project=this.pro.id,t.treenode&&t.treenode.length>0){const e=t.treenode[t.treenode.length-1];t.treenode=e}const a=await this.$api.getYApiImport(t);201===a.status&&((0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"success",message:"导入成功",duration:1e3}),this.closeModal()),this.isLoading=!1})},async getPostmanImport(){this.$refs.postmanRef.validate(async e=>{if(e){this.isLoading=!0;try{const e=new FormData;if(e.append("file",this.postmanForm.file),e.append("project",this.pro.id),this.postmanForm.treenode&&this.postmanForm.treenode.length>0){const t=this.postmanForm.treenode[this.postmanForm.treenode.length-1];e.append("treenode",t)}const t=await this.$api.getPostmanImport(e);201===t.status&&((0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"success",message:"Postman导入成功",duration:1e3}),this.closeModal())}catch(t){(0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"error",message:"Postman导入失败，请检查文件格式或网络",duration:2e3}),console.error("Postman导入错误:",t)}finally{this.isLoading=!1}}})},async getApipostImport(){this.$refs.apipostRef.validate(async e=>{if(e){this.isLoading=!0;try{const e=new FormData;if(e.append("file",this.apipostForm.file),e.append("project",this.pro.id),this.apipostForm.treenode&&this.apipostForm.treenode.length>0){const t=this.apipostForm.treenode[this.apipostForm.treenode.length-1];e.append("treenode",t)}const t=await this.$api.getApipostImport(e);201===t.status&&((0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"success",message:"Apipost导入成功",duration:1e3}),this.closeModal())}catch(t){(0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"error",message:"Apipost导入失败，请检查文件格式或网络",duration:2e3}),console.error("Apipost导入错误:",t)}finally{this.isLoading=!1}}})},async getSwaggerImport(){this.$refs.swaggerRef.validate(async e=>{if(e){this.isLoading=!0;try{let e={};if("file"===this.swaggerForm.importType){const t=new FormData;if(t.append("file",this.swaggerForm.file),t.append("project",this.pro.id),this.swaggerForm.treenode&&this.swaggerForm.treenode.length>0){const e=this.swaggerForm.treenode[this.swaggerForm.treenode.length-1];t.append("treenode",e)}e=t}else"url"===this.swaggerForm.importType&&(e={url:this.swaggerForm.url,project:this.pro.id,treenode:this.swaggerForm.treenode?this.swaggerForm.treenode[this.swaggerForm.treenode.length-1]:""});const t=await this.$api.getSwaggerImport(e);201===t.status&&((0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"success",message:"Swagger导入成功",duration:1e3}),this.closeModal())}catch(t){(0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"error",message:"Swagger导入失败，请检查文件格式或网络",duration:2e3}),console.error("Swagger导入错误:",t)}finally{this.isLoading=!1}}})},async getJsFetchImport(){this.$refs.jsFetchRef.validate(async e=>{if(e){this.isLoading=!0;try{const e=this.parseJsFetch(this.jsFetchForm.jsContent);let t={method:e.method,url:e.url,headers:e.headers,body:e.data||{},project:this.pro.id};if(this.jsFetchForm.treenode&&this.jsFetchForm.treenode.length>0){const e=this.jsFetchForm.treenode[this.jsFetchForm.treenode.length-1];t.treenode=e}const a=await this.$api.getJsFetchImport(t);201===a.status&&((0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"success",message:"JS fetch导入成功",duration:1e3}),this.closeModal())}catch(t){(0,element_plus__WEBPACK_IMPORTED_MODULE_5__.nk)({type:"error",message:"JS fetch导入失败，请检查代码格式或网络",duration:2e3}),console.error("JS fetch导入错误:",t)}finally{this.isLoading=!1}}})},async allTree(){const e=await this.$api.getTreeNode();200===e.status&&(this.treeOptions=e.data.result)}},created(){this.allTree()}}}}]);
//# sourceMappingURL=579.d2c3b6b9.js.map