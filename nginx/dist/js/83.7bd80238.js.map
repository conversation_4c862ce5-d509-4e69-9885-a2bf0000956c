{"version": 3, "file": "js/83.7bd80238.js", "mappings": "wMAEOA,MAAM,a,GAOEA,MAAM,e,GACJA,MAAM,c,SAQgCA,MAAM,e,GAE3CA,MAAM,4B,SAE+BA,MAAM,e,GAE3CA,MAAM,4B,SAEkCA,MAAM,e,GAE9CA,MAAM,4B,SAEkCA,MAAM,e,GAE9CA,MAAM,4B,SAEsBA,MAAM,c,GAElCA,MAAM,iB,GAWLA,MAAM,oB,GACJA,MAAM,gB,GAINA,MAAM,uB,GAaFA,MAAM,e,GAqBdA,MAAM,mB,GAQJA,MAAM,oB,GACJA,MAAM,gB,GAMVA,MAAM,mB,GAQJA,MAAM,oB,GACJA,MAAM,gB,GAMVA,MAAM,mB,GAYJA,MAAM,oB,GACJA,MAAM,gB,GAMZA,MAAM,sB,GAOFA,MAAM,Y,GAGHA,MAAM,W,GACPA,MAAM,e,GAEHA,MAAM,a,GAWNA,MAAM,Y,GAWXA,MAAM,oB,GACJA,MAAM,gB,GASVA,MAAM,mB,8oBAnLrBC,EAAAA,EAAAA,IA0LeC,GAAA,CA1LDC,OAAO,sBAAoB,C,iBACzC,IAwLM,EAxLNC,EAAAA,EAAAA,IAwLM,MAxLNC,EAwLM,EAtLJC,EAAAA,EAAAA,IAoCSC,EAAA,CApCAC,OAAQ,GAAIR,MAAM,S,kBACjB,IAAgC,G,aAAxCS,EAAAA,EAAAA,IAkCSC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAlCuBC,EAAAC,QAAO,CAAvBC,EAAMC,M,WAAtBd,EAAAA,EAAAA,IAkCSe,EAAA,CAlCiCC,IAAKF,EACtCG,GAAI,GAAKC,GAAI,GAAKC,GAAI,EAAIC,GAAI,EAAIC,GAAI,EACvCtB,MAAM,S,kBACZ,IA8BU,E,qBA9BVC,EAAAA,EAAAA,IA8BUsB,EAAA,CA9BDC,OAAO,QAAQxB,MAAM,yB,kBAC5B,IAQM,EARNI,EAAAA,EAAAA,IAQM,MARNqB,EAQM,EAPJrB,EAAAA,EAAAA,IAA2C,MAA3CsB,GAA2CC,EAAAA,EAAAA,IAAjBb,EAAKc,MAAI,IACnCtB,EAAAA,EAAAA,IAKUuB,EAAA,CAJN7B,MAAM,aACL,YAAW,EACX,UAASc,EAAKgB,MACdC,SAAU,M,sBAGS,iBAAfjB,EAAKkB,a,WAAhBvB,EAAAA,EAAAA,IAGM,MAHNwB,EAGM,C,aAFJ7B,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXA,EAAAA,EAAAA,IAA2F,OAA3F8B,EAA2F,EAApD5B,EAAAA,EAAAA,IAA0B6B,EAAA,M,iBAAjB,IAAO,EAAP7B,EAAAA,EAAAA,IAAO8B,K,wBAAYtB,EAAKuB,YAAU,S,eAE1D,iBAAfvB,EAAKkB,a,WAAhBvB,EAAAA,EAAAA,IAGM,MAHN6B,EAGM,C,aAFJlC,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXA,EAAAA,EAAAA,IAA8F,OAA9FmC,EAA8F,EAAvDjC,EAAAA,EAAAA,IAA6B6B,EAAA,M,iBAApB,IAAU,EAAV7B,EAAAA,EAAAA,IAAUkC,K,wBAAY1B,EAAKuB,YAAU,S,eAE7D,oBAAfvB,EAAKkB,a,WAAhBvB,EAAAA,EAAAA,IAGM,MAHNgC,EAGM,C,aAFJrC,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXA,EAAAA,EAAAA,IAA2F,OAA3FsC,EAA2F,EAApDpC,EAAAA,EAAAA,IAA0B6B,EAAA,M,iBAAjB,IAAO,EAAP7B,EAAAA,EAAAA,IAAO8B,K,wBAAYtB,EAAKuB,YAAU,S,eAE1D,oBAAfvB,EAAKkB,a,WAAhBvB,EAAAA,EAAAA,IAGM,MAHNkC,EAGM,C,aAFJvC,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXA,EAAAA,EAAAA,IAA8F,OAA9FwC,EAA8F,EAAvDtC,EAAAA,EAAAA,IAA6B6B,EAAA,M,iBAApB,IAAU,EAAV7B,EAAAA,EAAAA,IAAUkC,K,wBAAY1B,EAAKuB,YAAU,S,eAE7D,QAAfvB,EAAKkB,a,WAAhBvB,EAAAA,EAAAA,IAGM,MAHNoC,EAGM,EAFJzC,EAAAA,EAAAA,IAA4C,a,qBAAtC,UAAIA,EAAAA,EAAAA,IAA2B,UAAAuB,EAAAA,EAAAA,IAAtBb,EAAKgC,aAAW,MAC/B1C,EAAAA,EAAAA,IAA6D,OAA7D2C,EAA6D,C,qBAAjC,UAAI3C,EAAAA,EAAAA,IAAsB,UAAAuB,EAAAA,EAAAA,IAAjBb,EAAKkC,QAAM,S,kCA5BapC,EAAAqC,a,4BAmCrE3C,EAAAA,EAAAA,IA8ESC,EAAA,CA9EAC,OAAQ,GAAIR,MAAM,S,kBACvB,IA8CS,CA9CsDY,EAAAsC,SAAWtC,EAAAsC,QAAQC,OAAS,I,WAA3FlD,EAAAA,EAAAA,IA8CSe,EAAA,C,MA9CAE,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAKC,GAAI,EAAGrB,MAAM,S,kBAC/C,IA4CU,E,qBA5CVC,EAAAA,EAAAA,IA4CUsB,EAAA,CA5CDC,OAAO,QAAQxB,MAAM,c,CACjBoD,QAAMC,EAAAA,EAAAA,IACf,IAqCM,EArCNjD,EAAAA,EAAAA,IAqCM,MArCNkD,EAqCM,EApCJlD,EAAAA,EAAAA,IAGM,MAHNmD,EAGM,C,aAFJnD,EAAAA,EAAAA,IAA0B,cAAlB,aAAS,KACjBE,EAAAA,EAAAA,IAA+B6B,EAAA,M,iBAAtB,IAAY,EAAZ7B,EAAAA,EAAAA,IAAYkD,K,SAEvBpD,EAAAA,EAAAA,IA+BM,MA/BNqD,EA+BM,EA9BJnD,EAAAA,EAAAA,IA6BaoD,GAAA,CA5BXC,UAAU,SACVC,QAAQ,QACRC,MAAM,OACN,eAAa,uB,CAEFC,WAAST,EAAAA,EAAAA,IAClB,IAGY,EAHZ/C,EAAAA,EAAAA,IAGYyD,EAAA,CAHDC,KAAK,UAAUC,KAAK,QAAQjE,MAAM,c,kBAC3C,IAA0D,EAA1DM,EAAAA,EAAAA,IAA0D6B,EAAA,CAAjD+B,MAAA,wBAA0B,C,iBAAC,IAAY,EAAZ5D,EAAAA,EAAAA,IAAY6D,K,2BAAU,a,gCAI9D,IAgBM,EAhBN/D,EAAAA,EAAAA,IAgBM,MAhBNgE,EAgBM,EAfJ9D,EAAAA,EAAAA,IAWE+D,EAAA,C,WAVSzD,EAAA0D,S,qCAAA1D,EAAA0D,SAAQC,GACjBP,KAAK,gBACL,oBAAkB,OAClB,kBAAgB,OAChB,eAAa,sBACZ,eAAcpD,EAAA4D,mBACdC,UAAW7D,EAAA6D,UACZ,kBAAgB,IACfC,WAAW,EACZ1E,MAAM,e,mDAERM,EAAAA,EAAAA,IAEYyD,EAAA,CAFDC,KAAK,UAAWW,QAAOC,EAAAC,WAAY7E,MAAM,aAAciD,QAASrC,EAAAqC,S,kBACzE,IAA6B,EAA7B3C,EAAAA,EAAAA,IAA6B6B,EAAA,M,iBAApB,IAAU,EAAV7B,EAAAA,EAAAA,IAAUwE,M,2BAAU,U,sEAOzC,IAEM,EAFN1E,EAAAA,EAAAA,IAEM,MAFN2E,EAEM,EADJzE,EAAAA,EAAAA,IAAyC0E,GAAA,CAA9BC,SAAUrE,EAAAsC,SAAO,yB,YA1CsBtC,EAAAqC,a,uBA8COrC,EAAAsE,aAAetE,EAAAsE,YAAY/B,OAAS,I,WAAnGlD,EAAAA,EAAAA,IAcSe,EAAA,C,MAdAE,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAKC,GAAI,EAAGrB,MAAM,S,kBAC/C,IAYU,E,qBAZVC,EAAAA,EAAAA,IAYUsB,EAAA,CAZDC,OAAO,QAAQxB,MAAM,c,CACjBoD,QAAMC,EAAAA,EAAAA,IACf,IAKM,EALNjD,EAAAA,EAAAA,IAKM,MALN+E,EAKM,EAJJ/E,EAAAA,EAAAA,IAGM,MAHNgF,EAGM,C,eAFJhF,EAAAA,EAAAA,IAA0B,cAAlB,aAAS,KACjBE,EAAAA,EAAAA,IAAmC6B,EAAA,M,iBAA1B,IAAgB,EAAhB7B,EAAAA,EAAAA,IAAgB+E,M,6BAI/B,IAEM,EAFNjF,EAAAA,EAAAA,IAEM,MAFNkF,EAEM,EADJhF,EAAAA,EAAAA,IAAyDiF,GAAA,CAAxCN,SAAUrE,EAAAsE,aAAW,yB,YAVYtE,EAAAqC,a,uBAcOrC,EAAA4E,QAAU5E,EAAA4E,OAAOrC,OAAS,I,WAAzFlD,EAAAA,EAAAA,IAcSe,EAAA,C,MAdAE,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAKC,GAAI,EAAGrB,MAAM,S,kBAC/C,IAYU,E,qBAZVC,EAAAA,EAAAA,IAYUsB,EAAA,CAZDC,OAAO,QAAQxB,MAAM,c,CACjBoD,QAAMC,EAAAA,EAAAA,IACf,IAKM,EALNjD,EAAAA,EAAAA,IAKM,MALNqF,EAKM,EAJJrF,EAAAA,EAAAA,IAGM,MAHNsF,EAGM,C,eAFJtF,EAAAA,EAAAA,IAAwB,cAAhB,WAAO,KACfE,EAAAA,EAAAA,IAA+B6B,EAAA,M,iBAAtB,IAAY,EAAZ7B,EAAAA,EAAAA,IAAYqF,M,6BAI3B,IAEM,EAFNvF,EAAAA,EAAAA,IAEM,MAFNwF,EAEM,EADJtF,EAAAA,EAAAA,IAAwCuF,GAAA,CAA7BZ,SAAUrE,EAAA4E,QAAM,yB,YAVuB5E,EAAAqC,a,gCAiB5D3C,EAAAA,EAAAA,IA6DSC,EAAA,CA7DAC,OAAQ,IAAE,C,iBACf,IAyCS,CAzCsDI,EAAAkF,SAAWlF,EAAAkF,QAAQ3C,OAAS,I,WAA3FlD,EAAAA,EAAAA,IAyCSe,EAAA,C,MAzCAE,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAKC,GAAI,EAAGrB,MAAM,S,kBAC/C,IAuCU,E,qBAvCVC,EAAAA,EAAAA,IAuCUsB,EAAA,CAvCDC,OAAO,QAAQxB,MAAM,Y,CACjBoD,QAAMC,EAAAA,EAAAA,IACf,IAKM,EALNjD,EAAAA,EAAAA,IAKM,MALN2F,EAKM,EAJJ3F,EAAAA,EAAAA,IAGM,MAHN4F,EAGM,C,eAFJ5F,EAAAA,EAAAA,IAAuB,cAAf,UAAM,KACdE,EAAAA,EAAAA,IAA8B6B,EAAA,M,iBAArB,IAAW,EAAX7B,EAAAA,EAAAA,IAAW2F,M,6BAI5B,IA6BM,EA7BN7F,EAAAA,EAAAA,IA6BM,MA7BN8F,EA6BM,EA5BJ5F,EAAAA,EAAAA,IA2Bc6F,GAAA,M,iBA1BM,IAAoC,G,aAAtD1F,EAAAA,EAAAA,IAyBmBC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAzB2BC,EAAAkF,QAAO,CAA3BM,EAAUrF,M,WAApCd,EAAAA,EAAAA,IAyBmBoG,GAAA,CAxBApF,IAAKF,EACLuF,UAAWC,EAAAC,OAAOC,MAAML,EAASM,aAClC/C,UAAU,MACVgD,MAAM,W,kBACxB,IAmBM,EAnBNvG,EAAAA,EAAAA,IAmBM,MAnBNwG,EAmBM,CAlByB,QAAfR,EAASS,S,WAAvB5G,EAAAA,EAAAA,IAA+F6G,GAAA,C,MAAxD9C,KAAK,UAAUC,KAAK,S,kBAAQ,IAAmB,E,iBAAjBmC,EAASS,QAAM,K,yBACpF5G,EAAAA,EAAAA,IAAwD6G,GAAA,C,MAAzC7C,KAAK,S,kBAAQ,IAAmB,E,iBAAjBmC,EAASS,QAAM,K,aAC7CzG,EAAAA,EAAAA,IAA6C,OAA7C2G,GAA6CpF,EAAAA,EAAAA,IAArByE,EAASY,KAAG,IACpC5G,EAAAA,EAAAA,IAcM,MAdN6G,EAcM,C,eAbJ7G,EAAAA,EAAAA,IAAoC,QAA9BJ,MAAM,aAAY,SAAK,KAC7BI,EAAAA,EAAAA,IAA8C,OAA9C8G,GAA8CvF,EAAAA,EAAAA,IAApByE,EAASe,IAAE,G,eACrC/G,EAAAA,EAAAA,IAAuC,QAAjCJ,MAAM,aAAY,YAAQ,KAChCI,EAAAA,EAAAA,IAQO,QAPLJ,OAAKoH,EAAAA,EAAAA,IAAA,CAAC,aAAY,C,iBACqD,QAApBhB,EAASiB,Y,iBAAkF,QAApBjB,EAASiB,Y,eAAgF,QAApBjB,EAASiB,iB,QAKtMjB,EAASiB,aAAW,IAExBjH,EAAAA,EAAAA,IAAyD,OAAzDkH,GAAyD3F,EAAAA,EAAAA,IAAhCyE,EAASmB,gBAAc,S,yDAjCJ3G,EAAAqC,a,uBAyCUrC,EAAA4G,WAAa5G,EAAA4G,UAAUrE,OAAS,I,WAAhGlD,EAAAA,EAAAA,IAiBSe,EAAA,C,MAjBAE,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAIrB,MAAM,S,kBAChD,IAeU,E,qBAfVC,EAAAA,EAAAA,IAeUsB,EAAA,CAfDC,OAAO,QAAQxB,MAAM,c,CACjBoD,QAAMC,EAAAA,EAAAA,IACf,IAQM,EARNjD,EAAAA,EAAAA,IAQM,MARNqH,EAQM,EAPJrH,EAAAA,EAAAA,IAGM,MAHNsH,EAGM,C,eAFJtH,EAAAA,EAAAA,IAA4B,cAApB,eAAW,KACnBE,EAAAA,EAAAA,IAAmC6B,EAAA,M,iBAA1B,IAAgB,EAAhB7B,EAAAA,EAAAA,IAAgB+E,M,uBAE3BjF,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,eAAa,EACtBI,EAAAA,EAAAA,IAAmB,YAAb,Y,yBAIZ,IAEM,EAFNA,EAAAA,EAAAA,IAEM,MAFNuH,EAEM,EADJrH,EAAAA,EAAAA,IAAiDsH,GAAA,CAAnC3C,SAAUrE,EAAA4G,WAAS,yB,YAbiB5G,EAAAqC,a,uFCvKzD4E,IAAI,UAAU3D,MAAA,kB,0CAAnBzD,EAAAA,EAAAA,IAAgD,MAAhDJ,EAAgD,S,kCAMlD,GACEyH,MAAO,CACL7C,SAAU,CACRjB,KAAM+D,MACNC,UAAU,IAGdC,IAAAA,GACE,MAAO,CACLC,OAAQ,CACNvB,MAAO,CAAC,UAAW,UAAW,WAC9BwB,OAAQ,CAAEC,EAAG,SAAUC,EAAG,UAC1BC,QAAS,CAAC,EACVC,QAAS,CACPC,WAAY,CAAC,UAAW,OAAQ,OAAQ,QACxCC,OAAQ,IAEVC,MAAO,CAAE1E,KAAM,YACf2E,MAAO,CAAEC,IAAK,MACdC,OAAQ,CAAC,CAAE7E,KAAM,OAAS,CAAEA,KAAM,OAAS,CAAEA,KAAM,SAGzD,EACA8E,OAAAA,GACEC,KAAKC,aACP,EACAC,OAAAA,GACEF,KAAKC,aACP,EACAE,QAAS,CACPF,WAAAA,GAEED,KAAKb,OAAOK,QAAQE,OAASM,KAAK9D,SAASkE,IAAIrI,GAAQ,CACrDA,EAAKsI,KACLtI,EAAKuI,sBACLvI,EAAKwI,oBACLxI,EAAKyI,qBAGP,MAAMC,EAAWT,KAAKU,MAAMC,QACtBC,EAAUD,EAAAA,GAAaF,GAC7BG,EAAQC,UAAUb,KAAKb,OACzB,I,WC1CJ,MAAM2B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,Q,SCROhC,IAAI,QAAQ3D,MAAA,kB,0CAAjBzD,EAAAA,EAAAA,IAA8C,MAA9CJ,EAA8C,S,CAMhD,OACEyH,MAAO,CACL7C,SAAU,CACRjB,KAAM+D,MACNC,UAAU,IAGdC,IAAAA,GACE,MAAO,CACL6B,MAAO,KACP5B,OAAQ,CACNQ,MAAO,CACL1E,KAAM,WACN+F,aAAa,EACbC,SAAU,CACRC,UAAW,CACTtD,MAAO,oBACP9C,MAAO,IAGXqG,SAAU,CACRC,MAAM,GAERC,UAAW,CACTzD,MAAO,QACP0D,OAAQ,IAEVC,UAAW,CACTH,MAAM,GAERlC,KAAM,IAERU,MAAO,CACL3E,KAAM,QACN+F,YAAa,CAAC,EAAG,OACjBC,SAAU,CACRC,UAAW,CACTtD,MAAO,oBACP9C,MAAO,IAGXuG,UAAW,CACTzD,MAAO,SAET2D,UAAW,CACTH,MAAM,IAGVI,UAAW,CACTvG,KAAM,YACNmG,MAAM,EACNK,UAAW,EACXC,YAAa,EACbC,OAAQ,CACN,CACEC,GAAI,EACJC,GAAI,EACJjE,MAAO,aAIbkC,OAAQ,CACN,CACE7E,KAAM,OACN6G,OAAQ,GACRC,OAAQ,OACRb,UAAW,CACTtD,MAAO,UACP9C,MAAO,GAETkH,UAAW,CACTpE,MAAO,IAAI+C,EAAAA,GAAAA,GACT,EACA,EACA,EACA,EACA,CAAC,CACCsB,OAAQ,EACRrE,MAAO,WAET,CACEqE,OAAQ,GACRrE,MAAO,WAET,CACEqE,OAAQ,EACRrE,MAAO,aAGT,IAGJsB,KAAM,MAKhB,EACAa,OAAAA,GACEC,KAAKkC,WACP,EACAhC,OAAAA,GACEF,KAAKkC,WACP,EACA/B,QAAS,CACP+B,SAAAA,GACElC,KAAKe,MAAQJ,EAAAA,GAAaX,KAAKU,MAAMK,OAErCf,KAAKb,OAAOQ,MAAMT,KAAOc,KAAK9D,SAASkE,IAAIrI,GAAQA,EAAKoK,MAExDnC,KAAKb,OAAOW,OAAO,GAAGZ,KAAOc,KAAK9D,SAASkE,IAAIrI,GAAQA,EAAKqK,QAC5DpC,KAAKe,MAAMF,UAAUb,KAAKb,OAC5B,IClHJ,MAAM,IAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,KAEpE,U,UCNOL,IAAI,QAAQ3D,MAAA,kB,2CAAjBzD,EAAAA,EAAAA,IAA8C,MAA9CJ,GAA8C,S,CAMhD,QACEyH,MAAO,CACL7C,SAAU,CACRjB,KAAM+D,MACNC,UAAU,IAGdC,IAAAA,GACE,MAAO,CACP,CACF,EACAa,OAAAA,GACEC,KAAKkC,WACP,EACAhC,OAAAA,GACEF,KAAKkC,WACP,EACA/B,QAAS,CACP+B,SAAAA,GACE,MAAMnB,EAAQJ,EAAAA,GAAaX,KAAKU,MAAMK,OAChC5B,EAAS,CACbI,QAAS,CACP1E,QAAS,OACTwH,UAAW,cAEbjD,OAAQ,CACNkD,OAAQ,WACRC,KAAM,MACNC,IAAK,OAEP1C,OAAQ,CACN,CACEjH,KAAM,cACNoC,KAAM,MACNwH,OAAQ,CAAC,MAAO,OAChBC,OAAQ,CAAC,MAAO,OAChBC,mBAAmB,EACnBC,SAAU,EACVC,UAAW,CACTC,aAAc,IAEhBC,MAAO,CACL3B,MAAM,EACN4B,SAAU,UAEZC,SAAU,CACRF,MAAO,CACL3B,MAAM,EACN8B,SAAU,GACVC,WAAY,SAGhBC,UAAW,CACThC,MAAM,GAERlC,KAAMc,KAAK9D,YAIjB6E,EAAMF,UAAU1B,EAClB,IC9DJ,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAAS,MAEpE,U,UCNOL,IAAI,QAAQ3D,MAAA,kB,2CAAjBzD,EAAAA,EAAAA,IAA8C,MAA9CJ,GAA8C,S,CAMhD,QACEyH,MAAO,CACL7C,SAAU,CACRjB,KAAM+D,MACNC,UAAU,IAGdc,OAAAA,GACEC,KAAKkC,WACP,EACAhC,OAAAA,GACEF,KAAKkC,WACP,EACA/B,QAAS,CACP+B,SAAAA,GACE,MAAMnB,EAAQJ,EAAAA,GAAaX,KAAKU,MAAMK,OAChC5B,EAAS,CACbkE,KAAM,CACJb,IAAK,GACLc,OAAQ,GACRf,KAAM,GACNgB,MAAO,GACPC,cAAc,GAEhBjE,QAAS,CACP1E,QAAS,OACTwH,UAAWoB,IACT,MAAMC,EAAYD,EAAO,GAAGC,UACtBX,EAAQU,EAAO,GAAGE,eAClBC,EAAQH,EAAO,GAAGG,MACxB,MAAO,GAAG5D,KAAK9D,SAASwH,GAAWG,sBAAsBd,eAAmBa,MAE9EE,YAAa,CACX7I,KAAM,OACNiG,UAAW,CACTtD,MAAO,aAIb+B,MAAO,CACL1E,KAAM,WACN+F,aAAa,EACbK,UAAW,CACTD,MAAM,GAERH,SAAU,CACRC,UAAW,CACTtD,MAAO,oBACP9C,MAAO,IAGXqG,SAAU,CACRC,MAAM,GAERlC,KAAMc,KAAK9D,SAASkE,IAAIrI,GAAQA,EAAK4F,cAEvCiC,MAAO,CACL3E,KAAM,QACN8I,cAAe,CACbnG,MAAO,OACPsF,SAAU,GACVc,WAAY,IAEdzC,UAAW,CACTL,UAAW,CACTtD,MAAO,YAGXqD,SAAU,CACRC,UAAW,CACT,GAGJC,SAAU,CACRC,MAAM,IAGVtB,OAAQ,CAAC,CACPjH,KAAM,MACNoC,KAAM,OACN6G,QAAQ,EACRmC,YAAY,EACZC,WAAY,EACZC,OAAQ,EACRtB,UAAW,CACTjF,MAAO,UACPwG,YAAa,WAEflD,UAAW,CACTpG,MAAO,EACP8C,MAAO,WAEToE,UAAW,CACTpE,MAAO,IAAI+C,EAAAA,GAAAA,GACT,EACA,EACA,EACA,EACA,CAAC,CACCsB,OAAQ,EACRrE,MAAO,WAET,CACEqE,OAAQ,GACRrE,MAAO,WAET,CACEqE,OAAQ,EACRrE,MAAO,aAGT,IAGJsB,KAAMc,KAAK9D,SAASkE,IAAIrI,GAAQsM,WAAWtM,EAAKuM,eAGpDvD,EAAMF,UAAU1B,EAClB,ICtHJ,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,U,oCRwMA,IACEoF,WAAY,CACVC,OAAM,KAAEC,MAAK,KAAEC,MAAK,KACpBC,SAAQ,EAAEC,QAAO,KAAEC,eAAc,GACjCC,SAAQ,GAAEC,YAAW,GACrBC,IAAG,OAAEC,OAAM,UAAEC,SAAQ,YAAEC,SAAQ,YAC/BC,OAAM,UAAEC,aAAY,gBAAEC,SAAQ,YAAEC,QAAOA,GAAAA,SAEzCrG,IAAAA,GAEE,MAAMsG,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAClBC,EAAMC,QAAQD,EAAME,UAAY,QAGhC,MAAMC,EAAc1D,IAClB,MAAM2D,EAAO3D,EAAK4D,cACZC,EAAQC,OAAO9D,EAAK+D,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAO9D,EAAKkE,WAAWF,SAAS,EAAG,KAC/C,MAAO,GAAGL,KAAQE,KAASI,KAA2B,IAApBjE,EAAKmE,WAAmB,KAAOnE,EAAKmE,cAAoC,IAAtBnE,EAAKoE,aAAqB,KAAOpE,EAAKoE,gBAAsC,IAAtBpE,EAAKqE,aAAqB,KAAOrE,EAAKqE,gBAGlL,MAAO,CACLC,OAAQ,KACR3O,QAAS,KACT2E,OAAO,KACPtC,QAAQ,KACRsE,UAAU,KACVtC,YAAY,KACZY,QAAQ,KACRtB,mBAAoB,CAAC,WAAY,YAEjCF,SAAU,CAACsK,EAAWH,GAAQG,EAAWL,IACzC9J,UAAW,CACT,CACEgL,KAAM,OACN9C,MAAOA,KACL,MAAM4B,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,QACzB,CAACF,EAAOF,KAGnB,CACEkB,KAAM,QACN9C,MAAOA,KACL,MAAM4B,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,QACzB,CAACF,EAAOF,KAGnB,CACEkB,KAAM,QACN9C,MAAOA,KACL,MAAM4B,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,QACzB,CAACF,EAAOF,MAIrBmB,WAAY,CACVxO,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,IAAI,GAEN2B,SAAS,EAEb,EACAiG,QAAS,KACNyG,EAAAA,GAAAA,IAAa,CAAC,cACf,gBAAMC,CAAWC,EAAWC,GAC1B/G,KAAK9F,SAAU,EACf,IACE,IAAIgF,EAAO,CAAC,QAAWc,KAAKgH,IAAIC,IAC7BH,GAAaC,IACd7H,EAAO,CAAC,QAAWc,KAAKgH,IAAIC,GAAG,UAAaH,EAAW,QAAWC,IAEpE,MAAMG,QAAiBlH,KAAKmH,KAAKC,gBAAgBlI,GACzB,MAApBgI,EAASG,SACXrH,KAAKyG,OAASS,EAAShI,KACvBc,KAAKlI,QAAUkI,KAAKyG,OAAOa,aAC3BtH,KAAKvD,OAASuD,KAAKyG,OAAOc,YAC1BvH,KAAK7F,QAAU6F,KAAKyG,OAAOe,aAC3BxH,KAAKvB,UAAYuB,KAAKyG,OAAOgB,eAC7BzH,KAAK7D,YAAc6D,KAAKyG,OAAOiB,mBAC/B1H,KAAKjD,QAAUiD,KAAKyG,OAAOkB,SAE/B,CAAE,MAAOC,GACPC,QAAQD,MAAM,cAAeA,EAC/B,CAAE,QACA5H,KAAK9F,SAAU,CACjB,CACF,EACA4B,UAAAA,GACOkE,KAAKzE,WACRyE,KAAKzE,SAAW,IAElB,MAAMuL,EAAY9G,KAAKzE,SAAS,GAC1BwL,EAAU/G,KAAKzE,SAAS,GAC9ByE,KAAK6G,WAAWC,EAAWC,EAC7B,EACAe,YAAAA,GACE,MAAMhN,EAAQiN,OAAOC,WACrBhI,KAAK2G,WAAa,CAChBxO,GAAI2C,EAAQ,IACZ1C,GAAI0C,GAAS,KAAOA,EAAQ,IAC5BzC,GAAIyC,GAAS,KAAOA,EAAQ,IAC5BxC,GAAIwC,GAAS,KAAOA,EAAQ,KAC5BvC,GAAIuC,GAAS,KAEjB,GAEHmN,SAAU,KACNC,EAAAA,GAAAA,IAAS,CAAC,SAEdC,OAAAA,GAEG,MAAMrB,EAAY9G,KAAKzE,SAAS,GAC1BwL,EAAU/G,KAAKzE,SAAS,GAChCyE,KAAK6G,WAAWC,EAAWC,GACzB/G,KAAK8H,eACLC,OAAOK,iBAAiB,SAAUpI,KAAK8H,aAC1C,EACCO,aAAAA,GACEN,OAAOO,oBAAoB,SAAUtI,KAAK8H,aAC5C,GS3UF,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAASS,GAAQ,CAAC,YAAY,qBAEzF,S,4HCRI7Q,EAAAA,EAAAA,IAEO,aAAAkB,EAAAA,EAAAA,IADHf,EAAA2Q,cAAY,E,CCFpB,IAAIC,EAAW,EACf,MAAMC,EAAW,kBAAkBC,MAAM,KAEzC,IAAIC,EACAC,EAEJ,MAAMC,EAA6B,qBAAXf,OACxB,GAAIe,EACFF,EAAwB,WAExB,EACAC,EAAuB,WAEvB,MACK,CAGL,IAAIE,EAFJH,EAAwBb,OAAOa,sBAC/BC,EAAuBd,OAAOc,qBAG9B,IAAK,IAAIG,EAAI,EAAGA,EAAIN,EAAStO,OAAQ4O,IAAK,CACxC,GAAIJ,GAAyBC,EAAwB,MACrDE,EAASL,EAASM,GAClBJ,EAAwBA,GAAyBb,OAAOgB,EAAS,yBACjEF,EAAuBA,GAAwBd,OAAOgB,EAAS,yBAA2BhB,OAAOgB,EAAS,8BAC5G,CAGKH,GAA0BC,IAC7BD,EAAwB,SAASK,GAC/B,MAAMC,GAAW,IAAIzD,MAAOG,UAEtBuD,EAAaC,KAAKvJ,IAAI,EAAG,IAAMqJ,EAAWT,IAC1CxB,EAAKc,OAAOsB,WAAW,KAC3BJ,EAASC,EAAWC,IACnBA,GAEH,OADAV,EAAWS,EAAWC,EACflC,CACT,EAEA4B,EAAuB,SAAS5B,GAC9Bc,OAAOuB,aAAarC,EACtB,EAEJ,CDpCA,OACElI,MAAO,CACLwK,SAAU,CACRtO,KAAMuO,OACNvK,UAAU,EACVwK,QAAS,GAEXC,OAAQ,CACNzO,KAAMuO,OACNvK,UAAU,EACVwK,QAAS,MAEXzQ,SAAU,CACRiC,KAAMuO,OACNvK,UAAU,EACVwK,QAAS,KAEXE,SAAU,CACR1O,KAAM2O,QACN3K,UAAU,EACVwK,SAAS,GAEXI,SAAU,CACR5O,KAAMuO,OACNvK,UAAU,EACVwK,QAAS,EACTK,SAAAA,CAAUlG,GACR,OAAOA,GAAS,CAClB,GAEFmG,QAAS,CACP9O,KAAMgL,OACNhH,UAAU,EACVwK,QAAS,KAEXO,UAAW,CACT/O,KAAMgL,OACNhH,UAAU,EACVwK,QAAS,KAEXV,OAAQ,CACN9N,KAAMgL,OACNhH,UAAU,EACVwK,QAAS,IAEXQ,OAAQ,CACNhP,KAAMgL,OACNhH,UAAU,EACVwK,QAAS,IAEXS,UAAW,CACTjP,KAAM2O,QACN3K,UAAU,EACVwK,SAAS,GAEXU,SAAU,CACRlP,KAAMmP,SACNX,QAAQY,EAAGC,EAAGC,EAAGC,GACf,OAAOD,GAAiC,EAA3BnB,KAAKqB,IAAI,GAAI,GAAKJ,EAAIG,IAAU,KAAO,KAAOF,CAC7D,IAGJpL,IAAAA,GACE,MAAO,CACLwL,cAAe1K,KAAKuJ,SACpBf,aAAcxI,KAAK2K,aAAa3K,KAAKuJ,UACrCqB,SAAU,KACV3Q,QAAQ,EACR4Q,cAAe7K,KAAKhH,SACpB8R,UAAW,KACXvN,UAAW,KACXwN,UAAW,KACXC,IAAK,KAET,EACA/C,SAAU,CACRgD,SAAAA,GACE,OAAOjL,KAAKuJ,SAAWvJ,KAAK0J,MAC9B,GAEFwB,MAAO,CACL3B,QAAAA,GACMvJ,KAAK2J,UACP3J,KAAK0F,OAET,EACAgE,MAAAA,GACM1J,KAAK2J,UACP3J,KAAK0F,OAET,GAEF3F,OAAAA,GACMC,KAAK2J,UACP3J,KAAK0F,QAEP1F,KAAKmL,MAAM,kBACb,EACAhL,QAAS,CACPuF,KAAAA,GACE1F,KAAK0K,cAAgB1K,KAAKuJ,SAC1BvJ,KAAK8K,UAAY,KACjB9K,KAAK6K,cAAgB7K,KAAKhH,SAC1BgH,KAAK/F,QAAS,EACd+F,KAAKgL,IAAMpC,EAAsB5I,KAAKjH,MACxC,EACAqS,WAAAA,GACMpL,KAAK/F,QACP+F,KAAKqL,SACLrL,KAAK/F,QAAS,IAEd+F,KAAKsL,QACLtL,KAAK/F,QAAS,EAElB,EACAqR,KAAAA,GACEzC,EAAqB7I,KAAKgL,IAC5B,EACAK,MAAAA,GACErL,KAAK8K,UAAY,KACjB9K,KAAK6K,eAAiB7K,KAAK+K,UAC3B/K,KAAK0K,eAAiB1K,KAAK4K,SAC3BhC,EAAsB5I,KAAKjH,MAC7B,EACAwS,KAAAA,GACEvL,KAAK8K,UAAY,KACjBjC,EAAqB7I,KAAKgL,KAC1BhL,KAAKwI,aAAexI,KAAK2K,aAAa3K,KAAKuJ,SAC7C,EACAxQ,KAAAA,CAAMwE,GACCyC,KAAK8K,YAAW9K,KAAK8K,UAAYvN,GACtCyC,KAAKzC,UAAYA,EACjB,MAAMiO,EAAWjO,EAAYyC,KAAK8K,UAClC9K,KAAK+K,UAAY/K,KAAK6K,cAAgBW,EAElCxL,KAAKkK,UACHlK,KAAKiL,UACPjL,KAAK4K,SAAW5K,KAAK0K,cAAgB1K,KAAKmK,SAASqB,EAAU,EAAGxL,KAAK0K,cAAgB1K,KAAK0J,OAAQ1J,KAAK6K,eAEvG7K,KAAK4K,SAAW5K,KAAKmK,SAASqB,EAAUxL,KAAK0K,cAAe1K,KAAK0J,OAAS1J,KAAK0K,cAAe1K,KAAK6K,eAGjG7K,KAAKiL,UACPjL,KAAK4K,SAAW5K,KAAK0K,eAAkB1K,KAAK0K,cAAgB1K,KAAK0J,SAAW8B,EAAWxL,KAAK6K,eAE5F7K,KAAK4K,SAAW5K,KAAK0K,eAAiB1K,KAAK0J,OAAS1J,KAAK0K,gBAAkBc,EAAWxL,KAAK6K,eAG3F7K,KAAKiL,UACPjL,KAAK4K,SAAW5K,KAAK4K,SAAW5K,KAAK0J,OAAS1J,KAAK0J,OAAS1J,KAAK4K,SAEjE5K,KAAK4K,SAAW5K,KAAK4K,SAAW5K,KAAK0J,OAAS1J,KAAK0J,OAAS1J,KAAK4K,SAGnE5K,KAAKwI,aAAexI,KAAK2K,aAAa3K,KAAK4K,UACvCY,EAAWxL,KAAK6K,cAClB7K,KAAKgL,IAAMpC,EAAsB5I,KAAKjH,OAEtCiH,KAAKmL,MAAM,WAEf,EACAM,QAAAA,CAASC,GACP,OAAQC,MAAMtH,WAAWqH,GAC3B,EACAf,YAAAA,CAAaiB,GACXA,EAAMA,EAAIC,QAAQ7L,KAAK6J,UACvB+B,GAAO,GACP,MAAMvM,EAAIuM,EAAIjD,MAAM,KACpB,IAAImD,EAAKzM,EAAE,GACX,MAAM0M,EAAK1M,EAAEjF,OAAS,EAAI4F,KAAK+J,QAAU1K,EAAE,GAAK,GAC1C2M,EAAM,eACZ,GAAIhM,KAAKgK,YAAchK,KAAKyL,SAASzL,KAAKgK,WACxC,MAAOgC,EAAIC,KAAKH,GACdA,EAAKA,EAAGI,QAAQF,EAAK,KAAOhM,KAAKgK,UAAY,MAGjD,OAAOhK,KAAK+I,OAAS+C,EAAKC,EAAK/L,KAAKiK,MACtC,GAEFkC,SAAAA,GACEtD,EAAqB7I,KAAKgL,IAC5B,G,WEvLF,MAAMlK,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASyH,KAEpE,QCNA,IACsB,qBAAXR,QAA0BA,OAAOqE,KAC1CrE,OAAOqE,IAAIC,UAAU,WAAYC,E", "sources": ["webpack://frontend-web/./src/views/Workbench/Project.vue", "webpack://frontend-web/./src/components/echart/ApiChart.vue", "webpack://frontend-web/./src/components/echart/ApiChart.vue?420b", "webpack://frontend-web/./src/components/echart/WeekLoginChart.vue", "webpack://frontend-web/./src/components/echart/WeekLoginChart.vue?0a53", "webpack://frontend-web/./src/components/echart/BugChart.vue", "webpack://frontend-web/./src/components/echart/BugChart.vue?33a6", "webpack://frontend-web/./src/components/echart/ReportChart.vue", "webpack://frontend-web/./src/components/echart/ReportChart.vue?f685", "webpack://frontend-web/./src/views/Workbench/Project.vue?15a0", "webpack://frontend-web/./src/components/to/vue-countTo.vue", "webpack://frontend-web/./src/components/to/requestAnimationFrame.js", "webpack://frontend-web/./src/components/to/vue-countTo.vue?405c", "webpack://frontend-web/./src/components/to/index.js"], "sourcesContent": ["<template>\r\n  <el-scrollbar height=\"calc(100vh - 50px)\">\r\n  <div class=\"dashboard\">\r\n    <!-- Stats Cards Row -->\r\n    <el-row :gutter=\"12\" class=\"mb-30\">\r\n      <el-col v-for=\"(item, index) in proInfo\" :key=\"index\" \r\n              :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" :xl=\"3\" \r\n              class=\"mb-18\">\r\n        <el-card shadow=\"hover\" class=\"stat-card hover-scale\" v-loading=\"loading\">\r\n          <div class=\"card-header\">\r\n            <div class=\"stat-title\">{{item.name}}</div>\r\n            <countTo\r\n                class=\"stat-value\"\r\n                :start-val=\"0\"\r\n                :end-val=\"item.count\"\r\n                :duration=\"2600\">\r\n            </countTo>\r\n          </div>\r\n          <div v-if=\"item.changeType==='lastIncrease'\" class=\"stat-change\">\r\n            <span>自上周增长</span>\r\n            <span class=\"percentage increase-icon\"><el-icon><Top /></el-icon>{{item.percentage}}</span>\r\n          </div>\r\n          <div v-if=\"item.changeType==='lastDecrease'\" class=\"stat-change\">\r\n            <span>自上周下降</span>\r\n            <span class=\"percentage decrease-icon\"><el-icon><Bottom /></el-icon>{{item.percentage}}</span>\r\n          </div>\r\n          <div v-if=\"item.changeType==='yastdayIncrease'\" class=\"stat-change\">\r\n            <span>自昨日增长</span>\r\n            <span class=\"percentage increase-icon\"><el-icon><Top /></el-icon>{{item.percentage}}</span>\r\n          </div>\r\n          <div v-if=\"item.changeType==='yastdayDecrease'\" class=\"stat-change\">\r\n            <span>自昨日下降</span>\r\n            <span class=\"percentage decrease-icon\"><el-icon><Bottom /></el-icon>{{item.percentage}}</span>\r\n          </div>\r\n          <div v-if=\"item.changeType==='job'\" class=\"job-status\">\r\n            <span>运行中：<b>{{item.run_service}}</b></span>\r\n            <span class=\"paused-status\">已暂停：<b>{{item.paused}}</b></span>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- Charts Row -->\r\n    <el-row :gutter=\"18\" class=\"mb-20\">\r\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"9\" class=\"mb-18\" v-if=\"proCase && proCase.length > 0\">\r\n          <el-card shadow=\"hover\" class=\"chart-card\" v-loading=\"loading\">\r\n            <template #header>\r\n              <div class=\"card-header-flex\">\r\n                <div class=\"header-title\">\r\n                  <strong>近七日接口维护统计</strong>\r\n                  <el-icon><Suitcase /></el-icon>\r\n                </div>\r\n                <div class=\"date-filter-wrapper\">\r\n                  <el-popover\r\n                    placement=\"bottom\"\r\n                    trigger=\"click\"\r\n                    width=\"auto\"\r\n                    popper-class=\"date-filter-popover\"\r\n                  >\r\n                    <template #reference>\r\n                      <el-button type=\"primary\" size=\"small\" class=\"filter-btn\">\r\n                        <el-icon style=\"margin-right: 5px;\"><Calendar /></el-icon>\r\n                         时间筛选\r\n                      </el-button>\r\n                    </template>\r\n                    <div class=\"date-filter\">\r\n                      <el-date-picker\r\n                        v-model=\"dataTime\"\r\n                        type=\"datetimerange\"\r\n                        start-placeholder=\"开始时间\"\r\n                        end-placeholder=\"结束时间\"\r\n                        value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n                        :default-time=\"defaultTimeOptions\"\r\n                        :shortcuts=\"shortcuts\"\r\n                        range-separator=\"至\"\r\n                        :clearable=\"true\"\r\n                        class=\"date-picker\"\r\n                      />\r\n                      <el-button type=\"primary\" @click=\"submitForm\" class=\"search-btn\" :loading=\"loading\">\r\n                        <el-icon><Search /></el-icon>查询\r\n                      </el-button>\r\n                    </div>\r\n                  </el-popover>\r\n                </div>\r\n              </div>\r\n            </template>\r\n            <div class=\"chart-container\">\r\n              <ApiChart :testData=\"proCase\"></ApiChart>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"8\" class=\"mb-18\" v-if=\"buttonClick && buttonClick.length > 0\">\r\n          <el-card shadow=\"hover\" class=\"chart-card\" v-loading=\"loading\">\r\n            <template #header>\r\n              <div class=\"card-header-flex\">\r\n                <div class=\"header-title\">\r\n                  <strong>近七日平台使用频率</strong>\r\n                  <el-icon><DataAnalysis /></el-icon>\r\n                </div>\r\n              </div>\r\n            </template>\r\n            <div class=\"chart-container\">\r\n              <WeekLoginChart :testData=\"buttonClick\"></WeekLoginChart>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"7\" class=\"mb-18\" v-if=\"proBug && proBug.length > 0\">\r\n          <el-card shadow=\"hover\" class=\"chart-card\" v-loading=\"loading\">\r\n            <template #header>\r\n              <div class=\"card-header-flex\">\r\n                <div class=\"header-title\">\r\n                  <strong>bug处理情况</strong>\r\n                  <el-icon><PieChart /></el-icon>\r\n                </div>\r\n              </div>\r\n            </template>\r\n            <div class=\"chart-container\">\r\n              <BugChart :testData=\"proBug\"></BugChart>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n    </el-row>\r\n\r\n    <!-- Bottom Row -->\r\n    <el-row :gutter=\"18\">\r\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"9\" class=\"mb-18\" v-if=\"mockLog && mockLog.length > 0\">\r\n          <el-card shadow=\"hover\" class=\"log-card\" v-loading=\"loading\">\r\n            <template #header>\r\n              <div class=\"card-header-flex\">\r\n                <div class=\"header-title\">\r\n                  <strong>Mock日志</strong>\r\n                  <el-icon><Tickets /></el-icon>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          <div class=\"timeline-container\">\r\n            <el-timeline>\r\n              <el-timeline-item v-for=\"(activity, index) in mockLog\"\r\n                                :key=\"index\"\r\n                                :timestamp=\"$tools.rTime(activity.create_time)\"\r\n                                placement=\"top\"\r\n                                color=\"#0bbd87\">\r\n              <div class=\"log-item\">\r\n                <el-tag v-if=\"activity.method==='GET'\" type=\"success\" size=\"small\">{{activity.method}}</el-tag>\r\n                <el-tag v-else size=\"small\">{{activity.method}}</el-tag>\r\n                <span class=\"log-url\">{{activity.url}}</span>\r\n                <div class=\"log-details\">\r\n                  <span class=\"log-label\">调用IP：</span>\r\n                  <span class=\"log-value\">{{activity.ip}}</span>\r\n                  <span class=\"log-label\">HTTP状态码：</span>\r\n                  <span \r\n                    class=\"log-status\"\r\n                    :class=\"{\r\n                      'status-success': activity.status_code==='200',\r\n                      'status-warning': activity.status_code==='400',\r\n                      'status-error': activity.status_code==='500'\r\n                    }\">\r\n                    {{activity.status_code}}\r\n                  </span>\r\n                  <span class=\"log-time\">{{activity.time_consuming}}</span>\r\n                </div>\r\n              </div>\r\n              </el-timeline-item>\r\n            </el-timeline>\r\n          </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"15\" class=\"mb-18\" v-if=\"proReport && proReport.length > 0\">\r\n          <el-card shadow=\"hover\" class=\"chart-card\" v-loading=\"loading\">\r\n            <template #header>\r\n              <div class=\"card-header-flex\">\r\n                <div class=\"header-title\">\r\n                  <strong>近三天报告运行情况统计</strong>\r\n                  <el-icon><DataAnalysis /></el-icon>\r\n                </div>\r\n                <div class=\"header-info\">\r\n                  <span>通过率(%)</span>\r\n                </div>\r\n              </div>\r\n            </template>\r\n            <div class=\"chart-container\">\r\n              <ReportChart :testData=\"proReport\"></ReportChart>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n    </el-row>\r\n  </div>\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script >\r\nimport { ElCard, ElRow, ElCol} from 'element-plus';\r\nimport ApiChart from '../../components/echart/ApiChart.vue'\r\nimport WeekLoginChart from '../../components/echart/WeekLoginChart.vue'\r\nimport BugChart from '../../components/echart/BugChart.vue'\r\nimport ReportChart from '../../components/echart/ReportChart.vue'\r\nimport {mapMutations, mapState} from 'vuex';\r\nimport countTo from '../../components/to'\r\nimport { \r\n  Top, \r\n  Bottom, \r\n  Suitcase, \r\n  Calendar, \r\n  Search, \r\n  DataAnalysis, \r\n  PieChart, \r\n  Tickets, \r\n} from '@element-plus/icons-vue'\r\n\r\nexport default {\r\n  components: {\r\n    ElCard, ElRow, ElCol,\r\n    ApiChart, countTo, WeekLoginChart,\r\n    BugChart, ReportChart,\r\n    Top, Bottom, Suitcase, Calendar, \r\n    Search, DataAnalysis, PieChart, Tickets\r\n  },\r\n  data() {\r\n    // 创建默认的近七天时间范围\r\n    const end = new Date()\r\n    const start = new Date()\r\n    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n    \r\n    // 格式化日期为 \"YYYY-MM-DD HH:mm:ss\" 格式\r\n    const formatDate = (date) => {\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${date.getHours() === 0 ? '00' : date.getHours()}:${date.getMinutes() === 0 ? '00' : date.getMinutes()}:${date.getSeconds() === 0 ? '00' : date.getSeconds()}`\r\n    }\r\n    \r\n    return {\r\n      proall: null,\r\n      proInfo: null,\r\n      proBug:null,\r\n      proCase:null,\r\n      proReport:null,\r\n      buttonClick:null,\r\n      mockLog:null,\r\n      defaultTimeOptions: ['00:00:00', '23:59:59'],\r\n      // 默认设置为近七天的时间范围\r\n      dataTime: [formatDate(start), formatDate(end)],\r\n      shortcuts: [\r\n        {\r\n          text: '过去一周',\r\n          value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n            return [start, end]\r\n          },\r\n        },\r\n        {\r\n          text: '过去一个月',\r\n          value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n            return [start, end]\r\n          },\r\n        },\r\n        {\r\n          text: '过去三个月',\r\n          value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)\r\n            return [start, end]\r\n          },\r\n        },\r\n      ],\r\n      screenSize: {\r\n        xs: false,\r\n        sm: false,\r\n        md: false,\r\n        lg: false,\r\n        xl: false\r\n      },\r\n      loading: false\r\n    };\r\n  },\r\n \tmethods: {\r\n\t\t...mapMutations(['selectPro']),\r\n    async getProInfo(starttime, endtime) {\r\n      this.loading = true;\r\n      try {\r\n        let data = {\"project\": this.pro.id};\r\n        if(starttime && endtime){\r\n          data = {\"project\": this.pro.id,\"starttime\": starttime, \"endtime\": endtime}\r\n        }\r\n        const response = await this.$api.getProjectBoard(data);\r\n        if (response.status === 200) {\r\n          this.proall = response.data;\r\n          this.proInfo = this.proall.project_info;\r\n          this.proBug = this.proall.project_bug;\r\n          this.proCase = this.proall.project_case;\r\n          this.proReport = this.proall.project_report;\r\n          this.buttonClick = this.proall.track_button_click;\r\n          this.mockLog = this.proall.mock_log;\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目看板数据失败:', error);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    submitForm() {\r\n      if (!this.dataTime){\r\n        this.dataTime = []\r\n      }\r\n      const starttime = this.dataTime[0]\r\n      const endtime = this.dataTime[1]\r\n      this.getProInfo(starttime, endtime)\r\n    },\r\n    handleResize() {\r\n      const width = window.innerWidth;\r\n      this.screenSize = {\r\n        xs: width < 576,\r\n        sm: width >= 576 && width < 768,\r\n        md: width >= 768 && width < 992,\r\n        lg: width >= 992 && width < 1200,\r\n        xl: width >= 1200\r\n      };\r\n    }\r\n\t},\r\n\tcomputed: {\r\n\t\t...mapState(['pro']),\r\n\t},\r\n\tcreated() {\r\n    // 使用默认的近七天时间范围获取数据\r\n    const starttime = this.dataTime[0]\r\n    const endtime = this.dataTime[1]\r\n\t\tthis.getProInfo(starttime, endtime);\r\n    this.handleResize();\r\n    window.addEventListener('resize', this.handleResize);\r\n\t},\r\n  beforeUnmount() {\r\n    window.removeEventListener('resize', this.handleResize);\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.dashboard {\r\n  padding: 16px;\r\n  background: #f7fafc;\r\n  min-height: calc(100vh - 50px);\r\n}\r\n\r\n/* Responsive spacing */\r\n.mb-12 {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.mb-18 {\r\n  margin-bottom: 18px;\r\n}\r\n\r\n.mb-20 {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n\r\n/* Card styling */\r\n.el-card {\r\n  --el-card-border-color: transparent;\r\n  --el-card-border-radius: 12px;\r\n  --el-card-padding: 16px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;\r\n  transition: all 0.3s ease;\r\n  border: none;\r\n  overflow: hidden;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.hover-scale:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.09) !important;\r\n}\r\n\r\n/* Stats cards */\r\n.stat-card {\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);\r\n  opacity: 0.7;\r\n  transform: scaleX(0);\r\n  transform-origin: left;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.stat-card:hover::after {\r\n  transform: scaleX(1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.stat-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #4b5563;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #111827;\r\n  line-height: 1.2;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 14px;\r\n  color: #6b7280;\r\n  margin-top: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.increase-icon {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  color: #10b981;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.decrease-icon {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  color: #ef4444;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.percentage {\r\n  margin-left: auto;\r\n  font-weight: 600;\r\n  display: inline-flex;\r\n  align-items: center;\r\n}\r\n\r\n.percentage .el-icon {\r\n  margin-right: 4px;\r\n}\r\n\r\n.job-status {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  margin-top: 20px;\r\n  color: #6b7280;\r\n}\r\n\r\n.job-status b {\r\n  font-weight: 600;\r\n  color: #111827;\r\n  margin-left: 4px;\r\n}\r\n\r\n.paused-status {\r\n  color: #ef4444;\r\n}\r\n\r\n/* Chart cards */\r\n.chart-card {\r\n  height: 100%;\r\n  background: white;\r\n}\r\n\r\n.card-header-flex {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f1f5f9;\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n}\r\n\r\n.header-title i {\r\n  font-size: 18px;\r\n  color: #6366f1;\r\n}\r\n\r\n.date-filter-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.filter-btn {\r\n  background: #6366f1;\r\n  border-color: #6366f1;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n}\r\n\r\n.filter-btn:hover {\r\n  background: #4f46e5;\r\n  border-color: #4f46e5;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.date-filter-popover {\r\n  padding: 16px !important;\r\n  border-radius: 8px !important;\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;\r\n}\r\n\r\n.date-filter {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n  min-width: 320px;\r\n}\r\n\r\n.date-picker {\r\n  width: 100%;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .card-header-flex {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .date-filter-wrapper {\r\n    width: 100%;\r\n  }\r\n  \r\n  .date-picker {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-btn {\r\n    margin-left: 0;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.search-btn {\r\n  width: 100%;\r\n  background: #6366f1;\r\n  border-color: #6366f1;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-btn:hover {\r\n  background: #4f46e5;\r\n  border-color: #4f46e5;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.chart-container {\r\n  height: calc(100% - 40px);\r\n  padding: 16px;\r\n}\r\n\r\n/* Log card */\r\n.log-card {\r\n  height: 100%;\r\n}\r\n\r\n.timeline-container {\r\n  height: calc(100% - 52px);\r\n  padding: 8px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.log-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  width: 100%;\r\n}\r\n\r\n.log-url {\r\n  margin: 6px 0;\r\n  font-family: monospace;\r\n  color: #4b5563;\r\n  font-size: 13px;\r\n  word-break: break-all;\r\n}\r\n\r\n.log-details {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  align-items: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n.log-label {\r\n  font-weight: 600;\r\n  font-size: 13px;\r\n  color: #4b5563;\r\n}\r\n\r\n.log-value {\r\n  font-size: 13px;\r\n  color: #6b7280;\r\n}\r\n\r\n.log-status {\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n}\r\n\r\n.status-success {\r\n  background-color: #d1fae5;\r\n  color: #059669;\r\n}\r\n\r\n.status-warning {\r\n  background-color: #fef3c7;\r\n  color: #d97706;\r\n}\r\n\r\n.status-error {\r\n  background-color: #fee2e2;\r\n  color: #dc2626;\r\n}\r\n\r\n.log-time {\r\n  font-size: 13px;\r\n  color: #6b7280;\r\n  margin-left: auto;\r\n}\r\n\r\n.header-info {\r\n  font-size: 14px;\r\n  color: #6b7280;\r\n}\r\n\r\n/* Dark mode compatibility & theme variables */\r\n:root {\r\n  --primary-color: #6366f1;\r\n  --success-color: #10b981;\r\n  --warning-color: #f59e0b;\r\n  --danger-color: #ef4444;\r\n  --text-primary: #111827;\r\n  --text-secondary: #4b5563;\r\n  --text-tertiary: #6b7280;\r\n  --bg-card: #ffffff;\r\n  --bg-page: #f7fafc;\r\n}\r\n\r\n@media (prefers-color-scheme: dark) {\r\n  :root {\r\n    --primary-color: #818cf8;\r\n    --success-color: #34d399;\r\n    --warning-color: #fbbf24;\r\n    --danger-color: #f87171;\r\n    --text-primary: #f9fafb;\r\n    --text-secondary: #e5e7eb;\r\n    --text-tertiary: #d1d5db;\r\n    --bg-card: #1f2937;\r\n    --bg-page: #111827;\r\n  }\r\n}\r\n\r\n/* 修复popover样式，这需要全局生效 */\r\n:deep(.el-popover.date-filter-popover) {\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n}\r\n</style>\r\n", "<template>\r\n  <div ref=\"echarts\" style=\"height: 400px;\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\n\r\nexport default {\r\n  props: {\r\n    testData: {\r\n      type: Array,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      option: {\r\n        color: ['#409eff', '#67c23a', '#e6a23c'],\r\n        legend: { x: 'center', y: 'bottom' },\r\n        tooltip: {},\r\n        dataset: {\r\n          dimensions: ['product', '接口调试', '新增接口', '新增用例'],\r\n          source: [] // 使用 testData 设置数据\r\n        },\r\n        xAxis: { type: 'category' },\r\n        yAxis: { max: null },\r\n        series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.renderChart();\r\n  },\r\n  updated() {\r\n    this.renderChart();\r\n  },\r\n  methods: {\r\n    renderChart() {\r\n      // 设置数据\r\n      this.option.dataset.source = this.testData.map(item => [\r\n        item.user,\r\n        item.interface_debug_count,\r\n        item.interface_new_count,\r\n        item.testcase_new_count\r\n      ]);\r\n\r\n      const chartDom = this.$refs.echarts;\r\n      const myChart = echarts.init(chartDom);\r\n      myChart.setOption(this.option);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 可以添加样式 */\r\n</style>\r\n", "import { render } from \"./ApiChart.vue?vue&type=template&id=f376b006&scoped=true\"\nimport script from \"./ApiChart.vue?vue&type=script&lang=js\"\nexport * from \"./ApiChart.vue?vue&type=script&lang=js\"\n\nimport \"./ApiChart.vue?vue&type=style&index=0&id=f376b006&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-f376b006\"]])\n\nexport default __exports__", "<template>\r\n  <div ref=\"chart\" style=\"height: 400px;\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\n\r\nexport default {\r\n  props: {\r\n    testData: {\r\n      type: Array,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null,\r\n      option: {\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: 'rgb(103, 77, 204)', // 修改轴线颜色\r\n              width: 5\r\n            }\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLabel: {\r\n            color: 'black',\r\n            margin: 15 // 调整标签文字位置\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n          },\r\n          data: [] // 设置 x 轴数据\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          boundaryGap: [0, '30%'],\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: 'rgb(103, 77, 204)', // 修改轴线颜色\r\n              width: 5\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: 'black'\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n          }\r\n        },\r\n        visualMap: {\r\n          type: 'piecewise',\r\n          show: false,\r\n          dimension: 0,\r\n          seriesIndex: 0,\r\n          pieces: [\r\n            {\r\n              gt: 0,\r\n              lt: 7,\r\n              color: '#66b1ff'\r\n            }\r\n          ]\r\n        },\r\n        series: [\r\n          {\r\n            type: 'line',\r\n            smooth: 0.6,\r\n            symbol: 'none',\r\n            lineStyle: {\r\n              color: '#0d84ff',\r\n              width: 5\r\n            },\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(\r\n                0,\r\n                0,\r\n                0,\r\n                1,\r\n                [{\r\n                  offset: 0,\r\n                  color: '#79bbff'\r\n                },\r\n                {\r\n                  offset: 0.5,\r\n                  color: '#a0cfff'\r\n                },\r\n                {\r\n                  offset: 1,\r\n                  color: '#c6e2ff'\r\n                }\r\n                ],\r\n                false\r\n              )\r\n            },\r\n            data: [] // 设置 y 轴数据\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.initChart();\r\n  },\r\n  updated() {\r\n    this.initChart();\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$refs.chart);\r\n      // 设置 x 轴数据\r\n      this.option.xAxis.data = this.testData.map(item => item.date);\r\n      // 设置 y 轴数据\r\n      this.option.series[0].data = this.testData.map(item => item.clicks);\r\n      this.chart.setOption(this.option);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n</style>\r\n", "import { render } from \"./WeekLoginChart.vue?vue&type=template&id=746136b9\"\nimport script from \"./WeekLoginChart.vue?vue&type=script&lang=js\"\nexport * from \"./WeekLoginChart.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\r\n  <div ref=\"chart\" style=\"height: 400px;\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\n\r\nexport default {\r\n  props: {\r\n    testData: {\r\n      type: Array,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n    };\r\n  },\r\n  mounted() {\r\n    this.initChart();\r\n  },\r\n  updated() {\r\n    this.initChart();\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      const chart = echarts.init(this.$refs.chart);\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{d}%【{c}条】',\r\n        },\r\n        legend: {\r\n          orient: 'vertical',\r\n          left: '75%', // 居左\r\n          top: '35%', // 居中\r\n        },\r\n        series: [\r\n          {\r\n            name: 'Access From',\r\n            type: 'pie',\r\n            center: ['40%', '50%'],\r\n            radius: ['50%', '60%'],\r\n            avoidLabelOverlap: false,\r\n            padAngle: 5,\r\n            itemStyle: {\r\n              borderRadius: 10\r\n            },\r\n            label: {\r\n              show: false,\r\n              position: 'center'\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: 20,\r\n                fontWeight: 'bold'\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: this.testData // 使用数据\r\n          }\r\n        ]\r\n      };\r\n      chart.setOption(option);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n</style>\r\n", "import { render } from \"./BugChart.vue?vue&type=template&id=253072a3\"\nimport script from \"./BugChart.vue?vue&type=script&lang=js\"\nexport * from \"./BugChart.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\r\n  <div ref=\"chart\" style=\"height: 400px;\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\n\r\nexport default {\r\n  props: {\r\n    testData: {\r\n      type: Array,\r\n      required: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initChart();\r\n  },\r\n  updated() {\r\n    this.initChart();\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      const chart = echarts.init(this.$refs.chart);\r\n      const option = {\r\n        grid: {\r\n          top: 50,\r\n          bottom: 10,\r\n          left: 20,\r\n          right: 20,\r\n          containLabel: true\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          formatter: params => {\r\n            const dataIndex = params[0].dataIndex;\r\n            const label = params[0].axisValueLabel;\r\n            const value = params[0].value;\r\n            return `${this.testData[dataIndex].plan_id__name} <br/>${label} <br/> 通过率：${value}%`;\r\n          },\r\n          axisPointer: {\r\n            type: 'line',\r\n            lineStyle: {\r\n              color: '#95d475'\r\n            }\r\n          }\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: 'rgb(251, 212, 55)',\r\n              width: 5\r\n            }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          data: this.testData.map(item => item.create_time)\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            lineHeight: 40\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: '#eef5f0'\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              // color: '#00aa7f'\r\n            }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        series: [{\r\n          name: '通过率',\r\n          type: 'line',\r\n          smooth: true,\r\n          showSymbol: true,\r\n          symbolSize: 8,\r\n          zlevel: 3,\r\n          itemStyle: {\r\n            color: '#67C23A',\r\n            borderColor: '#a3c8d8'\r\n          },\r\n          lineStyle: {\r\n            width: 3,\r\n            color: '#67C23A'\r\n          },\r\n          areaStyle: {\r\n            color: new echarts.graphic.LinearGradient(\r\n              0,\r\n              0,\r\n              0,\r\n              1,\r\n              [{\r\n                offset: 0,\r\n                color: '#95d475'\r\n              },\r\n              {\r\n                offset: 0.5,\r\n                color: '#b3e19d'\r\n              },\r\n              {\r\n                offset: 1,\r\n                color: '#d1edc4'\r\n              }\r\n              ],\r\n              false\r\n            )\r\n          },\r\n          data: this.testData.map(item => parseFloat(item.pass_rate))\r\n        }]\r\n      };\r\n      chart.setOption(option);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 样式可以根据需要自行调整 */\r\n</style>\r\n", "import { render } from \"./ReportChart.vue?vue&type=template&id=11044330&scoped=true\"\nimport script from \"./ReportChart.vue?vue&type=script&lang=js\"\nexport * from \"./ReportChart.vue?vue&type=script&lang=js\"\n\nimport \"./ReportChart.vue?vue&type=style&index=0&id=11044330&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-11044330\"]])\n\nexport default __exports__", "import { render } from \"./Project.vue?vue&type=template&id=e162c9a6&scoped=true\"\nimport script from \"./Project.vue?vue&type=script&lang=js\"\nexport * from \"./Project.vue?vue&type=script&lang=js\"\n\nimport \"./Project.vue?vue&type=style&index=0&id=e162c9a6&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-e162c9a6\"]])\n\nexport default __exports__", "<template>\r\n    <span>\r\n      {{displayValue}}\r\n    </span>\r\n</template>\r\n<script>\r\nimport { requestAnimationFrame, cancelAnimationFrame } from './requestAnimationFrame.js'\r\nexport default {\r\n  props: {\r\n    startVal: {\r\n      type: Number,\r\n      required: false,\r\n      default: 0\r\n    },\r\n    endVal: {\r\n      type: Number,\r\n      required: false,\r\n      default: 2017\r\n    },\r\n    duration: {\r\n      type: Number,\r\n      required: false,\r\n      default: 3000\r\n    },\r\n    autoplay: {\r\n      type: Boolean,\r\n      required: false,\r\n      default: true\r\n    },\r\n    decimals: {\r\n      type: Number,\r\n      required: false,\r\n      default: 0,\r\n      validator(value) {\r\n        return value >= 0\r\n      }\r\n    },\r\n    decimal: {\r\n      type: String,\r\n      required: false,\r\n      default: '.'\r\n    },\r\n    separator: {\r\n      type: String,\r\n      required: false,\r\n      default: ','\r\n    },\r\n    prefix: {\r\n      type: String,\r\n      required: false,\r\n      default: ''\r\n    },\r\n    suffix: {\r\n      type: String,\r\n      required: false,\r\n      default: ''\r\n    },\r\n    useEasing: {\r\n      type: Boolean,\r\n      required: false,\r\n      default: true\r\n    },\r\n    easingFn: {\r\n      type: Function,\r\n      default(t, b, c, d) {\r\n        return c * (-Math.pow(2, -10 * t / d) + 1) * 1024 / 1023 + b;\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      localStartVal: this.startVal,\r\n      displayValue: this.formatNumber(this.startVal),\r\n      printVal: null,\r\n      paused: false,\r\n      localDuration: this.duration,\r\n      startTime: null,\r\n      timestamp: null,\r\n      remaining: null,\r\n      rAF: null\r\n    };\r\n  },\r\n  computed: {\r\n    countDown() {\r\n      return this.startVal > this.endVal\r\n    }\r\n  },\r\n  watch: {\r\n    startVal() {\r\n      if (this.autoplay) {\r\n        this.start();\r\n      }\r\n    },\r\n    endVal() {\r\n      if (this.autoplay) {\r\n        this.start();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    if (this.autoplay) {\r\n      this.start();\r\n    }\r\n    this.$emit('mountedCallback')\r\n  },\r\n  methods: {\r\n    start() {\r\n      this.localStartVal = this.startVal;\r\n      this.startTime = null;\r\n      this.localDuration = this.duration;\r\n      this.paused = false;\r\n      this.rAF = requestAnimationFrame(this.count);\r\n    },\r\n    pauseResume() {\r\n      if (this.paused) {\r\n        this.resume();\r\n        this.paused = false;\r\n      } else {\r\n        this.pause();\r\n        this.paused = true;\r\n      }\r\n    },\r\n    pause() {\r\n      cancelAnimationFrame(this.rAF);\r\n    },\r\n    resume() {\r\n      this.startTime = null;\r\n      this.localDuration = +this.remaining;\r\n      this.localStartVal = +this.printVal;\r\n      requestAnimationFrame(this.count);\r\n    },\r\n    reset() {\r\n      this.startTime = null;\r\n      cancelAnimationFrame(this.rAF);\r\n      this.displayValue = this.formatNumber(this.startVal);\r\n    },\r\n    count(timestamp) {\r\n      if (!this.startTime) this.startTime = timestamp;\r\n      this.timestamp = timestamp;\r\n      const progress = timestamp - this.startTime;\r\n      this.remaining = this.localDuration - progress;\r\n\r\n      if (this.useEasing) {\r\n        if (this.countDown) {\r\n          this.printVal = this.localStartVal - this.easingFn(progress, 0, this.localStartVal - this.endVal, this.localDuration)\r\n        } else {\r\n          this.printVal = this.easingFn(progress, this.localStartVal, this.endVal - this.localStartVal, this.localDuration);\r\n        }\r\n      } else {\r\n        if (this.countDown) {\r\n          this.printVal = this.localStartVal - ((this.localStartVal - this.endVal) * (progress / this.localDuration));\r\n        } else {\r\n          this.printVal = this.localStartVal + (this.endVal - this.localStartVal) * (progress / this.localDuration);\r\n        }\r\n      }\r\n      if (this.countDown) {\r\n        this.printVal = this.printVal < this.endVal ? this.endVal : this.printVal;\r\n      } else {\r\n        this.printVal = this.printVal > this.endVal ? this.endVal : this.printVal;\r\n      }\r\n\r\n      this.displayValue = this.formatNumber(this.printVal)\r\n      if (progress < this.localDuration) {\r\n        this.rAF = requestAnimationFrame(this.count);\r\n      } else {\r\n        this.$emit('callback');\r\n      }\r\n    },\r\n    isNumber(val) {\r\n      return !isNaN(parseFloat(val))\r\n    },\r\n    formatNumber(num) {\r\n      num = num.toFixed(this.decimals);\r\n      num += '';\r\n      const x = num.split('.');\r\n      let x1 = x[0];\r\n      const x2 = x.length > 1 ? this.decimal + x[1] : '';\r\n      const rgx = /(\\d+)(\\d{3})/;\r\n      if (this.separator && !this.isNumber(this.separator)) {\r\n        while (rgx.test(x1)) {\r\n          x1 = x1.replace(rgx, '$1' + this.separator + '$2');\r\n        }\r\n      }\r\n      return this.prefix + x1 + x2 + this.suffix;\r\n    }\r\n  },\r\n  destroyed() {\r\n    cancelAnimationFrame(this.rAF)\r\n  }\r\n};\r\n</script>\r\n", "let lastTime = 0\r\nconst prefixes = 'webkit moz ms o'.split(' ') // 各浏览器前缀\r\n\r\nlet requestAnimationFrame\r\nlet cancelAnimationFrame\r\n\r\nconst isServer = typeof window === 'undefined'\r\nif (isServer) {\r\n  requestAnimationFrame = function() {\r\n    return\r\n  }\r\n  cancelAnimationFrame = function() {\r\n    return\r\n  }\r\n} else {\r\n  requestAnimationFrame = window.requestAnimationFrame\r\n  cancelAnimationFrame = window.cancelAnimationFrame\r\n  let prefix\r\n    // 通过遍历各浏览器前缀，来得到requestAnimationFrame和cancelAnimationFrame在当前浏览器的实现形式\r\n  for (let i = 0; i < prefixes.length; i++) {\r\n    if (requestAnimationFrame && cancelAnimationFrame) { break }\r\n    prefix = prefixes[i]\r\n    requestAnimationFrame = requestAnimationFrame || window[prefix + 'RequestAnimationFrame']\r\n    cancelAnimationFrame = cancelAnimationFrame || window[prefix + 'CancelAnimationFrame'] || window[prefix + 'CancelRequestAnimationFrame']\r\n  }\r\n\r\n  // 如果当前浏览器不支持requestAnimationFrame和cancelAnimationFrame，则会退到setTimeout\r\n  if (!requestAnimationFrame || !cancelAnimationFrame) {\r\n    requestAnimationFrame = function(callback) {\r\n      const currTime = new Date().getTime()\r\n      // 为了使setTimteout的尽可能的接近每秒60帧的效果\r\n      const timeToCall = Math.max(0, 16 - (currTime - lastTime))\r\n      const id = window.setTimeout(() => {\r\n        callback(currTime + timeToCall)\r\n      }, timeToCall)\r\n      lastTime = currTime + timeToCall\r\n      return id\r\n    }\r\n\r\n    cancelAnimationFrame = function(id) {\r\n      window.clearTimeout(id)\r\n    }\r\n  }\r\n}\r\n\r\nexport { requestAnimationFrame, cancelAnimationFrame }\r\n", "import { render } from \"./vue-countTo.vue?vue&type=template&id=143bf602\"\nimport script from \"./vue-countTo.vue?vue&type=script&lang=js\"\nexport * from \"./vue-countTo.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import CountTo from './vue-countTo.vue';\r\nexport default CountTo;\r\nif (typeof window !== 'undefined' && window.Vue) {\r\n  window.Vue.component('count-to', CountTo);\r\n}\r\n"], "names": ["class", "_createBlock", "_component_el_scrollbar", "height", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_el_row", "gutter", "_createElementBlock", "_Fragment", "_renderList", "$data", "proInfo", "item", "index", "_component_el_col", "key", "xs", "sm", "md", "lg", "xl", "_component_el_card", "shadow", "_hoisted_2", "_hoisted_3", "_toDisplayString", "name", "_component_countTo", "count", "duration", "changeType", "_hoisted_4", "_hoisted_5", "_component_el_icon", "_component_Top", "percentage", "_hoisted_6", "_hoisted_7", "_component_Bottom", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "run_service", "_hoisted_13", "paused", "loading", "proCase", "length", "header", "_withCtx", "_hoisted_14", "_hoisted_15", "_component_Suitcase", "_hoisted_16", "_component_el_popover", "placement", "trigger", "width", "reference", "_component_el_button", "type", "size", "style", "_component_Calendar", "_hoisted_17", "_component_el_date_picker", "dataTime", "$event", "defaultTimeOptions", "shortcuts", "clearable", "onClick", "$options", "submitForm", "_component_Search", "_hoisted_18", "_component_ApiChart", "testData", "buttonClick", "_hoisted_19", "_hoisted_20", "_component_DataAnalysis", "_hoisted_21", "_component_Week<PERSON><PERSON><PERSON><PERSON><PERSON>", "proBug", "_hoisted_22", "_hoisted_23", "_component_<PERSON><PERSON><PERSON>", "_hoisted_24", "_component_<PERSON><PERSON><PERSON><PERSON>", "mockLog", "_hoisted_25", "_hoisted_26", "_component_Tickets", "_hoisted_27", "_component_el_timeline", "activity", "_component_el_timeline_item", "timestamp", "_ctx", "$tools", "rTime", "create_time", "color", "_hoisted_28", "method", "_component_el_tag", "_hoisted_29", "url", "_hoisted_30", "_hoisted_31", "ip", "_normalizeClass", "status_code", "_hoisted_32", "time_consuming", "proReport", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_component_ReportChart", "ref", "props", "Array", "required", "data", "option", "legend", "x", "y", "tooltip", "dataset", "dimensions", "source", "xAxis", "yAxis", "max", "series", "mounted", "this", "<PERSON><PERSON><PERSON>", "updated", "methods", "map", "user", "interface_debug_count", "interface_new_count", "testcase_new_count", "chartDom", "$refs", "echarts", "myChart", "setOption", "__exports__", "chart", "boundaryGap", "axisLine", "lineStyle", "axisTick", "show", "axisLabel", "margin", "splitLine", "visualMap", "dimension", "seriesIndex", "pieces", "gt", "lt", "smooth", "symbol", "areaStyle", "offset", "initChart", "date", "clicks", "formatter", "orient", "left", "top", "center", "radius", "avoidLabelOverlap", "padAngle", "itemStyle", "borderRadius", "label", "position", "emphasis", "fontSize", "fontWeight", "labelLine", "grid", "bottom", "right", "containLabel", "params", "dataIndex", "axisValueLabel", "value", "plan_id__name", "axisPointer", "nameTextStyle", "lineHeight", "showSymbol", "symbolSize", "zlevel", "borderColor", "parseFloat", "pass_rate", "components", "ElCard", "ElRow", "ElCol", "<PERSON><PERSON><PERSON><PERSON>", "countTo", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ReportChart", "Top", "Bottom", "Suitcase", "Calendar", "Search", "DataAnalysis", "<PERSON><PERSON><PERSON>", "Tickets", "end", "Date", "start", "setTime", "getTime", "formatDate", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "getHours", "getMinutes", "getSeconds", "proall", "text", "screenSize", "mapMutations", "getProInfo", "starttime", "endtime", "pro", "id", "response", "$api", "getProjectBoard", "status", "project_info", "project_bug", "project_case", "project_report", "track_button_click", "mock_log", "error", "console", "handleResize", "window", "innerWidth", "computed", "mapState", "created", "addEventListener", "beforeUnmount", "removeEventListener", "render", "displayValue", "lastTime", "prefixes", "split", "requestAnimationFrame", "cancelAnimationFrame", "isServer", "prefix", "i", "callback", "currTime", "timeToCall", "Math", "setTimeout", "clearTimeout", "startVal", "Number", "default", "endVal", "autoplay", "Boolean", "decimals", "validator", "decimal", "separator", "suffix", "useEasing", "easingFn", "Function", "t", "b", "c", "d", "pow", "localStartVal", "formatNumber", "printVal", "localDuration", "startTime", "remaining", "rAF", "countDown", "watch", "$emit", "pauseResume", "resume", "pause", "reset", "progress", "isNumber", "val", "isNaN", "num", "toFixed", "x1", "x2", "rgx", "test", "replace", "destroyed", "<PERSON><PERSON>", "component", "<PERSON><PERSON><PERSON>"], "sourceRoot": ""}