"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[815],{80815:function(e,t,l){l.r(t),l.d(t,{default:function(){return s}});var r=l(56768);const n={style:{padding:"20px"}};function a(e,t,l,a,o,u){const i=(0,r.g2)("el-input"),s=(0,r.g2)("el-button");return(0,r.uX)(),(0,r.CE)("div",n,[t[2]||(t[2]=(0,r.Lk)("h3",null,"性能报告详情页面测试",-1)),t[3]||(t[3]=(0,r.Lk)("p",null,"请输入报告ID进行测试：",-1)),(0,r.bF)(i,{modelValue:o.testReportId,"onUpdate:modelValue":t[0]||(t[0]=e=>o.testReportId=e),placeholder:"输入报告ID",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue"]),(0,r.bF)(s,{type:"primary",onClick:u.goToReportDetail},{default:(0,r.k6)(()=>t[1]||(t[1]=[(0,r.eW)("查看报告详情")])),_:1,__:[1]},8,["onClick"]),t[4]||(t[4]=(0,r.Lk)("div",{style:{"margin-top":"20px"}},[(0,r.Lk)("h4",null,"测试链接："),(0,r.Lk)("ul",null,[(0,r.Lk)("li",null,[(0,r.Lk)("a",{href:"#/PerformanceResult-Detail/1"},"报告ID=1")]),(0,r.Lk)("li",null,[(0,r.Lk)("a",{href:"#/PerformanceResult-Detail/2"},"报告ID=2")]),(0,r.Lk)("li",null,[(0,r.Lk)("a",{href:"#/PerformanceResult-Detail/3"},"报告ID=3")])])],-1))])}l(44114);var o={name:"TestReportDetail",data(){return{testReportId:"1"}},methods:{goToReportDetail(){this.testReportId?this.$router.push({name:"PerformanceResult-Detail",params:{id:this.testReportId}}):this.$message.error("请输入报告ID")}}},u=l(71241);const i=(0,u.A)(o,[["render",a]]);var s=i}}]);
//# sourceMappingURL=815.0d389910.js.map