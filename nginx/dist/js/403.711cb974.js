"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[403],{57403:function(e,l,a){a.r(l),a.d(l,{default:function(){return E}});a(18111),a(22489);var t=a(56768),o=a(24232),n=a(45130);const i={class:"cron-container"},r={class:"cron-header"},s={key:0,class:"stats-cards"},d={class:"stat-card"},c={class:"stat-value"},u={class:"stat-card"},p={class:"stat-value"},b={class:"stat-card"},m={class:"stat-value"},h={class:"table-container"},k={class:"table-header"},f={class:"table-actions"},g={class:"row-index"},v={class:"time-info"},T={class:"action-buttons"},F={class:"dialog-footer"},_={class:"dialog-footer"},C={class:"dialog-footer"};function y(e,l,a,y,V,D){const w=(0,t.g2)("Plus"),$=(0,t.g2)("el-icon"),x=(0,t.g2)("el-button"),A=(0,t.g2)("el-tooltip"),E=(0,t.g2)("el-table-column"),L=(0,t.g2)("el-tag"),R=(0,t.g2)("el-switch"),U=(0,t.g2)("Edit"),Y=(0,t.g2)("Delete"),S=(0,t.g2)("el-table"),W=(0,t.g2)("el-card"),I=(0,t.g2)("Notebook"),O=(0,t.g2)("el-input"),X=(0,t.g2)("el-form-item"),P=(0,t.g2)("el-option"),K=(0,t.g2)("el-select"),j=(0,t.g2)("Timer"),z=(0,t.g2)("timerTaskCron"),N=(0,t.g2)("el-popover"),H=(0,t.g2)("el-form"),q=(0,t.g2)("el-tab-pane"),Q=(0,t.g2)("el-divider"),B=(0,t.g2)("Link"),G=(0,t.g2)("Key"),J=(0,t.g2)("InfoFilled"),M=(0,t.g2)("el-cascader"),Z=(0,t.g2)("el-tabs"),ee=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.CE)("div",i,[(0,t.Lk)("div",r,[l[39]||(l[39]=(0,t.Lk)("div",{class:"title-container"},[(0,t.Lk)("h2",{class:"page-title"},"定时任务管理"),(0,t.Lk)("p",{class:"page-subtitle"},"创建和管理自动化测试任务")],-1)),(0,t.bF)(x,{type:"primary",class:"add-task-btn",onClick:l[0]||(l[0]=e=>V.addDialog=!0)},{default:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(w)]),_:1}),l[38]||(l[38]=(0,t.eW)(" 添加定时任务 "))]),_:1,__:[38]})]),V.cronList?((0,t.uX)(),(0,t.CE)("div",s,[(0,t.Lk)("div",d,[(0,t.Lk)("div",c,(0,o.v_)(V.cronList.length),1),l[40]||(l[40]=(0,t.Lk)("div",{class:"stat-label"},"总任务数",-1))]),(0,t.Lk)("div",u,[(0,t.Lk)("div",p,(0,o.v_)(V.cronList.filter(e=>e.status).length),1),l[41]||(l[41]=(0,t.Lk)("div",{class:"stat-label"},"运行中任务",-1))]),(0,t.Lk)("div",b,[(0,t.Lk)("div",m,(0,o.v_)(V.cronList.filter(e=>!e.status).length),1),l[42]||(l[42]=(0,t.Lk)("div",{class:"stat-label"},"暂停任务",-1))])])):(0,t.Q3)("",!0),(0,t.Lk)("div",h,[(0,t.bF)(W,{shadow:"hover",class:"cron-table-card"},{header:(0,t.k6)(()=>[(0,t.Lk)("div",k,[l[43]||(l[43]=(0,t.Lk)("span",{class:"table-title"},"定时任务列表",-1)),(0,t.Lk)("div",f,[(0,t.bF)(A,{content:"刷新数据",placement:"top"},{default:(0,t.k6)(()=>[(0,t.bF)(x,{type:"primary",text:"",icon:V.Refresh,circle:"",onClick:D.getAllCron},null,8,["icon","onClick"])]),_:1})])])]),default:(0,t.k6)(()=>[(0,t.bF)(S,{data:V.cronList,style:{width:"100%"},size:"default","empty-text":"暂无数据",border:"",stripe:"","highlight-current-row":"",class:"cron-table"},{default:(0,t.k6)(()=>[(0,t.bF)(E,{label:"序号",align:"center",width:"60"},{default:(0,t.k6)(e=>[(0,t.Lk)("span",g,(0,o.v_)(e.$index+1),1)]),_:1}),(0,t.bF)(E,{prop:"name",label:"名称",align:"center"}),(0,t.bF)(E,{prop:"plan_name",label:"执行任务",align:"center"}),(0,t.bF)(E,{prop:"env_name",label:"执行环境",align:"center"}),(0,t.bF)(E,{prop:"rule",label:"时间配置",align:"center"},{default:(0,t.k6)(e=>[(0,t.bF)(L,{size:"small",type:"info"},{default:(0,t.k6)(()=>[(0,t.eW)((0,o.v_)(e.row.rule),1)]),_:2},1024)]),_:1}),(0,t.bF)(E,{label:"创建时间",align:"center"},{default:(0,t.k6)(l=>[(0,t.Lk)("span",v,(0,o.v_)(e.$tools.rTime(l.row.create_time)),1)]),_:1}),(0,t.bF)(E,{label:"状态",align:"center",width:"100"},{default:(0,t.k6)(e=>[(0,t.bF)(R,{onChange:l=>D.switchCronStatus(e.row),modelValue:e.row.status,"onUpdate:modelValue":l=>e.row.status=l,"active-color":"#13ce66","inactive-color":"#b1b1b1"},null,8,["onChange","modelValue","onUpdate:modelValue"])]),_:1}),(0,t.bF)(E,{label:"操作",align:"center",width:"200"},{default:(0,t.k6)(e=>[(0,t.Lk)("div",T,[(0,t.bF)(x,{onClick:l=>D.showUpdateCronDlg(e.row),type:"primary",size:"small"},{default:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(U)]),_:1}),l[44]||(l[44]=(0,t.eW)(" 编辑 "))]),_:2,__:[44]},1032,["onClick"]),(0,t.bF)(x,{onClick:l=>D.delCron(e.row.id),type:"danger",size:"small"},{default:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(Y)]),_:1}),l[45]||(l[45]=(0,t.eW)(" 删除 "))]),_:2,__:[45]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])]),_:1})]),(0,t.bF)(ee,{modelValue:V.addDialog,"onUpdate:modelValue":l[20]||(l[20]=e=>V.addDialog=e),width:"50%",title:"新增定时执行任务","before-close":D.closeDialogCron,"custom-class":"modern-dialog","destroy-on-close":""},{default:(0,t.k6)(()=>[(0,t.bF)(Z,{type:"border-card",modelValue:V.currentTab,"onUpdate:modelValue":l[19]||(l[19]=e=>V.currentTab=e),class:"modern-tabs"},{default:(0,t.k6)(()=>[(0,t.bF)(q,{label:"测试计划自动运行",name:"first"},{default:(0,t.k6)(()=>[(0,t.bF)(H,{model:V.cronTabData,rules:V.rulescronTab,ref:"cronTabRef","label-width":"90px",class:"modern-form"},{default:(0,t.k6)(()=>[(0,t.bF)(X,{label:"任务名称",prop:"name"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.name,"onUpdate:modelValue":l[1]||(l[1]=e=>V.cronTabData.name=e),placeholder:"请输入任务名称"},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(I)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"测试环境",prop:"env"},{default:(0,t.k6)(()=>[(0,t.bF)(K,{modelValue:V.cronTabData.env,"onUpdate:modelValue":l[2]||(l[2]=e=>V.cronTabData.env=e),placeholder:"请选择环境",style:{width:"100%"},"no-data-text":"暂无数据"},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.testEnvs,e=>((0,t.uX)(),(0,t.Wv)(P,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"时间配置",prop:"rule"},{default:(0,t.k6)(()=>[(0,t.bF)(N,{visible:V.cronVisible,"onUpdate:visible":l[5]||(l[5]=e=>V.cronVisible=e),placement:"bottom-start",width:650,trigger:"manual",teleported:!1,"append-to-body":!1,"popper-class":"cron-popover","popper-style":{minHeight:"320px"}},{reference:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.rule,"onUpdate:modelValue":l[3]||(l[3]=e=>V.cronTabData.rule=e),clearable:"",readonly:"",placeholder:"请选择时间配置",onClick:l[4]||(l[4]=(0,n.D$)(e=>D.openCronPopover("first"),["stop"]))},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(j)]),_:1})]),_:1},8,["modelValue"])]),default:(0,t.k6)(()=>[(0,t.bF)(z,{runTimeStr:V.cronTabData.rule,onCloseTime:D.handleCloseTime,onRunTime:D.handleRunTime},null,8,["runTimeStr","onCloseTime","onRunTime"])]),_:1},8,["visible"])]),_:1}),(0,t.bF)(X,{label:"执行计划",prop:"plan"},{default:(0,t.k6)(()=>[(0,t.bF)(K,{style:{width:"100%"},modelValue:V.cronTabData.plan,"onUpdate:modelValue":l[6]||(l[6]=e=>V.cronTabData.plan=e),placeholder:"请选择","no-data-text":"暂无数据"},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.testPlans,e=>((0,t.uX)(),(0,t.Wv)(P,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"是否开启"},{default:(0,t.k6)(()=>[(0,t.bF)(R,{modelValue:V.cronTabData.status,"onUpdate:modelValue":l[7]||(l[7]=e=>V.cronTabData.status=e),"active-color":"#13ce66","inactive-color":"#c3c3c3","active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),(0,t.Lk)("div",F,[(0,t.bF)(x,{onClick:D.closeDialogCron,plain:""},{default:(0,t.k6)(()=>l[46]||(l[46]=[(0,t.eW)("取 消")])),_:1,__:[46]},8,["onClick"]),(0,t.bF)(x,{type:"primary",onClick:l[8]||(l[8]=e=>D.createCron(V.currentTab))},{default:(0,t.k6)(()=>l[47]||(l[47]=[(0,t.eW)("确 定")])),_:1,__:[47]})])]),_:1}),(0,t.bF)(q,{label:"YApi自动导入",name:"second"},{default:(0,t.k6)(()=>[(0,t.bF)(H,{model:V.cronTabData,rules:V.rulescronTab,ref:"cronTabRef","label-width":"100px",class:"modern-form"},{default:(0,t.k6)(()=>[(0,t.bF)(X,{label:"任务名称",prop:"name"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.name,"onUpdate:modelValue":l[9]||(l[9]=e=>V.cronTabData.name=e),placeholder:"请输入任务名称"},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(I)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"时间配置",prop:"rule"},{default:(0,t.k6)(()=>[(0,t.bF)(N,{visible:V.cronVisibleYApi,"onUpdate:visible":l[12]||(l[12]=e=>V.cronVisibleYApi=e),placement:"bottom-start",width:650,trigger:"manual",teleported:!1,"append-to-body":!1,"popper-class":"cron-popover","popper-style":{minHeight:"320px"}},{reference:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.rule,"onUpdate:modelValue":l[10]||(l[10]=e=>V.cronTabData.rule=e),clearable:"",readonly:"",placeholder:"请选择时间配置",onClick:l[11]||(l[11]=(0,n.D$)(e=>D.openCronPopover("second"),["stop"]))},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(j)]),_:1})]),_:1},8,["modelValue"])]),default:(0,t.k6)(()=>[(0,t.bF)(z,{runTimeStr:V.cronTabData.rule,onCloseTime:D.handleCloseTime,onRunTime:D.handleRunTime},null,8,["runTimeStr","onCloseTime","onRunTime"])]),_:1},8,["visible"])]),_:1}),(0,t.bF)(Q,{"content-position":"left"},{default:(0,t.k6)(()=>l[48]||(l[48]=[(0,t.eW)("YApi平台配置")])),_:1,__:[48]}),(0,t.bF)(X,{label:"平台地址",prop:"url"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.yapi.url,"onUpdate:modelValue":l[13]||(l[13]=e=>V.cronTabData.yapi.url=e),placeholder:"请输入YApi平台项目地址",clearable:""},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(B)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"平台TOKEN",prop:"token"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.yapi.token,"onUpdate:modelValue":l[14]||(l[14]=e=>V.cronTabData.yapi.token=e),placeholder:"请输入YApi平台项目token",clearable:"","show-password":""},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(G)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"平台项目ID",prop:"YApiId"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.yapi.YApiId,"onUpdate:modelValue":l[15]||(l[15]=e=>V.cronTabData.yapi.YApiId=e),placeholder:"请输入YApi平台项目id",clearable:""},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(J)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"节点/模块",prop:"treenode"},{default:(0,t.k6)(()=>[(0,t.bF)(M,{modelValue:V.cronTabData.yapi.treenode,"onUpdate:modelValue":l[16]||(l[16]=e=>V.cronTabData.yapi.treenode=e),options:V.treeOptions,props:{label:"name",value:"id",checkStrictly:!0},onChange:D.removeCascaderAriaOwns,onVisibleChange:D.removeCascaderAriaOwns,onExpandChange:D.removeCascaderAriaOwns,clearable:"","collapse-tags":"",filterable:"",placeholder:"请选择节点/模块"},null,8,["modelValue","options","onChange","onVisibleChange","onExpandChange"])]),_:1}),(0,t.bF)(X,{label:"是否开启"},{default:(0,t.k6)(()=>[(0,t.bF)(R,{modelValue:V.cronTabData.status,"onUpdate:modelValue":l[17]||(l[17]=e=>V.cronTabData.status=e),"active-color":"#13ce66","inactive-color":"#c3c3c3","active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),(0,t.Lk)("div",_,[(0,t.bF)(x,{onClick:D.closeDialogCron,plain:""},{default:(0,t.k6)(()=>l[49]||(l[49]=[(0,t.eW)("取 消")])),_:1,__:[49]},8,["onClick"]),(0,t.bF)(x,{type:"primary",onClick:l[18]||(l[18]=e=>D.createCron(V.currentTab))},{default:(0,t.k6)(()=>l[50]||(l[50]=[(0,t.eW)("确 定")])),_:1,__:[50]})])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue","before-close"]),(0,t.bF)(ee,{modelValue:V.editDialog,"onUpdate:modelValue":l[37]||(l[37]=e=>V.editDialog=e),width:"50%",title:"修改定时执行任务","before-close":D.closeDialogCron,"custom-class":"modern-dialog","destroy-on-close":""},{default:(0,t.k6)(()=>[10===V.cronTabData.type?((0,t.uX)(),(0,t.Wv)(H,{key:0,model:V.cronTabData,rules:V.rulescronTab,ref:"cronTabRef","label-width":"90px",class:"modern-form"},{default:(0,t.k6)(()=>[(0,t.bF)(X,{label:"名称",prop:"name"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.name,"onUpdate:modelValue":l[21]||(l[21]=e=>V.cronTabData.name=e)},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(I)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"测试环境",prop:"env"},{default:(0,t.k6)(()=>[(0,t.bF)(K,{modelValue:V.cronTabData.env,"onUpdate:modelValue":l[22]||(l[22]=e=>V.cronTabData.env=e),placeholder:"请选择环境",style:{width:"100%"},"no-data-text":"暂无数据"},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.testEnvs,e=>((0,t.uX)(),(0,t.Wv)(P,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"时间配置"},{default:(0,t.k6)(()=>[(0,t.bF)(N,{visible:V.cronVisibleEdit,"onUpdate:visible":l[25]||(l[25]=e=>V.cronVisibleEdit=e),placement:"bottom-start",width:650,trigger:"manual",teleported:!1,"append-to-body":!1,"popper-class":"cron-popover","popper-style":{minHeight:"320px"}},{reference:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.rule,"onUpdate:modelValue":l[23]||(l[23]=e=>V.cronTabData.rule=e),clearable:"",readonly:"",placeholder:"请选择时间",onClick:l[24]||(l[24]=(0,n.D$)(e=>D.openCronPopover(10),["stop"]))},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(j)]),_:1})]),_:1},8,["modelValue"])]),default:(0,t.k6)(()=>[(0,t.bF)(z,{runTimeStr:V.cronTabData.rule,onCloseTime:D.handleCloseTime,onRunTime:D.handleRunTime},null,8,["runTimeStr","onCloseTime","onRunTime"])]),_:1},8,["visible"])]),_:1}),(0,t.bF)(X,{label:"执行计划",prop:"plan"},{default:(0,t.k6)(()=>[(0,t.bF)(K,{style:{width:"100%"},modelValue:V.cronTabData.plan,"onUpdate:modelValue":l[26]||(l[26]=e=>V.cronTabData.plan=e),placeholder:"请选择","no-data-text":"暂无数据"},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.testPlans,e=>((0,t.uX)(),(0,t.Wv)(P,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"是否开启"},{default:(0,t.k6)(()=>[(0,t.bF)(R,{modelValue:V.cronTabData.status,"onUpdate:modelValue":l[27]||(l[27]=e=>V.cronTabData.status=e),"active-color":"#13ce66","inactive-color":"#c3c3c3","active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])):((0,t.uX)(),(0,t.Wv)(H,{key:1,model:V.cronTabData,rules:V.rulescronTab,ref:"cronTabRef","label-width":"100px",class:"modern-form"},{default:(0,t.k6)(()=>[(0,t.bF)(X,{label:"任务名称",prop:"name"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.name,"onUpdate:modelValue":l[28]||(l[28]=e=>V.cronTabData.name=e),placeholder:"请输入任务名称"},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(I)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"时间配置",prop:"rule"},{default:(0,t.k6)(()=>[(0,t.bF)(N,{visible:V.cronVisibleYApiEdit,"onUpdate:visible":l[31]||(l[31]=e=>V.cronVisibleYApiEdit=e),placement:"bottom-start",width:650,trigger:"manual",teleported:!1,"append-to-body":!1,"popper-class":"cron-popover","popper-style":{minHeight:"320px"}},{reference:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.rule,"onUpdate:modelValue":l[29]||(l[29]=e=>V.cronTabData.rule=e),clearable:"",readonly:"",placeholder:"请选择时间配置",onClick:l[30]||(l[30]=(0,n.D$)(e=>D.openCronPopover(20),["stop"]))},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(j)]),_:1})]),_:1},8,["modelValue"])]),default:(0,t.k6)(()=>[(0,t.bF)(z,{runTimeStr:V.cronTabData.rule,onCloseTime:D.handleCloseTime,onRunTime:D.handleRunTime},null,8,["runTimeStr","onCloseTime","onRunTime"])]),_:1},8,["visible"])]),_:1}),(0,t.bF)(Q,{"content-position":"left"},{default:(0,t.k6)(()=>l[51]||(l[51]=[(0,t.eW)("YApi平台配置")])),_:1,__:[51]}),(0,t.bF)(X,{label:"平台地址",prop:"url"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.yapi.url,"onUpdate:modelValue":l[32]||(l[32]=e=>V.cronTabData.yapi.url=e),placeholder:"请输入YApi平台项目地址",clearable:""},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(B)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"平台TOKEN",prop:"token"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.yapi.token,"onUpdate:modelValue":l[33]||(l[33]=e=>V.cronTabData.yapi.token=e),placeholder:"请输入YApi平台项目token",clearable:"","show-password":""},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(G)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"平台项目ID",prop:"YApiId"},{default:(0,t.k6)(()=>[(0,t.bF)(O,{modelValue:V.cronTabData.yapi.YApiId,"onUpdate:modelValue":l[34]||(l[34]=e=>V.cronTabData.yapi.YApiId=e),placeholder:"请输入YApi平台项目id",clearable:""},{prefix:(0,t.k6)(()=>[(0,t.bF)($,null,{default:(0,t.k6)(()=>[(0,t.bF)(J)]),_:1})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(X,{label:"节点/模块",prop:"treenode"},{default:(0,t.k6)(()=>[(0,t.bF)(M,{modelValue:V.cronTabData.yapi.treenode,"onUpdate:modelValue":l[35]||(l[35]=e=>V.cronTabData.yapi.treenode=e),options:V.treeOptions,props:{label:"name",value:"id",checkStrictly:!0},onChange:D.removeCascaderAriaOwns,onVisibleChange:D.removeCascaderAriaOwns,onExpandChange:D.removeCascaderAriaOwns,clearable:"","collapse-tags":"",filterable:"",placeholder:"请选择节点/模块"},null,8,["modelValue","options","onChange","onVisibleChange","onExpandChange"])]),_:1}),(0,t.bF)(X,{label:"是否开启"},{default:(0,t.k6)(()=>[(0,t.bF)(R,{modelValue:V.cronTabData.status,"onUpdate:modelValue":l[36]||(l[36]=e=>V.cronTabData.status=e),"active-color":"#13ce66","inactive-color":"#c3c3c3","active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),(0,t.Lk)("div",C,[(0,t.bF)(x,{onClick:D.closeDialogCron,plain:""},{default:(0,t.k6)(()=>l[52]||(l[52]=[(0,t.eW)("取 消")])),_:1,__:[52]},8,["onClick"]),(0,t.bF)(x,{type:"primary",onClick:D.UpdateCron},{default:(0,t.k6)(()=>l[53]||(l[53]=[(0,t.eW)("提交修改")])),_:1,__:[53]},8,["onClick"])])]),_:1},8,["modelValue","before-close"])])}a(7588),a(61701);var V=a(57890),D=a(60782),w=a(57477),$={data(){return{currentTab:"first",treeOptions:[],cronVisible:!1,cronVisibleYApi:!1,cronVisibleEdit:!1,cronVisibleYApiEdit:!1,Refresh:w.Refresh,rulescronTab:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],env:[{required:!0,message:"请选择环境",trigger:"blur"}],plan:[{required:!0,message:"请选择执行计划",trigger:"blur"}]},cronList:null,editDialog:!1,addDialog:!1,cronTabData:{name:"",status:!0,plan:null,env:null,rule:"",yapi:{token:"",YApiId:null,treenode:null,format:"list",project:null,url:"http://121.37.2.117:8081"},type:""}}},components:{timerTaskCron:V.A,Edit:w.Edit,Delete:w.Delete,Refresh:w.Refresh,Notebook:w.Notebook,Timer:w.Timer,Link:w.Link,Key:w.Key,InfoFilled:w.InfoFilled},computed:{...(0,D.aH)(["pro","testEnvs","testPlans"])},methods:{openCronPopover(e){event&&event.stopPropagation(),this.cronVisible=!1,this.cronVisibleYApi=!1,this.cronVisibleEdit=!1,this.cronVisibleYApiEdit=!1,setTimeout(()=>{"first"===e?this.cronVisible=!0:"second"===e?this.cronVisibleYApi=!0:10===e?this.cronVisibleEdit=!0:20===e?this.cronVisibleYApiEdit=!0:console.error("未知的值:",e),this.$nextTick(()=>{document.querySelectorAll(".cron-popover").forEach(e=>{e&&(e.style.overflow="visible")})})},100),document.removeEventListener("click",this.handleDocumentClick),document.addEventListener("click",this.handleDocumentClick)},handleDocumentClick(e){e.target.closest(".el-time-panel")||e.target.closest(".el-picker-panel")||e.target.closest(".time-picker-popper")||e.target.closest(".cron-wrapper")||e.target.closest(".el-popover")||e.target.closest(".cron-popover")||this.closeRunTimeCron()},cronFun(e){console.log("cronFun调用",e)},handleCloseTime(e){e&&this.closeRunTimeCron()},handleRunTime(e){this.cronTabData.rule=e,this.$message({type:"success",message:"时间配置已设置",duration:2e3}),setTimeout(()=>{this.closeRunTimeCron()},500)},closeRunTimeCron(){document.removeEventListener("click",this.handleDocumentClick),setTimeout(()=>{this.cronVisible=!1,this.cronVisibleYApi=!1,this.cronVisibleEdit=!1,this.cronVisibleYApiEdit=!1},300)},runTimeCron(e){this.cronTabData.rule=e},async getAllCron(){const e=await this.$api.getCrons(this.pro.id);200===e.status&&(this.cronList=e.data)},delCron(e){this.$confirm("此操作将永久删除该定时任务, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const l=await this.$api.delCron(e);204===l.status&&(this.$message({type:"success",message:"删除成功!"}),this.getAllCron())}).catch(()=>{this.$message({type:"info",message:"已取消删除"})})},async switchCronStatus(e){const l=await this.$api.updateCron(e.id,e);200===l.status?1==e.status?this.$message({type:"success",message:"定时运行已开启",duration:1e3}):this.$message({type:"warning",message:"定时运行已关闭",duration:1e3}):this.$message({type:"error",message:"修改状态失败",duration:1e3})},closeDialogCron(){this.editDialog=!1,this.addDialog=!1,this.cronTabData={name:"",rule:"",status:!0,plan:null,env:null,yapi:{token:"",YApiId:null,treenode:null,format:"list",project:null,url:"http://121.37.2.117:8081"},type:null},this.$refs.cronTabRef.clearValidate()},async createCron(e){if("first"===e){delete this.cronTabData.yapi;const e={...this.cronTabData,type:10,project:this.pro.id},l=await this.$api.createCron(e);201===l.status&&(this.$message({type:"success",message:"定时任务添加成功",duration:1e3}),this.closeDialogCron(),this.getAllCron())}else if("second"===e){let e={...this.cronTabData.yapi};if(e.project=this.pro.id,e.treenode&&e.treenode.length>0){const l=e.treenode[e.treenode.length-1];e.treenode=l}const l={...this.cronTabData,project:this.pro.id,yapi:e,type:20};console.log(l);const a=await this.$api.createCron(l);201===a.status&&(this.$message({type:"success",message:"定时任务添加成功",duration:1e3}),this.closeDialogCron(),this.getAllCron())}else console.log("待完善")},showUpdateCronDlg(e){this.cronTabData=JSON.parse(JSON.stringify(e)),this.editDialog=!0},async UpdateCron(){let e={...this.cronTabData.yapi};if(e.project=this.pro.id,e.treenode&&e.treenode.length>0){const l=e.treenode[e.treenode.length-1];e.treenode=l}const l={...this.cronTabData,yapi:e};console.log(l);const a=await this.$api.updateCron(this.cronTabData.id,l);200===a.status&&(this.$message({type:"success",message:"修改成功",duration:1e3}),this.closeDialogCron(),this.getAllCron())},removeCascaderAriaOwns(){this.$nextTick(()=>{const e=document.querySelectorAll(".el-cascader-panel .el-cascader-node[aria-owns]");Array.from(e).map(e=>e.removeAttribute("aria-owns"))})},async allTree(){const e=await this.$api.getTreeNode();200===e.status&&(this.treeOptions=e.data.result)}},created(){this.getAllCron(),this.allTree()},beforeUnmount(){document.removeEventListener("click",this.handleDocumentClick)}},x=a(71241);const A=(0,x.A)($,[["render",y],["__scopeId","data-v-01997d07"]]);var E=A},57890:function(e,l,a){a.d(l,{A:function(){return p}});var t=a(56768),o=a(45130),n=a(24232);const i={key:0,class:"time-preview"};function r(e,l,a,r,s,d){const c=(0,t.g2)("el-radio-button"),u=(0,t.g2)("el-radio-group"),p=(0,t.g2)("el-col"),b=(0,t.g2)("el-time-picker"),m=(0,t.g2)("el-row"),h=(0,t.g2)("el-radio"),k=(0,t.g2)("el-button");return(0,t.uX)(),(0,t.CE)("div",{style:{display:"inline-block"},class:"cron-wrapper",onClick:l[18]||(l[18]=(0,o.D$)(()=>{},["stop"]))},[(0,t.Lk)("div",{class:"form",onClick:l[17]||(l[17]=(0,o.D$)(()=>{},["stop"]))},[(0,t.bF)(m,null,{default:(0,t.k6)(()=>[(0,t.bF)(p,{span:60},{default:(0,t.k6)(()=>[(0,t.bF)(u,{modelValue:s.type,"onUpdate:modelValue":l[0]||(l[0]=e=>s.type=e),size:"large",style:{"margin-bottom":"20px",width:"500px"},onClick:l[1]||(l[1]=(0,o.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[(0,t.bF)(c,{label:"每天"}),(0,t.bF)(c,{label:"每周"}),(0,t.bF)(c,{label:"每月"})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(p,{span:5,style:{"margin-left":"20px"}},{default:(0,t.k6)(()=>[(0,t.Lk)("div",{onClick:l[7]||(l[7]=(0,o.D$)(()=>{},["stop"]))},[(0,t.bF)(b,{modelValue:s.time,"onUpdate:modelValue":l[2]||(l[2]=e=>s.time=e),placeholder:"选择时间",size:"large",style:{width:"140px"},"value-format":"H:m","popper-options":{strategy:"fixed",modifiers:[{name:"eventListeners",options:{scroll:!1,resize:!1}}]},"popper-class":"time-picker-popper",teleported:!1,onClick:l[3]||(l[3]=(0,o.D$)(()=>{},["stop"])),onFocus:l[4]||(l[4]=(0,o.D$)(()=>{},["stop"])),onBlur:l[5]||(l[5]=(0,o.D$)(()=>{},["stop"])),onChange:l[6]||(l[6]=(0,o.D$)(()=>{},["stop"]))},null,8,["modelValue"])])]),_:1})]),_:1}),(0,t.bF)(m,null,{default:(0,t.k6)(()=>[s.weekRadio?((0,t.uX)(),(0,t.CE)("div",{key:0,class:"radio-container",onClick:l[11]||(l[11]=(0,o.D$)(()=>{},["stop"]))},[(0,t.bF)(u,{modelValue:s.week,"onUpdate:modelValue":l[9]||(l[9]=e=>s.week=e),onClick:l[10]||(l[10]=(0,o.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.weekOption,e=>((0,t.uX)(),(0,t.Wv)(h,{key:e.cron,label:e.cron,onClick:l[8]||(l[8]=(0,o.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[(0,t.eW)((0,n.v_)(e.value),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])])):(0,t.Q3)("",!0),s.monthRadio?((0,t.uX)(),(0,t.CE)("div",{key:1,class:"radio-container",onClick:l[15]||(l[15]=(0,o.D$)(()=>{},["stop"]))},[(0,t.bF)(u,{modelValue:s.month,"onUpdate:modelValue":l[13]||(l[13]=e=>s.month=e),onClick:l[14]||(l[14]=(0,o.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.monthOption,e=>((0,t.uX)(),(0,t.Wv)(h,{key:e.cron,label:e.cron,onClick:l[12]||(l[12]=(0,o.D$)(()=>{},["stop"]))},{default:(0,t.k6)(()=>[(0,t.eW)((0,n.v_)(e.value),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])])):(0,t.Q3)("",!0)]),_:1}),(0,t.Lk)("div",{class:"footer",onClick:l[16]||(l[16]=(0,o.D$)(()=>{},["stop"]))},[s.time?((0,t.uX)(),(0,t.CE)("p",i,[l[19]||(l[19]=(0,t.eW)(" 当前设置: ")),(0,t.Lk)("span",null,(0,n.v_)(d.timePreview),1)])):(0,t.Q3)("",!0),(0,t.bF)(k,{size:"default",onClick:(0,o.D$)(d.closeCron,["stop"])},{default:(0,t.k6)(()=>l[20]||(l[20]=[(0,t.eW)("取消")])),_:1,__:[20]},8,["onClick"]),(0,t.bF)(k,{size:"default",type:"primary",onClick:(0,o.D$)(d.handleSummit,["stop"]),disabled:!s.time},{default:(0,t.k6)(()=>l[21]||(l[21]=[(0,t.eW)("确定")])),_:1,__:[21]},8,["onClick","disabled"])])])])}a(44114),a(18111),a(20116);var s=a(90144),d={name:"timerTaskCron",props:{runTimeStr:(0,s.KR)(),timeCronStr:{type:String,default:""}},data(){return{visible:!1,weekRadio:!1,monthRadio:!1,value:"",type:"每天",week:1,month:1,time:"",weekOption:[{title:"星期一",value:"星期一",cron:1},{title:"星期二",value:"星期二",cron:2},{title:"星期三",value:"星期三",cron:3},{title:"星期四",value:"星期四",cron:4},{title:"星期五",value:"星期五",cron:5},{title:"星期六",value:"星期六",cron:6},{title:"星期日",value:"星期日",cron:7}],monthOption:[]}},computed:{timePreview(){if(!this.time)return"";let e=this.time;if("每天"===this.type)e=`每天 ${this.time}`;else if("每周"===this.type){const l=this.weekOption.find(e=>e.cron===this.week)?.value||"";e=`每周${l} ${this.time}`}else if("每月"===this.type){const l=this.month<10?`${this.month}  号`:`${this.month} 号`;e=`每月${l} ${this.time}`}return e}},watch:{type(e,l){"每天"===this.type&&(this.weekRadio=!1,this.monthRadio=!1),"每周"===this.type&&(this.weekRadio=!0,this.monthRadio=!1),"每月"===this.type&&(this.weekRadio=!1,this.monthRadio=!0)},week(e,l){},month(e,l){}},created(){this.initData(),this.runTimeStr&&this.parseRunTimeStr(this.runTimeStr)},mounted(){document.addEventListener("click",this.handleGlobalClick)},unmounted(){document.removeEventListener("click",this.handleGlobalClick)},methods:{handleGlobalClick(e){(e.target.closest(".el-time-panel")||e.target.closest(".el-picker-panel")||e.target.closest(".time-picker-popper"))&&e.stopPropagation()},initData(){let e=[];var l="";for(let a=1;a<32;a++)l=a<10?"  号":"号",e.push({title:a+l,value:a+l,cron:a});this.monthOption=e},parseRunTimeStr(e){if(e)try{const l=e.split(" ");l.length>=5&&("*"!==l[0]&&"*"!==l[1]&&(this.time=`${l[1]}:${l[0]}`),"*"!==l[2]&&"*"===l[3]&&"*"===l[4]?(this.type="每月",this.month=parseInt(l[2]),this.monthRadio=!0):"*"===l[2]&&"*"!==l[3]&&"*"===l[4]?(this.type="每周",this.week=parseInt(l[3]),this.weekRadio=!0):this.type="每天")}catch(l){console.error("解析cron表达式失败",l)}},closeCron(){this.$emit("closeTime",!0),this.type="每天",this.week=1,this.month=1,this.time=""},handleSummit(){if(!this.time)return void(this.$message?this.$message({message:"请选择时间!",type:"warning"}):window.ElMessage?window.ElMessage.warning("请选择时间!"):alert("请选择时间!"));let e,l=this.time.split(":").reverse();"每天"===this.type&&(e=l.join(" ")+" * * *"),"每月"===this.type&&(e=l.join(" ")+" "+this.month+" * *"),"每周"===this.type&&(e=l.join(" ")+" * "+this.week+" *"),this.$emit("runTime",e),this.$emit("closeTime",!0),this.type="每天",this.week=1,this.month=1,this.time=""}}},c=a(71241);const u=(0,c.A)(d,[["render",r],["__scopeId","data-v-021b989a"]]);var p=u}}]);
//# sourceMappingURL=403.711cb974.js.map