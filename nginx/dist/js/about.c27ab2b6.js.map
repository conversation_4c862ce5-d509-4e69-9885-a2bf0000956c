{"version": 3, "file": "js/about.c27ab2b6.js", "mappings": "6LACMA,MAAM,Q,GACLA,MAAM,Y,GACNA,MAAM,a,0IAFZC,EAAAA,EAAAA,IAQM,MARNC,EAQM,EAPLC,EAAAA,EAAAA,IAAiD,MAAjDC,EAAiD,EAA3BC,EAAAA,EAAAA,IAAqBC,MAC3CH,EAAAA,EAAAA,IAKM,MALNI,EAKM,EAHLF,EAAAA,EAAAA,IAAaG,IAEbH,EAAAA,EAAAA,IAAkFI,EAAA,CAAzET,MAAM,OAAO,aAAW,a,kBAAY,IAA2B,EAA3BK,EAAAA,EAAAA,IAA2BK,K,kCCLrEV,MAAM,Y,GAEHA,MAAM,oB,GAENA,MAAM,Y,GAaTA,MAAM,a,GAMKA,MAAM,kB,GAYNA,MAAM,kB,GAcNA,MAAM,kB,GAYNA,MAAM,kB,GAYNA,MAAM,kB,gbAzEtBG,EAAAA,EAAAA,IAeM,MAfND,EAeM,EAdLG,EAAAA,EAAAA,IAacM,EAAA,CAbDC,QAAQ,QAAS,iBAAe,EAAQC,UAASC,EAAAC,cAAeC,MAAA,wF,CAOjEC,UAAQC,EAAAA,EAAAA,IAClB,IAGmB,EAHnBb,EAAAA,EAAAA,IAGmBc,EAAA,M,iBAFlB,IAA0D,EAA1Dd,EAAAA,EAAAA,IAA0De,EAAA,CAAxCC,QAAQ,UAAQ,C,iBAAC,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,cACnCjB,EAAAA,EAAAA,IAA0De,EAAA,CAAxCC,QAAQ,UAAQ,C,iBAAC,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,wCATrC,IAKO,EALPnB,EAAAA,EAAAA,IAKO,OALPC,EAKO,EAJFD,EAAAA,EAAAA,IAA4C,WAAzCE,EAAAA,EAAAA,IAAqCkB,EAAA,CAA9BC,KAAMV,EAAAW,OAAQzB,MAAM,U,oBACnCG,EAAAA,EAAAA,IAEY,OAFZI,EAEY,E,iBAFcO,EAAAY,UAAW,IACpC,IAAArB,EAAAA,EAAAA,IAAyDsB,EAAA,CAAhD3B,MAAM,kBAAgB,C,iBAAC,IAAe,EAAfK,EAAAA,EAAAA,IAAeuB,K,qCAYlDzB,EAAAA,EAAAA,IAoEM,MApEN0B,EAoEM,EAnELxB,EAAAA,EAAAA,IAkEeyB,EAAA,CAlEDC,OAAO,sBAAoB,C,iBACxC,IAgEU,EAhEV1B,EAAAA,EAAAA,IAgEU2B,EAAA,CAhEA,iBAAgBC,EAAAC,OAAOC,KAAMC,OAAA,GAAO,mBAAiB,UAAU,aAAW,OACrF,oBAAkB,OAAOpC,MAAM,wBAAyB,kBAAiB,CAAC,OAAQ,QAAS,QAAS,YAAa,Y,kBAC3G,IAWc,EAXdK,EAAAA,EAAAA,IAWcgC,EAAA,CAXDC,MAAM,aAAW,CACjBC,OAAKrB,EAAAA,EAAAA,IACd,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHNqC,EAGM,EAFJnC,EAAAA,EAAAA,IAAiCsB,EAAA,M,iBAAxB,IAAc,EAAdtB,EAAAA,EAAAA,IAAcoC,K,mBACvBtC,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGd,IAGe,EAHfE,EAAAA,EAAAA,IAGeqC,EAAA,CAHAJ,MAAO,YAAU,C,iBAC9B,IAAiC,EAAjCjC,EAAAA,EAAAA,IAAiCsB,EAAA,M,iBAAxB,IAAc,EAAdtB,EAAAA,EAAAA,IAAcoC,K,mBACvBtC,EAAAA,EAAAA,IAAiB,YAAX,QAAI,M,qBAGewC,EAAAC,mB,WAAhCC,EAAAA,EAAAA,IAagBR,EAAA,C,MAbHC,MAAM,Q,CACHC,OAAKrB,EAAAA,EAAAA,IACd,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHN2C,EAGM,EAFJzC,EAAAA,EAAAA,IAA2BsB,EAAA,M,iBAAlB,IAAQ,EAARtB,EAAAA,EAAAA,IAAQ0C,K,mBACjB5C,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGiB,IAAqB,G,aAAtDF,EAAAA,EAAAA,IAKW+C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IALqCN,EAAAO,MAARC,K,WAAxCN,EAAAA,EAAAA,IAKWH,EAAA,CALIJ,MAAOa,EAAKhB,KAA6BiB,IAAKD,EAAKhB,M,kBACrE,IAEe,EAFf9B,EAAAA,EAAAA,IAEesB,EAAA,CAFL3B,OAAKqD,EAAAA,EAAAA,IAAA,gBAA2C,eAAvBF,EAAKG,iB,kBACjC,IAAsC,G,WAAtCT,EAAAA,EAAAA,KAAsCU,EAAAA,EAAAA,IAAtBJ,EAAKG,mB,sBAE5BnD,EAAAA,EAAAA,IAA4B,aAAAqD,EAAAA,EAAAA,IAAnBL,EAAKM,MAAI,K,sDAGmBd,EAAAe,sB,WAAjCb,EAAAA,EAAAA,IAWYR,EAAA,C,MAXCC,MAAM,S,CACPC,OAAKrB,EAAAA,EAAAA,IACd,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHNwD,EAGM,EAFJtD,EAAAA,EAAAA,IAA8BsB,EAAA,M,iBAArB,IAAW,EAAXtB,EAAAA,EAAAA,IAAWuD,K,mBACpBzD,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGkB,IAAsB,G,aAAvDF,EAAAA,EAAAA,IAGU+C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAHsCN,EAAAkB,OAARV,K,WAAxCN,EAAAA,EAAAA,IAGUH,EAAA,CAHKJ,MAAOa,EAAKhB,KAA8BiB,IAAKD,EAAKhB,M,kBACvE,IAAyD,EAAzD9B,EAAAA,EAAAA,IAAyDsB,EAAA,M,iBAAhD,IAAsC,G,WAAtCkB,EAAAA,EAAAA,KAAsCU,EAAAA,EAAAA,IAAtBJ,EAAKG,mB,YAC9BnD,EAAAA,EAAAA,IAA4B,aAAAqD,EAAAA,EAAAA,IAAnBL,EAAKM,MAAI,K,sDAGiBd,EAAAe,sB,WAAjCb,EAAAA,EAAAA,IAWcR,EAAA,C,MAXDC,MAAM,S,CACLC,OAAKrB,EAAAA,EAAAA,IACd,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHN2D,EAGM,EAFJzD,EAAAA,EAAAA,IAA8BsB,EAAA,M,iBAArB,IAAW,EAAXtB,EAAAA,EAAAA,IAAW0D,K,mBACpB5D,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGkB,IAAsB,G,aAAvDF,EAAAA,EAAAA,IAGU+C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAHsCN,EAAAqB,OAARb,K,WAAxCN,EAAAA,EAAAA,IAGUH,EAAA,CAHKJ,MAAOa,EAAKhB,KAA8BiB,IAAKD,EAAKhB,M,kBACvE,IAAyD,EAAzD9B,EAAAA,EAAAA,IAAyDsB,EAAA,M,iBAAhD,IAAsC,G,WAAtCkB,EAAAA,EAAAA,KAAsCU,EAAAA,EAAAA,IAAtBJ,EAAKG,mB,YAC9BnD,EAAAA,EAAAA,IAA4B,aAAAqD,EAAAA,EAAAA,IAAnBL,EAAKM,MAAI,K,uDAGhBpD,EAAAA,EAAAA,IAWegC,EAAA,CAXFC,MAAM,WAAS,CACdC,OAAKrB,EAAAA,EAAAA,IACd,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHN8D,EAGM,EAFJ5D,EAAAA,EAAAA,IAA2BsB,EAAA,M,iBAAlB,IAAQ,EAARtB,EAAAA,EAAAA,IAAQ6D,K,mBACjB/D,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,iBAGmB,IAAuB,G,aAAxDF,EAAAA,EAAAA,IAGe+C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAHiCN,EAAAwB,QAARhB,K,WAAxCN,EAAAA,EAAAA,IAGeH,EAAA,CAHAJ,MAAOa,EAAKhB,KAA+BiB,IAAKD,EAAKhB,M,kBAClE,IAAyD,EAAzD9B,EAAAA,EAAAA,IAAyDsB,EAAA,M,iBAAhD,IAAsC,G,WAAtCkB,EAAAA,EAAAA,KAAsCU,EAAAA,EAAAA,IAAtBJ,EAAKG,mB,YAC9BnD,EAAAA,EAAAA,IAA4B,aAAAqD,EAAAA,EAAAA,IAAnBL,EAAKM,MAAI,K,gJAkB9B,MAAMW,EAAY,CAChB,CACAX,KAAM,OACNtB,KAAM,iBACNmB,cAAe,aAEhB,CACCG,KAAM,OACNtB,KAAM,YACNmB,cAAe,kBAEf,CACAG,KAAM,OACNtB,KAAM,gBACNmB,cAAe,iBAEhB,CACCG,KAAM,OACNtB,KAAM,WACNmB,cAAe,QAEhB,CACCG,KAAM,OACNtB,KAAM,WACNmB,cAAe,SAEhB,CACCG,KAAM,QACNtB,KAAM,QACNmB,cAAe,aAEhB,CACCG,KAAM,OACNtB,KAAM,WACNmB,cAAe,iBAIXe,EAAc,CAChB,CACFZ,KAAM,OACNtB,KAAM,cACNmB,cAAe,aAEf,CACAG,KAAM,OACNtB,KAAM,SACNmB,cAAe,QAEf,CACAG,KAAM,OACNtB,KAAM,cACNmB,cAAe,aAIXgB,EAAU,CACd,CACAb,KAAM,OACNtB,KAAM,mBACNmB,cAAe,OAEf,CACAG,KAAM,OACNtB,KAAM,qBACNmB,cAAe,aAEf,CACAG,KAAM,OACNtB,KAAM,UACNmB,cAAe,UAEf,CACAG,KAAM,OACNtB,KAAM,WACNmB,cAAe,aAEf,CACAG,KAAM,OACNtB,KAAM,oBACNmB,cAAe,cAEf,CACAG,KAAM,QACNtB,KAAM,uBACNmB,cAAe,cAIXiB,EAAU,GAEhB,OACEC,WAAY,CACVC,KAAI,KACJC,WAAU,aAAEC,KAAI,OAAEC,QAAO,UAAEC,QAAO,UAAEC,KAAI,OAAEC,YAAW,cACrDC,UAAS,YAAEC,eAAc,iBAAEC,cAAa,gBAAEC,KAAI,OAAEC,MAAK,QACrDC,UAAS,YAAEC,aAAY,eAAEC,UAAS,YAAEC,KAAI,OAAEC,SAAQ,WAClDC,IAAG,MAAEC,UAAS,YAAEC,OAAM,SAAEC,UAAS,YAAEC,WAAUA,EAAAA,YAEhDC,IAAAA,GACC,MAAO,CACN7C,MAAOkB,EACJP,OAAQS,EACRN,OAAQO,EACRJ,QAAQE,EACRzB,kBAAkB,EAClBc,qBAAqB,EACrBsC,QAAQ,CAAC,OAAQ,QAAS,SAE/B,EACAC,SAAU,KACJC,EAAAA,EAAAA,IAAS,CACVC,KAAMC,GAASA,EAAMD,OAEzBzE,QAAAA,GACC,OAAO2E,OAAOC,eAAeC,QAAQ,WACtC,EACE9E,MAAAA,GACA,OAAO4E,OAAOC,eAAeC,QAAQ,SACrC,GAEHC,QAAS,KACFC,EAAAA,EAAAA,IAAa,CAAC,aAAa,YACjC1F,aAAAA,CAAc2F,GACD,WAARA,GACHC,KAAKC,QAAQC,KAAK,CAAEpD,KAAM,eAC1B4C,OAAOC,eAAeQ,WAAW,gBAChCH,KAAKI,aACLJ,KAAKR,KAAKa,QAAQ7D,IACbwD,KAAKM,QAAQ9D,EAAKhB,SAGR,WAARuE,IACPC,KAAKI,aACFJ,KAAKR,KAAKa,QAAQ7D,IAChBwD,KAAKM,QAAQ9D,EAAKhB,QACxBkE,OAAOC,eAAeQ,WAAW,SACjCT,OAAOC,eAAeQ,WAAW,YACjCT,OAAOC,eAAeQ,WAAW,gBACjCT,OAAOC,eAAeQ,WAAW,UACjCH,KAAKC,QAAQC,KAAK,CAAEpD,KAAM,UAE5B,I,WC3OF,MAAMyD,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,Q,SCRMlH,MAAM,Q,GAELA,MAAM,W,GAmBNA,MAAM,c,GAkBFA,MAAM,a,GAEJA,MAAM,Y,GAGTA,MAAM,a,GAILA,MAAM,a,GAEJA,MAAM,Y,GAGTA,MAAM,a,GAKPA,MAAM,iB,uVA1DdG,EAAAA,EAAAA,IAgCM,MAhCND,EAgCM,EA9BLC,EAAAA,EAAAA,IAgBM,MAhBNC,EAgBM,EAfLC,EAAAA,EAAAA,IAceyB,EAAA,M,iBAbR,IAAmB,G,aAAzB7B,EAAAA,EAAAA,IAYO+C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAZahB,EAAAkE,KAAPgB,K,WAAblH,EAAAA,EAAAA,IAYO,QAZoBmD,IAAK+D,EAAI1D,KAAMzD,MAAM,Y,EAC/CK,EAAAA,EAAAA,IAUS+G,EAAA,CATPC,SAA0B,IAAhBpF,EAAAkE,KAAKmB,OACfC,QAAKC,GAAE1G,EAAA2G,UAAUN,EAAIhF,MACrBuF,QAAKF,GAAEvF,EAAA2E,QAAQC,KAAKM,EAAIhF,MACxBwF,KAAMR,EAAIhF,OAASF,EAAAC,OAAOC,KAAO,UAAY,GAC7CyF,OAAQT,EAAIhF,OAASF,EAAAC,OAAOC,KAAO,OAAS,QAC7C0F,KAAK,QACL7H,MAAM,e,kBAEN,IAAc,E,iBAAXmH,EAAI1D,MAAI,K,iFAOftD,EAAAA,EAAAA,IAUM,MAVNI,EAUM,EATLF,EAAAA,EAAAA,IAA0GyH,EAAA,CAA9FJ,QAAO5G,EAAAiH,YAAaJ,KAAK,UAAUE,KAAK,QAAQ7G,MAAA,yB,kBAA4B,IAAMM,EAAA,KAAAA,EAAA,K,QAAN,a,4BACrFjB,EAAAA,EAAAA,IAEY2H,EAAA,C,WAFQlH,EAAAmH,I,qCAAAnH,EAAAmH,IAAGT,GAAEU,YAAY,OAAOlH,MAAA,gBAAsB,eAAa,Q,kBAClE,IAAwB,G,aAAnCf,EAAAA,EAAAA,IAAyF+C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAA/DhB,EAAAkG,SAARhF,K,WAAlBN,EAAAA,EAAAA,IAAyFuF,EAAA,CAApDhF,IAAKD,EAAKkF,GAAKC,MAAOnF,EAAKM,KAAO8E,MAAOpF,EAAKkF,I,2DAEtEvH,EAAAmH,M,WAAlBpF,EAAAA,EAAAA,IAIa2F,EAAA,C,MAJUZ,OAAO,OAAOa,QAAQ,SAASC,UAAU,U,kBAC/D,IAEY,EAFZrI,EAAAA,EAAAA,IAEYyH,EAAA,CAFD9G,MAAA,sBAA0B0G,QAAO5G,EAAA6H,c,kBAC3C,IAA2B,EAA3BtI,EAAAA,EAAAA,IAA2BsB,EAAA,M,iBAAlB,IAAQ,EAARtB,EAAAA,EAAAA,IAAQuI,K,0DAMrBvI,EAAAA,EAAAA,IA6BYwI,EAAA,C,WA7BQlG,EAAAmG,Q,qCAAAnG,EAAAmG,QAAOtB,GAAEjF,MAAM,OAAOvC,MAAM,c,CAuBpC+I,QAAM7H,EAAAA,EAAAA,IAChB,IAGO,EAHPf,EAAAA,EAAAA,IAGO,OAHP6I,EAGO,EAFN3I,EAAAA,EAAAA,IAAwEyH,EAAA,CAA5DJ,QAAKpG,EAAA,KAAAA,EAAA,GAAAkG,GAAE1G,EAAAmI,QAAQtG,EAAAuG,UAAUvB,KAAK,UAAUwB,MAAA,I,kBAAM,IAAE7H,EAAA,KAAAA,EAAA,K,QAAF,S,cAC1DjB,EAAAA,EAAAA,IAAkDyH,EAAA,CAAtCJ,QAAKpG,EAAA,KAAAA,EAAA,GAAAkG,GAAE7E,EAAAmG,SAAU,I,kBAAO,IAAExH,EAAA,KAAAA,EAAA,K,QAAF,S,kCAzBtC,IAqBe,EArBfjB,EAAAA,EAAAA,IAqBeyB,EAAA,CArBDC,OAAO,SAAO,C,iBAC3B,IAmBkB,EAnBlB1B,EAAAA,EAAAA,IAmBkB+I,EAAA,CAnBDC,OAAA,GAAQC,OAAQ,EAAGtJ,MAAM,mBAAoB,cAAa,K,kBACpD,IAAqD,G,aAA3EC,EAAAA,EAAAA,IAQuB+C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IARsBN,EAAAuG,QAAQK,sBAAqB,CAA5ChB,EAAOnF,M,WAArCP,EAAAA,EAAAA,IAQuB2G,EAAA,CARsDpG,IAAG,SAAWA,K,CAC/EkF,OAAKpH,EAAAA,EAAAA,IACf,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHN0B,EAGM,EAFLxB,EAAAA,EAAAA,IAAqC+G,EAAA,CAA7BO,KAAK,WAAS,C,iBAAC,IAAKrG,EAAA,KAAAA,EAAA,K,QAAL,Y,cACvBnB,EAAAA,EAAAA,IAAuC,OAAvCqC,GAAuCgB,EAAAA,EAAAA,IAAbJ,GAAG,O,iBAG/B,IAAwC,EAAxCjD,EAAAA,EAAAA,IAAwC,MAAxC2C,GAAwCU,EAAAA,EAAAA,IAAd+E,GAAK,K,kCAEhCtI,EAAAA,EAAAA,IAQuB+C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IARsBN,EAAAuG,QAAQO,gBAAe,CAAtClB,EAAOnF,M,WAArCP,EAAAA,EAAAA,IAQuB2G,EAAA,CARgDpG,IAAG,UAAYA,K,CAC1EkF,OAAKpH,EAAAA,EAAAA,IACf,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHNwD,EAGM,EAFLtD,EAAAA,EAAAA,IAAsC+G,EAAA,CAA9BO,KAAK,WAAS,C,iBAAC,IAAMrG,EAAA,KAAAA,EAAA,K,QAAN,a,cACvBnB,EAAAA,EAAAA,IAAuC,OAAvC2D,GAAuCN,EAAAA,EAAAA,IAAbJ,GAAG,O,iBAG/B,IAAwC,EAAxCjD,EAAAA,EAAAA,IAAwC,MAAxC8D,GAAwCT,EAAAA,EAAAA,IAAd+E,GAAK,K,iEAiBpC,OACC/D,WAAY,CACXkF,KAAIA,EAAAA,MAEL3D,IAAAA,GACC,MAAO,CACN+C,SAAS,EACTa,aAAc,GACdT,QAAS,CAAC,EAEZ,EACAjD,SAAU,KACJC,EAAAA,EAAAA,IAAS,CACVC,KAAMC,GAASA,EAAMD,KACrByD,MAAOxD,GAASA,EAAMwD,MACtBzB,SAAU/B,GAASA,EAAM+B,SACzB0B,IAAKzD,GAASA,EAAMyD,MAExB5B,IAAK,CACJ6B,GAAAA,GACC,OAAOnD,KAAKiD,KACb,EACAG,GAAAA,CAAIC,GACHrD,KAAKsD,UAAUD,EAChB,IAGFxD,QAAS,KACLC,EAAAA,EAAAA,IAAa,CAAC,UAAW,YAAa,kBAEzC,kBAAMkC,GAEL,MAAMuB,QAAiBvD,KAAKwD,KAAKC,WAAWzD,KAAKiD,MAAMjD,KAAKkD,IAAIxB,IACxC,MAApB6B,EAASG,SACZ1D,KAAKuC,QAAUgB,EAASnE,MAEzBY,KAAKmC,SAAU,CAChB,EAEArB,SAAAA,CAAUtF,GACTwE,KAAKM,QAAQ9E,GAETwE,KAAKzE,OAAOC,OAASA,GACxBwE,KAAKC,QAAQC,KAAKF,KAAKR,KAAKQ,KAAKR,KAAKmB,OAAS,GAAGnF,KAEpD,EAEA4F,WAAAA,GACCpB,KAAKR,KAAKa,QAAQ7D,IACbwD,KAAKzE,OAAOC,OAASgB,EAAKhB,MAC7BwE,KAAKM,QAAQ9D,EAAKhB,OAGrB,EAEA8G,OAAAA,CAAQC,GACPvC,KAAKmC,SAAU,EACfnC,KAAK2D,cAAcpB,GACnBvC,KAAKC,QAAQC,KAAK,CAAEpD,KAAM,WAC3B,IC3HF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,KAEpE,QJOA,GACCA,KAAM,OACNe,WAAY,CACX+F,SAAQ,EACRC,KAAIA,GAELhE,QAAS,KACLiE,EAAAA,EAAAA,IAAW,CAAC,cAAe,cAAe,aAAc,gBAE5DC,OAAAA,GAGC/D,KAAKgE,aACLhE,KAAKiE,YACN,GKvBD,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O,4wRCPO7K,MAAM,a,GAyBMA,MAAM,iB,GAEFA,MAAM,iB,GAuCVA,MAAM,iB,GAEFA,MAAM,iB,kPArE5B6C,EAAAA,EAAAA,IAiFYiI,EAAA,M,iBAhFX,IA+EM,EA/EN3K,EAAAA,EAAAA,IA+EM,MA/END,EA+EM,C,eA9ELC,EAAAA,EAAAA,IAAmE,OAA9DH,MAAM,YAAU,EAACG,EAAAA,EAAAA,IAAuC,OAAlC4K,IAAAC,M,KACtB3K,EAAAA,EAAAA,IA4EU4K,EAAA,C,WA5EQtI,EAAAuI,W,qCAAAvI,EAAAuI,WAAU1D,GAAExH,MAAM,c,kBAClC,IAkCc,EAlCdK,EAAAA,EAAAA,IAkCc8K,EAAA,CAlCD7C,MAAM,MAAM7E,KAAK,S,kBAC5B,IAgCU,EAhCVpD,EAAAA,EAAAA,IAgCU+K,EAAA,CAhCDC,IAAI,WAAWrL,MAAM,aAAcsL,MAAO3I,EAAA4I,UAAYC,MAAO7I,EAAA8I,Y,kBACpE,IAQe,EARfpL,EAAAA,EAAAA,IAQeqL,EAAA,CARDC,KAAK,WAAW3L,MAAM,oB,kBAClC,IAMY,EANZK,EAAAA,EAAAA,IAMYuL,EAAA,CALV/D,KAAK,Q,WACIlF,EAAA4I,UAAU7J,S,qCAAViB,EAAA4I,UAAU7J,SAAQ8F,GAC3B,cAAY,OACZU,YAAY,QACZlI,MAAM,gB,gCAGVK,EAAAA,EAAAA,IAUeqL,EAAA,CAVDC,KAAK,WAAW3L,MAAM,oB,kBAClC,IAQY,EARZK,EAAAA,EAAAA,IAQYuL,EAAA,CAPVjE,KAAK,WACLE,KAAK,Q,WACIlF,EAAA4I,UAAUM,S,qCAAVlJ,EAAA4I,UAAUM,SAAQrE,GAC3BU,YAAY,QACZ,cAAY,OACZ,mBACAlI,MAAM,gB,gCAGVG,EAAAA,EAAAA,IAMM,MANNC,EAMM,EALFC,EAAAA,EAAAA,IAA0EyL,EAAA,C,WAApDnJ,EAAA0H,O,qCAAA1H,EAAA0H,OAAM7C,GAAExH,MAAM,qB,kBAAoB,IAAIsB,EAAA,KAAAA,EAAA,K,QAAJ,W,+BACxDnB,EAAAA,EAAAA,IAGM,MAHNI,EAGM,C,uBAHqB,aAEvBJ,EAAAA,EAAAA,IAAuE,QAAhEuH,QAAKpG,EAAA,KAAAA,EAAA,GAAAkG,GAAE1G,EAAAiL,cAAcpJ,EAAAuI,aAAalL,MAAM,eAAc,YAIrEK,EAAAA,EAAAA,IAEeqL,EAAA,M,iBADb,IAAuF,EAAvFrL,EAAAA,EAAAA,IAAuFyH,EAAA,CAA5ED,KAAK,QAAQF,KAAK,UAAU3H,MAAM,YAAa0H,QAAO5G,EAAAkL,O,kBAAO,IAAG1K,EAAA,MAAAA,EAAA,M,QAAH,U,yEAI9EjB,EAAAA,EAAAA,IAuCc8K,EAAA,CAvCD7C,MAAM,MAAM7E,KAAK,U,kBAC5B,IAqCU,EArCVpD,EAAAA,EAAAA,IAqCU+K,EAAA,CArCDpL,MAAM,aAAcsL,MAAO3I,EAAAsJ,Y,kBAClC,IAWe,EAXf5L,EAAAA,EAAAA,IAWeqL,EAAA,CAXD1L,MAAM,oBAAkB,C,iBACpC,IASY,EATZK,EAAAA,EAAAA,IASYuL,EAAA,CARVM,UAAA,GACCC,SAAUxJ,EAAAyJ,cACVC,QAAOvL,EAAAwL,eACRzE,KAAK,Q,WACIlF,EAAAsJ,WAAWvK,S,qCAAXiB,EAAAsJ,WAAWvK,SAAQ8F,GAC5B,cAAY,OACZU,YAAY,QACZlI,MAAM,gB,qDAGVK,EAAAA,EAAAA,IAaeqL,EAAA,CAbD1L,MAAM,oBAAkB,C,iBACpC,IAWY,EAXZK,EAAAA,EAAAA,IAWYuL,EAAA,CAVVM,UAAA,GACCC,SAAUxJ,EAAAyJ,cACVC,QAAOvL,EAAAwL,eACR3E,KAAK,WACLE,KAAK,Q,WACIlF,EAAAsJ,WAAWJ,S,qCAAXlJ,EAAAsJ,WAAWJ,SAAQrE,GAC5BU,YAAY,QACZ,cAAY,OACZ,mBACAlI,MAAM,gB,qDAGVG,EAAAA,EAAAA,IAMM,MANN0B,EAMM,EALFxB,EAAAA,EAAAA,IAA0EyL,EAAA,C,WAApDnJ,EAAA0H,O,qCAAA1H,EAAA0H,OAAM7C,GAAExH,MAAM,qB,kBAAoB,IAAIsB,EAAA,MAAAA,EAAA,M,QAAJ,W,gCACxDnB,EAAAA,EAAAA,IAGM,MAHNqC,EAGM,C,uBAHqB,aAEvBrC,EAAAA,EAAAA,IAAuE,QAAhEuH,QAAKpG,EAAA,KAAAA,EAAA,GAAAkG,GAAE1G,EAAAiL,cAAcpJ,EAAAuI,aAAalL,MAAM,eAAc,YAGrEK,EAAAA,EAAAA,IAEeqL,EAAA,M,iBADb,IAA6F,EAA7FrL,EAAAA,EAAAA,IAA6FyH,EAAA,CAAlFD,KAAK,QAAQF,KAAK,UAAU3H,MAAM,YAAa0H,QAAO5G,EAAAyL,a,kBAAa,IAAGjL,EAAA,MAAAA,EAAA,M,QAAH,U,mPCzC9F,MAAMkL,GAAMnB,EAAAA,EAAAA,IAAI,MAChB,IAAIoB,EAAQ,MACZC,EAAAA,EAAAA,IAAU,KACNC,MAOJ,MAAMA,EAAkBA,KACpBH,EAAIjE,MAAMqE,MAAQJ,EAAIjE,MAAMsE,WAAWC,YACvCN,EAAIjE,MAAMxG,OAASyK,EAAIjE,MAAMsE,WAAWE,aAGxC,IAAIC,EAAY,UACZC,EAAS,UACTC,EAAQ,UACRC,EAAe,UACfC,EAAc,UACdC,EAAS,UAGTC,EAAO,IAAIC,IAAAA,cAAkB,CAE7BC,QAAShB,EAAIjE,MACbkF,YAAY,EACZC,KAAM,MAKNC,EAAO,IAAIJ,IAAAA,aAAiB,CAC5BK,MAAON,EACPV,MAAO,IACP7K,OAAQ,IACR8L,MAAO,QACPC,MAAM,EACNC,aAAc,GACdC,OAAQ,KAIZ,IAAIT,IAAAA,aAAiB,CACjBK,MAAOD,EACPf,MAAO,IACP7K,OAAQ,IACR8L,MAAOZ,EACPa,MAAM,EACNC,aAAc,GACdC,OAAQ,IACRC,UAAW,CAAEC,GAAI,GAAIC,GAAI,MAI7B,IAAIC,EAAM,IAAIb,IAAAA,aAAiB,CAC3BK,MAAOD,EACP5L,OAAQ,GACR6K,MAAO,GACPoB,OAAQ,GACRF,MAAM,EACND,MAAOT,EACPa,UAAW,CAAEI,GAAI,IAAKF,GAAI,IAC1BJ,aAAc,MAGlB,IAAIR,IAAAA,aAAiB,CACjBK,MAAOQ,EACPrM,OAAQ,IACR6K,MAAO,GACPoB,OAAQ,GACRF,MAAM,EACND,MAAOX,EACPe,UAAW,CAAEE,EAAG,KAChBJ,aAAc,MAIlB,IAAIO,EAAa,IAAIf,IAAAA,OAAW,CAC5BK,MAAOQ,EACPjM,KAAM,CAAC,CAAEkM,GAAI,IAAM,CAAEA,EAAG,KACxBL,OAAQ,GACRH,MAAOV,EACPc,UAAW,CAAEE,EAAG,OAGpBG,EAAWC,KAAK,CACZV,MAAOT,EACPa,UAAW,CAAEE,EAAG,OAIpB,IAAIZ,IAAAA,aAAiB,CACjBK,MAAOU,EACPvM,OAAQ,GACR6K,MAAO,GACPqB,UAAW,CAAEI,GAAI,EAAGF,EAAG,IACvBL,MAAM,EACND,MAAOR,EACPW,OAAQ,KAGZ,IAAIT,IAAAA,aAAiB,CACjBK,MAAOU,EACPvM,OAAQ,GACR6K,MAAO,EACPqB,UAAW,CAAEI,EAAG,GAAIF,EAAG,IACvBL,MAAM,EACND,MAAOZ,EACPe,OAAQ,KAGZI,EAAII,UAAU,CACVP,UAAW,CAAEI,EAAG,IAAKF,GAAI,IACzBM,OAAQ,CAAEN,EAAGZ,IAAAA,IAAW,KAI5B,IAAImB,EAAM,IAAInB,IAAAA,aAAiB,CAC3BK,MAAOD,EACP5L,OAAQ,IACR6K,MAAO,GACPoB,OAAQ,GACRF,MAAM,EACND,MAAOX,EACPe,UAAW,CAAEI,GAAI,GAAIF,EAAG,KACxBJ,aAAc,MAIdY,EAAa,IAAIpB,IAAAA,OAAW,CAC5BK,MAAOc,EACPvM,KAAM,CAAC,CAAEkM,GAAI,IAAM,CAAEA,EAAG,KACxBL,OAAQ,GACRH,MAAOV,EACPc,UAAW,CAAEE,EAAG,OAGpBQ,EAAWJ,KAAK,CACZV,MAAOT,EACPa,UAAW,CAAEE,EAAG,OAIpB,IAAIZ,IAAAA,aAAiB,CACjBK,MAAOc,EACP9B,MAAO,GACP7K,OAAQ,GACRiM,OAAQ,GACRF,MAAM,EACND,MAAOR,EACPY,UAAW,CAAEI,GAAI,GAAIF,EAAG,KACxBJ,aAAc,KAGlBW,EAAIF,UAAU,CACVP,UAAW,CAAEI,EAAG,GAAIF,EAAG,KACvBM,OAAQ,CAAEN,EAAGZ,IAAAA,IAAW,KAK5B,IAAIqB,EAAO,IAAIrB,IAAAA,aAAiB,CAC5BK,MAAOD,EACPf,MAAO,IACP7K,OAAQ,IACR8M,MAAO,GACPd,aAAc,GACdC,OAAQ,GACRH,MAAOX,EACPY,MAAM,EACNG,UAAW,CAAEE,GAAI,OAIjBW,EAAS,IAAIvB,IAAAA,aAAiB,CAC9BK,MAAOgB,EACPhC,MAAO,IACP7K,OAAQ,IACRgM,aAAc,GACdF,MAAOb,EACPc,MAAM,EACNiB,UAAU,EACVd,UAAW,CAAEC,EAAG,MAIpB,IAAIX,IAAAA,MAAU,CACVK,MAAOkB,EACPlC,MAAO,GACP7K,OAAQ,EACRiM,OAAQ,GACRC,UAAW,CAAEI,EAAG,GAAIF,GAAI,GAAID,EAAG,IAC/BL,MAAO,QACPkB,UAAU,IAId,IAAIC,EAAM,IAAIzB,IAAAA,aAAiB,CAC3BK,MAAOgB,EACPhC,MAAO,GACP7K,OAAQ,GACRgM,aAAc,GACdC,OAAQ,GACRH,MAAOZ,EACPa,MAAM,EACNG,UAAW,CAAEI,GAAI,OAGrBW,EAAIT,KAAK,CACLN,UAAW,CAAEI,EAAG,OAIpB,IAAIY,EAAO,IAAI1B,IAAAA,OAAW,CACtBK,MAAOgB,EACPzM,KAAM,CAAC,CAAEkM,GAAI,KAAO,CAAEA,EAAG,MACzBJ,UAAW,CAAEE,EAAG,KAChBH,OAAQ,GACRH,MAAOV,IAGX8B,EAAKV,KAAK,CACNN,UAAW,CAAEE,EAAG,KAChBN,MAAOT,IAIX,IAAI8B,EAAW,IAAI3B,IAAAA,OAAW,CAC1BK,MAAOD,EACPxL,KAAM,CAAC,CAAEkM,GAAI,IAAM,CAAEA,EAAG,KACxBL,OAAQ,GACRC,UAAW,CAAEI,EAAG,IAAKH,EAAG,KACxBL,MAAOR,IAyCX,SAAS8B,IAEL7B,EAAKmB,OAAON,GAAK,KACjBb,EAAKmB,OAAOJ,GAAK,KACjBf,EAAKmB,OAAOP,GAAK,KACjBZ,EAAK8B,oBAEL3C,EAAQ4C,sBAAsBF,EAClC,CA9CAD,EAASX,KAAK,CACVN,UAAW,CAAEI,EAAG,IAAKF,EAAG,IAAKD,GAAI,KACjCL,MAAOR,IAGX6B,EAASX,KAAK,CACVN,UAAW,CAAEI,GAAI,IAAKF,EAAG,IAAKD,GAAI,KAClCL,MAAO,UAGXqB,EAASX,KAAK,CACVN,UAAW,CAAEI,GAAI,IAAKF,EAAG,IAAKD,GAAI,KAClCL,MAAOV,IAGX+B,EAASX,KAAK,CACVN,UAAW,CAAEI,EAAG,GAAIF,GAAI,GAAID,EAAG,KAC/BL,MAAOZ,IAGXiC,EAASX,KAAK,CACVN,UAAW,CAAEI,GAAI,IAAKF,EAAG,GAAID,EAAG,KAChCL,MAAOV,IAGX+B,EAASX,KAAK,CACVN,UAAW,CAAEI,GAAI,IAAKF,GAAI,IAAKD,EAAG,KAClCL,MAAOT,IAGX8B,EAASX,KAAK,CACVN,UAAW,CAAEI,EAAG,IAAKF,GAAI,IAAKD,GAAI,KAClCL,MAAO,UAIXP,EAAK8B,oBAaLD,K,OAEJG,EAAAA,EAAAA,IAAY,KACRC,qBAAqB9C,GACrBA,EAAQ,O,+EAlURxM,EAAAA,EAAAA,IA2BM,MA3BNC,EA2BM,C,0eAhBFsP,EAAAA,EAAAA,IAYOvN,EAAAwN,OAAA,aAZP,IAYO,EAXHtP,EAAAA,EAAAA,IAUM,MAVNC,EAUM,C,aATFD,EAAAA,EAAAA,IAAmC,OAA9BH,MAAM,gBAAe,OAAG,I,aAC7BG,EAAAA,EAAAA,IAAoD,OAA/CH,MAAM,mBAAkB,qBAAiB,I,aAC9CG,EAAAA,EAAAA,IAAmD,OAA9CH,MAAM,sBAAqB,iBAAa,KAC7CK,EAAAA,EAAAA,IAIcqP,EAAA,CAJDC,GAAG,KAAG,C,iBACf,IAESrO,EAAA,KAAAA,EAAA,KAFTnB,EAAAA,EAAAA,IAES,UAFDH,MAAM,uCAAsC,UAEpD,M,uCAKZC,EAAAA,EAAAA,IAEM,MAFNM,EAEM,EADFJ,EAAAA,EAAAA,IAA2B,U,QAAf,MAAJkL,IAAImB,G,yBAD2BG,Q,cCpBnD,MAAMzF,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,WFgFA,GACC1C,WAAY,CACXoL,UAASA,GAEV7J,IAAAA,GACG,MAAO,CAERwF,UAAW,CACV7J,SAAU,GACVmK,SAAU,IAERI,WAAY,CACVvK,SAAU,GACdmK,SAAU,GACNgE,WAAY,EACZC,YAAa,IAElBzF,QAAQ,EACL+B,eAAe,EAClBX,WAAY,CAEX/J,SAAU,CACT,CACCqO,UAAU,EACVC,QAAS,UACTpP,QAAS,SAIXiL,SAAU,CACT,CACCkE,UAAU,EACVC,QAAS,UACTpP,QAAS,UAITqP,SAAS,CACP,CAAC5H,GAAG,EAAE6H,OAAO,kCACb,CAAC7H,GAAG,EAAE6H,OAAO,iCACb,CAAC7H,GAAG,EAAE6H,OAAO,kCACb,CAAC7H,GAAG,EAAE6H,OAAO,qDACb,CAAC7H,GAAG,EAAE6H,OAAO,oDACb,CAAC7H,GAAG,EAAE6H,OAAO,qCACb,CAAC7H,GAAG,EAAE6H,OAAO,4CACb,CAAC7H,GAAG,EAAE6H,OAAO,8CACb,CAAC7H,GAAG,EAAE6H,OAAO,4CACb,CAAC7H,GAAG,GAAG6H,OAAO,sCACd,CAAC7H,GAAG,GAAG6H,OAAO,wCACd,CAAC7H,GAAG,GAAG6H,OAAO,qCACd,CAAC7H,GAAG,GAAG6H,OAAO,oCACd,CAAC7H,GAAG,GAAG6H,OAAO,8CACd,CAAC7H,GAAG,GAAG6H,OAAO,yCACd,CAAC7H,GAAG,GAAG6H,OAAO,mCACd,CAAC7H,GAAG,GAAG6H,OAAO,gCACd,CAAC7H,GAAG,GAAG6H,OAAO,mCACd,CAAC7H,GAAG,GAAG6H,OAAO,kCACd,CAAC7H,GAAG,GAAG6H,OAAO,uCAEhBhF,WAAY,QAEjB,EACA1E,QAAS,CACRuF,aAAAA,CAAcb,GAERvE,KAAKuE,WADS,UAAdA,EACkB,SAEF,OAEtB,EACEoB,cAAAA,GACE3F,KAAKyF,eAAgB,CACvB,EACA,iBAAMG,GACF,MAAM4D,EAAS,IAAIxJ,KAAKsF,YACG,KAAvBkE,EAAOL,cAAqBK,EAAOL,YAAcK,EAAOzO,UAC5D,MAAMwI,QAAiBvD,KAAKwD,KAAKiG,WAAWD,GACtB,MAAlBjG,EAASG,UACXgG,EAAAA,EAAAA,IAAe,CACXC,SAAU,IACV/N,MAAO,aACPoF,KAAM,YAEVhB,KAAKuE,WAAa,QAClBvE,KAAKsF,WAAa,CACdvK,SAAU,GACVmK,SAAU,GACVgE,WAAY,EACZC,YAAa,IAGvB,EACAS,UAAAA,GACE,MAAMC,EAAcC,KAAKC,MAAMD,KAAKE,SAAWhK,KAAKsJ,SAAS3I,QACvDsJ,EAAiBjK,KAAKsJ,SAASO,GACrCnK,OAAOC,eAAeuK,QAAQ,SAAUD,EAAeV,OAEzD,EAEFlE,KAAAA,GAECrF,KAAKmK,MAAMC,SAASC,SAASC,UAC5B,IAAKC,EAAO,OAEZ,MAAMhH,QAAiBvD,KAAKwD,KAAK6B,MAAMrF,KAAK4E,WAE5C,GAAuB,KAAnBrB,EAASG,OAAe,OAC5B,MAAM8G,EAASjH,EAASnE,MACpBsK,EAAAA,EAAAA,IAAe,CACTC,SAAU,IACV/N,MAAO,OACPoF,KAAM,YAIZhB,KAAK4J,aACTlK,OAAOC,eAAeuK,QAAQ,QAASM,EAAOC,OAC9C/K,OAAOC,eAAeuK,QAAQ,WAAYlK,KAAK4E,UAAU7J,UACrDiF,KAAK0D,OACRhE,OAAOgL,aAAaR,QAAQ,WAAYS,KAAKC,UAAU5K,KAAK4E,YAE5DlF,OAAOgL,aAAavK,WAAW,YAGhCH,KAAKC,QAAQC,KAAK,CAAEpD,KAAM,gBAE5B,GAED+N,OAAAA,GACC,MAAMC,EAAWpL,OAAOgL,aAAa9K,QAAQ,YACzCkL,IACH9K,KAAK4E,UAAY+F,KAAKI,MAAMD,GAC5B9K,KAAK0D,QAAS,EAEhB,GGvND,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASQ,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/Home.vue", "webpack://frontend-web/./src/components/common/LeftMenu.vue", "webpack://frontend-web/./src/components/common/LeftMenu.vue?dd88", "webpack://frontend-web/./src/components/common/Tags.vue", "webpack://frontend-web/./src/components/common/Tags.vue?6da2", "webpack://frontend-web/./src/views/Home.vue?9051", "webpack://frontend-web/./src/views/Login.vue", "webpack://frontend-web/./src/components/common/LoginBack.vue", "webpack://frontend-web/./src/components/common/LoginBack.vue?e9d8", "webpack://frontend-web/./src/views/Login.vue?240b"], "sourcesContent": ["<template>\r\n\t<div class=\"home\">\r\n\t\t<div class=\"left_box\"><LeftMenu></LeftMenu></div>\r\n\t\t<div class=\"right_box\">\r\n\t\t\t<!-- 顶部标签项 -->\r\n\t\t\t<Tags></Tags>\r\n\t\t\t<!-- 主体内容展示 -->\r\n\t\t\t<el-card class=\"main\" body-style=\"padding:0\"><router-view></router-view></el-card>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport LeftMenu from '../components/common/LeftMenu.vue';\r\nimport Tags from '../components/common/Tags.vue';\r\nimport {mapActions, mapState, mapMutations, mapGetters} from 'vuex';\r\nexport default {\r\n\tname: 'Home',\r\n\tcomponents: {\r\n\t\tLeftMenu,\r\n\t\tTags\r\n\t},\r\n\tmethods: {\r\n\t\t...mapActions(['getAllInter', 'getAllScent', 'getAllEnvs', 'getAllPlan'])\r\n\t},\r\n\tcreated() {\r\n\t\t// this.getAllInter();\r\n\t\t// this.getAllScent();\r\n\t\tthis.getAllEnvs();\r\n\t\tthis.getAllPlan();\r\n\t}\r\n};\r\n</script>\r\n\r\n<style type=\"text/css\" scoped>\r\n/* 背景颜色设置 */\r\n.home {\r\n\t background-image: linear-gradient(#001529, #001529) !important;\r\n\t/*background-image: linear-gradient(#001529, #00aa7f) !important;*/\r\n\t/* background: #00AA7F; */\r\n}\r\n/* 左侧盒子样式 */\r\n.left_box {\r\n\twidth: 202px;\r\n\theight: 100vh;\r\n\tborder-right: solid 1px #fff;\r\n}\r\n\r\n/* 右侧盒子样式 */\r\n.right_box {\r\n\tposition: absolute;\r\n\tleft: 200px;\r\n\ttop: 0px;\r\n\twidth: calc(100vw - 200px);\r\n\theight: 100vh;\r\n  background:#f5f7f9\r\n}\r\n.main {\r\n\tbackground: #fff;\r\n\theight: calc(100vh - 53px);\r\n\tmargin: 12px 12px 0 12px;\r\n}\r\n</style>\r\n", "<template>\r\n\t<!-- 用户信息 -->\r\n\t<div class=\"user_box\">\r\n\t\t<el-dropdown trigger=\"click\" :hide-on-click=\"false\" @command=\"handleCommand\" style=\"width: 100%; display: flex; justify-content: center; color: #fff; cursor: pointer;\">\r\n\t\t\t<span class=\"el-dropdown-link\">\r\n        <i><icon :icon=\"avatar\" class=\"u_head\"/></i>\r\n\t\t\t<span class=\"username\">{{ username }}\r\n\t\t\t\t<el-icon class=\"el-icon--right\"><CaretBottom /></el-icon>\r\n        </span>\r\n\t\t\t</span>\r\n\t\t\t<template #dropdown >\r\n\t\t\t\t<el-dropdown-menu >\r\n\t\t\t\t\t<el-dropdown-item command=\"select\">选择项目</el-dropdown-item>\r\n\t\t\t\t\t<el-dropdown-item command=\"logout\">注销登录</el-dropdown-item>\r\n\t\t\t\t</el-dropdown-menu>\r\n\t\t\t</template>\r\n\t\t</el-dropdown>\r\n\t</div>\r\n\t<!-- 左侧菜单 -->\r\n\t<div class=\"left_menu\">\r\n\t\t<el-scrollbar height=\"calc(100vh - 54px)\" >\r\n\t\t\t<el-menu :default-active=\"$route.path\" router background-color='#001529' text-color='#fff'\r\n\t\tactive-text-color='#fff' class=\"el-menu-vertical-demo\" :default-openeds=\"['test', 'test2', 'test3', 'dashboard', 'submenu']\" >\r\n        <el-sub-menu index=\"dashboard\">\r\n          <template #title>\r\n            <div class=\"centered-title\">\r\n              <el-icon><HomeFilled /></el-icon>\r\n              <span>平台看板</span>\r\n            </div>\r\n          </template>\r\n          <el-menu-item :index=\"'/project'\">\r\n            <el-icon><HomeFilled /></el-icon>\r\n            <span>接口看板</span>\r\n          </el-menu-item>\r\n        </el-sub-menu>\r\n\t\t\t\t <el-sub-menu index=\"test\" v-if=\"isTestMenuActive\">\r\n          <template #title>\r\n            <div class=\"centered-title\">\r\n              <el-icon><Link /></el-icon>\r\n              <span>接口测试</span>\r\n            </div>\r\n          </template>\r\n        <el-menu-item :index=\"item.path\" v-for=\"item in menus\" :key=\"item.path\">\r\n\t\t\t\t\t<el-icon :class=\"{ 'colored-icon': item.iconComponent === 'InfoFilled' }\">\r\n            <component :is=\"item.iconComponent\" />\r\n          </el-icon>\r\n\t\t\t\t\t<span>{{ item.name }}</span>\r\n\t\t\t\t</el-menu-item>\r\n       </el-sub-menu>\r\n         <el-sub-menu index=\"test2\" v-if=\"isTestsubMenuActive\">\r\n          <template #title>\r\n            <div class=\"centered-title\">\r\n              <el-icon><Compass /></el-icon>\r\n              <span>性能测试</span>\r\n            </div>\r\n          </template>\r\n         <el-menu-item :index=\"item.path\" v-for=\"item in menus1\" :key=\"item.path\">\r\n\t\t\t\t\t<el-icon><component :is=\"item.iconComponent\" /></el-icon>\r\n\t\t\t\t\t<span>{{ item.name }}</span>\r\n\t\t\t\t</el-menu-item>\r\n       </el-sub-menu>\r\n       <el-sub-menu index=\"test3\" v-if=\"isTestsubMenuActive\">\r\n          <template #title>\r\n            <div class=\"centered-title\">\r\n              <el-icon><Setting /></el-icon>\r\n              <span>其他工具</span>\r\n            </div>\r\n          </template>\r\n         <el-menu-item :index=\"item.path\" v-for=\"item in menus2\" :key=\"item.path\">\r\n\t\t\t\t\t<el-icon><component :is=\"item.iconComponent\" /></el-icon>\r\n\t\t\t\t\t<span>{{ item.name }}</span>\r\n\t\t\t\t</el-menu-item>\r\n       </el-sub-menu>\r\n       <el-sub-menu index=\"submenu\">\r\n          <template #title>\r\n            <div class=\"centered-title\">\r\n              <el-icon><Menu /></el-icon>\r\n              <span>其他菜单</span>\r\n            </div>\r\n          </template>\r\n          <el-menu-item :index=\"item.path\" v-for=\"item in submenu\" :key=\"item.path\">\r\n            <el-icon><component :is=\"item.iconComponent\" /></el-icon>\r\n            <span>{{ item.name }}</span>\r\n          </el-menu-item>\r\n        </el-sub-menu>\r\n\t\t\t</el-menu>\r\n\t\t</el-scrollbar>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport { Icon } from '@iconify/vue'\r\nimport {mapMutations, mapState} from 'vuex';\r\nimport { \r\n  HomeFilled, Link, Compass, Setting, Menu, CaretBottom,\r\n  Paperclip, QuestionFilled, CollectionTag, Coin, Timer, \r\n  Lightning, DataAnalysis, Promotion, User, Notebook, \r\n  Cpu, Stopwatch, Orange, VideoPlay, InfoFilled\r\n} from '@element-plus/icons-vue';\r\n\r\nconst menuList1 = [\r\n  {\r\n\t\tname: '接口管理',\r\n\t\tpath: '/new-interface',\r\n\t\ticonComponent: 'Paperclip'\r\n\t},\r\n\t{\r\n\t\tname: '接口用例',\r\n\t\tpath: '/TestCase',\r\n\t\ticonComponent: 'QuestionFilled'\r\n\t},\r\n  {\r\n\t\tname: '测试计划',\r\n\t\tpath: '/new-testplan',\r\n\t\ticonComponent: 'CollectionTag'\r\n\t},\r\n\t{\r\n\t\tname: '测试环境',\r\n\t\tpath: '/testenv',\r\n\t\ticonComponent: 'Coin'\r\n\t},\r\n\t{\r\n\t\tname: '定时任务',\r\n\t\tpath: '/crontab',\r\n\t\ticonComponent: 'Timer'\r\n\t},\r\n\t{\r\n\t\tname: 'bug管理',\r\n\t\tpath: '/bugs',\r\n\t\ticonComponent: 'Lightning'\r\n\t},\r\n\t{\r\n\t\tname: '测试报表',\r\n\t\tpath: '/records',\r\n\t\ticonComponent: 'DataAnalysis'\r\n\t}\r\n];\r\n\r\nconst submenuList = [\r\n    {\r\n\t\tname: '报告推送',\r\n\t\tpath: '/reportPush',\r\n\t\ticonComponent: 'Promotion'\r\n\t},\r\n  {\r\n\t\tname: '用户管理',\r\n\t\tpath: '/users',\r\n\t\ticonComponent: 'User'\r\n\t},\r\n  {\r\n\t\tname: '用例管理',\r\n\t\tpath: '/caseManage',\r\n\t\ticonComponent: 'Notebook'\r\n\t},\r\n];\r\n\r\nconst menuList2=[\r\n  {\r\n\t\tname: '性能任务',\r\n\t\tpath: '/performanceTask',\r\n\t\ticonComponent: 'Cpu'\r\n\t},\r\n  {\r\n\t\tname: '性能报告',\r\n\t\tpath: '/PerformanceResult',\r\n\t\ticonComponent: 'Stopwatch'\r\n\t},\r\n  {\r\n\t\tname: '机器管理',\r\n\t\tpath: '/server',\r\n\t\ticonComponent: 'Orange'\r\n\t},\r\n  {\r\n\t\tname: '预配设置',\r\n\t\tpath: '/makeSet',\r\n\t\ticonComponent: 'VideoPlay'\r\n\t},\r\n  {\r\n\t\tname: '性能告警',\r\n\t\tpath: '/PerformanceAlert',\r\n\t\ticonComponent: 'InfoFilled'\r\n\t},\r\n  {\r\n\t\tname: '基准线管理',\r\n\t\tpath: '/PerformanceBaseline',\r\n\t\ticonComponent: 'VideoPlay'\r\n\t},\r\n]\r\n\r\nconst menuList3=[]\r\n\r\nexport default {\r\n  components: {\r\n    Icon,\r\n    HomeFilled, Link, Compass, Setting, Menu, CaretBottom,\r\n    Paperclip, QuestionFilled, CollectionTag, Coin, Timer, \r\n    Lightning, DataAnalysis, Promotion, User, Notebook, \r\n    Cpu, Stopwatch, Orange, VideoPlay, InfoFilled\r\n  },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tmenus: menuList1,\r\n      menus1: menuList2,\r\n      menus2: menuList3,\r\n      submenu:submenuList,\r\n      isTestMenuActive: true,\r\n      isTestsubMenuActive: true,\r\n      openeds:['test', 'test2', 'test3']\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n    ...mapState({\r\n      tags: state => state.tags,\r\n    }),\r\n\t\tusername() {\r\n\t\t\treturn window.sessionStorage.getItem('username');\r\n\t\t},\r\n    avatar() {\r\n\t\t  return window.sessionStorage.getItem('avatar');\r\n    }\r\n\t},\r\n\tmethods: {\r\n     ...mapMutations(['clearEnvId','delTags']),\r\n\t\thandleCommand(cmd) {\r\n\t\t\tif (cmd === 'select') {\r\n\t\t\t\tthis.$router.push({ name: 'allProject' });\r\n\t\t\t\twindow.sessionStorage.removeItem('messageStore');\r\n\t\t\t  this.clearEnvId();\r\n\t\t\t  this.tags.forEach(item => {\r\n          this.delTags(item.path)});\r\n\r\n\t\t\t}\r\n\t\t\telse if (cmd === 'logout') {\r\n\t\t\t  this.clearEnvId();\r\n        this.tags.forEach(item => {\r\n          this.delTags(item.path)});\r\n\t\t\t\twindow.sessionStorage.removeItem('token');\r\n\t\t\t\twindow.sessionStorage.removeItem('username');\r\n\t\t\t\twindow.sessionStorage.removeItem('messageStore');\r\n\t\t\t\twindow.sessionStorage.removeItem('avatar');\r\n\t\t\t\tthis.$router.push({ name: 'login' });\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.user_box {\r\n  cursor: pointer;\r\n\theight: 53px;\r\n\tline-height: 53px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n  background-color: #001529;\r\n}\r\n.u_head {\r\n\theight: 53px;\r\n\tborder-radius: 50%;\r\n  width: 40px;\r\n  align-items: center;\r\n}\r\n.el-menu-item.is-active {\r\n      background-color: #409eff !important;\r\n      color: #fff;\r\n      span {\r\n        color: #fff !important;\r\n      }\r\n}\r\n.username {\r\n  height: 53px;\r\n  position: relative;\r\n  top: -23px;\r\n  margin-left: 6px;\r\n}\r\n.colored-icon {\r\n  color: rgb(245, 108, 108);\r\n}\r\n\r\n/* 添加样式使菜单项居中 */\r\n:deep(.el-menu) {\r\n  padding: 0;\r\n  border-right: none;\r\n}\r\n\r\n:deep(.el-menu-item) {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding-left: 0 !important;\r\n  text-align: center;\r\n}\r\n\r\n:deep(.el-sub-menu__title) {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding-left: 0 !important;\r\n}\r\n\r\n:deep(.el-sub-menu .el-menu-item) {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding-left: 0 !important;\r\n  min-width: 100%;\r\n  text-align: center;\r\n}\r\n\r\n.centered-title {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n/* 调整图标与文本间距 */\r\n:deep(.el-sub-menu__title span),\r\n:deep(.el-menu-item span) {\r\n  margin: 0 5px;\r\n}\r\n</style>\r\n", "import { render } from \"./LeftMenu.vue?vue&type=template&id=642fe5e7&scoped=true\"\nimport script from \"./LeftMenu.vue?vue&type=script&lang=js\"\nexport * from \"./LeftMenu.vue?vue&type=script&lang=js\"\n\nimport \"./LeftMenu.vue?vue&type=style&index=0&id=642fe5e7&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-642fe5e7\"]])\n\nexport default __exports__", "<template>\r\n\t<div class=\"tags\">\r\n\t\t<!-- 标签栏 -->\r\n\t\t<div class=\"tag_box\">\r\n\t\t\t<el-scrollbar>\r\n\t\t\t\t<span v-for=\"tag in tags\" :key=\"tag.name\" class=\"tag-item\">\r\n\t\t\t\t\t<el-tag\r\n\t\t\t\t\t\t:closable=\"tags.length !== 1\"\r\n\t\t\t\t\t\t@close=\"deletetag(tag.path)\"\r\n\t\t\t\t\t\t@click=\"$router.push(tag.path)\"\r\n\t\t\t\t\t\t:type=\"tag.path === $route.path ? 'primary' : ''\"\r\n\t\t\t\t\t\t:effect=\"tag.path === $route.path ? 'dark' : 'light'\"\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\tclass=\"tag-element\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{{ tag.name }}\r\n\t\t\t\t\t</el-tag>\r\n\t\t\t\t</span>\r\n\t\t\t</el-scrollbar>\r\n\t\t</div>\r\n\r\n\t\t<!-- 选择环境 -->\r\n\t\t<div class=\"select_env\">\r\n\t\t\t<el-button @click=\"closeAllTag\" type=\"primary\" size=\"small\" style=\"margin-right: 50px;\">关闭其他标签</el-button>\r\n      <el-select v-model=\"env\" placeholder=\"选择环境\" style=\"width: 180px;\" no-data-text=\"暂无数据\">\r\n        <el-option v-for=\"item in testEnvs\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n      </el-select>\r\n\t\t\t<el-tooltip v-if=\"env\" effect=\"dark\" content=\"查看环境信息\" placement=\"bottom\">\r\n\t\t\t\t<el-button style=\"margin-left: 5px\" @click=\"clickShowEnv\">\r\n\t\t\t\t\t<el-icon><View /></el-icon>\r\n\t\t\t\t</el-button>\r\n\t\t\t</el-tooltip>\r\n\t\t</div>\r\n\t</div>\r\n\t<!-- 显示环境详情 -->\r\n\t<el-dialog v-model=\"showEnv\" title=\"环境变量\" class=\"env-dialog\">\r\n\t\t<el-scrollbar height=\"500px\">\r\n\t\t\t<el-descriptions border :column=\"1\" class=\"env-descriptions\" :label-width=\"200\">\r\n\t\t\t\t<el-descriptions-item v-for=\"(value, key) in envInfo.debug_global_variable\" :key=\"`debug-${key}`\">\r\n\t\t\t\t\t<template #label>\r\n\t\t\t\t\t\t<div class=\"key-label\">\r\n\t\t\t\t\t\t\t<el-tag type=\"warning\">debug</el-tag>\r\n\t\t\t\t\t\t\t<span class=\"key-text\">{{ key }}</span>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t<div class=\"env-value\">{{ value }}</div>\r\n\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t<el-descriptions-item v-for=\"(value, key) in envInfo.global_variable\" :key=\"`global-${key}`\">\r\n\t\t\t\t\t<template #label>\r\n\t\t\t\t\t\t<div class=\"key-label\">\r\n\t\t\t\t\t\t\t<el-tag type=\"success\">global</el-tag>\r\n\t\t\t\t\t\t\t<span class=\"key-text\">{{ key }}</span>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t<div class=\"env-value\">{{ value }}</div>\r\n\t\t\t\t</el-descriptions-item>\r\n\t\t\t</el-descriptions>\r\n\t\t</el-scrollbar>\r\n\t\t<template #footer>\r\n\t\t\t<span class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"editEnv(envInfo)\" type=\"success\" plain>编辑</el-button>\r\n\t\t\t\t<el-button @click=\"showEnv = false\">关闭</el-button>\r\n\t\t\t</span>\r\n\t\t</template>\r\n\t</el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapMutations } from 'vuex';\r\nimport { View } from '@element-plus/icons-vue';\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\tView\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tshowEnv: false,\r\n\t\t\tenv_variable: [],\r\n\t\t\tenvInfo: {}\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n    ...mapState({\r\n      tags: state => state.tags,\r\n      envId: state => state.envId,\r\n      testEnvs: state => state.testEnvs,\r\n      pro: state => state.pro,\r\n    }),\r\n\t\tenv: {\r\n\t\t\tget() {\r\n\t\t\t\treturn this.envId;\r\n\t\t\t},\r\n\t\t\tset(val) {\r\n\t\t\t\tthis.selectEnv(val);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t...mapMutations(['delTags', 'selectEnv', 'selectEnvInfo']),\r\n\r\n\t\tasync clickShowEnv() {\r\n\t\t\t// 获取单个环境信息\r\n\t\t\tconst response = await this.$api.getEnvInfo(this.envId,this.pro.id);\r\n\t\t\tif (response.status === 200) {\r\n\t\t\t\tthis.envInfo = response.data;\r\n\t\t\t}\r\n\t\t\tthis.showEnv = true;\r\n\t\t},\r\n\t\t// 删除标签页\r\n\t\tdeletetag(path) {\r\n\t\t\tthis.delTags(path);\r\n\t\t\t// 如果被激活的标签删除了，则跳转路由到前一个标签的路由\r\n\t\t\tif (this.$route.path === path) {\r\n\t\t\t\tthis.$router.push(this.tags[this.tags.length - 1].path);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 关闭所有标签\r\n\t\tcloseAllTag() {\r\n\t\t\tthis.tags.forEach(item => {\r\n\t\t\t\tif (this.$route.path !== item.path) {\r\n\t\t\t\t\tthis.delTags(item.path);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 编辑环境\r\n\t\teditEnv(envInfo) {\r\n\t\t\tthis.showEnv = false;\r\n\t\t\tthis.selectEnvInfo(envInfo)\r\n\t\t\tthis.$router.push({ name: 'testenv' });\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n.tags {\r\n\tbackground: #fff;\r\n\theight: 37px;\r\n\tmargin: 2px 3px;\r\n\tline-height: 37px;\r\n\tdisplay: flex;\r\n}\r\n.tag_box {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n.tag-item {\r\n\tdisplay: inline-block;\r\n\tmargin-left: 10px;\r\n}\r\n.tag-element {\r\n\tcursor: pointer;\r\n}\r\n.select_env {\r\n\twidth: 400px;\r\n\tborder-left: solid 2px #f7f7f7;\r\n\ttext-align: center;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n/* 环境弹窗样式 */\r\n.env-dialog .el-dialog__body {\r\n\tpadding: 10px 20px;\r\n}\r\n\r\n.env-descriptions .el-descriptions-item__content {\r\n\tword-break: break-word;\r\n}\r\n\r\n.env-descriptions .el-descriptions-item__label {\r\n\twidth: 35% !important;\r\n\tmin-width: 200px;\r\n}\r\n\r\n.key-label {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\twidth: 100%;\r\n}\r\n\r\n.key-text {\r\n\tmargin-left: 5px;\r\n\tword-break: break-word;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.env-value {\r\n\twhite-space: pre-wrap;\r\n\tword-break: break-word;\r\n\tmax-width: 100%;\r\n\toverflow-wrap: break-word;\r\n}\r\n</style>\r\n", "import { render } from \"./Tags.vue?vue&type=template&id=64918f49\"\nimport script from \"./Tags.vue?vue&type=script&lang=js\"\nexport * from \"./Tags.vue?vue&type=script&lang=js\"\n\nimport \"./Tags.vue?vue&type=style&index=0&id=64918f49&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import { render } from \"./Home.vue?vue&type=template&id=025713db&scoped=true\"\nimport script from \"./Home.vue?vue&type=script&lang=js\"\nexport * from \"./Home.vue?vue&type=script&lang=js\"\n\nimport \"./Home.vue?vue&type=style&index=0&id=025713db&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-025713db\"]])\n\nexport default __exports__", "<template>\r\n\t<LoginBack>\r\n\t\t<div class=\"login_box\">\r\n\t\t\t<div class=\"logo_box\"><img src=\"../assets/images/logo.png\" /></div>\r\n        <el-tabs v-model=\"activeName\" class=\"login-tabs\">\r\n          <el-tab-pane label=\"登 录\" name=\"first\">\r\n            <el-form ref=\"loginRef\" class=\"login_from\" :model=\"loginForm\" :rules=\"rulesLogin\">\r\n              <el-form-item prop=\"username\" class=\"custom-form-item\">\r\n                <el-input \r\n                  size=\"large\" \r\n                  v-model=\"loginForm.username\" \r\n                  prefix-icon=\"User\" \r\n                  placeholder=\"请输入账号\"\r\n                  class=\"custom-input\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item prop=\"password\" class=\"custom-form-item\">\r\n                <el-input \r\n                  type=\"password\" \r\n                  size=\"large\" \r\n                  v-model=\"loginForm.password\" \r\n                  placeholder=\"请输入密码\" \r\n                  prefix-icon=\"Lock\" \r\n                  show-password\r\n                  class=\"custom-input\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <div class=\"login-options\">\r\n                  <el-checkbox v-model=\"status\" class=\"remember-checkbox\">记住用户</el-checkbox>\r\n                  <div class=\"register-link\">\r\n                      没有账号?\r\n                      <span @click=\"clickRegister(activeName)\" class=\"action-link\">去注册</span>\r\n                  </div>\r\n              </div>\r\n              <!-- 按钮 -->\r\n              <el-form-item>\r\n                <el-button size=\"large\" type=\"primary\" class=\"login-btn\" @click=\"login\">登 录</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"注 册\" name=\"second\">\r\n            <el-form class=\"login_from\" :model=\"createForm\">\r\n              <el-form-item class=\"custom-form-item\">\r\n                <el-input \r\n                  clearable \r\n                  :readonly=\"readonlyInput\" \r\n                  @focus=\"cancelReadOnly\" \r\n                  size=\"large\" \r\n                  v-model=\"createForm.username\" \r\n                  prefix-icon=\"User\" \r\n                  placeholder=\"请输入账号\"\r\n                  class=\"custom-input\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item class=\"custom-form-item\">\r\n                <el-input \r\n                  clearable \r\n                  :readonly=\"readonlyInput\" \r\n                  @focus=\"cancelReadOnly\"  \r\n                  type=\"password\" \r\n                  size=\"large\" \r\n                  v-model=\"createForm.password\" \r\n                  placeholder=\"请输入密码\" \r\n                  prefix-icon=\"Lock\" \r\n                  show-password\r\n                  class=\"custom-input\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <div class=\"login-options\">\r\n                  <el-checkbox v-model=\"status\" class=\"remember-checkbox\">记住用户</el-checkbox>\r\n                  <div class=\"register-link\">\r\n                      已有账号?\r\n                      <span @click=\"clickRegister(activeName)\" class=\"action-link\">去登录</span>\r\n                  </div>\r\n              </div>\r\n              <el-form-item>\r\n                <el-button size=\"large\" type=\"primary\" class=\"login-btn\" @click=\"createClick\">注 册</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n\t\t</div>\r\n\t</LoginBack>\r\n</template>\r\n\r\n<script type=\"text/javascript\">\r\nimport LoginBack from '../components/common/LoginBack.vue';\r\nimport {ElNotification} from \"element-plus\";\r\nexport default {\r\n\tcomponents: {\r\n\t\tLoginBack\r\n\t},\r\n\tdata() {\r\n    return {\r\n\t\t\t// 登录的数据对象\r\n\t\t\tloginForm: {\r\n\t\t\t\tusername: '',\r\n\t\t\t\tpassword: ''\r\n\t\t\t},\r\n      createForm: {\r\n        username: '',\r\n\t\t\t\tpassword: '',\r\n        project_id: 1,\r\n        weChat_name: ''\r\n      },\r\n\t\t\tstatus: false,\r\n      readonlyInput: true,\r\n\t\t\trulesLogin: {\r\n\t\t\t\t// 验证用户名是否合法\r\n\t\t\t\tusername: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请输入登录账号',\r\n\t\t\t\t\t\ttrigger: 'blur'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// 验证密码是否合法\r\n\t\t\t\tpassword: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请输入登录密码',\r\n\t\t\t\t\t\ttrigger: 'blur'\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t},\r\n      userIcon:[\r\n        {id:1,Emojis:\"streamline-emojis:amusing-face\"},\r\n        {id:2,Emojis:\"streamline-emojis:amazed-face\"},\r\n        {id:3,Emojis:\"streamline-emojis:anxious-face\"},\r\n        {id:4,Emojis:\"streamline-emojis:rolling-on-the-floor-laughing-1\"},\r\n        {id:5,Emojis:\"streamline-emojis:beaming-face-with-smiling-eyes\"},\r\n        {id:6,Emojis:\"streamline-emojis:astonished-face\"},\r\n        {id:7,Emojis:\"streamline-emojis:face-screaming-in-fear\"},\r\n        {id:8,Emojis:\"streamline-emojis:face-with-raised-eyebrow\"},\r\n        {id:9,Emojis:\"streamline-emojis:face-with-rolling-eyes\"},\r\n        {id:10,Emojis:\"streamline-emojis:face-with-tongue\"},\r\n        {id:11,Emojis:\"streamline-emojis:face-without-mouth\"},\r\n        {id:12,Emojis:\"streamline-emojis:drooling-face-1\"},\r\n        {id:13,Emojis:\"streamline-emojis:grimacing-face\"},\r\n        {id:14,Emojis:\"streamline-emojis:grinning-face-with-sweat\"},\r\n        {id:15,Emojis:\"streamline-emojis:face-blowing-a-kiss\"},\r\n        {id:16,Emojis:\"streamline-emojis:hushed-face-2\"},\r\n        {id:17,Emojis:\"streamline-emojis:lying-face\"},\r\n        {id:18,Emojis:\"streamline-emojis:star-struck-1\"},\r\n        {id:19,Emojis:\"streamline-emojis:winking-face\"},\r\n        {id:20,Emojis:\"streamline-emojis:upside-down-face\"}\r\n      ],\r\n      activeName: 'first'\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\tclickRegister(activeName) {\r\n\t\t  if (activeName ==='first') {\r\n        this.activeName = 'second'\r\n      }else {\r\n\t\t    this.activeName = 'first'\r\n      }\r\n\t\t},\r\n    cancelReadOnly() {\r\n      this.readonlyInput = false;\r\n    },\r\n    async createClick() {\r\n        const params = {...this.createForm}\r\n        if (params.weChat_name === '') {params.weChat_name = params.username}\r\n        const response = await this.$api.createUser(params)\r\n        if (response.status===201) {\r\n          ElNotification({\r\n              duration: 1000,\r\n              title: '创建成功，可以登录咯',\r\n              type: 'success',\r\n            })\r\n          this.activeName = 'first'\r\n          this.createForm = {\r\n              username: '',\r\n              password: '',\r\n              project_id: 1,\r\n              weChat_name: ''\r\n            };\r\n        }\r\n    },\r\n    userAvatar() {\r\n      const randomIndex = Math.floor(Math.random() * this.userIcon.length);\r\n      const selectedEmojis = this.userIcon[randomIndex];\r\n      window.sessionStorage.setItem('avatar', selectedEmojis.Emojis);\r\n\r\n    },\r\n\t\t// 登录的方法\r\n\t\tlogin() {\r\n\t\t\t// 通过表单的validate方法来验证表单，验证的结果会传递到validate的回调函数中\r\n\t\t\tthis.$refs.loginRef.validate(async vaild => {\r\n\t\t\t\tif (!vaild) return;\r\n\t\t\t\t// 发送请求\r\n\t\t\t\tconst response = await this.$api.login(this.loginForm);\r\n\t\t\t\t// 判断是否登录失败\r\n\t\t\t\tif (response.status != 200) return;\r\n\t\t\t\tconst result = response.data;\r\n        ElNotification({\r\n              duration: 1000,\r\n              title: '登录成功',\r\n              type: 'success',\r\n            })\r\n\t\t\t\t// 2、获取token,保存到客户端的sessionStorage中\r\n        // 保存用户头像到sessionStorage\r\n        this.userAvatar()\r\n\t\t\t\twindow.sessionStorage.setItem('token', result.token);\r\n\t\t\t\twindow.sessionStorage.setItem('username', this.loginForm.username);\r\n\t\t\t\tif (this.status) {\r\n\t\t\t\t\twindow.localStorage.setItem('userinfo', JSON.stringify(this.loginForm));\r\n\t\t\t\t} else {\r\n\t\t\t\t\twindow.localStorage.removeItem('userinfo');\r\n\t\t\t\t}\r\n\t\t\t\t// 3、通过编程式导航跳转到登录之后的页面中\r\n\t\t\t\tthis.$router.push({ name: 'allProject' })\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tconst userinfo = window.localStorage.getItem('userinfo');\r\n\t\tif (userinfo) {\r\n\t\t\tthis.loginForm = JSON.parse(userinfo);\r\n\t\t\tthis.status = true;\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n\t/* 登录框的样式 */\r\n\t.login_box {\r\n\t\tcolor: #fff;\r\n\t\twidth: 500px;\r\n\t\tmargin: 0 auto;\r\n    /* 移除背景色，使用更微妙的透明效果 */\r\n    background-color: rgba(0, 0, 0, 0.2); \r\n    border-radius: 12px;\r\n    padding: 30px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);\r\n    backdrop-filter: blur(8px);\r\n    /* 使用flexbox居中布局 */\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    border: 1px solid rgba(255, 255, 255, 0.08);\r\n\t}\r\n\r\n\t/* logo居中 */\r\n\t.logo_box {\r\n    margin-top: 0;\r\n\t\ttext-align: center;\r\n    height: 100px;\r\n    margin-bottom: 20px;\r\n\t}\r\n\r\n  /* 自定义表单项 */\r\n  .custom-form-item {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  /* 全局强制覆盖Element Plus的输入框样式 */\r\n  :deep(.el-input__wrapper) {\r\n    background-color: transparent !important;\r\n    box-shadow: none !important;\r\n    border: 1px solid rgba(255, 255, 255, 0.1) !important;\r\n  }\r\n\r\n  /* 自定义输入框 */\r\n  .custom-input :deep(.el-input__wrapper) {\r\n    background-color: transparent !important;\r\n    border-radius: 8px;\r\n    box-shadow: none !important;\r\n    border: 1px solid rgba(255, 255, 255, 0.1) !important;\r\n    padding: 12px 15px;\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  /* 当输入框有值时强制保持透明背景 */\r\n  .custom-input :deep(.el-input__wrapper.is-focus),\r\n  .custom-input :deep(.el-input__wrapper:hover),\r\n  .custom-input :deep(.el-input__wrapper.is-focus) {\r\n    border-color: #1296db !important;\r\n    box-shadow: 0 0 0 1px rgba(18, 150, 219, 0.3) !important;\r\n    background-color: transparent !important;\r\n  }\r\n\r\n  /* 对已填写内容的输入框强制应用透明背景 */\r\n  .custom-input :deep(.is-filled .el-input__wrapper) {\r\n    background-color: transparent !important;\r\n  }\r\n\r\n  /* Element Plus在验证后会给输入框添加额外类，确保这些状态下也保持透明 */\r\n  .custom-input :deep(.is-success .el-input__wrapper),\r\n  .custom-input :deep(.is-error .el-input__wrapper),\r\n  .custom-input :deep(.is-validating .el-input__wrapper) {\r\n    background-color: transparent !important;\r\n  }\r\n\r\n  .custom-input :deep(.el-input__inner) {\r\n    color: #fff !important;\r\n    height: 40px;\r\n    background-color: transparent !important;\r\n  }\r\n\r\n  /* 确保输入后文字显示为白色 */\r\n  .custom-input :deep(input) {\r\n    color: #fff !important;\r\n    background-color: transparent !important;\r\n  }\r\n\r\n  .custom-input :deep(.el-input__prefix) {\r\n    color: rgba(255, 255, 255, 0.7);\r\n    font-size: 18px;\r\n  }\r\n  \r\n  /* 解决密码输入框图标的问题 */\r\n  .custom-input :deep(.el-input__suffix) {\r\n    color: rgba(255, 255, 255, 0.7);\r\n  }\r\n  \r\n  /* 选项区域 */\r\n  .login-options {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  /* 记住用户复选框 */\r\n  .remember-checkbox {\r\n    color: rgba(255, 255, 255, 0.9);\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .remember-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {\r\n    background-color: #1296db;\r\n    border-color: #1296db;\r\n  }\r\n  \r\n  .remember-checkbox :deep(.el-checkbox__inner) {\r\n    background-color: transparent;\r\n    border-color: rgba(255, 255, 255, 0.5);\r\n  }\r\n\r\n  .register-link {\r\n    color: rgba(255, 255, 255, 0.7);\r\n    font-size: 14px;\r\n  }\r\n\r\n  .action-link {\r\n    color: #1296db;\r\n    cursor: pointer;\r\n    font-weight: bold;\r\n    margin-left: 5px;\r\n    position: relative;\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  .action-link:hover {\r\n    color: #39aae4;\r\n    text-decoration: underline;\r\n  }\r\n\r\n  /* 登录按钮 */\r\n  .login-btn {\r\n    width: 100%;\r\n    height: 45px;\r\n    border-radius: 8px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    letter-spacing: 2px;\r\n    background: linear-gradient(90deg, #1296db, #2a88d4);\r\n    border: none;\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  .login-btn:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(18, 150, 219, 0.3);\r\n    background: linear-gradient(90deg, #17a7f0, #3b99e5);\r\n  }\r\n\r\n  /* 标签页样式 */\r\n  .login-tabs :deep(.el-tabs__nav) {\r\n    width: 100%;\r\n    display: flex;\r\n  }\r\n\r\n  .login-tabs :deep(.el-tabs__item) {\r\n    flex: 1;\r\n    text-align: center;\r\n    height: 45px;\r\n    line-height: 45px;\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: rgba(255, 255, 255, 0.7);\r\n    transition: all 0.3s;\r\n  }\r\n\r\n  .login-tabs :deep(.el-tabs__item.is-active) {\r\n    color: #fff;\r\n    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);\r\n  }\r\n\r\n  .login-tabs :deep(.el-tabs__active-bar) {\r\n    background-color: #1296db;\r\n    height: 3px;\r\n    border-radius: 3px;\r\n  }\r\n</style>\r\n", "\r\n<template>\r\n    <div class=\"ve_404\">\r\n        <!-- partial:index.partial.html -->\r\n        <div class=\"moon\"></div>\r\n        <div class=\"moon__crater moon__crater1\"></div>\r\n        <div class=\"moon__crater moon__crater2\"></div>\r\n        <div class=\"moon__crater moon__crater3\"></div>\r\n        <div class=\"star star1\">⭐</div>\r\n        <div class=\"star star2\">⭐</div>\r\n        <div class=\"star star3\">⭐</div>\r\n        <div class=\"star star4\">⭐</div>\r\n        <div class=\"star star5\">⭐</div>\r\n        <slot>\r\n            <div class=\"error\">\r\n                <div class=\"error__title\">404</div>\r\n                <div class=\"error__subtitle\">🐱🐱🐱(⓿_⓿)🐱🐱🐱</div>\r\n                <div class=\"error__description\">看来你是迷路了......</div>\r\n                <router-link to=\"/\">\r\n                    <button class=\"error__button error__button--active\">\r\n                        回到首页\r\n                    </button>\r\n                </router-link>\r\n                <!-- <button class=\"error__button\">CONTACT</button> -->\r\n            </div>\r\n        </slot>\r\n        <div class=\"astronaut\" v-resize=\"{ resize: draw3dAstronaut }\">\r\n            <canvas ref=\"cav\"></canvas>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport Zdog from \"zdog\";\r\nimport { ref, onUnmounted, onMounted } from \"vue\";\r\nconst cav = ref(null);\r\nlet timer = null;\r\nonMounted(() => {\r\n    draw3dAstronaut();\r\n});\r\n/**\r\n * @description: 画3d太空人\r\n * @param {*}\r\n * @return {*}\r\n */\r\nconst draw3dAstronaut = () => {\r\n    cav.value.width = cav.value.parentNode.clientWidth;\r\n    cav.value.height = cav.value.parentNode.clientHeight;\r\n\r\n    // colours\r\n    let dark_navy = \"#131e38\";\r\n    let orange = \"#fe9642\";\r\n    let cream = \"#FFF8E7\";\r\n    let light_purple = \"#7f3f98\";\r\n    let dark_purple = \"#563795\";\r\n    let cheese = \"#fbc715\";\r\n\r\n    // create illo\r\n    let illo = new Zdog.Illustration({\r\n        // set canvas with selector\r\n        element: cav.value,\r\n        dragRotate: true,\r\n        zoom: 0.65,\r\n    });\r\n\r\n    /** Body **/\r\n    // Body\r\n    let body = new Zdog.RoundedRect({\r\n        addTo: illo,\r\n        width: 200,\r\n        height: 220,\r\n        color: \"white\",\r\n        fill: true,\r\n        cornerRadius: 16,\r\n        stroke: 60,\r\n    });\r\n\r\n    // Backpack\r\n    new Zdog.RoundedRect({\r\n        addTo: body,\r\n        width: 180,\r\n        height: 310,\r\n        color: orange,\r\n        fill: true,\r\n        cornerRadius: 24,\r\n        stroke: 120,\r\n        translate: { z: -85, y: -60 },\r\n    });\r\n\r\n    /** arm **/\r\n    let arm = new Zdog.RoundedRect({\r\n        addTo: body,\r\n        height: 30,\r\n        width: 28,\r\n        stroke: 60,\r\n        fill: true,\r\n        color: dark_purple,\r\n        translate: { x: -140, y: -64 },\r\n        cornerRadius: 0.05,\r\n    });\r\n\r\n    new Zdog.RoundedRect({\r\n        addTo: arm,\r\n        height: 120,\r\n        width: 12,\r\n        stroke: 60,\r\n        fill: true,\r\n        color: cream,\r\n        translate: { y: 120 },\r\n        cornerRadius: 0.05,\r\n    });\r\n\r\n    // bubble_arm\r\n    let bubble_arm = new Zdog.Shape({\r\n        addTo: arm,\r\n        path: [{ x: -20 }, { x: 20 }],\r\n        stroke: 32,\r\n        color: light_purple,\r\n        translate: { y: 210 },\r\n    });\r\n\r\n    bubble_arm.copy({\r\n        color: dark_purple,\r\n        translate: { y: 230 },\r\n    });\r\n\r\n    // hand\r\n    new Zdog.RoundedRect({\r\n        addTo: bubble_arm,\r\n        height: 32,\r\n        width: 22,\r\n        translate: { x: -8, y: 60 },\r\n        fill: true,\r\n        color: cheese,\r\n        stroke: 30,\r\n    });\r\n\r\n    new Zdog.RoundedRect({\r\n        addTo: bubble_arm,\r\n        height: 24,\r\n        width: 0,\r\n        translate: { x: 24, y: 50 },\r\n        fill: true,\r\n        color: orange,\r\n        stroke: 20,\r\n    });\r\n\r\n    arm.copyGraph({\r\n        translate: { x: 140, y: -64 },\r\n        rotate: { y: Zdog.TAU / 2 },\r\n    });\r\n\r\n    /** Leg **/\r\n    let leg = new Zdog.RoundedRect({\r\n        addTo: body,\r\n        height: 160,\r\n        width: 28,\r\n        stroke: 60,\r\n        fill: true,\r\n        color: cream,\r\n        translate: { x: -56, y: 230 },\r\n        cornerRadius: 0.05,\r\n    });\r\n\r\n    // bubble_leg\r\n    let bubble_leg = new Zdog.Shape({\r\n        addTo: leg,\r\n        path: [{ x: -28 }, { x: 28 }],\r\n        stroke: 32,\r\n        color: light_purple,\r\n        translate: { y: 100 },\r\n    });\r\n\r\n    bubble_leg.copy({\r\n        color: dark_purple,\r\n        translate: { y: 124 },\r\n    });\r\n\r\n    // foot\r\n    new Zdog.RoundedRect({\r\n        addTo: leg,\r\n        width: 96,\r\n        height: 24,\r\n        stroke: 40,\r\n        fill: true,\r\n        color: cheese,\r\n        translate: { x: -24, y: 170 },\r\n        cornerRadius: 24,\r\n    });\r\n\r\n    leg.copyGraph({\r\n        translate: { x: 56, y: 230 },\r\n        rotate: { y: Zdog.TAU / 2 },\r\n    });\r\n\r\n    /** Head **/\r\n    // Head\r\n    let head = new Zdog.RoundedRect({\r\n        addTo: body,\r\n        width: 216,\r\n        height: 180,\r\n        depth: 40,\r\n        cornerRadius: 80,\r\n        stroke: 60,\r\n        color: cream,\r\n        fill: true,\r\n        translate: { y: -300 },\r\n    });\r\n\r\n    //add helmet\r\n    let helmet = new Zdog.RoundedRect({\r\n        addTo: head,\r\n        width: 210,\r\n        height: 165,\r\n        cornerRadius: 64,\r\n        color: dark_navy,\r\n        fill: true,\r\n        backface: false,\r\n        translate: { z: 20 },\r\n    });\r\n\r\n    //add refletion\r\n    new Zdog.Rect({\r\n        addTo: helmet,\r\n        width: 48,\r\n        height: 2,\r\n        stroke: 10,\r\n        translate: { x: 24, y: -24, z: 10 },\r\n        color: \"white\",\r\n        backface: false,\r\n    });\r\n\r\n    // add ear\r\n    let ear = new Zdog.RoundedRect({\r\n        addTo: head,\r\n        width: 36,\r\n        height: 72,\r\n        cornerRadius: 80,\r\n        stroke: 20,\r\n        color: orange,\r\n        fill: true,\r\n        translate: { x: -140 },\r\n    });\r\n\r\n    ear.copy({\r\n        translate: { x: 140 },\r\n    });\r\n\r\n    // neck\r\n    let neck = new Zdog.Shape({\r\n        addTo: head,\r\n        path: [{ x: -110 }, { x: 110 }],\r\n        translate: { y: 120 },\r\n        stroke: 40,\r\n        color: light_purple,\r\n    });\r\n\r\n    neck.copy({\r\n        translate: { y: 160 },\r\n        color: dark_purple,\r\n    });\r\n\r\n    /** extra **/\r\n    let stripe_1 = new Zdog.Shape({\r\n        addTo: body,\r\n        path: [{ x: -20 }, { x: 20 }],\r\n        stroke: 10,\r\n        translate: { x: 200, z: 200 },\r\n        color: cheese,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: 320, y: 200, z: -400 },\r\n        color: cheese,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: -220, y: 300, z: -400 },\r\n        color: \"white\",\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: -100, y: 400, z: -280 },\r\n        color: light_purple,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: 50, y: -60, z: 150 },\r\n        color: orange,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: -250, y: 80, z: 300 },\r\n        color: light_purple,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: -350, y: -280, z: 175 },\r\n        color: dark_purple,\r\n    });\r\n\r\n    stripe_1.copy({\r\n        translate: { x: 250, y: -380, z: -175 },\r\n        color: \"white\",\r\n    });\r\n\r\n    // update & render\r\n    illo.updateRenderGraph();\r\n\r\n    function animate() {\r\n        // rotate illo each frame\r\n        illo.rotate.y += 0.005;\r\n        illo.rotate.x += 0.005;\r\n        illo.rotate.z += 0.005;\r\n        illo.updateRenderGraph();\r\n        // animate next frame\r\n        timer = requestAnimationFrame(animate);\r\n    }\r\n\r\n    // start animation\r\n    animate();\r\n};\r\nonUnmounted(() => {\r\n    cancelAnimationFrame(timer);\r\n    timer = null;\r\n});\r\n</script>\r\n\r\n<style  scoped>\r\n.ve_404 {\r\n    height: 100vh;\r\n    width: 100vw;\r\n    position: relative;\r\n    overflow: hidden;\r\n    background: linear-gradient(90deg, #2f3640 23%, #181b20 100%);\r\n}\r\n.moon {\r\n    background: linear-gradient(90deg, #d0d0d0 48%, #919191 100%);\r\n    position: absolute;\r\n    top: -30vh;\r\n    left: -80vh;\r\n    width: 154vh;\r\n    height: 160%;\r\n    content: \"\";\r\n    border-radius: 50%;\r\n    box-shadow: 0px 0px 30px -4px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.moon__crater {\r\n    position: absolute;\r\n    content: \"\";\r\n    border-radius: 100%;\r\n    background: linear-gradient(90deg, #7a7a7a 38%, #c3c3c3 100%);\r\n    opacity: 0.6;\r\n}\r\n\r\n.moon__crater1 {\r\n    top: 250px;\r\n    left: 500px;\r\n    width: 60px;\r\n    height: 180px;\r\n}\r\n\r\n.moon__crater2 {\r\n    top: 650px;\r\n    left: 340px;\r\n    width: 40px;\r\n    height: 80px;\r\n    transform: rotate(55deg);\r\n}\r\n\r\n.moon__crater3 {\r\n    top: -20px;\r\n    left: 40px;\r\n    width: 65px;\r\n    height: 120px;\r\n    transform: rotate(250deg);\r\n}\r\n\r\n.star {\r\n    color: grey;\r\n    position: absolute;\r\n    width: 10px;\r\n    height: 10px;\r\n    content: \"\";\r\n    border-radius: 100%;\r\n    transform: rotate(250deg);\r\n    opacity: 0.4;\r\n    animation-name: shimmer;\r\n    animation-duration: 1.5s;\r\n    animation-iteration-count: infinite;\r\n    animation-direction: alternate;\r\n}\r\n\r\n@keyframes shimmer {\r\n    from {\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        opacity: 0.7;\r\n    }\r\n}\r\n\r\n.star1 {\r\n    top: 40%;\r\n    left: 50%;\r\n    animation-delay: 1s;\r\n}\r\n\r\n.star2 {\r\n    top: 60%;\r\n    left: 90%;\r\n    animation-delay: 3s;\r\n}\r\n\r\n.star3 {\r\n    top: 10%;\r\n    left: 70%;\r\n    animation-delay: 2s;\r\n}\r\n\r\n.star4 {\r\n    top: 90%;\r\n    left: 40%;\r\n}\r\n\r\n.star5 {\r\n    top: 20%;\r\n    left: 30%;\r\n    animation-delay: 0.5s;\r\n}\r\n\r\n.astronaut {\r\n    position: absolute;\r\n    width: 35vw;\r\n    height: 100vh;\r\n    top: 0;\r\n    right: 0;\r\n    z-index: 0;\r\n}\r\n\r\n.error {\r\n    position: absolute;\r\n    left: 100px;\r\n    top: 400px;\r\n    transform: translateY(-60%);\r\n    font-family: \"Righteous\", cursive;\r\n    color: #363e49;\r\n    z-index: 1;\r\n}\r\n\r\n.error__title {\r\n    font-size: 10em;\r\n    font-weight: bold;\r\n    color: #d0d0d0;\r\n    text-shadow: -5px -5px 0 rgba(0, 0, 0, 0.7);\r\n    background-image: linear-gradient(90deg, #d0d0d0 48%, #919191 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n}\r\n\r\n.error__subtitle {\r\n    font-size: 2em;\r\n}\r\n\r\n.error__description {\r\n    opacity: 0.5;\r\n}\r\n\r\n.error__button {\r\n    min-width: 7em;\r\n    margin-top: 3em;\r\n    margin-right: 0.5em;\r\n    padding: 0.5em 2em;\r\n    outline: none;\r\n    border: 2px solid #2f3640;\r\n    background-color: transparent;\r\n    border-radius: 8em;\r\n    color: #576375;\r\n    cursor: pointer;\r\n    transition-duration: 0.2s;\r\n    font-size: 0.75em;\r\n    font-family: \"Righteous\", cursive;\r\n}\r\n\r\n.error__button:hover {\r\n    color: #21252c;\r\n}\r\n\r\n.error__button--active {\r\n    background-color: $base-color;\r\n    border: 2px solid $base-color;\r\n    color: white;\r\n}\r\n\r\n.error__button--active:hover {\r\n    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.5);\r\n    color: white;\r\n}\r\n</style>\r\n", "import script from \"./LoginBack.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./LoginBack.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./LoginBack.vue?vue&type=style&index=0&id=1b10676e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-1b10676e\"]])\n\nexport default __exports__", "import { render } from \"./Login.vue?vue&type=template&id=c35b05fe&scoped=true\"\nimport script from \"./Login.vue?vue&type=script&lang=js\"\nexport * from \"./Login.vue?vue&type=script&lang=js\"\n\nimport \"./Login.vue?vue&type=style&index=0&id=c35b05fe&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-c35b05fe\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_LeftMenu", "_hoisted_3", "_component_Tags", "_component_el_card", "_component_router_view", "_component_el_dropdown", "trigger", "onCommand", "$options", "handleCommand", "style", "dropdown", "_withCtx", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "_cache", "_component_icon", "icon", "avatar", "username", "_component_el_icon", "_component_CaretBottom", "_hoisted_4", "_component_el_scrollbar", "height", "_component_el_menu", "_ctx", "$route", "path", "router", "_component_el_sub_menu", "index", "title", "_hoisted_5", "_component_HomeFilled", "_component_el_menu_item", "$data", "isTestMenuActive", "_createBlock", "_hoisted_6", "_component_Link", "_Fragment", "_renderList", "menus", "item", "key", "_normalizeClass", "iconComponent", "_resolveDynamicComponent", "_toDisplayString", "name", "isTestsubMenuActive", "_hoisted_7", "_component_Compass", "menus1", "_hoisted_8", "_component_Setting", "menus2", "_hoisted_9", "_component_Menu", "submenu", "menuList1", "submenuList", "menuList2", "menuList3", "components", "Icon", "HomeFilled", "Link", "<PERSON>mp<PERSON>", "Setting", "<PERSON><PERSON>", "CaretBottom", "Paperclip", "QuestionFilled", "CollectionTag", "Coin", "Timer", "Lightning", "DataAnalysis", "Promotion", "User", "Notebook", "Cpu", "Stopwatch", "Orange", "VideoPlay", "InfoFilled", "data", "openeds", "computed", "mapState", "tags", "state", "window", "sessionStorage", "getItem", "methods", "mapMutations", "cmd", "this", "$router", "push", "removeItem", "clearEnvId", "for<PERSON>ach", "delTags", "__exports__", "tag", "_component_el_tag", "closable", "length", "onClose", "$event", "deletetag", "onClick", "type", "effect", "size", "_component_el_button", "closeAllTag", "_component_el_select", "env", "placeholder", "testEnvs", "_component_el_option", "id", "label", "value", "_component_el_tooltip", "content", "placement", "clickShowEnv", "_component_View", "_component_el_dialog", "showEnv", "footer", "_hoisted_10", "editEnv", "envInfo", "plain", "_component_el_descriptions", "border", "column", "debug_global_variable", "_component_el_descriptions_item", "global_variable", "View", "env_variable", "envId", "pro", "get", "set", "val", "selectEnv", "response", "$api", "getEnvInfo", "status", "selectEnvInfo", "LeftMenu", "Tags", "mapActions", "created", "getAllEnvs", "getAllPlan", "render", "_component_LoginBack", "src", "_imports_0", "_component_el_tabs", "activeName", "_component_el_tab_pane", "_component_el_form", "ref", "model", "loginForm", "rules", "rulesLogin", "_component_el_form_item", "prop", "_component_el_input", "password", "_component_el_checkbox", "clickRegister", "login", "createForm", "clearable", "readonly", "readonlyInput", "onFocus", "cancelReadOnly", "createClick", "cav", "timer", "onMounted", "draw3dAstronaut", "width", "parentNode", "clientWidth", "clientHeight", "dark_navy", "orange", "cream", "light_purple", "dark_purple", "cheese", "illo", "Zdog", "element", "dragRotate", "zoom", "body", "addTo", "color", "fill", "cornerRadius", "stroke", "translate", "z", "y", "arm", "x", "bubble_arm", "copy", "copyGraph", "rotate", "leg", "bubble_leg", "head", "depth", "helmet", "backface", "ear", "neck", "stripe_1", "animate", "updateRenderGraph", "requestAnimationFrame", "onUnmounted", "cancelAnimationFrame", "_renderSlot", "$slots", "_component_router_link", "to", "LoginBack", "project_id", "weChat_name", "required", "message", "userIcon", "Emojis", "params", "createUser", "ElNotification", "duration", "userAvatar", "randomIndex", "Math", "floor", "random", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "$refs", "loginRef", "validate", "async", "vaild", "result", "token", "localStorage", "JSON", "stringify", "mounted", "userinfo", "parse"], "sourceRoot": ""}