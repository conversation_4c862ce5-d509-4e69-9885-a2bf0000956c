{"version": 3, "file": "js/719.174a2baa.js", "mappings": "85BA4EMA,EAAc,sG,mCAtEpB,MAAMC,GAAQC,EAAAA,EAAAA,MAORC,IANSC,EAAAA,EAAAA,OAGGC,EAAAA,EAAAA,IAAS,IAAMJ,EAAMK,MAAMC,YAGvBC,EAAAA,EAAAA,IAAI,CACxBC,OAAQ,GACRC,MAAO,EACPC,QAAS,EACTC,KAAM,MAIFC,GAAYL,EAAAA,EAAAA,KAAI,GAGhBM,GAAiBC,EAAAA,EAAAA,IAAS,CAC9BC,KAAM,GACNC,OAAQ,GACRC,IAAK,GACLC,OAAQ,KAOJC,IAHoBZ,EAAAA,EAAAA,IAAI,KAGDA,EAAAA,EAAAA,IAAI,OAG3Ba,GAAUb,EAAAA,EAAAA,KAAI,GACdc,GAAYd,EAAAA,EAAAA,KAAI,GAChBe,GAAmBf,EAAAA,EAAAA,KAAI,GAGvBgB,GAAiBT,EAAAA,EAAAA,IAAS,CAC9BU,gBAAiB,CAAC,CAAEC,UAAU,EAAMC,QAAS,UAAWC,QAAS,WAI7DC,GAAWd,EAAAA,EAAAA,IAAS,CACxBe,aAAc,GACdC,OAAQ,CAAC,EACTC,QAAS,CAAC,EACVP,gBAAiB,GACjBQ,cAAe,GACfd,OAAQ,YAIJe,EAAgB,CACpB,CAAEC,MAAO,KAAMC,MAAO,IACtB,CAAED,MAAO,MAAOC,MAAO,OACvB,CAAED,MAAO,OAAQC,MAAO,QACxB,CAAED,MAAO,MAAOC,MAAO,OACvB,CAAED,MAAO,QAASC,MAAO,SACzB,CAAED,MAAO,SAAUC,MAAO,WAItBC,EAAgB,CACpB,CAAEF,MAAO,KAAMC,MAAO,IACtB,CAAED,MAAO,MAAOC,MAAO,WACvB,CAAED,MAAO,KAAMC,MAAO,QACtB,CAAED,MAAO,KAAMC,MAAO,SAqBlBE,IAPgB9B,EAAAA,EAAAA,IAAI,CACxB+B,MAAO,CAAEC,KAAM,EAAG5B,KAAM,IACxB6B,KAAM,CAAEzB,KAAM,OAAQoB,MAAO,KAC7BM,KAAM,CAAEC,GAAI,OAIS,CACrB,CACEA,GAAI,EACJ3B,KAAM,SACNC,OAAQ,MACRC,IAAK,aACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,UACRM,gBAAiB,KACjBQ,cAAe,MAEjB,CACEU,GAAI,EACJ3B,KAAM,QACNC,OAAQ,OACRC,IAAK,aACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,OACRM,gBAAiB,qDACjBQ,cAAe,sDAEjB,CACEU,GAAI,EACJ3B,KAAM,SACNC,OAAQ,MACRC,IAAK,iBACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,OACRM,gBAAiB,gCACjBQ,cAAe,iCAEjB,CACEU,GAAI,EACJ3B,KAAM,OACNC,OAAQ,SACRC,IAAK,iBACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,UACRM,gBAAiB,KACjBQ,cAAe,MAEjB,CACEU,GAAI,EACJ3B,KAAM,OACNC,OAAQ,OACRC,IAAK,aACL0B,aAAa,IAAIC,MAAOC,cACxB3B,OAAQ,OACRM,gBAAiB,iEACjBQ,cAAe,oEAKbc,EAAoBA,KACxBlC,EAAUuB,OAAQ,EAGlBY,WAAW,KACT,MAAMC,EAAeX,EAAeY,OAAOC,IACzC,MAAMC,GAAatC,EAAeE,MAAQmC,EAAKnC,KAAKqC,SAASvC,EAAeE,MACtEsC,GAAexC,EAAeG,QAAUkC,EAAKlC,SAAWH,EAAeG,OACvEsC,GAAYzC,EAAeI,KAAOiC,EAAKjC,IAAImC,SAASvC,EAAeI,KACnEsC,GAAe1C,EAAeK,QAAUgC,EAAKhC,SAAWL,EAAeK,OAC7E,OAAOiC,GAAaE,GAAeC,GAAYC,IAGjDrD,EAAciC,MAAQ,CACpB3B,OAAQwC,EACRvC,MAAOuC,EAAaQ,OACpB9C,QAAS,EACTC,KAAM,IAERC,EAAUuB,OAAQ,GACjB,MAiBCsB,EAAYA,KAChB5C,EAAeE,KAAO,GACtBF,EAAeG,OAAS,GACxBH,EAAeI,IAAM,GACrBJ,EAAeK,OAAS,GACxB4B,KAIIY,EAAaA,KACjBZ,KAIIa,GAASC,IACb1D,EAAciC,MAAMxB,KAAOiD,EAC3Bd,KAIIe,GAAgBD,IACpB1D,EAAciC,MAAMzB,QAAUkD,EAC9Bd,KAIIgB,GAAkBC,IACtB3C,EAAQe,OAAQ,EAChBhB,EAAqBgB,MAAQ4B,EAC7BnC,EAASC,aAAekC,EAAIrB,GAGxBqB,EAAIvC,gBACNI,EAASJ,gBAAkBuC,EAAIvC,gBAE/BI,EAASJ,gBAAkBzB,EAI7B6B,EAASI,cAAgB,GACzBJ,EAASV,OAAS,WAId8C,GAAcA,KAClBpD,EAAUuB,OAAQ,EAGlBY,WAAW,KAET,MAAMkB,EAAOC,KAAKC,SAAW,GAEzBF,GACFrC,EAASI,cAAgBJ,EAASJ,gBAClCI,EAASV,OAAS,OAClBkD,EAAAA,GAAUC,QAAQ,UAGlBzC,EAASI,cAAgB,wGAKzBJ,EAASV,OAAS,OAClBkD,EAAAA,GAAUE,MAAM,SAIlB,MAAMC,EAAQrE,EAAciC,MAAM3B,OAAOgE,UAAUtB,GAAQA,EAAKR,KAAOvB,EAAqBgB,MAAMO,KACnF,IAAX6B,IACFrE,EAAciC,MAAM3B,OAAO+D,GAAO/C,gBAAkBI,EAASJ,gBAC7DtB,EAAciC,MAAM3B,OAAO+D,GAAOvC,cAAgBJ,EAASI,cAC3D9B,EAAciC,MAAM3B,OAAO+D,GAAOrD,OAASU,EAASV,QAGtDN,EAAUuB,OAAQ,EAClBd,EAAUc,OAAQ,EAClBf,EAAQe,OAAQ,GACf,MAoDCsC,GAAkBV,IACjBA,EAAI/B,eAKTb,EAAqBgB,MAAQ4B,EAC7BnC,EAASJ,gBAAkBuC,EAAIvC,gBAC/BI,EAASI,cAAgB+B,EAAI/B,cAC7BX,EAAUc,OAAQ,GAPhBiC,EAAAA,GAAUM,QAAQ,YAWhBC,GAAcA,KAClBvD,EAAQe,OAAQ,EAChBd,EAAUc,OAAQ,EAClBb,EAAiBa,OAAQ,GAIrByC,GAAmBA,KACvBR,EAAAA,GAAUC,QAAQ,aAIdQ,GAAYA,KAChBjE,EAAUuB,OAAQ,EAElBY,WAAW,KACTqB,EAAAA,GAAUC,QAAQ,WAClBvB,IACAlC,EAAUuB,OAAQ,GACjB,M,OAIL2C,EAAAA,EAAAA,IAAU,KACRhC,M,wmBAKAiC,EAAAA,EAAAA,IA0FM,MA1FNC,EA0FM,EAzFJD,EAAAA,EAAAA,IAwFM,MAxFNE,EAwFM,C,eAvFJF,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAsBM,MAtBNG,EAsBM,EArBJH,EAAAA,EAAAA,IAEO,a,qBAFD,WACJI,EAAAA,EAAAA,IAAgJC,EAAA,C,WAA7HvE,EAAeE,K,qCAAfF,EAAeE,KAAIsE,GAAEC,YAAY,UAAUC,aAAa,MAAMC,UAAU,KAAMC,WAAW,EAAOC,MAAO,CAAAC,MAAA,U,0BAE5HZ,EAAAA,EAAAA,IAIO,a,qBAJD,WACJI,EAAAA,EAAAA,IAEYS,EAAA,C,WAFQ/E,EAAeG,O,qCAAfH,EAAeG,OAAMqE,GAAEC,YAAY,UAAWG,WAAW,EAAOC,MAAO,CAAAC,MAAA,U,kBAC9E,IAA6B,G,WAAxCE,EAAAA,EAAAA,IAAqGC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAA3E9D,EAARiB,IAAlBiC,EAAAA,EAAAA,IAAqGa,EAAA,CAA3DC,IAAK/C,EAAKf,MAAQD,MAAOgB,EAAKhB,MAAQC,MAAOe,EAAKf,O,4DAGhG4C,EAAAA,EAAAA,IAEO,a,uBAFD,WACJI,EAAAA,EAAAA,IAA+IC,EAAA,C,WAA5HvE,EAAeI,I,qCAAfJ,EAAeI,IAAGoE,GAAEC,YAAY,UAAUC,aAAa,MAAMC,UAAU,KAAMC,WAAW,EAAOC,MAAO,CAAAC,MAAA,U,0BAE3HZ,EAAAA,EAAAA,IAIO,a,uBAJD,WACJI,EAAAA,EAAAA,IAEYS,EAAA,C,WAFQ/E,EAAeK,O,qCAAfL,EAAeK,OAAMmE,GAAEC,YAAY,UAAWG,WAAW,EAAOC,MAAO,CAAAC,MAAA,U,kBAC9E,IAA6B,G,WAAxCE,EAAAA,EAAAA,IAAqGC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAA3E3D,EAARc,IAAlBiC,EAAAA,EAAAA,IAAqGa,EAAA,CAA3DC,IAAK/C,EAAKf,MAAQD,MAAOgB,EAAKhB,MAAQC,MAAOe,EAAKf,O,4DAIhG4C,EAAAA,EAAAA,IAGO,OAHPmB,EAGO,EAFLf,EAAAA,EAAAA,IAA2DgB,EAAA,CAA/CC,QAAO3C,EAAW4C,KAAK,W,kBAAU,IAAEC,EAAA,MAAAA,EAAA,M,QAAF,S,eAC7CnB,EAAAA,EAAAA,IAA0EgB,EAAA,CAA/DI,KAAK,UAAWH,QAAO1C,EAAY2C,KAAK,U,kBAAS,IAAEC,EAAA,MAAAA,EAAA,M,QAAF,S,mBAIhEvB,EAAAA,EAAAA,IAGM,MAHNyB,EAGM,EAFJrB,EAAAA,EAAAA,IAA8EgB,EAAA,CAAnEI,KAAK,UAAWH,QAAOvB,GAAWwB,KAAK,a,kBAAY,IAAIC,EAAA,MAAAA,EAAA,M,QAAJ,W,eAC9DnB,EAAAA,EAAAA,IAA0FgB,EAAA,CAA/EI,KAAK,UAAWH,QAAOxB,GAAkByB,KAAK,gB,kBAAe,IAAMC,EAAA,MAAAA,EAAA,M,QAAN,a,sCAI1EG,EAAAA,EAAAA,IA+CWC,EAAA,CA/CAC,KAAMzG,EAAAiC,MAAc3B,OAAQ,aAAW,OAA6BoG,OAAA,GAAOC,OAAA,I,kBACpF,IAIkB,EAJlB1B,EAAAA,EAAAA,IAIkB2B,EAAA,CAJD5E,MAAM,KAAK6E,MAAM,SAASpB,MAAM,M,CACpCqB,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,EACvBnC,EAAAA,EAAAA,IAAmC,aAAAoC,EAAAA,EAAAA,IAA1BD,EAAME,OAAS,GAAH,K,OAGzBjC,EAAAA,EAAAA,IAAiF2B,EAAA,CAAhE5E,MAAM,OAAOmF,KAAK,OAAON,MAAM,SAAS,8BACzD5B,EAAAA,EAAAA,IAoBkB2B,EAAA,CApBD5E,MAAM,OAAO6E,MAAM,SAASpB,MAAM,O,CACtCqB,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,EACvBnC,EAAAA,EAAAA,IAgBM,MAhBNuC,EAgBM,CAf6B,SAArBJ,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAA0B,EAAA,EADLpC,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,8BAEzB,QAArBkG,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAA6B,EAAA,EADLvC,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,8BAEzB,QAArBkG,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAA8B,EAAA,EADLxC,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,8BAEzB,UAArBkG,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAA+B,EAAA,EADLzC,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,8BAEzB,WAArBkG,EAAMnD,IAAI/C,S,WAAtB6E,EAAAA,EAAAA,IAEO,OAAAgC,EAAA,EADL1C,EAAAA,EAAAA,IAAoEqC,EAAA,CAA5DC,MAAM,UAAU9G,KAAK,S,kBAAQ,IAAsB,E,iBAAnBuG,EAAMnD,IAAI/C,QAAM,K,yCAKhEmE,EAAAA,EAAAA,IAAgF2B,EAAA,CAA/D5E,MAAM,OAAOmF,KAAK,MAAM,2BAAsBN,MAAM,YACrE5B,EAAAA,EAAAA,IAMkB2B,EAAA,CAND5E,MAAM,OAAO6E,MAAM,SAASpB,MAAM,O,CACtCqB,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,CACY,YAArBA,EAAMnD,IAAI7C,S,WAAxBuF,EAAAA,EAAAA,IAAsEe,EAAA,C,MAAxBjB,KAAK,Q,kBAAO,IAAGD,EAAA,MAAAA,EAAA,M,QAAH,U,eAClB,SAArBY,EAAMnD,IAAI7C,S,WAA7BuF,EAAAA,EAAAA,IAA0Ee,EAAA,C,MAA1BjB,KAAK,W,kBAAU,IAAED,EAAA,MAAAA,EAAA,M,QAAF,S,eACvB,SAArBY,EAAMnD,IAAI7C,S,WAA7BuF,EAAAA,EAAAA,IAAyEe,EAAA,C,MAAzBjB,KAAK,U,kBAAS,IAAED,EAAA,MAAAA,EAAA,M,QAAF,S,wCAGlEnB,EAAAA,EAAAA,IAIkB2B,EAAA,CAJD5E,MAAM,OAAO6E,MAAM,U,CACvBC,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,E,qBAChBtE,KAAKsE,EAAMnD,IAAIpB,aAAamF,kBAAc,K,OAGrD3C,EAAAA,EAAAA,IAKkB2B,EAAA,CALD5E,MAAM,KAAK6E,MAAM,SAASpB,MAAM,O,CACpCqB,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,EACvB/B,EAAAA,EAAAA,IAAgGgB,EAAA,CAApFC,QAAKf,GAAEvB,GAAeoD,EAAMnD,KAAMpD,KAAK,QAAQ4F,KAAK,UAAUwB,MAAA,I,kBAAM,IAAIzB,EAAA,MAAAA,EAAA,M,QAAJ,W,gCAChFnB,EAAAA,EAAAA,IAAgGgB,EAAA,CAApFC,QAAKf,GAAEZ,GAAeyC,EAAMnD,KAAMpD,KAAK,QAAQ4F,KAAK,UAAUwB,MAAA,I,kBAAM,IAAIzB,EAAA,MAAAA,EAAA,M,QAAJ,W,gEA5ClB1F,EAAAuB,UAkDpE4C,EAAAA,EAAAA,IAKM,MALNiD,EAKM,EAJJ7C,EAAAA,EAAAA,IAGgB8C,EAAA,CAHDC,WAAA,GAAWC,OAAO,0CAA2C,aAAY,CAAC,GAAI,GAAI,GAAI,KAClGC,aAAazE,GAAQ0E,gBAAgBxE,GAAeyE,MAAOpI,EAAAiC,MAAc1B,MACzE,eAAcP,EAAAiC,MAAczB,QAAS,YAAU,MAAM,YAAU,O,wCAOxEyE,EAAAA,EAAAA,IA6CYoD,GAAA,C,WA7CQnH,EAAAe,M,qCAAAf,EAAOe,MAAAkD,GAAEmD,MAAM,QAAQ7C,MAAM,MAAO,eAAchB,I,CAuCzD8D,QAAMxB,EAAAA,EAAAA,IACf,IAGO,EAHPlC,EAAAA,EAAAA,IAGO,OAHP2D,EAGO,EAFLvD,EAAAA,EAAAA,IAA8CgB,EAAA,CAAlCC,QAAOzB,IAAW,C,iBAAE,IAAE2B,EAAA,MAAAA,EAAA,M,QAAF,S,eAChCnB,EAAAA,EAAAA,IAA+DgB,EAAA,CAApDI,KAAK,UAAWH,QAAOpC,I,kBAAa,IAAIsC,EAAA,MAAAA,EAAA,M,QAAJ,W,mCAzCnD,IAqCM,CArCKnF,EAAAgB,Q,WAAX0D,EAAAA,EAAAA,IAqCM,MArCN8C,EAqCM,EApCJ5D,EAAAA,EAAAA,IAaM,MAbN6D,EAaM,EAZJzD,EAAAA,EAAAA,IAWkB0D,GAAA,CAXDL,MAAM,OAAQM,OAAQ,EAAGlC,OAAA,I,kBACxC,IAAyF,EAAzFzB,EAAAA,EAAAA,IAAyF4D,GAAA,CAAnE7G,MAAM,QAAM,C,iBAAC,IAA+B,E,iBAA5Bf,EAAAgB,MAAqBpB,MAAI,K,OAC/DoE,EAAAA,EAAAA,IAOuB4D,GAAA,CAPD7G,MAAM,QAAM,C,iBAChC,IAKS,EALTiD,EAAAA,EAAAA,IAKSqC,EAAA,CALAjB,KAAsC,QAAhCpF,EAAAgB,MAAqBnB,OAAmB,UAAqD,SAA3BG,EAAAgB,MAAqBnB,OAAM,UAAmE,QAA3BG,EAAAgB,MAAqBnB,OAAM,UAAkE,WAA3BG,EAAAgB,MAAqBnB,OAAM,iB,kBAI/O,IAAiC,E,iBAA9BG,EAAAgB,MAAqBnB,QAAM,K,0BAGlCmE,EAAAA,EAAAA,IAAkG4D,GAAA,CAA5E7G,MAAM,OAAQ8G,KAAM,G,kBAAG,IAA8B,E,iBAA3B7H,EAAAgB,MAAqBlB,KAAG,K,iBAI5E8D,EAAAA,EAAAA,IAoBM,MApBNkE,EAoBM,EAnBJ9D,EAAAA,EAAAA,IAkBU+D,GAAA,CAlBAC,MAAOvH,EAAWwH,MAAO7H,EAAgB,iBAAe,O,kBAChE,IAgBU,EAhBV4D,EAAAA,EAAAA,IAgBUkE,GAAA,CAhBD9C,KAAK,eAAa,C,iBACzB,IAQc,EARdpB,EAAAA,EAAAA,IAQcmE,GAAA,CARDpH,MAAM,QAAM,C,iBACvB,IAEe,EAFfiD,EAAAA,EAAAA,IAEeoE,GAAA,CAFDrH,MAAM,SAIpBiD,EAAAA,EAAAA,IAEeoE,GAAA,CAFDrH,MAAM,U,OAKtBiD,EAAAA,EAAAA,IAIcmE,GAAA,CAJDpH,MAAM,QAAM,C,iBACvB,IAEe,EAFfiD,EAAAA,EAAAA,IAEeoE,GAAA,CAFDrH,MAAM,SAASmF,KAAK,mB,kBAChC,IAAmH,EAAnHlC,EAAAA,EAAAA,IAAmHC,EAAA,C,WAAhGxD,EAASJ,gB,qCAATI,EAASJ,gBAAe6D,GAAEkB,KAAK,WAAYiD,KAAM,GAAIlE,YAAY,qB,0HAgBlGH,EAAAA,EAAAA,IAuFYoD,GAAA,C,WAvFQlH,EAAAc,M,qCAAAd,EAASc,MAAAkD,GAAEmD,MAAM,OAAO7C,MAAM,MAAO,eAAchB,I,CAiF1D8D,QAAMxB,EAAAA,EAAAA,IACf,IAGO,EAHPlC,EAAAA,EAAAA,IAGO,OAHP0E,EAGO,EAFLtE,EAAAA,EAAAA,IAA8CgB,EAAA,CAAlCC,QAAOzB,IAAW,C,iBAAE,IAAE2B,EAAA,MAAAA,EAAA,M,QAAF,S,eAChCnB,EAAAA,EAAAA,IAAwFgB,EAAA,CAA7EI,KAAK,UAAWH,QAAKE,EAAA,KAAAA,EAAA,GAAAjB,GAAEvB,GAAe3C,EAAAgB,S,kBAAuB,IAAImE,EAAA,MAAAA,EAAA,M,QAAJ,W,mCAnF5E,IA+EM,CA/EKnF,EAAAgB,Q,WAAX0D,EAAAA,EAAAA,IA+EM,MA/EN6D,EA+EM,EA9EJ3E,EAAAA,EAAAA,IAKM,MALN4E,EAKM,EAJJxE,EAAAA,EAAAA,IAGqByE,GAAA,CAHVpB,MAA2B,SAApB5G,EAASV,OAAoB,OAAS,OAC9CqF,KAA0B,SAApB3E,EAASV,OAAoB,UAAY,QAC/C2I,UAAU,EACX,gB,4BAGX9E,EAAAA,EAAAA,IAsEM,MAtEN+E,EAsEM,EArEJ3E,EAAAA,EAAAA,IAoEUkE,GAAA,CApED9C,KAAK,eAAa,C,iBACzB,IAec,EAfdpB,EAAAA,EAAAA,IAecmE,GAAA,CAfDpH,MAAM,QAAM,C,iBACvB,IAaS,EAbTiD,EAAAA,EAAAA,IAaS4E,GAAA,CAbAC,OAAQ,IAAE,C,iBACjB,IAKS,EALT7E,EAAAA,EAAAA,IAKS8E,GAAA,CALAjB,KAAM,IAAE,C,iBACf,IAGM,EAHNjE,EAAAA,EAAAA,IAGM,MAHNmF,EAGM,C,eAFJnF,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAA8D,MAA9DoF,GAA8DhD,EAAAA,EAAAA,IAAjCvF,EAASJ,iBAAe,O,OAGzD2D,EAAAA,EAAAA,IAKS8E,GAAA,CALAjB,KAAM,IAAE,C,iBACf,IAGM,EAHNjE,EAAAA,EAAAA,IAGM,MAHNqF,EAGM,C,eAFJrF,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAAmH,OAA9GsF,OAAKC,EAAAA,EAAAA,IAAA,CAAC,eAAc,iBAA+C,SAApB1I,EAASV,Y,QAAuBU,EAASI,eAAa,O,uBAMlHmD,EAAAA,EAAAA,IA4BcmE,GAAA,CA5BDpH,MAAM,QAAM,C,iBACvB,IA0BM,EA1BN6C,EAAAA,EAAAA,IA0BM,MA1BNwF,EA0BM,C,eAzBJxF,EAAAA,EAAAA,IAAe,UAAX,UAAM,IACqB,SAApBnD,EAASV,S,WAApB2E,EAAAA,EAAAA,IAEM,MAAA2E,EAAA,EADJrF,EAAAA,EAAAA,IAAiCsF,GAAA,CAAvBC,YAAY,gB,WAExB7E,EAAAA,EAAAA,IAoBM,MAAA8E,EAAA,EAnBJxF,EAAAA,EAAAA,IAAoEyE,GAAA,CAA1DpB,MAAM,OAAOjC,KAAK,UAAWsD,UAAU,EAAO,kBACxD9E,EAAAA,EAAAA,IAiBM,MAjBN6F,EAiBM,EAhBJzF,EAAAA,EAAAA,IAeWuB,EAAA,CAfAC,KAAM,C,qLAIdC,OAAA,I,kBACD,IAAyC,EAAzCzB,EAAAA,EAAAA,IAAyC2B,EAAA,CAAxB5E,MAAM,KAAKmF,KAAK,SACjClC,EAAAA,EAAAA,IAA+C2B,EAAA,CAA9B5E,MAAM,MAAMmF,KAAK,cAClClC,EAAAA,EAAAA,IAA6C2B,EAAA,CAA5B5E,MAAM,MAAMmF,KAAK,YAClClC,EAAAA,EAAAA,IAMkB2B,EAAA,CAND5E,MAAM,KAAKmF,KAAK,U,CACpBL,SAAOC,EAAAA,EAAAA,IAAEC,GAAK,CACY,cAArBA,EAAMnD,IAAI7C,S,WAAxBuF,EAAAA,EAAAA,IAA0Ee,EAAA,C,MAA1BjB,KAAK,U,kBAAS,IAAGD,EAAA,MAAAA,EAAA,M,QAAH,U,eACtB,YAArBY,EAAMnD,IAAI7C,S,WAA7BuF,EAAAA,EAAAA,IAA6Ee,EAAA,C,MAA1BjB,KAAK,W,kBAAU,IAAED,EAAA,MAAAA,EAAA,M,QAAF,S,4BAClEG,EAAAA,EAAAA,IAAyCe,EAAA,C,MAA1BjB,KAAK,W,kBAAU,IAAED,EAAA,MAAAA,EAAA,M,QAAF,S,+CAS5CnB,EAAAA,EAAAA,IAmBcmE,GAAA,CAnBDpH,MAAM,QAAM,C,iBACvB,IAiBM,EAjBN6C,EAAAA,EAAAA,IAiBM,MAjBN8F,EAiBM,EAhBJ1F,EAAAA,EAAAA,IAec2F,GAAA,M,iBAdZ,IAGmB,EAHnB3F,EAAAA,EAAAA,IAGmB4F,GAAA,CAHDC,UAAU,OAAOC,UAAU,MAAM1E,KAAK,W,kBACtD,IAAgB,C,eAAhBxB,EAAAA,EAAAA,IAAgB,SAAb,aAAS,KACZA,EAAAA,EAAAA,IAAyD,IAAzDmG,GAAyD/D,EAAAA,EAAAA,KAAA,IAA9BvE,MAAOkF,kBAAc,K,eAElD3C,EAAAA,EAAAA,IAImB4F,GAAA,CAJDC,UAAU,OAAOC,UAAU,MAAM1E,KAAK,Q,kBACtD,IAAkB,C,eAAlBxB,EAAAA,EAAAA,IAAkB,SAAf,eAAW,KACdA,EAAAA,EAAAA,IAA0C,SAAvC,SAAKoC,EAAAA,EAAAA,IAAGhG,EAAAgB,MAAqBlB,KAAG,IACnC8D,EAAAA,EAAAA,IAAgD,SAA7C,YAAQoC,EAAAA,EAAAA,IAAGhG,EAAAgB,MAAqBnB,QAAM,K,eAE3CmE,EAAAA,EAAAA,IAImB4F,GAAA,CAJAC,UAA+B,SAApBpJ,EAASV,OAAoB,OAAS,OAAQ+J,UAAU,MACpE1E,KAA0B,SAApB3E,EAASV,OAAoB,UAAY,U,kBAC/D,IAAoE,EAApE6D,EAAAA,EAAAA,IAAoE,UAAAoC,EAAAA,EAAAA,IAA1C,SAApBvF,EAASV,OAAoB,YAAc,cAA5B,IACrB6D,EAAAA,EAAAA,IAAyD,IAAzDoG,GAAyDhE,EAAAA,EAAAA,KAAA,IAA9BvE,MAAOkF,kBAAc,K,wHCpjBlE,MAAMsD,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://frontend-web/./src/views/Interface/BlindTest.vue", "webpack://frontend-web/./src/views/Interface/BlindTest.vue?58dc"], "sourcesContent": ["<script setup>\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { useStore } from 'vuex'\nimport { useRouter } from 'vue-router'\n\nconst store = useStore()\nconst router = useRouter()\n\n// 获取项目ID\nconst projectId = computed(() => store.state.projectId)\n\n// 盲测测试数据\nconst blindTestData = ref({\n  result: [],\n  count: 0,\n  current: 1,\n  size: 10\n})\n\n// 加载状态\nconst isLoading = ref(false)\n\n// 查询条件\nconst queryCondition = reactive({\n  name: '',\n  method: '',\n  url: '',\n  status: ''\n})\n\n// 测试结果详情\nconst testResultDetails = ref([])\n\n// 当前测试的接口\nconst currentTestInterface = ref(null)\n\n// 对话框显示状态\nconst testDlg = ref(false)\nconst resultDlg = ref(false)\nconst compareResultDlg = ref(false)\n\n// 表单校验规则\nconst rulesBlindTest = reactive({\n  expected_result: [{ required: true, message: '请输入预期结果', trigger: 'blur' }]\n})\n\n// 测试表单\nconst testForm = reactive({\n  interface_id: '',\n  params: {},\n  headers: {},\n  expected_result: '',\n  actual_result: '',\n  status: 'pending'\n})\n\n// 筛选方法类型列表\nconst methodOptions = [\n  { label: '全部', value: '' },\n  { label: 'GET', value: 'GET' },\n  { label: 'POST', value: 'POST' },\n  { label: 'PUT', value: 'PUT' },\n  { label: 'PATCH', value: 'PATCH' },\n  { label: 'DELETE', value: 'DELETE' }\n]\n\n// 测试状态选项\nconst statusOptions = [\n  { label: '全部', value: '' },\n  { label: '待测试', value: 'pending' },\n  { label: '通过', value: 'pass' },\n  { label: '失败', value: 'fail' }\n]\n\n// JSON样例数据\nconst jsonExample = `{\n  \"name\": \"API盲测示例\",\n  \"status\": 200,\n  \"data\": {\n    \"id\": 1001,\n    \"value\": \"示例值\"\n  }\n}`\n\n// 请求参数示例\nconst paramsExample = ref({\n  query: { page: 1, size: 10 },\n  body: { name: \"测试数据\", value: 123 },\n  path: { id: 456 }\n})\n\n// 模拟测试数据(用于演示)\nconst demoInterfaces = [\n  {\n    id: 1,\n    name: '获取用户列表',\n    method: 'GET',\n    url: '/api/users',\n    create_time: new Date().toISOString(),\n    status: 'pending',\n    expected_result: null,\n    actual_result: null\n  },\n  {\n    id: 2,\n    name: '创建新用户',\n    method: 'POST',\n    url: '/api/users',\n    create_time: new Date().toISOString(),\n    status: 'pass',\n    expected_result: '{\"code\":200,\"message\":\"用户创建成功\",\"data\":{\"id\":1003}}',\n    actual_result: '{\"code\":200,\"message\":\"用户创建成功\",\"data\":{\"id\":1003}}'\n  },\n  {\n    id: 3,\n    name: '更新用户信息',\n    method: 'PUT',\n    url: '/api/users/:id',\n    create_time: new Date().toISOString(),\n    status: 'fail',\n    expected_result: '{\"code\":200,\"message\":\"更新成功\"}',\n    actual_result: '{\"code\":400,\"message\":\"参数错误\"}'\n  },\n  {\n    id: 4,\n    name: '删除用户',\n    method: 'DELETE',\n    url: '/api/users/:id',\n    create_time: new Date().toISOString(),\n    status: 'pending',\n    expected_result: null,\n    actual_result: null\n  },\n  {\n    id: 5,\n    name: '用户登录',\n    method: 'POST',\n    url: '/api/login',\n    create_time: new Date().toISOString(),\n    status: 'pass',\n    expected_result: '{\"code\":200,\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"}',\n    actual_result: '{\"code\":200,\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"}'\n  }\n]\n\n// 加载接口数据\nconst loadBlindTestData = () => {\n  isLoading.value = true\n\n  // 这里模拟API调用，实际项目中应替换为真实的API调用\n  setTimeout(() => {\n    const filteredData = demoInterfaces.filter(item => {\n      const nameMatch = !queryCondition.name || item.name.includes(queryCondition.name)\n      const methodMatch = !queryCondition.method || item.method === queryCondition.method\n      const urlMatch = !queryCondition.url || item.url.includes(queryCondition.url)\n      const statusMatch = !queryCondition.status || item.status === queryCondition.status\n      return nameMatch && methodMatch && urlMatch && statusMatch\n    })\n\n    blindTestData.value = {\n      result: filteredData,\n      count: filteredData.length,\n      current: 1,\n      size: 10\n    }\n    isLoading.value = false\n  }, 500)\n\n  // 实际API调用可以参考下面的注释代码\n  /*\n  this.$api.getBlindTests(projectId.value, queryCondition).then(res => {\n    if (res.status === 200) {\n      blindTestData.value = res.data\n    }\n    isLoading.value = false\n  }).catch(err => {\n    console.error(err)\n    isLoading.value = false\n  })\n  */\n}\n\n// 重置查询条件\nconst resetForm = () => {\n  queryCondition.name = ''\n  queryCondition.method = ''\n  queryCondition.url = ''\n  queryCondition.status = ''\n  loadBlindTestData()\n}\n\n// 提交查询\nconst submitForm = () => {\n  loadBlindTestData()\n}\n\n// 分页大小变化\nconst sizes = (val) => {\n  blindTestData.value.size = val\n  loadBlindTestData()\n}\n\n// 分页当前页变化\nconst currentPages = (val) => {\n  blindTestData.value.current = val\n  loadBlindTestData()\n}\n\n// 开始进行API盲测\nconst startBlindTest = (row) => {\n  testDlg.value = true\n  currentTestInterface.value = row\n  testForm.interface_id = row.id\n\n  // 如果已经有测试结果，则加载之前的结果\n  if (row.expected_result) {\n    testForm.expected_result = row.expected_result\n  } else {\n    testForm.expected_result = jsonExample\n  }\n\n  // 清空上次的测试结果\n  testForm.actual_result = ''\n  testForm.status = 'pending'\n}\n\n// 执行API测试\nconst executeTest = () => {\n  isLoading.value = true\n\n  // 模拟API调用测试\n  setTimeout(() => {\n    // 模拟测试结果(50%概率通过，50%概率失败)\n    const pass = Math.random() > 0.5\n\n    if (pass) {\n      testForm.actual_result = testForm.expected_result\n      testForm.status = 'pass'\n      ElMessage.success('测试通过')\n    } else {\n      // 模拟失败结果\n      testForm.actual_result = `{\n        \"code\": 400,\n        \"message\": \"参数错误\",\n        \"errors\": [\"参数格式不正确\", \"缺少必要参数\"]\n      }`\n      testForm.status = 'fail'\n      ElMessage.error('测试失败')\n    }\n\n    // 更新界面数据\n    const index = blindTestData.value.result.findIndex(item => item.id === currentTestInterface.value.id)\n    if (index !== -1) {\n      blindTestData.value.result[index].expected_result = testForm.expected_result\n      blindTestData.value.result[index].actual_result = testForm.actual_result\n      blindTestData.value.result[index].status = testForm.status\n    }\n\n    isLoading.value = false\n    resultDlg.value = true\n    testDlg.value = false\n  }, 1000)\n\n  // 实际项目中的API调用可参考下面注释代码\n  /*\n  this.$api.runBlindTest(testForm).then(res => {\n    if (res.status === 200) {\n      testForm.actual_result = JSON.stringify(res.data.result, null, 2)\n\n      // 比较预期结果和实际结果\n      try {\n        const expected = JSON.parse(testForm.expected_result)\n        const actual = res.data.result\n\n        // 这里可以添加自定义的结果比较逻辑\n        const isEqual = JSON.stringify(expected) === JSON.stringify(actual)\n        testForm.status = isEqual ? 'pass' : 'fail'\n\n        if (isEqual) {\n          ElMessage.success('测试通过')\n        } else {\n          ElMessage.error('测试失败')\n        }\n      } catch (e) {\n        testForm.status = 'fail'\n        ElMessage.error('预期结果格式错误，无法比较')\n      }\n\n      // 更新界面数据\n      const index = blindTestData.value.result.findIndex(item => item.id === currentTestInterface.value.id)\n      if (index !== -1) {\n        blindTestData.value.result[index].expected_result = testForm.expected_result\n        blindTestData.value.result[index].actual_result = testForm.actual_result\n        blindTestData.value.result[index].status = testForm.status\n      }\n\n      resultDlg.value = true\n      testDlg.value = false\n    }\n    isLoading.value = false\n  }).catch(err => {\n    console.error(err)\n    testForm.status = 'fail'\n    testForm.actual_result = JSON.stringify(err.response.data, null, 2)\n    ElMessage.error('测试执行失败')\n    isLoading.value = false\n    resultDlg.value = true\n    testDlg.value = false\n  })\n  */\n}\n\n// 查看测试结果详情\nconst viewTestResult = (row) => {\n  if (!row.actual_result) {\n    ElMessage.warning('该接口尚未测试')\n    return\n  }\n\n  currentTestInterface.value = row\n  testForm.expected_result = row.expected_result\n  testForm.actual_result = row.actual_result\n  resultDlg.value = true\n}\n\n// 关闭对话框\nconst closeDialog = () => {\n  testDlg.value = false\n  resultDlg.value = false\n  compareResultDlg.value = false\n}\n\n// 导出测试报告\nconst exportTestReport = () => {\n  ElMessage.success('测试报告导出成功')\n}\n\n// 批量测试\nconst batchTest = () => {\n  isLoading.value = true\n\n  setTimeout(() => {\n    ElMessage.success('批量测试已完成')\n    loadBlindTestData()\n    isLoading.value = false\n  }, 2000)\n}\n\n// 页面加载时获取数据\nonMounted(() => {\n  loadBlindTestData()\n})\n</script>\n\n<template>\n  <div class=\"blind-test-container\">\n    <div class=\"title\">\n      <h2>接口盲测</h2>\n      <div class=\"query_model\">\n        <span>接口名称\n          <el-input v-model=\"queryCondition.name\" placeholder=\"请输入接口名称\" autocomplete=\"off\" maxlength=\"30\" :clearable=\"true\" :style=\"{ width: '200px' }\" />\n        </span>\n        <span>请求类型\n          <el-select v-model=\"queryCondition.method\" placeholder=\"请选择请求类型\" :clearable=\"true\" :style=\"{ width: '200px' }\">\n            <el-option v-for=\"item in methodOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\n          </el-select>\n        </span>\n        <span>接口地址\n          <el-input v-model=\"queryCondition.url\" placeholder=\"请输入接口地址\" autocomplete=\"off\" maxlength=\"30\" :clearable=\"true\" :style=\"{ width: '200px' }\" />\n        </span>\n        <span>测试状态\n          <el-select v-model=\"queryCondition.status\" placeholder=\"请选择测试状态\" :clearable=\"true\" :style=\"{ width: '200px' }\">\n            <el-option v-for=\"item in statusOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\n          </el-select>\n        </span>\n\n        <span class=\"buttons\">\n          <el-button @click=\"resetForm\" icon=\"Refresh\">重置</el-button>\n          <el-button type=\"success\" @click=\"submitForm\" icon=\"Search\">查询</el-button>\n        </span>\n      </div>\n\n      <div class=\"operation-buttons\">\n        <el-button type=\"primary\" @click=\"batchTest\" icon=\"VideoPlay\">批量盲测</el-button>\n        <el-button type=\"success\" @click=\"exportTestReport\" icon=\"DocumentCopy\">导出测试报告</el-button>\n      </div>\n\n      <!-- 接口列表 -->\n      <el-table :data=\"blindTestData.result\" empty-text=\"暂无数据\" v-loading=\"isLoading\" border stripe>\n        <el-table-column label=\"序号\" align=\"center\" width=\"60\">\n          <template #default=\"scope\">\n            <span>{{ scope.$index + 1 }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"接口名称\" prop=\"name\" align=\"center\" show-overflow-tooltip />\n        <el-table-column label=\"请求类型\" align=\"center\" width=\"100\">\n          <template #default=\"scope\">\n            <div style=\"font-weight: bold\">\n              <span v-if=\"scope.row.method === 'POST'\">\n                <el-tag color=\"#49cc90\" size=\"large\">{{ scope.row.method }}</el-tag>\n              </span>\n              <span v-if=\"scope.row.method === 'GET'\">\n                <el-tag color=\"#61affe\" size=\"large\">{{ scope.row.method }}</el-tag>\n              </span>\n              <span v-if=\"scope.row.method === 'PUT'\">\n                <el-tag color=\"#fca130\" size=\"large\">{{ scope.row.method }}</el-tag>\n              </span>\n              <span v-if=\"scope.row.method === 'PATCH'\">\n                <el-tag color=\"#50e3c2\" size=\"large\">{{ scope.row.method }}</el-tag>\n              </span>\n              <span v-if=\"scope.row.method === 'DELETE'\">\n                <el-tag color=\"#f93e3e\" size=\"large\">{{ scope.row.method }}</el-tag>\n              </span>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"接口地址\" prop=\"url\" show-overflow-tooltip align=\"center\" />\n        <el-table-column label=\"测试状态\" align=\"center\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag v-if=\"scope.row.status === 'pending'\" type=\"info\">待测试</el-tag>\n            <el-tag v-else-if=\"scope.row.status === 'pass'\" type=\"success\">通过</el-tag>\n            <el-tag v-else-if=\"scope.row.status === 'fail'\" type=\"danger\">失败</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"创建时间\" align=\"center\">\n          <template #default=\"scope\">\n            {{ new Date(scope.row.create_time).toLocaleString() }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" width=\"220\">\n          <template #default=\"scope\">\n            <el-button @click=\"startBlindTest(scope.row)\" size=\"small\" type=\"primary\" plain>开始盲测</el-button>\n            <el-button @click=\"viewTestResult(scope.row)\" size=\"small\" type=\"success\" plain>查看结果</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页组件 -->\n      <div class=\"pagination-block\">\n        <el-pagination background layout=\"total, sizes, prev, pager, next, jumper\" :page-sizes=\"[10, 20, 50, 100]\"\n          @size-change=\"sizes\" @current-change=\"currentPages\" :total=\"blindTestData.count\"\n          :current-page=\"blindTestData.current\" next-text=\"下一页\" prev-text=\"上一页\">\n        </el-pagination>\n      </div>\n    </div>\n  </div>\n\n  <!-- 盲测对话框 -->\n  <el-dialog v-model=\"testDlg\" title=\"API盲测\" width=\"60%\" :before-close=\"closeDialog\">\n    <div v-if=\"currentTestInterface\" class=\"blind-test-dialog\">\n      <div class=\"interface-info\">\n        <el-descriptions title=\"接口信息\" :column=\"2\" border>\n          <el-descriptions-item label=\"接口名称\">{{ currentTestInterface.name }}</el-descriptions-item>\n          <el-descriptions-item label=\"请求方式\">\n            <el-tag :type=\"currentTestInterface.method === 'GET' ? 'primary' :\n              currentTestInterface.method === 'POST' ? 'success' :\n              currentTestInterface.method === 'PUT' ? 'warning' :\n              currentTestInterface.method === 'DELETE' ? 'danger' : 'info'\">\n              {{ currentTestInterface.method }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"接口地址\" :span=\"2\">{{ currentTestInterface.url }}</el-descriptions-item>\n        </el-descriptions>\n      </div>\n\n      <div class=\"interface-test-form\">\n        <el-form :model=\"testForm\" :rules=\"rulesBlindTest\" label-position=\"top\">\n          <el-tabs type=\"border-card\">\n            <el-tab-pane label=\"请求参数\">\n              <el-form-item label=\"请求头\">\n<!--                <el-input v-model=\"JSON.stringify(paramsExample.value.query, null, 2)\" type=\"textarea\" :rows=\"5\" placeholder=\"请输入请求头参数（JSON格式）\"></el-input>-->\n              </el-form-item>\n\n              <el-form-item label=\"请求体\">\n<!--                <el-input v-model=\"JSON.stringify(paramsExample.value.body, null, 2)\" type=\"textarea\" :rows=\"5\" placeholder=\"请输入请求体参数（JSON格式）\"></el-input>-->\n              </el-form-item>\n            </el-tab-pane>\n\n            <el-tab-pane label=\"预期结果\">\n              <el-form-item label=\"预期响应结果\" prop=\"expected_result\">\n                <el-input v-model=\"testForm.expected_result\" type=\"textarea\" :rows=\"10\" placeholder=\"请输入预期响应结果（JSON格式）\"></el-input>\n              </el-form-item>\n            </el-tab-pane>\n          </el-tabs>\n        </el-form>\n      </div>\n    </div>\n    <template #footer>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"closeDialog\">取消</el-button>\n        <el-button type=\"primary\" @click=\"executeTest\">执行测试</el-button>\n      </span>\n    </template>\n  </el-dialog>\n\n  <!-- 测试结果对话框 -->\n  <el-dialog v-model=\"resultDlg\" title=\"测试结果\" width=\"70%\" :before-close=\"closeDialog\">\n    <div v-if=\"currentTestInterface\" class=\"test-result-container\">\n      <div class=\"result-header\">\n        <el-alert :title=\"testForm.status === 'pass' ? '测试通过' : '测试失败'\"\n                 :type=\"testForm.status === 'pass' ? 'success' : 'error'\"\n                 :closable=\"false\"\n                 show-icon />\n      </div>\n\n      <div class=\"result-content\">\n        <el-tabs type=\"border-card\">\n          <el-tab-pane label=\"结果对比\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <div class=\"result-panel\">\n                  <h3>预期结果</h3>\n                  <pre class=\"json-content\">{{ testForm.expected_result }}</pre>\n                </div>\n              </el-col>\n              <el-col :span=\"12\">\n                <div class=\"result-panel\">\n                  <h3>实际结果</h3>\n                  <pre class=\"json-content\" :class=\"{'error-content': testForm.status === 'fail'}\">{{ testForm.actual_result }}</pre>\n                </div>\n              </el-col>\n            </el-row>\n          </el-tab-pane>\n\n          <el-tab-pane label=\"差异分析\">\n            <div class=\"diff-analysis\">\n              <h3>差异分析结果</h3>\n              <div v-if=\"testForm.status === 'pass'\">\n                <el-empty description=\"没有发现差异\" />\n              </div>\n              <div v-else>\n                <el-alert title=\"发现差异\" type=\"warning\" :closable=\"false\" show-icon />\n                <div class=\"diff-items\">\n                  <el-table :data=\"[\n                    { key: 'code', expected: '200', actual: '400', status: 'different' },\n                    { key: 'message', expected: '成功', actual: '参数错误', status: 'different' },\n                    { key: 'data', expected: '对象', actual: '不存在', status: 'missing' }\n                  ]\" border>\n                    <el-table-column label=\"字段\" prop=\"key\" />\n                    <el-table-column label=\"预期值\" prop=\"expected\" />\n                    <el-table-column label=\"实际值\" prop=\"actual\" />\n                    <el-table-column label=\"状态\" prop=\"status\">\n                      <template #default=\"scope\">\n                        <el-tag v-if=\"scope.row.status === 'different'\" type=\"danger\">不一致</el-tag>\n                        <el-tag v-else-if=\"scope.row.status === 'missing'\" type=\"warning\">缺失</el-tag>\n                        <el-tag v-else type=\"success\">一致</el-tag>\n                      </template>\n                    </el-table-column>\n                  </el-table>\n                </div>\n              </div>\n            </div>\n          </el-tab-pane>\n\n          <el-tab-pane label=\"测试日志\">\n            <div class=\"test-logs\">\n              <el-timeline>\n                <el-timeline-item timestamp=\"测试开始\" placement=\"top\" type=\"primary\">\n                  <p>开始执行API盲测</p>\n                  <p class=\"log-time\">{{ new Date().toLocaleString() }}</p>\n                </el-timeline-item>\n                <el-timeline-item timestamp=\"发送请求\" placement=\"top\" type=\"info\">\n                  <p>向服务器发送API请求</p>\n                  <p>URL: {{ currentTestInterface.url }}</p>\n                  <p>Method: {{ currentTestInterface.method }}</p>\n                </el-timeline-item>\n                <el-timeline-item :timestamp=\"testForm.status === 'pass' ? '测试通过' : '测试失败'\" placement=\"top\"\n                                 :type=\"testForm.status === 'pass' ? 'success' : 'danger'\">\n                  <p>{{ testForm.status === 'pass' ? '响应结果与预期一致' : '响应结果与预期不一致' }}</p>\n                  <p class=\"log-time\">{{ new Date().toLocaleString() }}</p>\n                </el-timeline-item>\n              </el-timeline>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </div>\n    <template #footer>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"closeDialog\">关闭</el-button>\n        <el-button type=\"primary\" @click=\"startBlindTest(currentTestInterface)\">重新测试</el-button>\n      </span>\n    </template>\n  </el-dialog>\n</template>\n\n<style scoped>\n.blind-test-container {\n  padding: 16px;\n  height: calc(100vh - 65px);\n  overflow: auto;\n}\n\n.title {\n  margin-bottom: 20px;\n}\n\n.title h2 {\n  margin-bottom: 20px;\n  font-weight: 600;\n  font-size: 20px;\n  color: #303133;\n}\n\n.query_model {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  margin-bottom: 20px;\n  background-color: #f5f7fa;\n  padding: 15px;\n  border-radius: 4px;\n}\n\n.query_model > span {\n  display: flex;\n  align-items: center;\n  margin-right: 15px;\n  margin-bottom: 10px;\n}\n\n.operation-buttons {\n  margin-bottom: 15px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.buttons {\n  margin-left: auto;\n}\n\n.pagination-block {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n}\n\n/* 盲测对话框样式 */\n.blind-test-dialog {\n  padding: 10px;\n}\n\n.interface-info {\n  margin-bottom: 20px;\n}\n\n.interface-test-form {\n  margin-top: 20px;\n}\n\n/* 测试结果对话框样式 */\n.test-result-container {\n  padding: 10px;\n}\n\n.result-header {\n  margin-bottom: 20px;\n}\n\n.result-content {\n  margin-top: 20px;\n}\n\n.result-panel {\n  border: 1px solid #e6e6e6;\n  border-radius: 4px;\n  padding: 15px;\n  height: 400px;\n  overflow: auto;\n}\n\n.result-panel h3 {\n  margin-top: 0;\n  margin-bottom: 10px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.json-content {\n  white-space: pre-wrap;\n  font-family: 'Courier New', Courier, monospace;\n  font-size: 14px;\n  background-color: #f9f9f9;\n  padding: 10px;\n  border-radius: 4px;\n  line-height: 1.5;\n  overflow: auto;\n}\n\n.error-content {\n  background-color: #fff5f5;\n  border-left: 3px solid #f56c6c;\n}\n\n.diff-analysis {\n  padding: 10px;\n}\n\n.diff-items {\n  margin-top: 15px;\n}\n\n.test-logs {\n  padding: 10px;\n}\n\n.log-time {\n  color: #909399;\n  font-size: 12px;\n}\n\n/* 添加响应式设计 */\n@media screen and (max-width: 992px) {\n  .query_model {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .query_model > span {\n    width: 100%;\n    margin-bottom: 10px;\n  }\n\n  .buttons {\n    margin-left: 0;\n    width: 100%;\n    display: flex;\n    justify-content: flex-end;\n  }\n}\n</style>", "import script from \"./BlindTest.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./BlindTest.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./BlindTest.vue?vue&type=style&index=0&id=19d702e0&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-19d702e0\"]])\n\nexport default __exports__"], "names": ["jsonExample", "store", "useStore", "blindTestData", "useRouter", "computed", "state", "projectId", "ref", "result", "count", "current", "size", "isLoading", "queryCondition", "reactive", "name", "method", "url", "status", "currentTestInterface", "testDlg", "resultDlg", "compareResultDlg", "rulesBlindTest", "expected_result", "required", "message", "trigger", "testForm", "interface_id", "params", "headers", "actual_result", "methodOptions", "label", "value", "statusOptions", "demoInterfaces", "query", "page", "body", "path", "id", "create_time", "Date", "toISOString", "loadBlindTestData", "setTimeout", "filteredData", "filter", "item", "nameMatch", "includes", "methodMatch", "urlMatch", "statusMatch", "length", "resetForm", "submitForm", "sizes", "val", "currentPages", "startBlindTest", "row", "executeTest", "pass", "Math", "random", "ElMessage", "success", "error", "index", "findIndex", "viewTestResult", "warning", "closeDialog", "exportTestReport", "batchTest", "onMounted", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_input", "$event", "placeholder", "autocomplete", "maxlength", "clearable", "style", "width", "_component_el_select", "_createElementBlock", "_Fragment", "_renderList", "_component_el_option", "key", "_hoisted_4", "_component_el_button", "onClick", "icon", "_cache", "type", "_hoisted_5", "_createBlock", "_component_el_table", "data", "border", "stripe", "_component_el_table_column", "align", "default", "_withCtx", "scope", "_toDisplayString", "$index", "prop", "_hoisted_6", "_hoisted_7", "_component_el_tag", "color", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "toLocaleString", "plain", "_hoisted_12", "_component_el_pagination", "background", "layout", "onSizeChange", "onCurrentChange", "total", "_component_el_dialog", "title", "footer", "_hoisted_16", "_hoisted_13", "_hoisted_14", "_component_el_descriptions", "column", "_component_el_descriptions_item", "span", "_hoisted_15", "_component_el_form", "model", "rules", "_component_el_tabs", "_component_el_tab_pane", "_component_el_form_item", "rows", "_hoisted_30", "_hoisted_17", "_hoisted_18", "_component_el_alert", "closable", "_hoisted_19", "_component_el_row", "gutter", "_component_el_col", "_hoisted_20", "_hoisted_21", "_hoisted_22", "class", "_normalizeClass", "_hoisted_23", "_hoisted_24", "_component_el_empty", "description", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_component_el_timeline", "_component_el_timeline_item", "timestamp", "placement", "_hoisted_28", "_hoisted_29", "__exports__"], "sourceRoot": ""}