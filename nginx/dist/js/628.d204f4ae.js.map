{"version": 3, "file": "js/628.d204f4ae.js", "mappings": "oNACOA,MAAM,kB,GACJA,MAAM,oB,GACJA,MAAM,kB,GACJA,MAAM,e,GAKJA,MAAM,c,GAERA,MAAM,kB,GAmCVA,MAAM,kB,GAW2CC,MAAA,wC,GAM/CD,MAAM,a,GASNA,MAAM,kB,GAoBJE,KAAK,SAASF,MAAM,e,GAGhBA,MAAM,wB,SAE8BA,MAAM,c,GACtCA,MAAM,a,GACHA,MAAM,YAAaC,MAAO,CAAAE,MAAA,sB,GAE1BH,MAAM,c,aAELC,MAAA,mB,aAGAA,MAAA,mB,aAGAA,MAAA,mB,aAGAA,MAAA,mB,aAGAA,MAAA,mB,aAGAA,MAAA,8B,GAKJD,MAAM,e,GAYNA,MAAM,Y,GACHA,MAAM,a,SAKsBA,MAAM,c,GACrCA,MAAM,a,GACHA,MAAM,YAAaC,MAAO,CAAAE,MAAA,sB,GAG7BH,MAAM,0B,GACJA,MAAM,uB,iBAkBRA,MAAM,a,GACHA,MAAM,YAAaC,MAAO,CAAAE,MAAA,qB,GAG7BH,MAAM,e,GACJA,MAAM,wB,aAWNA,MAAM,Q,GACJA,MAAM,gB,GAINA,MAAM,gB,aAcRA,MAAM,Q,GACJA,MAAM,gB,IAKNA,MAAM,gB,qBAkByBA,MAAM,c,IACzCA,MAAM,a,IACHA,MAAM,YAAaC,MAAO,CAAAE,MAAA,qB,IAG7BH,MAAM,e,IAYJA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,IACNA,MAAM,Y,UAKwBA,MAAM,c,IACtCA,MAAM,a,IACHA,MAAM,YAAaC,MAAO,CAAAE,MAAA,sB,IAG7BH,MAAM,e,IAQiDA,MAAM,e,UAG5BA,MAAM,c,IACvCA,MAAM,a,IACHA,MAAM,YAAaC,MAAO,CAAAE,MAAA,sB,IAG7BH,MAAM,+B,IACJA,MAAM,gB,IAgBZA,MAAM,gB,IA0BhBC,MAAA,kB,IAEkCA,MAAA,mB,IACDA,MAAA,mB,IACAA,MAAA,qB,IACAA,MAAA,mB,gCAuBIA,MAAA,mB,UACYA,MAAA,mB,UACpCA,MAAA,mB,w+BAtVlBG,EAAAA,EAAAA,IA6VM,MA7VNC,EA6VM,EA5VJC,EAAAA,EAAAA,IAuCM,MAvCNC,EAuCM,EAtCJD,EAAAA,EAAAA,IAqCM,MArCNE,EAqCM,EApCJF,EAAAA,EAAAA,IAMM,MANNG,EAMM,EALJC,EAAAA,EAAAA,IAGYC,GAAA,CAHDX,MAAM,cAAeY,QAAOC,GAAAC,Q,kBACrC,IAA2B,EAA3BJ,EAAAA,EAAAA,IAA2BK,GAAA,M,iBAAlB,IAAQ,EAARL,EAAAA,EAAAA,IAAQM,M,qBACjBV,EAAAA,EAAAA,IAAe,YAAT,MAAE,M,6BAEVA,EAAAA,EAAAA,IAAwE,MAAxEW,GAAwEC,EAAAA,EAAAA,IAA/B,OAA+B,KAAxCC,KAAgB,OAAS,QAArB,MAEtCb,EAAAA,EAAAA,IA4BM,MA5BNc,EA4BM,EA3BJV,EAAAA,EAAAA,IAKaW,GAAA,CALDC,QAAQ,SAASC,UAAU,MAAOC,SAAUC,GAAAC,U,kBACtD,IAGY,CAHMD,GAAAC,U,4BAAlBC,EAAAA,EAAAA,IAGYhB,GAAA,C,MAHgBX,MAAM,gBAAiBY,QAAKgB,EAAA,KAAAA,EAAA,GAAAC,GAAEhB,GAAAiB,eAAc,K,kBACtE,IAAgC,EAAhCpB,EAAAA,EAAAA,IAAgCK,GAAA,M,iBAAvB,IAAa,EAAbL,EAAAA,EAAAA,IAAaqB,M,qBACtBzB,EAAAA,EAAAA,IAAe,YAAT,MAAE,M,uCAGZI,EAAAA,EAAAA,IAKaW,GAAA,CALDC,QAAQ,SAASC,UAAU,MAAOC,UAAWC,GAAAC,U,kBACvD,IAGY,CAHKD,GAAAC,W,WAAjBC,EAAAA,EAAAA,IAGYhB,GAAA,C,MAHeX,MAAM,gBAAiBY,QAAKgB,EAAA,KAAAA,EAAA,GAAAC,GAAEhB,GAAAiB,eAAc,K,kBACrE,IAA8B,EAA9BpB,EAAAA,EAAAA,IAA8BK,GAAA,M,iBAArB,IAAW,EAAXL,EAAAA,EAAAA,IAAWsB,M,qBACpB1B,EAAAA,EAAAA,IAAe,YAAT,MAAE,M,uDAGZI,EAAAA,EAAAA,IAKaW,GAAA,CALDC,QAAQ,WAAWC,UAAU,O,kBACvC,IAGY,EAHZb,EAAAA,EAAAA,IAGYC,GAAA,CAHDX,MAAM,gBAAgBiC,KAAK,UAAWrB,QAAOC,GAAAqB,S,kBACtD,IAAgC,EAAhCxB,EAAAA,EAAAA,IAAgCK,GAAA,M,iBAAvB,IAAa,EAAbL,EAAAA,EAAAA,IAAayB,M,qBACtB7B,EAAAA,EAAAA,IAAe,YAAT,MAAE,M,qCAGZI,EAAAA,EAAAA,IAQaW,GAAA,CARAC,QAAuB,OAAhB,KAAOH,KAAgB,OAAS,QAASI,UAAU,O,kBACrE,IAMY,EANZb,EAAAA,EAAAA,IAMYC,GAAA,CALVX,MAAM,4BACNiC,KAAK,UACJrB,QAAKgB,EAAA,KAAAA,EAAA,GAAAC,GAAgB,OAAhB,KAAOV,KAAgBN,GAAAuB,eAAiBvB,GAAAwB,gB,kBAC9C,IAA+B,EAA/B3B,EAAAA,EAAAA,IAA+BK,GAAA,M,iBAAtB,IAAY,EAAZL,EAAAA,EAAAA,IAAY4B,M,qBACrBhC,EAAAA,EAAAA,IAAe,YAAT,MAAE,M,2CAOlBI,EAAAA,EAAAA,IAwQU6B,GAAA,CAxQDC,OAAQ,IAAE,C,iBAEnB,IAeS,EAfT9B,EAAAA,EAAAA,IAeS+B,GAAA,CAfAC,KAAM,GAAC,C,iBAChB,IAaM,EAbNpC,EAAAA,EAAAA,IAaM,MAbNqC,EAaM,EAZJjC,EAAAA,EAAAA,IAWMkC,GAAA,CAXIC,MAAOpB,GAAAqB,SAAYC,MAAOtB,GAAAuB,UAAWC,IAAI,UAAU,cAAY,OAAOhD,MAAA,uB,kBAChF,IAEe,EAFfS,EAAAA,EAAAA,IAEewC,GAAA,CAFDC,MAAM,OAAQC,KAAK,OAAOC,KAAK,Q,kBAC3C,IAA2D,EAA3D3C,EAAAA,EAAAA,IAA2D4C,GAAA,C,WAAvC7B,GAAAqB,SAASS,K,qCAAT9B,GAAAqB,SAASS,KAAI1B,GAAG2B,YAAY,W,gCAElD9C,EAAAA,EAAAA,IAEewC,GAAA,CAFDE,KAAK,aAAaD,MAAM,OAAOE,KAAK,Q,kBAChD,IAAqD,EAArD3C,EAAAA,EAAAA,IAAqD4C,GAAA,C,WAAlC7B,GAAAqB,SAASW,W,qCAAThC,GAAAqB,SAASW,WAAU5B,GAAIL,SAAA,I,gCAE/Cd,EAAAA,EAAAA,IAEkBwC,GAAA,CAFJC,MAAM,OAAOC,KAAK,Q,kBAC3B,IAAyE,EAAzE1C,EAAAA,EAAAA,IAAyE4C,GAAA,CAA/DrB,KAAK,W,WAAoBR,GAAAqB,SAASY,K,qCAATjC,GAAAqB,SAASY,KAAI7B,GAAI2B,YAAY,S,gCAElE9C,EAAAA,EAAAA,IAAyIwC,GAAA,CAA3HC,MAAM,QAAQ,cAAY,Q,kBAAO,IAA2E,EAA3E7C,EAAAA,EAAAA,IAA2E,MAA3EqD,GAA2EzC,EAAAA,EAAAA,IAA1BO,GAAAqB,SAASc,WAAS,K,6CAKpHlD,EAAAA,EAAAA,IAoPS+B,GAAA,CApPAC,KAAM,IAAE,C,iBACf,IAOM,EAPNpC,EAAAA,EAAAA,IAOM,MAPNuD,EAOM,EANJnD,EAAAA,EAAAA,IAAyHoD,GAAA,CAAjH9D,MAAM,eAAeG,MAAM,UAAUF,MAAA,sCAAyCW,QAAOC,GAAAkD,a,kBAAa,IAAMnC,EAAA,MAAAA,EAAA,M,QAAN,a,6BAC1GlB,EAAAA,EAAAA,IAAgIoD,GAAA,CAAxH9D,MAAM,eAAeG,MAAM,UAAUF,MAAA,sCAAyCW,QAAKgB,EAAA,KAAAA,EAAA,GAAAC,GAAEhB,GAAAmD,cAAc,Q,kBAAO,IAAKpC,EAAA,MAAAA,EAAA,M,QAAL,Y,eAClHlB,EAAAA,EAAAA,IAAmIoD,GAAA,CAA3H9D,MAAM,eAAeG,MAAM,YAAYF,MAAA,sCAAyCW,QAAKgB,EAAA,KAAAA,EAAA,GAAAC,GAAEhB,GAAAmD,cAAc,S,kBAAQ,IAAKpC,EAAA,MAAAA,EAAA,M,QAAL,Y,eACrHlB,EAAAA,EAAAA,IAAsIoD,GAAA,CAA9H9D,MAAM,eAAeG,MAAM,YAAYF,MAAA,sCAAyCW,QAAKgB,EAAA,KAAAA,EAAA,GAAAC,GAAEhB,GAAAmD,cAAc,Y,kBAAW,IAAKpC,EAAA,MAAAA,EAAA,M,QAAL,Y,eACxHlB,EAAAA,EAAAA,IAAoIoD,GAAA,CAA5H9D,MAAM,eAAeG,MAAM,YAAYF,MAAA,sCAAyCW,QAAKgB,EAAA,KAAAA,EAAA,GAAAC,GAAEhB,GAAAmD,cAAc,S,kBAAQ,IAAMpC,EAAA,MAAAA,EAAA,M,QAAN,a,eACrHlB,EAAAA,EAAAA,IAAoIoD,GAAA,CAA5H9D,MAAM,eAAeG,MAAM,YAAYF,MAAA,sCAAyCW,QAAKgB,EAAA,MAAAA,EAAA,IAAAC,GAAEhB,GAAAmD,cAAc,U,kBAAS,IAAKpC,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAExHlB,EAAAA,EAAAA,IA0OeuD,GAAA,CA1ODC,OAAO,sBAAsBC,OAAA,GAAQC,SAAQvD,GAAAwD,c,kBAC3D,IAwOM,EAxON/D,EAAAA,EAAAA,IAwOM,MAxONgE,EAwOM,G,WAvON3C,EAAAA,EAAAA,IAsOU4C,GAAA,CArOPC,KAAM/C,GAAAgD,MACNC,MAAO7D,GAAA8D,aACRC,UAAA,GACCC,IAAKpD,GAAAqD,QACL,qBAAoBrD,GAAAC,SACpB,wBAAsB,EACtBqD,YAAYlE,GAAAmE,gBACZ,aAAYnE,GAAAoE,UACZC,WAAWrE,GAAAsE,gBACXC,gBAAiBvE,GAAAwE,gBACjBC,gBAAiBzE,GAAA0E,gBACjBC,gBAAiB3E,GAAA4E,gBACjBC,eAAgB7E,GAAA8E,eAChBC,cAAe/E,GAAAgF,cAChB7F,MAAM,e,kBAGR,EADkB8F,OAAKtB,UAAI,CACZA,EAAKuB,W,WAApBpE,EAAAA,EAAAA,IAkNUqE,GAAA,C,MAlNqBhG,OAAKiG,EAAAA,EAAAA,IAAA,0BAA6BzB,EAAKuB,SAAS9D,U,kBAC7E,IAgNM,EAhNN3B,EAAAA,EAAAA,IAgNM,MAhNN4F,EAgNM,EA/MLxF,EAAAA,EAAAA,IA8MS6B,GAAA,CA9MAC,OAAQ,GAAIP,KAAK,OAAOkE,MAAM,SAASC,QAAQ,U,kBACvD,IA+LS,EA/LT1F,EAAAA,EAAAA,IA+LS+B,GAAA,CA/LAC,KAAM,GAAI1C,MAAM,qB,kBACvB,IA6LM,EA7LNM,EAAAA,EAAAA,IA6LM,MA7LN+F,EA6LM,CA3LyB,QAAlB7B,EAAKuB,SAAS9D,O,WAAzB7B,EAAAA,EAAAA,IAyCM,MAzCNkG,EAyCM,EAxCJhG,EAAAA,EAAAA,IAuBM,MAvBNiG,EAuBM,EAtBJjG,EAAAA,EAAAA,IAA4G,OAA5GkG,GAA4GtF,EAAAA,EAAAA,IAAzCL,GAAA4F,aAAaX,EAAKY,OAAQZ,IAAI,IACjGpF,EAAAA,EAAAA,IAAuCoD,GAAA,CAA/B3D,MAAM,WAAS,C,iBAAC,IAAMyB,EAAA,MAAAA,EAAA,M,QAAN,a,eACxBtB,EAAAA,EAAAA,IAmBO,OAnBPqG,EAmBO,CAlBgC,SAAzBnC,EAAKuB,SAASa,S,WAA1BxG,EAAAA,EAAAA,IAEO,OAAAyG,EAAA,EADLvG,EAAAA,EAAAA,IAAyD,IAAzDwG,GAAyD5F,EAAAA,EAAAA,IAA3BsD,EAAKuB,SAASa,QAAM,O,eAEf,QAAzBpC,EAAKuB,SAASa,S,WAA1BxG,EAAAA,EAAAA,IAEO,OAAA2G,EAAA,EADLzG,EAAAA,EAAAA,IAAyD,IAAzD0G,GAAyD9F,EAAAA,EAAAA,IAA3BsD,EAAKuB,SAASa,QAAM,O,eAEf,QAAzBpC,EAAKuB,SAASa,S,WAA1BxG,EAAAA,EAAAA,IAEO,OAAA6G,EAAA,EADL3G,EAAAA,EAAAA,IAAyD,IAAzD4G,GAAyDhG,EAAAA,EAAAA,IAA3BsD,EAAKuB,SAASa,QAAM,O,eAEf,UAAzBpC,EAAKuB,SAASa,S,WAA1BxG,EAAAA,EAAAA,IAEO,OAAA+G,EAAA,EADL7G,EAAAA,EAAAA,IAAyD,IAAzD8G,GAAyDlG,EAAAA,EAAAA,IAA3BsD,EAAKuB,SAASa,QAAM,O,eAEf,WAAzBpC,EAAKuB,SAASa,S,WAA1BxG,EAAAA,EAAAA,IAEO,OAAAiH,EAAA,EADL/G,EAAAA,EAAAA,IAAyD,IAAzDgH,GAAyDpG,EAAAA,EAAAA,IAA3BsD,EAAKuB,SAASa,QAAM,O,eAEf,SAAzBpC,EAAKuB,SAASa,S,WAA1BxG,EAAAA,EAAAA,IAEO,OAAAmH,EAAA,EADLjH,EAAAA,EAAAA,IAAoE,IAApEkH,GAAoEtG,EAAAA,EAAAA,IAA3BsD,EAAKuB,SAASa,QAAM,O,oBAKnEtG,EAAAA,EAAAA,IAcM,MAdNmH,EAcM,EAbJ/G,EAAAA,EAAAA,IAUcgH,GAAA,CAVDC,QAAQ,QAAS/G,QAAKgB,EAAA,MAAAA,EAAA,KAAAgG,EAAAA,EAAAA,IAAN,OAAW,Y,CAG3BC,UAAQC,EAAAA,EAAAA,IACjB,IAImB,EAJnBpH,EAAAA,EAAAA,IAImBqH,GAAA,M,iBAHG,IAAmC,G,aAArD3H,EAAAA,EAAAA,IAEmB4H,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFcpH,GAAAqH,oBAARC,K,WAAzBxG,EAAAA,EAAAA,IAEmByG,GAAA,CAFoCvD,IAAKsD,EAAKE,GAAIC,QAAQ,MAAO1H,QAAKiB,GAAEhB,GAAA0H,SAASJ,EAAK3D,EAAKuB,W,kBAC5G,IAAa,E,iBAAXoC,EAAK5E,MAAI,K,+DALjB,IAA8H,CAAtD,IAA3CiF,OAAOC,KAAKjE,EAAKuB,SAAS2C,MAAMC,S,WAA7DhH,EAAAA,EAAAA,IAA8HhB,GAAA,C,MAAnHsB,KAAK,OAA4DrB,QAAKgB,EAAA,MAAAA,EAAA,KAAAgG,EAAAA,EAAAA,IAAN,OAAW,Y,kBAAC,IAA2B,E,iBAAzBpD,EAAKuB,SAAS2C,KAAKnF,MAAI,K,yBAChH5B,EAAAA,EAAAA,IAA2DhB,GAAA,C,MAAzCsB,KAAK,OAASrB,QAAKgB,EAAA,MAAAA,EAAA,KAAAgG,EAAAA,EAAAA,IAAN,OAAW,Y,kBAAC,IAAIhG,EAAA,MAAAA,EAAA,M,QAAJ,W,6BAS/CtB,EAAAA,EAAAA,IAA+C,IAA/CsI,GAA+C1H,EAAAA,EAAAA,IAAxBsD,EAAKuB,SAAS8C,KAAG,IACxCvI,EAAAA,EAAAA,IAAsD,OAAtDwI,GAAsD5H,EAAAA,EAAAA,IAA5BsD,EAAKuB,SAASxC,MAAI,S,eAKnB,OAAlBiB,EAAKuB,SAAS9D,O,WAAzB7B,EAAAA,EAAAA,IAoBM,MApBN2I,EAoBM,EAnBJzI,EAAAA,EAAAA,IAGM,MAHN0I,EAGM,EAFJ1I,EAAAA,EAAAA,IAA4G,OAA5G2I,GAA4G/H,EAAAA,EAAAA,IAAzCL,GAAA4F,aAAaX,EAAKY,OAAQZ,IAAI,IACjGpF,EAAAA,EAAAA,IAAgDoD,GAAA,CAAxC3D,MAAM,qBAAmB,C,iBAAC,IAAKyB,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAEpCtB,EAAAA,EAAAA,IAcM,MAdN4I,EAcM,EAbJ5I,EAAAA,EAAAA,IAYM,MAZN6I,EAYM,EAXJzI,EAAAA,EAAAA,IAAkG4C,GAAA,CAAxFtD,MAAM,YAAYwD,YAAY,gB,WAAyBgB,EAAKuB,SAASzE,QAAQ8H,S,yBAAtB5E,EAAKuB,SAASzE,QAAQ8H,SAAQvH,G,8CAC/FnB,EAAAA,EAAAA,IAOY2I,GAAA,C,WAPQ7E,EAAKuB,SAASzE,QAAQgI,a,yBAAtB9E,EAAKuB,SAASzE,QAAQgI,aAAYzH,EAAE2B,YAAY,MAAMvD,MAAA,iB,kBAEtE,IAAuB,G,aADzBG,EAAAA,EAAAA,IAKE4H,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJexG,GAAA8H,QAARpB,K,WADTxG,EAAAA,EAAAA,IAKE6H,GAAA,CAHC3E,IAAKsD,EAAKsB,MACVtG,MAAOgF,EAAKhF,MACZsG,MAAOtB,EAAKsB,O,qFAGjB/I,EAAAA,EAAAA,IAAmF4C,GAAA,CAAzEtD,MAAM,YAAYwD,YAAY,I,WAAagB,EAAKuB,SAASzE,QAAQmI,M,yBAAtBjF,EAAKuB,SAASzE,QAAQmI,MAAK5H,G,8CAChFnB,EAAAA,EAAAA,IAA2E4C,GAAA,CAAjEtD,MAAM,YAAYwD,YAAY,K,WAAcgB,EAAKuB,SAASrC,K,yBAAdc,EAAKuB,SAASrC,KAAI7B,G,oEAMjD,QAAlB2C,EAAKuB,SAAS9D,O,WAAzB7B,EAAAA,EAAAA,IAcM,O,MAdiCJ,MAAM,aAAcY,QAAKiB,GAAEhB,GAAA6I,aAAalF,EAAKuB,W,EAClFzF,EAAAA,EAAAA,IAGM,MAHNqJ,EAGM,EAFJrJ,EAAAA,EAAAA,IAA2G,OAA3GsJ,GAA2G1I,EAAAA,EAAAA,IAAzCL,GAAA4F,aAAaX,EAAKY,OAAQZ,IAAI,IAChGpF,EAAAA,EAAAA,IAA+CoD,GAAA,CAAvC3D,MAAM,oBAAkB,C,iBAAC,IAAKyB,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAEnCtB,EAAAA,EAAAA,IAQM,MARNuJ,EAQM,EAPJvJ,EAAAA,EAAAA,IAMM,MANNwJ,EAMM,EALJpJ,EAAAA,EAAAA,IAIiBqJ,GAAA,C,WAJQvF,EAAKuB,SAASzE,QAAQ0I,O,yBAAtBxF,EAAKuB,SAASzE,QAAQ0I,OAAMnI,EAAGjB,QAAKgB,EAAA,MAAAA,EAAA,KAAAgG,EAAAA,EAAAA,IAAN,OAAW,WAAC5H,MAAM,e,kBACvE,IAAqD,EAArDU,EAAAA,EAAAA,IAAqDuJ,GAAA,CAA3C9G,MAAM,QAAQsG,MAAM,S,kBAAQ,IAAI7H,EAAA,MAAAA,EAAA,M,QAAJ,W,eACtClB,EAAAA,EAAAA,IAAkDuJ,GAAA,CAAxC9G,MAAM,MAAMsG,MAAM,O,kBAAM,IAAK7H,EAAA,MAAAA,EAAA,M,QAAL,Y,eAClClB,EAAAA,EAAAA,IAAiEuJ,GAAA,CAAvD9G,MAAM,QAAQsG,MAAM,QAAQjI,SAAA,I,kBAAS,IAAOI,EAAA,MAAAA,EAAA,M,QAAP,c,2FAK1B,QAAlB4C,EAAKuB,SAAS9D,MAAgBuC,EAAKuB,SAASmE,M,WAAvD9J,EAAAA,EAAAA,IA0CM,OA1CuDyE,IAAKL,EAAK6D,GAAKzH,QAAKgB,EAAA,MAAAA,EAAA,KAAAgG,EAAAA,EAAAA,IAAN,OAAW,WAAC5H,MAAM,gB,CACpD,UAA5BwE,EAAKuB,SAASzE,QAAQ0I,QAAgD,KAA5BxF,EAAKuB,SAASzE,QAAQ0I,S,WAA3E5J,EAAAA,EAAAA,IAkBM,MAAA+J,EAAA,EAjBJ7J,EAAAA,EAAAA,IAgBM,MAhBN8J,EAgBM,EAfJ9J,EAAAA,EAAAA,IAGM,MAHN+J,EAGM,C,eAFJ/J,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVI,EAAAA,EAAAA,IAA+F4C,GAAA,C,WAA5EkB,EAAKuB,SAASzE,QAAQgJ,W,yBAAtB9F,EAAKuB,SAASzE,QAAQgJ,WAAUzI,EAAE5B,MAAA,gBAAqBuD,YAAY,Q,gDAExFlD,EAAAA,EAAAA,IAUM,MAVNiK,EAUM,C,eATJjK,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVI,EAAAA,EAAAA,IAME8J,GAAA,C,WALShG,EAAKuB,SAASzE,QAAQmJ,c,yBAAtBjG,EAAKuB,SAASzE,QAAQmJ,cAAa5I,EAC3C6I,IAAK,EACLC,IAAK,IACN,oBAAkB,QAClBnH,YAAY,K,4DAEdlD,EAAAA,EAAAA,IAAc,YAAR,KAAC,Y,eAI0B,QAA5BkE,EAAKuB,SAASzE,QAAQ0I,S,WAAjC5J,EAAAA,EAAAA,IAoBM,MAAAwK,EAAA,EAnBJtK,EAAAA,EAAAA,IAkBM,MAlBNuK,EAkBM,EAjBJvK,EAAAA,EAAAA,IAIM,MAJNwK,EAIM,EAHJpK,EAAAA,EAAAA,IAAkG4C,GAAA,CAAxFrD,MAAA,gBAAqBuD,YAAY,S,WAAkBgB,EAAKuB,SAASzE,QAAQyJ,a,yBAAtBvG,EAAKuB,SAASzE,QAAQyJ,aAAYlJ,G,4DAC/FvB,EAAAA,EAAAA,IAAS,SAAN,MAAE,KACLI,EAAAA,EAAAA,IAAqG4C,GAAA,CAA3FrD,MAAA,gBAAqBuD,YAAY,gB,WAAyBgB,EAAKuB,SAASzE,QAAQ8H,S,yBAAtB5E,EAAKuB,SAASzE,QAAQ8H,SAAQvH,G,gDAEpGvB,EAAAA,EAAAA,IAWM,MAXN0K,GAWM,C,eAVJ1K,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVI,EAAAA,EAAAA,IAOE8J,GAAA,C,WANShG,EAAKuB,SAASzE,QAAQmJ,c,yBAAtBjG,EAAKuB,SAASzE,QAAQmJ,cAAa5I,EAC3C6I,IAAK,EACLC,IAAK,IACNtH,KAAK,QACL,oBAAkB,QAClBG,YAAY,K,4DAEdlD,EAAAA,EAAAA,IAAc,YAAR,KAAC,Y,eAI0B,UAA5BkE,EAAKuB,SAASzE,QAAQ0I,S,WAAjC5J,EAAAA,EAAAA,IAAsF,MAAA6K,GAAArJ,EAAA,MAAAA,EAAA,MAAnCtB,EAAAA,EAAAA,IAA6B,OAAxBN,MAAM,QAAO,SAAK,Q,kCAI/C,WAAlBwE,EAAKuB,SAAS9D,O,WAAzB7B,EAAAA,EAAAA,IAYM,MAZN8K,GAYM,EAXJ5K,EAAAA,EAAAA,IAGM,MAHN6K,GAGM,EAFJ7K,EAAAA,EAAAA,IAA2G,OAA3G8K,IAA2GlK,EAAAA,EAAAA,IAAzCL,GAAA4F,aAAaX,EAAKY,OAAQZ,IAAI,IAChGpF,EAAAA,EAAAA,IAA+CoD,GAAA,CAAvC3D,MAAM,oBAAkB,C,iBAAC,IAAKyB,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAEnCtB,EAAAA,EAAAA,IAMM,MANN+K,GAMM,CALY7G,EAAKuB,SAASuF,W,WAA9B3J,EAAAA,EAAAA,IAA4J2B,GAAA,C,iBAA3GkB,EAAKuB,SAASxC,K,yBAAdiB,EAAKuB,SAASxC,KAAI1B,EAAG0J,OAAI1J,GAAEhB,GAAA2K,cAAchH,EAAKuB,UAAW9C,IAAI,QAAQwI,UAAU,KAAM7K,QAAKgB,EAAA,MAAAA,EAAA,KAAAgG,EAAAA,EAAAA,IAAN,OAAW,Y,oEAChJjG,EAAAA,EAAAA,IAGYhB,GAAA,C,MAHMX,MAAM,gBAAgB0L,MAAA,GAAMzJ,KAAK,OAAQrB,QAAK,C,GAAEC,GAAA8K,aAAanH,EAAKuB,U,uBAAW,OAAW,a,kBACxG,IAAsB,E,iBAApBvB,EAAKuB,SAASxC,MAAM,IACtB,IAAA7C,EAAAA,EAAAA,IAA2BK,GAAA,M,iBAAlB,IAAQ,EAARL,EAAAA,EAAAA,IAAQkL,M,+DAIvBlL,EAAAA,EAAAA,IASS6B,GAAA,CATAC,OAAQ,GAAiE5B,QAAKgB,EAAA,MAAAA,EAAA,KAAAgG,EAAAA,EAAAA,IAAN,OAAW,WAAC5H,MAAM,iB,kBACjG,IAAyG,EAAzGU,EAAAA,EAAAA,IAAyG+B,GAAA,CAAhGC,KAAM,IAAE,C,iBAAE,IAA6E,EAA7EhC,EAAAA,EAAAA,IAA6EmL,GAAA,C,WAA5DrH,EAAKuB,SAAS+F,O,yBAAdtH,EAAKuB,SAAS+F,OAAMjK,EAAEkK,KAAK,SAASC,MAAM,U,2DAC9EtL,EAAAA,EAAAA,IAMS+B,GAAA,CANAC,KAAM,GAAC,C,iBACd,IAA6B,EAA7BhC,EAAAA,EAAAA,IAA6BuL,GAAA,M,iBAAjB,IAAIrK,EAAA,MAAAA,EAAA,M,QAAJ,W,eACZtB,EAAAA,EAAAA,IAA0I,MAA1I4L,GAA0I,EAApHxL,EAAAA,EAAAA,IAA8GC,GAAA,CAAnGsB,KAAK,UAAUoB,KAAK,OAAOqI,MAAA,GAAO9K,QAAKiB,GAAEhB,GAAAsL,iBAAiB3H,EAAKuB,SAAQ,S,kBAAU,IAAMnE,EAAA,MAAAA,EAAA,M,QAAN,a,kCAClHtB,EAAAA,EAAAA,IAAyI,MAAzI8L,GAAyI,EAAnH1L,EAAAA,EAAAA,IAA6GC,GAAA,CAAlGsB,KAAK,UAAUoB,KAAK,OAAOqI,MAAA,GAAO9K,QAAKiB,GAAEhB,GAAAsL,iBAAiB3H,EAAKuB,SAAQ,Q,kBAAS,IAAMnE,EAAA,MAAAA,EAAA,M,QAAN,a,kCACjHtB,EAAAA,EAAAA,IAAyI,MAAzI+L,GAAyI,EAAnH3L,EAAAA,EAAAA,IAA6GC,GAAA,CAAlGsB,KAAK,UAAUoB,KAAK,OAAOqI,MAAA,GAAO9K,QAAKiB,GAAEhB,GAAAsL,iBAAiB3H,EAAKuB,SAAQ,Q,kBAAS,IAAMnE,EAAA,MAAAA,EAAA,M,QAAN,a,kCACjHtB,EAAAA,EAAAA,IAA0I,MAA1IgM,GAA0I,EAApH5L,EAAAA,EAAAA,IAA8GC,GAAA,CAAnGsB,KAAK,UAAUoB,KAAK,OAAOqI,MAAA,GAAO9K,QAAKiB,GAAEhB,GAAAsL,iBAAiB3H,EAAKuB,SAAQ,Q,kBAAS,IAAOnE,EAAA,MAAAA,EAAA,M,QAAP,c,kEAPtE,WAAlB4C,EAAKuB,SAAS9D,MAAmBuC,EAAKuB,SAASmE,OAY/C,QAAlB1F,EAAKuB,SAAS9D,O,WAAzB7B,EAAAA,EAAAA,IAYM,MAZNmM,GAYM,EAXJjM,EAAAA,EAAAA,IAGM,MAHNkM,GAGM,EAFJlM,EAAAA,EAAAA,IAA4G,OAA5GmM,IAA4GvL,EAAAA,EAAAA,IAAzCL,GAAA4F,aAAaX,EAAKY,OAAQZ,IAAI,IACjGpF,EAAAA,EAAAA,IAAiDoD,GAAA,CAAzC3D,MAAM,qBAAmB,C,iBAAC,IAAMyB,EAAA,MAAAA,EAAA,M,QAAN,a,iBAEpCtB,EAAAA,EAAAA,IAMM,MANNoM,GAMM,CALYlI,EAAKuB,SAASuF,W,WAA9B3J,EAAAA,EAAAA,IAA4J2B,GAAA,C,iBAA3GkB,EAAKuB,SAASxC,K,yBAAdiB,EAAKuB,SAASxC,KAAI1B,EAAG0J,OAAI1J,GAAEhB,GAAA2K,cAAchH,EAAKuB,UAAW9C,IAAI,QAAQwI,UAAU,KAAM7K,QAAKgB,EAAA,MAAAA,EAAA,KAAAgG,EAAAA,EAAAA,IAAN,OAAW,Y,oEAChJjG,EAAAA,EAAAA,IAGYhB,GAAA,C,MAHMX,MAAM,gBAAgB0L,MAAA,GAAMzJ,KAAK,OAAQrB,QAAK,C,GAAEC,GAAA8K,aAAanH,EAAKuB,U,uBAAW,OAAW,a,kBACxG,IAAsB,E,iBAApBvB,EAAKuB,SAASxC,MAAM,IACtB,IAAA7C,EAAAA,EAAAA,IAA2BK,GAAA,M,iBAAlB,IAAQ,EAARL,EAAAA,EAAAA,IAAQkL,M,+DAIvBtL,EAAAA,EAAAA,IAAwG,MAAxGqM,GAAwG/K,EAAA,MAAAA,EAAA,MAAtBtB,EAAAA,EAAAA,IAAgB,SAAb,aAAS,K,YAA/D,QAAlBkE,EAAKuB,SAAS9D,MAAgBuC,EAAKuB,SAASmE,OAG5B,SAAlB1F,EAAKuB,SAAS9D,O,WAAzB7B,EAAAA,EAAAA,IAkBM,MAlBNwM,GAkBM,EAjBJtM,EAAAA,EAAAA,IAGM,MAHNuM,GAGM,EAFJvM,EAAAA,EAAAA,IAA4G,OAA5GwM,IAA4G5L,EAAAA,EAAAA,IAAzCL,GAAA4F,aAAaX,EAAKY,OAAQZ,IAAI,IACjGpF,EAAAA,EAAAA,IAAgDoD,GAAA,CAAxC3D,MAAM,qBAAmB,C,iBAAC,IAAKyB,EAAA,MAAAA,EAAA,M,QAAL,Y,iBAEpCtB,EAAAA,EAAAA,IAYM,MAZNyM,GAYM,EAXJzM,EAAAA,EAAAA,IAUM,MAVN0M,GAUM,EATJtM,EAAAA,EAAAA,IAOE8J,GAAA,C,WANShG,EAAKuB,SAASzE,QAAQ2L,K,yBAAtBzI,EAAKuB,SAASzE,QAAQ2L,KAAIpL,EAClC6I,IAAK,EACLC,IAAK,IACNtH,KAAK,QACL,oBAAkB,QAClBG,YAAY,K,4DAEdlD,EAAAA,EAAAA,IAAc,YAAR,KAAC,Y,+BAMjBI,EAAAA,EAAAA,IAYS+B,GAAA,CAZAC,KAAM,GAAC,C,iBACd,IAUM,EAVNpC,EAAAA,EAAAA,IAUM,MAVN4M,GAUM,EATJxM,EAAAA,EAAAA,IAMEyM,GAAA,C,WALS3I,EAAK4I,O,yBAAL5I,EAAK4I,OAAMvL,EACpB,mBACAwB,KAAK,UACJzC,QAAKiB,GAAEhB,GAAAwM,YAAY7I,GACpBvE,MAAA,sE,wDAEFS,EAAAA,EAAAA,IAAiGC,GAAA,CAAtF0C,KAAK,UAAUiK,KAAK,WAAY1M,QAAKiB,GAAEhB,GAAA0M,SAAS/I,GAAOgJ,OAAA,GAAOxN,MAAM,iB,qBAC/EU,EAAAA,EAAAA,IAA4GC,GAAA,CAAjG0C,KAAK,UAAUiK,KAAK,SAASrL,KAAK,SAAUrB,QAAKiB,GAAEhB,GAAA4M,QAAQjJ,GAAOgJ,OAAA,GAAOxN,MAAM,iB,mTAYrFyB,GAAAiM,Y,WAAf/L,EAAAA,EAAAA,IAAkGgM,GAAA,C,MAAvEC,aAAY/M,GAAAgN,gBAAkBC,aAAajN,GAAAkN,kB,0DAEtErN,EAAAA,EAAAA,IAA+NsN,GAAA,C,WAA3MvM,GAAAwM,Y,uCAAAxM,GAAAwM,YAAWpM,GAAK,eAAa,EAAOwB,KAAK,O,kBAAM,IAAgJ,EAAhJ3C,EAAAA,EAAAA,IAAgJwN,GAAA,CAAnIjL,IAAI,WAAYkL,cAAatN,GAAAuN,YAAeC,aAAc5M,GAAA4M,aAAeC,QAAS7M,GAAA6M,QAAUrO,MAAA,oB,6EAE7KS,EAAAA,EAAAA,IAqCUsN,GAAA,C,WArCUvM,GAAA8M,U,uCAAA9M,GAAA8M,UAAS1M,GAAG,eAAa,EAAOwB,KAAK,O,kBAC1D,IAmCM,EAnCN/C,EAAAA,EAAAA,IAmCM,MAnCNkO,GAmCM,EAlCL9N,EAAAA,EAAAA,IAKkB+N,GAAA,CALDC,MAAM,OAAOC,OAAA,GAAQC,OAAQ,EAAG3O,MAAA,yB,kBAChD,IAA+G,EAA/GS,EAAAA,EAAAA,IAA+GmO,GAAA,CAAzF1L,MAAM,MAAI,C,iBAAE,IAAsD,EAAtD7C,EAAAA,EAAAA,IAAsD,IAAtDwO,IAAsD5N,EAAAA,EAAAA,IAAzBO,GAAAsN,eAAeC,KAAG,K,OACjFtO,EAAAA,EAAAA,IAAkHmO,GAAA,CAA5F1L,MAAM,MAAI,C,iBAAC,IAA0D,EAA1D7C,EAAAA,EAAAA,IAA0D,IAA1D2O,IAA0D/N,EAAAA,EAAAA,IAA7BO,GAAAsN,eAAeG,SAAO,K,OACpFxO,EAAAA,EAAAA,IAAiHmO,GAAA,CAA3F1L,MAAM,MAAI,C,iBAAC,IAAyD,EAAzD7C,EAAAA,EAAAA,IAAyD,IAAzD6O,IAAyDjO,EAAAA,EAAAA,IAA1BO,GAAAsN,eAAeK,MAAI,K,OACnF1O,EAAAA,EAAAA,IAAgHmO,GAAA,CAA1F1L,MAAM,MAAI,C,iBAAC,IAAwD,EAAxD7C,EAAAA,EAAAA,IAAwD,IAAxD+O,IAAwDnO,EAAAA,EAAAA,IAA3BO,GAAAsN,eAAeO,OAAK,K,6BAEnFhP,EAAAA,EAAAA,IAA8D,OAAzDL,MAAA,sCAAuC,EAACK,EAAAA,EAAAA,IAAW,SAAR,U,KAChDI,EAAAA,EAAAA,IA0BeuD,GAAA,CA1BDC,OAAO,uBAAqB,C,iBACzC,IAwBW,EAxBXxD,EAAAA,EAAAA,IAwBW6O,GAAA,CAxBA/K,KAAM/C,GAAAsN,eAAeS,MAAOvP,MAAA,eAAoB,aAAW,Q,kBACrE,IAIkB,EAJlBS,EAAAA,EAAAA,IAIkB+O,GAAA,CAJDxN,KAAK,UAAQ,CAClByN,SAAO5H,EAAAA,EAAAA,IAC4BpD,GADrB,EACxBhE,EAAAA,EAAAA,IAA6CiP,GAAA,CAAhCC,OAAQlL,EAAMmL,K,4BAG7BnP,EAAAA,EAAAA,IAA2C+O,GAAA,CAA1BtM,MAAM,MAAMC,KAAK,UAClC1C,EAAAA,EAAAA,IAIuB+O,GAAA,CAJNtM,MAAM,OAAOC,KAAK,U,CACjBsM,SAAO5H,EAAAA,EAAAA,IACoDpD,GAD7C,CACS,QAAnBA,EAAMmL,IAAI5N,O,WAAtB7B,EAAAA,EAAAA,IAAmE,OAAA0P,IAAA5O,EAAAA,EAAAA,IAA1BwD,EAAMmL,IAAIjJ,QAAM,K,wBAGnElG,EAAAA,EAAAA,IAIuB+O,GAAA,CAJNtM,MAAM,QAAQC,KAAK,e,CAClBsM,SAAO5H,EAAAA,EAAAA,IACyDpD,GADlD,CACS,QAAnBA,EAAMmL,IAAI5N,O,WAAtB7B,EAAAA,EAAAA,IAAwE,OAAA2P,IAAA7O,EAAAA,EAAAA,IAA/BwD,EAAMmL,IAAIG,aAAW,K,wBAGxEtP,EAAAA,EAAAA,IAMkB+O,GAAA,CANDtM,MAAM,OAAOC,KAAK,QAAQ,YAAU,Q,CACzCsM,SAAO5H,EAAAA,EAAAA,IACwEpD,GADjE,CACO,MAAnBA,EAAMmL,IAAII,Q,WAAtB7P,EAAAA,EAAAA,IAAyF,OAAzF8P,IAAyFhP,EAAAA,EAAAA,IAAzBwD,EAAMmL,IAAII,OAAK,IACpC,MAAnBvL,EAAMmL,IAAII,Q,WAA3B7P,EAAAA,EAAAA,IAA8F,OAA9F+P,IAA8FjP,EAAAA,EAAAA,IAAzBwD,EAAMmL,IAAII,OAAK,M,WAC3F7P,EAAAA,EAAAA,IAA+D,OAA/DgQ,IAA+DlP,EAAAA,EAAAA,IAAzBwD,EAAMmL,IAAII,OAAK,M,kMAoB5D,IACEI,WAAW,CACTC,QAAO,KACPC,YAAW,KACXC,WAAU,KACVC,OAAMA,GAAAA,GAERjM,IAAAA,GACE,MAAM,CACJ1B,SAAS,CACPS,KAAK,GACLE,WAAW,GACXC,KAAK,GACLE,UAAU,EACV8M,QAAQ,IAEV1N,UAAW,CACbO,KAAM,CACL,CACCoN,UAAU,EACVC,QAAS,UACTjJ,QAAS,SAGXlE,WAAY,CACX,CACCkN,UAAU,EACVC,QAAS,QACTjJ,QAAS,UAITlD,MAAM,GACNiJ,WAAU,EACVO,aAAa,EACbI,aAAc,GACdC,SAAS,EACTC,WAAU,EACVQ,eAAe,GACf8B,eAAe,CACbtN,KAAM,GACNtB,KAAM,GACNX,QAAS,CAAC,EACVwK,OAAO,GACPpI,KAAK,GACLgN,QAAQ,IAEVnH,QAAS,CACL,CAAEE,MAAO,QAAStG,MAAO,MACzB,CAAEsG,MAAO,WAAYtG,MAAO,OAC5B,CAAEsG,MAAO,WAAYtG,MAAO,MAC5B,CAAEsG,MAAO,cAAetG,MAAO,OAC/B,CAAEsG,MAAO,cAAetG,MAAO,MAC/B,CAAEsG,MAAO,WAAYtG,MAAO,MAC5B,CAAEsG,MAAO,qBAAsBtG,MAAO,QACtC,CAAEsG,MAAO,kBAAmBtG,MAAO,QACnC,CAAEsG,MAAO,QAAStG,MAAO,KACzB,CAAEsG,MAAO,WAAYtG,MAAO,OAEhC2N,KAAK,GACLhM,QAAS,GACTpD,UAAU,EAEZ,EAEFqP,SAAU,KACPC,EAAAA,GAAAA,IAAS,CAAC,MAAM,OAAO,QAAQ,aAChC9I,mBAAAA,GACE,MAAM+I,EAAc,IAAIC,KAAKC,UAE7B,OADAF,EAAYG,QAAQ,CAAE1I,KAAK,WAAYnF,KAAM,SACtC0N,CACT,EACAI,QAAAA,GACD,OAAOC,OAAOC,eAAeC,QAAQ,WACtC,EACE7M,YAAAA,GACE,MAAO,CACL8M,SAAU,WACVtO,MAAO,OAEX,GAEFuO,MAAO,CACLjN,MAAO,CACLkN,MAAM,IAGVC,QAAS,KACJC,EAAAA,GAAAA,IAAa,CAAC,WAAW,kBAC9BpL,YAAAA,CAAaC,EAAQZ,GACnB,MAAMgM,EAAQpL,EAAOqL,WAAWC,QAAQlM,GACxC,OAAOgM,EAAQ,CACjB,EAEC,iBAAMzP,GACL6O,KAAKe,MAAMC,QAAQC,SAASC,UAE5B,IAAKC,EAAO,OACZ,MAAMC,EAAS,IAAIpB,KAAKpO,UACxBwP,EAAO5B,QAAUQ,KAAKG,SACtBiB,EAAO7O,WAAayN,KAAKqB,IAAIlK,GAC7B,MAAMmK,QAAiBtB,KAAKuB,KAAKC,eAAeJ,GACtB,MAApBE,EAASpF,SACXuF,EAAAA,GAAAA,IAAU,CACR1Q,KAAM,UACN2O,QAAS,OACTgC,SAAU,OAGd,EAGH,kBAAMxQ,GACL8O,KAAKe,MAAMC,QAAQC,SAASC,UAE5B,IAAKC,EAAO,OACZ,MAAMC,EAAS,IAAIpB,KAAKpO,UACxBwP,EAAO7O,WAAayN,KAAKqB,IAAIlK,GAC7BiK,EAAOO,SAAW3B,KAAKG,SACvBiB,EAAOQ,YAAc5B,KAAK6B,OAAOC,iBAC1BV,EAAOW,mBACPX,EAAO5B,eACP4B,EAAOY,QACd,MAAMV,QAAiBtB,KAAKuB,KAAKU,eAAeb,EAAOjK,GAAIiK,GAC/B,MAApBE,EAASpF,UACXuF,EAAAA,GAAAA,IAAU,CACR1Q,KAAM,UACN2O,QAAS,OACTgC,SAAU,MAEd1B,KAAKkC,iBAGV,EAGD,kBAAMA,GACJ,MAAMC,EAAsBnC,KAAKzM,MAC9B6O,OAAOC,GAAQA,EAAKxN,UAAmC,QAAvBwN,EAAKxN,SAAS9D,MAC9CuR,IAAID,GAAQA,GACTf,QAAiBtB,KAAKuB,KAAKgB,oBAAoBJ,GAC3B,MAApBb,EAASpF,QACX8D,KAAKwC,YAAYxC,KAAK/P,KAAKkH,GAEjC,EAECsL,QAAAA,GACOzC,KAAK/P,MACP+P,KAAKpO,SAAWoO,KAAK/P,KACrB+P,KAAKpO,SAASW,WAAayN,KAAKqB,IAAIhP,MAEtC2N,KAAKpO,SAASW,WAAayN,KAAKqB,IAAIhP,IAEvC,EAEAzC,MAAAA,GACOoQ,KAAK/P,MAAQ+P,KAAK/P,KAAKyS,WAC3B1C,KAAK2C,QAAQC,KAAK,CAAEvQ,KAAM,iBAC1B2N,KAAK6C,kBAEH7C,KAAK2C,QAAQC,KAAK,CAAEvQ,KAAM,aAC1B2N,KAAK6C,gBACT,EACD/O,eAAAA,CAAgBR,GACY,QAArBA,EAAKuB,SAAS9D,MAClBiP,KAAK7C,aAAe7J,EAAKwP,cACzB9C,KAAKjD,aAAc,EACnBiD,KAAK+C,UAAU,KACb/C,KAAKe,MAAMiC,SAASC,iBAAiBjD,KAAK7C,kBAIf,WAArB7J,EAAKuB,SAAS9D,MAGO,QAArBuC,EAAKuB,SAAS9D,QAFjBuC,EAAKuB,SAASmE,KAAO1F,EAAKuB,SAASmE,IAM1C,EACDkE,WAAAA,GACG8C,KAAKkD,YAAa,EAClBlD,KAAKjD,aAAc,CACrB,EACAoG,eAAAA,CAAgBxE,GACd,OAAQA,GACN,IAAK,MACH,MAAO,4CACT,IAAK,KACH,MAAO,4CACT,IAAK,MACH,MAAO,2CACT,IAAK,SACH,MAAO,2CACT,IAAK,OACH,MAAO,4CACT,IAAK,MACH,MAAO,4CACT,QACE,MAAO,GAEf,EACC5K,SAAAA,CAAUqP,EAAcC,EAAStS,GAE9B,MAAMuS,EAAqB,CAAC,MAAO,MACnC,QAAKA,EAAmBC,SAASF,EAAS/P,KAAKuB,SAAS9D,QACtC,SAATA,GAA4B,SAATA,EAKhC,EACF,cAAMsL,CAAS/I,EAAMkQ,EAAW,KAAMC,GAAa,GACjDC,MAAMC,kBACN,IAAIC,EAAU5D,KAAKzM,MAAMkE,OAAS,EAAIuI,KAAKzM,MAAMkE,OAAS,EAAI,EAK9D,GAA2B,QAAvBnE,EAAKuB,SAAS9D,WACViP,KAAKuB,KAAKsC,mBAAmB,CAAE5T,KAAM+P,KAAK/P,KAAKkH,GAAI2L,cAAexP,EAAKwP,cAAegB,KAAMF,EAASG,UAAWP,QACjH,CACLlQ,EAAKuB,SAAS5E,KAAO+P,KAAK/P,KAAKkH,GAC/B7D,EAAKuB,SAASiP,KAAOF,EACrBtQ,EAAKuB,SAASkP,UAAYP,EAC1B,MAAMQ,QAA2BhE,KAAKuB,KAAK0C,iBAAiB3Q,EAAKuB,UAC3DqP,EAASF,EAAmB1Q,KAAK4Q,OAEvC,GAAI5Q,EAAKiN,SAAU,CACjB,IAAI4D,EAAa7Q,EAAKiN,SAAS+B,IAAI,CAAC8B,EAAOxD,KACzC,MAAMyD,EAASZ,GAAc7C,IAAUtN,EAAKiN,SAAS9I,OAAS,EAC9D,OAAOuI,KAAK3D,SAAS+H,EAAOF,EAAQG,WAEhCC,QAAQxG,IAAIqG,EACpB,CACF,CAGIV,GACFzD,KAAKwC,YAAYxC,KAAK/P,KAAKkH,GAE/B,EAEE,aAAMoF,CAAQjJ,GAEZ,GADAoQ,MAAMC,kBACmB,QAArBrQ,EAAKuB,SAAS9D,KAAa,CACzB,MAAMuQ,QAAiBtB,KAAKuB,KAAKgD,gBAAgBjR,EAAK6D,IACrC,MAApBmK,EAASpF,QACT8D,KAAKwC,YAAYxC,KAAK/P,KAAKkH,GAE9B,KACK,CACH,MAAMmK,QAAiBtB,KAAKuB,KAAKgD,gBAAgBjR,EAAK6D,IACzD,GAAwB,MAApBmK,EAASpF,OAAgB,CAC3B,MAAMsI,QAAYxE,KAAKuB,KAAKkD,gBAAgBnR,EAAKoR,gBAC9B,MAAfF,EAAItI,QACN8D,KAAKwC,YAAYxC,KAAK/P,KAAKkH,GAE/B,CAAC,CACF,EACAtE,WAAAA,GACCmN,KAAKe,MAAMC,QAAQC,SAASC,UAEtBC,IACLnB,KAAKxD,WAAY,IACnB,EACAK,gBAAAA,GACImD,KAAKxD,WAAY,CACnB,EACF,qBAAMG,CAAgBrJ,GACpB,IAAIsQ,EAAU5D,KAAKzM,MAAMkE,OAAS,EAAIuI,KAAKzM,MAAMkE,OAAS,EAAI,EAC1DkN,EAAe,GACnBrR,EAAKsR,QAAQ3N,IAEX,IAAI4N,EAAU,CACZf,KAAMF,EACN3T,KAAM+P,KAAK/P,KAAKkH,GAChB2L,cAAe7L,GAEjB0N,EAAa/B,KAAKiC,GAClBjB,MAEF,MAAMtC,QAAiBtB,KAAKuB,KAAKuD,oBAAoBH,GAC3B,MAApBrD,EAASpF,QACT8D,KAAKwC,YAAYxC,KAAK/P,KAAKkH,GAEnC,EACA,iBAAMqL,CAAYlE,GAChB,MAAMgD,QAAiBtB,KAAKuB,KAAKwD,gBAAgBzG,GACrB,MAApBgD,EAASpF,SACb8D,KAAKzM,MAAQ+N,EAAShO,KACtB0M,KAAKpO,SAASc,UAAYsN,KAAKgF,WAAWhF,KAAKzM,OAC/CyM,KAAKpP,eAAc,GAEzB,EAEAoU,UAAAA,CAAWzR,GACP,IAAI0R,EAAQ,EAOZ,OANA1R,EAAMqR,QAAQvC,IACV4C,GAAS,EACL5C,EAAK9B,UAAY8B,EAAK9B,SAAS9I,OAAS,IACxCwN,GAASjF,KAAKgF,WAAW3C,EAAK9B,aAG/B0E,CACX,EAEA,iBAAM9I,GACJuH,MAAMC,kBACN3D,KAAK/L,iBACP,EAEAiR,eAAAA,CAAgBjO,EAAM3D,EAAMoQ,GAEtBA,IACFA,EAAMC,kBACND,EAAMyB,kBAIRC,QAAQC,IAAI,uBAAwB,CAClCpO,KAAMA,EACN3D,KAAMA,EACNgS,SAAUrO,EAAOA,EAAK5E,KAAO,KAC7BkT,SAAUjS,EAAOA,EAAKvC,KAAO,KAC7ByU,OAAQlS,EAAOA,EAAK6D,GAAK,OAI3B6I,KAAK3I,SAASJ,EAAM3D,EACtB,EAEA,qBAAMW,GACJ,MAAMwR,EAAeA,CAAC7Q,EAAM4O,EAAUkC,KAEtC9Q,EAAKkP,KAAO4B,EAER9Q,EAAK2L,UAAY3L,EAAK2L,SAAS9I,OAAS,GACxC7C,EAAK2L,SAASqE,QAAQ,CAACR,EAAOuB,KAE1BvB,EAAML,UAAYnP,EAAKuC,GAEvBiN,EAAMN,KAAO6B,EAAa,EAE1BF,EAAarB,EAAOxP,EAAKuC,GAAIiN,EAAMN,SAK3C9D,KAAKzM,MAAMqR,QAAQ,CAACpP,EAAQoQ,KAExBpQ,EAAOsO,KAAO8B,EAAc,EAExBpQ,EAAO+K,UAAY/K,EAAO+K,SAAS9I,OAAS,GAE5CgO,EAAajQ,EAAQA,EAAO2B,GAAI3B,EAAOsO,QAIhD,MAAMxC,QAAiBtB,KAAKuB,KAAKsE,oBAAoB7F,KAAKzM,OAClC,MAApB+N,EAASpF,SACN4J,EAAAA,GAAAA,IAAe,CACXpE,SAAU,IACVlE,MAAO,OACPzM,KAAM,WAGlB,EAGA,aAAMC,GACL,GAAIgP,KAAK+F,MAAO,CACf,MAAM3E,EAAS,CACd4E,IAAKhG,KAAK+F,MACVE,MAAOjG,KAAK/P,KAAKkH,KAEZ2O,EAAAA,GAAAA,IAAe,CACbtI,MAAO,OACPkC,QAAS,cACT3O,KAAM,UACN2Q,SAAS,MAEjB,MAAMJ,QAAiBtB,KAAKuB,KAAK2E,SAASlG,KAAK/P,KAAKkH,GAAIiK,GACjC,KAAnBE,EAASpF,SAEZ8D,KAAKnC,eAAiByD,EAAShO,KAC/B0M,KAAK3C,WAAY,EAEnB,MACC2C,KAAKmG,SAAS,CACbpV,KAAM,UACN2O,QAAS,aACTgC,SAAU,KAGb,EAEAlJ,YAAAA,CAAalF,GACXA,EAAK0F,KAAQ1F,EAAK0F,GACpB,EAEAyB,YAAAA,CAAanH,GACRA,EAAKvC,KAAiBuC,EAAK8G,UAAW,EACzC4F,KAAK+C,UAAU,KACb/C,KAAKe,MAAMqF,MAAMC,SAErB,EACA/L,aAAAA,CAAchH,GACZA,EAAK8G,UAAW,CAClB,EACAjH,YAAAA,CAAamT,GAEX,EAEFrL,gBAAAA,CAAiB3H,EAAKiT,GACrB,OAAQA,GACP,IAAK,MACJjT,EAAKsH,QAAU,4DACf,MACD,IAAK,MACJtH,EAAKsH,QAAU,0DACf,MACD,IAAK,OACJtH,EAAKsH,QAAU,kDACf,MACD,IAAK,MACJtH,EAAKsH,QACJ,gKACD,MAEH,EAEAzG,eAAAA,CAAgBS,EAAM4R,GACpBpB,QAAQC,IAAI,aAAczQ,GAE1BoL,KAAK+C,UAAU,KACb,MAAM0D,EAAYC,SAASC,cAAc,iCACrCF,IACFA,EAAU1X,MAAM6X,QAAU,QAC1BH,EAAU1X,MAAM8X,WAAa,UAC7BJ,EAAU1X,MAAM+X,QAAU,OAK9BJ,SAASK,iBAAiB,YAAa,SAASrD,GAC9C,MAAMsD,EAAStD,EAAMuD,QACfC,EAAcR,SAASC,cAAc,YACrCQ,EAAaD,EAAcA,EAAYE,wBAAwBC,IAAM,EAEvEL,EAAS,KAAOG,EAAa,EAC/B/G,OAAOkH,SAAS,GAAI,IACXN,EAAS5G,OAAOmH,YAAc,KACvCnH,OAAOkH,SAAS,EAAG,GAEvB,EACF,EAEAjT,eAAAA,CAAgB+O,EAAcC,EAAUmD,GACtCpB,QAAQC,IAAI,mBAAoBhC,EAASpR,MAC3C,EAEAsC,eAAAA,CAAgB6O,EAAcC,EAAUmD,GACtCpB,QAAQC,IAAI,mBAAoBhC,EAASpR,MAC3C,EAEAwC,cAAAA,CAAe2O,EAAcC,EAAUmD,GACrCpB,QAAQC,IAAI,kBAAmBhC,EAASpR,MAC1C,EAEA0C,aAAAA,CAAcyO,EAAcC,EAAUmE,EAAUhB,GAC9CpB,QAAQC,IAAI,iBAAkBhC,GAAYA,EAASpR,MAAOuV,EAC5D,EAGA,mBAAM1U,CAAc2U,GAClB,IAAI7D,EAAU5D,KAAKzM,MAAMkE,OAAS,EAAIuI,KAAKzM,MAAMkE,OAAS,EAAI,EAC9D,GAAY,OAATgQ,EACDzH,KAAKL,eAAiB,CACpBtN,KAAM,QACNtB,KAAM,KACNX,QAAS,CACP8H,SAAS,GACTE,aAAa,GACbG,MAAM,IAERqC,OAAO,GACPpI,KAAK,GACLgN,QAAQQ,KAAKG,eAGZ,GAAY,QAATsH,EACJzH,KAAKL,eAAiB,CACpBtN,KAAM,QACNtB,KAAM,MACNX,QAAS,CACP0I,OAAO,QACPM,WAAW,GACXG,cAAc,GACdrB,SAAS,GACT2B,aAAa,IAEfe,OAAO,GACPpI,KAAK,GACLgN,QAAQQ,KAAKG,eAId,GAAY,WAATsH,EACNzH,KAAKL,eAAiB,CACpBtN,KAAM,QACNtB,KAAM,SACNX,QAAS,CAAC,EACVwK,OAAO,GACPpI,KAAK,GACLgN,QAAQQ,KAAKG,eAGZ,GAAY,SAATsH,EACNzH,KAAKL,eAAiB,CACpBtN,KAAM,QACNtB,KAAM,OACNX,QAAS,CACP2L,KAAK,IAEPnB,OAAO,GACPpI,KAAK,GACLgN,QAAQQ,KAAKG,cAGZ,IAAY,QAATsH,EAUN,MAAM,IAAIC,MAAM,wBAThB1H,KAAKL,eAAiB,CACpBtN,KAAM,QACNtB,KAAM,MACNX,QAAS,CAAC,EACVwK,OAAO,GACPpI,KAAK,GACLgN,QAAQQ,KAAKG,SAIjB,CACA,MAAM6D,QAA2BhE,KAAKuB,KAAKoG,mBAAmB3H,KAAKL,gBACjE,GAAkC,MAA9BqE,EAAmB9H,OAAgB,CACjC,MAAMoF,QAAiBtB,KAAKuB,KAAKsC,mBAAmB,CAAE5T,KAAM+P,KAAK/P,KAAKkH,GAAIuN,eAAeV,EAAmB1Q,KAAK6D,GAAI2M,KAAMF,IACjG,MAApBtC,EAASpF,QACX8D,KAAKwC,YAAYxC,KAAK/P,KAAKkH,GAEjC,CACN,EAEFvG,aAAAA,CAAcJ,GACTwP,KAAKpM,SAAW,IAAIgU,KACpB5H,KAAKxP,SAAWA,CAClB,EACF,cAAM6G,CAASJ,EAAK3D,GACjB,IAAI8N,EAEFA,EADa,SAAZnK,EAAK5E,KACG,CAACmF,KAAK,CAAC,GAEP,CAACA,KAAK,CAACA,KAAKP,EAAKO,KAAKnF,KAAK4E,EAAK5E,OAE3C,MAAMiP,QAAiBtB,KAAKuB,KAAKsG,mBAAoBvU,EAAK6D,GAAIiK,GACtC,MAApBE,EAASpF,SACTuF,EAAAA,GAAAA,IAAU,CACR1Q,KAAM,UACN2O,QAAS,OACTgC,SAAU,MAGhB1B,KAAKwC,YAAYxC,KAAK/P,KAAKkH,GAC7B,GAGF2Q,OAAAA,GACE9H,KAAKyC,WACDzC,KAAK/P,MAAQ+P,KAAK/P,KAAKkH,IACxB6I,KAAKwC,YAAYxC,KAAK/P,KAAKkH,GAEhC,EAEA4Q,OAAAA,GAEE,MAAMhZ,EAAQ2X,SAASsB,cAAc,SACrCjZ,EAAMkZ,UAAY,ihBAgBlBvB,SAASwB,KAAKC,YAAYpZ,EAC5B,G,YC77BF,MAAMqZ,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASC,IAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/views/TestCase/TestCaseDetail.vue", "webpack://frontend-web/./src/views/TestCase/TestCaseDetail.vue?353e"], "sourcesContent": ["<template>\r\n  <div class=\"page-container\">\r\n    <div class=\"page-header-card\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <el-button class=\"back-button\" @click=\"goBack\">\r\n            <el-icon><Back /></el-icon>\r\n            <span>返回</span>\r\n          </el-button>\r\n          <div class=\"page-title\">{{ this.case !== null ? '编辑用例' : '新增用例' }}</div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-tooltip content=\"展开所有步骤\" placement=\"top\" :disabled=\"isExpand\">\r\n            <el-button v-if=\"!isExpand\" class=\"action-button\" @click=\"rowOpenORFold(true)\">\r\n              <el-icon><ArrowDown /></el-icon>\r\n              <span>展开</span>\r\n            </el-button>\r\n          </el-tooltip>\r\n          <el-tooltip content=\"折叠所有步骤\" placement=\"top\" :disabled=\"!isExpand\">\r\n            <el-button v-if=\"isExpand\" class=\"action-button\" @click=\"rowOpenORFold(false)\">\r\n              <el-icon><ArrowUp /></el-icon>\r\n              <span>折叠</span>\r\n            </el-button>\r\n          </el-tooltip>\r\n          <el-tooltip content=\"调试运行测试用例\" placement=\"top\">\r\n            <el-button class=\"action-button\" type=\"success\" @click=\"runCase\">\r\n              <el-icon><VideoPlay /></el-icon>\r\n              <span>调试</span>\r\n            </el-button>\r\n          </el-tooltip>\r\n          <el-tooltip :content=\"this.case !== null ? '保存更改' : '创建新用例'\" placement=\"top\">\r\n            <el-button \r\n              class=\"action-button save-button\" \r\n              type=\"primary\" \r\n              @click=\"this.case !== null ? editCaseSave() : addCaseSave()\">\r\n              <el-icon><Document /></el-icon>\r\n              <span>保存</span>\r\n            </el-button>\r\n          </el-tooltip>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <el-row :gutter=\"10\">\r\n    <!-- 左边内容-->\r\n    <el-col :span=\"6\">\r\n    <div class=\"tree-component\">\r\n      <el-form :model=\"editForm\"  :rules=\"rulesCase\" ref=\"CaseRef\" label-width=\"80px\" style=\"max-width: 500px\">\r\n      <el-form-item label=\"用例名称\"  prop=\"name\" size=\"mini\">\r\n        <el-input  v-model=\"editForm.name\"  placeholder=\"请输入用例名称\"/>\r\n      </el-form-item>\r\n      <el-form-item prop=\"project_id\" label=\"所属项目\" size=\"mini\">\r\n        <el-input v-model=\"editForm.project_id\"   disabled />\r\n      </el-form-item>\r\n\t\t\t<el-form-item label=\"用例描述\" prop=\"desc\">\r\n        <el-input type=\"textarea\" v-model=\"editForm.desc\"   placeholder=\"请输入备注\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"步骤总数：\" label-width=\"93px\"><div style=\"color: #00aaff;font-weight: bold;\">{{editForm.stepCount}}</div></el-form-item>\r\n\t\t</el-form>\r\n    </div>\r\n    </el-col>\r\n    <!-- 右边内容-->\r\n    <el-col :span=\"18\">\r\n      <div class=\"stepStyle\">\r\n        <el-tag class=\"el-icon-plus\" color=\"#61649f\" style=\"margin-right: 10px;width: 100px\" @click=\"showApiCite\">HTTP请求</el-tag>\r\n        <el-tag class=\"el-icon-plus\" color=\"#E6A23C\" style=\"margin-right: 10px;width: 100px\" @click=\"AddController('if')\">条件控制器</el-tag>\r\n        <el-tag class=\"el-icon-plus\" color=\"#02A7F0FF\" style=\"margin-right: 10px;width: 100px\" @click=\"AddController('for')\">循环控制器</el-tag>\r\n        <el-tag class=\"el-icon-plus\" color=\"#7B4D12FF\" style=\"margin-right: 10px;width: 100px\" @click=\"AddController('script')\">自定义脚本</el-tag>\r\n        <el-tag class=\"el-icon-plus\" color=\"#783887FF\" style=\"margin-right: 10px;width: 100px\" @click=\"AddController('sql')\">SQL控制器</el-tag>\r\n        <el-tag class=\"el-icon-plus\" color=\"#67C23AFF\" style=\"margin-right: 10px;width: 100px\" @click=\"AddController('time')\">等待控制器</el-tag>\r\n      </div>\r\n      <el-scrollbar height=\"calc(100vh - 190px)\" always @scroll=\"handleScroll\">\r\n      <div class=\"tree-container\">\r\n      <el-tree\r\n        :data=\"steps\"\r\n        :props=\"defaultProps\"\r\n        draggable\r\n        :key=\"treeKey\"\r\n        :default-expand-all=\"isExpand\"\r\n        :expand-on-click-node=\"false\"\r\n        @node-click=\"handleStepClick\"\r\n        :allow-drop=\"allowDrop\"\r\n        @node-drop=\"updateStepOrder\"\r\n        @node-drag-start=\"handleDragStart\"\r\n        @node-drag-enter=\"handleDragEnter\"\r\n        @node-drag-leave=\"handleDragLeave\"\r\n        @node-drag-over=\"handleDragOver\"\r\n        @node-drag-end=\"handleDragEnd\"\r\n        class=\"custom-tree\"\r\n      >\r\n    <template v-slot=\"{ node,data }\">\r\n      <el-card v-if=\"data.stepInfo\" :class=\"['step-card', `step-card-${data.stepInfo.type}`]\">\r\n        <div slot=\"header\" class=\"card-header\">\r\n         <el-row :gutter=\"10\" type=\"flex\" align=\"middle\" justify=\"center\">\r\n          <el-col :span=\"18\" class=\"card-main-content\">\r\n            <div class=\"card-content-wrapper\">\r\n              <!--api展示-->\r\n              <div v-if=\"data.stepInfo.type==='api'\" class=\"card-inner\">\r\n                <div class=\"card-left\">\r\n                  <span class=\"step-icon\" :style=\"{ color: 'rgb(97, 100, 159)' }\">{{ getCardIndex(node.parent, node) }}</span>\r\n                  <el-tag color=\"#61649f\">HTTP请求</el-tag>\r\n                  <span class=\"method-tag\">\r\n                    <span v-if=\"data.stepInfo.method === 'POST'\">\r\n                      <b style=\"color: #49cc90;\">{{ data.stepInfo.method }}</b>\r\n                    </span>\r\n                    <span v-if=\"data.stepInfo.method === 'GET'\">\r\n                      <b style=\"color: #61affe;\">{{ data.stepInfo.method }}</b>\r\n                    </span>\r\n                    <span v-if=\"data.stepInfo.method === 'PUT'\">\r\n                      <b style=\"color: #fca130;\">{{ data.stepInfo.method }}</b>\r\n                    </span>\r\n                    <span v-if=\"data.stepInfo.method === 'PATCH'\">\r\n                      <b style=\"color: #50e3c2;\">{{ data.stepInfo.method }}</b>\r\n                    </span>\r\n                    <span v-if=\"data.stepInfo.method === 'DELETE'\">\r\n                      <b style=\"color: #f93e3e;\">{{ data.stepInfo.method }}</b>\r\n                    </span>\r\n                    <span v-if=\"data.stepInfo.method === 'DEAD'\">\r\n                      <b style=\"color: rgb(201, 233, 104);\">{{ data.stepInfo.method }}</b>\r\n                    </span>\r\n                  </span>\r\n                </div>\r\n\r\n                <div class=\"card-center\">\r\n                  <el-dropdown trigger=\"click\" @click.stop>\r\n                      <el-button type=\"text\" v-if=\"Object.keys(data.stepInfo.host).length !== 0\" @click.stop>{{data.stepInfo.host.name}}</el-button>\r\n                      <el-button v-else type=\"text\"  @click.stop>默认环境</el-button>\r\n                    <template #dropdown>\r\n                      <el-dropdown-menu>\r\n                          <el-dropdown-item v-for=\"item in testEnvsWithDefault\" :key=\"item.id\" command='api' @click=\"envClick(item,data.stepInfo)\">\r\n                            {{item.name}}\r\n                          </el-dropdown-item>\r\n                      </el-dropdown-menu>\r\n                    </template>\r\n                  </el-dropdown>\r\n                  <b class=\"card-url\">{{ data.stepInfo.url }}</b>\r\n                  <span class=\"card-name\">{{data.stepInfo.name }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!--if控制器展示-->\r\n              <div v-if=\"data.stepInfo.type==='if'\" class=\"card-inner\">\r\n                <div class=\"card-left\">\r\n                  <span class=\"step-icon\" :style=\"{ color: 'rgb(230, 162, 60)' }\">{{ getCardIndex(node.parent, node) }}</span>\r\n                  <el-tag color=\"rgb(230, 162, 60)\">条件控制器</el-tag>\r\n                </div>\r\n                <div class=\"card-center if-content\">\r\n                  <div class=\"if-controls-wrapper\">\r\n                    <el-input class=\"input-def\" placeholder=\"变量，例如{{name}}\" v-model=\"data.stepInfo.content.variable\"/>\r\n                    <el-select v-model=\"data.stepInfo.content.JudgmentMode\" placeholder=\"请选择\" style=\"width: 100px\">\r\n                      <el-option\r\n                        v-for=\"item in options\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.label\"\r\n                        :value=\"item.value\"\r\n                      />\r\n                    </el-select>\r\n                    <el-input class=\"input-def\" placeholder=\"值\" v-model=\"data.stepInfo.content.value\"/>\r\n                    <el-input class=\"input-def\" placeholder=\"备注\" v-model=\"data.stepInfo.desc\"/>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!--循环控制器展示-->\r\n              <div v-if=\"data.stepInfo.type==='for'\" class=\"card-inner\" @click=\"toggleExpand(data.stepInfo)\">\r\n                <div class=\"card-left\">\r\n                  <span class=\"step-icon\" :style=\"{ color: 'rgb(2, 167, 240)' }\">{{ getCardIndex(node.parent, node) }}</span>\r\n                  <el-tag color=\"rgb(2, 167, 240)\">循环控制器</el-tag>\r\n                </div>\r\n                <div class=\"card-center\">\r\n                  <div class=\"for-controls-wrapper\">\r\n                    <el-radio-group v-model=\"data.stepInfo.content.select\" @click.stop class=\"radio-group\">\r\n                      <el-radio label=\"count\" value=\"count\">次数循环</el-radio>\r\n                      <el-radio label=\"for\" value=\"for\">for循环</el-radio>\r\n                      <el-radio label=\"while\" value=\"while\" disabled>while循环</el-radio>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div v-if=\"data.stepInfo.type==='for' && data.stepInfo.dlg\" :key=\"data.id\" @click.stop class=\"loop-details\">\r\n                <div v-if=\"data.stepInfo.content.select==='count' || data.stepInfo.content.select===''\">\r\n                  <div class=\"loop\">\r\n                    <div class=\"loop-control\">\r\n                      <span>循环次数</span>\r\n                      <el-input v-model=\"data.stepInfo.content.cycleIndex\" style=\"width: 200px\" placeholder=\"循环次数\" />\r\n                    </div>\r\n                    <div class=\"loop-control\">\r\n                      <span>循环间隔</span>\r\n                      <el-input-number\r\n                        v-model=\"data.stepInfo.content.cycleInterval\"\r\n                        :min=\"0\"\r\n                        :max=\"999\"\r\n                        controls-position=\"right\"\r\n                        placeholder=\"秒\"\r\n                      />\r\n                      <span>秒</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"data.stepInfo.content.select==='for'\">\r\n                  <div class=\"loop\">\r\n                    <div class=\"loop-control\">\r\n                      <el-input style=\"width: 200px\" placeholder=\"定义变量名称\" v-model=\"data.stepInfo.content.variableName\"/>\r\n                      <b>in</b>\r\n                      <el-input style=\"width: 200px\" placeholder=\"变量，例如{{name}}\" v-model=\"data.stepInfo.content.variable\"/>\r\n                    </div>\r\n                    <div class=\"loop-control\">\r\n                      <span>循环间隔</span>\r\n                      <el-input-number\r\n                        v-model=\"data.stepInfo.content.cycleInterval\"\r\n                        :min=\"0\"\r\n                        :max=\"999\"\r\n                        size=\"small\"\r\n                        controls-position=\"right\"\r\n                        placeholder=\"秒\"\r\n                      />\r\n                      <span>秒</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"data.stepInfo.content.select==='while'\"><div class=\"loop\">敬请期待！</div></div>\r\n              </div>\r\n\r\n              <!--自定义脚本展示-->\r\n              <div v-if=\"data.stepInfo.type==='script'\" class=\"card-inner\">\r\n                <div class=\"card-left\">\r\n                  <span class=\"step-icon\" :style=\"{ color: 'rgb(123, 77, 18)' }\">{{ getCardIndex(node.parent, node) }}</span>\r\n                  <el-tag color=\"rgb(123, 77, 18)\">自定义脚本</el-tag>\r\n                </div>\r\n                <div class=\"card-center\">\r\n                  <el-input v-if=\"data.stepInfo.inputDlg\" v-model=\"data.stepInfo.name\" @blur=\"cancelEditing(data.stepInfo)\" ref=\"input\" maxlength=\"50\" @click.stop></el-input>\r\n                  <el-button v-else class=\"script-button\" plain type=\"text\" @click=\"startEditing(data.stepInfo)\" @click.stop>\r\n                    {{data.stepInfo.name}}\r\n                    <el-icon><Edit /></el-icon>\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <el-row :gutter=\"10\" v-show=\"data.stepInfo.type==='script' && data.stepInfo.dlg\" @click.stop class=\"script-editor\">\r\n                <el-col :span=\"18\"><Editor v-model=\"data.stepInfo.script\" lang=\"python\" theme=\"chrome\"></Editor></el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-divider>脚本模板</el-divider>\r\n                  <div class=\"code_mod\"><el-button type=\"success\" size=\"mini\" plain @click=\"addSetUptCodeMod(data.stepInfo,'func')\">导入变量模块</el-button></div>\r\n                  <div class=\"code_mod\"><el-button type=\"success\" size=\"mini\" plain @click=\"addSetUptCodeMod(data.stepInfo,'ENV')\">预设全局变量</el-button></div>\r\n                  <div class=\"code_mod\"><el-button type=\"success\" size=\"mini\" plain @click=\"addSetUptCodeMod(data.stepInfo,'env')\">预设局部变量</el-button></div>\r\n                  <div class=\"code_mod\"><el-button type=\"success\" size=\"mini\" plain @click=\"addSetUptCodeMod(data.stepInfo,'sql')\">执行sql查询</el-button></div>\r\n                </el-col>\r\n              </el-row>\r\n\r\n              <!--sql控制器展示-->\r\n              <div v-if=\"data.stepInfo.type==='sql'\" class=\"card-inner\">\r\n                <div class=\"card-left\">\r\n                  <span class=\"step-icon\" :style=\"{ color: 'rgb(120, 56, 135)' }\">{{ getCardIndex(node.parent, node) }}</span>\r\n                  <el-tag color=\"rgb(120, 56, 135)\">SQL控制器</el-tag>\r\n                </div>\r\n                <div class=\"card-center\">\r\n                  <el-input v-if=\"data.stepInfo.inputDlg\" v-model=\"data.stepInfo.name\" @blur=\"cancelEditing(data.stepInfo)\" ref=\"input\" maxlength=\"50\" @click.stop></el-input>\r\n                  <el-button v-else class=\"script-button\" plain type=\"text\" @click=\"startEditing(data.stepInfo)\" @click.stop>\r\n                    {{data.stepInfo.name}}\r\n                    <el-icon><Edit /></el-icon>\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div v-show=\"data.stepInfo.type==='sql' && data.stepInfo.dlg\" class=\"sql-message\"><i>该功能敬请期待噢！</i></div>\r\n\r\n              <!--time控制器展示-->\r\n              <div v-if=\"data.stepInfo.type==='time'\" class=\"card-inner\">\r\n                <div class=\"card-left\">\r\n                  <span class=\"step-icon\" :style=\"{ color: 'rgb(103, 194, 58)' }\">{{ getCardIndex(node.parent, node) }}</span>\r\n                  <el-tag color=\"rgb(103, 194, 58)\">等待控制器</el-tag>\r\n                </div>\r\n                <div class=\"card-center time-controller\">\r\n                  <div class=\"time-control\">\r\n                    <el-input-number\r\n                      v-model=\"data.stepInfo.content.time\"\r\n                      :min=\"0\"\r\n                      :max=\"999\"\r\n                      size=\"small\"\r\n                      controls-position=\"right\"\r\n                      placeholder=\"秒\"\r\n                    />\r\n                    <span>秒</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <div class=\"card-actions\">\r\n              <el-switch\r\n                v-model=\"data.status\"\r\n                inline-prompt\r\n                size=\"default\"\r\n                @click=\"switchClick(data)\"\r\n                style=\"--el-switch-on-color: #53a8ff; --el-switch-off-color: #f56c6c\"\r\n              />\r\n              <el-button size=\"default\" icon=\"document\" @click=\"copyTree(data)\" circle class=\"action-button\" />\r\n              <el-button size=\"default\" icon=\"delete\" type=\"danger\" @click=\"delTree(data)\" circle class=\"action-button\" />\r\n            </div>\r\n          </el-col>\r\n         </el-row>\r\n        </div>\r\n      </el-card>\r\n    </template>\r\n      </el-tree>\r\n      </div>\r\n      </el-scrollbar>\r\n    </el-col>\r\n    </el-row >\r\n    <apiCite v-if=\"addApiDlg\" @childEvent=\"handleChildData\" @close-modal=\"handleCloseModal\"></apiCite>\r\n  \t<!-- 调试测试步骤窗口 -->\r\n    <el-drawer v-model=\"editCaseDlg\"   :with-header=\"false\" size=\"50%\"><newEditCase ref=\"childRef\" @closeDrawer=\"handleClose\"  :Interface_id=\"Interface_id\" :copyDlg=\"copyDlg\"  style=\"padding: 0 10px;\"></newEditCase></el-drawer>\r\n    <!-- 显示运行结果 -->\r\n\t  <el-drawer v-model=\"ResultDlg\" :with-header=\"false\" size=\"50%\">\r\n\t\t<div style=\"padding:20px;\">\r\n\t\t\t<el-descriptions title=\"执行结果\" border :column=\"4\" style=\"text-align: center;\">\r\n\t\t\t\t<el-descriptions-item label=\"总数\" ><b style=\"color: #00aaff\">{{ runScentResult.all }}</b></el-descriptions-item>\r\n\t\t\t\t<el-descriptions-item label=\"通过\"><b style=\"color: #00aa7f\">{{ runScentResult.success }}</b></el-descriptions-item>\r\n\t\t\t\t<el-descriptions-item label=\"失败\"><b style=\"color: orangered\">{{ runScentResult.fail }}</b></el-descriptions-item>\r\n\t\t\t\t<el-descriptions-item label=\"错误\"><b style=\"color: #fca130\">{{ runScentResult.error }}</b></el-descriptions-item>\r\n\t\t\t</el-descriptions>\r\n\t\t\t<div style=\"height: 40px;line-height: 40px;\"><b>执行详情</b></div>\r\n\t\t\t<el-scrollbar height=\"calc(100vh - 180px)\">\r\n\t\t\t\t<el-table :data=\"runScentResult.cases\" style=\"width: 100%\" empty-text=\"暂无数据\">\r\n\t\t\t\t\t<el-table-column type=\"expand\">\r\n\t\t\t\t\t\t<template #default=\"props\">\r\n\t\t\t\t\t\t\t<caseResult :result=\"props.row\"></caseResult>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t<el-table-column label=\"步骤名\" prop=\"name\" />\r\n\t\t\t\t\t<el-table-column label=\"请求方法\" prop=\"method\">\r\n            <template #default=\"props\">\r\n               <span v-if=\"props.row.type === 'api'\">{{ props.row.method }}</span>\r\n\t\t\t\t\t\t</template>\r\n          </el-table-column>\r\n\t\t\t\t\t<el-table-column label=\"响应状态码\" prop=\"status_cede\">\r\n            <template #default=\"props\">\r\n               <span v-if=\"props.row.type === 'api'\">{{ props.row.status_cede }}</span>\r\n\t\t\t\t\t\t</template>\r\n          </el-table-column>\r\n\t\t\t\t\t<el-table-column label=\"执行结果\" prop=\"state\" min-width=\"40px\">\r\n\t\t\t\t\t\t<template #default=\"props\">\r\n\t\t\t\t\t\t\t<span v-if=\"props.row.state == '成功'\" style=\"color: #00AA7F;\">{{ props.row.state }}</span>\r\n              <span v-else-if=\"props.row.state == '错误'\" style=\"color: #F56C6C;\">{{ props.row.state }}</span>\r\n\t\t\t\t\t\t\t<span v-else style=\"color:#fca130\">{{ props.row.state }}</span>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</el-table-column>\r\n\t\t\t\t</el-table>\r\n\t\t\t</el-scrollbar>\r\n\t\t</div>\r\n\t</el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {mapMutations, mapState} from \"vuex\";\r\nimport {ElMessage, ElNotification} from \"element-plus\";\r\nimport apiCite from '../../views/TestCase/apiCiteDlg.vue';\r\nimport newEditCase from '../../components/common/InterfaceNew/neweditCase.vue';\r\nimport caseResult from '../../components/common/caseResult.vue';\r\nimport Editor from'../../components/common/Editor.vue';\r\nimport { ref, reactive, toRefs, getCurrentInstance, onMounted, nextTick, watch } from 'vue'\r\nimport { ArrowDown, ArrowRight, Edit, Plus, Delete, DocumentAdd, Refresh, VideoPlay, Back, CaretRight, Document, View } from '@element-plus/icons-vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nexport default {\r\n  components:{\r\n    apiCite,\r\n    newEditCase,\r\n    caseResult,\r\n    Editor\r\n  },\r\n  data() {\r\n    return{\r\n      editForm:{\r\n        name:'',\r\n        project_id:'',\r\n        desc:'',\r\n        stepCount:0,\r\n        creator:''\r\n      },\r\n      rulesCase: {\r\n\t\t\t\tname: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请输入用例名称',\r\n\t\t\t\t\t\ttrigger: 'blur'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tproject_id: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请选择项目',\r\n\t\t\t\t\t\ttrigger: 'blur'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t},\r\n      steps:[],\r\n      addApiDlg:false,\r\n      editCaseDlg: false,\r\n      Interface_id: '',\r\n      copyDlg: false,\r\n      ResultDlg:false,\r\n      runScentResult:'',\r\n      ControllerData:{\r\n        name: \"\",\r\n        type: \"\",\r\n        content: {},\r\n        script:\"\",\r\n        desc:\"\",\r\n        creator:\"\",\r\n      },\r\n      options: [\r\n          { value: 'equal', label: '等于' },\r\n          { value: 'notEqual', label: '不等于' },\r\n          { value: 'contains', label: '包含' },\r\n          { value: 'notContains', label: '不包含' },\r\n          { value: 'greaterThan', label: '大于' },\r\n          { value: 'lessThan', label: '小于' },\r\n          { value: 'greaterThanOrEqual', label: '大于等于' },\r\n          { value: 'lessThanOrEqual', label: '小于等于' },\r\n          { value: 'empty', label: '空' },\r\n          { value: 'notEmpty', label: '非空' }\r\n        ],\r\n      test:'',\r\n      treeKey: '',\r\n      isExpand: false,\r\n      }\r\n    },\r\n\r\n  computed: {\r\n\t\t...mapState(['pro','case','envId','testEnvs']),\r\n    testEnvsWithDefault() {\r\n      const withDefault = [...this.testEnvs];\r\n      withDefault.unshift({ host:'默认环境host', name: '默认环境' });\r\n      return withDefault;\r\n    },\r\n    username() {\r\n\t\t\treturn window.sessionStorage.getItem('username');\r\n\t\t},\r\n    defaultProps() {\r\n      return {\r\n        children: 'children',\r\n        label: 'name',\r\n      }\r\n    },\r\n\t},\r\n  watch: {\r\n    steps: {\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapMutations(['CaseInfo','clearCaseInfo']),\r\n  getCardIndex(parent, node) {\r\n    const index = parent.childNodes.indexOf(node);\r\n    return index + 1;\r\n  },\r\n\r\n   async addCaseSave() {\r\n    this.$refs.CaseRef.validate(async vaild => {\r\n    // 判断是否验证通过，不通过则直接retrue\r\n    if (!vaild) return;\r\n    const params = {...this.editForm}\r\n    params.creator = this.username\r\n    params.project_id = this.pro.id\r\n    const response = await this.$api.createTestCase(params);\r\n      if (response.status === 201) {\r\n        ElMessage({\r\n          type: 'success',\r\n          message: '保存成功',\r\n          duration: 1000\r\n        });\r\n      }\r\n    })},\r\n\r\n   // 保存用例信息\r\n   async editCaseSave() {\r\n    this.$refs.CaseRef.validate(async vaild => {\r\n    // 判断是否验证通过，不通过则直接retrue\r\n    if (!vaild) return;\r\n    const params = {...this.editForm}\r\n    params.project_id = this.pro.id\r\n    params.modifier = this.username;\r\n    params.update_time = this.$tools.newTime()\r\n    delete params.create_time\r\n    delete params.creator\r\n    delete params.project\r\n    const response = await this.$api.updateTestCase(params.id, params);\r\n        if (response.status === 200) {\r\n          ElMessage({\r\n            type: 'success',\r\n            message: '保存成功',\r\n            duration: 1000\r\n          });\r\n        this.editStepSave()\r\n        }\r\n    })\r\n   },\r\n\r\n  // 保存步骤信息\r\n  async editStepSave() {\r\n    const ControllerStepsData = this.steps\r\n      .filter(step => step.stepInfo && step.stepInfo.type !== \"api\")\r\n      .map(step => step);\r\n    const response = await this.$api.updatesStepControll(ControllerStepsData)\r\n      if (response.status === 201) {\r\n        this.getCaseStep(this.case.id)\r\n      }\r\n  },\r\n\r\n   reaiTime() {\r\n      if (this.case) {\r\n        this.editForm = this.case;\r\n        this.editForm.project_id = this.pro.name;\r\n      } else {\r\n      this.editForm.project_id = this.pro.name;\r\n      }\r\n   },\r\n\r\n   goBack() {\r\n      if (this.case && this.case.back_type) {\r\n      this.$router.push({ name: 'new-testplan' });\r\n      this.clearCaseInfo();\r\n      } else {\r\n        this.$router.push({ name: 'TestCase' });\r\n        this.clearCaseInfo()}\r\n    },\r\n   handleStepClick(data) {\r\n      if (data.stepInfo.type==='api'){\r\n      this.Interface_id = data.interfaceStep\r\n      this.editCaseDlg = true\r\n      this.$nextTick(() => {\r\n        this.$refs.childRef.getInterfaceInfo(this.Interface_id);\r\n          }\r\n        )\r\n      }\r\n      else if(data.stepInfo.type==='script') {\r\n           data.stepInfo.dlg = !data.stepInfo.dlg;\r\n      }\r\n      else if(data.stepInfo.type==='sql'){\r\n        data.stepInfo.dlg = !data.stepInfo.dlg\r\n      }\r\n      else {}\r\n    },\r\n   handleClose() {\r\n      this.addCaseDlg = false;\r\n      this.editCaseDlg = false;\r\n    },\r\n    getRowClassName(row) {\r\n      switch (row) {\r\n        case 'api':\r\n          return '--el-card-border-color:rgb(97, 100, 159);'\r\n        case 'if':\r\n          return '--el-card-border-color:rgb(230, 162, 60);'\r\n        case 'for':\r\n          return '--el-card-border-color:rgb(2, 167, 240);'\r\n        case 'script':\r\n          return '--el-card-border-color:rgb(123, 77, 18);'\r\n        case 'time':\r\n          return '--el-card-border-color:rgb(103, 194, 58);'\r\n        case 'sql':\r\n          return '--el-card-border-color:rgb(120, 56, 135);'\r\n        default:\r\n          return '';\r\n    }\r\n  },\r\n   allowDrop(draggingNode, dropNode,type) {\r\n      // 只有 type 为 api, for, if 的节点可以作为父级节点\r\n      const allowedParentTypes = ['for', 'if'];\r\n      if (!allowedParentTypes.includes(dropNode.data.stepInfo.type)) {\r\n        return type === \"prev\" || type === \"next\";\r\n\r\n      }else {\r\n        return true\r\n      };\r\n  },\r\nasync copyTree(data, parentId = null, isLastCall = true) {\r\n  event.stopPropagation();\r\n  let order_s = this.steps.length > 0 ? this.steps.length + 1 : 1;\r\n\r\n  // if (data.parent_id) {\r\n  //       parentId = data.parent_id;\r\n  //   }\r\n  if (data.stepInfo.type === 'api') {\r\n    await this.$api.createTestCaseStep({ case: this.case.id, interfaceStep: data.interfaceStep, sort: order_s, parent_id: parentId });\r\n  } else {\r\n    data.stepInfo.case = this.case.id;\r\n    data.stepInfo.sort = order_s;\r\n    data.stepInfo.parent_id = parentId;\r\n    const Controllerresponse = await this.$api.copyStepControll(data.stepInfo);\r\n    const setpId = Controllerresponse.data.setpId;\r\n    // 递归复制子节点\r\n    if (data.children) {\r\n      let childCalls = data.children.map((child, index) => {\r\n        const isLast = isLastCall && index === data.children.length - 1; // 是否为最后一次调用\r\n        return this.copyTree(child, setpId, isLast); // 递归调用 copyTree 函数\r\n      });\r\n      await Promise.all(childCalls); // 等待所有子节点的递归调用完成\r\n    }\r\n  }\r\n\r\n  // 所有递归调用完成后再刷新页面\r\n  if (isLastCall) {\r\n    this.getCaseStep(this.case.id);\r\n  }\r\n},\r\n\r\n  async delTree(data) {\r\n    event.stopPropagation();\r\n    if (data.stepInfo.type==='api'){\r\n          const response = await this.$api.delTestCaseStep(data.id);\r\n\t\t\tif (response.status === 204) {\r\n\t\t\t    this.getCaseStep(this.case.id)\r\n\t\t\t}\r\n    }\r\n    else {\r\n      const response = await this.$api.delTestCaseStep(data.id);\r\n\t\t\tif (response.status === 204) {\r\n\t\t\t  const res = await this.$api.delStepControll(data.controllerStep);\r\n\t\t\t  if (res.status === 204){\r\n\t\t\t    this.getCaseStep(this.case.id)\r\n          }\r\n\t\t\t}}\r\n  },\r\n  showApiCite() {\r\n   this.$refs.CaseRef.validate(async vaild => {\r\n    // 判断是否验证通过，不通过则直接return\r\n    if (!vaild) return;\r\n    this.addApiDlg = true;})\r\n  },\r\n  handleCloseModal() {\r\n      this.addApiDlg = false; // 关闭弹窗\r\n    },\r\n  async handleChildData(data) {\r\n    let order_s = this.steps.length > 0 ? this.steps.length + 1 : 1;\r\n    let newDataArray = [];\r\n    data.forEach(item => {\r\n      // 遍历data数组中的每个元素，为每个元素创建一个新的对象，其中包含 sort、case 和 interfaceStep 字段\r\n      let newItem = {\r\n        sort: order_s,\r\n        case: this.case.id,\r\n        interfaceStep: item\r\n      };\r\n      newDataArray.push(newItem); // 将新创建的对象添加到新数组中\r\n      order_s++;\r\n    });\r\n    const response = await this.$api.createsTestCaseStep(newDataArray)\r\n      if (response.status === 201) {\r\n          this.getCaseStep(this.case.id)\r\n      }\r\n  },\r\n  async getCaseStep(cases) {\r\n    const response = await this.$api.getTestCaseStep(cases)\r\n        if (response.status === 200) {\r\n        this.steps = response.data;\r\n        this.editForm.stepCount = this.countSteps(this.steps);\r\n        this.rowOpenORFold(true)\r\n        }\r\n  },\r\n  // 递归函数来统计步骤总数\r\n  countSteps(steps) {\r\n      let count = 0;\r\n      steps.forEach(step => {\r\n          count += 1; // 计算当前步骤\r\n          if (step.children && step.children.length > 0) {\r\n              count += this.countSteps(step.children); // 递归计算子步骤\r\n          }\r\n      });\r\n      return count;\r\n  },\r\n\r\n  async switchClick() {\r\n    event.stopPropagation();\r\n    this.updateStepOrder()\r\n  },\r\n\r\n  handleItemClick(item, data, event) {\r\n    // 阻止事件冒泡\r\n    if (event) {\r\n      event.stopPropagation();\r\n      event.preventDefault();\r\n    }\r\n    \r\n    // 添加详细日志\r\n    console.log('handleItemClick 被调用:', { \r\n      item: item, \r\n      data: data,\r\n      itemName: item ? item.name : null,\r\n      dataType: data ? data.type : null,\r\n      dataId: data ? data.id : null\r\n    });\r\n    \r\n    // 调用环境点击处理函数\r\n    this.envClick(item, data);\r\n  },\r\n  \r\n  async updateStepOrder() {\r\n    const setParentIds = (node, parentId, parentSort) => {\r\n    // 设置父节点的排序字段\r\n    node.sort = parentSort;\r\n    // 如果节点有子节点，则递归设置子节点的 parent_id 和排序字段\r\n    if (node.children && node.children.length > 0) {\r\n        node.children.forEach((child, childIndex) => {\r\n            // 设置子节点的 parent_id 为当前节点的 id\r\n            child.parent_id = node.id;\r\n            // 设置子节点的排序字段\r\n            child.sort = childIndex + 1;\r\n            // 递归调用，处理子节点的子节点\r\n            setParentIds(child, node.id, child.sort);\r\n          });\r\n        }\r\n    };\r\n    // 遍历步骤数组，设置父节点的排序字段和子节点的 parent_id 和排序字段\r\n    this.steps.forEach((parent, parentIndex) => {\r\n        // 设置父节点的排序字段\r\n        parent.sort = parentIndex + 1;\r\n        // 如果父节点有子节点，则设置子节点的 parent_id 和排序字段\r\n        if (parent.children && parent.children.length > 0) {\r\n            // 调用函数设置父节点和子节点的属性\r\n            setParentIds(parent, parent.id, parent.sort);\r\n        }\r\n    });\r\n\t\t\t// 发送请求后端修改用例顺序\r\n\t\t\tconst response = await this.$api.updateCaseStepOrder(this.steps);\r\n\t\t\tif (response.status === 200) {\r\n          ElNotification({\r\n              duration: 500,\r\n              title: '调整成功',\r\n              type: 'success',\r\n            })\r\n\t\t\t}\r\n\t\t},\r\n\r\n  // 运行测试用例\r\n  async runCase() {\r\n\t\t\tif (this.envId) {\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\tenv: this.envId,\r\n\t\t\t\t\tscene: this.case.id\r\n\t\t\t\t};\r\n          ElNotification({\r\n            title: '开始运行',\r\n            message: '运行过程中请稍等片刻噢',\r\n            type: 'success',\r\n            duration:1000\r\n          });\r\n\t\t\t\tconst response = await this.$api.runCases(this.case.id, params);\r\n\t\t\t\tif (response.status == 200) {\r\n\t\t\t\t\t// 显示执行结果到窗口页面\r\n\t\t\t\t\tthis.runScentResult = response.data;\r\n\t\t\t\t\tthis.ResultDlg = true;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis.$message({\r\n\t\t\t\t\ttype: 'warning',\r\n\t\t\t\t\tmessage: '当前未选中执行环境!',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n  toggleExpand(data) {\r\n    data.dlg = ! data.dlg;\r\n  },\r\n\r\n  startEditing(data) {\r\n    if(data.type==='script'){data.inputDlg = true}else {data.inputDlg = true}\r\n    this.$nextTick(() => {\r\n      this.$refs.input.focus();\r\n    });\r\n  },\r\n  cancelEditing(data) {\r\n    data.inputDlg = false;\r\n  },\r\n  handleScroll(e) {\r\n    // 可以在这里处理滚动事件，如需要时同步其他元素的滚动位置等\r\n    // console.log('Scrolling', e);\r\n  },\r\n  addSetUptCodeMod(data,tp) {\r\n\t\t\tswitch (tp) {\r\n\t\t\t\tcase 'ENV':\r\n\t\t\t\t\tdata.script += '\\n# 设置全局变量 \\nBaseTest().save_global_variable(\"变量名\",\"变量值\")';\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'env':\r\n\t\t\t\t\tdata.script += '\\n# 设置局部变量  \\nBaseTest().save_env_variable(\"变量名\",\"变量值\")';\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'func':\r\n\t\t\t\t\tdata.script += '\\nfrom apitestengine.core.cases import BaseTest';\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'sql':\r\n\t\t\t\t\tdata.script +=\r\n\t\t\t\t\t\t'\\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\\n# db.连接名.execute_all(sql语句) \\nsql = \"SELECT count(*) as count FROM futureloan.member\"\\nres = db.aliyun.execute_all(sql)';\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n  // 添加拖拽事件处理\r\n  handleDragStart(node, ev) {\r\n    console.log('drag start', node);\r\n    // 强制创建拖拽指示器\r\n    this.$nextTick(() => {\r\n      const indicator = document.querySelector('.el-tree-node__drop-indicator');\r\n      if (indicator) {\r\n        indicator.style.display = 'block';\r\n        indicator.style.visibility = 'visible';\r\n        indicator.style.opacity = '1';\r\n      }\r\n    });\r\n    \r\n    // 添加滚动屏幕功能\r\n    document.addEventListener('mousemove', function(event) {\r\n      const mouseY = event.clientY;\r\n      const treeElement = document.querySelector('.el-tree');\r\n      const elementTop = treeElement ? treeElement.getBoundingClientRect().top : 0;\r\n\r\n      if (mouseY < 100 && elementTop > 0) {\r\n        window.scrollBy(0, -10);\r\n      } else if (mouseY > window.innerHeight - 100) {\r\n        window.scrollBy(0, 10);\r\n      }\r\n    });\r\n  },\r\n  \r\n  handleDragEnter(draggingNode, dropNode, ev) {\r\n    console.log('tree drag enter:', dropNode.label);\r\n  },\r\n  \r\n  handleDragLeave(draggingNode, dropNode, ev) {\r\n    console.log('tree drag leave:', dropNode.label);\r\n  },\r\n  \r\n  handleDragOver(draggingNode, dropNode, ev) {\r\n    console.log('tree drag over:', dropNode.label);\r\n  },\r\n  \r\n  handleDragEnd(draggingNode, dropNode, dropType, ev) {\r\n    console.log('tree drag end:', dropNode && dropNode.label, dropType);\r\n  },\r\n\r\n  // 添加条件控制器\r\n  async AddController(types) {\r\n    let order_s = this.steps.length > 0 ? this.steps.length + 1 : 1;\r\n    if(types ==='if'){\r\n      this.ControllerData = {\r\n        name: \"条件控制器\",\r\n        type: \"if\",\r\n        content: {\r\n          variable:\"\",\r\n          JudgmentMode:\"\",\r\n          value:\"\",\r\n        },\r\n        script:\"\",\r\n        desc:\"\",\r\n        creator:this.username,\r\n      }\r\n    }\r\n    else if(types ==='for'){\r\n        this.ControllerData = {\r\n          name: \"循环控制器\",\r\n          type: \"for\",\r\n          content: {\r\n            select:\"count\",\r\n            cycleIndex:\"\",\r\n            cycleInterval:\"\",\r\n            variable:\"\",\r\n            variableName:\"\"\r\n          },\r\n          script:\"\",\r\n          desc:\"\",\r\n          creator:this.username,\r\n      }\r\n\r\n    }\r\n    else if(types ==='script'){\r\n      this.ControllerData = {\r\n        name: \"自定义脚本\",\r\n        type: \"script\",\r\n        content: {},\r\n        script:\"\",\r\n        desc:\"\",\r\n        creator:this.username,\r\n      }\r\n    }\r\n    else if(types ==='time'){\r\n      this.ControllerData = {\r\n        name: \"定时控制器\",\r\n        type: \"time\",\r\n        content: {\r\n          time:\"\"\r\n        },\r\n        script:\"\",\r\n        desc:\"\",\r\n        creator:this.username,\r\n      }\r\n    }\r\n    else if(types ==='sql'){\r\n      this.ControllerData = {\r\n        name: \"数据库操作\",\r\n        type: \"sql\",\r\n        content: {},\r\n        script:\"\",\r\n        desc:\"\",\r\n        creator:this.username,\r\n      }\r\n    }else {\r\n      throw new Error('types is not defined');\r\n    }\r\n    const Controllerresponse = await this.$api.createStepControll(this.ControllerData)\r\n      if (Controllerresponse.status === 201) {\r\n            const response = await this.$api.createTestCaseStep({ case: this.case.id, controllerStep:Controllerresponse.data.id, sort: order_s })\r\n              if (response.status === 201) {\r\n                this.getCaseStep(this.case.id)\r\n              }\r\n          }\r\n    },\r\n\r\n  rowOpenORFold(isExpand) {\r\n\t      this.treeKey = +new Date()\r\n\t      this.isExpand = isExpand\r\n\t    },\r\n   async envClick(item,data){\r\n      let params;\r\n      if(item.name==='默认环境'){\r\n        params = {host:{}}\r\n      }else {\r\n        params = {host:{host:item.host,name:item.name}}\r\n      }\r\n      const response = await this.$api.updatenewInterface( data.id, params);\r\n      if (response.status === 200) {\r\n          ElMessage({\r\n            type: 'success',\r\n            message: '变更成功',\r\n            duration: 1000\r\n          });\r\n          }\r\n      this.getCaseStep(this.case.id)\r\n    },\r\n\r\n  },\r\n  created() {\r\n    this.reaiTime();\r\n    if (this.case && this.case.id) {\r\n       this.getCaseStep(this.case.id)\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    // 添加全局样式确保拖拽提示线显示\r\n    const style = document.createElement('style');\r\n    style.innerHTML = `\r\n      .el-tree-node__drop-indicator {\r\n        position: absolute !important;\r\n        left: 0 !important;\r\n        right: 0 !important;\r\n        height: 5px !important;\r\n        background-color: #2b85e4 !important;\r\n        z-index: 100000 !important;\r\n        pointer-events: none !important;\r\n        border-radius: 2px !important;\r\n        box-shadow: 0 0 6px rgba(43, 133, 228, 0.8) !important;\r\n        display: block !important;\r\n        visibility: visible !important;\r\n        opacity: 1 !important;\r\n      }\r\n    `;\r\n    document.head.appendChild(style);\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.el-dropdown-menu__item {\r\n  color: #606266;\r\n  cursor: pointer !important;\r\n  z-index: 9999;\r\n  &:hover {\r\n    background-color: #ebf5ff;\r\n  }\r\n}\r\n\r\n.el-dropdown {\r\n  z-index: 800;\r\n}\r\n.el-tree {\r\n    --el-tree-node-hover-background-color: #ecf5ff;\r\n    margin-right: 50px;\r\n    position: relative;\r\n    overflow: visible !important;\r\n}\r\n.tree-component {\r\n  height: 100vh;\r\n  margin-left: 15px;\r\n  padding-right: 10px;\r\n  padding-top: 15px;\r\n  box-shadow: 5px 0 5px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n/* 确保树节点允许溢出内容显示，以支持下拉菜单 */\r\n:deep(.el-tree-node__content) {\r\n    padding: 4px 4px 4px 0px;\r\n    height: auto;\r\n    overflow: visible !important;\r\n}\r\n\r\n/* 卡片通用样式 */\r\n.step-card {\r\n  border-radius: 10px;\r\n  margin-bottom: 8px;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  width: 100%; \r\n  position: relative;\r\n  z-index: 5;\r\n  overflow: visible !important;\r\n}\r\n\r\n/* 不同类型卡片的边框颜色 */\r\n.step-card-api {\r\n  border-left: 4px solid #61649f;\r\n}\r\n.step-card-if {\r\n  border-left: 4px solid rgb(230, 162, 60);\r\n}\r\n.step-card-for {\r\n  border-left: 4px solid rgb(2, 167, 240);\r\n}\r\n.step-card-script {\r\n  border-left: 4px solid rgb(123, 77, 18);\r\n}\r\n.step-card-sql {\r\n  border-left: 4px solid rgb(120, 56, 135);\r\n}\r\n.step-card-time {\r\n  border-left: 4px solid rgb(103, 194, 58);\r\n}\r\n\r\n.card-header {\r\n  padding: 12px;\r\n  width: 100%;\r\n}\r\n\r\n.card-content-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  overflow: visible; /* 修改为visible，允许下拉菜单显示在容器外 */\r\n  max-width: 100%; /* 确保不超出父容器 */\r\n}\r\n\r\n.card-inner {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start; /* 从左开始 */\r\n  width: 100%;\r\n  padding: 0 20px; /* 增加左右内边距从15px到20px */\r\n  min-height: 40px; /* 确保最小高度 */\r\n  position: relative; /* 添加相对定位 */\r\n  overflow: visible; /* 确保下拉菜单可见 */\r\n}\r\n\r\n.card-left {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 180px; /* 进一步增加宽度 */\r\n  min-width: 180px; /* 确保最小宽度也相应增加 */\r\n  justify-content: flex-start; /* 左对齐 */\r\n  flex-shrink: 0; /* 防止被压缩 */\r\n  margin-right: 15px; /* 增加右边距 */\r\n  gap: 10px; /* 使用gap属性控制子元素间距 */\r\n}\r\n\r\n.card-center {\r\n  margin-left: 10px; /* 减少左边距从50px到10px */\r\n  flex-grow: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center; /* 居中对齐操作按钮 */\r\n  gap: 8px; /* 按钮之间增加间距 */\r\n}\r\n\r\n.action-button {\r\n  margin-left: 0; /* 删除左边距，使用gap控制间距 */\r\n  transition: all 0.2s;\r\n}\r\n\r\n.action-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.method-tag {\r\n  min-width: 60px;\r\n  margin-left: 0; /* 移除左边距，依靠父元素的gap来控制间距 */\r\n  font-size: 15px;\r\n  flex-shrink: 0; /* 防止被压缩 */\r\n}\r\n\r\n.card-url {\r\n  font-size: 15px;\r\n  margin: 0 10px;\r\n  flex: 1;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 60%; /* 防止URL过长 */\r\n}\r\n\r\n.card-name {\r\n  font-size: 14px;\r\n  color: #888;\r\n  margin-left: 5px;\r\n}\r\n\r\n.el-tag {\r\n  color: #ffffff;\r\n  width: 80px;\r\n  height: 30px;\r\n  text-align: center;\r\n  font-size: 13px;\r\n  line-height: 30px;\r\n  margin-right: 0; /* 移除右边距，使用上面的gap代替 */\r\n  margin-left: 0; /* 移除左边距，使用上面的gap代替 */\r\n  flex-shrink: 0; /* 防止标签被压缩 */\r\n}\r\n\r\n.step-icon {\r\n  display: inline-flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  margin-right: 0; /* 移除右边距，使用上面的gap代替 */\r\n  font-weight: bold;\r\n  border-radius: 50%;\r\n  text-align: center;\r\n  background-color: white;\r\n  border: 2px solid currentColor;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n  box-sizing: border-box;\r\n  flex-shrink: 0; /* 防止图标被压缩 */\r\n}\r\n\r\n.input-def {\r\n  width: 160px;\r\n  margin-right: 0; /* 移除右边距 */\r\n  margin-bottom: 0; /* 移除底部边距 */\r\n  flex-shrink: 0; /* 防止输入框被压缩 */\r\n}\r\n\r\n.if-content {\r\n  flex-wrap: nowrap;\r\n  gap: 8px;\r\n  justify-content: flex-start; /* 左对齐内容 */\r\n  width: 100%;\r\n  padding: 8px 0;\r\n  overflow: visible; /* 修改为visible，允许下拉菜单显示 */\r\n  position: relative; /* 添加相对定位 */\r\n}\r\n\r\n.if-controls-wrapper {\r\n  display: flex;\r\n  flex-wrap: wrap; /* 允许在小屏幕上换行 */\r\n  align-items: center;\r\n  width: 100%;\r\n  gap: 8px;\r\n  position: relative; /* 添加相对定位 */\r\n  overflow: visible !important; /* 确保下拉菜单可见 */\r\n  min-width: min-content; /* 确保内容不会被压缩 */\r\n  z-index: 20; /* 增加层级 */\r\n}\r\n\r\n.radio-group {\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  gap: 20px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.for-controls-wrapper {\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: visible;\r\n}\r\n\r\n.loop {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n  flex-wrap: wrap; /* 允许换行 */\r\n  gap: 20px;\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  overflow: visible;\r\n  position: relative;\r\n}\r\n\r\n.loop-control {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  white-space: nowrap;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.loop-details {\r\n  background-color: #f9f9f9;\r\n  border-radius: 0 0 8px 8px;\r\n  padding: 10px 15px;\r\n  margin-top: 5px;\r\n  width: 100%;\r\n  overflow: visible;\r\n  position: relative;\r\n}\r\n\r\n.script-button {\r\n  color: #333;\r\n  border: none;\r\n  outline: none;\r\n  font-size: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n  width: auto;\r\n  margin: 0; /* 移除边距 */\r\n  padding: 0 10px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.script-button .el-icon {\r\n  margin-left: 5px;\r\n  font-size: 14px;\r\n}\r\n\r\n.code_mod {\r\n  margin-bottom: 8px;\r\n  text-align: center;\r\n}\r\n\r\n.code_mod .el-button {\r\n  margin: 0 auto;\r\n  display: block;\r\n  width: 100%;\r\n}\r\n\r\n.script-editor {\r\n  padding: 10px 0;\r\n  border-top: 1px dashed #eee;\r\n  margin-top: 10px;\r\n  width: 100%;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.sql-message {\r\n  text-align: center;\r\n  color: #666;\r\n  padding: 15px 0;\r\n  font-style: italic;\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.time-controller {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start; /* 左对齐 */\r\n  width: 100%;\r\n  flex-wrap: nowrap;\r\n}\r\n\r\n.time-control {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 确保树组件中的内容宽度统一 */\r\n:deep(.el-tree-node__content) {\r\n  padding: 4px 4px 4px 0px;\r\n  height: auto;\r\n  width: 100%;\r\n  background: #f9f9f9;\r\n  min-width: fit-content; /* 确保内容不会被压缩 */\r\n}\r\n\r\n.card-main-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  overflow: visible; /* 修改为visible以允许内容溢出 */\r\n  width: 100%;\r\n  min-width: fit-content; /* 确保内容不会被压缩 */\r\n}\r\n\r\n/* 顶部操作按钮样式统一 */\r\n.stepStyle {\r\n  margin-left: 45px;\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n}\r\n\r\n/* 现代化头部样式 */\r\n.page-header-card {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 24px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.page-header-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  height: 100%;\r\n  width: 4px;\r\n  background: linear-gradient(to bottom, #409eff, #2585ff);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-button {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  border: none;\r\n  background: transparent;\r\n  color: #606266;\r\n  transition: all 0.3s;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.back-button:hover {\r\n  color: #409eff;\r\n  transform: translateX(-2px);\r\n  background-color: rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.page-title {\r\n  margin-left: 16px;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  color: #303133;\r\n  border-left: 4px solid #409eff;\r\n  padding-left: 16px;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-actions .action-button {\r\n  margin-left: 10px;\r\n  transition: all 0.2s;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 4px;\r\n  height: 36px;\r\n}\r\n\r\n.header-actions .action-button:first-child {\r\n  margin-left: 0;\r\n}\r\n\r\n.header-actions .action-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.save-button {\r\n  font-weight: 500;\r\n  min-width: 86px;\r\n}\r\n\r\n.page-container {\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n  padding: 16px;\r\n}\r\n\r\n:deep(.el-tree) {\r\n  overflow: visible !important;\r\n}\r\n\r\n:deep(.el-scrollbar__wrap) {\r\n  overflow-x: auto !important;\r\n  overflow-y: auto !important;\r\n}\r\n\r\n:deep(.el-scrollbar__view) {\r\n  overflow: visible;\r\n}\r\n\r\n:deep(.judgment-popper) {\r\n  z-index: 9999 !important;\r\n  position: absolute !important; /* 改为绝对定位 */\r\n  transform-origin: center top !important;\r\n}\r\n\r\n:deep(.el-popper), \r\n:deep(.el-dropdown-menu) {\r\n  z-index: 99999 !important;\r\n}\r\n\r\n:deep(.el-dropdown-menu .el-dropdown-menu__item) {\r\n  cursor: pointer !important;\r\n}\r\n\r\n/* 优化所有控制器元素的z-index */\r\n.el-dropdown,\r\n.el-radio-group {\r\n  z-index: 5;\r\n}\r\n\r\n/* 确保el-tree和卡片显示正常 */\r\n:deep(.el-tree-node) {\r\n  overflow: visible;\r\n}\r\n\r\n:deep(.el-card) {\r\n  overflow: visible;\r\n  --el-card-padding:0;\r\n}\r\n.card-header[data-v-731abb76] {\r\n    padding: 6px;\r\n    width: 100%;\r\n}\r\n:deep(.el-card__body) {\r\n  overflow: visible;\r\n}\r\n\r\n/* 进一步增强下拉菜单的显示 */\r\n:deep(.el-dropdown__popper) {\r\n  position: absolute !important; /* 改为绝对定位 */\r\n  z-index: 99999 !important;\r\n}\r\n\r\n.env-dropdown-item {\r\n  padding: 8px 20px;\r\n  cursor: pointer !important;\r\n  display: block;\r\n  width: 100%;\r\n  text-align: left;\r\n}\r\n\r\n/* 修复HTTP请求环境选择的样式 */\r\n.custom-dropdown-item {\r\n  padding: 10px 20px;\r\n  cursor: pointer !important;\r\n  display: block;\r\n  width: 100%;\r\n  text-align: left;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n  box-sizing: border-box;\r\n  white-space: nowrap;\r\n  z-index: 999999 !important;\r\n  position: relative;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.custom-dropdown-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.custom-dropdown-item:active {\r\n  background-color: #d9ecff;\r\n  color: #409eff;\r\n}\r\n\r\n/* 自定义树组件样式 */\r\n.custom-tree {\r\n  overflow: visible !important;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* 添加媒体查询，针对小屏幕进行优化 */\r\n@media (max-width: 1200px) {\r\n  .card-inner {\r\n    flex-wrap: wrap;\r\n    padding: 10px;\r\n  }\r\n  \r\n  .card-left {\r\n    width: auto;\r\n    min-width: 150px;\r\n    margin-right: 10px;\r\n  }\r\n  \r\n  .if-controls-wrapper {\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .loop-control {\r\n    margin-bottom: 10px;\r\n  }\r\n  \r\n  .card-actions {\r\n    justify-content: flex-end;\r\n  }\r\n  \r\n  .card-center {\r\n    width: 100%;\r\n    margin-top: 10px;\r\n    margin-left: 0;\r\n  }\r\n\r\n  .card-url {\r\n    max-width: 100%;\r\n    margin: 5px 0;\r\n  }\r\n  \r\n  .input-def {\r\n    width: 100%;\r\n    margin-bottom: 8px;\r\n  }\r\n  \r\n  .el-tag {\r\n    width: auto;\r\n    padding: 0 8px;\r\n  }\r\n  \r\n  /* HTTP请求专用样式 */\r\n  .step-card-api .card-center {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .step-card-api .card-url {\r\n    max-width: 100%;\r\n    width: 100%;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    order: 2;\r\n  }\r\n  \r\n  .step-card-api .el-dropdown {\r\n    margin-bottom: 5px;\r\n    order: 1;\r\n  }\r\n  \r\n  .step-card-api .card-name {\r\n    order: 3;\r\n    margin-top: 5px;\r\n  }\r\n  \r\n  /* 条件控制器专用样式 */\r\n  .step-card-if .if-controls-wrapper {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .step-card-if .if-controls-wrapper .el-input:last-child {\r\n    grid-column: span 2;\r\n  }\r\n  \r\n  /* 循环控制器专用样式 */\r\n  .step-card-for .for-controls-wrapper {\r\n    width: 100%;\r\n  }\r\n  \r\n  .step-card-for .loop {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .step-card-for .loop-control {\r\n    width: 100%;\r\n    margin-bottom: 8px;\r\n  }\r\n}\r\n\r\n/* 树容器样式 - 添加横向滚动支持 */\r\n.tree-container {\r\n  margin-left: 20px;\r\n  margin-right: 20px;\r\n  min-width: 90%;\r\n  overflow-x: auto;\r\n  padding-bottom: 20px; /* 为横向滚动条预留空间 */\r\n}\r\n\r\n/* 确保嵌套步骤有足够空间显示 */\r\n:deep(.el-tree-node__children) {\r\n  margin-left: 20px; /* 增加嵌套层级的缩进 */\r\n  position: relative;\r\n}\r\n\r\n/* 为嵌套层级添加左侧指示线 */\r\n:deep(.el-tree-node__children)::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  height: 100%;\r\n  width: 2px;\r\n  background-color: #ebeef5;\r\n  z-index: 1;\r\n}\r\n\r\n/* 修复嵌套步骤的宽度问题 */\r\n:deep(.el-tree-node.is-expanded > .el-tree-node__children) {\r\n  display: block;\r\n  width: 100%;\r\n  min-width: fit-content;\r\n  overflow: visible;\r\n}\r\n\r\n/* 确保步骤卡片相对于树的位置 */\r\n.step-card {\r\n  border-radius: 10px;\r\n  margin-bottom: 8px;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  width: 100%; \r\n  position: relative;\r\n  z-index: 5;\r\n  overflow: visible !important;\r\n  min-width: fit-content; /* 确保卡片不会被压缩 */\r\n}\r\n\r\n/* 添加el-tree拖拽提示线样式 */\r\n:deep(.el-tree-node__drop-indicator) {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px !important; /* 增加高度使其更明显 */\r\n  background-color: #2b85e4 !important; /* 更鲜明的蓝色 */\r\n  z-index: 99999 !important; /* 确保最高层级 */\r\n  pointer-events: none;\r\n  border-radius: 2px;\r\n  box-shadow: 0 0 6px rgba(43, 133, 228, 0.8); /* 添加阴影效果使线更明显 */\r\n}\r\n\r\n/* 加强拖拽提示线可见性 */\r\n:deep(.el-tree-node.is-drop-inner > .el-tree-node__content) {\r\n  background-color: rgba(43, 133, 228, 0.2) !important;\r\n  border: 1px dashed #2b85e4 !important; /* 添加边框 */\r\n}\r\n\r\n/* 鼠标拖拽样式 */\r\n:deep(.el-tree-node.is-dragging) {\r\n  opacity: 0.7;\r\n  background-color: #f0f9ff !important;\r\n  border: 1px dashed #2b85e4 !important;\r\n}\r\n\r\n:deep(.el-tree-node.is-dragging-over) {\r\n  background-color: #e6f1fc !important;\r\n}\r\n\r\n/* 确保drop-indicator不被遮挡 */\r\n:deep(.el-tree) {\r\n  position: relative; /* 确保相对定位 */\r\n}\r\n\r\n:deep(.el-tree-node) {\r\n  position: relative; /* 确保相对定位 */\r\n}\r\n\r\n/* 全局覆盖el-tree-node__drop-indicator样式，防止被其他样式覆盖 */\r\nbody .el-tree-node__drop-indicator {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px !important;\r\n  background-color: #2b85e4 !important;\r\n  z-index: 99999 !important;\r\n  pointer-events: none;\r\n  border-radius: 2px;\r\n  box-shadow: 0 0 6px rgba(43, 133, 228, 0.8);\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .card-main-content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .card-actions {\r\n    width: 100%;\r\n    justify-content: center;\r\n    margin-top: 10px;\r\n  }\r\n  \r\n  :deep(.el-row) {\r\n    margin-left: 0 !important;\r\n    margin-right: 0 !important;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-wrap: wrap;\r\n    gap: 8px;\r\n    justify-content: flex-end;\r\n  }\r\n  \r\n  .header-actions .action-button {\r\n    margin-left: 5px;\r\n  }\r\n  \r\n  /* 减小间距和调整布局 */\r\n  .card-inner {\r\n    padding: 5px 10px;\r\n  }\r\n  \r\n  .el-card {\r\n    margin-bottom: 5px;\r\n  }\r\n  \r\n  .card-left {\r\n    min-width: 130px;\r\n    gap: 5px;\r\n  }\r\n  \r\n  .step-icon {\r\n    width: 20px;\r\n    height: 20px;\r\n    font-size: 12px;\r\n    line-height: 20px;\r\n  }\r\n  \r\n  .el-tag {\r\n    font-size: 12px;\r\n    height: 25px;\r\n    line-height: 25px;\r\n  }\r\n  \r\n  /* 修复脚本编辑器在小屏幕上的显示 */\r\n  .script-editor {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  :deep(.script-editor .el-col) {\r\n    width: 100% !important;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 确保步骤卡片相对于树的位置 */\r\n.step-card {\r\n  border-radius: 10px;\r\n  margin-bottom: 8px;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  width: 100%; \r\n  position: relative;\r\n  z-index: 5;\r\n  overflow: visible !important;\r\n  min-width: fit-content; /* 确保卡片不会被压缩 */\r\n}\r\n\r\n/* 修复嵌套步骤的滚动问题 */\r\n:deep(.el-scrollbar__wrap) {\r\n  overflow-x: scroll !important;\r\n}\r\n\r\n/* 确保树组件在横向滚动时内容不会被截断 */\r\n:deep(.el-tree) {\r\n  min-width: fit-content;\r\n  width: 100%;\r\n}\r\n\r\n:deep(.el-tree-node) {\r\n  white-space: nowrap;\r\n  min-width: fit-content;\r\n}\r\n\r\n/* 添加el-tree拖拽提示线样式 */\r\n</style>", "import { render } from \"./TestCaseDetail.vue?vue&type=template&id=5780c1de&scoped=true\"\nimport script from \"./TestCaseDetail.vue?vue&type=script&lang=js\"\nexport * from \"./TestCaseDetail.vue?vue&type=script&lang=js\"\n\nimport \"./TestCaseDetail.vue?vue&type=style&index=0&id=5780c1de&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-5780c1de\"]])\n\nexport default __exports__"], "names": ["class", "style", "slot", "color", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_button", "onClick", "$options", "goBack", "_component_el_icon", "_component_Back", "_hoisted_5", "_toDisplayString", "case", "_hoisted_6", "_component_el_tooltip", "content", "placement", "disabled", "$data", "isExpand", "_createBlock", "_cache", "$event", "rowOpenORFold", "_component_ArrowDown", "_component_ArrowUp", "type", "runCase", "_component_VideoPlay", "editCaseSave", "addCaseSave", "_component_Document", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_7", "_component_el_form", "model", "editForm", "rules", "rulesCase", "ref", "_component_el_form_item", "label", "prop", "size", "_component_el_input", "name", "placeholder", "project_id", "desc", "_hoisted_8", "stepCount", "_hoisted_9", "_component_el_tag", "showApiCite", "AddController", "_component_el_scrollbar", "height", "always", "onScroll", "handleScroll", "_hoisted_10", "_component_el_tree", "data", "steps", "props", "defaultProps", "draggable", "key", "<PERSON><PERSON><PERSON>", "onNodeClick", "handleStepClick", "allowDrop", "onNodeDrop", "updateStepOrder", "onNodeDragStart", "handleDragStart", "onNodeDragEnter", "handleDragEnter", "onNodeDragLeave", "handleDragLeave", "onNodeDragOver", "handleDragOver", "onNodeDragEnd", "handleDragEnd", "node", "stepInfo", "_component_el_card", "_normalizeClass", "_hoisted_11", "align", "justify", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "getCardIndex", "parent", "_hoisted_16", "method", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_component_el_dropdown", "trigger", "_withModifiers", "dropdown", "_withCtx", "_component_el_dropdown_menu", "_Fragment", "_renderList", "testEnvsWithDefault", "item", "_component_el_dropdown_item", "id", "command", "envClick", "Object", "keys", "host", "length", "_hoisted_30", "url", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "variable", "_component_el_select", "JudgmentMode", "options", "_component_el_option", "value", "toggleExpand", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_component_el_radio_group", "select", "_component_el_radio", "dlg", "_hoisted_42", "_hoisted_43", "_hoisted_44", "cycleIndex", "_hoisted_45", "_component_el_input_number", "cycleInterval", "min", "max", "_hoisted_46", "_hoisted_47", "_hoisted_48", "variableName", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "inputDlg", "onBlur", "cancelEditing", "maxlength", "plain", "startEditing", "_component_Edit", "_component_Editor", "script", "lang", "theme", "_component_el_divider", "_hoisted_55", "addSetUptCodeMod", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "time", "_hoisted_69", "_component_el_switch", "status", "switchClick", "icon", "copyTree", "circle", "delTree", "addApiDlg", "_component_apiCite", "onChildEvent", "handleChildData", "onCloseModal", "handleCloseModal", "_component_el_drawer", "editCaseDlg", "_component_newEditCase", "onCloseDrawer", "handleClose", "Interface_id", "copyDlg", "ResultDlg", "_hoisted_70", "_component_el_descriptions", "title", "border", "column", "_component_el_descriptions_item", "_hoisted_71", "runScentResult", "all", "_hoisted_72", "success", "_hoisted_73", "fail", "_hoisted_74", "error", "_component_el_table", "cases", "_component_el_table_column", "default", "_component_caseResult", "result", "row", "_hoisted_75", "_hoisted_76", "status_cede", "state", "_hoisted_77", "_hoisted_78", "_hoisted_79", "components", "apiCite", "newEditCase", "caseResult", "Editor", "creator", "required", "message", "ControllerData", "test", "computed", "mapState", "<PERSON><PERSON><PERSON><PERSON>", "this", "testEnvs", "unshift", "username", "window", "sessionStorage", "getItem", "children", "watch", "deep", "methods", "mapMutations", "index", "childNodes", "indexOf", "$refs", "CaseRef", "validate", "async", "vaild", "params", "pro", "response", "$api", "createTestCase", "ElMessage", "duration", "modifier", "update_time", "$tools", "newTime", "create_time", "project", "updateTestCase", "editStepSave", "ControllerStepsData", "filter", "step", "map", "updatesStepControll", "getCaseStep", "reaiTime", "back_type", "$router", "push", "clearCaseInfo", "interfaceStep", "$nextTick", "childRef", "getInterfaceInfo", "addCaseDlg", "getRowClassName", "draggingNode", "dropNode", "allowedParentTypes", "includes", "parentId", "isLastCall", "event", "stopPropagation", "order_s", "createTestCaseStep", "sort", "parent_id", "Controllerresponse", "copyStepControll", "setpId", "childCalls", "child", "isLast", "Promise", "delTestCaseStep", "res", "delStepControll", "controllerStep", "newDataArray", "for<PERSON>ach", "newItem", "createsTestCaseStep", "getTestCaseStep", "countSteps", "count", "handleItemClick", "preventDefault", "console", "log", "itemName", "dataType", "dataId", "setParentIds", "parentSort", "childIndex", "parentIndex", "updateCaseStepOrder", "ElNotification", "envId", "env", "scene", "runCases", "$message", "input", "focus", "e", "tp", "ev", "indicator", "document", "querySelector", "display", "visibility", "opacity", "addEventListener", "mouseY", "clientY", "treeElement", "elementTop", "getBoundingClientRect", "top", "scrollBy", "innerHeight", "dropType", "types", "Error", "createStepControll", "Date", "updatenewInterface", "created", "mounted", "createElement", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "__exports__", "render"], "sourceRoot": ""}