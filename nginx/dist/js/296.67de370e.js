"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[296],{84296:function(e,t,a){a.r(t),a.d(t,{default:function(){return gs}});var s=a(56768),i=a(45130),r=a(24232);const o={class:"outer"},n={class:"box"},l={class:"report-header-content"},c={class:"task-info-section"},p={class:"task-title-area"},h={class:"task-title-container"},u={class:"task-description"},m={class:"desc-text"},d={class:"metrics-section"},g={class:"metric-card"},f={class:"metric-icon"},D={class:"metric-content"},_={class:"metric-values"},v={class:"metric-item"},k={class:"metric-value"},b={class:"metric-item"},y={class:"metric-value"},T={class:"metric-card"},R={class:"metric-icon"},C={class:"metric-content"},L={class:"metric-values"},F={class:"metric-item"},w={class:"metric-value"},x={class:"metric-item"},S={class:"metric-value"},$={class:"metric-card"},I={class:"metric-icon"},M={class:"metric-content"},U={class:"metric-values"},W={class:"metric-item"},P={class:"metric-value"},E={class:"metric-item"},q={class:"metric-value"},N={class:"metric-card"},A={class:"metric-icon"},V={class:"metric-content"},O={class:"metric-values"},B={class:"metric-item"},X={class:"metric-value"},G={class:"metric-item"},z={class:"metric-value"},H={class:"tab-navigation-container"},K={class:"action-buttons"},j={key:0,class:"munu"},Q={style:{display:"flex","align-items":"center","justify-content":"space-between"}},J={class:"title-info"},Y={style:{display:"flex","align-items":"center"}},Z={class:"spacing"},ee={style:{"margin-left":"5px"}},te={style:{"margin-bottom":"30px"}},ae={class:"interface-name"},se={class:"charts-container"},ie={class:"chart-wrapper"},re={class:"chart-wrapper"},oe={class:"chart-wrapper"},ne={class:"chart-wrapper"},le={class:"chart-wrapper"},ce={class:"chart-wrapper"},pe={class:"title-info",style:{"margin-top":"30px"}},he={key:1,class:"p_text",style:{"margin-top":"15px","line-height":"1.8"}},ue={class:"munu"},me={class:"gui-controls"},de={class:"title-info"},ge={key:0,class:"gui-server-selection"},fe={key:0,class:"test-not-running"},De={key:1,class:"iframe-container"},_e=["src"],ve={key:0,class:"iframe-error"},ke={style:{"margin-top":"10px","text-align":"center"}},be={key:2,class:"no-gui-message"},ye={key:1,class:"munu"},Te={class:"title-info"},Re={class:"ws-status-indicator"},Ce={class:"monitor-grid"},Le={class:"monitor-card"},Fe={class:"metric-item"},we={class:"metric-item"},xe={class:"metric-item"},Se={class:"metric-item"},$e={class:"monitor-card"},Ie={class:"metric-item"},Me={class:"metric-item"},Ue={class:"metric-item"},We={class:"metric-item"},Pe={class:"monitor-card"},Ee={class:"metric-item"},qe={class:"metric-item"},Ne={class:"metric-item"},Ae={class:"metric-item"},Ve={key:2,class:"munu"},Oe={key:0,style:{"margin-top":"18px"}},Be=["src"],Xe={key:3,class:"munu"},Ge={class:"title-info"},ze={class:"log-controls"},He={class:"log-container"},Ke={class:"log-time"},je={class:"log-level"},Qe={key:0,class:"log-category"},Je={class:"log-url"},Ye={key:2,class:"log-message"},Ze={key:4,class:"log-details"},et={key:0,class:"no-logs"},tt={class:"empty-log-content"},at={class:"analysis-dialog-content"},st={class:"analysis-section"},it={class:"score-display"},rt={class:"score-text"},ot={class:"analysis-section"},nt={key:0},lt={class:"analysis-section"},ct={key:0},pt={class:"recommendations-list"},ht={key:0,class:"config-dialog-content"},ut={class:"config-section-title"},mt={class:"config-item"},dt={class:"config-value"},gt={class:"config-item"},ft={class:"config-value"},Dt={class:"config-item"},_t={class:"config-value"},vt={class:"config-item"},kt={class:"config-value"},bt={class:"config-item"},yt={class:"config-value"},Tt={class:"config-item"},Rt={class:"config-value"},Ct={class:"config-section-title"},Lt={class:"config-item"},Ft={class:"config-value"},wt={class:"config-item"},xt={class:"config-value"},St={class:"config-item"},$t={class:"config-value"},It={class:"config-item"},Mt={class:"config-value"},Ut={class:"config-section-title"},Wt={class:"config-item"},Pt={class:"config-value"},Et={class:"config-item"},qt={class:"config-value"},Nt={class:"config-item"},At={class:"config-value"},Vt={class:"config-item"},Ot={class:"config-value"},Bt={class:"notification-dialog-content"},Xt={class:"notification-dialog-header"},Gt={class:"notification-title"},zt={class:"notification-type-selector"},Ht={class:"option-icon email-icon"},Kt={class:"option-icon webhook-icon"},jt={class:"option-icon dingtalk-icon"},Qt={class:"option-icon wechat-icon"},Jt={class:"notification-report-preview"},Yt={class:"preview-header"},Zt={class:"preview-content"},ea={class:"preview-item"},ta={class:"item-value"},aa={class:"preview-item"},sa={class:"item-value"},ia={class:"metrics-section"},ra={class:"metrics-row"},oa={class:"metric-item-wide"},na={class:"metric-value"},la={class:"metric-item-wide"},ca={class:"metric-value"},pa={class:"metrics-row"},ha={class:"metric-item-wide"},ua={class:"metric-value"},ma={class:"metric-item-wide"},da={class:"metric-value"},ga={class:"notification-dialog-footer"},fa={class:"baseline-info-hint"},Da={class:"dialog-footer"},_a={style:{display:"flex","justify-content":"space-between","align-items":"center"}},va={style:{"font-size":"12px",color:"#999"}},ka={class:"dialog-footer"},ba={class:"comparison-container"},ya={class:"comparison-card baseline"},Ta={class:"comparison-header"},Ra={class:"comparison-metrics"},Ca={class:"metric-row"},La={class:"metric-value"},Fa={class:"metric-row"},wa={class:"metric-value"},xa={class:"metric-row"},Sa={class:"metric-value"},$a={class:"metric-row"},Ia={class:"metric-value"},Ma={class:"metric-row"},Ua={class:"metric-value"},Wa={class:"comparison-card current"},Pa={class:"comparison-header"},Ea={class:"comparison-metrics"},qa={class:"metric-row"},Na={class:"metric-value"},Aa={class:"metric-row"},Va={class:"metric-value"},Oa={class:"metric-row"},Ba={class:"metric-value"},Xa={class:"metric-row"},Ga={class:"metric-value"},za={class:"metric-row"},Ha={class:"metric-value"},Ka={class:"comparison-conclusion"},ja={class:"conclusion-header"},Qa={class:"conclusion-content"},Ja={key:0},Ya={key:1},Za={class:"dialog-footer"};function es(e,t,a,es,ts,as){const ss=(0,s.g2)("Edit"),is=(0,s.g2)("el-icon"),rs=(0,s.g2)("el-button"),os=(0,s.g2)("el-tag"),ns=(0,s.g2)("el-input"),ls=(0,s.g2)("icon"),cs=(0,s.g2)("el-card"),ps=(0,s.g2)("el-menu-item"),hs=(0,s.g2)("el-menu"),us=(0,s.g2)("ArrowLeft"),ms=(0,s.g2)("Check"),ds=(0,s.g2)("arrow-down"),gs=(0,s.g2)("Document"),fs=(0,s.g2)("el-dropdown-item"),Ds=(0,s.g2)("Monitor"),_s=(0,s.g2)("DocumentCopy"),vs=(0,s.g2)("Files"),ks=(0,s.g2)("el-dropdown-menu"),bs=(0,s.g2)("el-dropdown"),ys=(0,s.g2)("Bell"),Ts=(0,s.g2)("TrendCharts"),Rs=(0,s.g2)("DataAnalysis"),Cs=(0,s.g2)("PieChart"),Ls=(0,s.g2)("Setting"),Fs=(0,s.g2)("el-divider"),ws=(0,s.g2)("SwitchButton"),xs=(0,s.g2)("View"),Ss=(0,s.g2)("el-table-column"),$s=(0,s.g2)("el-tooltip"),Is=(0,s.g2)("el-table"),Ms=(0,s.g2)("ResponseTimeChart"),Us=(0,s.g2)("el-alert"),Ws=(0,s.g2)("el-empty"),Ps=(0,s.g2)("el-switch"),Es=(0,s.g2)("Search"),qs=(0,s.g2)("el-option"),Ns=(0,s.g2)("el-select"),As=(0,s.g2)("el-scrollbar"),Vs=(0,s.g2)("el-progress"),Os=(0,s.g2)("el-dialog"),Bs=(0,s.g2)("el-col"),Xs=(0,s.g2)("el-row"),Gs=(0,s.g2)("el-form-item"),zs=(0,s.g2)("Message"),Hs=(0,s.g2)("Link"),Ks=(0,s.g2)("ChatDotRound"),js=(0,s.g2)("ChatRound"),Qs=(0,s.g2)("el-form"),Js=(0,s.g2)("Position"),Ys=(0,s.g2)("el-input-number"),Zs=(0,s.g2)("InfoFilled");return(0,s.uX)(),(0,s.CE)(s.FK,null,[(0,s.bF)(As,{height:"calc(100vh - 100px)"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",o,[(0,s.Lk)("div",n,[(0,s.bF)(cs,{class:"report-header-card"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",l,[(0,s.Lk)("div",c,[(0,s.Lk)("div",p,[(0,s.Lk)("div",h,[(0,s.bF)(rs,{class:"task-name-button",type:"text",onClick:[as.editTaskName,t[0]||(t[0]=(0,i.D$)(()=>{},["stop"]))]},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(ts.taskName)+" ",1),(0,s.bF)(is,{class:"edit-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(ss)]),_:1})]),_:1},8,["onClick"]),"已完成"===ts.taskType?((0,s.uX)(),(0,s.Wv)(os,{key:0,class:"task-status-tag success",effect:"light"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(ts.taskType),1)]),_:1})):(0,s.Q3)("",!0),"运行中"===ts.taskType?((0,s.uX)(),(0,s.Wv)(os,{key:1,class:"task-status-tag running",effect:"light"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(ts.taskType),1)]),_:1})):(0,s.Q3)("",!0),"运行失败"===ts.taskType?((0,s.uX)(),(0,s.Wv)(os,{key:2,class:"task-status-tag error",effect:"light"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(ts.taskType),1)]),_:1})):(0,s.Q3)("",!0)])]),(0,s.Lk)("div",u,[(0,s.Lk)("div",m,"结果分析："+(0,r.v_)(ts.desc),1)]),ts.inputDlg&&"taskName"===ts.editingField?((0,s.uX)(),(0,s.Wv)(ns,{key:0,modelValue:ts.taskName,"onUpdate:modelValue":t[1]||(t[1]=e=>ts.taskName=e),onBlur:as.cancelEditing,ref:"input",size:"small",class:"task-name-input",onClick:t[2]||(t[2]=(0,i.D$)(()=>{},["stop"]))},null,8,["modelValue","onBlur"])):(0,s.Q3)("",!0)]),(0,s.Lk)("div",d,[(0,s.Lk)("div",g,[(0,s.Lk)("div",f,[(0,s.bF)(ls,{icon:"grommet-icons:performance","font-size":"28"})]),(0,s.Lk)("div",D,[t[39]||(t[39]=(0,s.Lk)("div",{class:"metric-title"},"性能指标",-1)),(0,s.Lk)("div",_,[(0,s.Lk)("div",v,[t[37]||(t[37]=(0,s.Lk)("span",{class:"metric-label"},"平均TPS：",-1)),(0,s.Lk)("span",k,(0,r.v_)((ts.reportData.avgTps||0).toFixed(2)),1)]),(0,s.Lk)("div",b,[t[38]||(t[38]=(0,s.Lk)("span",{class:"metric-label"},"平均响应时间：",-1)),(0,s.Lk)("span",y,(0,r.v_)(as.formatResponseTime(ts.reportData.avgResponseTime)),1)])])])]),(0,s.Lk)("div",T,[(0,s.Lk)("div",R,[(0,s.bF)(ls,{icon:"grommet-icons:cpu","font-size":"28"})]),(0,s.Lk)("div",C,[t[42]||(t[42]=(0,s.Lk)("div",{class:"metric-title"},"系统资源",-1)),(0,s.Lk)("div",L,[(0,s.Lk)("div",F,[t[40]||(t[40]=(0,s.Lk)("span",{class:"metric-label"},"执行结束后CPU：",-1)),(0,s.Lk)("span",w,(0,r.v_)(as.getCurrentCpuUsage())+"%",1)]),(0,s.Lk)("div",x,[t[41]||(t[41]=(0,s.Lk)("span",{class:"metric-label"},"执行结束后内存：",-1)),(0,s.Lk)("span",S,(0,r.v_)(as.getCurrentMemoryUsage())+"%",1)])])])]),(0,s.Lk)("div",$,[(0,s.Lk)("div",I,[(0,s.bF)(ls,{icon:"el:user","font-size":"28"})]),(0,s.Lk)("div",M,[t[45]||(t[45]=(0,s.Lk)("div",{class:"metric-title"},"创建信息",-1)),(0,s.Lk)("div",U,[(0,s.Lk)("div",W,[t[43]||(t[43]=(0,s.Lk)("span",{class:"metric-label"},"创建人：",-1)),(0,s.Lk)("span",P,(0,r.v_)(ts.reportData.executor||"未知"),1)]),(0,s.Lk)("div",E,[t[44]||(t[44]=(0,s.Lk)("span",{class:"metric-label"},"创建时间：",-1)),(0,s.Lk)("span",q,(0,r.v_)(as.formatTime(ts.reportData.create_time)),1)])])])]),(0,s.Lk)("div",N,[(0,s.Lk)("div",A,[(0,s.bF)(ls,{icon:"el:time","font-size":"28"})]),(0,s.Lk)("div",V,[t[48]||(t[48]=(0,s.Lk)("div",{class:"metric-title"},"运行信息",-1)),(0,s.Lk)("div",O,[(0,s.Lk)("div",B,[t[46]||(t[46]=(0,s.Lk)("span",{class:"metric-label"},"运行时长：",-1)),(0,s.Lk)("span",X,(0,r.v_)(as.formatDuration(ts.reportData.duration)),1)]),(0,s.Lk)("div",G,[t[47]||(t[47]=(0,s.Lk)("span",{class:"metric-label"},"错误率：",-1)),(0,s.Lk)("span",z,(0,r.v_)(ts.reportData.errorRate||0)+"%",1)])])])])])])]),_:1}),(0,s.Lk)("div",H,[(0,s.bF)(hs,{"default-active":ts.activeIndex,mode:"horizontal",onSelect:as.handleSelect,class:"report-tabs"},{default:(0,s.k6)(()=>[(0,s.bF)(ps,{index:"1"},{default:(0,s.k6)(()=>t[49]||(t[49]=[(0,s.eW)("指标详情")])),_:1,__:[49]}),(0,s.bF)(ps,{index:"2"},{default:(0,s.k6)(()=>t[50]||(t[50]=[(0,s.eW)("GUI")])),_:1,__:[50]}),(0,s.bF)(ps,{index:"3"},{default:(0,s.k6)(()=>t[51]||(t[51]=[(0,s.eW)("压力机监控")])),_:1,__:[51]}),(0,s.bF)(ps,{index:"4"},{default:(0,s.k6)(()=>t[52]||(t[52]=[(0,s.eW)("被测服务监控")])),_:1,__:[52]}),(0,s.bF)(ps,{index:"5"},{default:(0,s.k6)(()=>t[53]||(t[53]=[(0,s.eW)("日志")])),_:1,__:[53]})]),_:1},8,["default-active","onSelect"]),(0,s.Lk)("div",K,[(0,s.bF)(rs,{type:"info",onClick:as.back,class:"action-btn"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(us)]),_:1}),t[54]||(t[54]=(0,s.eW)(" 返回 "))]),_:1,__:[54]},8,["onClick"]),(0,s.bF)(rs,{type:"primary",onClick:as.saveReport,class:"action-btn"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(ms)]),_:1}),t[55]||(t[55]=(0,s.eW)(" 保存 "))]),_:1,__:[55]},8,["onClick"]),(0,s.bF)(bs,{trigger:"click",onCommand:as.handleExportCommand,class:"action-dropdown"},{dropdown:(0,s.k6)(()=>[(0,s.bF)(ks,null,{default:(0,s.k6)(()=>[(0,s.bF)(fs,{command:"excel"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(gs)]),_:1}),t[57]||(t[57]=(0,s.eW)("导出Excel报告 "))]),_:1,__:[57]}),(0,s.bF)(fs,{command:"html"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ds)]),_:1}),t[58]||(t[58]=(0,s.eW)("生成HTML报告 "))]),_:1,__:[58]}),(0,s.bF)(fs,{command:"pdf"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(_s)]),_:1}),t[59]||(t[59]=(0,s.eW)("导出PDF报告 "))]),_:1,__:[59]}),(0,s.bF)(fs,{command:"raw"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(vs)]),_:1}),t[60]||(t[60]=(0,s.eW)("导出原始数据 "))]),_:1,__:[60]})]),_:1})]),default:(0,s.k6)(()=>[(0,s.bF)(rs,{type:"primary",disabled:!ts.reportData.id,class:"action-btn"},{default:(0,s.k6)(()=>[t[56]||(t[56]=(0,s.eW)(" 导出")),(0,s.bF)(is,{class:"el-icon--right"},{default:(0,s.k6)(()=>[(0,s.bF)(ds)]),_:1})]),_:1,__:[56]},8,["disabled"])]),_:1},8,["onCommand"]),(0,s.bF)(rs,{onClick:as.showNotificationDialog,type:"success",class:"action-btn"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(ys)]),_:1}),t[61]||(t[61]=(0,s.eW)(" 通知 "))]),_:1,__:[61]},8,["onClick"]),(0,s.bF)(bs,{trigger:"click",onCommand:as.handleMoreCommand,class:"action-dropdown"},{dropdown:(0,s.k6)(()=>[(0,s.bF)(ks,null,{default:(0,s.k6)(()=>[(0,s.bF)(fs,{command:"baseline"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ts)]),_:1}),t[63]||(t[63]=(0,s.eW)("创建基准线 "))]),_:1,__:[63]}),(0,s.bF)(fs,{command:"compare"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Rs)]),_:1}),t[64]||(t[64]=(0,s.eW)("与基准线对比 "))]),_:1,__:[64]}),(0,s.bF)(fs,{command:"analyze"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Cs)]),_:1}),t[65]||(t[65]=(0,s.eW)("性能分析 "))]),_:1,__:[65]}),(0,s.bF)(fs,{command:"config"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ls)]),_:1}),t[66]||(t[66]=(0,s.eW)("查看压测配置 "))]),_:1,__:[66]})]),_:1})]),default:(0,s.k6)(()=>[(0,s.bF)(rs,{type:"info",class:"action-btn"},{default:(0,s.k6)(()=>[t[62]||(t[62]=(0,s.eW)(" 更多")),(0,s.bF)(is,{class:"el-icon--right"},{default:(0,s.k6)(()=>[(0,s.bF)(ds)]),_:1})]),_:1,__:[62]})]),_:1},8,["onCommand"])])]),"1"===ts.activeIndex?((0,s.uX)(),(0,s.CE)("div",j,[(0,s.Lk)("div",Q,[(0,s.Lk)("div",J,[(0,s.bF)(Fs,{direction:"vertical",class:"divider"}),t[67]||(t[67]=(0,s.eW)(" 压测信息 "))]),(0,s.Lk)("div",Y,["运行中"===ts.taskType?((0,s.uX)(),(0,s.Wv)(rs,{key:0,class:"runStop",type:"warning"},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(ws)]),_:1}),t[68]||(t[68]=(0,s.Lk)("span",{style:{"margin-left":"5px"}},"暂停运行",-1))]),_:1,__:[68]})):((0,s.uX)(),(0,s.Wv)(rs,{key:1,class:"runStop",type:"success",onClick:as.rerunTest},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(ws)]),_:1}),t[69]||(t[69]=(0,s.Lk)("span",{style:{"margin-left":"5px"}},"重新运行",-1))]),_:1,__:[69]},8,["onClick"]))])]),(0,s.Lk)("div",Z,[(0,s.Lk)("span",null,"任务类型："+(0,r.v_)(as.getTaskTypeText(ts.reportData.taskType||ts.reportData.task?.taskType)),1),(0,s.Lk)("span",null,"压测模式："+(0,r.v_)(as.getRunPatternText(ts.reportData.taskType)),1),(0,s.Lk)("span",null,"控制模式："+(0,r.v_)(as.getDistributedModeText(ts.reportData.task?.distributed_mode)),1),(0,s.Lk)("span",null,"并发用户数："+(0,r.v_)(as.getCurrentUserCount()),1),(0,s.Lk)("span",null,"总请求数："+(0,r.v_)(ts.reportData.totalRequests||0),1),(0,s.Lk)("span",null,"成功请求数："+(0,r.v_)(ts.reportData.successRequests||0),1)]),(0,s.bF)(rs,{style:{"margin-right":"25px",position:"relative","margin-top":"10px"},type:ts.isFilterMode?"warning":"success",onClick:as.toggleFilterMode},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(xs)]),_:1}),(0,s.Lk)("span",ee,(0,r.v_)(ts.isFilterMode?"显示全部":"仅查看"),1)]),_:1},8,["type","onClick"]),t[72]||(t[72]=(0,s.Lk)("div",{class:"table-desc"},[(0,s.Lk)("span",null,"Max(ms)：最大响应时间"),(0,s.Lk)("span",null,"Min(ms)：最小响应时间"),(0,s.Lk)("span",null,"Avg(ms)：平均响应时间"),(0,s.Lk)("span",null,"90%ile(ms)：90%响应时间线"),(0,s.Lk)("span",null,"99%ile(ms)：99%响应时间线"),(0,s.Lk)("span",null,"平均RPS：平均每秒请求数"),(0,s.Lk)("span",null,"平均TPS：平均每秒处理事务数")],-1)),(0,s.Lk)("div",te,[(0,s.bF)(Is,{ref:"table",data:as.filteredMetrics,style:{width:"100%"},border:"","empty-text":"暂无性能指标数据，请等待测试执行或测试完成后查看",onSelectionChange:as.handleSelectionChange,"row-key":e=>e.method+"_"+e.name,"reserve-selection":!0,onRowClick:as.handleRowClick},{default:(0,s.k6)(()=>[(0,s.bF)(Ss,{type:"selection",width:"55",selectable:e=>"ALL"!==e.method},null,8,["selectable"]),(0,s.bF)(Ss,{prop:"name",label:"接口名称",align:"center","min-width":"300"},{default:(0,s.k6)(e=>[(0,s.bF)($s,{content:e.row.name,placement:"top"},{default:(0,s.k6)(()=>[(0,s.Lk)("span",ae,(0,r.v_)(e.row.name),1)]),_:2},1032,["content"])]),_:1}),(0,s.bF)(Ss,{prop:"totalRequests",label:"总请求数",width:"120",align:"center"}),(0,s.bF)(Ss,{prop:"successRequests",label:"请求成功数",align:"center",width:"110"}),(0,s.bF)(Ss,{prop:"failedRequests",label:"请求失败数",align:"center",width:"110"}),(0,s.bF)(Ss,{prop:"currentUsers",label:"并发用户数",align:"center",width:"110"},{default:(0,s.k6)(e=>[(0,s.eW)((0,r.v_)(as.getUserCountForRow(e.row)),1)]),_:1}),(0,s.bF)(Ss,{prop:"maxResponseTime",label:"Max(ms)",align:"center",width:"100"}),(0,s.bF)(Ss,{prop:"minResponseTime",label:"Min(ms)",align:"center",width:"100"}),(0,s.bF)(Ss,{prop:"avgResponseTime",label:"Avg(ms)",align:"center",width:"100"}),(0,s.bF)(Ss,{prop:"p50ResponseTime",label:"50%ile(ms)",align:"center",width:"110"}),(0,s.bF)(Ss,{prop:"p90ResponseTime",label:"90%ile(ms)",align:"center",width:"110"}),(0,s.bF)(Ss,{prop:"p95ResponseTime",label:"95%ile(ms)",align:"center",width:"110"}),(0,s.bF)(Ss,{prop:"p99ResponseTime",label:"99%ile(ms)",align:"center",width:"110"}),(0,s.bF)(Ss,{prop:"avgRps",label:"平均RPS",align:"center",width:"100"}),(0,s.bF)(Ss,{prop:"avgTpsAvg",label:"平均TPS",align:"center",width:"100"}),(0,s.bF)(Ss,{prop:"errorRate",label:"错误率%",align:"center",width:"110"},{default:(0,s.k6)(e=>[e.row.errorRate>5?((0,s.uX)(),(0,s.Wv)(os,{key:0,class:"task-status-tag danger",type:"danger"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(e.row.errorRate)+"%",1)]),_:2},1024)):e.row.errorRate>1?((0,s.uX)(),(0,s.Wv)(os,{key:1,class:"task-status-tag warning",type:"warning"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(e.row.errorRate)+"%",1)]),_:2},1024)):((0,s.uX)(),(0,s.Wv)(os,{key:2,class:"task-status-tag success",type:"success"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(e.row.errorRate)+"%",1)]),_:2},1024))]),_:1})]),_:1},8,["data","onSelectionChange","row-key","onRowClick"])]),(0,s.Lk)("div",se,[(0,s.Lk)("div",ie,[((0,s.uX)(),(0,s.Wv)(Ms,{chartTitle:"平均响应时间",chartWidth:"100%",chartHeight:"350px",chartUnit:"单位(毫秒)",xData:ts.xData,seriesData:as.responseTimeSeriesData,key:"response-time-"+ts.chartUpdateKey,chartType:"responseTime"},null,8,["xData","seriesData"]))]),(0,s.Lk)("div",re,[((0,s.uX)(),(0,s.Wv)(Ms,{chartTitle:"每秒请求数(RPS)",chartWidth:"100%",chartHeight:"350px",chartUnit:"单位(个)",xData:ts.xData,seriesData:as.rpsSeriesData,key:"rps-"+ts.chartUpdateKey,chartType:"rps"},null,8,["xData","seriesData"]))]),(0,s.Lk)("div",oe,[((0,s.uX)(),(0,s.Wv)(Ms,{chartTitle:"每秒处理事务数(TPS)",chartWidth:"100%",chartHeight:"350px",chartUnit:"单位(个)",xData:ts.xData,seriesData:as.tpsSeriesData,key:"tps-"+ts.chartUpdateKey,chartType:"tps"},null,8,["xData","seriesData"]))]),(0,s.Lk)("div",ne,[((0,s.uX)(),(0,s.Wv)(Ms,{chartTitle:"并发用户数",chartWidth:"100%",chartHeight:"350px",chartUnit:"单位(个)",xData:ts.xData,seriesData:as.usersSeriesData,key:"users-"+ts.chartUpdateKey,chartType:"users"},null,8,["xData","seriesData"]))]),(0,s.Lk)("div",le,[((0,s.uX)(),(0,s.Wv)(Ms,{chartTitle:"90%响应时间线",chartWidth:"100%",chartHeight:"350px",chartUnit:"单位(毫秒)",xData:ts.xData,seriesData:as.p90SeriesData,key:"p90-"+ts.chartUpdateKey,chartType:"p90"},null,8,["xData","seriesData"]))]),(0,s.Lk)("div",ce,[((0,s.uX)(),(0,s.Wv)(Ms,{chartTitle:"99%响应时间线",chartWidth:"100%",chartHeight:"350px",chartUnit:"单位(毫秒)",xData:ts.xData,seriesData:as.p99SeriesData,key:"p99-"+ts.chartUpdateKey,chartType:"p99"},null,8,["xData","seriesData"]))])]),(0,s.Lk)("div",pe,[(0,s.bF)(Fs,{direction:"vertical",class:"divider"}),t[71]||(t[71]=(0,s.eW)("结果分析 ")),(0,s.bF)(rs,{disabled:!as.isEdit,onClick:as.startEditing,type:"primary",size:"small",style:{"margin-left":"10px"}},{default:(0,s.k6)(()=>t[70]||(t[70]=[(0,s.eW)("编辑")])),_:1,__:[70]},8,["disabled","onClick"])]),ts.inputDlg&&"desc"===ts.editingField?((0,s.uX)(),(0,s.Wv)(ns,{key:0,style:{"font-size":"14px","margin-top":"15px"},rows:8,type:"textarea",modelValue:ts.desc,"onUpdate:modelValue":t[3]||(t[3]=e=>ts.desc=e),onBlur:as.saveAnalysis,ref:"input",onClick:t[4]||(t[4]=(0,i.D$)(()=>{},["stop"])),placeholder:"请输入测试结果分析..."},null,8,["modelValue","onBlur"])):ts.inputDlg&&"desc"===ts.editingField?(0,s.Q3)("",!0):((0,s.uX)(),(0,s.CE)("p",he,(0,r.v_)(ts.desc),1))])):(0,s.Q3)("",!0),(0,s.bo)((0,s.Lk)("div",ue,[(0,s.Lk)("div",me,[(0,s.Lk)("div",de,[(0,s.bF)(Fs,{direction:"vertical",class:"divider"}),t[73]||(t[73]=(0,s.eW)(" GUI监控 ")),(0,s.bF)(rs,{type:as.getTestStatusType(),size:"small",effect:"light"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(as.getTestStatusText()),1)]),_:1},8,["type"])]),"运行中"===as.getTestStatusText()?((0,s.uX)(),(0,s.CE)("div",ge,[(0,s.bF)(rs,{type:"success",onClick:as.refreshGUI,size:"small"},{default:(0,s.k6)(()=>t[74]||(t[74]=[(0,s.eW)(" 刷新GUI ")])),_:1,__:[74]},8,["onClick"])])):(0,s.Q3)("",!0)]),as.isTestRunning?ts.guiUrl?((0,s.uX)(),(0,s.CE)("div",De,[(0,s.Lk)("iframe",{src:ts.guiUrl,onLoad:t[5]||(t[5]=(...e)=>as.handleIframeLoad&&as.handleIframeLoad(...e)),onError:t[6]||(t[6]=(...e)=>as.handleIframeError&&as.handleIframeError(...e))},null,40,_e),ts.iframeError?((0,s.uX)(),(0,s.CE)("div",ve,[(0,s.bF)(Us,{title:"GUI加载失败",description:ts.iframeErrorMessage,type:"error","show-icon":""},{default:(0,s.k6)(()=>t[75]||(t[75]=[(0,s.Lk)("div",{style:{"margin-top":"10px"}},[(0,s.Lk)("p",null,"可能的原因："),(0,s.Lk)("ul",{style:{margin:"5px 0","padding-left":"20px"}},[(0,s.Lk)("li",null,"测试服务未正确启动"),(0,s.Lk)("li",null,"服务器不可访问"),(0,s.Lk)("li",null,"测试已结束，GUI界面已关闭")])],-1)])),_:1},8,["description"]),(0,s.Lk)("div",ke,[(0,s.bF)(rs,{type:"primary",onClick:as.retryLoadGUI},{default:(0,s.k6)(()=>t[76]||(t[76]=[(0,s.eW)(" 重试加载 ")])),_:1,__:[76]},8,["onClick"])])])):(0,s.Q3)("",!0)])):((0,s.uX)(),(0,s.CE)("div",be,[(0,s.bF)(Ws,{description:"无可用的GUI界面"},{default:(0,s.k6)(()=>t[77]||(t[77]=[(0,s.Lk)("p",null,"后端未提供GUI URL，可能该测试没有可视化界面",-1)])),_:1,__:[77]})])):((0,s.uX)(),(0,s.CE)("div",fe,[(0,s.bF)(Us,{title:"测试未运行",description:as.getTestNotRunningMessage(),type:"info","show-icon":"",closable:!1},null,8,["description"])]))],512),[[i.aG,"2"===ts.activeIndex]]),"3"===ts.activeIndex?((0,s.uX)(),(0,s.CE)("div",ye,[(0,s.Lk)("div",Te,[(0,s.bF)(Fs,{direction:"vertical",class:"divider"}),t[79]||(t[79]=(0,s.eW)(" 压力机监控 ")),(0,s.bF)(rs,{type:"primary",size:"small",onClick:as.refreshMonitoringData},{default:(0,s.k6)(()=>t[78]||(t[78]=[(0,s.eW)("刷新数据")])),_:1,__:[78]},8,["onClick"]),(0,s.Lk)("div",Re,[(0,s.bF)($s,{content:as.getWebSocketStatusText(),placement:"top"},{default:(0,s.k6)(()=>[(0,s.bF)(os,{type:as.getWebSocketStatusType(),class:"task-status-tag info",size:"small",effect:"light",onClick:as.handleWebSocketStatusClick,style:{cursor:"pointer"}},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(xs)]),_:1}),(0,s.eW)(" "+(0,r.v_)(as.getWebSocketStatusText()),1)]),_:1},8,["type","onClick"])]),_:1},8,["content"])])]),(0,s.Lk)("div",Ce,[(0,s.Lk)("div",Le,[t[84]||(t[84]=(0,s.Lk)("h4",null,"系统资源",-1)),(0,s.Lk)("div",Fe,[t[80]||(t[80]=(0,s.Lk)("span",null,"CPU使用率:",-1)),(0,s.Lk)("span",{class:(0,r.C4)(as.getCpuLevelClass(ts.monitoringData.cpu_percent))},(0,r.v_)(ts.monitoringData.cpu_percent||0)+"%",3)]),(0,s.Lk)("div",we,[t[81]||(t[81]=(0,s.Lk)("span",null,"内存使用率:",-1)),(0,s.Lk)("span",{class:(0,r.C4)(as.getMemoryLevelClass(ts.monitoringData.memory_percent))},(0,r.v_)(ts.monitoringData.memory_percent||0)+"%",3)]),(0,s.Lk)("div",xe,[t[82]||(t[82]=(0,s.Lk)("span",null,"磁盘使用率:",-1)),(0,s.Lk)("span",null,(0,r.v_)(ts.monitoringData.disk_percent||0)+"%",1)]),(0,s.Lk)("div",Se,[t[83]||(t[83]=(0,s.Lk)("span",null,"网络IO:",-1)),(0,s.Lk)("span",null,"↑"+(0,r.v_)(as.formatBytes(ts.monitoringData.network_sent))+" ↓"+(0,r.v_)(as.formatBytes(ts.monitoringData.network_recv)),1)])]),(0,s.Lk)("div",$e,[t[89]||(t[89]=(0,s.Lk)("h4",null,"压力机状态",-1)),(0,s.Lk)("div",Ie,[t[85]||(t[85]=(0,s.Lk)("span",null,"活跃连接数:",-1)),(0,s.Lk)("span",null,(0,r.v_)(ts.monitoringData.active_connections||0),1)]),(0,s.Lk)("div",Me,[t[86]||(t[86]=(0,s.Lk)("span",null,"当前RPS:",-1)),(0,s.Lk)("span",null,(0,r.v_)(ts.monitoringData.current_rps||0),1)]),(0,s.Lk)("div",Ue,[t[87]||(t[87]=(0,s.Lk)("span",null,"当前用户数:",-1)),(0,s.Lk)("span",null,(0,r.v_)(ts.monitoringData.current_users||0),1)]),(0,s.Lk)("div",We,[t[88]||(t[88]=(0,s.Lk)("span",null,"错误率:",-1)),(0,s.Lk)("span",{class:(0,r.C4)(as.getErrorRateClass(ts.monitoringData.error_rate))},(0,r.v_)(ts.monitoringData.error_rate||0)+"%",3)])]),(0,s.Lk)("div",Pe,[t[94]||(t[94]=(0,s.Lk)("h4",null,"服务器信息",-1)),(0,s.Lk)("div",Ee,[t[90]||(t[90]=(0,s.Lk)("span",null,"服务器类型:",-1)),(0,s.Lk)("span",null,(0,r.v_)(ts.monitoringData.server_type||"single"),1)]),(0,s.Lk)("div",qe,[t[91]||(t[91]=(0,s.Lk)("span",null,"CPU核心数:",-1)),(0,s.Lk)("span",null,(0,r.v_)(ts.monitoringData.cpu_cores||"N/A"),1)]),(0,s.Lk)("div",Ne,[t[92]||(t[92]=(0,s.Lk)("span",null,"总内存:",-1)),(0,s.Lk)("span",null,(0,r.v_)(as.formatBytes(ts.monitoringData.total_memory)),1)]),(0,s.Lk)("div",Ae,[t[93]||(t[93]=(0,s.Lk)("span",null,"运行时长:",-1)),(0,s.Lk)("span",null,(0,r.v_)(as.formatDuration(ts.monitoringData.uptime)),1)])])])])):(0,s.Q3)("",!0),"4"===ts.activeIndex?((0,s.uX)(),(0,s.CE)("div",Ve,[(0,s.Lk)("div",null,[(0,s.bF)(ns,{modelValue:ts.monitorUrl,"onUpdate:modelValue":t[7]||(t[7]=e=>ts.monitorUrl=e),placeholder:"输入被测服务监控页面URL",size:"small",style:{width:"320px","margin-left":"20px"},onKeyup:(0,i.jR)(as.setMonitorIframeUrl,["enter"]),clearable:""},null,8,["modelValue","onKeyup"]),(0,s.bF)(rs,{type:"success",size:"small",onClick:as.setMonitorIframeUrl,style:{"margin-left":"8px"}},{default:(0,s.k6)(()=>t[95]||(t[95]=[(0,s.eW)("确定")])),_:1,__:[95]},8,["onClick"]),(0,s.bF)(rs,{type:"info",size:"small",onClick:as.resetMonitorIframeUrl,style:{"margin-left":"4px"}},{default:(0,s.k6)(()=>t[96]||(t[96]=[(0,s.eW)("重置")])),_:1,__:[96]},8,["onClick"])]),ts.monitorIframeUrl?((0,s.uX)(),(0,s.CE)("div",Oe,[(0,s.Lk)("iframe",{src:ts.monitorIframeUrl,style:{width:"100%","min-height":"600px",border:"1px solid #eee","border-radius":"8px"},frameborder:"0"},null,8,Be)])):(0,s.Q3)("",!0)])):(0,s.Q3)("",!0),"5"===ts.activeIndex?((0,s.uX)(),(0,s.CE)("div",Xe,[(0,s.Lk)("div",Ge,[(0,s.bF)(Fs,{direction:"vertical",class:"divider"}),t[99]||(t[99]=(0,s.eW)(" 实时日志 ")),(0,s.bF)(rs,{type:"primary",size:"small",onClick:as.refreshLogData},{default:(0,s.k6)(()=>t[97]||(t[97]=[(0,s.eW)("刷新日志")])),_:1,__:[97]},8,["onClick"]),(0,s.bF)(rs,{type:"warning",size:"small",onClick:as.clearLogs},{default:(0,s.k6)(()=>t[98]||(t[98]=[(0,s.eW)("清空日志")])),_:1,__:[98]},8,["onClick"]),(0,s.bF)(Ps,{modelValue:ts.autoRefresh,"onUpdate:modelValue":t[8]||(t[8]=e=>ts.autoRefresh=e),onChange:as.toggleAutoRefresh,"active-text":"自动刷新","inactive-text":"手动刷新"},null,8,["modelValue","onChange"])]),(0,s.Lk)("div",ze,[(0,s.bF)(ns,{modelValue:ts.logFilter,"onUpdate:modelValue":t[9]||(t[9]=e=>ts.logFilter=e),placeholder:"过滤日志...",size:"small",style:{width:"200px","margin-right":"10px"},clearable:""},{prefix:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Es)]),_:1})]),_:1},8,["modelValue"]),(0,s.bF)(Ns,{modelValue:ts.logLevel,"onUpdate:modelValue":t[10]||(t[10]=e=>ts.logLevel=e),placeholder:"日志级别",size:"small",style:{width:"120px","margin-right":"10px"}},{default:(0,s.k6)(()=>[(0,s.bF)(qs,{label:"全部",value:"all"}),(0,s.bF)(qs,{label:"debug",value:"debug"}),(0,s.bF)(qs,{label:"info",value:"info"}),(0,s.bF)(qs,{label:"warning",value:"warning"}),(0,s.bF)(qs,{label:"error",value:"error"})]),_:1},8,["modelValue"]),(0,s.bF)(Ns,{modelValue:ts.logCategory,"onUpdate:modelValue":t[11]||(t[11]=e=>ts.logCategory=e),placeholder:"日志类型",size:"small",style:{width:"120px","margin-right":"10px"}},{default:(0,s.k6)(()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ts.logCategories,(e,t)=>((0,s.uX)(),(0,s.Wv)(qs,{key:t,label:e,value:t},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),(0,s.bF)(os,{class:"task-status-tag info",type:"info"},{default:(0,s.k6)(()=>[(0,s.eW)("共 "+(0,r.v_)(as.filteredLogs.length)+" 条日志",1)]),_:1})]),(0,s.Lk)("div",He,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(as.filteredLogs,(e,t)=>((0,s.uX)(),(0,s.CE)("div",{key:t,class:(0,r.C4)([as.getLogClass(e.level),"log-item"])},[(0,s.Lk)("span",Ke,"["+(0,r.v_)(as.formatTime(e.timestamp))+"]",1),(0,s.Lk)("span",je,"["+(0,r.v_)(e.level.toUpperCase())+"]",1),e.category?((0,s.uX)(),(0,s.CE)("span",Qe,[(0,s.bF)(is,{size:14},{default:(0,s.k6)(()=>[((0,s.uX)(),(0,s.Wv)((0,s.$y)(as.getLogCategoryIcon(e.category))))]),_:2},1024)])):(0,s.Q3)("",!0),e.formatted&&e.formatted.isFormatted?((0,s.uX)(),(0,s.CE)(s.FK,{key:1},[(0,s.Lk)("span",{class:(0,r.C4)([as.getMethodClass(e.formatted.method),"log-method"])},(0,r.v_)(e.formatted.method),3),(0,s.Lk)("span",Je,(0,r.v_)(e.formatted.url),1),e.formatted.status?((0,s.uX)(),(0,s.CE)("span",{key:0,class:(0,r.C4)([as.getStatusClass(e.formatted.status),"log-status"])},(0,r.v_)(e.formatted.status),3)):(0,s.Q3)("",!0)],64)):((0,s.uX)(),(0,s.CE)("span",Ye,(0,r.v_)(e.message),1)),e.details?((0,s.uX)(),(0,s.Wv)(rs,{key:3,type:"text",size:"small",onClick:t=>e.showDetails=!e.showDetails,class:"details-toggle"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(e.showDetails?"收起":"详情"),1)]),_:2},1032,["onClick"])):(0,s.Q3)("",!0),e.details&&e.showDetails?((0,s.uX)(),(0,s.CE)("div",Ze,[(0,s.Lk)("pre",null,(0,r.v_)(e.details),1)])):(0,s.Q3)("",!0)],2))),128)),0===as.filteredLogs.length?((0,s.uX)(),(0,s.CE)("div",et,[(0,s.Lk)("div",tt,[(0,s.bF)(is,{class:"empty-log-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(gs)]),_:1}),t[100]||(t[100]=(0,s.Lk)("p",{class:"empty-log-text"},"暂无日志数据",-1)),t[101]||(t[101]=(0,s.Lk)("p",{class:"empty-log-hint"},"日志将在测试运行时实时显示",-1))])])):(0,s.Q3)("",!0)])])):(0,s.Q3)("",!0)])])]),_:1}),(0,s.bF)(Os,{modelValue:ts.analysisDialogData.visible,"onUpdate:modelValue":t[12]||(t[12]=e=>ts.analysisDialogData.visible=e),title:"性能分析报告",width:"60%","destroy-on-close":!0},{default:(0,s.k6)(()=>[(0,s.Lk)("div",at,[(0,s.Lk)("div",st,[t[102]||(t[102]=(0,s.Lk)("h4",null,"性能得分",-1)),(0,s.Lk)("div",it,[(0,s.bF)(Vs,{type:"circle",percentage:ts.analysisDialogData.performance_score,color:as.getScoreColor(ts.analysisDialogData.performance_score),width:120},{default:(0,s.k6)(({percentage:e})=>[(0,s.Lk)("span",rt,(0,r.v_)(e)+"/100",1)]),_:1},8,["percentage","color"])])]),(0,s.Lk)("div",ot,[t[103]||(t[103]=(0,s.Lk)("h4",null,"发现的瓶颈",-1)),ts.analysisDialogData.bottlenecks.length>0?((0,s.uX)(),(0,s.CE)("div",nt,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ts.analysisDialogData.bottlenecks,(e,t)=>((0,s.uX)(),(0,s.Wv)(Us,{key:t,title:e.type,description:e.description,type:as.getSeverityType(e.severity),"show-icon":"",style:{"margin-bottom":"10px"}},null,8,["title","description","type"]))),128))])):((0,s.uX)(),(0,s.Wv)(Ws,{key:1,description:"未发现明显性能瓶颈"}))]),(0,s.Lk)("div",lt,[t[104]||(t[104]=(0,s.Lk)("h4",null,"优化建议",-1)),ts.analysisDialogData.recommendations.length>0?((0,s.uX)(),(0,s.CE)("div",ct,[(0,s.Lk)("ul",pt,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ts.analysisDialogData.recommendations,(e,t)=>((0,s.uX)(),(0,s.CE)("li",{key:t,class:"recommendation-item"},[(0,s.bF)(is,{class:"recommendation-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(ms)]),_:1}),(0,s.eW)(" "+(0,r.v_)(e),1)]))),128))])])):((0,s.uX)(),(0,s.Wv)(Ws,{key:1,description:"暂无优化建议"}))])])]),_:1},8,["modelValue"]),(0,s.bF)(Os,{modelValue:ts.configDialogData.visible,"onUpdate:modelValue":t[13]||(t[13]=e=>ts.configDialogData.visible=e),title:"压测配置详情",width:"80%","destroy-on-close":!0},{default:(0,s.k6)(()=>[ts.configDialogData.taskConfig?((0,s.uX)(),(0,s.CE)("div",ht,[(0,s.bF)(cs,{class:"config-section",shadow:"never"},{header:(0,s.k6)(()=>[(0,s.Lk)("div",ut,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ls)]),_:1}),t[105]||(t[105]=(0,s.eW)(" 基础配置 "))])]),default:(0,s.k6)(()=>[(0,s.bF)(Xs,{gutter:20},{default:(0,s.k6)(()=>[(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",mt,[t[106]||(t[106]=(0,s.Lk)("span",{class:"config-label"},"任务名称：",-1)),(0,s.Lk)("span",dt,(0,r.v_)(ts.configDialogData.taskConfig.taskName||"未设置"),1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",gt,[t[107]||(t[107]=(0,s.Lk)("span",{class:"config-label"},"任务类型：",-1)),(0,s.Lk)("span",ft,(0,r.v_)(as.getTaskTypeText(ts.configDialogData.taskConfig.taskType)),1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Dt,[t[108]||(t[108]=(0,s.Lk)("span",{class:"config-label"},"压测模式：",-1)),(0,s.Lk)("span",_t,(0,r.v_)(as.getRunPatternText(ts.configDialogData.taskConfig.runPattern)),1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",vt,[t[109]||(t[109]=(0,s.Lk)("span",{class:"config-label"},"控制模式：",-1)),(0,s.Lk)("span",kt,(0,r.v_)(as.getDistributedModeText(ts.configDialogData.taskConfig.distributed_mode)),1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",bt,[t[110]||(t[110]=(0,s.Lk)("span",{class:"config-label"},"创建人：",-1)),(0,s.Lk)("span",yt,(0,r.v_)(ts.configDialogData.taskConfig.creator||"未知"),1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Tt,[t[111]||(t[111]=(0,s.Lk)("span",{class:"config-label"},"创建时间：",-1)),(0,s.Lk)("span",Rt,(0,r.v_)(as.formatTime(ts.configDialogData.taskConfig.create_time)),1)])]),_:1})]),_:1})]),_:1}),(0,s.bF)(cs,{class:"config-section",shadow:"never"},{header:(0,s.k6)(()=>[(0,s.Lk)("div",Ct,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Rs)]),_:1}),t[112]||(t[112]=(0,s.eW)(" 性能参数 "))])]),default:(0,s.k6)(()=>[(0,s.bF)(Xs,{gutter:20},{default:(0,s.k6)(()=>[(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Lt,[t[113]||(t[113]=(0,s.Lk)("span",{class:"config-label"},"并发用户数：",-1)),(0,s.Lk)("span",Ft,(0,r.v_)(ts.configDialogData.taskConfig.concurrent_users||ts.configDialogData.taskConfig.users||"未设置"),1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",wt,[t[114]||(t[114]=(0,s.Lk)("span",{class:"config-label"},"持续时间：",-1)),(0,s.Lk)("span",xt,(0,r.v_)(ts.configDialogData.taskConfig.duration||"未设置")+"秒",1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",St,[t[115]||(t[115]=(0,s.Lk)("span",{class:"config-label"},"爬坡时间：",-1)),(0,s.Lk)("span",$t,(0,r.v_)(ts.configDialogData.taskConfig.spawn_rate||ts.configDialogData.taskConfig.ramp_up||"未设置")+"秒",1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",It,[t[116]||(t[116]=(0,s.Lk)("span",{class:"config-label"},"QPS限制：",-1)),(0,s.Lk)("span",Mt,(0,r.v_)(ts.configDialogData.taskConfig.qps_limit||"无限制"),1)])]),_:1})]),_:1})]),_:1}),ts.configDialogData.taskConfig.environment?((0,s.uX)(),(0,s.Wv)(cs,{key:0,class:"config-section",shadow:"never"},{header:(0,s.k6)(()=>[(0,s.Lk)("div",Ut,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ds)]),_:1}),t[117]||(t[117]=(0,s.eW)(" 环境配置 "))])]),default:(0,s.k6)(()=>[(0,s.bF)(Xs,{gutter:20},{default:(0,s.k6)(()=>[(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Wt,[t[118]||(t[118]=(0,s.Lk)("span",{class:"config-label"},"环境名称：",-1)),(0,s.Lk)("span",Pt,(0,r.v_)(ts.configDialogData.taskConfig.environment.name||"默认环境"),1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Et,[t[119]||(t[119]=(0,s.Lk)("span",{class:"config-label"},"基础URL：",-1)),(0,s.Lk)("span",qt,(0,r.v_)(ts.configDialogData.taskConfig.environment.base_url||ts.configDialogData.taskConfig.environment.host||"未设置"),1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Nt,[t[120]||(t[120]=(0,s.Lk)("span",{class:"config-label"},"协议：",-1)),(0,s.Lk)("span",At,(0,r.v_)(ts.configDialogData.taskConfig.environment.protocol||"HTTP"),1)])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Vt,[t[121]||(t[121]=(0,s.Lk)("span",{class:"config-label"},"超时设置：",-1)),(0,s.Lk)("span",Ot,(0,r.v_)(ts.configDialogData.taskConfig.environment.timeout||ts.configDialogData.taskConfig.timeout||"30")+"秒",1)])]),_:1})]),_:1})]),_:1})):(0,s.Q3)("",!0)])):(0,s.Q3)("",!0)]),_:1},8,["modelValue"]),(0,s.bF)(Os,{modelValue:ts.notificationDialogData.visible,"onUpdate:modelValue":t[21]||(t[21]=e=>ts.notificationDialogData.visible=e),title:"测试报告推送",width:"680px","custom-class":"modern-notification-dialog",required:!0,"before-close":as.cancelNotification,top:"5vh",center:"","append-to-body":"","destroy-on-close":""},{footer:(0,s.k6)(()=>[(0,s.Lk)("div",ga,[(0,s.bF)(rs,{onClick:as.cancelNotification,plain:""},{default:(0,s.k6)(()=>t[136]||(t[136]=[(0,s.eW)("取消")])),_:1,__:[136]},8,["onClick"]),(0,s.bF)(rs,{type:"primary",onClick:as.confirmSendNotification},{default:(0,s.k6)(()=>[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Js)]),_:1}),t[137]||(t[137]=(0,s.eW)(" 发送通知 "))]),_:1,__:[137]},8,["onClick"])])]),default:(0,s.k6)(()=>[(0,s.Lk)("div",Bt,[(0,s.Lk)("div",Xt,[(0,s.Lk)("div",Gt,[(0,s.bF)(is,{class:"notification-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(ys)]),_:1}),t[122]||(t[122]=(0,s.Lk)("span",null,"分享测试报告结果",-1))]),t[123]||(t[123]=(0,s.Lk)("div",{class:"notification-subtitle"},"选择通知方式，将测试结果分享给团队成员",-1))]),(0,s.bF)(Qs,{model:ts.notificationDialogData,rules:ts.notificationRules,ref:"notificationFormRef","label-position":"top",class:"notification-form"},{default:(0,s.k6)(()=>[(0,s.bF)(Gs,{prop:"name",label:"通知标题"},{default:(0,s.k6)(()=>[(0,s.bF)(ns,{modelValue:ts.notificationDialogData.name,"onUpdate:modelValue":t[14]||(t[14]=e=>ts.notificationDialogData.name=e),maxlength:"50",minlength:"1",placeholder:"例如：性能测试报告 - 系统优化后结果",class:"notification-input"},null,8,["modelValue"])]),_:1}),(0,s.bF)(Gs,{prop:"pushType",label:"选择通知方式"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",zt,[(0,s.Lk)("div",{class:(0,r.C4)(["notification-type-option",{selected:"email"===ts.notificationDialogData.pushType}]),onClick:t[15]||(t[15]=e=>ts.notificationDialogData.pushType="email")},[(0,s.Lk)("div",Ht,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(zs)]),_:1})]),t[124]||(t[124]=(0,s.Lk)("span",null,"邮件",-1))],2),(0,s.Lk)("div",{class:(0,r.C4)(["notification-type-option",{selected:"webhook"===ts.notificationDialogData.pushType}]),onClick:t[16]||(t[16]=e=>ts.notificationDialogData.pushType="webhook")},[(0,s.Lk)("div",Kt,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Hs)]),_:1})]),t[125]||(t[125]=(0,s.Lk)("span",null,"Webhook",-1))],2),(0,s.Lk)("div",{class:(0,r.C4)(["notification-type-option",{selected:"dingtalk"===ts.notificationDialogData.pushType}]),onClick:t[17]||(t[17]=e=>ts.notificationDialogData.pushType="dingtalk")},[(0,s.Lk)("div",jt,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ks)]),_:1})]),t[126]||(t[126]=(0,s.Lk)("span",null,"钉钉",-1))],2),(0,s.Lk)("div",{class:(0,r.C4)(["notification-type-option",{selected:"wechat"===ts.notificationDialogData.pushType}]),onClick:t[18]||(t[18]=e=>ts.notificationDialogData.pushType="wechat")},[(0,s.Lk)("div",Qt,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(js)]),_:1})]),t[127]||(t[127]=(0,s.Lk)("span",null,"微信",-1))],2)])]),_:1}),ts.notificationDialogData.pushType&&"email"!==ts.notificationDialogData.pushType?((0,s.uX)(),(0,s.Wv)(Gs,{key:0,prop:"webhook",label:"推送地址"},{default:(0,s.k6)(()=>[(0,s.bF)(ns,{modelValue:ts.notificationDialogData.webhook,"onUpdate:modelValue":t[19]||(t[19]=e=>ts.notificationDialogData.webhook=e),minlength:"3",placeholder:"请输入webhook地址",class:"notification-input"},{prefix:(0,s.k6)(()=>["webhook"===ts.notificationDialogData.pushType?((0,s.uX)(),(0,s.Wv)(is,{key:0},{default:(0,s.k6)(()=>[(0,s.bF)(Hs)]),_:1})):(0,s.Q3)("",!0),"dingtalk"===ts.notificationDialogData.pushType?((0,s.uX)(),(0,s.Wv)(is,{key:1},{default:(0,s.k6)(()=>[(0,s.bF)(Ks)]),_:1})):(0,s.Q3)("",!0),"wechat"===ts.notificationDialogData.pushType?((0,s.uX)(),(0,s.Wv)(is,{key:2},{default:(0,s.k6)(()=>[(0,s.bF)(js)]),_:1})):(0,s.Q3)("",!0)]),_:1},8,["modelValue"])]),_:1})):(0,s.Q3)("",!0),"email"===ts.notificationDialogData.pushType?((0,s.uX)(),(0,s.Wv)(Gs,{key:1,prop:"recipients",label:"接收人"},{default:(0,s.k6)(()=>[(0,s.bF)(Ns,{multiple:"",modelValue:ts.notificationDialogData.recipients,"onUpdate:modelValue":t[20]||(t[20]=e=>ts.notificationDialogData.recipients=e),placeholder:"请选择接收人",style:{width:"100%"},"collapse-tags":"","collapse-tags-tooltip":"",class:"notification-select"},{default:(0,s.k6)(()=>[((0,s.uX)(),(0,s.Wv)(qs,{label:"@all",value:"@all",key:"@all",disabled:ts.notificationDialogData.recipients.length>0&&!ts.notificationDialogData.recipients.includes("@all")},null,8,["disabled"])),(0,s.bF)(qs,{label:"<EMAIL>",value:"<EMAIL>",disabled:ts.notificationDialogData.recipients.includes("@all")},null,8,["disabled"])]),_:1},8,["modelValue"]),t[128]||(t[128]=(0,s.Lk)("div",{class:"notification-tip"},"选择 @all 将通知所有人，与其他接收人互斥",-1))]),_:1,__:[128]})):(0,s.Q3)("",!0),(0,s.Lk)("div",Jt,[(0,s.Lk)("div",Yt,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(gs)]),_:1}),t[129]||(t[129]=(0,s.Lk)("span",null,"报告摘要",-1))]),(0,s.Lk)("div",Zt,[(0,s.Lk)("div",ea,[t[130]||(t[130]=(0,s.Lk)("span",{class:"item-label"},"报告名称:",-1)),(0,s.Lk)("span",ta,(0,r.v_)(ts.reportData.reportName),1)]),(0,s.Lk)("div",aa,[t[131]||(t[131]=(0,s.Lk)("span",{class:"item-label"},"测试状态:",-1)),(0,s.Lk)("span",sa,[(0,s.bF)(rs,{type:"0"===ts.reportData.reportStatus?"success":"info",size:"small"},{default:(0,s.k6)(()=>[(0,s.eW)((0,r.v_)(as.getStatusText(ts.reportData.reportStatus)),1)]),_:1},8,["type"])])]),(0,s.Lk)("div",ia,[(0,s.Lk)("div",ra,[(0,s.Lk)("div",oa,[t[132]||(t[132]=(0,s.Lk)("div",{class:"metric-label"},"响应时间",-1)),(0,s.Lk)("div",na,(0,r.v_)(as.formatResponseTime(ts.reportData.avgResponseTime)),1)]),(0,s.Lk)("div",la,[t[133]||(t[133]=(0,s.Lk)("div",{class:"metric-label"},"成功率",-1)),(0,s.Lk)("div",ca,(0,r.v_)(as.getSuccessRate())+"%",1)])]),(0,s.Lk)("div",pa,[(0,s.Lk)("div",ha,[t[134]||(t[134]=(0,s.Lk)("div",{class:"metric-label"},"平均TPS",-1)),(0,s.Lk)("div",ua,(0,r.v_)((ts.reportData.avgTps||0).toFixed(2)),1)]),(0,s.Lk)("div",ma,[t[135]||(t[135]=(0,s.Lk)("div",{class:"metric-label"},"并发用户",-1)),(0,s.Lk)("div",da,(0,r.v_)(ts.reportData.maxUsers||ts.reportData.avgUsers||"未设置"),1)])])])])])]),_:1},8,["model","rules"])])]),_:1},8,["modelValue","before-close"]),(0,s.bF)(Os,{modelValue:ts.baselineDialogData.visible,"onUpdate:modelValue":t[31]||(t[31]=e=>ts.baselineDialogData.visible=e),title:"创建性能基准线",width:"600px","destroy-on-close":""},{footer:(0,s.k6)(()=>[(0,s.Lk)("span",Da,[(0,s.bF)(rs,{onClick:t[30]||(t[30]=e=>ts.baselineDialogData.visible=!1)},{default:(0,s.k6)(()=>t[140]||(t[140]=[(0,s.eW)("取消")])),_:1,__:[140]}),(0,s.bF)(rs,{type:"primary",onClick:as.submitBaselineForm,loading:ts.baselineDialogData.loading},{default:(0,s.k6)(()=>t[141]||(t[141]=[(0,s.eW)("创建")])),_:1,__:[141]},8,["onClick","loading"])])]),default:(0,s.k6)(()=>[(0,s.bF)(Qs,{model:ts.baselineDialogData.form,rules:ts.baselineFormRules,ref:"baselineFormRef","label-width":"120px"},{default:(0,s.k6)(()=>[(0,s.bF)(Gs,{label:"基准线名称",prop:"name"},{default:(0,s.k6)(()=>[(0,s.bF)(ns,{modelValue:ts.baselineDialogData.form.name,"onUpdate:modelValue":t[22]||(t[22]=e=>ts.baselineDialogData.form.name=e),placeholder:"请输入基准线名称"},null,8,["modelValue"])]),_:1}),(0,s.bF)(Gs,{label:"描述",prop:"description"},{default:(0,s.k6)(()=>[(0,s.bF)(ns,{modelValue:ts.baselineDialogData.form.description,"onUpdate:modelValue":t[23]||(t[23]=e=>ts.baselineDialogData.form.description=e),type:"textarea",rows:3,placeholder:"请输入基准线描述"},null,8,["modelValue"])]),_:1}),(0,s.bF)(Fs,{"content-position":"center"},{default:(0,s.k6)(()=>t[138]||(t[138]=[(0,s.eW)("性能指标")])),_:1,__:[138]}),(0,s.bF)(Xs,{gutter:20},{default:(0,s.k6)(()=>[(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.bF)(Gs,{label:"响应时间(ms)",prop:"avg_response_time"},{default:(0,s.k6)(()=>[(0,s.bF)(Ys,{modelValue:ts.baselineDialogData.form.avg_response_time,"onUpdate:modelValue":t[24]||(t[24]=e=>ts.baselineDialogData.form.avg_response_time=e),precision:2,min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.bF)(Gs,{label:"TPS",prop:"avg_tps"},{default:(0,s.k6)(()=>[(0,s.bF)(Ys,{modelValue:ts.baselineDialogData.form.avg_tps,"onUpdate:modelValue":t[25]||(t[25]=e=>ts.baselineDialogData.form.avg_tps=e),precision:2,min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),(0,s.bF)(Xs,{gutter:20},{default:(0,s.k6)(()=>[(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.bF)(Gs,{label:"成功率(%)",prop:"success_rate"},{default:(0,s.k6)(()=>[(0,s.bF)(Ys,{modelValue:ts.baselineDialogData.form.success_rate,"onUpdate:modelValue":t[26]||(t[26]=e=>ts.baselineDialogData.form.success_rate=e),precision:2,min:0,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.bF)(Gs,{label:"CPU使用率(%)",prop:"avg_cpu"},{default:(0,s.k6)(()=>[(0,s.bF)(Ys,{modelValue:ts.baselineDialogData.form.avg_cpu,"onUpdate:modelValue":t[27]||(t[27]=e=>ts.baselineDialogData.form.avg_cpu=e),precision:2,min:0,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),(0,s.bF)(Xs,{gutter:20},{default:(0,s.k6)(()=>[(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.bF)(Gs,{label:"内存使用率(%)",prop:"avg_memory"},{default:(0,s.k6)(()=>[(0,s.bF)(Ys,{modelValue:ts.baselineDialogData.form.avg_memory,"onUpdate:modelValue":t[28]||(t[28]=e=>ts.baselineDialogData.form.avg_memory=e),precision:2,min:0,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.bF)(Gs,{label:"是否激活"},{default:(0,s.k6)(()=>[(0,s.bF)(Ps,{modelValue:ts.baselineDialogData.form.is_active,"onUpdate:modelValue":t[29]||(t[29]=e=>ts.baselineDialogData.form.is_active=e)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),(0,s.Lk)("div",fa,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Zs)]),_:1}),t[139]||(t[139]=(0,s.Lk)("span",null,"这些性能指标将作为未来性能测试的参考基准",-1))])]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),(0,s.bF)(Os,{modelValue:ts.baselineCompareDialogData.visible,"onUpdate:modelValue":t[34]||(t[34]=e=>ts.baselineCompareDialogData.visible=e),title:"选择要对比的基准线",width:"500px","destroy-on-close":""},{footer:(0,s.k6)(()=>[(0,s.Lk)("span",ka,[(0,s.bF)(rs,{onClick:t[33]||(t[33]=e=>ts.baselineCompareDialogData.visible=!1)},{default:(0,s.k6)(()=>t[142]||(t[142]=[(0,s.eW)("取消")])),_:1,__:[142]}),(0,s.bF)(rs,{type:"primary",onClick:as.submitCompareBaseline,loading:ts.baselineCompareDialogData.loading},{default:(0,s.k6)(()=>t[143]||(t[143]=[(0,s.eW)("开始对比")])),_:1,__:[143]},8,["onClick","loading"])])]),default:(0,s.k6)(()=>[(0,s.bF)(Qs,{"label-width":"100px"},{default:(0,s.k6)(()=>[(0,s.bF)(Gs,{label:"基准线"},{default:(0,s.k6)(()=>[(0,s.bF)(Ns,{modelValue:ts.baselineCompareDialogData.selectedBaselineId,"onUpdate:modelValue":t[32]||(t[32]=e=>ts.baselineCompareDialogData.selectedBaselineId=e),style:{width:"100%"}},{default:(0,s.k6)(()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(ts.baselineCompareDialogData.baselineList,e=>((0,s.uX)(),(0,s.Wv)(qs,{key:e.id,label:e.name,value:e.id},{default:(0,s.k6)(()=>[(0,s.Lk)("div",_a,[(0,s.Lk)("span",null,(0,r.v_)(e.name),1),(0,s.Lk)("span",va,(0,r.v_)(as.formatTime(e.create_time)),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),(0,s.bF)(Os,{modelValue:ts.baselineCompareResultData.visible,"onUpdate:modelValue":t[36]||(t[36]=e=>ts.baselineCompareResultData.visible=e),title:"性能基准线对比结果",width:"800px","destroy-on-close":""},{footer:(0,s.k6)(()=>[(0,s.Lk)("span",Za,[(0,s.bF)(rs,{onClick:t[35]||(t[35]=e=>ts.baselineCompareResultData.visible=!1)},{default:(0,s.k6)(()=>t[157]||(t[157]=[(0,s.eW)("关闭")])),_:1,__:[157]}),(0,s.bF)(rs,{type:"primary",onClick:as.exportComparisonResult},{default:(0,s.k6)(()=>t[158]||(t[158]=[(0,s.eW)("导出对比报告")])),_:1,__:[158]},8,["onClick"])])]),default:(0,s.k6)(()=>[(0,s.Lk)("div",ba,[(0,s.bF)(Xs,{gutter:20},{default:(0,s.k6)(()=>[(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",ya,[(0,s.Lk)("div",Ta,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Ts)]),_:1}),t[144]||(t[144]=(0,s.Lk)("span",null,"基准线",-1))]),(0,s.Lk)("div",Ra,[(0,s.Lk)("div",Ca,[t[145]||(t[145]=(0,s.Lk)("span",{class:"metric-label"},"响应时间:",-1)),(0,s.Lk)("span",La,(0,r.v_)(ts.baselineCompareResultData.baseline_metrics.avg_response_time.toFixed(2))+"ms",1)]),(0,s.Lk)("div",Fa,[t[146]||(t[146]=(0,s.Lk)("span",{class:"metric-label"},"TPS:",-1)),(0,s.Lk)("span",wa,(0,r.v_)(ts.baselineCompareResultData.baseline_metrics.avg_tps.toFixed(2)),1)]),(0,s.Lk)("div",xa,[t[147]||(t[147]=(0,s.Lk)("span",{class:"metric-label"},"成功率:",-1)),(0,s.Lk)("span",Sa,(0,r.v_)(ts.baselineCompareResultData.baseline_metrics.success_rate.toFixed(2))+"%",1)]),(0,s.Lk)("div",$a,[t[148]||(t[148]=(0,s.Lk)("span",{class:"metric-label"},"CPU使用率:",-1)),(0,s.Lk)("span",Ia,(0,r.v_)(ts.baselineCompareResultData.baseline_metrics.avg_cpu?.toFixed(2)||"0.00")+"%",1)]),(0,s.Lk)("div",Ma,[t[149]||(t[149]=(0,s.Lk)("span",{class:"metric-label"},"内存使用率:",-1)),(0,s.Lk)("span",Ua,(0,r.v_)(ts.baselineCompareResultData.baseline_metrics.avg_memory?.toFixed(2)||"0.00")+"%",1)])])])]),_:1}),(0,s.bF)(Bs,{span:12},{default:(0,s.k6)(()=>[(0,s.Lk)("div",Wa,[(0,s.Lk)("div",Pa,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(gs)]),_:1}),t[150]||(t[150]=(0,s.Lk)("span",null,"当前报告",-1))]),(0,s.Lk)("div",Ea,[(0,s.Lk)("div",qa,[t[151]||(t[151]=(0,s.Lk)("span",{class:"metric-label"},"响应时间:",-1)),(0,s.Lk)("span",Na,(0,r.v_)(ts.baselineCompareResultData.current_metrics.avg_response_time.toFixed(2))+"ms",1),(0,s.Lk)("span",{class:(0,r.C4)(as.getComparisonClass(ts.baselineCompareResultData.current_metrics.avg_response_time,ts.baselineCompareResultData.baseline_metrics.avg_response_time,"response_time"))},(0,r.v_)(as.getComparisonText(ts.baselineCompareResultData.current_metrics.avg_response_time,ts.baselineCompareResultData.baseline_metrics.avg_response_time,"response_time")),3)]),(0,s.Lk)("div",Aa,[t[152]||(t[152]=(0,s.Lk)("span",{class:"metric-label"},"TPS:",-1)),(0,s.Lk)("span",Va,(0,r.v_)(ts.baselineCompareResultData.current_metrics.avg_tps.toFixed(2)),1),(0,s.Lk)("span",{class:(0,r.C4)(as.getComparisonClass(ts.baselineCompareResultData.current_metrics.avg_tps,ts.baselineCompareResultData.baseline_metrics.avg_tps,"tps"))},(0,r.v_)(as.getComparisonText(ts.baselineCompareResultData.current_metrics.avg_tps,ts.baselineCompareResultData.baseline_metrics.avg_tps,"tps")),3)]),(0,s.Lk)("div",Oa,[t[153]||(t[153]=(0,s.Lk)("span",{class:"metric-label"},"成功率:",-1)),(0,s.Lk)("span",Ba,(0,r.v_)(ts.baselineCompareResultData.current_metrics.success_rate.toFixed(2))+"%",1),(0,s.Lk)("span",{class:(0,r.C4)(as.getComparisonClass(ts.baselineCompareResultData.current_metrics.success_rate,ts.baselineCompareResultData.baseline_metrics.success_rate,"success_rate"))},(0,r.v_)(as.getComparisonText(ts.baselineCompareResultData.current_metrics.success_rate,ts.baselineCompareResultData.baseline_metrics.success_rate,"success_rate")),3)]),(0,s.Lk)("div",Xa,[t[154]||(t[154]=(0,s.Lk)("span",{class:"metric-label"},"CPU使用率:",-1)),(0,s.Lk)("span",Ga,(0,r.v_)(ts.baselineCompareResultData.current_metrics.avg_cpu?.toFixed(2)||"0.00")+"%",1),(0,s.Lk)("span",{class:(0,r.C4)(as.getComparisonClass(ts.baselineCompareResultData.current_metrics.avg_cpu||0,ts.baselineCompareResultData.baseline_metrics.avg_cpu||0,"cpu"))},(0,r.v_)(as.getComparisonText(ts.baselineCompareResultData.current_metrics.avg_cpu||0,ts.baselineCompareResultData.baseline_metrics.avg_cpu||0,"cpu")),3)]),(0,s.Lk)("div",za,[t[155]||(t[155]=(0,s.Lk)("span",{class:"metric-label"},"内存使用率:",-1)),(0,s.Lk)("span",Ha,(0,r.v_)(ts.baselineCompareResultData.current_metrics.avg_memory?.toFixed(2)||"0.00")+"%",1),(0,s.Lk)("span",{class:(0,r.C4)(as.getComparisonClass(ts.baselineCompareResultData.current_metrics.avg_memory||0,ts.baselineCompareResultData.baseline_metrics.avg_memory||0,"memory"))},(0,r.v_)(as.getComparisonText(ts.baselineCompareResultData.current_metrics.avg_memory||0,ts.baselineCompareResultData.baseline_metrics.avg_memory||0,"memory")),3)])])])]),_:1})]),_:1}),(0,s.Lk)("div",Ka,[(0,s.Lk)("div",ja,[(0,s.bF)(is,null,{default:(0,s.k6)(()=>[(0,s.bF)(Zs)]),_:1}),t[156]||(t[156]=(0,s.Lk)("span",null,"对比结论",-1))]),(0,s.Lk)("div",Qa,[ts.baselineCompareResultData.conclusion?((0,s.uX)(),(0,s.CE)("p",Ja,(0,r.v_)(ts.baselineCompareResultData.conclusion),1)):((0,s.uX)(),(0,s.CE)("p",Ya,(0,r.v_)(as.generateComparisonConclusion()),1))])])])]),_:1},8,["modelValue"])],64)}a(44114),a(18111),a(22489),a(20116),a(7588),a(61701),a(14603),a(47566),a(98721);var ts=a(82484);const as={class:"chart-container"},ss={class:"chart-header"},is={class:"chart-title"},rs={class:"option-label"};function os(e,t,a,i,o,n){const l=(0,s.g2)("el-option"),c=(0,s.g2)("el-select");return(0,s.uX)(),(0,s.CE)("div",as,[(0,s.Lk)("div",ss,[(0,s.Lk)("h3",is,(0,r.v_)(a.chartTitle),1),(0,s.bF)(c,{modelValue:o.selectedSeries,"onUpdate:modelValue":t[0]||(t[0]=e=>o.selectedSeries=e),onChange:n.updateLegend,class:"series-selector",size:"small"},{default:(0,s.k6)(()=>[(0,s.bF)(l,{value:"All"},{default:(0,s.k6)(()=>t[1]||(t[1]=[(0,s.Lk)("span",{class:"option-label"},"全部接口",-1)])),_:1,__:[1]}),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(a.seriesData,(e,t)=>((0,s.uX)(),(0,s.Wv)(l,{key:t,value:e.name},{default:(0,s.k6)(()=>[(0,s.Lk)("span",rs,(0,r.v_)(e.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","onChange"])]),(0,s.Lk)("div",{ref:"chart",style:(0,r.Tr)({width:a.chartWidth,height:a.chartHeight})},null,4)])}var ns=a(91006),ls={name:"ResponseTimeChart",props:{chartTitle:{type:String,default:"平均响应时间"},chartUnit:{type:String,default:"单位(毫秒)"},chartWidth:{type:String,default:"400px"},chartHeight:{type:String,default:"300px"},xData:{type:Array,required:!0},seriesData:{type:Array,required:!0},chartType:{type:String,default:"responseTime"}},data(){return{selectedSeries:"All"}},mounted(){this.$nextTick(()=>{this.initChart()})},watch:{xData:{handler(){this.$nextTick(()=>{this.updateChart()})},deep:!0},seriesData:{handler(){this.$nextTick(()=>{this.updateChart()})},deep:!0}},methods:{initChart(){if(!this.$refs.chart)return;const e=ns.Ts(this.$refs.chart);this.chartInstance=e,window.addEventListener("resize",()=>{e.resize()}),this.updateChart()},updateLegend(){this.updateChart()},updateChart(){if(!this.chartInstance)return;const e=this.chartInstance;if(!this.xData||!this.seriesData||0===this.seriesData.length)return void console.log("图表数据为空，跳过更新");const t=this.getChartStyleConfig(),a=this.seriesData.map((e,a)=>({name:e.name,type:"line",data:"All"===this.selectedSeries||this.selectedSeries===e.name?e.values:[],showSymbol:t.showSymbol,symbolSize:t.symbolSize,smooth:t.smooth,lineStyle:{width:t.lineWidth,type:this.getLineType(a,t),shadowColor:"rgba(0, 0, 0, 0.3)",shadowBlur:t.shadowBlur,shadowOffsetY:1,cap:"round"},itemStyle:{color:this.getSeriesColor(a,t),borderWidth:2,borderColor:this.getSeriesColor(a,t),shadowColor:"rgba(0, 0, 0, 0.3)",shadowBlur:2},areaStyle:this.getAreaStyle(a,t),emphasis:{focus:"series",lineStyle:{width:t.lineWidth+1,shadowBlur:t.shadowBlur+2},itemStyle:{borderWidth:3,shadowBlur:4}}})),s={color:t.colors,tooltip:{trigger:"axis",formatter:e=>{let t=`<div style="font-weight: bold; margin-bottom: 5px;">${e[0].axisValue}</div>`;return e.forEach(e=>{void 0!==e.data&&(t+=`\n                  <div style="display: flex; align-items: center; margin: 3px 0;">\n                    <span style="display: inline-block; width: 10px; height: 10px; \n                      background-color: ${e.color}; border-radius: 50%; margin-right: 5px;">\n                    </span>\n                    <span>${e.seriesName}: ${e.data} ${this.chartUnit}</span>\n                  </div>`)}),t},backgroundColor:"rgba(255, 255, 255, 0.95)",borderColor:"#ddd",borderWidth:1,textStyle:{color:"#333",fontSize:12},extraCssText:"box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px;"},grid:{left:"3%",right:"4%",bottom:"10%",top:"5%",containLabel:!0},xAxis:{type:"category",data:this.xData,axisLabel:{rotate:45,color:"#666",fontSize:11,interval:"auto"},axisLine:{lineStyle:{color:"#e8e8e8",width:1}},axisTick:{show:!1}},yAxis:{name:this.chartUnit,nameLocation:"middle",nameGap:40,nameTextStyle:{color:"#666",fontSize:12},type:"value",axisLabel:{color:"#666",fontSize:11,formatter:e=>e>=1e3?(e/1e3).toFixed(1)+"k":e},axisLine:{show:!1},splitLine:{lineStyle:{color:"#f0f0f0",type:"dashed"}}},series:a,legend:{show:"All"===this.selectedSeries&&a.length>1,bottom:0,data:a.map(e=>e.name),textStyle:{fontSize:11,color:"#666"},icon:t.legendIcon,itemWidth:16,itemHeight:10,itemGap:10},animation:!0,animationDuration:500,animationEasing:"cubicOut"};e.setOption(s,!0)},getChartStyleConfig(){const e={responseTime:{colors:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc","#ff9f7f"],smooth:!0,lineWidth:3,showSymbol:!1,symbolSize:6,shadowBlur:5,useGradient:!0,legendIcon:"roundRect",defaultLineType:"solid",areaOpacity:.15},rps:{colors:["#5b8ff9","#61ddaa","#f6bd16","#7262fd","#78d3f8","#9661bc","#f6903d","#008685","#f08bb4","#65b581"],smooth:!0,lineWidth:3,showSymbol:!1,symbolSize:6,shadowBlur:4,useGradient:!0,legendIcon:"circle",defaultLineType:"solid",areaOpacity:.2},tps:{colors:["#ff6b3b","#626c91","#a0a7e6","#c4ebad","#96dee8","#ff9d6c","#bad6ff","#bbe2bb","#ffd0a9","#d7baf9"],smooth:!0,lineWidth:3,showSymbol:!1,symbolSize:6,shadowBlur:3,useGradient:!0,legendIcon:"pin",defaultLineType:"solid",areaOpacity:.15},users:{colors:["#1890ff","#2fc25b","#facc14","#223273","#8543e0","#13c2c2","#3436c7","#f04864","#5cdbd3","#6dc8ec"],smooth:!1,lineWidth:4,showSymbol:!0,symbolSize:8,shadowBlur:6,useGradient:!0,legendIcon:"rect",defaultLineType:"solid",areaOpacity:.25},p90:{colors:["#9b8bfe","#26deca","#f8c032","#ff5722","#07a2a4","#4cc9f0","#4361ee","#7209b7","#f72585","#3a0ca3"],smooth:!0,lineWidth:3,showSymbol:!1,symbolSize:6,shadowBlur:4,useGradient:!0,legendIcon:"diamond",defaultLineType:"dashed",areaOpacity:.1},p99:{colors:["#e84a5f","#2a9d8f","#e9c46a","#264653","#f4a261","#ff9f1c","#2ec4b6","#e71d36","#011627","#fdfffc"],smooth:!0,lineWidth:3,showSymbol:!1,symbolSize:6,shadowBlur:5,useGradient:!0,legendIcon:"triangle",defaultLineType:"dotted",areaOpacity:.12}};return e[this.chartType]||e.responseTime},getLineType(e,t){const a=["solid","dashed","dotted"];return"p90"===this.chartType||"p99"===this.chartType?t.defaultLineType:a[e%a.length]},getSeriesColor(e,t){return t.colors[e%t.colors.length]},getAreaStyle(e,t){const a=this.getSeriesColor(e,t);return"All"!==this.selectedSeries&&t.useGradient?{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:a+Math.floor(255*t.areaOpacity).toString(16).padStart(2,"0")},{offset:1,color:a+"00"}]},origin:"auto"}:null},beforeDestroy(){this.chartInstance&&this.chartInstance.dispose(),window.removeEventListener("resize",this.resizeHandler)}}},cs=a(71241);const ps=(0,cs.A)(ls,[["render",os],["__scopeId","data-v-507a96e2"]]);var hs=ps,us=a(57477),ms={name:"PerformanceResult-Detail",components:{Icon:ts.In,ResponseTimeChart:hs,Edit:us.Edit,View:us.View,SwitchButton:us.SwitchButton,InfoFilled:us.InfoFilled,TrendCharts:us.TrendCharts,Search:us.Search,ArrowDown:us.ArrowDown,ArrowLeft:us.ArrowLeft,Check:us.Check,Download:us.Download,Document:us.Document,Monitor:us.Monitor,DocumentCopy:us.DocumentCopy,Files:us.Files,Bell:us.Bell,Message:us.Message,Link:us.Link,ChatDotRound:us.ChatDotRound,ChatRound:us.ChatRound,MoreFilled:us.MoreFilled,DataAnalysis:us.DataAnalysis,PieChart:us.PieChart,Setting:us.Setting,Connection:us.Connection,CircleClose:us.CircleClose},data(){return{reportId:null,selectedKeys:[],reportData:{},taskName:"",desc:"",taskType:"",inputDlg:!1,editingField:"",tempTaskName:"",activeIndex:"1",textResult:"",reportList:[],xData:[],seriesData:[],realTimeData:{},monitoringData:{},logData:[],baselineDialogData:{visible:!1,loading:!1,mode:"create",form:{name:"",description:"",task_id:null,avg_response_time:0,avg_tps:0,success_rate:0,avg_cpu:0,avg_memory:0,is_active:!0,project_id:null}},baselineFormRules:{name:[{required:!0,message:"请输入基准线名称",trigger:"blur"},{min:2,max:100,message:"长度在2到100个字符",trigger:"blur"}],task_id:[{required:!0,message:"请选择关联任务",trigger:"change"}]},baselineCompareDialogData:{visible:!1,loading:!1,baselineList:[],selectedBaselineId:null},baselineCompareResultData:{visible:!1,baseline_metrics:{avg_response_time:0,avg_tps:0,success_rate:0,avg_cpu:0,avg_memory:0},current_metrics:{avg_response_time:0,avg_tps:0,success_rate:0,avg_cpu:0,avg_memory:0},conclusion:""},logCategories:{all:"全部",system:"系统",request:"请求",error:"错误",performance:"性能",event:"事件"},logCategory:"all",wsConnection:null,wsReconnectAttempts:0,wsMaxReconnectAttempts:5,wsReconnectInterval:null,wsConnectionStatus:"disconnected",autoRefresh:!0,refreshInterval:null,targetServiceData:{},logFilter:"",logLevel:"all",tableLoading:!1,chartUpdateKey:0,monitoringDataLoaded:!1,targetServiceDataLoaded:!1,logDataLoaded:!1,monitoringRefreshInterval:null,targetServiceRefreshInterval:null,responseTimeData:[],rpsData:[],tpsData:[],usersData:[],p50Data:[],p90Data:[],p95Data:[],p99Data:[],errorRateData:[],interfaceChartData:{responseTime:{},rps:{},tps:{},users:{},p50:{},p90:{},p95:{},p99:{},errorRate:{}},availableInterfaces:[],realTimeUpdateInterval:null,isFilterMode:!1,selectedRows:[],guiUrl:"",hasGuiUrlLoaded:!1,iframeError:!1,iframeErrorMessage:"",analysisDialogData:{visible:!1,performance_score:0,bottlenecks:[],recommendations:[]},configDialogData:{visible:!1,taskConfig:null},notificationDialogData:{visible:!1,type:"",pushType:"wechat",name:"",webhook:"",recipients:[]},notificationRules:{name:[{required:!0,message:"请输入推送名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 个字符",trigger:"blur"}],pushType:[{required:!0,message:"请选择推送类型",trigger:"change"}],webhook:[{required:!0,message:"请输入webhook地址",trigger:"blur"},{min:3,message:"webhook地址至少3个字符",trigger:"blur"}],recipients:[{required:!0,message:"请选择接收人",trigger:"change"}]},monitorUrl:"",monitorIframeUrl:""}},computed:{isTestRunning(){return"1"===this.reportData.reportStatus||"运行中"===this.taskType},isEdit(){return!0},filteredLogs(){let e=this.logData;return this.logLevel&&"all"!==this.logLevel&&(e=e.filter(e=>e.level===this.logLevel)),this.logCategory&&"all"!==this.logCategory&&(e=e.filter(e=>e.category===this.logCategory)),this.logFilter&&(e=e.filter(e=>e.message.toLowerCase().includes(this.logFilter.toLowerCase())||e.details&&e.details.toLowerCase().includes(this.logFilter.toLowerCase()))),e.sort((e,t)=>new Date(e.timestamp)-new Date(t.timestamp))},responseTimeSeriesData(){const e=[];return this.isFilterMode&&this.selectedRows.length>0&&(this.selectedKeys=this.selectedRows.map(e=>e.method+"_"+e.name)),Object.keys(this.interfaceChartData.responseTime).forEach(t=>{this.isFilterMode&&this.selectedKeys&&!this.selectedKeys.includes(t)||this.interfaceChartData.responseTime[t].length>0&&e.push({name:t,values:this.interfaceChartData.responseTime[t]})}),e.length>0?e:[{name:"平均响应时间",values:[]}]},rpsSeriesData(){const e=[];return this.isFilterMode&&this.selectedRows.length>0&&(this.selectedKeys=this.selectedRows.map(e=>e.method+"_"+e.name)),Object.keys(this.interfaceChartData.rps).forEach(t=>{this.isFilterMode&&this.selectedKeys&&!this.selectedKeys.includes(t)||this.interfaceChartData.rps[t].length>0&&e.push({name:t,values:this.interfaceChartData.rps[t]})}),0===e.length&&this.rpsData&&this.rpsData.length>0&&e.push({name:"总体RPS",values:this.rpsData}),e.length>0?e:[{name:"RPS",values:[]}]},tpsSeriesData(){const e=[];return this.isFilterMode&&this.selectedRows.length>0&&(this.selectedKeys=this.selectedRows.map(e=>e.method+"_"+e.name)),Object.keys(this.interfaceChartData.tps).forEach(t=>{this.isFilterMode&&this.selectedKeys&&!this.selectedKeys.includes(t)||this.interfaceChartData.tps[t].length>0&&e.push({name:t,values:this.interfaceChartData.tps[t]})}),0===e.length&&this.tpsData&&this.tpsData.length>0&&e.push({name:"总体TPS",values:this.tpsData}),e.length>0?e:[{name:"TPS",values:[]}]},usersSeriesData(){const e=[];return this.usersData.length>0&&e.push({name:"并发用户数",values:this.usersData}),e.length>0?e:[{name:"并发用户数",values:[]}]},p50SeriesData(){const e=[];return this.isFilterMode&&this.selectedRows.length>0&&(this.selectedKeys=this.selectedRows.map(e=>e.method+"_"+e.name)),Object.keys(this.interfaceChartData.p50).forEach(t=>{this.isFilterMode&&this.selectedKeys&&!this.selectedKeys.includes(t)||this.interfaceChartData.p50[t].length>0&&e.push({name:t,values:this.interfaceChartData.p50[t]})}),e.length>0?e:[{name:"50%响应时间线",values:[]}]},p90SeriesData(){const e=[];return this.isFilterMode&&this.selectedRows.length>0&&(this.selectedKeys=this.selectedRows.map(e=>e.method+"_"+e.name)),Object.keys(this.interfaceChartData.p90).forEach(t=>{this.isFilterMode&&this.selectedKeys&&!this.selectedKeys.includes(t)||this.interfaceChartData.p90[t].length>0&&e.push({name:t,values:this.interfaceChartData.p90[t]})}),e.length>0?e:[{name:"90%响应时间线",values:[]}]},p95SeriesData(){const e=[];return this.isFilterMode&&this.selectedRows.length>0&&(this.selectedKeys=this.selectedRows.map(e=>e.method+"_"+e.name)),Object.keys(this.interfaceChartData.p95).forEach(t=>{this.isFilterMode&&this.selectedKeys&&!this.selectedKeys.includes(t)||this.interfaceChartData.p95[t].length>0&&e.push({name:t,values:this.interfaceChartData.p95[t]})}),e.length>0?e:[{name:"95%响应时间线",values:[]}]},p99SeriesData(){const e=[];return this.isFilterMode&&this.selectedRows.length>0&&(this.selectedKeys=this.selectedRows.map(e=>e.method+"_"+e.name)),Object.keys(this.interfaceChartData.p99).forEach(t=>{this.isFilterMode&&this.selectedKeys&&!this.selectedKeys.includes(t)||this.interfaceChartData.p99[t].length>0&&e.push({name:t,values:this.interfaceChartData.p99[t]})}),e.length>0?e:[{name:"99%响应时间线",values:[]}]},filteredMetrics(){const e=this.getDetailedMetrics();if(!this.isFilterMode||0===this.selectedRows.length)return e;const t=this.selectedRows.map(e=>e.name),a=e.filter(e=>"ALL"===e.method||t.includes(e.name)),s=a.filter(e=>"ALL"===e.method),i=a.filter(e=>"ALL"!==e.method);return[...s,...i]}},methods:{getSuccessRate(){if(!this.reportData||!this.reportData.totalRequests||this.reportData.totalRequests<=0)return 0;const e=this.reportData.successRequests||0;return Math.round(e/this.reportData.totalRequests*100)},initWebSocket(){if(this.cleanupWebSocket(),this.reportId&&"1"===this.reportData.reportStatus){this.wsConnectionStatus="connecting";try{const e="https:"===window.location.protocol?"wss:":"ws:",t="ws://192.168.2.213:8000",a=`${e}//${t}/ws/performance/report/${this.reportId}/`;this.wsConnection=new WebSocket(a);const s=setTimeout(()=>{this.wsConnection&&this.wsConnection.readyState===WebSocket.CONNECTING&&(this.wsConnection.close(),this.handleWebSocketError("连接超时"))},1e4);this.wsConnection.onopen=()=>{clearTimeout(s),this.wsConnectionStatus="connected",this.wsReconnectAttempts=0,this.startHeartbeat(),this.$message.success("实时监控已启动")},this.wsConnection.onmessage=e=>{try{const t=JSON.parse(e.data);this.handleWebSocketMessage(t)}catch(t){console.error("解析WebSocket消息失败:",t)}},this.wsConnection.onerror=e=>{clearTimeout(s),console.error("WebSocket连接错误:",e),this.handleWebSocketError("连接错误")},this.wsConnection.onclose=e=>{clearTimeout(s),this.wsConnectionStatus="disconnected",this.stopHeartbeat(),this.shouldReconnect(e)&&this.attemptReconnect()}}catch(e){this.handleWebSocketError("创建连接失败")}}},cleanupWebSocket(){this.wsConnection&&(this.wsConnection.close(),this.wsConnection=null),this.stopHeartbeat(),this.clearReconnectTimer(),this.wsConnectionStatus="disconnected"},shouldReconnect(e){return 1e3!==e.code&&("1"===this.reportData.reportStatus&&(this.wsReconnectAttempts>=this.wsMaxReconnectAttempts?(this.$message.warning("实时监控连接已断开，请手动刷新页面"),!1):!!this.autoRefresh))},attemptReconnect(){this.wsReconnectAttempts++;const e=Math.min(1e3*Math.pow(2,this.wsReconnectAttempts),3e4);console.log(`尝试第${this.wsReconnectAttempts}次重连，${e}ms后重试`),this.wsReconnectInterval=setTimeout(()=>{"connected"!==this.wsConnectionStatus&&this.initWebSocket()},e)},clearReconnectTimer(){this.wsReconnectInterval&&(clearTimeout(this.wsReconnectInterval),this.wsReconnectInterval=null)},handleWebSocketError(e){this.wsConnectionStatus="error",console.error("WebSocket错误:",e),0===this.wsReconnectAttempts&&this.enablePollingMode()},enablePollingMode(){this.refreshInterval&&clearInterval(this.refreshInterval),this.autoRefresh&&"1"===this.reportData.reportStatus&&(this.refreshInterval=setInterval(()=>{this.loadMonitoringData(),this.loadTargetServiceData(),this.loadLogData()},5e3))},startHeartbeat(){this.heartbeatInterval=setInterval(()=>{this.wsConnection&&this.wsConnection.readyState===WebSocket.OPEN&&this.wsConnection.send(JSON.stringify({type:"heartbeat"}))},3e4)},stopHeartbeat(){this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null)},reconnectWebSocket(){this.wsReconnectAttempts=0,this.initWebSocket()},handleWebSocketMessage(e){if("performance_update"===e.type){if(this.realTimeData=e.data,this.updateChartData(),e.data&&e.data.total){const t=e.data.total;this.updateBasicMetricsFromWebSocket(t),this.addPerformanceLog({tps:t.current_rps,responseTime:t.avg_response_time,users:t.current_users,errorRate:t.error_rate})}e.data&&e.data.detailed_stats&&this.$nextTick(()=>{this.$forceUpdate()})}else if("log_update"===e.type){const t=this.enhanceLogData(e.data);this.logData.push(t),this.logData.length>2e3&&(this.logData=this.logData.slice(-2e3)),this.scrollToLogBottom()}else"monitoring_update"===e.type||"system_resources"===e.type?(this.monitoringData=e.data,this.addSystemResourceLog(e.data),this.$forceUpdate()):"test_complete"===e.type?(this.addEventLog("测试完成","收到测试完成信号，正在验证测试状态...","info"),setTimeout(()=>{this.verifyTestCompletion()},3e3)):"test_failed"===e.type?(this.taskType="运行失败",this.reportData.reportStatus="99",this.addEventLog("测试失败","测试执行失败，请检查详细错误信息","error"),this.stopAllRealTimeUpdates()):"request_success"!==e.type&&"request_failure"!==e.type||this.addRequestLog(e)},enhanceLogData(e){if(e.category)return e;let t="system";const a=e.message||"";a.includes("ERROR")||a.includes("Exception")||"error"===e.level?t="error":a.includes("Request")||a.includes("HTTP")||a.includes("GET ")||a.includes("POST ")||a.includes("PUT ")||a.includes("DELETE ")?t="request":a.includes("TPS")||a.includes("RPS")||a.includes("响应时间")||a.includes("用户数")||a.includes("性能")?t="performance":(a.includes("开始")||a.includes("结束")||a.includes("启动")||a.includes("停止")||a.includes("完成"))&&(t="event");let s="";if(a.includes("{")&&a.includes("}"))try{const e=a.indexOf("{"),t=a.lastIndexOf("}")+1,i=a.substring(e,t),r=JSON.parse(i);s=JSON.stringify(r,null,2)}catch(i){}return{...e,category:t,details:s,formatted:this.formatLogMessage(e)}},formatLogMessage(e){const t=e.message||"";if(t.includes("HTTP")&&(t.includes("GET")||t.includes("POST")||t.includes("PUT")||t.includes("DELETE"))){const e=t.match(/(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\s+([^\s]+)/),a=t.match(/status\s+(\d+)/i)||t.match(/(\d{3})/);if(e){const t=e[1],s=e[2],i=a?a[1]:"";return{method:t,url:s,status:i,isFormatted:!0}}}return null},scrollToLogBottom(){"5"===this.activeIndex&&this.$nextTick(()=>{const e=document.querySelector(".log-container");e&&(e.scrollTop=e.scrollHeight)})},addSystemResourceLog(e){if(!e)return;const t=new Date,a=this.logData.find(e=>"system"===e.category&&"resource"===e.type);if(a){const e=new Date(a.timestamp);if(t-e<6e4)return}this.logData.push({timestamp:(new Date).toISOString(),level:"info",category:"system",type:"resource",message:`系统资源: CPU ${e.cpu_percent||0}%, 内存 ${e.memory_percent||0}%, 磁盘 ${e.disk_percent||0}%`,details:JSON.stringify({cpu:e.cpu_percent||0,memory:e.memory_percent||0,disk:e.disk_percent||0,network_sent:this.formatBytes(e.network_sent||0),network_recv:this.formatBytes(e.network_recv||0)},null,2)}),this.logData.length>2e3&&(this.logData=this.logData.slice(-2e3)),this.scrollToLogBottom()},addPerformanceLog(e){if(!e)return;const t=new Date,a=this.logData.find(e=>"performance"===e.category&&"metrics"===e.type);if(a){const e=new Date(a.timestamp);if(t-e<6e4)return}this.logData.push({timestamp:(new Date).toISOString(),level:"info",category:"performance",type:"metrics",message:`性能指标: TPS ${e.tps?.toFixed(2)||0}, 响应时间 ${e.responseTime?.toFixed(2)||0}ms, 用户数 ${e.users||0}, 错误率 ${e.errorRate?.toFixed(2)||0}%`,details:JSON.stringify({tps:e.tps?.toFixed(2)||0,responseTime:e.responseTime?.toFixed(2)||0,users:e.users||0,errorRate:e.errorRate?.toFixed(2)||0},null,2)}),this.logData.length>2e3&&(this.logData=this.logData.slice(-2e3)),this.scrollToLogBottom()},addEventLog(e,t,a="info"){this.logData.push({timestamp:(new Date).toISOString(),level:a,category:"event",type:"event",message:`${e}: ${t}`}),this.logData.length>2e3&&(this.logData=this.logData.slice(-2e3)),this.scrollToLogBottom()},addRequestLog(e){if(!e||!e.data)return;const t=e.data,a="request_success"===e.type,s=a?"info":"error",i=a?`请求成功: ${t.method||"GET"} ${t.url||"/"} (${t.response_time||0}ms)`:`请求失败: ${t.method||"GET"} ${t.url||"/"} - ${t.error||"未知错误"}`;this.logData.push({timestamp:(new Date).toISOString(),level:s,category:"request",type:a?"success":"failure",message:i,details:JSON.stringify({method:t.method||"GET",url:t.url||"/",status:t.status||(a?200:500),response_time:t.response_time||0,error:t.error||null,exception:t.exception||null},null,2),formatted:{method:t.method||"GET",url:t.url||"/",status:t.status||(a?200:500),isFormatted:!0}}),this.logData.length>2e3&&(this.logData=this.logData.slice(-2e3)),this.scrollToLogBottom()},getLogClass(e){return`log-${e}`},getStatusClass(e){if(!e)return"";const t=parseInt(e);return t>=200&&t<300?"status-success":t>=300&&t<400?"status-redirect":t>=400&&t<500?"status-client-error":t>=500?"status-server-error":""},getMethodClass(e){if(!e)return"";switch(e.toUpperCase()){case"GET":return"method-get";case"POST":return"method-post";case"PUT":return"method-put";case"DELETE":return"method-delete";case"PATCH":return"method-patch";case"HEAD":return"method-head";case"OPTIONS":return"method-options";default:return""}},getLogCategoryIcon(e){switch(e){case"system":return"Monitor";case"request":return"Connection";case"error":return"CircleClose";case"performance":return"DataAnalysis";case"event":return"Bell";default:return"Document"}},async loadLogData(){try{const e={level:"all"===this.logLevel?"":this.logLevel,limit:2e3,offset:0},t=await this.$api.getTaskReportLogs(this.reportId,e);this.logData=(t.data.logs||[]).map(e=>this.enhanceLogData(e)),0===this.logData.length&&this.logData.push({timestamp:(new Date).toISOString(),level:"info",category:"event",type:"event",message:"日志初始化: 日志系统已初始化，等待测试数据..."}),this.scrollToLogBottom()}catch(e){this.logData=[],this.logData.push({timestamp:(new Date).toISOString(),level:"error",category:"event",type:"event",message:`日志加载失败: ${e.message||"未知错误"}`}),this.scrollToLogBottom()}},async rerunTest(){try{await this.$confirm("将使用相同的测试配置，创建新的测试报告进行重新测试？","确认重新运行",{confirmButtonText:"确定运行",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}),this.$message.info("正在启动测试，请稍候...");let e=null;if(this.reportData.task&&("object"===typeof this.reportData.task&&this.reportData.task.id?e=this.reportData.task.id:"number"===typeof this.reportData.task&&(e=this.reportData.task)),e||(e=this.reportData.taskId||this.reportData.task_id),!e)throw new Error("无法获取任务ID");let t=null;t=this.reportData.env?"object"===typeof this.reportData.env?this.reportData.env.id:this.reportData.env:this.reportData.envId||this.reportData.env_id;const a=await this.$api.runTask(e,{rerun:!0,copy_settings:!0,env_id:t,new_report_name:`${this.reportData.reportName}_重跑_${(new Date).toISOString().slice(0,16).replace("T","_").replace(/:/g,"")}`});if(200===a.status){this.$message.success("测试重新运行成功！");const e=a.data.report_id||a.data.id;e?this.$router.push({name:"PerformanceResult-Detail",params:{id:e}}):setTimeout(()=>{location.reload()},2e3)}}catch(e){if("cancel"===e)return void this.$message.info("已取消重新运行");this.$message.error("重新运行失败: "+(e.response?.data?.message||e.message||"未知错误"))}},async verifyTestCompletion(){try{const e=await this.$api.getTaskReportDetail(this.reportId),t=e.data,a="0"===t.reportStatus,s=t.endTime&&null!==t.endTime,i=t.totalRequests&&t.totalRequests>0;a&&(s||i)?(this.taskType="已完成",this.reportData.reportStatus="0",Object.assign(this.reportData,t),this.stopAllRealTimeUpdates(),this.$message.success("性能测试已完成"),this.generateChartData()):setTimeout(()=>{this.verifyTestCompletion()},5e3)}catch(e){this.taskType="已完成",this.reportData.reportStatus="0",this.stopAllRealTimeUpdates(),this.loadReportData()}},updateBasicMetricsFromWebSocket(e){void 0!==e.current_rps&&(this.reportData.avgTps=e.current_rps),void 0!==e.avg_response_time&&(this.reportData.avgResponseTime=e.avg_response_time),void 0!==e.min_response_time&&(this.reportData.minResponseTime=e.min_response_time),void 0!==e.max_response_time&&(this.reportData.maxResponseTime=e.max_response_time),void 0!==e.median_response_time&&(this.reportData.p50ResponseTime=e.median_response_time),void 0!==e.p90_response_time&&(this.reportData.p90ResponseTime=e.p90_response_time),void 0!==e.p95_response_time&&(this.reportData.p95ResponseTime=e.p95_response_time),void 0!==e.p99_response_time&&(this.reportData.p99ResponseTime=e.p99_response_time),void 0!==e.num_requests&&(this.reportData.totalRequests=e.num_requests),void 0!==e.num_failures&&(this.reportData.failedRequests=e.num_failures,this.reportData.successRequests=(e.num_requests||0)-e.num_failures),void 0!==e.error_rate&&(this.reportData.errorRate=e.error_rate),void 0!==e.current_users&&(this.reportData.currentUsers=e.current_users),void 0!==e.elapsed_time&&(this.reportData.duration=Math.floor(e.elapsed_time)),this.$forceUpdate()},async loadReportData(){try{const e=await this.$api.getTaskReportDetail(this.reportId);this.reportData=e.data,this.taskName=this.reportData.reportName,this.desc=this.reportData.resultAnalyse||"暂无分析结果",this.taskType=this.getStatusText(this.reportData.reportStatus),"0"!==this.reportData.reportStatus&&"99"!==this.reportData.reportStatus||this.generateAnalysisResult(),this.generateChartData(),"1"===this.reportData.reportStatus&&(this.initWebSocket(),this.startRealTimeDataUpdate())}catch(e){this.$message.error("加载报告数据失败")}},generateAnalysisResult(){if(!this.reportData)return;let e=this.desc||"";if(!e||"暂无分析结果"===e)if("0"===this.reportData.reportStatus){const t=this.getSuccessRate(),a=(this.reportData.avgResponseTime||0).toFixed(2),s=this.reportData.avgTps||0,i=this.reportData.errorRate||0;e="测试已成功完成。\n",e+=`总体性能表现：平均响应时间 ${a}ms，平均TPS ${s}，成功率 ${t}%。\n`,e+=a<100?"响应时间表现优秀，系统响应迅速。":a<500?"响应时间表现良好，符合一般性能要求。":a<1e3?"响应时间偏高，建议进行性能优化。":"响应时间过高，存在明显性能瓶颈，需要立即优化。",i>5?e+=`\n错误率较高(${i}%)，建议检查系统稳定性。`:i>1&&(e+=`\n存在少量错误(${i}%)，建议关注异常情况。`)}else if("99"===this.reportData.reportStatus){if(e="测试运行失败。\n",this.reportData.totalRequests>0){const t=this.reportData.failedRequests||0,a=this.reportData.errorRate||0;e+=`在执行 ${this.reportData.totalRequests} 个请求中，失败 ${t} 个，错误率 ${a}%。\n`}e+="建议检查：\n",e+="1. 目标服务是否正常运行\n",e+="2. 网络连接是否稳定\n",e+="3. 测试脚本配置是否正确\n",e+="4. 服务器资源是否充足"}this.reportData.avgCpu&&this.reportData.avgMemory&&(e+=`\n\n资源使用情况：平均CPU ${this.reportData.avgCpu}%，平均内存 ${this.reportData.avgMemory}%。`,this.reportData.avgCpu>80?e+="CPU使用率过高，可能成为性能瓶颈。":this.reportData.avgMemory>85&&(e+="内存使用率过高，建议增加内存或优化内存使用。")),this.desc=e},async loadMonitoringData(){try{const e=await this.$api.getSystemResourceStatus(),t=e.data.current||{};this.monitoringData={cpu_percent:t.cpu?.percent||0,memory_percent:t.memory?.percent||0,disk_percent:t.disk?.percent||0,network_sent:t.network?.bytes_sent||0,network_recv:t.network?.bytes_recv||0,active_connections:t.connections||0,current_rps:t.rps||0,current_users:t.users||0,error_rate:t.error_rate||0,server_type:t.server_type||"single",cpu_cores:t.cpu?.count||0,total_memory:t.memory?.total||0,uptime:t.uptime||null}}catch(e){console.error("加载监控数据失败:",e),this.monitoringData={cpu_percent:0,memory_percent:0,disk_percent:0,network_sent:0,network_recv:0,active_connections:0,current_rps:0,current_users:0,error_rate:0,server_type:"unknown",cpu_cores:0,total_memory:0,uptime:null}}},async loadTargetServiceData(){try{const e=await this.$api.getTargetServiceStatus(this.reportId);this.targetServiceData=e.data||{}}catch(e){console.error("加载目标服务数据失败:",e),this.targetServiceData={service_status:"unknown",connection_pool_active:0,connection_pool_total:0,db_connections:0,cache_hit_rate:0,response_time_trend:{labels:[],values:[]},recent_errors:[]}}},async refreshLogData(){await this.loadLogData(),this.$message.success("日志已刷新"),this.scrollToLogBottom()},clearLogs(){this.logData=[],this.$message.success("日志已清空")},initEmptyChartData(){this.xData=[],this.responseTimeData=[],this.rpsData=[],this.tpsData=[],this.usersData=[],this.p50Data=[],this.p90Data=[],this.p95Data=[],this.p99Data=[],this.errorRateData=[]},generateChartData(){console.log("开始生成图表数据"),this.initEmptyChartData();let e=!1;if(this.reportData.reportResult)try{const t=JSON.parse(this.reportData.reportResult);t.stats_history&&t.stats_history.length>0?(this.updateChartDataFromHistory(t.stats_history),e=!0):t.detailed_stats&&"0"===this.reportData.reportStatus&&(this.updateChartDataFromDetailedStats(t),e=!0)}catch(t){console.error("解析报告结果失败:",t)}!e&&this.reportData.totalRequests&&this.reportData.totalRequests>0&&"0"===this.reportData.reportStatus&&(this.generateChartFromReportData(),e=!0),e||"1"!==this.reportData.reportStatus?e||console.log("无可用数据，显示空图表"):(console.log("测试运行中，启动实时数据更新"),this.startRealTimeDataUpdate()),this.$nextTick(()=>{if("1"===this.activeIndex){const e=this.$refs.table;e&&e.$forceUpdate(),this.$forceUpdate()}})},updateChartDataFromHistory(e){if(this.xData=[],this.responseTimeData=[],this.rpsData=[],this.tpsData=[],this.usersData=[],this.p50Data=[],this.p90Data=[],this.p95Data=[],this.p99Data=[],this.errorRateData=[],this.interfaceChartData={responseTime:{},rps:{},tps:{},users:{},p50:{},p90:{},p95:{},p99:{},errorRate:{}},this.availableInterfaces=[],e.length>0){const t=e[e.length-1];this.realTimeData={total:t.total||{},detailed_stats:t.detailed_stats||{}}}e.forEach((e,t)=>{const a=e.timestamp||(new Date).toISOString();this.xData.push(new Date(a).toLocaleTimeString());const s=e.total||{};this.responseTimeData.push(s.avg_response_time||0),this.rpsData.push(s.current_rps||0),this.tpsData.push(s.current_tps||s.current_rps||0),this.usersData.push(s.current_users||0),this.p50Data.push(s.p50_response_time||0),this.p90Data.push(s.p90_response_time||0),this.p95Data.push(s.p95_response_time||0),this.p99Data.push(s.p99_response_time||0),this.errorRateData.push(s.error_rate||0),e.detailed_stats&&Object.keys(e.detailed_stats).forEach(t=>{const a=e.detailed_stats[t],s=this.getCorrectHttpMethod(t,a),i=this.getCorrectInterfaceName(t,a),r=s+"_"+i;this.interfaceChartData.responseTime[r]||(this.interfaceChartData.responseTime[r]=[],this.interfaceChartData.rps[r]=[],this.interfaceChartData.tps[r]=[],this.interfaceChartData.users[r]=[],this.interfaceChartData.p50[r]=[],this.interfaceChartData.p90[r]=[],this.interfaceChartData.p95[r]=[],this.interfaceChartData.p99[r]=[],this.interfaceChartData.errorRate[r]=[]),this.interfaceChartData.responseTime[r].push(a.avg_response_time||0),this.interfaceChartData.rps[r].push(a.current_rps||0),this.interfaceChartData.tps[r].push(a.current_tps||a.current_rps||0),this.interfaceChartData.users[r].push(a.current_users||0),this.interfaceChartData.p50[r].push(a.p50_response_time||0),this.interfaceChartData.p90[r].push(a.p90_response_time||0),this.interfaceChartData.p95[r].push(a.p95_response_time||0),this.interfaceChartData.p99[r].push(a.p99_response_time||0),this.interfaceChartData.errorRate[r].push(a.error_rate||0),this.availableInterfaces.includes(r)||this.availableInterfaces.push(r)})})},updateChartDataFromDetailedStats(e){this.realTimeData={total:e.total||{},detailed_stats:e.detailed_stats||{}};const t=e.total||{},a=e.timestamp||(new Date).toISOString();this.xData=[new Date(a).toLocaleTimeString()],this.responseTimeData=[t.avg_response_time||0],this.rpsData=[t.current_rps||0],this.tpsData=[t.current_tps||t.current_rps||0],this.usersData=[t.current_users||0],this.p50Data=[t.p50_response_time||0],this.p90Data=[t.p90_response_time||0],this.p95Data=[t.p95_response_time||0],this.p99Data=[t.p99_response_time||0],this.errorRateData=[t.error_rate||0]},generateChartFromReportData(){const e=this.reportData.duration||300,t=Math.min(e/10,30);this.xData=[],this.responseTimeData=[],this.rpsData=[],this.tpsData=[],this.usersData=[],this.p50Data=[],this.p90Data=[],this.p95Data=[],this.p99Data=[],this.errorRateData=[];const a=this.reportData.avgResponseTime||0,s=this.reportData.avgTps||0,i=this.reportData.totalRequests||0,r=e>0?i/e:s,o=this.reportData.p50ResponseTime||1.5*a,n=this.reportData.p90ResponseTime||2*a,l=this.reportData.p95ResponseTime||2.5*a,c=this.reportData.p99ResponseTime||3*a,p=this.reportData.errorRate||0,h=this.reportData.startTime?new Date(this.reportData.startTime):new Date;for(let u=0;u<t;u++){const e=new Date(h.getTime()+1e4*u);this.xData.push(e.toLocaleTimeString());const t=.1;this.responseTimeData.push(this.addVariation(a,t)),this.rpsData.push(this.addVariation(r,t)),this.tpsData.push(this.addVariation(s,t)),this.usersData.push(Math.max(1,Math.floor(this.addVariation(50,t)))),this.p50Data.push(this.addVariation(o,t)),this.p90Data.push(this.addVariation(n,t)),this.p95Data.push(this.addVariation(l,t)),this.p99Data.push(this.addVariation(c,t)),this.errorRateData.push(Math.max(0,this.addVariation(p,t)))}},addVariation(e,t){const a=2*(Math.random()-.5)*t,s=e*(1+a);return Math.max(0,Number(s.toFixed(2)))},generateRealisticData(e,t,a,s){const i=[];let r=(t+a)/2,o=.1*(Math.random()-.5);for(let n=0;n<e;n++){r+=o*(a-t)*.05;const e=(Math.random()-.5)*(a-t)*.3;let n=r+e;n=Math.max(t,Math.min(a,n)),"error_rate"===s?n=Math.max(0,Math.min(10,n)):"users"===s&&(n=Math.floor(n)),i.push(Number(n.toFixed(2))),Math.random()<.1&&(o=.1*(Math.random()-.5))}return i},startRealTimeDataUpdate(){console.log("启动实时数据更新"),this.realTimeUpdateInterval&&clearInterval(this.realTimeUpdateInterval),this.realTimeUpdateInterval=setInterval(()=>{this.fetchRealTimeReportData()},3e3)},async fetchRealTimeReportData(){try{const t=await this.$api.getTaskReportDetail(this.reportId),a=t.data;if(null!==a.gui_url&&""!==a.gui_url&&(this.guiUrl=a.gui_url,!1===this.hasGuiUrlLoaded&&this.refreshGUI(),this.hasGuiUrlLoaded=!0),this.updateReportMetrics(a),a.reportResult&&(this.reportData.reportResult=a.reportResult),a.reportResult)try{const e=JSON.parse(a.reportResult);e.stats_history&&e.stats_history.length>0?this.updateChartDataFromHistory(e.stats_history):e.total&&this.addRealTimeDataFromStats(e.total,e.detailed_stats),e.detailed_stats&&this.$nextTick(()=>{if(this.$forceUpdate(),"1"===this.activeIndex){const e=this.$refs.table;e&&e.$forceUpdate()}})}catch(e){console.error("解析实时统计数据失败:",e)}"1"!==a.reportStatus&&(console.log("测试已完成，停止实时更新"),this.stopAllRealTimeUpdates(),await this.loadReportData())}catch(e){console.error("获取实时报告数据失败:",e),this.stopAllRealTimeUpdates()}},stopAllRealTimeUpdates(){console.log("停止所有实时数据更新..."),this.stopRealTimeDataUpdate(),this.stopMonitoringRefresh(),this.stopTargetServiceRefresh(),this.refreshInterval&&(clearInterval(this.refreshInterval),this.refreshInterval=null),this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null),this.autoRefresh=!1,this.cleanupWebSocket(),console.log("所有实时数据更新已停止")},stopRealTimeDataUpdate(){this.realTimeUpdateInterval&&(clearInterval(this.realTimeUpdateInterval),this.realTimeUpdateInterval=null,console.log("实时数据更新已停止"))},updateReportMetrics(e){void 0!==e.avgTps&&(this.reportData.avgTps=e.avgTps),void 0!==e.avgResponseTime&&(this.reportData.avgResponseTime=e.avgResponseTime),void 0!==e.avgCpu&&(this.reportData.avgCpu=e.avgCpu),void 0!==e.avgMemory&&(this.reportData.avgMemory=e.avgMemory),void 0!==e.totalRequests&&(this.reportData.totalRequests=e.totalRequests),void 0!==e.successRequests&&(this.reportData.successRequests=e.successRequests),void 0!==e.failedRequests&&(this.reportData.failedRequests=e.failedRequests),void 0!==e.errorRate&&(this.reportData.errorRate=e.errorRate),void 0!==e.duration&&(this.reportData.duration=e.duration),void 0!==e.reportStatus&&(this.reportData.reportStatus=e.reportStatus,this.taskType=this.getStatusText(e.reportStatus))},addRealTimeDataFromStats(e,t=null){const a=(new Date).toLocaleTimeString();this.xData.push(a),this.responseTimeData.push(e.avg_response_time||0),this.rpsData.push(e.current_rps||0),this.tpsData.push(e.current_tps||e.current_rps||0),this.usersData.push(e.current_users||0),this.p50Data.push(e.p50_response_time||0),this.p90Data.push(e.p90_response_time||0),this.p95Data.push(e.p95_response_time||0),this.p99Data.push(e.p99_response_time||0),this.errorRateData.push(e.error_rate||0),t&&Object.entries(t).forEach(([t,a])=>{const s=this.getCorrectInterfaceName(t,a);this.interfaceChartData.responseTime[s]||(this.interfaceChartData.responseTime[s]=[],this.interfaceChartData.rps[s]=[],this.interfaceChartData.tps[s]=[],this.interfaceChartData.users[s]=[],this.interfaceChartData.p50[s]=[],this.interfaceChartData.p90[s]=[],this.interfaceChartData.p95[s]=[],this.interfaceChartData.p99[s]=[],this.interfaceChartData.errorRate[s]=[]),this.interfaceChartData.responseTime[s].push(a.avg_response_time||0),this.interfaceChartData.rps[s].push(a.current_rps||0),this.interfaceChartData.tps[s].push(a.current_tps||a.current_rps||0),this.interfaceChartData.users[s].push(a.current_users||e.current_users||0),this.interfaceChartData.p50[s].push(a.p50_response_time||0),this.interfaceChartData.p90[s].push(a.p90_response_time||0),this.interfaceChartData.p95[s].push(a.p95_response_time||0),this.interfaceChartData.p99[s].push(a.p99_response_time||0),this.interfaceChartData.errorRate[s].push(a.error_rate||0),this.availableInterfaces.includes(s)||this.availableInterfaces.push(s)})},generateNextValue(e,t,a){if(0===e.length)return(t+a)/2;const s=e[e.length-1],i=a-t,r=.1*i,o=(Math.random()-.5)*r;let n=s+o;return n=Math.max(t,Math.min(a,n)),Number(n.toFixed(2))},updateChartData(){if(this.realTimeData){const e=(new Date).toLocaleTimeString();this.xData.push(e),this.xData.length>20&&(this.xData.shift(),this.responseTimeData.shift(),this.rpsData.shift(),this.tpsData.shift(),this.usersData.shift(),this.p50Data.shift(),this.p90Data.shift(),this.p95Data.shift(),this.p99Data.shift(),this.errorRateData.shift(),Object.keys(this.interfaceChartData.responseTime).forEach(e=>{this.interfaceChartData.responseTime[e].shift(),this.interfaceChartData.rps[e].shift(),this.interfaceChartData.tps[e].shift(),this.interfaceChartData.users[e].shift(),this.interfaceChartData.p50[e].shift(),this.interfaceChartData.p90[e].shift(),this.interfaceChartData.p95[e].shift(),this.interfaceChartData.p99[e].shift(),this.interfaceChartData.errorRate[e].shift()}));const t=this.realTimeData.total||{};this.responseTimeData.push(t.avg_response_time||0),this.rpsData.push(t.current_rps||0),this.tpsData.push(t.current_tps||t.current_rps||0),this.usersData.push(t.current_users||0),this.p50Data.push(t.p50_response_time||0),this.p90Data.push(t.p90_response_time||0),this.p95Data.push(t.p95_response_time||0),this.p99Data.push(t.p99_response_time||0),this.errorRateData.push(t.error_rate||0),this.realTimeData.detailed_stats&&Object.entries(this.realTimeData.detailed_stats).forEach(([e,a])=>{const s=this.getCorrectInterfaceName(e,a);this.interfaceChartData.responseTime[s]||(this.interfaceChartData.responseTime[s]=[],this.interfaceChartData.rps[s]=[],this.interfaceChartData.tps[s]=[],this.interfaceChartData.users[s]=[],this.interfaceChartData.p50[s]=[],this.interfaceChartData.p90[s]=[],this.interfaceChartData.p95[s]=[],this.interfaceChartData.p99[s]=[],this.interfaceChartData.errorRate[s]=[]),this.interfaceChartData.responseTime[s].push(a.avg_response_time||0),this.interfaceChartData.rps[s].push(a.current_rps||0),this.interfaceChartData.tps[s].push(a.current_tps||a.current_rps||0),this.interfaceChartData.users[s].push(a.current_users||t.current_users||0),this.interfaceChartData.p50[s].push(a.p50_response_time||0),this.interfaceChartData.p90[s].push(a.p90_response_time||0),this.interfaceChartData.p95[s].push(a.p95_response_time||0),this.interfaceChartData.p99[s].push(a.p99_response_time||0),this.interfaceChartData.errorRate[s].push(a.error_rate||0),this.availableInterfaces.includes(s)||this.availableInterfaces.push(s)})}},getDetailedMetrics(){if(!this.reportData)return[];if(this.realTimeData&&(this.realTimeData.detailed_stats||this.realTimeData.total)){const e=this.realTimeData.detailed_stats||{};return this.formatDetailedStats(e)}if(this.reportData.reportResult)try{const e=JSON.parse(this.reportData.reportResult);if(e.total&&(this.realTimeData=this.realTimeData||{},this.realTimeData.total=e.total),e.detailed_stats)return this.formatDetailedStats(e.detailed_stats)}catch(e){console.error("解析详细统计数据失败:",e)}return"0"===this.reportData.reportStatus&&this.reportData.totalRequests&&this.reportData.totalRequests>0?[{name:this.reportData.reportName||"总体",method:"ALL",totalRequests:this.reportData.totalRequests||0,successRequests:this.reportData.successRequests||0,failedRequests:this.reportData.failedRequests||0,maxResponseTime:this.reportData.maxResponseTime||0,minResponseTime:this.reportData.minResponseTime||0,avgResponseTime:this.reportData.avgResponseTime||0,p50ResponseTime:this.reportData.p50ResponseTime||0,p90ResponseTime:this.reportData.p90ResponseTime||0,p95ResponseTime:this.reportData.p95ResponseTime||0,p99ResponseTime:this.reportData.p99ResponseTime||0,rps:this.calculateRPS(),avgRps:this.calculateAvgRPS(),avgTps:this.reportData.avgTps||0,avgTpsAvg:this.reportData.avgTps||0,errorRate:(this.reportData.errorRate||0).toFixed(2)}]:[]},formatDetailedStats(e){const t=[],a=[];for(const[s,i]of Object.entries(e)){const e={name:this.getCorrectInterfaceName(s,i),method:this.getCorrectHttpMethod(s,i),totalRequests:i.num_requests||0,successRequests:i.success_requests||i.num_requests-i.num_failures||0,failedRequests:i.num_failures||0,maxResponseTime:Math.round(i.max_response_time||0),minResponseTime:Math.round(i.min_response_time||0),avgResponseTime:Math.round(i.avg_response_time||0),p50ResponseTime:Math.round(i.median_response_time||0),p90ResponseTime:Math.round(i.p90_response_time||0),p95ResponseTime:Math.round(i.p95_response_time||0),p99ResponseTime:Math.round(i.p99_response_time||0),rps:"number"===typeof i.current_rps?Number(i.current_rps).toFixed(2):"0.00",avgRps:"number"===typeof i.current_rps?Number(i.current_rps).toFixed(2):"0.00",avgTps:"number"===typeof i.current_rps?Number(i.current_rps).toFixed(2):"0.00",avgTpsAvg:"number"===typeof i.current_rps?Number(i.current_rps).toFixed(2):"0.00",errorRate:"number"===typeof i.error_rate?Number(i.error_rate).toFixed(2):"0.00",currentUsers:i.current_users||i.users||0};a.push(e),t.push(e)}if(a.length>0){const e=this.calculateTotalFromInterfaces(a);t.unshift(e)}else if(this.realTimeData&&this.realTimeData.total){const e=this.realTimeData.total,a=this.reportData.duration||1,s=e.num_requests?(e.num_requests/a).toFixed(2):"0.00";t.unshift({name:"总体",method:"ALL",totalRequests:e.num_requests||0,successRequests:e.num_requests-e.num_failures||0,failedRequests:e.num_failures||0,maxResponseTime:Math.round(e.max_response_time||0),minResponseTime:Math.round(e.min_response_time||0),avgResponseTime:Math.round(e.avg_response_time||0),p50ResponseTime:Math.round(e.median_response_time||0),p90ResponseTime:Math.round(e.p90_response_time||0),p95ResponseTime:Math.round(e.p95_response_time||0),p99ResponseTime:Math.round(e.p99_response_time||0),rps:(e.current_rps||0).toFixed(2),avgRps:s,avgTps:(e.current_tps||e.current_rps||0).toFixed(2),avgTpsAvg:s,errorRate:(e.error_rate||0).toFixed(2),currentUsers:e.current_users||this.getCurrentUserCount()})}else if(this.reportData&&this.reportData.totalRequests>0){const e=this.reportData.duration||1,a=this.reportData.totalRequests?(this.reportData.totalRequests/e).toFixed(2):"0.00";t.unshift({name:"总体",method:"ALL",totalRequests:this.reportData.totalRequests||0,successRequests:this.reportData.successRequests||0,failedRequests:this.reportData.failedRequests||0,maxResponseTime:Math.round(this.reportData.maxResponseTime||0),minResponseTime:Math.round(this.reportData.minResponseTime||0),avgResponseTime:Math.round(this.reportData.avgResponseTime||0),p50ResponseTime:Math.round(this.reportData.p50ResponseTime||0),p90ResponseTime:Math.round(this.reportData.p90ResponseTime||0),p95ResponseTime:Math.round(this.reportData.p95ResponseTime||0),p99ResponseTime:Math.round(this.reportData.p99ResponseTime||0),rps:a,avgRps:a,avgTps:(this.reportData.avgTps||0).toFixed(2),avgTpsAvg:(this.reportData.avgTps||0).toFixed(2),errorRate:(this.reportData.errorRate||0).toFixed(2),currentUsers:this.getCurrentUserCount()})}return t},calculateTotalFromInterfaces(e){let t=0,a=0,s=0,i=0,r=0,o=0,n=0,l=1/0,c=0,p=0,h=0,u=0,m=0;e.forEach(e=>{t+=e.totalRequests,a+=e.successRequests,s+=e.failedRequests,i+=parseFloat(e.rps),r+=parseFloat(e.avgTps),o=Math.max(o,e.currentUsers||0),n=Math.max(n,e.maxResponseTime),e.minResponseTime>0&&(l=Math.min(l,e.minResponseTime)),c+=e.avgResponseTime*e.totalRequests,p+=e.p50ResponseTime*e.totalRequests,h+=e.p90ResponseTime*e.totalRequests,u+=e.p95ResponseTime*e.totalRequests,m+=e.p99ResponseTime*e.totalRequests});const d=t>0?Math.round(c/t):0,g=t>0?Math.round(p/t):0,f=t>0?Math.round(h/t):0,D=t>0?Math.round(u/t):0,_=t>0?Math.round(m/t):0,v=t>0?(s/t*100).toFixed(2):"0.00";return l===1/0&&(l=0),{name:"总体",method:"ALL",totalRequests:t,successRequests:a,failedRequests:s,maxResponseTime:n,minResponseTime:Math.round(l),avgResponseTime:d,p50ResponseTime:g,p90ResponseTime:f,p95ResponseTime:D,p99ResponseTime:_,rps:i.toFixed(2),avgRps:i.toFixed(2),avgTps:r.toFixed(2),avgTpsAvg:r.toFixed(2),errorRate:v,currentUsers:o||this.getCurrentUserCount()}},calculateTotalStats(e){const t={name:"总体",method:"ALL",totalRequests:0,successRequests:0,failedRequests:0,maxResponseTime:0,minResponseTime:1/0,avgResponseTime:0,p50ResponseTime:0,p90ResponseTime:0,p95ResponseTime:0,p99ResponseTime:0,rps:0,avgRps:0,avgTps:0,avgTpsAvg:0,errorRate:0};let a=0,s=0,i=0,r=0,o=0;return e.forEach(e=>{t.totalRequests+=e.totalRequests,t.successRequests+=e.successRequests,t.failedRequests+=e.failedRequests,t.maxResponseTime=Math.max(t.maxResponseTime,e.maxResponseTime),t.minResponseTime=Math.min(t.minResponseTime,e.minResponseTime),t.rps+=parseFloat(e.rps),t.avgRps+=parseFloat(e.avgRps),t.avgTps+=parseFloat(e.avgTps),t.avgTpsAvg+=parseFloat(e.avgTpsAvg),a+=e.avgResponseTime*e.totalRequests,s+=e.p50ResponseTime*e.totalRequests,i+=e.p90ResponseTime*e.totalRequests,r+=e.p95ResponseTime*e.totalRequests,o+=e.p99ResponseTime*e.totalRequests}),t.totalRequests>0?(t.avgResponseTime=Math.round(a/t.totalRequests),t.p50ResponseTime=Math.round(s/t.totalRequests),t.p90ResponseTime=Math.round(i/t.totalRequests),t.p95ResponseTime=Math.round(r/t.totalRequests),t.p99ResponseTime=Math.round(o/t.totalRequests),t.errorRate=Number(t.failedRequests/t.totalRequests*100).toFixed(2)):t.errorRate="0.00",t.minResponseTime===1/0&&(t.minResponseTime=0),t.rps=Number(t.rps||0).toFixed(2),t.avgRps=Number(t.avgRps||0).toFixed(2),t.avgTps=Number(t.avgTps||0).toFixed(2),t.avgTpsAvg=Number(t.avgTpsAvg||0).toFixed(2),t},calculateRPS(){if(!this.reportData)return"0.00";if(this.realTimeData&&this.realTimeData.total&&"number"===typeof this.realTimeData.total.current_rps)return Number(this.realTimeData.total.current_rps).toFixed(2);const e=this.reportData.duration||1,t=this.reportData.totalRequests||0;return Number(t/e).toFixed(2)},calculateAvgRPS(){if(!this.reportData)return"0.00";const e=this.reportData.duration||1,t=this.reportData.totalRequests||0;return Number(t/e).toFixed(2)},generateReportSummary(){if(!this.reportData)return"暂无数据";const e=this.reportData.avgResponseTime||0,t=this.reportData.p50ResponseTime||0,a=this.reportData.p90ResponseTime||0,s=this.reportData.p95ResponseTime||0,i=this.reportData.p99ResponseTime||0,r=this.calculateRPS(),o=this.reportData.avgTps||0;return`平均响应时间为${e}ms；50%响应时间线的值为${t}ms；90%响应时间线的值为${a}ms；95%响应时间线的值为${s}ms；99%响应时间线的值为${i}ms；RPS为${r}；TPS为${o}`},formatTime(e){return e?new Date(e).toLocaleString():"--"},formatResponseTime(e){return e&&"number"===typeof e?e<1e3?`${Number(e).toFixed(2)}ms`:`${Number(e/1e3).toFixed(2)}s`:"0ms"},formatDuration(e){if(!e)return"0s";const t=Math.floor(e/3600),a=Math.floor(e%3600/60),s=e%60;return`${t}h ${a}m ${s}s`},formatBytes(e){if(!e||"number"!==typeof e||isNaN(e))return"0B";const t=1024,a=["B","KB","MB","GB"],s=Math.floor(Math.log(Math.max(1,e))/Math.log(t)),i=e/Math.pow(t,s);return parseFloat(Number(i).toFixed(2))+a[Math.min(s,a.length-1)]},getTaskTypeText(e){const t={10:"普通任务",20:"定时任务"};return t[e]||"未知"},getRunPatternText(e){const t={10:"并发模式",20:"阶梯模式"};return t[e]||"未知"},getDistributedModeText(e){const t={single:"单独模式",distributed:"集合模式"};return t[e]||"未知"},getStatusText(e){const t={1:"运行中",0:"已完成",99:"运行失败"};return t[e]||"未知"},getCpuLevelClass(e){return e>80?"metric-danger":e>60?"metric-warning":"metric-normal"},getMemoryLevelClass(e){return e>85?"metric-danger":e>70?"metric-warning":"metric-normal"},getErrorRateClass(e){return e>5?"metric-danger":e>1?"metric-warning":"metric-normal"},getLogClass(e){return`log-${e}`},async refreshMonitoringData(){await this.loadMonitoringData(),this.$message.success("监控数据已刷新")},toggleAutoRefresh(){if(this.autoRefresh){if("1"!==this.reportData.reportStatus)return this.$message.info("测试已完成，无需开启自动刷新"),void(this.autoRefresh=!1);this.refreshInterval=setInterval(()=>{this.loadMonitoringData(),this.loadTargetServiceData(),this.loadLogData()},1e4),this.initWebSocket()}else this.refreshInterval&&(clearInterval(this.refreshInterval),this.refreshInterval=null),this.cleanupWebSocket()},getWebSocketStatusText(){switch(this.wsConnectionStatus){case"connected":return"实时监控";case"connecting":return"连接中";case"disconnected":return"已断开";case"error":return"连接错误";default:return"未连接"}},getWebSocketStatusType(){switch(this.wsConnectionStatus){case"connected":return"success";case"connecting":return"warning";case"disconnected":return"info";case"error":return"danger";default:return"info"}},handleWebSocketStatusClick(){switch(this.wsConnectionStatus){case"connected":this.$message.success("WebSocket连接正常");break;case"connecting":this.$message.info("正在连接WebSocket...");break;case"disconnected":case"error":this.$confirm("WebSocket连接已断开，是否尝试重新连接？","连接提示",{confirmButtonText:"重新连接",cancelButtonText:"取消",type:"warning"}).then(()=>{this.reconnectWebSocket()}).catch(()=>{this.$message.info("已取消重连")});break;default:"1"===this.reportData.reportStatus?this.initWebSocket():this.$message.info("测试未运行，无需实时监控")}},startEditing(){this.inputDlg=!0,this.editingField="desc",this.$nextTick(()=>{this.$refs.input&&this.$refs.input.focus()})},editTaskName(){this.inputDlg=!0,this.editingField="taskName",this.tempTaskName=this.taskName,this.$nextTick(()=>{this.$refs.input&&this.$refs.input.focus()})},cancelEditing(){this.inputDlg=!1,"taskName"===this.editingField&&this.tempTaskName&&(this.taskName=this.tempTaskName)},async saveTaskName(){this.inputDlg=!1,this.taskName!==this.reportData.reportName&&this.saveReport()},async saveAnalysis(){this.inputDlg=!1,this.desc!==this.reportData.resultAnalyse&&this.saveReport()},handleSelect(e){this.activeIndex=e,"1"===e?this.reportData&&this.reportData.reportResult&&(this.generateChartData(),this.$nextTick(()=>{const e=this.$refs.table;e&&e.$forceUpdate(),this.$forceUpdate()})):"3"!==e||this.monitoringDataLoaded?"4"!==e||this.targetServiceDataLoaded?"5"===e&&(this.logDataLoaded?this.scrollToLogBottom():(this.loadLogData(),this.logDataLoaded=!0)):(this.loadTargetServiceData(),this.targetServiceDataLoaded=!0,"1"===this.reportData.reportStatus&&this.autoRefresh&&this.startTargetServiceRefresh()):(this.loadMonitoringData(),this.monitoringDataLoaded=!0,"1"===this.reportData.reportStatus&&this.autoRefresh&&this.startMonitoringRefresh()),"3"!==e&&this.stopMonitoringRefresh(),"4"!==e&&this.stopTargetServiceRefresh()},startMonitoringRefresh(){this.monitoringRefreshInterval&&clearInterval(this.monitoringRefreshInterval),this.monitoringRefreshInterval=setInterval(()=>{this.loadMonitoringData()},5e3)},stopMonitoringRefresh(){this.monitoringRefreshInterval&&(clearInterval(this.monitoringRefreshInterval),this.monitoringRefreshInterval=null)},startTargetServiceRefresh(){this.targetServiceRefreshInterval&&clearInterval(this.targetServiceRefreshInterval),this.targetServiceRefreshInterval=setInterval(()=>{this.loadTargetServiceData()},5e3)},stopTargetServiceRefresh(){this.targetServiceRefreshInterval&&(clearInterval(this.targetServiceRefreshInterval),this.targetServiceRefreshInterval=null)},back(){window.history.back()},async handleExportCommand(e){if(this.reportData.id)if("excel"===e){this.tableLoading=!0;try{let t,a=`性能测试报告_${this.reportData.reportName}_${(new Date).toISOString().slice(0,10)}`;switch(e){case"excel":t=await this.$api.exportSingleReport(this.reportData.id),a+=".xlsx";break;case"html":t=await this.mockGenerateHtmlReport(),a+=".html";break;case"pdf":t=await this.mockGeneratePdfReport(),a+=".pdf";break;case"raw":t=await this.$api.exportTestData({report_id:this.reportData.id,format:"json"}),a+=".json";break;default:return void this.$message.warning("未知的导出格式")}if(t&&200===t.status){const e=new Blob([t.data]),s=document.createElement("a");s.href=URL.createObjectURL(e),s.download=a,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(s.href),this.$message.success("报告导出成功")}}catch(t){console.error("导出失败:",t),this.$message.error("导出失败: "+(t.response?.data?.message||t.message||"未知错误"))}finally{this.tableLoading=!1}}else this.$message.info(("html"===e?"HTML报告":"pdf"===e?"PDF报告":"原始数据")+"导出功能即将上线，敬请期待！");else this.$message.warning("请先加载报告数据")},async mockGenerateHtmlReport(){return new Promise(e=>{setTimeout(()=>{const t=this.generateHtmlReportContent();e({status:200,data:t})},1e3)})},async mockGeneratePdfReport(){return new Promise(e=>{setTimeout(()=>{const t=`PDF报告内容 - ${this.reportData.reportName}`;e({status:200,data:t})},1500)})},async handleNotificationCommand(e){this.showNotificationDialog(e)},showNotificationDialog(){this.notificationDialogData={visible:!0,pushType:"wechat",name:`${this.reportData.reportName}性能测试报告通知`,webhook:"",recipients:[]}},cancelNotification(){this.notificationDialogData.visible=!1,this.$refs.notificationFormRef?.resetFields()},async confirmSendNotification(){this.$refs.notificationFormRef&&this.$refs.notificationFormRef.validate(async e=>{if(!e)return!1;this.notificationDialogData.visible=!1,await this.sendNotification(this.notificationDialogData.pushType)})},async sendNotification(e){this.$message.info("敬请期待！通知功能正在开发中...")},async mockSendNotification(e){return new Promise(e=>{setTimeout(()=>{e({status:200,data:{message:"通知发送成功"}})},1e3)})},generateNotificationContent(){const e=this.reportData,t=this.formatTime(e.endTime||e.createTime||new Date),a=this.getStatusText(e.reportStatus),s=this.getSuccessRate();let i=`## 性能测试报告: ${e.reportName}\n\n`;return i+=`**测试状态**: ${a}\n`,i+=`**完成时间**: ${t}\n\n`,i+="### 性能指标\n",i+=`- 平均响应时间: ${this.formatResponseTime(e.avgResponseTime)}\n`,i+=`- 平均TPS: ${(e.avgTps||0).toFixed(2)}\n`,i+=`- 成功率: ${s}%\n`,e.maxResponseTime&&(i+=`- 最大响应时间: ${this.formatResponseTime(e.maxResponseTime)}\n`),e.totalRequests&&(i+=`- 总请求数: ${e.totalRequests}\n`),e.duration&&(i+=`- 测试持续时间: ${e.duration}秒\n`),(e.maxUsers||e.avgUsers)&&(i+=`- 并发用户数: ${e.maxUsers||e.avgUsers}\n`),(e.avgCpu||e.avgMemory)&&(i+="\n### 资源使用情况\n",e.avgCpu&&(i+=`- 平均CPU使用率: ${e.avgCpu}%\n`),e.avgMemory&&(i+=`- 平均内存使用率: ${e.avgMemory}%\n`)),this.desc&&"暂无分析结果"!==this.desc&&(i+="\n### 分析结果\n",i+=this.desc.split("\n").map(e=>`> ${e}`).join("\n")),i},refreshGUI(){if(this.guiUrl){const e=document.querySelector(".iframe-container iframe");e&&(e.src=this.guiUrl),this.iframeError=!1,this.iframeErrorMessage=""}else this.$message.warning("没有可用的GUI URL")},handleIframeLoad(){this.iframeError=!1,this.iframeErrorMessage=""},handleIframeError(){this.iframeError=!0,this.iframeErrorMessage=`无法加载GUI界面：${this.guiUrl}，请检查服务器状态`},retryLoadGUI(){this.refreshGUI()},getTestStatusType(){return this.isTestRunning?"info":"0"===this.reportData.reportStatus?"success":"warning"},getTestStatusText(){return this.isTestRunning?"运行中":"0"===this.reportData.reportStatus?"已完成":"未运行"},getTestNotRunningMessage(){return"0"===this.reportData.reportStatus?"GUI界面只在测试运行时可用。此测试已完成，无法显示实时GUI界面。":"GUI界面需要在性能测试运行时才能访问。请先启动性能测试，然后返回查看GUI监控界面。"},async handleMoreCommand(e){try{switch(e){case"baseline":await this.createBaseline();break;case"compare":await this.compareWithBaseline();break;case"analyze":await this.analyzePerformance();break;case"config":this.showTestConfig();break;default:this.$message.warning("未知的操作类型")}}catch(t){console.error("操作失败:",t),this.$message.error("操作失败: "+(t.message||"未知错误"))}},async createBaseline(){try{this.baselineDialogData={visible:!0,loading:!1,mode:"create",form:{name:`${this.reportData.reportName}_基准线`,description:"基于当前报告创建的性能基准线",task_id:this.reportData.task?.id||this.reportData.taskId,avg_response_time:this.reportData.avgResponseTime||0,avg_tps:this.reportData.avgTps||0,success_rate:this.getSuccessRate()||0,avg_cpu:this.reportData.avgCpu||0,avg_memory:this.reportData.avgMemory||0,is_active:!0,project_id:this.reportData.project?.id||this.$route.params.projectId}}}catch(e){this.$message.error("准备创建基准线失败: "+(e.response?.data?.message||e.message||"未知错误"))}},async submitBaselineForm(){try{await this.$refs.baselineFormRef.validate(),this.baselineDialogData.loading=!0;const e={...this.baselineDialogData.form};"create"===this.baselineDialogData.mode&&(e.report_id=this.reportData.id);const t=await this.$api.createBaseline(e);200!==t.status&&201!==t.status||(this.$message.success("基准线创建成功"),this.baselineDialogData.visible=!1)}catch(e){this.$message.error("创建基准线失败: "+(e.response?.data?.message||e.message||"未知错误"))}finally{this.baselineDialogData.loading=!1}},async compareWithBaseline(){try{const e=await this.$api.getBaselines({project_id:this.reportData.project?.id||this.$route.params.projectId,task_id:this.reportData.task?.id||this.reportData.taskId,is_active:!0});if(200===e.status){let t=[];e.data.results&&Array.isArray(e.data.results)?t=e.data.results:Array.isArray(e.data)?t=e.data:e.data.baselines&&Array.isArray(e.data.baselines)&&(t=e.data.baselines),t.length>0?this.baselineCompareDialogData={visible:!0,loading:!1,baselineList:t,selectedBaselineId:t[0].id}:this.$message.warning("暂无可用的基准线，请先创建基准线")}}catch(e){this.$message.error("获取基准线列表失败: "+(e.response?.data?.message||e.message||"未知错误"))}},async submitCompareBaseline(){try{if(!this.baselineCompareDialogData.selectedBaselineId)return void this.$message.warning("请选择要对比的基准线");this.baselineCompareDialogData.loading=!0;try{const t=this.baselineCompareDialogData.baselineList.find(e=>e.id===this.baselineCompareDialogData.selectedBaselineId);let a={};if(t)a=t;else try{const e=await this.$api.getBaselineDetail(this.baselineCompareDialogData.selectedBaselineId);200===e.status&&(e.data.baseline?a=e.data.baseline:e.data&&(a=e.data))}catch(e){console.error("获取基准线详情失败:",e)}const s=await this.$api.compareWithBaseline(this.reportData.id,{baseline_id:this.baselineCompareDialogData.selectedBaselineId});if(200===s.status){this.baselineCompareDialogData.visible=!1;const e=s.data,t={avg_response_time:void 0!==a.response_time?this.safeParseFloat(a.response_time):this.safeParseFloat(a.avg_response_time),avg_tps:void 0!==a.tps?this.safeParseFloat(a.tps):this.safeParseFloat(a.avg_tps),success_rate:void 0!==a.error_rate?this.safeParseFloat(100-a.error_rate):this.safeParseFloat(a.success_rate),avg_cpu:this.safeParseFloat(a.cpu_usage||a.avg_cpu),avg_memory:this.safeParseFloat(a.memory_usage||a.avg_memory)};let i=null;e.baseline_metrics&&(i={avg_response_time:this.safeParseFloat(e.baseline_metrics.avg_response_time||e.baseline_metrics.response_time),avg_tps:this.safeParseFloat(e.baseline_metrics.avg_tps||e.baseline_metrics.tps),success_rate:void 0!==e.baseline_metrics.success_rate?this.safeParseFloat(e.baseline_metrics.success_rate):void 0!==e.baseline_metrics.error_rate?this.safeParseFloat(100-e.baseline_metrics.error_rate):0,avg_cpu:this.safeParseFloat(e.baseline_metrics.avg_cpu||e.baseline_metrics.cpu_usage),avg_memory:this.safeParseFloat(e.baseline_metrics.avg_memory||e.baseline_metrics.memory_usage)});const r=i||t;this.baselineCompareResultData={visible:!0,baseline_metrics:r,current_metrics:e.current_metrics||{avg_response_time:this.reportData.avgResponseTime||0,avg_tps:this.reportData.avgTps||0,success_rate:this.getSuccessRate()||0,avg_cpu:this.reportData.avgCpu||0,avg_memory:this.reportData.avgMemory||0},conclusion:e.conclusion||""}}}catch(t){console.error("API调用失败，使用模拟数据:",t),this.baselineCompareDialogData.visible=!1,this.baselineCompareResultData={visible:!0,baseline_metrics:{avg_response_time:180.5,avg_tps:85.75,success_rate:99.2,avg_cpu:45.3,avg_memory:60.8},current_metrics:{avg_response_time:this.reportData.avgResponseTime||200,avg_tps:this.reportData.avgTps||80,success_rate:this.getSuccessRate()||98,avg_cpu:this.reportData.avgCpu||50,avg_memory:this.reportData.avgMemory||65},conclusion:""}}}catch(t){this.$message.error("基准线对比失败: "+(t.response?.data?.message||t.message||"未知错误"))}finally{this.baselineCompareDialogData.loading=!1}},async analyzePerformance(){try{const e=this.generateRealAnalysis(this.reportData);this.$message.success("性能分析完成，请查看分析结果"),this.showAnalysisResults(e)}catch(e){this.$message.error("性能分析失败: "+(e.message||"未知错误"))}},generateRealAnalysis(e){let t=0;const a=[],s=[],i=this.safeParseFloat(e.avgResponseTime);if(i>0){let e=0;i<100?e=25:i<300?(e=20,s.push("当前响应时间表现良好，但仍有优化空间。考虑优化数据库查询和索引。")):i<500?(e=15,a.push({type:"响应时间",severity:"medium",description:`平均响应时间(${i.toFixed(2)}ms)偏高，建议优化数据库查询和应用代码`}),s.push("优化关键API响应时间，检查慢查询并优化数据库索引。")):(e=10,a.push({type:"响应时间",severity:"high",description:`平均响应时间(${i.toFixed(2)}ms)过高，严重影响用户体验`}),s.push("紧急优化响应时间：检查并优化数据库查询，考虑增加缓存层，优化应用代码。")),t+=e}const r=this.safeParseFloat(e.avgTps);if(r>0){const i=this.safeParseFloat(e.maxUsers||e.avgUsers),o=Math.max(i/3,1);let n=0;r>=1.5*o?(n=25,s.push("系统吞吐量表现优秀，可以考虑进一步增加并发用户数进行压测。")):r>=o?(n=20,s.push("系统吞吐量表现良好，符合预期。")):r>=.7*o?(n=15,a.push({type:"TPS",severity:"medium",description:`平均TPS(${r.toFixed(2)})低于预期，系统吞吐能力有待提高`}),s.push("提高系统吞吐量：优化关键服务代码，考虑使用异步处理非关键请求。")):(n=10,a.push({type:"TPS",severity:"high",description:`平均TPS(${r.toFixed(2)})远低于预期，系统吞吐能力严重不足`}),s.push("系统吞吐量亟需提升：检查系统瓶颈，考虑服务扩容或重构关键组件。")),t+=n}const o=this.safeParseFloat(e.errorRate);if(void 0!==o){let e=0;o<.1?e=25:o<1?e=20:o<5?(e=15,a.push({type:"错误率",severity:"medium",description:`错误率(${o.toFixed(2)}%)略高，影响系统稳定性`}),s.push("改善错误处理机制，增加服务异常监控，优化异常情况下的重试逻辑。")):(e=5,a.push({type:"错误率",severity:"high",description:`错误率(${o.toFixed(2)}%)过高，系统稳定性差`}),s.push("紧急修复高错误率问题：完善错误处理，增加容错机制，优化系统稳定性。")),t+=e}const n=this.safeParseFloat(e.avgCpu);if(n>0){let e=0;n<60?e=15:n<80?(e=10,a.push({type:"CPU使用率",severity:"low",description:`CPU使用率(${n.toFixed(2)}%)较高，但仍在可接受范围内`})):(e=5,a.push({type:"CPU使用率",severity:"high",description:`CPU使用率(${n.toFixed(2)}%)过高，可能成为系统瓶颈`}),s.push("降低CPU使用率：优化计算密集型代码，考虑增加服务器资源或负载均衡。")),t+=e}const l=this.safeParseFloat(e.avgMemory);if(l>0){let e=0;l<70?e=10:l<85?(e=5,a.push({type:"内存使用率",severity:"medium",description:`内存使用率(${l.toFixed(2)}%)较高，接近警戒线`}),s.push("优化内存使用：检查内存泄漏，优化大对象处理，考虑增加内存容量。")):(e=0,a.push({type:"内存使用率",severity:"high",description:`内存使用率(${l.toFixed(2)}%)过高，存在OOM风险`}),s.push("紧急处理内存问题：排查内存泄漏，优化大数据处理逻辑，增加服务器内存。")),t+=e}0===t&&(t=70),0===a.length&&a.push({type:"系统表现",severity:"low",description:"未发现明显性能瓶颈，系统整体表现良好"}),0===s.length&&(s.push("持续监控系统性能，定期进行压力测试以保持性能水平。"),s.push("考虑建立性能基准线，用于未来性能对比和评估。"));const c={response_time_trend:i<300?"良好":i<500?"需关注":"需优化",error_rate_trend:o<1?"稳定":o<5?"波动":"不稳定",tps_trend:r>0?r>50?"高":"中等":"低"};return e.maxUsers>0&&(o<1&&n<70&&l<70?s.push(`当前并发用户数(${e.maxUsers})下系统表现良好，可以考虑提高并发数进行更大规模测试。`):(o>5||n>85||l>85)&&s.push(`当前并发用户数(${e.maxUsers})已接近系统负载上限，建议优化系统后再增加并发。`)),{performance_score:Math.min(Math.round(t),100),bottlenecks:a,recommendations:s,trend_analysis:c}},showAnalysisResults(e){const{performance_score:t,bottlenecks:a,recommendations:s}=e;this.analysisDialogData={visible:!0,performance_score:t,bottlenecks:a,recommendations:s}},showTestConfig(){this.reportData.task||this.reportData.taskId?this.showTestConfigDialog():this.$message.warning("无法获取测试配置信息")},async showTestConfigDialog(){try{let e=this.reportData.task;this.configDialogData={visible:!0,taskConfig:e}}catch(e){console.error("获取配置失败:",e),this.$message.error("获取配置失败: "+(e.response?.data?.message||e.message||"未知错误"))}},getCurrentCpuUsage(){return this.monitoringData&&"number"===typeof this.monitoringData.cpu_percent?Number(this.monitoringData.cpu_percent).toFixed(1):this.reportData&&"number"===typeof this.reportData.avgCpu?Number(this.reportData.avgCpu).toFixed(1):"0.0"},getUserCountForRow(e){return"ALL"===e.method?this.getCurrentUserCount():e.currentUsers?e.currentUsers:this.getCurrentUserCount()},getCurrentUserCount(){if(this.realTimeData&&this.realTimeData.total&&this.realTimeData.total.current_users)return this.realTimeData.total.current_users;if(this.reportData.maxUsers)return this.reportData.maxUsers;if(this.reportData.avgUsers)return Math.round(this.reportData.avgUsers);if(this.reportData.task){if(this.reportData.task.concurrencyNumber)return this.reportData.task.concurrencyNumber;if(this.reportData.task.users)return this.reportData.task.users}return"-"},getCurrentMemoryUsage(){return this.monitoringData&&"number"===typeof this.monitoringData.memory_percent?Number(this.monitoringData.memory_percent).toFixed(1):this.reportData&&"number"===typeof this.reportData.avgMemory?Number(this.reportData.avgMemory).toFixed(1):"0.0"},getCorrectInterfaceName(e,t){if(t.name)return t.name;if(t.path)return t.path;const a=["GET","POST","PUT","DELETE","PATCH","HEAD","OPTIONS"];for(const s of a)if(e.startsWith(s+" "))return e.substring(s.length+1);return e},getCorrectHttpMethod(e,t){if(t.method)return t.method;const a=["GET","POST","PUT","DELETE","PATCH","HEAD","OPTIONS"];for(const s of a)if(e.startsWith(s+" "))return s;return"N/A"},isHttpMethod(e){const t=["GET","POST","PUT","DELETE","PATCH","HEAD","OPTIONS"];return t.includes(e?.toUpperCase())},extractInterfaceName(e,t){if(t.path)return t.path;const a=["GET","POST","PUT","DELETE","PATCH","HEAD","OPTIONS"];for(const s of a)if(e.startsWith(s+" "))return e.substring(s.length+1);return e},getScoreColor(e){return e>=80?"#67c23a":e>=60?"#e6a23c":"#f56c6c"},getSeverityType(e){const t={low:"success",medium:"warning",high:"danger"};return t[e]||"info"},async saveReport(){try{const e={reportName:this.taskName,resultAnalyse:this.desc},t=await this.$api.updateTaskReportDetail(this.reportData.id,e);200===t.status&&(this.$message.success("保存成功"),this.reportData.reportName=this.taskName,this.reportData.resultAnalyse=this.desc)}catch(e){console.error("保存失败:",e),this.$message.error("保存失败: "+(e.response?.data?.message||e.message||"未知错误"))}},handleRowClick(e,t,a){"selection"!==t.type&&(this.isFilterMode||this.$refs.table.toggleRowSelection(e))},handleSelectionChange(e){this.selectedRows=e},toggleFilterMode(){if(this.isFilterMode)this.isFilterMode=!1,this.$message.info("已显示全部数据，保留已选中的接口");else{const e=this.$refs.table.getSelectionRows?this.$refs.table.getSelectionRows():[];if(0===e.length)return void this.$message.warning("请先勾选需要查看的接口");this.selectedRows=e,this.isFilterMode=!0,this.$message.success(`已筛选 ${this.selectedRows.length} 个接口，图表和数据表已更新`)}},setMonitorIframeUrl(){this.monitorUrl&&/^https?:\/\//.test(this.monitorUrl)?this.monitorIframeUrl=this.monitorUrl:this.$message.warning("请输入以 http:// 或 https:// 开头的有效链接")},resetMonitorIframeUrl(){this.monitorUrl="",this.monitorIframeUrl=""},getComparisonClass(e,t,a){const s=parseFloat(e)||0,i=parseFloat(t)||0;if(0===i)return 0===s?"comparison-similar":["avg_tps","tps","success_rate"].includes(a)?"comparison-better":"comparison-worse";const r=["avg_tps","tps","success_rate"].includes(a),o=(s-i)/i*100;return r?o>=10?"comparison-better":o<=-10?"comparison-worse":"comparison-similar":o<=-10?"comparison-better":o>=10?"comparison-worse":"comparison-similar"},getComparisonText(e,t,a){const s=parseFloat(e)||0,i=parseFloat(t)||0;if(0===i)return 0===s?"0 (0%)":`+${s.toFixed(2)} (-)`;const r=s-i,o=(r/i*100).toFixed(1),n=r>=0?`+${r.toFixed(2)}`:r.toFixed(2),l=r>=0?`+${o}%`:`${o}%`;return`${n} (${l})`},generateComparisonConclusion(){const e=this.baselineCompareResultData;if(!e||!e.current_metrics||!e.baseline_metrics)return"无法生成对比结论，数据不完整。";const t=e.current_metrics,a=e.baseline_metrics;let s="性能对比分析：\n";if(void 0!==t.avg_response_time&&void 0!==a.avg_response_time&&a.avg_response_time>0){const e=(t.avg_response_time-a.avg_response_time)/a.avg_response_time*100;s+=e>10?`• 响应时间较基准线增加了${e.toFixed(1)}%，性能有所下降。\n`:e<-10?`• 响应时间较基准线减少了${Math.abs(e).toFixed(1)}%，性能有所提升。\n`:`• 响应时间与基准线相比基本持平，波动在${Math.abs(e).toFixed(1)}%范围内。\n`}else s+="• 响应时间数据不完整，无法进行比较。\n";if(void 0!==t.avg_tps&&void 0!==a.avg_tps&&a.avg_tps>0){const e=(t.avg_tps-a.avg_tps)/a.avg_tps*100;s+=e>10?`• TPS较基准线提高了${e.toFixed(1)}%，吞吐能力有所提升。\n`:e<-10?`• TPS较基准线下降了${Math.abs(e).toFixed(1)}%，吞吐能力有所下降。\n`:`• TPS与基准线相比基本持平，波动在${Math.abs(e).toFixed(1)}%范围内。\n`}else s+="• TPS数据不完整，无法进行比较。\n";if(void 0!==t.success_rate&&void 0!==a.success_rate){const e=t.success_rate-a.success_rate;s+=e>1?`• 成功率较基准线提高了${e.toFixed(1)}个百分点，系统稳定性有所提升。\n`:e<-1?`• 成功率较基准线下降了${Math.abs(e).toFixed(1)}个百分点，系统稳定性有所下降。\n`:"• 成功率与基准线相比基本持平，系统稳定性保持一致。\n"}else s+="• 成功率数据不完整，无法进行比较。\n";if(void 0!==t.avg_cpu&&void 0!==a.avg_cpu&&a.avg_cpu>0){const e=(t.avg_cpu-a.avg_cpu)/a.avg_cpu*100;s+=e>10?`• CPU使用率较基准线增加了${e.toFixed(1)}%，可能需要关注资源使用情况。\n`:e<-10?`• CPU使用率较基准线减少了${Math.abs(e).toFixed(1)}%，资源利用效率有所提升。\n`:"• CPU使用率与基准线相比基本持平。\n"}if(void 0!==t.avg_memory&&void 0!==a.avg_memory&&a.avg_memory>0){const e=(t.avg_memory-a.avg_memory)/a.avg_memory*100;s+=e>10?`• 内存使用率较基准线增加了${e.toFixed(1)}%，可能需要关注内存占用情况。\n`:e<-10?`• 内存使用率较基准线减少了${Math.abs(e).toFixed(1)}%，内存利用效率有所提升。\n`:"• 内存使用率与基准线相比基本持平。\n"}let i=[];if(void 0!==t.avg_response_time&&void 0!==a.avg_response_time&&a.avg_response_time>0){const e=(t.avg_response_time-a.avg_response_time)/a.avg_response_time*100;e>10&&i.push("响应时间增加")}if(void 0!==t.avg_tps&&void 0!==a.avg_tps&&a.avg_tps>0){const e=(t.avg_tps-a.avg_tps)/a.avg_tps*100;e<-10&&i.push("TPS下降")}if(void 0!==t.success_rate&&void 0!==a.success_rate){const e=t.success_rate-a.success_rate;e<-1&&i.push("成功率下降")}const r=i.length;return s+=0===r?"\n总体结论：当前性能测试结果与基准线相比表现良好，未发现明显性能退化。":1===r?`\n总体结论：当前性能测试结果与基准线相比存在${i[0]}的情况，建议关注上述性能变化。`:`\n总体结论：当前性能测试结果与基准线相比存在多项指标下降（${i.join("、")}），建议详细分析性能变化原因。`,s},async exportComparisonResult(){try{this.$message.info("正在准备导出对比报告...");this.reportData.id,this.baselineCompareDialogData.selectedBaselineId;setTimeout(()=>{this.$message.success("对比报告导出成功")},1500)}catch(e){this.$message.error("导出对比报告失败: "+(e.response?.data?.message||e.message||"未知错误"))}},safeParseFloat(e){if(void 0===e||null===e)return 0;if("null"===e||"undefined"===e||""===e)return 0;const t=parseFloat(e);return isNaN(t)||!isFinite(t)?0:t}},mounted(){(async()=>{if(this.reportId=this.$route.params.id||this.$route.query.id||this.$route.query.reportId,!this.reportId)return this.$message.error("报告ID不能为空，请从报告列表页面进入"),void this.$router.push({name:"PerformanceResult"});await this.loadReportData(),this.activeIndex="1"})()},beforeUnmount(){this.stopAllRealTimeUpdates()}};const ds=(0,cs.A)(ms,[["render",es],["__scopeId","data-v-ed76f3a4"]]);var gs=ds}}]);
//# sourceMappingURL=296.67de370e.js.map