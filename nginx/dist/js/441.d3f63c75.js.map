{"version": 3, "file": "js/441.d3f63c75.js", "mappings": "wMACOA,MAAM,uB,GAEJA,MAAM,kB,GACJA,MAAM,e,GAEJA,MAAM,qB,GAORA,MAAM,oB,GAkBRA,MAAM,kB,GAMIA,MAAM,e,GAKRC,IAAI,WAAWD,MAAM,mB,GAOnBA,MAAM,e,GAKRC,IAAI,gBAAgBD,MAAM,mB,GAUxBA,MAAM,e,GAKRC,IAAI,WAAWD,MAAM,mB,GAOnBA,MAAM,e,GAKRC,IAAI,cAAcD,MAAM,mB,GAOtBA,MAAM,e,GAKRC,IAAI,eAAeD,MAAM,mB,GAUvBA,MAAM,e,sWAtGvBE,EAAAA,EAAAA,IA4HM,MA5HNC,EA4HM,EA1HJC,EAAAA,EAAAA,IAyBM,MAzBNC,EAyBM,EAxBJD,EAAAA,EAAAA,IAQM,MARNE,EAQM,C,aAPJF,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAKM,MALNG,EAKM,EAJJC,EAAAA,EAAAA,IAGSC,EAAA,CAHAC,KAA2B,cAArBC,EAAAC,iBAAmC,UAAY,SAAUC,OAAO,Q,kBAC7E,IAAiC,EAAjCL,EAAAA,EAAAA,IAAiCM,EAAA,M,iBAAxB,IAAc,EAAdN,EAAAA,EAAAA,IAAcO,K,eAAU,KACjCC,EAAAA,EAAAA,IAAwB,cAArBL,EAAAC,iBAAmC,MAAQ,OAA3B,K,sBAIzBR,EAAAA,EAAAA,IAcM,MAdNa,EAcM,EAbJT,EAAAA,EAAAA,IAYkBU,EAAA,M,iBAXhB,IAMY,EANZV,EAAAA,EAAAA,IAMYW,EAAA,CALTT,KAAMC,EAAAS,aAAe,SAAW,UAChCC,QAAOC,EAAAC,iBACPC,QAASb,EAAAa,S,kBACV,IAA0E,EAA1EhB,EAAAA,EAAAA,IAA0EM,EAAA,M,iBAAjE,IAAkC,CAAhBH,EAAAS,e,WAAgBK,EAAAA,EAAAA,IAAqBC,EAAA,CAAAC,IAAA,O,WAAvDF,EAAAA,EAAAA,IAAkCG,EAAA,CAAAD,IAAA,O,eAA+B,KAC1EX,EAAAA,EAAAA,IAAGL,EAAAS,aAAe,OAAS,QAAZ,K,sCAEjBZ,EAAAA,EAAAA,IAGYW,EAAA,CAHAE,QAAOC,EAAAO,aAAW,C,iBAC5B,IAA8B,EAA9BrB,EAAAA,EAAAA,IAA8BM,EAAA,M,iBAArB,IAAW,EAAXN,EAAAA,EAAAA,IAAWsB,K,2BAAU,W,wCAQtC1B,EAAAA,EAAAA,IA6FM,MA7FN2B,EA6FM,EA3FJvB,EAAAA,EAAAA,IAwBSwB,EAAA,CAxBAC,OAAQ,IAAE,C,iBACjB,IAUS,EAVTzB,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHNmC,EAGM,C,aAFJnC,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZI,EAAAA,EAAAA,IAA2DC,EAAA,CAAnD+B,KAAK,SAAO,C,iBAAC,IAA6B,E,iBAA1B7B,EAAA8B,YAAYC,QAAU,GAAJ,K,2BAG9C,IAAkD,EAAlDtC,EAAAA,EAAAA,IAAkD,MAAlDuC,EAAkD,Y,eAItDnC,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHNwC,EAGM,C,aAFJxC,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVI,EAAAA,EAAAA,IAAsEC,EAAA,CAA9D+B,KAAK,SAAO,C,iBAAC,IAAsC,E,iBAAnC7B,EAAA8B,YAAYI,iBAAmB,GAAI,KAAE,K,2BAGjE,IAAuD,EAAvDzC,EAAAA,EAAAA,IAAuD,MAAvD0C,EAAuD,Y,uBAM7DtC,EAAAA,EAAAA,IAoCSwB,EAAA,CApCAC,OAAQ,GAAIc,MAAA,uB,kBACnB,IAUS,EAVTvC,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHN4C,EAGM,C,aAFJ5C,EAAAA,EAAAA,IAAoB,YAAd,WAAO,KACbI,EAAAA,EAAAA,IAAgFC,EAAA,CAAxE+B,KAAK,SAAO,C,iBAAC,IAAiD,E,iBAA9C7B,EAAAsC,cAAcC,KAAKC,SAASC,QAAQ,IAAM,GAAI,IAAC,K,2BAG3E,IAAkD,EAAlDhD,EAAAA,EAAAA,IAAkD,MAAlDiD,EAAkD,Y,eAItD7C,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHNkD,EAGM,C,aAFJlD,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXI,EAAAA,EAAAA,IAAmFC,EAAA,CAA3E+B,KAAK,SAAO,C,iBAAC,IAAoD,E,iBAAjD7B,EAAAsC,cAAcM,QAAQJ,SAASC,QAAQ,IAAM,GAAI,IAAC,K,2BAG9E,IAAqD,EAArDhD,EAAAA,EAAAA,IAAqD,MAArDoD,EAAqD,Y,eAIzDhD,EAAAA,EAAAA,IAUS0B,EAAA,CAVAC,KAAM,GAAC,C,iBACd,IAQU,EARV3B,EAAAA,EAAAA,IAQU4B,EAAA,CARDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHNqD,EAGM,C,aAFJrD,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVI,EAAAA,EAAAA,IAAuFC,EAAA,CAA/E+B,KAAK,SAAO,C,iBAAC,IAAyD,E,iBAAtDlB,EAAAoC,YAAY/C,EAAAsC,cAAcU,SAASC,YAAc,IAAJ,K,2BAGzE,IAAsD,EAAtDxD,EAAAA,EAAAA,IAAsD,MAAtDyD,EAAsD,Y,uBAM5DrD,EAAAA,EAAAA,IAwBSwB,EAAA,CAxBDe,MAAA,uBAAyB,C,iBAC/B,IAsBS,EAtBTvC,EAAAA,EAAAA,IAsBS0B,EAAA,CAtBAC,KAAM,IAAE,C,iBACf,IAoBU,EApBV3B,EAAAA,EAAAA,IAoBU4B,EAAA,CApBDpC,MAAM,gBAAc,CAChBqC,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNlC,EAAAA,EAAAA,IAGM,MAHN0D,EAGM,C,aAFJ1D,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVI,EAAAA,EAAAA,IAA2DW,EAAA,CAAhDqB,KAAK,QAASnB,QAAOC,EAAAyC,W,kBAAW,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,gDAG/C,IAYW,EAZXxD,EAAAA,EAAAA,IAYWyD,EAAA,CAZAC,KAAMvD,EAAAwD,WAAWC,OAAO,IAAK5B,KAAK,QAAQ6B,OAAA,I,kBACnD,IAIkB,EAJlB7D,EAAAA,EAAAA,IAIkB8D,EAAA,CAJDC,KAAK,YAAYC,MAAM,KAAKC,MAAM,O,CACtCC,SAAOpC,EAAAA,EAAAA,IAChB,EADoBqC,SAAG,E,qBAChBC,KAAKD,EAAIE,WAAWC,sBAAkB,K,OAGjDtE,EAAAA,EAAAA,IAAyD8D,EAAA,CAAxCC,KAAK,SAASC,MAAM,MAAMC,MAAM,SACjDjE,EAAAA,EAAAA,IAAuE8D,EAAA,CAAtDC,KAAK,kBAAkBC,MAAM,WAAWC,MAAM,SAC/DjE,EAAAA,EAAAA,IAAiE8D,EAAA,CAAhDC,KAAK,gBAAgBC,MAAM,OAAOC,MAAM,SACzDjE,EAAAA,EAAAA,IAA+D8D,EAAA,CAA9CC,KAAK,YAAYC,MAAM,SAASC,MAAM,SACvDjE,EAAAA,EAAAA,IAAgE8D,EAAA,CAA/CC,KAAK,aAAaC,MAAM,SAASC,MAAM,SACxDjE,EAAAA,EAAAA,IAAkE8D,EAAA,CAAjDC,KAAK,gBAAgBC,MAAM,QAAQC,MAAM,U,gIAiBxE,GACEM,KAAM,qBACNC,WAAY,CACVC,WAAU,aAAEC,UAAS,YAAEC,WAAU,aAAEC,QAAOA,EAAAA,SAE5ClB,IAAAA,GACE,MAAO,CACLmB,UAAW,KACXzE,iBAAkB,eAClBQ,cAAc,EACdI,SAAS,EACTiB,YAAa,CAAC,EACdQ,cAAe,CAAC,EAChBkB,WAAY,GACZmB,OAAQ,CAAC,EACTC,cAAe,GAEfC,kBAAmB,EACnBC,qBAAsB,EACtBC,kBAAmB,IACnBC,eAAgB,KAChBC,eAAgB,KAChBC,kBAAmB,IACnBC,sBAAsB,EACtBC,uBAAwB,CAAC,IAAM,IAAM,IAAM,IAAO,KAEtD,EACAC,SAAU,KACLC,EAAAA,EAAAA,IAAS,CACVC,cAAeC,GAASA,EAAMD,qBAAiB,GAAKE,OAAOC,OAAOC,UAGtEC,OAAAA,GACEC,KAAKC,aACLD,KAAKE,mBAGLC,SAASC,iBAAiB,mBAAoBJ,KAAKK,wBAGnDC,OAAOF,iBAAiB,QAASJ,KAAKO,mBACtCD,OAAOF,iBAAiB,OAAQJ,KAAKQ,iBACvC,EAEAC,aAAAA,GAEET,KAAKV,sBAAuB,EAG5BU,KAAKU,mBAGLV,KAAKW,iBAGLX,KAAKY,gBAGLT,SAASU,oBAAoB,mBAAoBb,KAAKK,wBACtDC,OAAOO,oBAAoB,QAASb,KAAKO,mBACzCD,OAAOO,oBAAoB,OAAQb,KAAKQ,iBAC1C,EAGAM,SAAAA,GACgC,iBAA1Bd,KAAK5F,kBAAwC4F,KAAKV,sBACpDU,KAAKE,kBAET,EAEAa,WAAAA,GAEEf,KAAKgB,iBACP,EACAC,QAAS,CACPhB,UAAAA,GACED,KAAKkB,UAAU,KACblB,KAAKmB,eACLnB,KAAKoB,oBACLpB,KAAKqB,eACLrB,KAAKsB,kBACLtB,KAAKuB,oBAET,EAEAJ,YAAAA,GACE,MAAMK,EAAWxB,KAAKyB,MAAMC,SAC5B,IAAKF,EAAU,OAEfxB,KAAKlB,OAAO6C,IAAMC,EAAAA,GAAaJ,GAC/B,MAAMK,EAAS,CACbC,MAAO,CAAEC,KAAM,SAAUC,KAAM,UAC/BC,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,OAC9B8D,OAAQ,CAAC,CACP9D,KAAM,MACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,WACpBC,UAAW,CAAED,MAAO,8BAGxBxC,KAAKlB,OAAO6C,IAAIe,UAAUb,EAC5B,EAEAT,iBAAAA,GACE,MAAMI,EAAWxB,KAAKyB,MAAMkB,cAC5B,IAAKnB,EAAU,OAEfxB,KAAKlB,OAAO8D,SAAWhB,EAAAA,GAAaJ,GACpC,MAAMK,EAAS,CACbC,MAAO,CAAEC,KAAM,OAAQC,KAAM,UAC7BC,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,MAC9B8D,OAAQ,CAAC,CACP9D,KAAM,SACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,cAGxBxC,KAAKlB,OAAO8D,SAASF,UAAUb,EACjC,EAEAR,YAAAA,GACE,MAAMG,EAAWxB,KAAKyB,MAAMoB,SAC5B,IAAKrB,EAAU,OAEfxB,KAAKlB,OAAOpC,IAAMkF,EAAAA,GAAaJ,GAC/B,MAAMK,EAAS,CACbI,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,IAAKuE,IAAK,KACxCT,OAAQ,CAAC,CACP9D,KAAM,MACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,WACpBC,UAAW,CAAED,MAAO,8BAGxBxC,KAAKlB,OAAOpC,IAAIgG,UAAUb,EAC5B,EAEAP,eAAAA,GACE,MAAME,EAAWxB,KAAKyB,MAAMsB,YAC5B,IAAKvB,EAAU,OAEfxB,KAAKlB,OAAO/B,OAAS6E,EAAAA,GAAaJ,GAClC,MAAMK,EAAS,CACbI,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,IAAKuE,IAAK,KACxCT,OAAQ,CAAC,CACP9D,KAAM,KACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,WACpBC,UAAW,CAAED,MAAO,+BAGxBxC,KAAKlB,OAAO/B,OAAO2F,UAAUb,EAC/B,EAEAN,gBAAAA,GACE,MAAMC,EAAWxB,KAAKyB,MAAMuB,aAC5B,IAAKxB,EAAU,OAEfxB,KAAKlB,OAAO3B,QAAUyE,EAAAA,GAAaJ,GACnC,MAAMK,EAAS,CACbI,QAAS,CAAEC,QAAS,QACpBC,MAAO,CAAEjI,KAAM,WAAYwD,KAAM,IACjC0E,MAAO,CAAElI,KAAM,QAASqE,KAAM,MAC9B8D,OAAQ,CACN,CACE9D,KAAM,KACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,YAEtB,CACEjE,KAAM,KACNrE,KAAM,OACNoI,QAAQ,EACR5E,KAAM,GACN6E,UAAW,CAAEC,MAAO,UAAWtI,KAAM,aAI3C8F,KAAKlB,OAAO3B,QAAQuF,UAAUb,EAChC,EAEA3B,gBAAAA,GAEE,GAAIF,KAAKV,qBAAsB,OAE/B,IAAKU,KAAKN,cAER,YADAuD,EAAAA,GAAUC,MAAM,WAKlBlD,KAAKU,mBAEL,MAAMyC,EAA0C,WAA7B7C,OAAO8C,SAASC,SAAwB,OAAS,MAC9DC,EAAQ,GAAGH,MAAe7C,OAAO8C,SAASG,+BAA+BvD,KAAKN,iBAEpF,IACEM,KAAKnB,UAAY,IAAI2E,UAAUF,GAE/BtD,KAAKnB,UAAU4E,OAASzD,KAAK0D,oBAC7B1D,KAAKnB,UAAU8E,UAAY3D,KAAK4D,uBAChC5D,KAAKnB,UAAUgF,QAAU7D,KAAK8D,qBAC9B9D,KAAKnB,UAAUkF,QAAU/D,KAAKgE,qBAG9BhE,KAAKiE,kBAAoBC,WAAW,KAC9BlE,KAAKnB,WAAamB,KAAKnB,UAAUsF,aAAeX,UAAUY,aAC5DpE,KAAKnB,UAAUwF,QACfrE,KAAKsE,4BAEN,IAEL,CAAE,MAAOpB,GACPqB,QAAQrB,MAAM,mBAAoBA,GAClClD,KAAKwE,sBAAsBtB,EAC7B,CACF,EAEAQ,mBAAAA,GACM1D,KAAKV,uBAETU,KAAK5F,iBAAmB,YACxB4F,KAAKhB,kBAAoB,EACzBgB,KAAKyE,sBAELxB,EAAAA,GAAUyB,QAAQ,iBAGd1E,KAAKiE,oBACPU,aAAa3E,KAAKiE,mBAClBjE,KAAKiE,kBAAoB,MAI3BjE,KAAK4E,iBAGL5E,KAAK6E,mBACP,EAEAjB,sBAAAA,CAAuBkB,GACrB,IAAI9E,KAAKV,qBAET,IACE,MAAM5B,EAAOqH,KAAKC,MAAMF,EAAMpH,MAC9BsC,KAAKiF,wBAAwBvH,EAC/B,CAAE,MAAOwF,GACPqB,QAAQrB,MAAM,mBAAoBA,EACpC,CACF,EAEAY,oBAAAA,CAAqBgB,GACf9E,KAAKV,uBAETU,KAAK5F,iBAAmB,eACxB4F,KAAKpF,cAAe,EACpBoF,KAAKkF,gBAGDlF,KAAKiE,oBACPU,aAAa3E,KAAKiE,mBAClBjE,KAAKiE,kBAAoB,MAG3BM,QAAQY,IAAI,iBAAkBL,EAAMM,KAAMN,EAAMO,QAG7B,MAAfP,EAAMM,MAAgC,OAAfN,EAAMM,OAC/BnC,EAAAA,GAAUqC,QAAQ,iBAClBtF,KAAKuF,oBAET,EAEAvB,oBAAAA,CAAqBd,GACflD,KAAKV,uBAETiF,QAAQrB,MAAM,eAAgBA,GAC9BlD,KAAK5F,iBAAmB,eAGpB4F,KAAKiE,oBACPU,aAAa3E,KAAKiE,mBAClBjE,KAAKiE,kBAAoB,MAE7B,EAEAK,uBAAAA,GACEC,QAAQiB,KAAK,iBACbvC,EAAAA,GAAUC,MAAM,iBAChBlD,KAAK5F,iBAAmB,eACxB4F,KAAKuF,kBACP,EAEAf,qBAAAA,CAAsBtB,GACpBqB,QAAQrB,MAAM,iBAAkBA,GAChCD,EAAAA,GAAUC,MAAM,iBAChBlD,KAAK5F,iBAAmB,eACxB4F,KAAKuF,kBACP,EAEAN,uBAAAA,CAAwBvH,GAEtB,GAAkB,SAAdA,EAAKxD,KAKT,OAAQwD,EAAKxD,MACX,IAAK,yBACHqK,QAAQY,IAAI,QAASzH,EAAK+H,SAC1B,MACF,IAAK,iBACHzF,KAAK0F,kBAAkBhI,EAAKA,MAC5B,MACF,IAAK,qBACHsC,KAAK2F,sBAAsBjI,EAAKA,MAChC,MACF,IAAK,eACHuF,EAAAA,GAAUyB,QAAQ,WAClB,MACF,IAAK,iBACHzB,EAAAA,GAAUyB,QAAQ,WAClB1E,KAAKpF,cAAe,EACpB,MACF,IAAK,cACHqI,EAAAA,GAAUC,MAAM,WAAaxF,EAAKwF,OAClClD,KAAKpF,cAAe,EACpB,MACF,IAAK,QACHqI,EAAAA,GAAUC,MAAMxF,EAAK+H,SACrB,MACF,QACElB,QAAQiB,KAAK,oBAAqB9H,EAAKxD,WA7BzC8F,KAAK4F,aAAexH,KAAKyH,KA+B7B,EAEAN,gBAAAA,GACE,GAAIvF,KAAKV,sBAAwBU,KAAKhB,mBAAqBgB,KAAKf,qBAI9D,YAHIe,KAAKhB,mBAAqBgB,KAAKf,sBACjCgE,EAAAA,GAAUC,MAAM,4BAKpBlD,KAAKyE,sBAGL,MAAMqB,EAAeC,KAAKC,IAAIhG,KAAKhB,kBAAmBgB,KAAKT,uBAAuB0G,OAAS,GACrFC,EAAQlG,KAAKT,uBAAuBuG,GAE1CvB,QAAQY,IAAI,OAAOnF,KAAKhB,kBAAoB,YAAYkH,OAExDlG,KAAKb,eAAiB+E,WAAW,KAC1BlE,KAAKV,uBACRU,KAAKhB,oBACLgB,KAAKE,qBAENgG,EACL,EAEAtB,cAAAA,GACE5E,KAAKkF,gBAELlF,KAAKZ,eAAiB+G,YAAY,KAC5BnG,KAAKnB,WAAamB,KAAKnB,UAAUsF,aAAeX,UAAU4C,MAC5DpG,KAAKnB,UAAUwH,KAAKtB,KAAKuB,UAAU,CAAEpM,KAAM,WAE5C8F,KAAKX,kBACV,EAEA6F,aAAAA,GACMlF,KAAKZ,iBACPmH,cAAcvG,KAAKZ,gBACnBY,KAAKZ,eAAiB,KAE1B,EAEAqF,mBAAAA,GACMzE,KAAKb,iBACPwF,aAAa3E,KAAKb,gBAClBa,KAAKb,eAAiB,KAE1B,EAEAwB,cAAAA,GACEX,KAAKyE,sBACLzE,KAAKkF,gBAEDlF,KAAKiE,oBACPU,aAAa3E,KAAKiE,mBAClBjE,KAAKiE,kBAAoB,KAE7B,EAEAvD,gBAAAA,GACMV,KAAKnB,YAEPmB,KAAKnB,UAAU4E,OAAS,KACxBzD,KAAKnB,UAAU8E,UAAY,KAC3B3D,KAAKnB,UAAUgF,QAAU,KACzB7D,KAAKnB,UAAUkF,QAAU,KAGrB/D,KAAKnB,UAAUsF,aAAeX,UAAU4C,MACxCpG,KAAKnB,UAAUsF,aAAeX,UAAUY,YAC1CpE,KAAKnB,UAAUwF,MAAM,IAAM,qBAG7BrE,KAAKnB,UAAY,MAGnBmB,KAAKkF,gBACLlF,KAAKyE,qBACP,EAEAzD,eAAAA,GAEMhB,KAAKnB,WACPmB,KAAKnB,UAAUwF,MAAM,IAAM,yBAE7BrE,KAAKkF,gBACLlF,KAAKyE,qBACP,EAGApE,sBAAAA,GACML,KAAKV,uBAELa,SAASqG,OAEXxG,KAAKgB,kBAGyB,iBAA1BhB,KAAK5F,mBACP4F,KAAKhB,kBAAoB,EACzBgB,KAAKE,oBAGX,EAGAK,iBAAAA,GACMP,KAAKV,sBAEqB,iBAA1BU,KAAK5F,mBACP4F,KAAKhB,kBAAoB,EACzBgB,KAAKE,mBAET,EAEAM,gBAAAA,GAGE+D,QAAQY,IAAI,SACd,EAEAsB,mBAAAA,GAEEzG,KAAKU,kBACP,EAEAgF,iBAAAA,CAAkBhI,GAChB,IAAKA,GAAQsC,KAAKV,qBAAsB,OAGxCU,KAAK/D,YAAc,IAAKyB,GAGxB,MAAMW,GAAY,IAAID,MAAOsI,cACvBC,EAAe,CACnBtI,YACAnC,OAAQwB,EAAKkJ,SAAW,EACxBvK,gBAAiBqB,EAAKrB,iBAAmB,EACzCwK,cAAenJ,EAAKoJ,gBAAkB,EACtCC,UAAWrJ,EAAKsJ,YAAc,EAC9BC,WAAYjH,KAAKvD,cAAcC,KAAKC,SAAW,EAC/CuK,cAAelH,KAAKvD,cAAcM,QAAQJ,SAAW,EACrDwK,YAAanH,KAAKvD,cAAcU,SAASC,YAAc,EACvDgK,YAAapH,KAAKvD,cAAcU,SAASkK,YAAc,GAIzDrH,KAAKsH,aAAaX,GAGlB3G,KAAKuH,qBACP,EAEA5B,qBAAAA,CAAsBjI,GACfA,IAAQsC,KAAKV,uBAEA,qBAAd5B,EAAKxD,KAEP8F,KAAKvD,cAAgB,IAAKiB,EAAKA,MAG/BsC,KAAK/D,YAAc,IAAK+D,KAAK/D,eAAgByB,GAG/CsC,KAAKuH,sBACP,EAEAD,YAAAA,CAAaE,GAIX,GAHAxH,KAAKrC,WAAW8J,KAAKD,GAGjBxH,KAAKrC,WAAWsI,OAASjG,KAAKjB,cAAe,CAC/C,MAAM2I,EAAa1H,KAAKrC,WAAWsI,OAASjG,KAAKjB,cACjDiB,KAAKrC,WAAWgK,OAAO,EAAGD,EAC5B,CACF,EAEAH,mBAAAA,GAEMvH,KAAK4H,kBACPjD,aAAa3E,KAAK4H,kBAGpB5H,KAAK4H,iBAAmB1D,WAAW,KAC5BlE,KAAKV,sBACRU,KAAK6H,gBAEN,IACL,EAEAA,YAAAA,GACE,GAAI7H,KAAKV,uBAAyBU,KAAKrC,WAAWsI,OAAQ,OAG1D,MAAM6B,EAAQ9H,KAAKrC,WAAWoK,IAAIC,GAAK,IAAI5J,KAAK4J,EAAE3J,WAAWC,sBAG7D0B,KAAKiI,oBAAoBH,EAC3B,EAEAG,mBAAAA,CAAoBH,GAClB,MAAMI,EAAe,CACnB,CACEC,MAAOnI,KAAKlB,OAAO6C,IACnBjE,KAAMsC,KAAKrC,WAAWoK,IAAIC,GAAKA,EAAE9L,SAEnC,CACEiM,MAAOnI,KAAKlB,OAAO8D,SACnBlF,KAAMsC,KAAKrC,WAAWoK,IAAIC,GAAKA,EAAE3L,kBAEnC,CACE8L,MAAOnI,KAAKlB,OAAOpC,IACnBgB,KAAMsC,KAAKrC,WAAWoK,IAAIC,GAAKA,EAAEf,aAEnC,CACEkB,MAAOnI,KAAKlB,OAAO/B,OACnBW,KAAMsC,KAAKrC,WAAWoK,IAAIC,GAAKA,EAAEd,iBAKrCgB,EAAaE,QAAQ,EAAGD,QAAOzK,WAC7B,GAAIyK,GAASA,EAAMzF,UACjB,IACEyF,EAAMzF,UAAU,CACdP,MAAO,CAAEzE,KAAMoK,GACfzF,OAAQ,CAAC,CAAE3E,KAAMA,MAChB,GAAO,EACZ,CAAE,MAAOwF,GACPqB,QAAQiB,KAAK,UAAWtC,EAC1B,IAKJlD,KAAKqI,oBACP,EAGAA,kBAAAA,GACE,IAAKrI,KAAKlB,OAAO3B,QAAS,OAE1B,MAAM2K,EAAQ9H,KAAKrC,WAAWoK,IAAIC,GAAK,IAAI5J,KAAK4J,EAAE3J,WAAWC,sBACvDgK,EAAkBtI,KAAKrC,WAAWoK,IAAIC,IAC1C,MAAMO,EAAQP,EAAEb,aAAenH,KAAKvD,cAAcU,SAASC,YAAc,EACzE,OAAQmL,EAAQ,KAAO,MAAM3L,QAAQ,KAEjC4L,EAAkBxI,KAAKrC,WAAWoK,IAAIC,IAC1C,MAAMO,EAAQP,EAAEZ,aAAepH,KAAKvD,cAAcU,SAASkK,YAAc,EACzE,OAAQkB,EAAQ,KAAO,MAAM3L,QAAQ,KAGvCoD,KAAKlB,OAAO3B,QAAQuF,UAAU,CAC5BP,MAAO,CAAEzE,KAAMoK,GACfzF,OAAQ,CACN,CAAE3E,KAAM4K,GACR,CAAE5K,KAAM8K,KAGd,EAEAzN,gBAAAA,GACE,GAA8B,cAA1BiF,KAAK5F,iBAEP,YADA6I,EAAAA,GAAUC,MAAM,gBAIlBlD,KAAKhF,SAAU,EACf,MAAMyN,EAASzI,KAAKpF,aAAe,kBAAoB,mBAEvDoF,KAAKnB,UAAUwH,KAAKtB,KAAKuB,UAAU,CAAEpM,KAAMuO,KAE3CvE,WAAW,KACTlE,KAAKpF,cAAgBoF,KAAKpF,aAC1BoF,KAAKhF,SAAU,GACd,IACL,EAEA6J,gBAAAA,GACgC,cAA1B7E,KAAK5F,kBACP4F,KAAKnB,UAAUwH,KAAKtB,KAAKuB,UAAU,CAAEpM,KAAM,uBAE/C,EAEAmB,WAAAA,GACE2E,KAAK6E,mBACL5B,EAAAA,GAAUyB,QAAQ,QACpB,EAEAnH,SAAAA,GAEEyC,KAAKrC,WAAWsI,OAAS,EACzBjG,KAAK/D,YAAc,CAAC,EACpB+D,KAAKvD,cAAgB,CAAC,EAGtBuD,KAAK6H,eACL5E,EAAAA,GAAUyB,QAAQ,QACpB,EAEAxH,WAAAA,CAAYqL,GACV,GAAc,IAAVA,EAAa,MAAO,MACxB,MAAMG,EAAI,KACJC,EAAQ,CAAC,IAAK,KAAM,KAAM,MAC1BC,EAAI7C,KAAK8C,MAAM9C,KAAKZ,IAAIoD,GAASxC,KAAKZ,IAAIuD,IAChD,OAAOI,YAAYP,EAAQxC,KAAKgD,IAAIL,EAAGE,IAAIhM,QAAQ,IAAM,IAAM+L,EAAMC,EACvE,EAEAhI,aAAAA,GAEMZ,KAAK4H,mBACPjD,aAAa3E,KAAK4H,kBAClB5H,KAAK4H,iBAAmB,MAI1BoB,OAAOC,KAAKjJ,KAAKlB,QAAQsJ,QAAQjN,IAC/B,MAAMgN,EAAQnI,KAAKlB,OAAO3D,GAC1B,GAAIgN,GAAkC,oBAAlBA,EAAMe,QACxB,IACEf,EAAMe,SACR,CAAE,MAAOhG,GACPqB,QAAQiB,KAAK,QAAQrK,SAAY+H,EACnC,IAKJlD,KAAKlB,OAAS,CAAC,CACjB,I,WC3yBJ,MAAMqK,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceMonitor.vue", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceMonitor.vue?a92e"], "sourcesContent": ["<template>\n  <div class=\"performance-monitor\">\n    <!-- 顶部状态区域 -->\n    <div class=\"monitor-header\">\n      <div class=\"header-info\">\n        <h2>性能实时监控</h2>\n        <div class=\"connection-status\">\n          <el-tag :type=\"connectionStatus === 'connected' ? 'success' : 'danger'\" effect=\"dark\">\n            <el-icon><Connection /></el-icon>\n            {{ connectionStatus === 'connected' ? '已连接' : '未连接' }}\n          </el-tag>\n        </div>\n      </div>\n      <div class=\"monitor-controls\">\n        <el-button-group>\n          <el-button \n            :type=\"isMonitoring ? 'danger' : 'primary'\" \n            @click=\"toggleMonitoring\"\n            :loading=\"loading\">\n            <el-icon><VideoPlay v-if=\"!isMonitoring\" /><VideoPause v-else /></el-icon>\n            {{ isMonitoring ? '停止监控' : '开始监控' }}\n          </el-button>\n          <el-button @click=\"refreshData\">\n            <el-icon><Refresh /></el-icon>\n            刷新\n          </el-button>\n        </el-button-group>\n      </div>\n    </div>\n\n    <!-- 监控面板 -->\n    <div class=\"monitor-panels\">\n      <!-- 性能指标面板 -->\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-card class=\"monitor-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>TPS 趋势</span>\n                <el-tag size=\"small\">{{ currentData.avgTps || 0 }}</el-tag>\n              </div>\n            </template>\n            <div ref=\"tpsChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-card class=\"monitor-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>响应时间</span>\n                <el-tag size=\"small\">{{ currentData.avgResponseTime || 0 }}ms</el-tag>\n              </div>\n            </template>\n            <div ref=\"responseChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 系统资源面板 -->\n      <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n        <el-col :span=\"8\">\n          <el-card class=\"monitor-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>CPU 使用率</span>\n                <el-tag size=\"small\">{{ currentSystem.cpu?.percent?.toFixed(1) || 0 }}%</el-tag>\n              </div>\n            </template>\n            <div ref=\"cpuChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"8\">\n          <el-card class=\"monitor-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>内存使用率</span>\n                <el-tag size=\"small\">{{ currentSystem.memory?.percent?.toFixed(1) || 0 }}%</el-tag>\n              </div>\n            </template>\n            <div ref=\"memoryChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"8\">\n          <el-card class=\"monitor-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>网络流量</span>\n                <el-tag size=\"small\">{{ formatBytes(currentSystem.network?.bytes_recv || 0) }}</el-tag>\n              </div>\n            </template>\n            <div ref=\"networkChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 详细数据表格 -->\n      <el-row style=\"margin-top: 20px;\">\n        <el-col :span=\"24\">\n          <el-card class=\"monitor-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>实时数据</span>\n                <el-button size=\"small\" @click=\"clearData\">清空数据</el-button>\n              </div>\n            </template>\n            <el-table :data=\"recentData.slice(-10)\" size=\"small\" stripe>\n              <el-table-column prop=\"timestamp\" label=\"时间\" width=\"200\">\n                <template #default=\"{ row }\">\n                  {{ new Date(row.timestamp).toLocaleTimeString() }}\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"avgTps\" label=\"TPS\" width=\"100\" />\n              <el-table-column prop=\"avgResponseTime\" label=\"响应时间(ms)\" width=\"120\" />\n              <el-table-column prop=\"totalRequests\" label=\"总请求数\" width=\"120\" />\n              <el-table-column prop=\"errorRate\" label=\"错误率(%)\" width=\"100\" />\n              <el-table-column prop=\"cpuPercent\" label=\"CPU(%)\" width=\"100\" />\n              <el-table-column prop=\"memoryPercent\" label=\"内存(%)\" width=\"100\" />\n            </el-table>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ElMessage } from \"element-plus\";\nimport { mapState } from \"vuex\";\nimport {\n  Connection, VideoPlay, VideoPause, Refresh\n} from '@element-plus/icons-vue';\nimport * as echarts from 'echarts';\n\nexport default {\n  name: 'PerformanceMonitor',\n  components: {\n    Connection, VideoPlay, VideoPause, Refresh\n  },\n  data() {\n    return {\n      websocket: null,\n      connectionStatus: 'disconnected',\n      isMonitoring: false,\n      loading: false,\n      currentData: {},\n      currentSystem: {},\n      recentData: [],\n      charts: {},\n      maxDataPoints: 50,\n      // WebSocket连接管理\n      reconnectAttempts: 0,\n      maxReconnectAttempts: 5,\n      reconnectInterval: 5000,\n      reconnectTimer: null,\n      heartbeatTimer: null,\n      heartbeatInterval: 30000,\n      isComponentDestroyed: false,\n      connectionRetryBackoff: [1000, 2000, 5000, 10000, 30000] // 重连退避策略\n    }\n  },\n  computed: {\n    ...mapState({\n      currentTaskId: state => state.currentTaskId || this.$route.params.taskId\n    })\n  },\n  mounted() {\n    this.initCharts();\n    this.connectWebSocket();\n    \n    // 添加页面可见性监听，优化性能\n    document.addEventListener('visibilitychange', this.handleVisibilityChange);\n    \n    // 添加窗口焦点监听\n    window.addEventListener('focus', this.handleWindowFocus);\n    window.addEventListener('blur', this.handleWindowBlur);\n  },\n  \n  beforeUnmount() {\n    // 标记组件已销毁\n    this.isComponentDestroyed = true;\n    \n    // 清理WebSocket连接\n    this.cleanupWebSocket();\n    \n    // 清理定时器\n    this.clearAllTimers();\n    \n    // 清理图表\n    this.destroyCharts();\n    \n    // 移除事件监听器\n    document.removeEventListener('visibilitychange', this.handleVisibilityChange);\n    window.removeEventListener('focus', this.handleWindowFocus);\n    window.removeEventListener('blur', this.handleWindowBlur);\n  },\n  \n  // 添加组件激活/停用处理（用于keep-alive）\n  activated() {\n    if (this.connectionStatus === 'disconnected' && !this.isComponentDestroyed) {\n      this.connectWebSocket();\n    }\n  },\n  \n  deactivated() {\n    // 组件被缓存时暂停连接\n    this.pauseConnection();\n  },\n  methods: {\n    initCharts() {\n      this.$nextTick(() => {\n        this.initTpsChart();\n        this.initResponseChart();\n        this.initCpuChart();\n        this.initMemoryChart();\n        this.initNetworkChart();\n      });\n    },\n\n    initTpsChart() {\n      const chartDom = this.$refs.tpsChart;\n      if (!chartDom) return;\n      \n      this.charts.tps = echarts.init(chartDom);\n      const option = {\n        title: { text: 'TPS 趋势', left: 'center' },\n        tooltip: { trigger: 'axis' },\n        xAxis: { type: 'category', data: [] },\n        yAxis: { type: 'value', name: 'TPS' },\n        series: [{\n          name: 'TPS',\n          type: 'line',\n          smooth: true,\n          data: [],\n          lineStyle: { color: '#409EFF' },\n          areaStyle: { color: 'rgba(64, 158, 255, 0.3)' }\n        }]\n      };\n      this.charts.tps.setOption(option);\n    },\n\n    initResponseChart() {\n      const chartDom = this.$refs.responseChart;\n      if (!chartDom) return;\n      \n      this.charts.response = echarts.init(chartDom);\n      const option = {\n        title: { text: '响应时间', left: 'center' },\n        tooltip: { trigger: 'axis' },\n        xAxis: { type: 'category', data: [] },\n        yAxis: { type: 'value', name: '毫秒' },\n        series: [{\n          name: '平均响应时间',\n          type: 'line',\n          smooth: true,\n          data: [],\n          lineStyle: { color: '#67C23A' }\n        }]\n      };\n      this.charts.response.setOption(option);\n    },\n\n    initCpuChart() {\n      const chartDom = this.$refs.cpuChart;\n      if (!chartDom) return;\n      \n      this.charts.cpu = echarts.init(chartDom);\n      const option = {\n        tooltip: { trigger: 'axis' },\n        xAxis: { type: 'category', data: [] },\n        yAxis: { type: 'value', name: '%', max: 100 },\n        series: [{\n          name: 'CPU',\n          type: 'line',\n          smooth: true,\n          data: [],\n          lineStyle: { color: '#E6A23C' },\n          areaStyle: { color: 'rgba(230, 162, 60, 0.3)' }\n        }]\n      };\n      this.charts.cpu.setOption(option);\n    },\n\n    initMemoryChart() {\n      const chartDom = this.$refs.memoryChart;\n      if (!chartDom) return;\n      \n      this.charts.memory = echarts.init(chartDom);\n      const option = {\n        tooltip: { trigger: 'axis' },\n        xAxis: { type: 'category', data: [] },\n        yAxis: { type: 'value', name: '%', max: 100 },\n        series: [{\n          name: '内存',\n          type: 'line',\n          smooth: true,\n          data: [],\n          lineStyle: { color: '#F56C6C' },\n          areaStyle: { color: 'rgba(245, 108, 108, 0.3)' }\n        }]\n      };\n      this.charts.memory.setOption(option);\n    },\n\n    initNetworkChart() {\n      const chartDom = this.$refs.networkChart;\n      if (!chartDom) return;\n      \n      this.charts.network = echarts.init(chartDom);\n      const option = {\n        tooltip: { trigger: 'axis' },\n        xAxis: { type: 'category', data: [] },\n        yAxis: { type: 'value', name: 'MB' },\n        series: [\n          {\n            name: '接收',\n            type: 'line',\n            smooth: true,\n            data: [],\n            lineStyle: { color: '#909399' }\n          },\n          {\n            name: '发送',\n            type: 'line',\n            smooth: true,\n            data: [],\n            lineStyle: { color: '#909399', type: 'dashed' }\n          }\n        ]\n      };\n      this.charts.network.setOption(option);\n    },\n\n    connectWebSocket() {\n      // 如果组件已销毁，不进行连接\n      if (this.isComponentDestroyed) return;\n      \n      if (!this.currentTaskId) {\n        ElMessage.error('未找到任务ID');\n        return;\n      }\n\n      // 清理现有连接\n      this.cleanupWebSocket();\n\n      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n      const wsUrl = `${wsProtocol}//${window.location.host}/ws/performance/monitor/${this.currentTaskId}/`;\n      \n      try {\n        this.websocket = new WebSocket(wsUrl);\n        \n        this.websocket.onopen = this.handleWebSocketOpen;\n        this.websocket.onmessage = this.handleWebSocketMessage;\n        this.websocket.onclose = this.handleWebSocketClose;\n        this.websocket.onerror = this.handleWebSocketError;\n        \n        // 设置连接超时\n        this.connectionTimeout = setTimeout(() => {\n          if (this.websocket && this.websocket.readyState === WebSocket.CONNECTING) {\n            this.websocket.close();\n            this.handleConnectionTimeout();\n          }\n        }, 10000);\n        \n      } catch (error) {\n        console.error('WebSocket连接创建失败:', error);\n        this.handleConnectionError(error);\n      }\n    },\n\n    handleWebSocketOpen() {\n      if (this.isComponentDestroyed) return;\n      \n      this.connectionStatus = 'connected';\n      this.reconnectAttempts = 0;\n      this.clearReconnectTimer();\n      \n      ElMessage.success('WebSocket连接成功');\n      \n      // 清理连接超时\n      if (this.connectionTimeout) {\n        clearTimeout(this.connectionTimeout);\n        this.connectionTimeout = null;\n      }\n      \n      // 启动心跳检测\n      this.startHeartbeat();\n      \n      // 获取当前状态\n      this.getCurrentStatus();\n    },\n\n    handleWebSocketMessage(event) {\n      if (this.isComponentDestroyed) return;\n      \n      try {\n        const data = JSON.parse(event.data);\n        this.processWebSocketMessage(data);\n      } catch (error) {\n        console.error('WebSocket消息解析失败:', error);\n      }\n    },\n\n    handleWebSocketClose(event) {\n      if (this.isComponentDestroyed) return;\n      \n      this.connectionStatus = 'disconnected';\n      this.isMonitoring = false;\n      this.stopHeartbeat();\n      \n      // 清理连接超时\n      if (this.connectionTimeout) {\n        clearTimeout(this.connectionTimeout);\n        this.connectionTimeout = null;\n      }\n      \n      console.log('WebSocket连接关闭:', event.code, event.reason);\n      \n      // 只有在非正常关闭时才尝试重连\n      if (event.code !== 1000 && event.code !== 1001) {\n        ElMessage.warning('WebSocket连接断开');\n        this.attemptReconnect();\n      }\n    },\n\n    handleWebSocketError(error) {\n      if (this.isComponentDestroyed) return;\n      \n      console.error('WebSocket错误:', error);\n      this.connectionStatus = 'disconnected';\n      \n      // 清理连接超时\n      if (this.connectionTimeout) {\n        clearTimeout(this.connectionTimeout);\n        this.connectionTimeout = null;\n      }\n    },\n\n    handleConnectionTimeout() {\n      console.warn('WebSocket连接超时');\n      ElMessage.error('WebSocket连接超时');\n      this.connectionStatus = 'disconnected';\n      this.attemptReconnect();\n    },\n\n    handleConnectionError(error) {\n      console.error('WebSocket连接错误:', error);\n      ElMessage.error('WebSocket连接错误');\n      this.connectionStatus = 'disconnected';\n      this.attemptReconnect();\n    },\n\n    processWebSocketMessage(data) {\n      // 心跳响应\n      if (data.type === 'pong') {\n        this.lastPongTime = Date.now();\n        return;\n      }\n      \n      switch (data.type) {\n        case 'connection_established':\n          console.log('连接建立:', data.message);\n          break;\n        case 'current_status':\n          this.updateCurrentData(data.data);\n          break;\n        case 'performance_update':\n          this.updatePerformanceData(data.data);\n          break;\n        case 'test_started':\n          ElMessage.success('性能测试已开始');\n          break;\n        case 'test_completed':\n          ElMessage.success('性能测试已完成');\n          this.isMonitoring = false;\n          break;\n        case 'test_failed':\n          ElMessage.error('性能测试失败: ' + data.error);\n          this.isMonitoring = false;\n          break;\n        case 'error':\n          ElMessage.error(data.message);\n          break;\n        default:\n          console.warn('未知的WebSocket消息类型:', data.type);\n      }\n    },\n\n    attemptReconnect() {\n      if (this.isComponentDestroyed || this.reconnectAttempts >= this.maxReconnectAttempts) {\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n          ElMessage.error('WebSocket重连失败，已达到最大重试次数');\n        }\n        return;\n      }\n\n      this.clearReconnectTimer();\n      \n      // 使用退避策略计算重连延迟\n      const backoffIndex = Math.min(this.reconnectAttempts, this.connectionRetryBackoff.length - 1);\n      const delay = this.connectionRetryBackoff[backoffIndex];\n      \n      console.log(`准备第 ${this.reconnectAttempts + 1} 次重连，延迟 ${delay}ms`);\n      \n      this.reconnectTimer = setTimeout(() => {\n        if (!this.isComponentDestroyed) {\n          this.reconnectAttempts++;\n          this.connectWebSocket();\n        }\n      }, delay);\n    },\n\n    startHeartbeat() {\n      this.stopHeartbeat();\n      \n      this.heartbeatTimer = setInterval(() => {\n        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n          this.websocket.send(JSON.stringify({ type: 'ping' }));\n        }\n      }, this.heartbeatInterval);\n    },\n\n    stopHeartbeat() {\n      if (this.heartbeatTimer) {\n        clearInterval(this.heartbeatTimer);\n        this.heartbeatTimer = null;\n      }\n    },\n\n    clearReconnectTimer() {\n      if (this.reconnectTimer) {\n        clearTimeout(this.reconnectTimer);\n        this.reconnectTimer = null;\n      }\n    },\n\n    clearAllTimers() {\n      this.clearReconnectTimer();\n      this.stopHeartbeat();\n      \n      if (this.connectionTimeout) {\n        clearTimeout(this.connectionTimeout);\n        this.connectionTimeout = null;\n      }\n    },\n\n    cleanupWebSocket() {\n      if (this.websocket) {\n        // 移除事件监听器防止内存泄漏\n        this.websocket.onopen = null;\n        this.websocket.onmessage = null;\n        this.websocket.onclose = null;\n        this.websocket.onerror = null;\n        \n        // 关闭连接\n        if (this.websocket.readyState === WebSocket.OPEN || \n            this.websocket.readyState === WebSocket.CONNECTING) {\n          this.websocket.close(1000, 'Component cleanup');\n        }\n        \n        this.websocket = null;\n      }\n      \n      this.stopHeartbeat();\n      this.clearReconnectTimer();\n    },\n\n    pauseConnection() {\n      // 暂停连接但不清理资源（用于keep-alive）\n      if (this.websocket) {\n        this.websocket.close(1000, 'Component deactivated');\n      }\n      this.stopHeartbeat();\n      this.clearReconnectTimer();\n    },\n\n    // 页面可见性变化处理\n    handleVisibilityChange() {\n      if (this.isComponentDestroyed) return;\n      \n      if (document.hidden) {\n        // 页面隐藏时暂停连接\n        this.pauseConnection();\n      } else {\n        // 页面显示时恢复连接\n        if (this.connectionStatus === 'disconnected') {\n          this.reconnectAttempts = 0;\n          this.connectWebSocket();\n        }\n      }\n    },\n\n    // 窗口焦点处理\n    handleWindowFocus() {\n      if (this.isComponentDestroyed) return;\n      \n      if (this.connectionStatus === 'disconnected') {\n        this.reconnectAttempts = 0;\n        this.connectWebSocket();\n      }\n    },\n\n    handleWindowBlur() {\n      // 窗口失焦时可以选择性地暂停某些操作\n      // 这里保持连接，只是记录日志\n      console.log('窗口失去焦点');\n    },\n\n    disconnectWebSocket() {\n      // 向后兼容的方法\n      this.cleanupWebSocket();\n    },\n\n    updateCurrentData(data) {\n      if (!data || this.isComponentDestroyed) return;\n      \n      // 深拷贝数据以防止引用导致的内存泄漏\n      this.currentData = { ...data };\n      \n      // 添加到历史数据，使用优化的内存管理\n      const timestamp = new Date().toISOString();\n      const newDataPoint = {\n        timestamp,\n        avgTps: data.avg_tps || 0,\n        avgResponseTime: data.avgResponseTime || 0,\n        totalRequests: data.total_requests || 0,\n        errorRate: data.error_rate || 0,\n        cpuPercent: this.currentSystem.cpu?.percent || 0,\n        memoryPercent: this.currentSystem.memory?.percent || 0,\n        networkRecv: this.currentSystem.network?.bytes_recv || 0,\n        networkSent: this.currentSystem.network?.bytes_sent || 0\n      };\n      \n      // 使用更高效的数组管理\n      this.addDataPoint(newDataPoint);\n      \n      // 批量更新图表，减少重绘频率\n      this.scheduleChartUpdate();\n    },\n\n    updatePerformanceData(data) {\n      if (!data || this.isComponentDestroyed) return;\n      \n      if (data.type === 'system_resources') {\n        // 深拷贝系统数据\n        this.currentSystem = { ...data.data };\n      } else {\n        // 合并性能数据\n        this.currentData = { ...this.currentData, ...data };\n      }\n      \n      this.scheduleChartUpdate();\n    },\n\n    addDataPoint(dataPoint) {\n      this.recentData.push(dataPoint);\n      \n      // 当数据点超过最大限制时，批量删除旧数据而不是逐个删除\n      if (this.recentData.length > this.maxDataPoints) {\n        const excessData = this.recentData.length - this.maxDataPoints;\n        this.recentData.splice(0, excessData);\n      }\n    },\n\n    scheduleChartUpdate() {\n      // 使用防抖来减少图表更新频率\n      if (this.chartUpdateTimer) {\n        clearTimeout(this.chartUpdateTimer);\n      }\n      \n      this.chartUpdateTimer = setTimeout(() => {\n        if (!this.isComponentDestroyed) {\n          this.updateCharts();\n        }\n      }, 100); // 100ms防抖\n    },\n\n    updateCharts() {\n      if (this.isComponentDestroyed || !this.recentData.length) return;\n      \n      // 批量处理数据，减少DOM操作\n      const times = this.recentData.map(d => new Date(d.timestamp).toLocaleTimeString());\n      \n      // 使用批处理更新所有图表\n      this.updateChartsInBatch(times);\n    },\n\n    updateChartsInBatch(times) {\n      const chartUpdates = [\n        {\n          chart: this.charts.tps,\n          data: this.recentData.map(d => d.avgTps)\n        },\n        {\n          chart: this.charts.response,\n          data: this.recentData.map(d => d.avgResponseTime)\n        },\n        {\n          chart: this.charts.cpu,\n          data: this.recentData.map(d => d.cpuPercent)\n        },\n        {\n          chart: this.charts.memory,\n          data: this.recentData.map(d => d.memoryPercent)\n        }\n      ];\n\n      // 批量更新图表以提高性能\n      chartUpdates.forEach(({ chart, data }) => {\n        if (chart && chart.setOption) {\n          try {\n            chart.setOption({\n              xAxis: { data: times },\n              series: [{ data: data }]\n            }, false, true); // 使用notMerge=false, lazyUpdate=true优化性能\n          } catch (error) {\n            console.warn('图表更新失败:', error);\n          }\n        }\n      });\n      \n      // 单独更新网络图表（因为有两个数据系列）\n      this.updateNetworkChart();\n    },\n\n    // 更新网络图表\n    updateNetworkChart() {\n      if (!this.charts.network) return;\n      \n      const times = this.recentData.map(d => new Date(d.timestamp).toLocaleTimeString());\n      const networkRecvData = this.recentData.map(d => {\n        const bytes = d.networkRecv || this.currentSystem.network?.bytes_recv || 0;\n        return (bytes / 1024 / 1024).toFixed(2); // 转换为MB\n      });\n      const networkSentData = this.recentData.map(d => {\n        const bytes = d.networkSent || this.currentSystem.network?.bytes_sent || 0;\n        return (bytes / 1024 / 1024).toFixed(2); // 转换为MB\n      });\n      \n      this.charts.network.setOption({\n        xAxis: { data: times },\n        series: [\n          { data: networkRecvData },\n          { data: networkSentData }\n        ]\n      });\n    },\n\n    toggleMonitoring() {\n      if (this.connectionStatus !== 'connected') {\n        ElMessage.error('WebSocket未连接');\n        return;\n      }\n      \n      this.loading = true;\n      const action = this.isMonitoring ? 'stop_monitoring' : 'start_monitoring';\n      \n      this.websocket.send(JSON.stringify({ type: action }));\n      \n      setTimeout(() => {\n        this.isMonitoring = !this.isMonitoring;\n        this.loading = false;\n      }, 1000);\n    },\n\n    getCurrentStatus() {\n      if (this.connectionStatus === 'connected') {\n        this.websocket.send(JSON.stringify({ type: 'get_current_status' }));\n      }\n    },\n\n    refreshData() {\n      this.getCurrentStatus();\n      ElMessage.success('数据已刷新');\n    },\n\n    clearData() {\n      // 清空数据数组，释放内存\n      this.recentData.length = 0;\n      this.currentData = {};\n      this.currentSystem = {};\n      \n      // 立即更新图表\n      this.updateCharts();\n      ElMessage.success('数据已清空');\n    },\n\n    formatBytes(bytes) {\n      if (bytes === 0) return '0 B';\n      const k = 1024;\n      const sizes = ['B', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n\n    destroyCharts() {\n      // 清理图表更新定时器\n      if (this.chartUpdateTimer) {\n        clearTimeout(this.chartUpdateTimer);\n        this.chartUpdateTimer = null;\n      }\n      \n      // 销毁所有图表实例并清理引用\n      Object.keys(this.charts).forEach(key => {\n        const chart = this.charts[key];\n        if (chart && typeof chart.dispose === 'function') {\n          try {\n            chart.dispose();\n          } catch (error) {\n            console.warn(`销毁图表 ${key} 时出错:`, error);\n          }\n        }\n      });\n      \n      // 清空图表对象\n      this.charts = {};\n    }\n  }\n}\n</script>\n\n<style scoped>\n.performance-monitor {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 100px);\n}\n\n.monitor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 16px 24px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\n  margin-bottom: 20px;\n}\n\n.header-info {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.header-info h2 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.monitor-controls {\n  display: flex;\n  gap: 10px;\n}\n\n.monitor-panels {\n  flex: 1;\n}\n\n.monitor-card {\n  margin-bottom: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n}\n\n.chart-container {\n  height: 300px;\n  width: 100%;\n}\n\n:deep(.el-card__header) {\n  padding: 15px 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n:deep(.el-card__body) {\n  padding: 20px;\n}\n\n.connection-status {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n</style>", "import { render } from \"./PerformanceMonitor.vue?vue&type=template&id=e2def50a&scoped=true\"\nimport script from \"./PerformanceMonitor.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceMonitor.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceMonitor.vue?vue&type=style&index=0&id=e2def50a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-e2def50a\"]])\n\nexport default __exports__"], "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_tag", "type", "$data", "connectionStatus", "effect", "_component_el_icon", "_component_Connection", "_toDisplayString", "_hoisted_5", "_component_el_button_group", "_component_el_button", "isMonitoring", "onClick", "$options", "toggleMonitoring", "loading", "_createBlock", "_component_VideoPause", "key", "_component_VideoPlay", "refreshData", "_component_Refresh", "_hoisted_6", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_card", "header", "_withCtx", "_hoisted_7", "size", "currentData", "avgTps", "_hoisted_8", "_hoisted_9", "avgResponseTime", "_hoisted_10", "style", "_hoisted_11", "currentSystem", "cpu", "percent", "toFixed", "_hoisted_12", "_hoisted_13", "memory", "_hoisted_14", "_hoisted_15", "formatBytes", "network", "bytes_recv", "_hoisted_16", "_hoisted_17", "clearData", "_cache", "_component_el_table", "data", "recentData", "slice", "stripe", "_component_el_table_column", "prop", "label", "width", "default", "row", "Date", "timestamp", "toLocaleTimeString", "name", "components", "Connection", "VideoPlay", "VideoPause", "Refresh", "websocket", "charts", "maxDataPoints", "reconnectAttempts", "maxReconnectAttempts", "reconnectInterval", "reconnectTimer", "heartbeatTimer", "heartbeatInterval", "isComponentDestroyed", "connectionRetryBackoff", "computed", "mapState", "currentTaskId", "state", "$route", "params", "taskId", "mounted", "this", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectWebSocket", "document", "addEventListener", "handleVisibilityChange", "window", "handleWindowFocus", "handleWindowBlur", "beforeUnmount", "cleanupWebSocket", "clearAllTimers", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "activated", "deactivated", "pauseConnection", "methods", "$nextTick", "initTpsChart", "initResponseChart", "initCpuChart", "initMemoryChart", "initNetworkChart", "chartDom", "$refs", "tpsChart", "tps", "echarts", "option", "title", "text", "left", "tooltip", "trigger", "xAxis", "yAxis", "series", "smooth", "lineStyle", "color", "areaStyle", "setOption", "responseChart", "response", "cpuChart", "max", "memoryChart", "networkChart", "ElMessage", "error", "wsProtocol", "location", "protocol", "wsUrl", "host", "WebSocket", "onopen", "handleWebSocketOpen", "onmessage", "handleWebSocketMessage", "onclose", "handleWebSocketClose", "onerror", "handleWebSocketError", "connectionTimeout", "setTimeout", "readyState", "CONNECTING", "close", "handleConnectionTimeout", "console", "handleConnectionError", "clearReconnectTimer", "success", "clearTimeout", "startHeartbeat", "getCurrentStatus", "event", "JSON", "parse", "processWebSocketMessage", "stopHeartbeat", "log", "code", "reason", "warning", "attemptReconnect", "warn", "message", "updateCurrentData", "updatePerformanceData", "lastPongTime", "now", "backoffIndex", "Math", "min", "length", "delay", "setInterval", "OPEN", "send", "stringify", "clearInterval", "hidden", "disconnectWebSocket", "toISOString", "newDataPoint", "avg_tps", "totalRequests", "total_requests", "errorRate", "error_rate", "cpuPer<PERSON>", "memoryPercent", "networkRecv", "networkSent", "bytes_sent", "addDataPoint", "scheduleChartUpdate", "dataPoint", "push", "excessData", "splice", "chartUpdateTimer", "updateCharts", "times", "map", "d", "updateChartsInBatch", "chartUpdates", "chart", "for<PERSON>ach", "updateNetworkChart", "networkRecvData", "bytes", "networkSentData", "action", "k", "sizes", "i", "floor", "parseFloat", "pow", "Object", "keys", "dispose", "__exports__", "render"], "sourceRoot": ""}