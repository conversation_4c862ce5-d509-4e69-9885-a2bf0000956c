{"version": 3, "file": "js/627.9a1b4e2e.js", "mappings": "gTAmCA,MAAMA,GAAMC,EAAAA,EAAAA,IAAI,MAChB,IAAIC,EAAQ,MACZC,EAAAA,EAAAA,IAAU,KACNC,MAOJ,MAAMA,EAAkBA,KACpBJ,EAAIK,MAAMC,MAAQN,EAAIK,MAAME,WAAWC,YACvCR,EAAIK,MAAMI,OAAST,EAAIK,MAAME,WAAWG,aAGxC,IAAIC,EAAY,UACZC,EAAS,UACTC,EAAQ,UACRC,EAAe,UACfC,EAAc,UACdC,EAAS,UAGTC,EAAO,IAAIC,IAAAA,cAAkB,CAE7BC,QAASnB,EAAIK,MACbe,YAAY,EACZC,KAAM,MAKNC,EAAO,IAAIJ,IAAAA,aAAiB,CAC5BK,MAAON,EACPX,MAAO,IACPG,OAAQ,IACRe,MAAO,QACPC,MAAM,EACNC,aAAc,GACdC,OAAQ,KAIZ,IAAIT,IAAAA,aAAiB,CACjBK,MAAOD,EACPhB,MAAO,IACPG,OAAQ,IACRe,MAAOZ,EACPa,MAAM,EACNC,aAAc,GACdC,OAAQ,IACRC,UAAW,CAAEC,GAAI,GAAIC,GAAI,MAI7B,IAAIC,EAAM,IAAIb,IAAAA,aAAiB,CAC3BK,MAAOD,EACPb,OAAQ,GACRH,MAAO,GACPqB,OAAQ,GACRF,MAAM,EACND,MAAOT,EACPa,UAAW,CAAEI,GAAI,IAAKF,GAAI,IAC1BJ,aAAc,MAGlB,IAAIR,IAAAA,aAAiB,CACjBK,MAAOQ,EACPtB,OAAQ,IACRH,MAAO,GACPqB,OAAQ,GACRF,MAAM,EACND,MAAOX,EACPe,UAAW,CAAEE,EAAG,KAChBJ,aAAc,MAIlB,IAAIO,EAAa,IAAIf,IAAAA,OAAW,CAC5BK,MAAOQ,EACPG,KAAM,CAAC,CAAEF,GAAI,IAAM,CAAEA,EAAG,KACxBL,OAAQ,GACRH,MAAOV,EACPc,UAAW,CAAEE,EAAG,OAGpBG,EAAWE,KAAK,CACZX,MAAOT,EACPa,UAAW,CAAEE,EAAG,OAIpB,IAAIZ,IAAAA,aAAiB,CACjBK,MAAOU,EACPxB,OAAQ,GACRH,MAAO,GACPsB,UAAW,CAAEI,GAAI,EAAGF,EAAG,IACvBL,MAAM,EACND,MAAOR,EACPW,OAAQ,KAGZ,IAAIT,IAAAA,aAAiB,CACjBK,MAAOU,EACPxB,OAAQ,GACRH,MAAO,EACPsB,UAAW,CAAEI,EAAG,GAAIF,EAAG,IACvBL,MAAM,EACND,MAAOZ,EACPe,OAAQ,KAGZI,EAAIK,UAAU,CACVR,UAAW,CAAEI,EAAG,IAAKF,GAAI,IACzBO,OAAQ,CAAEP,EAAGZ,IAAAA,IAAW,KAI5B,IAAIoB,EAAM,IAAIpB,IAAAA,aAAiB,CAC3BK,MAAOD,EACPb,OAAQ,IACRH,MAAO,GACPqB,OAAQ,GACRF,MAAM,EACND,MAAOX,EACPe,UAAW,CAAEI,GAAI,GAAIF,EAAG,KACxBJ,aAAc,MAIda,EAAa,IAAIrB,IAAAA,OAAW,CAC5BK,MAAOe,EACPJ,KAAM,CAAC,CAAEF,GAAI,IAAM,CAAEA,EAAG,KACxBL,OAAQ,GACRH,MAAOV,EACPc,UAAW,CAAEE,EAAG,OAGpBS,EAAWJ,KAAK,CACZX,MAAOT,EACPa,UAAW,CAAEE,EAAG,OAIpB,IAAIZ,IAAAA,aAAiB,CACjBK,MAAOe,EACPhC,MAAO,GACPG,OAAQ,GACRkB,OAAQ,GACRF,MAAM,EACND,MAAOR,EACPY,UAAW,CAAEI,GAAI,GAAIF,EAAG,KACxBJ,aAAc,KAGlBY,EAAIF,UAAU,CACVR,UAAW,CAAEI,EAAG,GAAIF,EAAG,KACvBO,OAAQ,CAAEP,EAAGZ,IAAAA,IAAW,KAK5B,IAAIsB,EAAO,IAAItB,IAAAA,aAAiB,CAC5BK,MAAOD,EACPhB,MAAO,IACPG,OAAQ,IACRgC,MAAO,GACPf,aAAc,GACdC,OAAQ,GACRH,MAAOX,EACPY,MAAM,EACNG,UAAW,CAAEE,GAAI,OAIjBY,EAAS,IAAIxB,IAAAA,aAAiB,CAC9BK,MAAOiB,EACPlC,MAAO,IACPG,OAAQ,IACRiB,aAAc,GACdF,MAAOb,EACPc,MAAM,EACNkB,UAAU,EACVf,UAAW,CAAEC,EAAG,MAIpB,IAAIX,IAAAA,MAAU,CACVK,MAAOmB,EACPpC,MAAO,GACPG,OAAQ,EACRkB,OAAQ,GACRC,UAAW,CAAEI,EAAG,GAAIF,GAAI,GAAID,EAAG,IAC/BL,MAAO,QACPmB,UAAU,IAId,IAAIC,EAAM,IAAI1B,IAAAA,aAAiB,CAC3BK,MAAOiB,EACPlC,MAAO,GACPG,OAAQ,GACRiB,aAAc,GACdC,OAAQ,GACRH,MAAOZ,EACPa,MAAM,EACNG,UAAW,CAAEI,GAAI,OAGrBY,EAAIT,KAAK,CACLP,UAAW,CAAEI,EAAG,OAIpB,IAAIa,EAAO,IAAI3B,IAAAA,OAAW,CACtBK,MAAOiB,EACPN,KAAM,CAAC,CAAEF,GAAI,KAAO,CAAEA,EAAG,MACzBJ,UAAW,CAAEE,EAAG,KAChBH,OAAQ,GACRH,MAAOV,IAGX+B,EAAKV,KAAK,CACNP,UAAW,CAAEE,EAAG,KAChBN,MAAOT,IAIX,IAAI+B,EAAW,IAAI5B,IAAAA,OAAW,CAC1BK,MAAOD,EACPY,KAAM,CAAC,CAAEF,GAAI,IAAM,CAAEA,EAAG,KACxBL,OAAQ,GACRC,UAAW,CAAEI,EAAG,IAAKH,EAAG,KACxBL,MAAOR,IAyCX,SAAS+B,IAEL9B,EAAKoB,OAAOP,GAAK,KACjBb,EAAKoB,OAAOL,GAAK,KACjBf,EAAKoB,OAAOR,GAAK,KACjBZ,EAAK+B,oBAEL9C,EAAQ+C,sBAAsBF,EAClC,CA9CAD,EAASX,KAAK,CACVP,UAAW,CAAEI,EAAG,IAAKF,EAAG,IAAKD,GAAI,KACjCL,MAAOR,IAGX8B,EAASX,KAAK,CACVP,UAAW,CAAEI,GAAI,IAAKF,EAAG,IAAKD,GAAI,KAClCL,MAAO,UAGXsB,EAASX,KAAK,CACVP,UAAW,CAAEI,GAAI,IAAKF,EAAG,IAAKD,GAAI,KAClCL,MAAOV,IAGXgC,EAASX,KAAK,CACVP,UAAW,CAAEI,EAAG,GAAIF,GAAI,GAAID,EAAG,KAC/BL,MAAOZ,IAGXkC,EAASX,KAAK,CACVP,UAAW,CAAEI,GAAI,IAAKF,EAAG,GAAID,EAAG,KAChCL,MAAOV,IAGXgC,EAASX,KAAK,CACVP,UAAW,CAAEI,GAAI,IAAKF,GAAI,IAAKD,EAAG,KAClCL,MAAOT,IAGX+B,EAASX,KAAK,CACVP,UAAW,CAAEI,EAAG,IAAKF,GAAI,IAAKD,GAAI,KAClCL,MAAO,UAIXP,EAAK+B,oBAaLD,K,OAEJG,EAAAA,EAAAA,IAAY,KACRC,qBAAqBjD,GACrBA,EAAQ,O,+EAlURkD,EAAAA,EAAAA,IA2BM,MA3BNC,EA2BM,C,0eAhBFC,EAAAA,EAAAA,IAYOC,EAAAC,OAAA,aAZP,IAYO,EAXHC,EAAAA,EAAAA,IAUM,MAVNC,EAUM,C,aATFD,EAAAA,EAAAA,IAAmC,OAA9BE,MAAM,gBAAe,OAAG,I,aAC7BF,EAAAA,EAAAA,IAAoD,OAA/CE,MAAM,mBAAkB,qBAAiB,I,aAC9CF,EAAAA,EAAAA,IAAmD,OAA9CE,MAAM,sBAAqB,iBAAa,KAC7CC,EAAAA,EAAAA,IAIcC,EAAA,CAJDC,GAAG,KAAG,C,iBACf,IAESC,EAAA,KAAAA,EAAA,KAFTN,EAAAA,EAAAA,IAES,UAFDE,MAAM,uCAAsC,UAEpD,M,uCAKZP,EAAAA,EAAAA,IAEM,MAFNY,EAEM,EADFP,EAAAA,EAAAA,IAA2B,U,QAAf,MAAJxD,IAAID,G,yBAD2BI,Q,cCpBnD,MAAM6D,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://frontend-web/./src/views/404.vue", "webpack://frontend-web/./src/views/404.vue?16d8"], "sourcesContent": ["\n<template>\n    <div class=\"ve_404\">\n        <!-- partial:index.partial.html -->\n        <div class=\"moon\"></div>\n        <div class=\"moon__crater moon__crater1\"></div>\n        <div class=\"moon__crater moon__crater2\"></div>\n        <div class=\"moon__crater moon__crater3\"></div>\n        <div class=\"star star1\">⭐</div>\n        <div class=\"star star2\">⭐</div>\n        <div class=\"star star3\">⭐</div>\n        <div class=\"star star4\">⭐</div>\n        <div class=\"star star5\">⭐</div>\n        <slot>\n            <div class=\"error\">\n                <div class=\"error__title\">404</div>\n                <div class=\"error__subtitle\">🐱🐱🐱(⓿_⓿)🐱🐱🐱</div>\n                <div class=\"error__description\">看来你是迷路了......</div>\n                <router-link to=\"/\">\n                    <button class=\"error__button error__button--active\">\n                        回到首页\n                    </button>\n                </router-link>\n                <!-- <button class=\"error__button\">CONTACT</button> -->\n            </div>\n        </slot>\n        <div class=\"astronaut\" v-resize=\"{ resize: draw3dAstronaut }\">\n            <canvas ref=\"cav\"></canvas>\n        </div>\n    </div>\n</template>\n\n<script setup>\nimport Zdog from \"zdog\";\nimport { ref, onUnmounted, onMounted } from \"vue\";\nconst cav = ref(null);\nlet timer = null;\nonMounted(() => {\n    draw3dAstronaut();\n});\n/**\n * @description: 画3d太空人\n * @param {*}\n * @return {*}\n */\nconst draw3dAstronaut = () => {\n    cav.value.width = cav.value.parentNode.clientWidth;\n    cav.value.height = cav.value.parentNode.clientHeight;\n\n    // colours\n    let dark_navy = \"#131e38\";\n    let orange = \"#fe9642\";\n    let cream = \"#FFF8E7\";\n    let light_purple = \"#7f3f98\";\n    let dark_purple = \"#563795\";\n    let cheese = \"#fbc715\";\n\n    // create illo\n    let illo = new Zdog.Illustration({\n        // set canvas with selector\n        element: cav.value,\n        dragRotate: true,\n        zoom: 0.65,\n    });\n\n    /** Body **/\n    // Body\n    let body = new Zdog.RoundedRect({\n        addTo: illo,\n        width: 200,\n        height: 220,\n        color: \"white\",\n        fill: true,\n        cornerRadius: 16,\n        stroke: 60,\n    });\n\n    // Backpack\n    new Zdog.RoundedRect({\n        addTo: body,\n        width: 180,\n        height: 310,\n        color: orange,\n        fill: true,\n        cornerRadius: 24,\n        stroke: 120,\n        translate: { z: -85, y: -60 },\n    });\n\n    /** arm **/\n    let arm = new Zdog.RoundedRect({\n        addTo: body,\n        height: 30,\n        width: 28,\n        stroke: 60,\n        fill: true,\n        color: dark_purple,\n        translate: { x: -140, y: -64 },\n        cornerRadius: 0.05,\n    });\n\n    new Zdog.RoundedRect({\n        addTo: arm,\n        height: 120,\n        width: 12,\n        stroke: 60,\n        fill: true,\n        color: cream,\n        translate: { y: 120 },\n        cornerRadius: 0.05,\n    });\n\n    // bubble_arm\n    let bubble_arm = new Zdog.Shape({\n        addTo: arm,\n        path: [{ x: -20 }, { x: 20 }],\n        stroke: 32,\n        color: light_purple,\n        translate: { y: 210 },\n    });\n\n    bubble_arm.copy({\n        color: dark_purple,\n        translate: { y: 230 },\n    });\n\n    // hand\n    new Zdog.RoundedRect({\n        addTo: bubble_arm,\n        height: 32,\n        width: 22,\n        translate: { x: -8, y: 60 },\n        fill: true,\n        color: cheese,\n        stroke: 30,\n    });\n\n    new Zdog.RoundedRect({\n        addTo: bubble_arm,\n        height: 24,\n        width: 0,\n        translate: { x: 24, y: 50 },\n        fill: true,\n        color: orange,\n        stroke: 20,\n    });\n\n    arm.copyGraph({\n        translate: { x: 140, y: -64 },\n        rotate: { y: Zdog.TAU / 2 },\n    });\n\n    /** Leg **/\n    let leg = new Zdog.RoundedRect({\n        addTo: body,\n        height: 160,\n        width: 28,\n        stroke: 60,\n        fill: true,\n        color: cream,\n        translate: { x: -56, y: 230 },\n        cornerRadius: 0.05,\n    });\n\n    // bubble_leg\n    let bubble_leg = new Zdog.Shape({\n        addTo: leg,\n        path: [{ x: -28 }, { x: 28 }],\n        stroke: 32,\n        color: light_purple,\n        translate: { y: 100 },\n    });\n\n    bubble_leg.copy({\n        color: dark_purple,\n        translate: { y: 124 },\n    });\n\n    // foot\n    new Zdog.RoundedRect({\n        addTo: leg,\n        width: 96,\n        height: 24,\n        stroke: 40,\n        fill: true,\n        color: cheese,\n        translate: { x: -24, y: 170 },\n        cornerRadius: 24,\n    });\n\n    leg.copyGraph({\n        translate: { x: 56, y: 230 },\n        rotate: { y: Zdog.TAU / 2 },\n    });\n\n    /** Head **/\n    // Head\n    let head = new Zdog.RoundedRect({\n        addTo: body,\n        width: 216,\n        height: 180,\n        depth: 40,\n        cornerRadius: 80,\n        stroke: 60,\n        color: cream,\n        fill: true,\n        translate: { y: -300 },\n    });\n\n    //add helmet\n    let helmet = new Zdog.RoundedRect({\n        addTo: head,\n        width: 210,\n        height: 165,\n        cornerRadius: 64,\n        color: dark_navy,\n        fill: true,\n        backface: false,\n        translate: { z: 20 },\n    });\n\n    //add refletion\n    new Zdog.Rect({\n        addTo: helmet,\n        width: 48,\n        height: 2,\n        stroke: 10,\n        translate: { x: 24, y: -24, z: 10 },\n        color: \"white\",\n        backface: false,\n    });\n\n    // add ear\n    let ear = new Zdog.RoundedRect({\n        addTo: head,\n        width: 36,\n        height: 72,\n        cornerRadius: 80,\n        stroke: 20,\n        color: orange,\n        fill: true,\n        translate: { x: -140 },\n    });\n\n    ear.copy({\n        translate: { x: 140 },\n    });\n\n    // neck\n    let neck = new Zdog.Shape({\n        addTo: head,\n        path: [{ x: -110 }, { x: 110 }],\n        translate: { y: 120 },\n        stroke: 40,\n        color: light_purple,\n    });\n\n    neck.copy({\n        translate: { y: 160 },\n        color: dark_purple,\n    });\n\n    /** extra **/\n    let stripe_1 = new Zdog.Shape({\n        addTo: body,\n        path: [{ x: -20 }, { x: 20 }],\n        stroke: 10,\n        translate: { x: 200, z: 200 },\n        color: cheese,\n    });\n\n    stripe_1.copy({\n        translate: { x: 320, y: 200, z: -400 },\n        color: cheese,\n    });\n\n    stripe_1.copy({\n        translate: { x: -220, y: 300, z: -400 },\n        color: \"white\",\n    });\n\n    stripe_1.copy({\n        translate: { x: -100, y: 400, z: -280 },\n        color: light_purple,\n    });\n\n    stripe_1.copy({\n        translate: { x: 50, y: -60, z: 150 },\n        color: orange,\n    });\n\n    stripe_1.copy({\n        translate: { x: -250, y: 80, z: 300 },\n        color: light_purple,\n    });\n\n    stripe_1.copy({\n        translate: { x: -350, y: -280, z: 175 },\n        color: dark_purple,\n    });\n\n    stripe_1.copy({\n        translate: { x: 250, y: -380, z: -175 },\n        color: \"white\",\n    });\n\n    // update & render\n    illo.updateRenderGraph();\n\n    function animate() {\n        // rotate illo each frame\n        illo.rotate.y += 0.005;\n        illo.rotate.x += 0.005;\n        illo.rotate.z += 0.005;\n        illo.updateRenderGraph();\n        // animate next frame\n        timer = requestAnimationFrame(animate);\n    }\n\n    // start animation\n    animate();\n};\nonUnmounted(() => {\n    cancelAnimationFrame(timer);\n    timer = null;\n});\n</script>\n\n<style  scoped>\n.ve_404 {\n    height: 100vh;\n    width: 100vw;\n    position: relative;\n    overflow: hidden;\n    background: linear-gradient(90deg, #2f3640 23%, #181b20 100%);\n}\n.moon {\n    background: linear-gradient(90deg, #d0d0d0 48%, #919191 100%);\n    position: absolute;\n    top: -30vh;\n    left: -80vh;\n    width: 160vh;\n    height: 160%;\n    content: \"\";\n    border-radius: 50%;\n    box-shadow: 0px 0px 30px -4px rgba(0, 0, 0, 0.5);\n}\n\n.moon__crater {\n    position: absolute;\n    content: \"\";\n    border-radius: 100%;\n    background: linear-gradient(90deg, #7a7a7a 38%, #c3c3c3 100%);\n    opacity: 0.6;\n}\n\n.moon__crater1 {\n    top: 250px;\n    left: 500px;\n    width: 60px;\n    height: 180px;\n}\n\n.moon__crater2 {\n    top: 650px;\n    left: 340px;\n    width: 40px;\n    height: 80px;\n    transform: rotate(55deg);\n}\n\n.moon__crater3 {\n    top: -20px;\n    left: 40px;\n    width: 65px;\n    height: 120px;\n    transform: rotate(250deg);\n}\n\n.star {\n    color: grey;\n    position: absolute;\n    width: 10px;\n    height: 10px;\n    content: \"\";\n    border-radius: 100%;\n    transform: rotate(250deg);\n    opacity: 0.4;\n    animation-name: shimmer;\n    animation-duration: 1.5s;\n    animation-iteration-count: infinite;\n    animation-direction: alternate;\n}\n\n@keyframes shimmer {\n    from {\n        opacity: 0;\n    }\n\n    to {\n        opacity: 0.7;\n    }\n}\n\n.star1 {\n    top: 40%;\n    left: 50%;\n    animation-delay: 1s;\n}\n\n.star2 {\n    top: 60%;\n    left: 90%;\n    animation-delay: 3s;\n}\n\n.star3 {\n    top: 10%;\n    left: 70%;\n    animation-delay: 2s;\n}\n\n.star4 {\n    top: 90%;\n    left: 40%;\n}\n\n.star5 {\n    top: 20%;\n    left: 30%;\n    animation-delay: 0.5s;\n}\n\n.astronaut {\n    position: absolute;\n    width: 60vw;\n    height: 100vh;\n    top: 0;\n    right: 0;\n    z-index: 0;\n}\n\n.error {\n    position: absolute;\n    left: 100px;\n    top: 400px;\n    transform: translateY(-60%);\n    font-family: \"Righteous\", cursive;\n    color: #363e49;\n    z-index: 1;\n}\n\n.error__title {\n    font-size: 10em;\n    font-weight: bold;\n    color: #d0d0d0;\n    text-shadow: -5px -5px 0 rgba(0, 0, 0, 0.7);\n    background-image: linear-gradient(90deg, #d0d0d0 48%, #919191 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n}\n\n.error__subtitle {\n    font-size: 2em;\n}\n\n.error__description {\n    opacity: 0.5;\n}\n\n.error__button {\n    min-width: 7em;\n    margin-top: 3em;\n    margin-right: 0.5em;\n    padding: 0.5em 2em;\n    outline: none;\n    border: 2px solid #2f3640;\n    background-color: transparent;\n    border-radius: 8em;\n    color: #576375;\n    cursor: pointer;\n    transition-duration: 0.2s;\n    font-size: 0.75em;\n    font-family: \"Righteous\", cursive;\n}\n\n.error__button:hover {\n    color: #21252c;\n}\n\n.error__button--active {\n    background-color: $base-color;\n    border: 2px solid $base-color;\n    color: white;\n}\n\n.error__button--active:hover {\n    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.5);\n    color: white;\n}\n</style>\n", "import script from \"./404.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./404.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./404.vue?vue&type=style&index=0&id=7c1fa82d&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-7c1fa82d\"]])\n\nexport default __exports__"], "names": ["cav", "ref", "timer", "onMounted", "draw3dAstronaut", "value", "width", "parentNode", "clientWidth", "height", "clientHeight", "dark_navy", "orange", "cream", "light_purple", "dark_purple", "cheese", "illo", "Zdog", "element", "dragRotate", "zoom", "body", "addTo", "color", "fill", "cornerRadius", "stroke", "translate", "z", "y", "arm", "x", "bubble_arm", "path", "copy", "copyGraph", "rotate", "leg", "bubble_leg", "head", "depth", "helmet", "backface", "ear", "neck", "stripe_1", "animate", "updateRenderGraph", "requestAnimationFrame", "onUnmounted", "cancelAnimationFrame", "_createElementBlock", "_hoisted_1", "_renderSlot", "_ctx", "$slots", "_createElementVNode", "_hoisted_2", "class", "_createVNode", "_component_router_link", "to", "_cache", "_hoisted_3", "__exports__"], "sourceRoot": ""}