"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[628],{46050:function(e,t,l){l.r(t),l.d(t,{default:function(){return Oe}});var a=l(56768),s=l(24232),o=l(45130);const n={class:"page-container"},d={class:"page-header-card"},i={class:"header-content"},r={class:"header-left"},c={class:"page-title"},p={class:"header-actions"},u={class:"tree-component"},f={style:{color:"#00aaff","font-weight":"bold"}},h={class:"stepStyle"},m={class:"tree-container"},k={slot:"header",class:"card-header"},g={class:"card-content-wrapper"},b={key:0,class:"card-inner"},_={class:"card-left"},v={class:"step-icon",style:{color:"rgb(97, 100, 159)"}},C={class:"method-tag"},y={key:0},I={style:{color:"#49cc90"}},F={key:1},w={style:{color:"#61affe"}},L={key:2},E={style:{color:"#fca130"}},V={key:3},D={style:{color:"#50e3c2"}},x={key:4},S={style:{color:"#f93e3e"}},U={key:5},W={style:{color:"rgb(201, 233, 104)"}},T={class:"card-center"},$={class:"card-url"},X={class:"card-name"},A={key:1,class:"card-inner"},R={class:"card-left"},Q={class:"step-icon",style:{color:"rgb(230, 162, 60)"}},q={class:"card-center if-content"},N={class:"if-controls-wrapper"},O=["onClick"],z={class:"card-left"},B={class:"step-icon",style:{color:"rgb(2, 167, 240)"}},M={class:"card-center"},P={class:"for-controls-wrapper"},j={key:0},H={class:"loop"},K={class:"loop-control"},G={class:"loop-control"},J={key:1},Y={class:"loop"},Z={class:"loop-control"},ee={class:"loop-control"},te={key:2},le={key:4,class:"card-inner"},ae={class:"card-left"},se={class:"step-icon",style:{color:"rgb(123, 77, 18)"}},oe={class:"card-center"},ne={class:"code_mod"},de={class:"code_mod"},ie={class:"code_mod"},re={class:"code_mod"},ce={key:5,class:"card-inner"},pe={class:"card-left"},ue={class:"step-icon",style:{color:"rgb(120, 56, 135)"}},fe={class:"card-center"},he={class:"sql-message"},me={key:6,class:"card-inner"},ke={class:"card-left"},ge={class:"step-icon",style:{color:"rgb(103, 194, 58)"}},be={class:"card-center time-controller"},_e={class:"time-control"},ve={class:"card-actions"},Ce={style:{padding:"20px"}},ye={style:{color:"#00aaff"}},Ie={style:{color:"#00aa7f"}},Fe={style:{color:"orangered"}},we={style:{color:"#fca130"}},Le={key:0},Ee={key:0},Ve={key:0,style:{color:"#00AA7F"}},De={key:1,style:{color:"#F56C6C"}},xe={key:2,style:{color:"#fca130"}};function Se(e,t,l,Se,Ue,We){const Te=(0,a.g2)("Back"),$e=(0,a.g2)("el-icon"),Xe=(0,a.g2)("el-button"),Ae=(0,a.g2)("ArrowDown"),Re=(0,a.g2)("el-tooltip"),Qe=(0,a.g2)("ArrowUp"),qe=(0,a.g2)("VideoPlay"),Ne=(0,a.g2)("Document"),Oe=(0,a.g2)("el-input"),ze=(0,a.g2)("el-form-item"),Be=(0,a.g2)("el-form"),Me=(0,a.g2)("el-col"),Pe=(0,a.g2)("el-tag"),je=(0,a.g2)("el-dropdown-item"),He=(0,a.g2)("el-dropdown-menu"),Ke=(0,a.g2)("el-dropdown"),Ge=(0,a.g2)("el-option"),Je=(0,a.g2)("el-select"),Ye=(0,a.g2)("el-radio"),Ze=(0,a.g2)("el-radio-group"),et=(0,a.g2)("el-input-number"),tt=(0,a.g2)("Edit"),lt=(0,a.g2)("Editor"),at=(0,a.g2)("el-divider"),st=(0,a.g2)("el-row"),ot=(0,a.g2)("el-switch"),nt=(0,a.g2)("el-card"),dt=(0,a.g2)("el-tree"),it=(0,a.g2)("el-scrollbar"),rt=(0,a.g2)("apiCite"),ct=(0,a.g2)("newEditCase"),pt=(0,a.g2)("el-drawer"),ut=(0,a.g2)("el-descriptions-item"),ft=(0,a.g2)("el-descriptions"),ht=(0,a.g2)("caseResult"),mt=(0,a.g2)("el-table-column"),kt=(0,a.g2)("el-table");return(0,a.uX)(),(0,a.CE)("div",n,[(0,a.Lk)("div",d,[(0,a.Lk)("div",i,[(0,a.Lk)("div",r,[(0,a.bF)(Xe,{class:"back-button",onClick:We.goBack},{default:(0,a.k6)(()=>[(0,a.bF)($e,null,{default:(0,a.k6)(()=>[(0,a.bF)(Te)]),_:1}),t[23]||(t[23]=(0,a.Lk)("span",null,"返回",-1))]),_:1,__:[23]},8,["onClick"]),(0,a.Lk)("div",c,(0,s.v_)(null!==this.case?"编辑用例":"新增用例"),1)]),(0,a.Lk)("div",p,[(0,a.bF)(Re,{content:"展开所有步骤",placement:"top",disabled:Ue.isExpand},{default:(0,a.k6)(()=>[Ue.isExpand?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(Xe,{key:0,class:"action-button",onClick:t[0]||(t[0]=e=>We.rowOpenORFold(!0))},{default:(0,a.k6)(()=>[(0,a.bF)($e,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ae)]),_:1}),t[24]||(t[24]=(0,a.Lk)("span",null,"展开",-1))]),_:1,__:[24]}))]),_:1},8,["disabled"]),(0,a.bF)(Re,{content:"折叠所有步骤",placement:"top",disabled:!Ue.isExpand},{default:(0,a.k6)(()=>[Ue.isExpand?((0,a.uX)(),(0,a.Wv)(Xe,{key:0,class:"action-button",onClick:t[1]||(t[1]=e=>We.rowOpenORFold(!1))},{default:(0,a.k6)(()=>[(0,a.bF)($e,null,{default:(0,a.k6)(()=>[(0,a.bF)(Qe)]),_:1}),t[25]||(t[25]=(0,a.Lk)("span",null,"折叠",-1))]),_:1,__:[25]})):(0,a.Q3)("",!0)]),_:1},8,["disabled"]),(0,a.bF)(Re,{content:"调试运行测试用例",placement:"top"},{default:(0,a.k6)(()=>[(0,a.bF)(Xe,{class:"action-button",type:"success",onClick:We.runCase},{default:(0,a.k6)(()=>[(0,a.bF)($e,null,{default:(0,a.k6)(()=>[(0,a.bF)(qe)]),_:1}),t[26]||(t[26]=(0,a.Lk)("span",null,"调试",-1))]),_:1,__:[26]},8,["onClick"])]),_:1}),(0,a.bF)(Re,{content:null!==this.case?"保存更改":"创建新用例",placement:"top"},{default:(0,a.k6)(()=>[(0,a.bF)(Xe,{class:"action-button save-button",type:"primary",onClick:t[2]||(t[2]=e=>null!==this.case?We.editCaseSave():We.addCaseSave())},{default:(0,a.k6)(()=>[(0,a.bF)($e,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ne)]),_:1}),t[27]||(t[27]=(0,a.Lk)("span",null,"保存",-1))]),_:1,__:[27]})]),_:1},8,["content"])])])]),(0,a.bF)(st,{gutter:10},{default:(0,a.k6)(()=>[(0,a.bF)(Me,{span:6},{default:(0,a.k6)(()=>[(0,a.Lk)("div",u,[(0,a.bF)(Be,{model:Ue.editForm,rules:Ue.rulesCase,ref:"CaseRef","label-width":"80px",style:{"max-width":"500px"}},{default:(0,a.k6)(()=>[(0,a.bF)(ze,{label:"用例名称",prop:"name",size:"mini"},{default:(0,a.k6)(()=>[(0,a.bF)(Oe,{modelValue:Ue.editForm.name,"onUpdate:modelValue":t[3]||(t[3]=e=>Ue.editForm.name=e),placeholder:"请输入用例名称"},null,8,["modelValue"])]),_:1}),(0,a.bF)(ze,{prop:"project_id",label:"所属项目",size:"mini"},{default:(0,a.k6)(()=>[(0,a.bF)(Oe,{modelValue:Ue.editForm.project_id,"onUpdate:modelValue":t[4]||(t[4]=e=>Ue.editForm.project_id=e),disabled:""},null,8,["modelValue"])]),_:1}),(0,a.bF)(ze,{label:"用例描述",prop:"desc"},{default:(0,a.k6)(()=>[(0,a.bF)(Oe,{type:"textarea",modelValue:Ue.editForm.desc,"onUpdate:modelValue":t[5]||(t[5]=e=>Ue.editForm.desc=e),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1}),(0,a.bF)(ze,{label:"步骤总数：","label-width":"93px"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",f,(0,s.v_)(Ue.editForm.stepCount),1)]),_:1})]),_:1},8,["model","rules"])])]),_:1}),(0,a.bF)(Me,{span:18},{default:(0,a.k6)(()=>[(0,a.Lk)("div",h,[(0,a.bF)(Pe,{class:"el-icon-plus",color:"#61649f",style:{"margin-right":"10px",width:"100px"},onClick:We.showApiCite},{default:(0,a.k6)(()=>t[28]||(t[28]=[(0,a.eW)("HTTP请求")])),_:1,__:[28]},8,["onClick"]),(0,a.bF)(Pe,{class:"el-icon-plus",color:"#E6A23C",style:{"margin-right":"10px",width:"100px"},onClick:t[6]||(t[6]=e=>We.AddController("if"))},{default:(0,a.k6)(()=>t[29]||(t[29]=[(0,a.eW)("条件控制器")])),_:1,__:[29]}),(0,a.bF)(Pe,{class:"el-icon-plus",color:"#02A7F0FF",style:{"margin-right":"10px",width:"100px"},onClick:t[7]||(t[7]=e=>We.AddController("for"))},{default:(0,a.k6)(()=>t[30]||(t[30]=[(0,a.eW)("循环控制器")])),_:1,__:[30]}),(0,a.bF)(Pe,{class:"el-icon-plus",color:"#7B4D12FF",style:{"margin-right":"10px",width:"100px"},onClick:t[8]||(t[8]=e=>We.AddController("script"))},{default:(0,a.k6)(()=>t[31]||(t[31]=[(0,a.eW)("自定义脚本")])),_:1,__:[31]}),(0,a.bF)(Pe,{class:"el-icon-plus",color:"#783887FF",style:{"margin-right":"10px",width:"100px"},onClick:t[9]||(t[9]=e=>We.AddController("sql"))},{default:(0,a.k6)(()=>t[32]||(t[32]=[(0,a.eW)("SQL控制器")])),_:1,__:[32]}),(0,a.bF)(Pe,{class:"el-icon-plus",color:"#67C23AFF",style:{"margin-right":"10px",width:"100px"},onClick:t[10]||(t[10]=e=>We.AddController("time"))},{default:(0,a.k6)(()=>t[33]||(t[33]=[(0,a.eW)("等待控制器")])),_:1,__:[33]})]),(0,a.bF)(it,{height:"calc(100vh - 190px)",always:"",onScroll:We.handleScroll},{default:(0,a.k6)(()=>[(0,a.Lk)("div",m,[((0,a.uX)(),(0,a.Wv)(dt,{data:Ue.steps,props:We.defaultProps,draggable:"",key:Ue.treeKey,"default-expand-all":Ue.isExpand,"expand-on-click-node":!1,onNodeClick:We.handleStepClick,"allow-drop":We.allowDrop,onNodeDrop:We.updateStepOrder,onNodeDragStart:We.handleDragStart,onNodeDragEnter:We.handleDragEnter,onNodeDragLeave:We.handleDragLeave,onNodeDragOver:We.handleDragOver,onNodeDragEnd:We.handleDragEnd,class:"custom-tree"},{default:(0,a.k6)(({node:e,data:l})=>[l.stepInfo?((0,a.uX)(),(0,a.Wv)(nt,{key:0,class:(0,s.C4)(["step-card",`step-card-${l.stepInfo.type}`])},{default:(0,a.k6)(()=>[(0,a.Lk)("div",k,[(0,a.bF)(st,{gutter:10,type:"flex",align:"middle",justify:"center"},{default:(0,a.k6)(()=>[(0,a.bF)(Me,{span:18,class:"card-main-content"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",g,["api"===l.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",b,[(0,a.Lk)("div",_,[(0,a.Lk)("span",v,(0,s.v_)(We.getCardIndex(e.parent,e)),1),(0,a.bF)(Pe,{color:"#61649f"},{default:(0,a.k6)(()=>t[34]||(t[34]=[(0,a.eW)("HTTP请求")])),_:1,__:[34]}),(0,a.Lk)("span",C,["POST"===l.stepInfo.method?((0,a.uX)(),(0,a.CE)("span",y,[(0,a.Lk)("b",I,(0,s.v_)(l.stepInfo.method),1)])):(0,a.Q3)("",!0),"GET"===l.stepInfo.method?((0,a.uX)(),(0,a.CE)("span",F,[(0,a.Lk)("b",w,(0,s.v_)(l.stepInfo.method),1)])):(0,a.Q3)("",!0),"PUT"===l.stepInfo.method?((0,a.uX)(),(0,a.CE)("span",L,[(0,a.Lk)("b",E,(0,s.v_)(l.stepInfo.method),1)])):(0,a.Q3)("",!0),"PATCH"===l.stepInfo.method?((0,a.uX)(),(0,a.CE)("span",V,[(0,a.Lk)("b",D,(0,s.v_)(l.stepInfo.method),1)])):(0,a.Q3)("",!0),"DELETE"===l.stepInfo.method?((0,a.uX)(),(0,a.CE)("span",x,[(0,a.Lk)("b",S,(0,s.v_)(l.stepInfo.method),1)])):(0,a.Q3)("",!0),"DEAD"===l.stepInfo.method?((0,a.uX)(),(0,a.CE)("span",U,[(0,a.Lk)("b",W,(0,s.v_)(l.stepInfo.method),1)])):(0,a.Q3)("",!0)])]),(0,a.Lk)("div",T,[(0,a.bF)(Ke,{trigger:"click",onClick:t[13]||(t[13]=(0,o.D$)(()=>{},["stop"]))},{dropdown:(0,a.k6)(()=>[(0,a.bF)(He,null,{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(We.testEnvsWithDefault,e=>((0,a.uX)(),(0,a.Wv)(je,{key:e.id,command:"api",onClick:t=>We.envClick(e,l.stepInfo)},{default:(0,a.k6)(()=>[(0,a.eW)((0,s.v_)(e.name),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),default:(0,a.k6)(()=>[0!==Object.keys(l.stepInfo.host).length?((0,a.uX)(),(0,a.Wv)(Xe,{key:0,type:"text",onClick:t[11]||(t[11]=(0,o.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>[(0,a.eW)((0,s.v_)(l.stepInfo.host.name),1)]),_:2},1024)):((0,a.uX)(),(0,a.Wv)(Xe,{key:1,type:"text",onClick:t[12]||(t[12]=(0,o.D$)(()=>{},["stop"]))},{default:(0,a.k6)(()=>t[35]||(t[35]=[(0,a.eW)("默认环境")])),_:1,__:[35]}))]),_:2},1024),(0,a.Lk)("b",$,(0,s.v_)(l.stepInfo.url),1),(0,a.Lk)("span",X,(0,s.v_)(l.stepInfo.name),1)])])):(0,a.Q3)("",!0),"if"===l.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",A,[(0,a.Lk)("div",R,[(0,a.Lk)("span",Q,(0,s.v_)(We.getCardIndex(e.parent,e)),1),(0,a.bF)(Pe,{color:"rgb(230, 162, 60)"},{default:(0,a.k6)(()=>t[36]||(t[36]=[(0,a.eW)("条件控制器")])),_:1,__:[36]})]),(0,a.Lk)("div",q,[(0,a.Lk)("div",N,[(0,a.bF)(Oe,{class:"input-def",placeholder:"变量，例如{{name}}",modelValue:l.stepInfo.content.variable,"onUpdate:modelValue":e=>l.stepInfo.content.variable=e},null,8,["modelValue","onUpdate:modelValue"]),(0,a.bF)(Je,{modelValue:l.stepInfo.content.JudgmentMode,"onUpdate:modelValue":e=>l.stepInfo.content.JudgmentMode=e,placeholder:"请选择",style:{width:"100px"}},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(Ue.options,e=>((0,a.uX)(),(0,a.Wv)(Ge,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),(0,a.bF)(Oe,{class:"input-def",placeholder:"值",modelValue:l.stepInfo.content.value,"onUpdate:modelValue":e=>l.stepInfo.content.value=e},null,8,["modelValue","onUpdate:modelValue"]),(0,a.bF)(Oe,{class:"input-def",placeholder:"备注",modelValue:l.stepInfo.desc,"onUpdate:modelValue":e=>l.stepInfo.desc=e},null,8,["modelValue","onUpdate:modelValue"])])])])):(0,a.Q3)("",!0),"for"===l.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",{key:2,class:"card-inner",onClick:e=>We.toggleExpand(l.stepInfo)},[(0,a.Lk)("div",z,[(0,a.Lk)("span",B,(0,s.v_)(We.getCardIndex(e.parent,e)),1),(0,a.bF)(Pe,{color:"rgb(2, 167, 240)"},{default:(0,a.k6)(()=>t[37]||(t[37]=[(0,a.eW)("循环控制器")])),_:1,__:[37]})]),(0,a.Lk)("div",M,[(0,a.Lk)("div",P,[(0,a.bF)(Ze,{modelValue:l.stepInfo.content.select,"onUpdate:modelValue":e=>l.stepInfo.content.select=e,onClick:t[14]||(t[14]=(0,o.D$)(()=>{},["stop"])),class:"radio-group"},{default:(0,a.k6)(()=>[(0,a.bF)(Ye,{label:"count",value:"count"},{default:(0,a.k6)(()=>t[38]||(t[38]=[(0,a.eW)("次数循环")])),_:1,__:[38]}),(0,a.bF)(Ye,{label:"for",value:"for"},{default:(0,a.k6)(()=>t[39]||(t[39]=[(0,a.eW)("for循环")])),_:1,__:[39]}),(0,a.bF)(Ye,{label:"while",value:"while",disabled:""},{default:(0,a.k6)(()=>t[40]||(t[40]=[(0,a.eW)("while循环")])),_:1,__:[40]})]),_:2},1032,["modelValue","onUpdate:modelValue"])])])],8,O)):(0,a.Q3)("",!0),"for"===l.stepInfo.type&&l.stepInfo.dlg?((0,a.uX)(),(0,a.CE)("div",{key:l.id,onClick:t[15]||(t[15]=(0,o.D$)(()=>{},["stop"])),class:"loop-details"},["count"===l.stepInfo.content.select||""===l.stepInfo.content.select?((0,a.uX)(),(0,a.CE)("div",j,[(0,a.Lk)("div",H,[(0,a.Lk)("div",K,[t[41]||(t[41]=(0,a.Lk)("span",null,"循环次数",-1)),(0,a.bF)(Oe,{modelValue:l.stepInfo.content.cycleIndex,"onUpdate:modelValue":e=>l.stepInfo.content.cycleIndex=e,style:{width:"200px"},placeholder:"循环次数"},null,8,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",G,[t[42]||(t[42]=(0,a.Lk)("span",null,"循环间隔",-1)),(0,a.bF)(et,{modelValue:l.stepInfo.content.cycleInterval,"onUpdate:modelValue":e=>l.stepInfo.content.cycleInterval=e,min:0,max:999,"controls-position":"right",placeholder:"秒"},null,8,["modelValue","onUpdate:modelValue"]),t[43]||(t[43]=(0,a.Lk)("span",null,"秒",-1))])])])):(0,a.Q3)("",!0),"for"===l.stepInfo.content.select?((0,a.uX)(),(0,a.CE)("div",J,[(0,a.Lk)("div",Y,[(0,a.Lk)("div",Z,[(0,a.bF)(Oe,{style:{width:"200px"},placeholder:"定义变量名称",modelValue:l.stepInfo.content.variableName,"onUpdate:modelValue":e=>l.stepInfo.content.variableName=e},null,8,["modelValue","onUpdate:modelValue"]),t[44]||(t[44]=(0,a.Lk)("b",null,"in",-1)),(0,a.bF)(Oe,{style:{width:"200px"},placeholder:"变量，例如{{name}}",modelValue:l.stepInfo.content.variable,"onUpdate:modelValue":e=>l.stepInfo.content.variable=e},null,8,["modelValue","onUpdate:modelValue"])]),(0,a.Lk)("div",ee,[t[45]||(t[45]=(0,a.Lk)("span",null,"循环间隔",-1)),(0,a.bF)(et,{modelValue:l.stepInfo.content.cycleInterval,"onUpdate:modelValue":e=>l.stepInfo.content.cycleInterval=e,min:0,max:999,size:"small","controls-position":"right",placeholder:"秒"},null,8,["modelValue","onUpdate:modelValue"]),t[46]||(t[46]=(0,a.Lk)("span",null,"秒",-1))])])])):(0,a.Q3)("",!0),"while"===l.stepInfo.content.select?((0,a.uX)(),(0,a.CE)("div",te,t[47]||(t[47]=[(0,a.Lk)("div",{class:"loop"},"敬请期待！",-1)]))):(0,a.Q3)("",!0)])):(0,a.Q3)("",!0),"script"===l.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",le,[(0,a.Lk)("div",ae,[(0,a.Lk)("span",se,(0,s.v_)(We.getCardIndex(e.parent,e)),1),(0,a.bF)(Pe,{color:"rgb(123, 77, 18)"},{default:(0,a.k6)(()=>t[48]||(t[48]=[(0,a.eW)("自定义脚本")])),_:1,__:[48]})]),(0,a.Lk)("div",oe,[l.stepInfo.inputDlg?((0,a.uX)(),(0,a.Wv)(Oe,{key:0,modelValue:l.stepInfo.name,"onUpdate:modelValue":e=>l.stepInfo.name=e,onBlur:e=>We.cancelEditing(l.stepInfo),ref:"input",maxlength:"50",onClick:t[16]||(t[16]=(0,o.D$)(()=>{},["stop"]))},null,8,["modelValue","onUpdate:modelValue","onBlur"])):((0,a.uX)(),(0,a.Wv)(Xe,{key:1,class:"script-button",plain:"",type:"text",onClick:[e=>We.startEditing(l.stepInfo),t[17]||(t[17]=(0,o.D$)(()=>{},["stop"]))]},{default:(0,a.k6)(()=>[(0,a.eW)((0,s.v_)(l.stepInfo.name)+" ",1),(0,a.bF)($e,null,{default:(0,a.k6)(()=>[(0,a.bF)(tt)]),_:1})]),_:2},1032,["onClick"]))])])):(0,a.Q3)("",!0),(0,a.bo)((0,a.bF)(st,{gutter:10,onClick:t[18]||(t[18]=(0,o.D$)(()=>{},["stop"])),class:"script-editor"},{default:(0,a.k6)(()=>[(0,a.bF)(Me,{span:18},{default:(0,a.k6)(()=>[(0,a.bF)(lt,{modelValue:l.stepInfo.script,"onUpdate:modelValue":e=>l.stepInfo.script=e,lang:"python",theme:"chrome"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),(0,a.bF)(Me,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(at,null,{default:(0,a.k6)(()=>t[49]||(t[49]=[(0,a.eW)("脚本模板")])),_:1,__:[49]}),(0,a.Lk)("div",ne,[(0,a.bF)(Xe,{type:"success",size:"mini",plain:"",onClick:e=>We.addSetUptCodeMod(l.stepInfo,"func")},{default:(0,a.k6)(()=>t[50]||(t[50]=[(0,a.eW)("导入变量模块")])),_:2,__:[50]},1032,["onClick"])]),(0,a.Lk)("div",de,[(0,a.bF)(Xe,{type:"success",size:"mini",plain:"",onClick:e=>We.addSetUptCodeMod(l.stepInfo,"ENV")},{default:(0,a.k6)(()=>t[51]||(t[51]=[(0,a.eW)("预设全局变量")])),_:2,__:[51]},1032,["onClick"])]),(0,a.Lk)("div",ie,[(0,a.bF)(Xe,{type:"success",size:"mini",plain:"",onClick:e=>We.addSetUptCodeMod(l.stepInfo,"env")},{default:(0,a.k6)(()=>t[52]||(t[52]=[(0,a.eW)("预设局部变量")])),_:2,__:[52]},1032,["onClick"])]),(0,a.Lk)("div",re,[(0,a.bF)(Xe,{type:"success",size:"mini",plain:"",onClick:e=>We.addSetUptCodeMod(l.stepInfo,"sql")},{default:(0,a.k6)(()=>t[53]||(t[53]=[(0,a.eW)("执行sql查询")])),_:2,__:[53]},1032,["onClick"])])]),_:2},1024)]),_:2},1536),[[o.aG,"script"===l.stepInfo.type&&l.stepInfo.dlg]]),"sql"===l.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",ce,[(0,a.Lk)("div",pe,[(0,a.Lk)("span",ue,(0,s.v_)(We.getCardIndex(e.parent,e)),1),(0,a.bF)(Pe,{color:"rgb(120, 56, 135)"},{default:(0,a.k6)(()=>t[54]||(t[54]=[(0,a.eW)("SQL控制器")])),_:1,__:[54]})]),(0,a.Lk)("div",fe,[l.stepInfo.inputDlg?((0,a.uX)(),(0,a.Wv)(Oe,{key:0,modelValue:l.stepInfo.name,"onUpdate:modelValue":e=>l.stepInfo.name=e,onBlur:e=>We.cancelEditing(l.stepInfo),ref:"input",maxlength:"50",onClick:t[19]||(t[19]=(0,o.D$)(()=>{},["stop"]))},null,8,["modelValue","onUpdate:modelValue","onBlur"])):((0,a.uX)(),(0,a.Wv)(Xe,{key:1,class:"script-button",plain:"",type:"text",onClick:[e=>We.startEditing(l.stepInfo),t[20]||(t[20]=(0,o.D$)(()=>{},["stop"]))]},{default:(0,a.k6)(()=>[(0,a.eW)((0,s.v_)(l.stepInfo.name)+" ",1),(0,a.bF)($e,null,{default:(0,a.k6)(()=>[(0,a.bF)(tt)]),_:1})]),_:2},1032,["onClick"]))])])):(0,a.Q3)("",!0),(0,a.bo)((0,a.Lk)("div",he,t[55]||(t[55]=[(0,a.Lk)("i",null,"该功能敬请期待噢！",-1)]),512),[[o.aG,"sql"===l.stepInfo.type&&l.stepInfo.dlg]]),"time"===l.stepInfo.type?((0,a.uX)(),(0,a.CE)("div",me,[(0,a.Lk)("div",ke,[(0,a.Lk)("span",ge,(0,s.v_)(We.getCardIndex(e.parent,e)),1),(0,a.bF)(Pe,{color:"rgb(103, 194, 58)"},{default:(0,a.k6)(()=>t[56]||(t[56]=[(0,a.eW)("等待控制器")])),_:1,__:[56]})]),(0,a.Lk)("div",be,[(0,a.Lk)("div",_e,[(0,a.bF)(et,{modelValue:l.stepInfo.content.time,"onUpdate:modelValue":e=>l.stepInfo.content.time=e,min:0,max:999,size:"small","controls-position":"right",placeholder:"秒"},null,8,["modelValue","onUpdate:modelValue"]),t[57]||(t[57]=(0,a.Lk)("span",null,"秒",-1))])])])):(0,a.Q3)("",!0)])]),_:2},1024),(0,a.bF)(Me,{span:6},{default:(0,a.k6)(()=>[(0,a.Lk)("div",ve,[(0,a.bF)(ot,{modelValue:l.status,"onUpdate:modelValue":e=>l.status=e,"inline-prompt":"",size:"default",onClick:e=>We.switchClick(l),style:{"--el-switch-on-color":"#53a8ff","--el-switch-off-color":"#f56c6c"}},null,8,["modelValue","onUpdate:modelValue","onClick"]),(0,a.bF)(Xe,{size:"default",icon:"document",onClick:e=>We.copyTree(l),circle:"",class:"action-button"},null,8,["onClick"]),(0,a.bF)(Xe,{size:"default",icon:"delete",type:"danger",onClick:e=>We.delTree(l),circle:"",class:"action-button"},null,8,["onClick"])])]),_:2},1024)]),_:2},1024)])]),_:2},1032,["class"])):(0,a.Q3)("",!0)]),_:1},8,["data","props","default-expand-all","onNodeClick","allow-drop","onNodeDrop","onNodeDragStart","onNodeDragEnter","onNodeDragLeave","onNodeDragOver","onNodeDragEnd"]))])]),_:1},8,["onScroll"])]),_:1})]),_:1}),Ue.addApiDlg?((0,a.uX)(),(0,a.Wv)(rt,{key:0,onChildEvent:We.handleChildData,onCloseModal:We.handleCloseModal},null,8,["onChildEvent","onCloseModal"])):(0,a.Q3)("",!0),(0,a.bF)(pt,{modelValue:Ue.editCaseDlg,"onUpdate:modelValue":t[21]||(t[21]=e=>Ue.editCaseDlg=e),"with-header":!1,size:"50%"},{default:(0,a.k6)(()=>[(0,a.bF)(ct,{ref:"childRef",onCloseDrawer:We.handleClose,Interface_id:Ue.Interface_id,copyDlg:Ue.copyDlg,style:{padding:"0 10px"}},null,8,["onCloseDrawer","Interface_id","copyDlg"])]),_:1},8,["modelValue"]),(0,a.bF)(pt,{modelValue:Ue.ResultDlg,"onUpdate:modelValue":t[22]||(t[22]=e=>Ue.ResultDlg=e),"with-header":!1,size:"50%"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",Ce,[(0,a.bF)(ft,{title:"执行结果",border:"",column:4,style:{"text-align":"center"}},{default:(0,a.k6)(()=>[(0,a.bF)(ut,{label:"总数"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",ye,(0,s.v_)(Ue.runScentResult.all),1)]),_:1}),(0,a.bF)(ut,{label:"通过"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",Ie,(0,s.v_)(Ue.runScentResult.success),1)]),_:1}),(0,a.bF)(ut,{label:"失败"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",Fe,(0,s.v_)(Ue.runScentResult.fail),1)]),_:1}),(0,a.bF)(ut,{label:"错误"},{default:(0,a.k6)(()=>[(0,a.Lk)("b",we,(0,s.v_)(Ue.runScentResult.error),1)]),_:1})]),_:1}),t[58]||(t[58]=(0,a.Lk)("div",{style:{height:"40px","line-height":"40px"}},[(0,a.Lk)("b",null,"执行详情")],-1)),(0,a.bF)(it,{height:"calc(100vh - 180px)"},{default:(0,a.k6)(()=>[(0,a.bF)(kt,{data:Ue.runScentResult.cases,style:{width:"100%"},"empty-text":"暂无数据"},{default:(0,a.k6)(()=>[(0,a.bF)(mt,{type:"expand"},{default:(0,a.k6)(e=>[(0,a.bF)(ht,{result:e.row},null,8,["result"])]),_:1}),(0,a.bF)(mt,{label:"步骤名",prop:"name"}),(0,a.bF)(mt,{label:"请求方法",prop:"method"},{default:(0,a.k6)(e=>["api"===e.row.type?((0,a.uX)(),(0,a.CE)("span",Le,(0,s.v_)(e.row.method),1)):(0,a.Q3)("",!0)]),_:1}),(0,a.bF)(mt,{label:"响应状态码",prop:"status_cede"},{default:(0,a.k6)(e=>["api"===e.row.type?((0,a.uX)(),(0,a.CE)("span",Ee,(0,s.v_)(e.row.status_cede),1)):(0,a.Q3)("",!0)]),_:1}),(0,a.bF)(mt,{label:"执行结果",prop:"state","min-width":"40px"},{default:(0,a.k6)(e=>["成功"==e.row.state?((0,a.uX)(),(0,a.CE)("span",Ve,(0,s.v_)(e.row.state),1)):"错误"==e.row.state?((0,a.uX)(),(0,a.CE)("span",De,(0,s.v_)(e.row.state),1)):((0,a.uX)(),(0,a.CE)("span",xe,(0,s.v_)(e.row.state),1))]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1},8,["modelValue"])])}l(44114),l(18111),l(22489),l(7588),l(61701);var Ue=l(60782),We=l(51219),Te=l(93851),$e=l(93491),Xe=l(80225),Ae=l(67638),Re=l(53629),Qe={components:{apiCite:$e.A,newEditCase:Xe.A,caseResult:Ae.A,Editor:Re.A},data(){return{editForm:{name:"",project_id:"",desc:"",stepCount:0,creator:""},rulesCase:{name:[{required:!0,message:"请输入用例名称",trigger:"blur"}],project_id:[{required:!0,message:"请选择项目",trigger:"blur"}]},steps:[],addApiDlg:!1,editCaseDlg:!1,Interface_id:"",copyDlg:!1,ResultDlg:!1,runScentResult:"",ControllerData:{name:"",type:"",content:{},script:"",desc:"",creator:""},options:[{value:"equal",label:"等于"},{value:"notEqual",label:"不等于"},{value:"contains",label:"包含"},{value:"notContains",label:"不包含"},{value:"greaterThan",label:"大于"},{value:"lessThan",label:"小于"},{value:"greaterThanOrEqual",label:"大于等于"},{value:"lessThanOrEqual",label:"小于等于"},{value:"empty",label:"空"},{value:"notEmpty",label:"非空"}],test:"",treeKey:"",isExpand:!1}},computed:{...(0,Ue.aH)(["pro","case","envId","testEnvs"]),testEnvsWithDefault(){const e=[...this.testEnvs];return e.unshift({host:"默认环境host",name:"默认环境"}),e},username(){return window.sessionStorage.getItem("username")},defaultProps(){return{children:"children",label:"name"}}},watch:{steps:{deep:!0}},methods:{...(0,Ue.PY)(["CaseInfo","clearCaseInfo"]),getCardIndex(e,t){const l=e.childNodes.indexOf(t);return l+1},async addCaseSave(){this.$refs.CaseRef.validate(async e=>{if(!e)return;const t={...this.editForm};t.creator=this.username,t.project_id=this.pro.id;const l=await this.$api.createTestCase(t);201===l.status&&(0,We.nk)({type:"success",message:"保存成功",duration:1e3})})},async editCaseSave(){this.$refs.CaseRef.validate(async e=>{if(!e)return;const t={...this.editForm};t.project_id=this.pro.id,t.modifier=this.username,t.update_time=this.$tools.newTime(),delete t.create_time,delete t.creator,delete t.project;const l=await this.$api.updateTestCase(t.id,t);200===l.status&&((0,We.nk)({type:"success",message:"保存成功",duration:1e3}),this.editStepSave())})},async editStepSave(){const e=this.steps.filter(e=>e.stepInfo&&"api"!==e.stepInfo.type).map(e=>e),t=await this.$api.updatesStepControll(e);201===t.status&&this.getCaseStep(this.case.id)},reaiTime(){this.case?(this.editForm=this.case,this.editForm.project_id=this.pro.name):this.editForm.project_id=this.pro.name},goBack(){this.case&&this.case.back_type?(this.$router.push({name:"new-testplan"}),this.clearCaseInfo()):(this.$router.push({name:"TestCase"}),this.clearCaseInfo())},handleStepClick(e){"api"===e.stepInfo.type?(this.Interface_id=e.interfaceStep,this.editCaseDlg=!0,this.$nextTick(()=>{this.$refs.childRef.getInterfaceInfo(this.Interface_id)})):("script"===e.stepInfo.type||"sql"===e.stepInfo.type)&&(e.stepInfo.dlg=!e.stepInfo.dlg)},handleClose(){this.addCaseDlg=!1,this.editCaseDlg=!1},getRowClassName(e){switch(e){case"api":return"--el-card-border-color:rgb(97, 100, 159);";case"if":return"--el-card-border-color:rgb(230, 162, 60);";case"for":return"--el-card-border-color:rgb(2, 167, 240);";case"script":return"--el-card-border-color:rgb(123, 77, 18);";case"time":return"--el-card-border-color:rgb(103, 194, 58);";case"sql":return"--el-card-border-color:rgb(120, 56, 135);";default:return""}},allowDrop(e,t,l){const a=["for","if"];return!!a.includes(t.data.stepInfo.type)||("prev"===l||"next"===l)},async copyTree(e,t=null,l=!0){event.stopPropagation();let a=this.steps.length>0?this.steps.length+1:1;if("api"===e.stepInfo.type)await this.$api.createTestCaseStep({case:this.case.id,interfaceStep:e.interfaceStep,sort:a,parent_id:t});else{e.stepInfo.case=this.case.id,e.stepInfo.sort=a,e.stepInfo.parent_id=t;const s=await this.$api.copyStepControll(e.stepInfo),o=s.data.setpId;if(e.children){let t=e.children.map((t,a)=>{const s=l&&a===e.children.length-1;return this.copyTree(t,o,s)});await Promise.all(t)}}l&&this.getCaseStep(this.case.id)},async delTree(e){if(event.stopPropagation(),"api"===e.stepInfo.type){const t=await this.$api.delTestCaseStep(e.id);204===t.status&&this.getCaseStep(this.case.id)}else{const t=await this.$api.delTestCaseStep(e.id);if(204===t.status){const t=await this.$api.delStepControll(e.controllerStep);204===t.status&&this.getCaseStep(this.case.id)}}},showApiCite(){this.$refs.CaseRef.validate(async e=>{e&&(this.addApiDlg=!0)})},handleCloseModal(){this.addApiDlg=!1},async handleChildData(e){let t=this.steps.length>0?this.steps.length+1:1,l=[];e.forEach(e=>{let a={sort:t,case:this.case.id,interfaceStep:e};l.push(a),t++});const a=await this.$api.createsTestCaseStep(l);201===a.status&&this.getCaseStep(this.case.id)},async getCaseStep(e){const t=await this.$api.getTestCaseStep(e);200===t.status&&(this.steps=t.data,this.editForm.stepCount=this.countSteps(this.steps),this.rowOpenORFold(!0))},countSteps(e){let t=0;return e.forEach(e=>{t+=1,e.children&&e.children.length>0&&(t+=this.countSteps(e.children))}),t},async switchClick(){event.stopPropagation(),this.updateStepOrder()},handleItemClick(e,t,l){l&&(l.stopPropagation(),l.preventDefault()),console.log("handleItemClick 被调用:",{item:e,data:t,itemName:e?e.name:null,dataType:t?t.type:null,dataId:t?t.id:null}),this.envClick(e,t)},async updateStepOrder(){const e=(t,l,a)=>{t.sort=a,t.children&&t.children.length>0&&t.children.forEach((l,a)=>{l.parent_id=t.id,l.sort=a+1,e(l,t.id,l.sort)})};this.steps.forEach((t,l)=>{t.sort=l+1,t.children&&t.children.length>0&&e(t,t.id,t.sort)});const t=await this.$api.updateCaseStepOrder(this.steps);200===t.status&&(0,Te.df)({duration:500,title:"调整成功",type:"success"})},async runCase(){if(this.envId){const e={env:this.envId,scene:this.case.id};(0,Te.df)({title:"开始运行",message:"运行过程中请稍等片刻噢",type:"success",duration:1e3});const t=await this.$api.runCases(this.case.id,e);200==t.status&&(this.runScentResult=t.data,this.ResultDlg=!0)}else this.$message({type:"warning",message:"当前未选中执行环境!",duration:1e3})},toggleExpand(e){e.dlg=!e.dlg},startEditing(e){e.type,e.inputDlg=!0,this.$nextTick(()=>{this.$refs.input.focus()})},cancelEditing(e){e.inputDlg=!1},handleScroll(e){},addSetUptCodeMod(e,t){switch(t){case"ENV":e.script+='\n# 设置全局变量 \nBaseTest().save_global_variable("变量名","变量值")';break;case"env":e.script+='\n# 设置局部变量  \nBaseTest().save_env_variable("变量名","变量值")';break;case"func":e.script+="\nfrom apitestengine.core.cases import BaseTest";break;case"sql":e.script+='\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\n# db.连接名.execute_all(sql语句) \nsql = "SELECT count(*) as count FROM futureloan.member"\nres = db.aliyun.execute_all(sql)';break}},handleDragStart(e,t){console.log("drag start",e),this.$nextTick(()=>{const e=document.querySelector(".el-tree-node__drop-indicator");e&&(e.style.display="block",e.style.visibility="visible",e.style.opacity="1")}),document.addEventListener("mousemove",function(e){const t=e.clientY,l=document.querySelector(".el-tree"),a=l?l.getBoundingClientRect().top:0;t<100&&a>0?window.scrollBy(0,-10):t>window.innerHeight-100&&window.scrollBy(0,10)})},handleDragEnter(e,t,l){console.log("tree drag enter:",t.label)},handleDragLeave(e,t,l){console.log("tree drag leave:",t.label)},handleDragOver(e,t,l){console.log("tree drag over:",t.label)},handleDragEnd(e,t,l,a){console.log("tree drag end:",t&&t.label,l)},async AddController(e){let t=this.steps.length>0?this.steps.length+1:1;if("if"===e)this.ControllerData={name:"条件控制器",type:"if",content:{variable:"",JudgmentMode:"",value:""},script:"",desc:"",creator:this.username};else if("for"===e)this.ControllerData={name:"循环控制器",type:"for",content:{select:"count",cycleIndex:"",cycleInterval:"",variable:"",variableName:""},script:"",desc:"",creator:this.username};else if("script"===e)this.ControllerData={name:"自定义脚本",type:"script",content:{},script:"",desc:"",creator:this.username};else if("time"===e)this.ControllerData={name:"定时控制器",type:"time",content:{time:""},script:"",desc:"",creator:this.username};else{if("sql"!==e)throw new Error("types is not defined");this.ControllerData={name:"数据库操作",type:"sql",content:{},script:"",desc:"",creator:this.username}}const l=await this.$api.createStepControll(this.ControllerData);if(201===l.status){const e=await this.$api.createTestCaseStep({case:this.case.id,controllerStep:l.data.id,sort:t});201===e.status&&this.getCaseStep(this.case.id)}},rowOpenORFold(e){this.treeKey=+new Date,this.isExpand=e},async envClick(e,t){let l;l="默认环境"===e.name?{host:{}}:{host:{host:e.host,name:e.name}};const a=await this.$api.updatenewInterface(t.id,l);200===a.status&&(0,We.nk)({type:"success",message:"变更成功",duration:1e3}),this.getCaseStep(this.case.id)}},created(){this.reaiTime(),this.case&&this.case.id&&this.getCaseStep(this.case.id)},mounted(){const e=document.createElement("style");e.innerHTML="\n      .el-tree-node__drop-indicator {\n        position: absolute !important;\n        left: 0 !important;\n        right: 0 !important;\n        height: 5px !important;\n        background-color: #2b85e4 !important;\n        z-index: 100000 !important;\n        pointer-events: none !important;\n        border-radius: 2px !important;\n        box-shadow: 0 0 6px rgba(43, 133, 228, 0.8) !important;\n        display: block !important;\n        visibility: visible !important;\n        opacity: 1 !important;\n      }\n    ",document.head.appendChild(e)}},qe=l(71241);const Ne=(0,qe.A)(Qe,[["render",Se],["__scopeId","data-v-5780c1de"]]);var Oe=Ne}}]);
//# sourceMappingURL=628.d204f4ae.js.map