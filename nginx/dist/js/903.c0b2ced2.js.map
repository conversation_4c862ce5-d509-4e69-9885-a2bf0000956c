{"version": 3, "file": "js/903.c0b2ced2.js", "mappings": "83CAiYA,MAAM,MAAEA,IAAUC,EAAAA,EAAAA,OAAwB,CAAED,MAAO,MAK7CE,GAAQC,EAAAA,EAAAA,MACRC,GAAUC,EAAAA,EAAAA,IAAS,IAAMH,EAAMI,MAAMC,KAGrCC,IAAOC,EAAAA,EAAAA,IAAI,IACXC,IAAcD,EAAAA,EAAAA,IAAI,MAClBE,IAAUF,EAAAA,EAAAA,IAAI,MACdG,IAAeH,EAAAA,EAAAA,IAAI,IACnBI,IAAcJ,EAAAA,EAAAA,IAAI,IAClBK,IAAgBL,EAAAA,EAAAA,IAAI,OACpBM,IAAeN,EAAAA,EAAAA,KAAI,GACnBO,IAAmBP,EAAAA,EAAAA,KAAI,GACvBQ,IAAsBR,EAAAA,EAAAA,KAAI,GAC1BS,IAAgBT,EAAAA,EAAAA,KAAI,GACpBU,IAAYV,EAAAA,EAAAA,IAAI,MAChBW,IAAYX,EAAAA,EAAAA,IAAI,MAGhBY,IAAcZ,EAAAA,EAAAA,IAAI,GAClBa,IAAWb,EAAAA,EAAAA,IAAI,IAGfc,GAAa,CACjB,MAAO,CAAEC,KAAM,SAAUC,KAAM,UAAWC,MAAO,WACjD,MAAO,CAAEF,KAAM,UAAWC,KAAM,UAAWC,MAAO,eAClD,OAAQ,CAAEF,KAAM,UAAWC,KAAM,cAAeC,MAAO,aACvD,OAAQ,CAAEF,KAAM,OAAQC,KAAM,SAAUC,MAAO,eAC/C,MAAO,CAAEF,KAAM,OAAQC,KAAM,cAAeC,MAAO,WAI/CC,GAAgB,CACpB,CAAEC,MAAO,MAAOC,MAAO,MAAOJ,KAAM,WACpC,CAAEG,MAAO,MAAOC,MAAO,MAAOJ,KAAM,WACpC,CAAEG,MAAO,OAAQC,MAAO,OAAQJ,KAAM,eACtC,CAAEG,MAAO,OAAQC,MAAO,OAAQJ,KAAM,UACtC,CAAEG,MAAO,MAAOC,MAAO,MAAOJ,KAAM,gBAIhCK,IAAarB,EAAAA,EAAAA,IAAI,CACrBsB,GAAI,GACJC,OAAQ,GACRC,OAAQ,KAIJC,IAAe7B,EAAAA,EAAAA,IAAS,KACrB,CACL8B,QAAS3B,GAAKqB,MAAMO,OAAOC,GAAsB,QAAfA,EAAIL,QACtCM,WAAY9B,GAAKqB,MAAMO,OAAOC,GAAsB,QAAfA,EAAIL,QACzCO,UAAW/B,GAAKqB,MAAMO,OAAOC,GAAsB,SAAfA,EAAIL,QACxCQ,YAAahC,GAAKqB,MAAMO,OAAOC,GAAsB,SAAfA,EAAIL,QAC1CS,OAAQjC,GAAKqB,MAAMO,OAAOC,GAAsB,QAAfA,EAAIL,WAInCU,IAAYrC,EAAAA,EAAAA,IAAS,KAClB,CACLsC,MAAOnC,GAAKqB,MAAMe,OAClBT,QAASD,GAAaL,MAAMM,QAAQS,OACpCN,WAAYJ,GAAaL,MAAMS,WAAWM,OAC1CL,UAAWL,GAAaL,MAAMU,UAAUK,OACxCJ,YAAaN,GAAaL,MAAMW,YAAYI,OAC5CH,OAAQP,GAAaL,MAAMY,OAAOG,UAKhCC,IAAYxC,EAAAA,EAAAA,IAAS,IAAM,CAC/B,CAAEmB,KAAM,QAASC,KAAM,UAAWI,MAAOa,GAAUb,MAAMc,MAAOf,MAAO,SACvE,CAAEJ,KAAM,UAAWC,KAAM,UAAWI,MAAOa,GAAUb,MAAMM,QAASP,MAAO,OAC3E,CAAEJ,KAAM,cAAeC,KAAM,UAAWI,MAAOa,GAAUb,MAAMS,WAAYV,MAAO,OAClF,CAAEJ,KAAM,YAAaC,KAAM,cAAeI,MAAOa,GAAUb,MAAMU,UAAWX,MAAO,OACnF,CAAEJ,KAAM,cAAeC,KAAM,SAAUI,MAAOa,GAAUb,MAAMW,YAAaZ,MAAO,QAClF,CAAEJ,KAAM,SAAUC,KAAM,cAAeI,MAAOa,GAAUb,MAAMY,OAAQb,MAAO,SAIzEkB,IAAqBzC,EAAAA,EAAAA,IAAS,KAClC,IAAKQ,GAAYgB,MAAO,OAAOjB,GAAaiB,MAE5C,MAAMkB,EAAQlC,GAAYgB,MAAMmB,cAChC,OAAOpC,GAAaiB,MAAMO,OAAOC,GAC/BA,EAAIY,KAAKD,cAAcE,SAASH,IAChCV,EAAIc,cAAcH,cAAcE,SAASH,IACzCK,OAAOf,EAAIN,IAAImB,SAASH,MAKtBM,IAAchD,EAAAA,EAAAA,IAAS,KAC3B,MAAMiD,GAAcjC,GAAYQ,MAAQ,GAAKP,GAASO,MAChD0B,EAAWD,EAAahC,GAASO,MACvC,OAAOiB,GAAmBjB,MAAM2B,MAAMF,EAAYC,KAI9CE,GAAYC,UAChB,GAAKtD,EAAQyB,OAAOE,GAApB,CAEAhB,GAAac,OAAQ,EACrB,IACE,IAAK7B,IAAUA,EAAM2D,KAEnB,YADAC,EAAAA,GAAUC,MAAM,aAIlB,MAAMC,QAAiB9D,EAAM2D,KAAKI,QAAQ,CACxC3D,QAASA,EAAQyB,MAAME,KAGD,MAApB+B,EAAS9B,SACXxB,GAAKqB,MAAQiC,EAASE,KACtBC,GAAWnD,GAAce,OAE7B,CAAE,MAAOgC,GACPD,EAAAA,GAAUC,MAAM,aAChBK,QAAQL,MAAM,wBAAyBA,EACzC,CAAE,QACA9C,GAAac,OAAQ,CACvB,CAtB8B,GAyB1BoC,GAAc7B,IAIlB,OAHAtB,GAAce,MAAQO,EACtBf,GAAYQ,MAAQ,EAEZO,GACN,IAAK,UACHxB,GAAaiB,MAAQK,GAAaL,MAAMM,QACxC,MACF,IAAK,aACHvB,GAAaiB,MAAQK,GAAaL,MAAMS,WACxC,MACF,IAAK,YACH1B,GAAaiB,MAAQK,GAAaL,MAAMU,UACxC,MACF,IAAK,cACH3B,GAAaiB,MAAQK,GAAaL,MAAMW,YACxC,MACF,IAAK,SACH5B,GAAaiB,MAAQK,GAAaL,MAAMY,OACxC,MACF,QACE7B,GAAaiB,MAAQrB,GAAKqB,QAI1BsC,GAAcT,UAClBhD,GAAYmB,MAAQQ,EACpBrB,GAAiBa,OAAQ,EAEzB,IACE,IAAK7B,IAAUA,EAAM2D,KAAM,OAE3B,MAAMG,QAAiB9D,EAAM2D,KAAKS,WAAW,CAAE/B,IAAKA,EAAIN,KAChC,MAApB+B,EAAS9B,QAAkB8B,EAASE,KAAKpB,OAAS,EACpDjC,GAAQkB,MAAQiC,EAASE,KAEzBrD,GAAQkB,MAAQ,EAEpB,CAAE,MAAOgC,GACPD,EAAAA,GAAUC,MAAM,eAChBK,QAAQL,MAAM,4BAA6BA,GAC3ClD,GAAQkB,MAAQ,EAClB,GAGIwC,GAAoBhC,IACxBP,GAAWD,MAAQ,CACjBE,GAAIM,EAAIN,GACRC,OAAQK,EAAIL,OACZC,OAAQ,IAEVhB,GAAoBY,OAAQ,GAGxByC,GAAYZ,UAChB,GAAK5B,GAAWD,MAAME,GAEtB,GAAKD,GAAWD,MAAMI,OAAOsC,OAA7B,CAKArD,GAAcW,OAAQ,EACtB,IACE,IAAK7B,IAAUA,EAAM2D,KAAM,OAE3B,MAAMG,QAAiB9D,EAAM2D,KAAKW,UAAUxC,GAAWD,MAAME,GAAID,GAAWD,OAE5E,GAAwB,MAApBiC,EAAS9B,SACX4B,EAAAA,GAAUY,QAAQ,CAChBC,QAAS,YACTjD,KAAM,UACNkD,SAAU,MAEZzD,GAAoBY,OAAQ,QAGtB4B,KAGFzC,GAAiBa,OAASnB,GAAYmB,OAAO,CAC/C,MAAM8C,QAAa3E,EAAM2D,KAAKS,WAAW,CAAE/B,IAAK3B,GAAYmB,MAAME,KAClE,GAAoB,MAAhB4C,EAAK3C,OAAgB,CACvBrB,GAAQkB,MAAQ8C,EAAKX,KAGrB,MAAMY,EAAapE,GAAKqB,MAAMgD,KAAKC,GAAKA,EAAE/C,KAAOrB,GAAYmB,MAAME,IAC/D6C,IACFlE,GAAYmB,MAAQ+C,EAExB,CACF,CAEJ,CAAE,MAAOf,GACPD,EAAAA,GAAUC,MAAM,aAChBK,QAAQL,MAAM,wBAAyBA,EACzC,CAAE,QACA3C,GAAcW,OAAQ,CACxB,CAtCA,MAFE+B,EAAAA,GAAUmB,QAAQ,YA2ChBC,GAAYtB,UAChB3C,GAAac,OAAQ,EACrB,IACE,IAAK7B,IAAUA,EAAM2D,KAAM,OAE3B,MAAMG,QAAiB9D,EAAM2D,KAAKqB,UAAUjD,GAEpB,MAApB+B,EAAS9B,SACX4B,EAAAA,GAAUY,QAAQ,CAChBC,QAAS,OACTjD,KAAM,UACNkD,SAAU,YAENjB,KAGF/C,GAAYmB,OAASnB,GAAYmB,MAAME,KAAOA,IAChDf,GAAiBa,OAAQ,GAG/B,CAAE,MAAOgC,GACPD,EAAAA,GAAUC,MAAM,WAChBK,QAAQL,MAAM,wBAAyBA,EACzC,CAAE,QACA9C,GAAac,OAAQ,CACvB,GAGIoD,GAAeA,KACnB,IAAK9D,GAAUU,QAAUT,GAAUS,MAAO,OAC1C,IAAK7B,IAAUA,EAAMkF,OAAQ,OAG7B,MAAMC,EAAY,CAChB3E,GAAKqB,MAAMe,OACXV,GAAaL,MAAMU,UAAUK,OAC7BV,GAAaL,MAAMS,WAAWM,OAC9BV,GAAaL,MAAMM,QAAQS,OAC3BV,GAAaL,MAAMW,YAAYI,OAC/BV,GAAaL,MAAMY,OAAOG,QAGtBwC,EAAc,CAAC,QAAS,OAAQ,MAAO,MAAO,OAAQ,OAG5DpF,EAAMkF,OAAOG,OAAOlE,GAAUU,MAAOsD,EAAWC,GAGhDpF,EAAMkF,OAAOI,OAAOlE,GAAUS,MAAO,CACnC,CAAEA,MAAOK,GAAaL,MAAMU,UAAUK,OAAQ2C,KAAM,QACpD,CAAE1D,MAAOK,GAAaL,MAAMS,WAAWM,OAAQ2C,KAAM,OACrD,CAAE1D,MAAOK,GAAaL,MAAMM,QAAQS,OAAQ2C,KAAM,OAClD,CAAE1D,MAAOK,GAAaL,MAAMW,YAAYI,OAAQ2C,KAAM,QACtD,CAAE1D,MAAOK,GAAaL,MAAMY,OAAOG,OAAQ2C,KAAM,UAK/CC,GAAoBC,IACxBnE,GAASO,MAAQ4D,GAGbC,GAAuBC,IAC3BtE,GAAYQ,MAAQ8D,GAIhBC,GAAiB5D,GACdT,GAAWS,IAASR,MAAQ,OAO/BqE,GAAkB7D,GACfT,GAAWS,IAASN,OAAS,UAGhCoE,GAAmBC,GACnBA,EAAO7C,SAAS,QAAgB,UAChC6C,EAAO7C,SAAS,OAAe,UAC/B6C,EAAO7C,SAAS,OAAe,SAC/B6C,EAAO7C,SAAS,MAAc,OAC3B,UAGH8C,GAAmBD,GACnBA,EAAO7C,SAAS,QAAgB,cAChC6C,EAAO7C,SAAS,OAAe,UAC/B6C,EAAO7C,SAAS,OAAe,UAC/B6C,EAAO7C,SAAS,MAAc,cAC9B6C,EAAO7C,SAAS,QAAgB,SAC7B,aAGH+C,GAAcA,EAAGC,SACd,sBAAsBL,GAAeK,EAAIlE,WAIlDmE,EAAAA,EAAAA,IAAUzC,gBACFD,MACN2C,EAAAA,EAAAA,IAAS,KACPnB,UAKJoB,EAAAA,EAAAA,IAAM,IAAM7F,GAAKqB,MAAO,MACtBuE,EAAAA,EAAAA,IAAS,KACPnB,QAED,CAAEqB,MAAM,KAGXD,EAAAA,EAAAA,IAAMxF,GAAa,KACjBQ,GAAYQ,MAAQ,IAItB,MAAM0E,GAASC,GACRxG,GAAUA,EAAMyG,OACdzG,EAAMyG,OAAOF,MAAMC,GADUA,EAKhCE,GAASF,GACRxG,GAAUA,EAAMyG,OACdzG,EAAMyG,OAAOC,MAAQ1G,EAAMyG,OAAOC,MAAMF,GAAQxG,EAAMyG,OAAOF,MAAMC,GADtCA,E,quBAtuBpCG,EAAAA,EAAAA,IAiXM,MAjXNC,EAiXM,EAhXJC,EAAAA,EAAAA,IA+WeC,GAAA,CA/WDC,OAAO,sBAAoB,C,iBACvC,IAwNM,EAxNNC,EAAAA,EAAAA,IAwNM,MAxNNC,EAwNM,EAtNJD,EAAAA,EAAAA,IAYM,MAZNE,EAYM,C,eAXJF,EAAAA,EAAAA,IAEM,OAFDtF,MAAM,eAAa,EACtBsF,EAAAA,EAAAA,IAAkC,MAA9BtF,MAAM,cAAa,Y,KAEzBsF,EAAAA,EAAAA,IAOM,MAPNG,EAOM,EANJN,EAAAA,EAAAA,IAKWO,EAAA,CALAvF,MAAOa,GAAAb,MAAUM,QAAUkF,OAA8B,IAAtB3E,GAAAb,MAAUM,QAAeT,MAAM,iB,kBAC3E,IAGY,EAHZmF,EAAAA,EAAAA,IAGYS,EAAA,CAHD9F,KAAK,SAASiE,KAAK,QAAQ/D,MAAM,c,kBAC1C,IAA8B,EAA9BmF,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAC,EAAAA,Y,OACpBT,EAAAA,EAAAA,IAAwC,YAAlC,QAAIU,EAAAA,EAAAA,IAAGhF,GAAAb,MAAUM,SAAO,K,wCAOtC6E,EAAAA,EAAAA,IAgBM,MAhBNW,EAgBM,EAfJd,EAAAA,EAAAA,IAcSe,GAAA,CAdAC,OAAQ,IAAE,C,iBACC,IAAkC,G,aAApDlB,EAAAA,EAAAA,IAYSmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAZiClF,GAAAhB,MAAS,CAAzBmG,EAAMC,M,WAAhCC,EAAAA,EAAAA,IAYSC,EAAA,CAZAC,KAAM,EAAuCC,IAAKJ,G,kBACzD,IAUM,EAVNjB,EAAAA,EAAAA,IAUM,OAVDtF,OAAK4G,EAAAA,EAAAA,IAAA,CAAC,YAAW,aAAsBN,EAAKxG,U,EAC/CwF,EAAAA,EAAAA,IAIM,MAJNuB,EAIM,EAHJ1B,EAAAA,EAAAA,IAEUU,EAAA,M,iBADR,IAA6B,G,WAA7BW,EAAAA,EAAAA,KAA6BM,EAAAA,EAAAA,IAAbR,EAAKvG,U,cAGzBuF,EAAAA,EAAAA,IAGM,MAHNyB,EAGM,EAFJzB,EAAAA,EAAAA,IAA8C,MAA9C0B,GAA8ChB,EAAAA,EAAAA,IAAnBM,EAAKnG,OAAK,IACrCmF,EAAAA,EAAAA,IAA8C,MAA9C2B,GAA8CjB,EAAAA,EAAAA,IAAnBM,EAAKpG,OAAK,M,mCAQ/CoF,EAAAA,EAAAA,IA6BM,MA7BN4B,EA6BM,EA5BJ/B,EAAAA,EAAAA,IA2BSe,GAAA,CA3BAC,OAAQ,IAAE,C,iBACjB,IAYS,EAZThB,EAAAA,EAAAA,IAYSsB,EAAA,CAZAC,KAAM,IAAE,C,iBACf,IAUM,EAVNpB,EAAAA,EAAAA,IAUM,MAVN6B,EAUM,EATJ7B,EAAAA,EAAAA,IAOM,MAPN8B,EAOM,C,eANJ9B,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAIM,MAJN+B,EAIM,EAHJlC,EAAAA,EAAAA,IAEYS,EAAA,CAFD7B,KAAK,QAAQuD,KAAA,I,kBACtB,IAA8B,EAA9BnC,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAyB,EAAAA,Y,mBAI1BjC,EAAAA,EAAAA,IAAiD,OAA5CtF,MAAM,gB,QAAoB,YAAJjB,IAAIU,I,qBAGnC0F,EAAAA,EAAAA,IAYSsB,EAAA,CAZAC,KAAM,IAAE,C,iBACf,IAUM,EAVNpB,EAAAA,EAAAA,IAUM,MAVNkC,EAUM,EATJlC,EAAAA,EAAAA,IAOM,MAPNmC,EAOM,C,eANJnC,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAIM,MAJNoC,EAIM,EAHJvC,EAAAA,EAAAA,IAEYS,EAAA,CAFD7B,KAAK,QAAQuD,KAAA,I,kBACtB,IAA8B,EAA9BnC,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAyB,EAAAA,Y,mBAI1BjC,EAAAA,EAAAA,IAAiD,OAA5CtF,MAAM,gB,QAAoB,YAAJjB,IAAIW,I,+BAOvC4F,EAAAA,EAAAA,IAmJM,MAnJNqC,EAmJM,EAlJJrC,EAAAA,EAAAA,IA2BM,MA3BNsC,EA2BM,EA1BJtC,EAAAA,EAAAA,IAGM,MAHNuC,EAGM,C,eAFJvC,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAAsD,OAAtDwC,EAA0B,MAAE9B,EAAAA,EAAAA,IAAGlH,GAAAqB,MAAKe,QAAS,KAAE,MAGjDoE,EAAAA,EAAAA,IAoBM,MApBNyC,EAoBM,EAnBJ5C,EAAAA,EAAAA,IAOiB6C,GAAA,C,WAPQ5I,GAAAe,M,qCAAAf,GAAae,MAAA8H,GAAElE,KAAK,QAASmE,SAAMC,EAAA,KAAAA,EAAA,GAAGC,GAAQ7F,GAAW6F,K,kBAChF,IAAiD,EAAjDjD,EAAAA,EAAAA,IAAiDkD,GAAA,CAAhCnI,MAAM,OAAK,C,iBAAC,IAAEiI,EAAA,MAAAA,EAAA,M,QAAF,S,eAC7BhD,EAAAA,EAAAA,IAAsDkD,GAAA,CAArCnI,MAAM,WAAS,C,iBAAC,IAAGiI,EAAA,MAAAA,EAAA,M,QAAH,U,eACjChD,EAAAA,EAAAA,IAAyDkD,GAAA,CAAxCnI,MAAM,cAAY,C,iBAAC,IAAGiI,EAAA,MAAAA,EAAA,M,QAAH,U,eACpChD,EAAAA,EAAAA,IAAwDkD,GAAA,CAAvCnI,MAAM,aAAW,C,iBAAC,IAAGiI,EAAA,MAAAA,EAAA,M,QAAH,U,eACnChD,EAAAA,EAAAA,IAA2DkD,GAAA,CAA1CnI,MAAM,eAAa,C,iBAAC,IAAIiI,EAAA,MAAAA,EAAA,M,QAAJ,W,eACrChD,EAAAA,EAAAA,IAAqDkD,GAAA,CAApCnI,MAAM,UAAQ,C,iBAAC,IAAGiI,EAAA,MAAAA,EAAA,M,QAAH,U,wCAGlChD,EAAAA,EAAAA,IASWmD,GAAA,C,WARAnJ,GAAAgB,M,qCAAAhB,GAAWgB,MAAA8H,GACpBM,YAAY,WACZvI,MAAM,eACNwI,UAAA,I,CAEWC,QAAMC,EAAAA,EAAAA,IACf,IAA6B,EAA7BvD,EAAAA,EAAAA,IAA6BU,EAAA,M,iBAApB,IAAU,EAAVV,EAAAA,EAAAA,KAAUW,EAAAA,EAAAA,IAAA6C,EAAAA,W,yDAM3BnC,EAAAA,EAAAA,IAwGWoC,GAAA,CAvGRtG,KAAMX,GAAAxB,MACP0I,MAAA,eACA,UAAQ,KAEP,iBAAgBtE,GAChBuE,WAAWrG,GACZzC,MAAM,a,kBAEN,IAQkB,EARlBmF,EAAAA,EAAAA,IAQkB4D,GAAA,CARDjJ,KAAK,UAAQ,CACjBkJ,SAAON,EAAAA,EAAAA,IAAEO,GAAK,EACvB3D,EAAAA,EAAAA,IAIM,MAJN4D,EAIM,EAHJ5D,EAAAA,EAAAA,IAAmD,U,eAAhDA,EAAAA,EAAAA,IAAuB,cAAf,UAAM,K,QAAS,KAACU,EAAAA,EAAAA,IAAGiD,EAAMzE,IAAIjD,MAAI,MAC5C+D,EAAAA,EAAAA,IAA2D,U,eAAxDA,EAAAA,EAAAA,IAAsB,cAAd,SAAK,K,QAAS,KAACU,EAAAA,EAAAA,IAAGiD,EAAMzE,IAAI/C,eAAa,MACpD6D,EAAAA,EAAAA,IAAgE,U,eAA7DA,EAAAA,EAAAA,IAAsB,cAAd,SAAK,K,QAAS,KAACU,EAAAA,EAAAA,IAAGnB,GAAMoE,EAAMzE,IAAI2E,cAAW,S,OAK9DhE,EAAAA,EAAAA,IAIkB4D,GAAA,CAJD7I,MAAM,GAAGkJ,MAAM,M,CACnBJ,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvB/D,EAAAA,EAAAA,IAA4F,OAAvFtF,OAAK4G,EAAAA,EAAAA,IAAA,CAAC,iBAAgB,cAAyBzC,GAAekF,EAAM7E,IAAIlE,W,iBAIjF6E,EAAAA,EAAAA,IAA4C4D,GAAA,CAA3BO,KAAK,KAAKpJ,MAAM,UAEjCiF,EAAAA,EAAAA,IAIkB4D,GAAA,CAJDO,KAAK,OAAOpJ,MAAM,QAAQ,4B,CAC9B8I,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvB/D,EAAAA,EAAAA,IAAiD,MAAjDiE,GAAiDvD,EAAAA,EAAAA,IAAvBqD,EAAM7E,IAAIjD,MAAI,K,OAI5C4D,EAAAA,EAAAA,IAA2F4D,GAAA,CAA1EO,KAAK,gBAAgBpJ,MAAM,OAAO,YAAU,MAAM,8BAEnEiF,EAAAA,EAAAA,IAOkB4D,GAAA,CAPD7I,MAAM,QAAM,CAChB8I,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvB/D,EAAAA,EAAAA,IAGM,MAHNkE,EAGM,EAFJrE,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQsE,M,OACjBnE,EAAAA,EAAAA,IAA+C,aAAAU,EAAAA,EAAAA,IAAtCnB,GAAMwE,EAAM7E,IAAI2E,cAAW,O,OAK1ChE,EAAAA,EAAAA,IAWkB4D,GAAA,CAXD7I,MAAM,KAAKkJ,MAAM,MAAMM,MAAM,U,CACjCV,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvBlE,EAAAA,EAAAA,IAOSwE,GAAA,CANN7J,KAAMoE,GAAcmF,EAAM7E,IAAIlE,QAC/BsJ,OAAO,QACP5J,OAAK4G,EAAAA,EAAAA,IAAA,CAAC,aAAY,UACEzC,GAAekF,EAAM7E,IAAIlE,W,kBAE7C,IAAmC,EAAnCgF,EAAAA,EAAAA,IAAmC,aAAAU,EAAAA,EAAAA,IAA1BqD,EAAM7E,IAAIlE,QAAM,K,qCAK/B6E,EAAAA,EAAAA,IA8CkB4D,GAAA,CA9CDK,MAAM,MAAMM,MAAM,SAASG,MAAM,QAAQ3J,MAAM,M,CACnD8I,SAAON,EAAAA,EAAAA,IAAEW,GAAK,EACvB/D,EAAAA,EAAAA,IA0CM,MA1CNwE,EA0CM,EAzCJ3E,EAAAA,EAAAA,IASa4E,GAAA,CATDC,QAAQ,OAAOC,UAAU,O,kBACnC,IAOY,EAPZ9E,EAAAA,EAAAA,IAOYS,EAAA,CANVsE,OAAA,GACApK,KAAK,UACLqK,MAAA,GACCC,SAAKC,EAAAA,EAAAA,IAAApC,GAAOxF,GAAY4G,EAAM7E,KAAG,W,kBAElC,IAA2B,EAA3BW,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,KAAQW,EAAAA,EAAAA,IAAAwE,EAAAA,S,6CAIrBnF,EAAAA,EAAAA,IASa4E,GAAA,CATDC,QAAQ,OAAOC,UAAU,O,kBACnC,IAOY,EAPZ9E,EAAAA,EAAAA,IAOYS,EAAA,CANVsE,OAAA,GACApK,KAAK,UACLqK,MAAA,GACCC,SAAKC,EAAAA,EAAAA,IAAApC,GAAOtF,GAAiB0G,EAAM7E,KAAG,W,kBAEvC,IAA2B,EAA3BW,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,KAAQW,EAAAA,EAAAA,IAAAyE,EAAAA,S,6CAIrBpF,EAAAA,EAAAA,IAkBa4E,GAAA,CAlBDC,QAAQ,QAAQC,UAAU,O,kBACpC,IAgBgB,EAhBhB9E,EAAAA,EAAAA,IAgBgBqF,GAAA,CAfdC,MAAM,cACN,sBAAoB,KACpB,qBAAmB,KAClBC,UAAOzC,GAAE3E,GAAU+F,EAAM7E,IAAInE,K,CAEnBsK,WAASjC,EAAAA,EAAAA,IAClB,IAOY,EAPZvD,EAAAA,EAAAA,IAOYS,EAAA,CANVsE,OAAA,GACApK,KAAK,SACLqK,MAAA,GACCC,QAAKjC,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBAEX,IAA6B,EAA7BlF,EAAAA,EAAAA,IAA6BU,EAAA,M,iBAApB,IAAU,EAAVV,EAAAA,EAAAA,KAAUW,EAAAA,EAAAA,IAAA8E,EAAAA,W,yFA5FtBvL,GAAAc,UAsGbmF,EAAAA,EAAAA,IAUM,MAVNuF,EAUM,EATJ1F,EAAAA,EAAAA,IAQE2F,GAAA,CAPQ,eAAcnL,GAAAQ,M,sCAAAR,GAAWQ,MAAA8H,GACzB,YAAWrI,GAAAO,M,mCAAAP,GAAQO,MAAA8H,GAC1B,aAAY,CAAC,GAAI,GAAI,GAAI,IAC1B8C,OAAO,0CACN9J,MAAO/B,GAAAiB,MAAae,OACpB8J,aAAalH,GACbmH,gBAAgBjH,I,oDAOzBmB,EAAAA,EAAAA,IAmGY+F,GAAA,C,WAlGD5L,GAAAa,M,qCAAAb,GAAgBa,MAAA8H,GACzBkD,UAAU,MACVpH,KAAK,MACL,sBACA,eAAa,qB,CAEFqH,QAAM1C,EAAAA,EAAAA,IACf,IAUM,EAVNpD,EAAAA,EAAAA,IAUM,MAVN+F,EAUM,C,eATJ/F,EAAAA,EAAAA,IAAmC,MAA/BtF,MAAM,gBAAe,SAAK,IAEtBhB,GAAAmB,Q,WADRqG,EAAAA,EAAAA,IAOSmD,GAAA,C,MALN7J,KAAMoE,GAAclF,GAAAmB,MAAYG,QACjCsJ,OAAO,OACP5J,MAAM,qB,kBAEN,IAAwB,E,iBAArBhB,GAAAmB,MAAYG,QAAM,K,uDAK3B,IA6Ee,EA7Ef6E,EAAAA,EAAAA,IA6EeC,GAAA,CA7EDC,OAAO,uBAAqB,C,iBACxC,IA2EM,CA3EKrG,GAAAmB,Q,WAAX8E,EAAAA,EAAAA,IA2EM,MA3ENqG,EA2EM,EA1EJnG,EAAAA,EAAAA,IAgBUoG,GAAA,CAhBDvL,MAAM,wBAAwBwL,OAAO,S,CACjCJ,QAAM1C,EAAAA,EAAAA,IACf,IAEM,EAFNpD,EAAAA,EAAAA,IAEM,MAFNmG,EAEM,EADJnG,EAAAA,EAAAA,IAA+C,YAA3CH,EAAAA,EAAAA,IAAiCU,EAAA,M,iBAAxB,IAAc,EAAdV,EAAAA,EAAAA,KAAcW,EAAAA,EAAAA,IAAA4F,EAAAA,e,6BAAU,gB,iBAIzC,IAQkB,EARlBvG,EAAAA,EAAAA,IAQkBwG,GAAA,CARAC,OAAQ,EAAGC,OAAA,I,kBAC3B,IAAgF,EAAhF1G,EAAAA,EAAAA,IAAgF2G,GAAA,CAA1D5L,MAAM,UAAQ,C,iBAAC,IAAoB,E,iBAAjBlB,GAAAmB,MAAYE,IAAE,K,OACtD8E,EAAAA,EAAAA,IAA8D2G,GAAA,CAAxC5L,MAAM,OAAK,C,iBAAC,IAAKiI,EAAA,MAAAA,EAAA,M,QAAL,Y,eAClChD,EAAAA,EAAAA,IAAyF2G,GAAA,CAAnE5L,MAAM,QAAM,C,iBAAC,IAA+B,E,iBAA5BlB,GAAAmB,MAAYsB,eAAa,K,OAC/D0D,EAAAA,EAAAA,IAA8F2G,GAAA,CAAxE5L,MAAM,QAAM,C,iBAAC,IAAoC,E,iBAAjC2E,GAAM7F,GAAAmB,MAAYgJ,cAAW,K,OACnEhE,EAAAA,EAAAA,IAEuB2G,GAAA,CAFD5L,MAAM,QAASwG,KAAM,G,kBACzC,IAAyD,EAAzDpB,EAAAA,EAAAA,IAAyD,MAAzDyG,GAAyD/F,EAAAA,EAAAA,IAAzBhH,GAAAmB,MAAYoB,MAAI,K,uBAKtD4D,EAAAA,EAAAA,IAOUoG,GAAA,CAPDvL,MAAM,6BAA6BwL,OAAO,S,CACtCJ,QAAM1C,EAAAA,EAAAA,IACf,IAEM,EAFNpD,EAAAA,EAAAA,IAEM,MAFN0G,EAEM,EADJ1G,EAAAA,EAAAA,IAA8C,YAA1CH,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAmG,EAAAA,Y,6BAAU,kB,iBAGtC,IAA6D,EAA7D9G,EAAAA,EAAAA,IAA6D+G,EAAAA,EAAA,CAApDC,OAAQnN,GAAAmB,MAAYiM,KAAOC,SAAS,G,2BAGhCpN,GAAAkB,OAAWlB,GAAAkB,MAAQe,OAAS,I,WAA3CsF,EAAAA,EAAAA,IAqCU+E,GAAA,C,MArCoCvL,MAAM,4BAA4BwL,OAAO,S,CAC1EJ,QAAM1C,EAAAA,EAAAA,IACf,IAEM,EAFNpD,EAAAA,EAAAA,IAEM,MAFNgH,EAEM,EADJhH,EAAAA,EAAAA,IAA4C,YAAxCH,EAAAA,EAAAA,IAA8BU,EAAA,M,iBAArB,IAAW,EAAXV,EAAAA,EAAAA,KAAWW,EAAAA,EAAAA,IAAAmG,EAAAA,Y,6BAAU,gB,iBAItC,IA6Bc,EA7Bd9G,EAAAA,EAAAA,IA6BcoH,GAAA,M,iBA3BV,IAAoC,G,aADtCtH,EAAAA,EAAAA,IA2BmBmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA1BWpH,GAAAkB,MAAO,CAA3BqM,EAAUjG,M,WADpBC,EAAAA,EAAAA,IA2BmBiG,GAAA,CAzBhB9F,IAAKJ,EACLmG,UAAW1H,GAAMwH,EAASrD,aAC1BrJ,KAAMsE,GAAgBoI,EAASnI,QAC/BsI,QAAQ,EACR5I,KAAgB,IAAVwC,EAAc,QAAU,U,kBAE/B,IAkBU,EAlBVpB,EAAAA,EAAAA,IAkBUoG,GAAA,CAlBDvL,MAAM,sBAAoB,C,iBACjC,IAGK,EAHLsF,EAAAA,EAAAA,IAGK,KAHLsH,EAGK,EAFHzH,EAAAA,EAAAA,IAAuEU,EAAA,M,iBAA9D,IAAoD,G,WAApDW,EAAAA,EAAAA,KAAoDM,EAAAA,EAAAA,IAApCxC,GAAgBkI,EAASnI,a,oBAAqB,KACvE2B,EAAAA,EAAAA,IAAGwG,EAASnI,QAAM,KAGXmI,EAASjM,S,WAAlB0E,EAAAA,EAAAA,IAA2E,IAA3E4H,GAA2E7G,EAAAA,EAAAA,IAAtBwG,EAASjM,QAAM,K,gBAEpE+E,EAAAA,EAAAA,IASM,MATNwH,EASM,EARJxH,EAAAA,EAAAA,IAGO,OAHPyH,EAGO,EAFL5H,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,KAAQW,EAAAA,EAAAA,IAAAkH,EAAAA,S,eAAU,KAC3BhH,EAAAA,EAAAA,IAAGwG,EAASS,aAAW,MAEzB3H,EAAAA,EAAAA,IAGO,OAHP4H,EAGO,EAFL/H,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,IAAQsE,M,eAAU,KAC3BzD,EAAAA,EAAAA,IAAGnB,GAAM2H,EAASrD,cAAW,S,0FAQzC3C,EAAAA,EAAAA,IAAmE2G,GAAA,C,MAAlDC,YAAY,SAAU,aAAY,QAEnD9H,EAAAA,EAAAA,IAKM,MALN+H,EAKM,EAJJlI,EAAAA,EAAAA,IAA0ES,EAAA,CAA/D7B,KAAK,UAAWqG,QAAKjC,EAAA,KAAAA,EAAA,GAAAF,GAAE3I,GAAAa,OAAmB,I,kBAAO,IAAEgI,EAAA,MAAAA,EAAA,M,QAAF,S,eAC5DhD,EAAAA,EAAAA,IAEYS,EAAA,CAFD7B,KAAK,UAAUjE,KAAK,UAAWsK,QAAKjC,EAAA,KAAAA,EAAA,GAAAF,GAAEtF,GAAiB3D,GAAAmB,S,kBAChE,IAA2B,EAA3BgF,EAAAA,EAAAA,IAA2BU,EAAA,M,iBAAlB,IAAQ,EAARV,EAAAA,EAAAA,KAAQW,EAAAA,EAAAA,IAAAyE,EAAAA,S,6BAAU,a,qEAQrCpF,EAAAA,EAAAA,IA4CYmI,GAAA,C,WA3CD/N,GAAAY,M,uCAAAZ,GAAmBY,MAAA8H,GAC5BwC,MAAM,UACNrB,MAAM,MACN,sBACA,eAAa,iB,CA+BFmE,QAAM7E,EAAAA,EAAAA,IACf,IAKM,EALNpD,EAAAA,EAAAA,IAKM,MALNkI,EAKM,EAJJrI,EAAAA,EAAAA,IAA8DS,EAAA,CAAlDwE,QAAKjC,EAAA,MAAAA,EAAA,IAAAF,GAAE1I,GAAAY,OAAsB,I,kBAAO,IAAEgI,EAAA,MAAAA,EAAA,M,QAAF,S,eAChDhD,EAAAA,EAAAA,IAEYS,EAAA,CAFD9F,KAAK,UAAWsK,QAAOxH,GAAY6K,QAASjO,GAAAW,O,kBACrD,IAA4B,EAA5BgF,EAAAA,EAAAA,IAA4BU,EAAA,M,iBAAnB,IAAS,EAATV,EAAAA,EAAAA,KAASW,EAAAA,EAAAA,IAAA4H,EAAAA,U,6BAAU,a,iDAjClC,IA2BU,EA3BVvI,EAAAA,EAAAA,IA2BUwI,GAAA,CA3BAC,MAAOxN,GAAAD,MAAY,iBAAe,MAAM,iBAAYH,MAAM,e,kBAClE,IAee,EAffmF,EAAAA,EAAAA,IAee0I,GAAA,CAfD3N,MAAM,QAAM,C,iBACxB,IAaM,EAbNoF,EAAAA,EAAAA,IAaM,MAbNwI,EAaM,G,WAZJ7I,EAAAA,EAAAA,IAWMmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAVoBpG,GAAa,CAA7BK,EAAQqG,KADlBrB,EAAAA,EAAAA,IAWM,OATHqB,IAAKA,EACN3G,OAAK4G,EAAAA,EAAAA,IAAA,CAAC,gBAAe,QACDxG,GAAAD,MAAWG,SAAWA,EAAOH,SAChDiK,QAAKnC,GAAE7H,GAAAD,MAAWG,OAASA,EAAOH,O,EAEnCgF,EAAAA,EAAAA,IAEUU,EAAA,CAFD7F,MAAM,eAAa,C,iBAC1B,IAA+B,G,WAA/BwG,EAAAA,EAAAA,KAA+BM,EAAAA,EAAAA,IAAfxG,EAAOP,U,YAEzBuF,EAAAA,EAAAA,IAAkD,MAAlDyI,GAAkD/H,EAAAA,EAAAA,IAArB1F,EAAOJ,OAAK,I,uBAK/CiF,EAAAA,EAAAA,IAQe0I,GAAA,CARD3N,MAAM,QAAM,C,iBACxB,IAME,EANFiF,EAAAA,EAAAA,IAMEmD,GAAA,C,WALSlI,GAAAD,MAAWI,O,qCAAXH,GAAAD,MAAWI,OAAM0H,GAC1BnI,KAAK,WACJkO,SAAU,CAAAC,QAAA,EAAAC,QAAA,GACX3F,YAAY,aACZvI,MAAM,gB,qGC7VpB,MAAMmO,IAA2B,QAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,S,0ICSUnO,MAAM,e,GAGP6I,MAAA,mB,aAsCA7I,MAAM,e,SAM6B6I,MAAA,mB,SAOLA,MAAA,mB,SACKA,MAAA,mB,SAC3BA,MAAA,mB,SAK0BA,MAAA,mB,SAC1BA,MAAA,mB,SASRA,MAAA,0D,GAcI7I,MAAM,iB,0ZArGhBmF,EAAAA,EAAAA,IAsFQiJ,EAAA,CAtFC,cAAY,KAAKvF,MAAA,uBAA2B/I,KAAK,cAAcK,MAAM,KAAK4D,KAAK,Q,kBACzF,IAYgB,CAZkB,OAAfsK,EAAAlC,OAAOrM,O,WAA1B0G,EAAAA,EAAAA,IAYgB8H,EAAA,C,MAZyBpO,MAAM,MAAM2D,KAAK,M,kBACtD,IAUM,CAVKwK,EAAAlC,OAAOoC,kB,WAAlBtJ,EAAAA,EAAAA,IAUM,MAAAC,EAAA,CATOmJ,EAAAlC,OAAOoC,gBAAgB,gBAAgB/M,SAAS,sB,WAA3DyD,EAAAA,EAAAA,IAGM,MAAAM,EAAA,EADJJ,EAAAA,EAAAA,IAA4FqJ,EAAA,CAAnFC,UAAU,E,WAAeJ,EAAAlC,OAAOuC,c,qCAAPL,EAAAlC,OAAOuC,cAAazG,GAAE0G,KAAK,OAAOC,MAAM,U,uCAE5E3J,EAAAA,EAAAA,IAIM,MAAAO,EAAA,EAHJL,EAAAA,EAAAA,IAEeC,EAAA,CAFDC,OAAO,QAAUwJ,QAAK1G,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAA0G,EAA1GlF,EAAAA,EAAAA,IAA0GqJ,EAAA,CAAjGC,UAAU,EAAMK,UAAQT,EAAAlC,OAAOuC,cAAeC,KAAK,OAAOC,MAAM,SAASvJ,OAAO,S,6EAKjE,OAAfgJ,EAAAlC,OAAOrM,O,WAA1B0G,EAAAA,EAAAA,IAWc8H,EAAA,C,MAX2BpO,MAAM,MAAM2D,KAAK,M,kBACtD,IASe,EATfsB,EAAAA,EAAAA,IASeC,EAAA,CATDC,OAAO,QAASwJ,QAAK1G,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAOI,CAP2BgE,EAAAlC,OAAOoC,kB,WAAtCtJ,EAAAA,EAAAA,IAOI,MAPJQ,EAOI,G,aANLR,EAAAA,EAAAA,IAKMmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IALsBgI,EAAAlC,OAAOoC,gBAAe,CAArCpO,EAAOwG,M,WAApB1B,EAAAA,EAAAA,IAKM,aAJLE,EAAAA,EAAAA,IAGSwE,EAAA,CAHDd,MAAA,qBAAyB/I,KAAK,Q,kBACrC,IAAgD,EAAhDwF,EAAAA,EAAAA,IAAgD,IAAhDW,GAAgDD,EAAAA,EAAAA,IAAlBW,EAAM,OAAH,IACjCrB,EAAAA,EAAAA,IAAwB,aAAAU,EAAAA,EAAAA,IAAf7F,GAAK,K,yEAMgB,OAAfkO,EAAAlC,OAAOrM,O,WAA1B0G,EAAAA,EAAAA,IA4Bc8H,EAAA,C,MA5B2BpO,MAAM,OAAO2D,KAAK,M,kBACvD,IA0Be,EA1BfsB,EAAAA,EAAAA,IA0BeC,EAAA,CA1BDC,OAAO,QAASwJ,QAAK1G,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACzC,IAwBI,CAxBOgE,EAAAlC,OAAO4C,gB,WAAlB9J,EAAAA,EAAAA,IAwBI,MAAA4B,EAAA,EAvBL1B,EAAAA,EAAAA,IAsBc6J,EAAA,C,WAtBQC,EAAAC,Y,qCAAAD,EAAAC,YAAWjH,GAAEjI,MAAM,e,kBACxC,IAMmB,EANnBmF,EAAAA,EAAAA,IAMmBgK,EAAA,CANDtL,KAAK,KAAG,CACd4G,OAAK/B,EAAAA,EAAAA,IACf,IAAcP,EAAA,KAAAA,EAAA,KAAd7C,EAAAA,EAAAA,IAAc,SAAX,WAAO,M,iBAEX,IAA+C,EAA/CA,EAAAA,EAAAA,IAA+C,WAA1C,qBAAiBU,EAAAA,EAAAA,IAAGqI,EAAAlC,OAAOiD,QAAM,IACtC9J,EAAAA,EAAAA,IAAyC,WAApC,kBAAcU,EAAAA,EAAAA,IAAGqI,EAAAlC,OAAOkD,KAAG,K,OAEjClK,EAAAA,EAAAA,IAOmBgK,EAAA,CAPDtL,KAAK,KAAG,CACd4G,OAAK/B,EAAAA,EAAAA,IACf,IAAsBP,EAAA,MAAAA,EAAA,MAAtB7C,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEd,IAA8C,G,aAAnDL,EAAAA,EAAAA,IAEMmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFsBgI,EAAAlC,OAAOmD,gBAAe,CAArCnP,EAAOwG,M,WAApB1B,EAAAA,EAAAA,IAEM,aADLK,EAAAA,EAAAA,IAAsC,aAAAU,EAAAA,EAAAA,IAA7BW,EAAM,MAAQxG,GAAK,O,eAG9BgF,EAAAA,EAAAA,IAKmBgK,EAAA,CALDtL,KAAK,KAAG,CACd4G,OAAK/B,EAAAA,EAAAA,IACf,IAAsBP,EAAA,MAAAA,EAAA,MAAtB7C,EAAAA,EAAAA,IAAsB,SAAnB,mBAAe,M,iBAEnB,IAAuC,EAAvCA,EAAAA,EAAAA,IAAuC,aAAAU,EAAAA,EAAAA,IAA9BqI,EAAAlC,OAAO4C,eAAa,K,oFAMjC5J,EAAAA,EAAAA,IAYcmJ,EAAA,CAZDpO,MAAM,MAAI,C,iBACtB,IAUe,EAVfiF,EAAAA,EAAAA,IAUeC,EAAA,CAVDC,OAAO,QAASwJ,QAAK1G,EAAA,KAAAA,EAAA,IAAAkC,EAAAA,EAAAA,IAAN,OAAW,Y,kBACvC,IAQM,EARN/E,EAAAA,EAAAA,IAQM,MARNyB,EAQM,G,aAPL9B,EAAAA,EAAAA,IAMMmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IANuBgI,EAAAlC,OAAOoD,SAAQ,CAA/BjJ,EAAMC,M,WAAnBtB,EAAAA,EAAAA,IAMM,YAL8C,UAAZqB,EAAK,K,WAA5CE,EAAAA,EAAAA,IAAmFmD,EAAA,C,MAA3Ed,MAAA,sB,kBAAqD,IAAa,E,iBAAVvC,EAAK,IAAD,K,YACZ,YAAZA,EAAK,K,WAAjDE,EAAAA,EAAAA,IAAwGmD,EAAA,C,MAAhGd,MAAA,qBAA2D/I,KAAK,W,kBAAU,IAAa,E,iBAAVwG,EAAK,IAAD,K,YACjC,UAAZA,EAAK,K,WAAjDE,EAAAA,EAAAA,IAAqGmD,EAAA,C,MAA7Fd,MAAA,qBAAyD/I,KAAK,U,kBAAS,IAAa,E,iBAAVwG,EAAK,IAAD,K,YAC9B,SAAZA,EAAK,K,WAAjDE,EAAAA,EAAAA,IAAqGmD,EAAA,C,MAA7Fd,MAAA,qBAAwD/I,KAAK,W,kBAAU,IAAa,E,iBAAVwG,EAAK,IAAD,K,YAC1D,WAAZA,EAAK,K,WAArBrB,EAAAA,EAAAA,IAAiF,MAAjF+B,GAAiFhB,EAAAA,EAAAA,IAAhBM,EAAK,IAAD,K,4CAKzEnB,EAAAA,EAAAA,IAMcmJ,EAAA,CANDkB,SAAA,IAAQ,CACTtP,OAAKwI,EAAAA,EAAAA,IACf,IAAkG,CAArE,OAAjB2F,EAAAlC,OAAOvN,Q,WAAnBqG,EAAAA,EAAAA,IAAkG,OAAlGgC,GAAkGjB,EAAAA,EAAAA,IAAA,YAAtBqI,EAAAlC,OAAOvN,OAAK,IACtD,OAAjByP,EAAAlC,OAAOvN,Q,WAAxBqG,EAAAA,EAAAA,IAAuG,OAAvGiC,GAAuGlB,EAAAA,EAAAA,IAAA,YAAtBqI,EAAAlC,OAAOvN,OAAK,M,WAC7FqG,EAAAA,EAAAA,IAA8D,OAA9DkC,GAA8DnB,EAAAA,EAAAA,IAAtBqI,EAAAlC,OAAOvN,OAAK,M,MAGpB,OAAfyP,EAAAlC,OAAOrM,O,WAA1B0G,EAAAA,EAAAA,IAKc8H,EAAA,C,MAL2BkB,SAAA,I,CAC7BtP,OAAKwI,EAAAA,EAAAA,IACf,IAA4G,CAAhG2F,EAAAlC,OAAOsD,aAAe,M,WAAlCxK,EAAAA,EAAAA,IAA4G,OAA5GmC,GAA4GpB,EAAAA,EAAAA,IAAA,YAA5BqI,EAAAlC,OAAOsD,aAAW,M,WAClGxK,EAAAA,EAAAA,IAAkF,OAAlFoC,GAAkFrB,EAAAA,EAAAA,IAAA,YAA5BqI,EAAAlC,OAAOsD,aAAW,M,wBAG1EtK,EAAAA,EAAAA,IAIcmJ,EAAA,CAJDkB,SAAA,IAAQ,CACTtP,OAAKwI,EAAAA,EAAAA,IACf,IAAiC,E,2BAAlB2F,EAAAlC,OAAOuD,UAAQ,K,cAIuD,OAAjBrB,EAAAlC,OAAOvN,OAAkByP,EAAAhC,U,WAA7FpH,EAAAA,EAAAA,IAEM,MAFNuC,EAEM,EADJrC,EAAAA,EAAAA,IAAqFS,EAAA,CAAxEwE,QAAOuF,EAAAC,cAAe9P,KAAK,UAAUqK,MAAA,GAAMpG,KAAK,Q,kBAAO,IAAKoE,EAAA,MAAAA,EAAA,M,QAAL,Y,gDAGtEhD,EAAAA,EAAAA,IAeYmI,EAAA,CAfD7C,MAAM,Q,WAAiBwE,EAAAY,U,qCAAAZ,EAAAY,UAAS5H,GAAEmB,MAAM,MAAO,eAAcuG,EAAAG,mB,CAS3DvC,QAAM7E,EAAAA,EAAAA,IACf,IAGM,EAHNpD,EAAAA,EAAAA,IAGM,MAHNmC,EAGM,EAFJtC,EAAAA,EAAAA,IAAqDS,EAAA,CAAzCwE,QAAOuF,EAAAG,mBAAiB,C,iBAAE,IAAG3H,EAAA,MAAAA,EAAA,M,QAAH,U,6BACtChD,EAAAA,EAAAA,IAA0DS,EAAA,CAA/C9F,KAAK,UAAWsK,QAAOuF,EAAAI,S,kBAAS,IAAG5H,EAAA,MAAAA,EAAA,M,QAAH,U,iDAX/C,IAOU,EAPVhD,EAAAA,EAAAA,IAOUwI,EAAA,CAPAC,MAAOqB,EAAAe,SAAO,C,iBACtB,IAIe,EAJf7K,EAAAA,EAAAA,IAIe0I,EAAA,CAJD3N,MAAM,QAAM,C,iBACxB,IAEY,EAFZiF,EAAAA,EAAAA,IAEY8K,EAAA,CAFDlM,KAAK,Q,WAAiBkL,EAAAe,QAAQE,U,qCAARjB,EAAAe,QAAQE,UAASjI,GAAEM,YAAY,WAAWM,MAAA,gB,kBACT,IAA0B,G,aAA1F5D,EAAAA,EAAAA,IAAsHmB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAAvC4I,EAAAkB,WAARC,K,WAAvE5J,EAAAA,EAAAA,IAAsH6J,EAAA,CAA1GnQ,MAAOkQ,EAAKvM,KAAO,IAAMuM,EAAKf,IAAMlP,MAAOiQ,EAAK/P,GAAgCsG,IAAKyJ,EAAK/P,I,oEAG1G8E,EAAAA,EAAAA,IAAiK0I,EAAA,CAAnJ3N,MAAM,SAAO,C,iBAAC,IAAsH,EAAtHiF,EAAAA,EAAAA,IAAsHmD,EAAA,CAA3G0F,SAAU,CAAAC,QAAA,EAAAC,QAAA,G,WAAqCe,EAAAe,QAAQzO,K,qCAAR0N,EAAAe,QAAQzO,KAAI0G,GAAEnI,KAAK,WAAWwQ,aAAa,O,0HAczI,GACCrH,MAAO,CACNkD,OAAQ,CACPnD,QAAS,CAAC,GAEXqD,QAAS,CACRrD,SAAS,IAGXrK,SAAU,KACN4R,EAAAA,EAAAA,IAAS,CAAC,SAEdC,WAAY,CACXC,OAAMA,EAAAA,GAEPnO,IAAAA,GACC,MAAO,CACN4M,YAAa,CAAC,IAAK,IAAK,KAExBW,WAAW,EAEXG,QAAS,CACRE,UAAW,KACX3O,KAAM,GACN6K,KAAM,GACN9L,OAAQ,OAEN6P,WAAW,GAEhB,EACAO,QAAS,CACR,aAAMX,GACLY,KAAKX,QAAQtR,QAAUiS,KAAK9R,IAAIwB,GAChCsQ,KAAKX,QAAQ5D,KAAOuE,KAAKxE,OACzB,MAAM/J,QAAiBuO,KAAK1O,KAAK2O,WAAWD,KAAKX,SACzB,MAApB5N,EAAS9B,SACZqQ,KAAKE,SAAS,CACb/Q,KAAM,UACNiD,QAAS,UACTC,SAAU,MAEX2N,KAAKd,WAAY,EACjBc,KAAKX,QAAU,CACdE,UAAW,KACX3O,KAAM,GACN6K,KAAM,GACN9L,OAAQ,OAGX,EAEEwP,iBAAAA,GACEa,KAAKd,WAAY,EACjBc,KAAKX,QAAU,CAChBE,UAAW,KACX3O,KAAM,GACN6K,KAAM,GACN9L,OAAQ,MAEP,EAGF,mBAAMsP,GACJ,MAAMxN,QAAiBuO,KAAK1O,KAAK6O,mBACT,MAApB1O,EAAS9B,SACXqQ,KAAKR,WAAa/N,EAASE,KAC3BqO,KAAKd,WAAY,EAErB,I,WChLJ,MAAM1B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS4C,KAEpE,O", "sources": ["webpack://frontend-web/./src/views/BugManage.vue", "webpack://frontend-web/./src/views/BugManage.vue?cef3", "webpack://frontend-web/./src/components/common/caseResult.vue", "webpack://frontend-web/./src/components/common/caseResult.vue?0f1a"], "sourcesContent": ["<template>\r\n  <div class=\"bug-dashboard\">\r\n    <el-scrollbar height=\"calc(100vh - 55px)\">\r\n      <div class=\"dashboard-container\">\r\n        <!-- 顶部区域 -->\r\n        <div class=\"dashboard-header\">\r\n          <div class=\"header-left\">\r\n            <h1 class=\"page-title\">Bug 管理</h1>\r\n          </div>\r\n          <div class=\"header-right\">\r\n            <el-badge :value=\"bugCounts.pending\" :hidden=\"bugCounts.pending === 0\" class=\"pending-badge\">\r\n              <el-button type=\"danger\" size=\"large\" class=\"action-btn\">\r\n                <el-icon><Warning /></el-icon>\r\n                <span>待处理 {{ bugCounts.pending }}</span>\r\n              </el-button>\r\n            </el-badge>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 统计卡片区 -->\r\n        <div class=\"stats-section\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"4\" v-for=\"(item, index) in statCards\" :key=\"index\">\r\n              <div class=\"stat-card\" :class=\"`stat-card-${item.type}`\">\r\n                <div class=\"stat-icon\">\r\n                  <el-icon>\r\n                    <component :is=\"item.icon\" />\r\n                  </el-icon>\r\n                </div>\r\n                <div class=\"stat-data\">\r\n                  <div class=\"stat-value\">{{ item.value }}</div>\r\n                  <div class=\"stat-label\">{{ item.label }}</div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 图表区域 -->\r\n        <div class=\"charts-section\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"chart-wrapper\">\r\n                <div class=\"chart-header\">\r\n                  <h3>Bug 分布</h3>\r\n                  <div class=\"chart-actions\">\r\n                    <el-button size=\"small\" text>\r\n                      <el-icon><Refresh /></el-icon>\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n                <div class=\"chart-content\" ref=\"chart1Box\"></div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"chart-wrapper\">\r\n                <div class=\"chart-header\">\r\n                  <h3>状态占比</h3>\r\n                  <div class=\"chart-actions\">\r\n                    <el-button size=\"small\" text>\r\n                      <el-icon><Refresh /></el-icon>\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n                <div class=\"chart-content\" ref=\"chart2Box\"></div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- Bug列表区域 -->\r\n        <div class=\"bug-list-section\">\r\n          <div class=\"list-header\">\r\n            <div class=\"title-section\">\r\n              <h2>BUG 列表</h2>\r\n              <span class=\"bug-counter\">共 {{ bugs.length }} 项</span>\r\n            </div>\r\n\r\n            <div class=\"filter-section\">\r\n              <el-radio-group v-model=\"currentFilter\" size=\"large\" @change=\"(val) => filterBugs(val)\">\r\n                <el-radio-button label=\"all\">全部</el-radio-button>\r\n                <el-radio-button label=\"pending\">待处理</el-radio-button>\r\n                <el-radio-button label=\"inProgress\">处理中</el-radio-button>\r\n                <el-radio-button label=\"completed\">已完成</el-radio-button>\r\n                <el-radio-button label=\"unnecessary\">无需处理</el-radio-button>\r\n                <el-radio-button label=\"closed\">已关闭</el-radio-button>\r\n              </el-radio-group>\r\n\r\n              <el-input\r\n                v-model=\"searchQuery\"\r\n                placeholder=\"搜索Bug...\"\r\n                class=\"search-input\"\r\n                clearable\r\n              >\r\n                <template #prefix>\r\n                  <el-icon><Search /></el-icon>\r\n                </template>\r\n              </el-input>\r\n            </div>\r\n          </div>\r\n\r\n          <el-table\r\n            :data=\"displayBugs\"\r\n            style=\"width: 100%\"\r\n            row-key=\"id\"\r\n            v-loading=\"tableLoading\"\r\n            :row-class-name=\"getRowClass\"\r\n            @row-click=\"showBugInfo\"\r\n            class=\"bug-table\"\r\n          >\r\n            <el-table-column type=\"expand\">\r\n              <template #default=\"props\">\r\n                <div class=\"bug-expand-detail\">\r\n                  <p><strong>Bug描述:</strong> {{ props.row.desc }}</p>\r\n                  <p><strong>所属接口:</strong> {{ props.row.interface_url }}</p>\r\n                  <p><strong>提交时间:</strong> {{ rTime(props.row.create_time) }}</p>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"\" width=\"50\">\r\n              <template #default=\"scope\">\r\n                <div class=\"bug-status-dot\" :class=\"'bug-status-' + getStatusClass(scope.row.status)\"></div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"id\" label=\"接口名称\"   />\r\n\r\n            <el-table-column prop=\"desc\" label=\"Bug描述\" show-overflow-tooltip>\r\n              <template #default=\"scope\">\r\n                <div class=\"bug-title\">{{ scope.row.desc }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"interface_url\" label=\"所属接口\" min-width=\"150\" show-overflow-tooltip />\r\n\r\n            <el-table-column label=\"提交时间\" >\r\n              <template #default=\"scope\">\r\n                <div class=\"time-info\">\r\n                  <el-icon><Time /></el-icon>\r\n                  <span>{{ rTime(scope.row.create_time) }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"状态\" width=\"120\" align=\"center\">\r\n              <template #default=\"scope\">\r\n                <el-tag\r\n                  :type=\"getStatusType(scope.row.status)\"\r\n                  effect=\"light\"\r\n                  class=\"status-tag\"\r\n                  :class=\"'status-' + getStatusClass(scope.row.status)\"\r\n                >\r\n                  <span>{{ scope.row.status }}</span>\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column width=\"140\" align=\"center\" fixed=\"right\" label=\"操作\">\r\n              <template #default=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-tooltip content=\"查看详情\" placement=\"top\">\r\n                    <el-button\r\n                      circle\r\n                      type=\"primary\"\r\n                      plain\r\n                      @click.stop=\"showBugInfo(scope.row)\"\r\n                    >\r\n                      <el-icon><View /></el-icon>\r\n                    </el-button>\r\n                  </el-tooltip>\r\n\r\n                  <el-tooltip content=\"更新状态\" placement=\"top\">\r\n                    <el-button\r\n                      circle\r\n                      type=\"warning\"\r\n                      plain\r\n                      @click.stop=\"openUpdateDialog(scope.row)\"\r\n                    >\r\n                      <el-icon><Edit /></el-icon>\r\n                    </el-button>\r\n                  </el-tooltip>\r\n\r\n                  <el-tooltip content=\"删除Bug\" placement=\"top\">\r\n                    <el-popconfirm\r\n                      title=\"确定要删除此Bug吗?\"\r\n                      confirm-button-text=\"确定\"\r\n                      cancel-button-text=\"取消\"\r\n                      @confirm=\"deleteBug(scope.row.id)\"\r\n                    >\r\n                      <template #reference>\r\n                        <el-button\r\n                          circle\r\n                          type=\"danger\"\r\n                          plain\r\n                          @click.stop\r\n                        >\r\n                          <el-icon><Delete /></el-icon>\r\n                        </el-button>\r\n                      </template>\r\n                    </el-popconfirm>\r\n                  </el-tooltip>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <div class=\"pagination-wrapper\">\r\n            <el-pagination\r\n              v-model:current-page=\"currentPage\"\r\n              v-model:page-size=\"pageSize\"\r\n              :page-sizes=\"[10, 20, 30, 50]\"\r\n              layout=\"total, sizes, prev, pager, next, jumper\"\r\n              :total=\"filteredBugs.length\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Bug详情抽屉 -->\r\n      <el-drawer\r\n        v-model=\"bugDetailVisible\"\r\n        direction=\"rtl\"\r\n        size=\"55%\"\r\n        destroy-on-close\r\n        custom-class=\"bug-detail-drawer\"\r\n      >\r\n        <template #header>\r\n          <div class=\"drawer-header\">\r\n            <h2 class=\"drawer-title\">Bug详情</h2>\r\n            <el-tag\r\n              v-if=\"selectedBug\"\r\n              :type=\"getStatusType(selectedBug.status)\"\r\n              effect=\"dark\"\r\n              class=\"drawer-status-tag\"\r\n            >\r\n              {{ selectedBug.status }}\r\n            </el-tag>\r\n          </div>\r\n        </template>\r\n\r\n        <el-scrollbar height=\"calc(100vh - 120px)\">\r\n          <div v-if=\"selectedBug\" class=\"bug-detail-content\">\r\n            <el-card class=\"detail-card info-card\" shadow=\"hover\">\r\n              <template #header>\r\n                <div class=\"card-header\">\r\n                  <h3><el-icon><InfoFilled /></el-icon> 基本信息</h3>\r\n                </div>\r\n              </template>\r\n\r\n              <el-descriptions :column=\"2\" border>\r\n                <el-descriptions-item label=\"Bug ID\">{{ selectedBug.id }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"提交者\">admin</el-descriptions-item>\r\n                <el-descriptions-item label=\"所属接口\">{{ selectedBug.interface_url }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"提交时间\">{{ rTime(selectedBug.create_time) }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"Bug描述\" :span=\"2\">\r\n                  <div class=\"bug-description\">{{ selectedBug.desc }}</div>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-card>\r\n\r\n            <el-card class=\"detail-card test-info-card\" shadow=\"hover\">\r\n              <template #header>\r\n                <div class=\"card-header\">\r\n                  <h3><el-icon><Tickets /></el-icon> 用例执行信息</h3>\r\n                </div>\r\n              </template>\r\n              <Result :result=\"selectedBug.info\" :showbtn=\"false\"></Result>\r\n            </el-card>\r\n\r\n            <el-card v-if=\"bugLogs && bugLogs.length > 0\" class=\"detail-card timeline-card\" shadow=\"hover\">\r\n              <template #header>\r\n                <div class=\"card-header\">\r\n                  <h3><el-icon><Tickets /></el-icon> 处理记录</h3>\r\n                </div>\r\n              </template>\r\n\r\n              <el-timeline>\r\n                <el-timeline-item\r\n                  v-for=\"(activity, index) in bugLogs\"\r\n                  :key=\"index\"\r\n                  :timestamp=\"rDate(activity.create_time)\"\r\n                  :type=\"getTimelineType(activity.handle)\"\r\n                  :hollow=\"true\"\r\n                  :size=\"index === 0 ? 'large' : 'normal'\"\r\n                >\r\n                  <el-card class=\"timeline-item-card\">\r\n                    <h4 class=\"activity-title\">\r\n                      <el-icon><component :is=\"getActivityIcon(activity.handle)\" /></el-icon>\r\n                      {{ activity.handle }}\r\n                    </h4>\r\n\r\n                    <p v-if=\"activity.remark\" class=\"activity-remark\">{{ activity.remark }}</p>\r\n\r\n                    <div class=\"activity-footer\">\r\n                      <span class=\"operator\">\r\n                        <el-icon><User /></el-icon>\r\n                        {{ activity.update_user }}\r\n                      </span>\r\n                      <span class=\"time\">\r\n                        <el-icon><Time /></el-icon>\r\n                        {{ rTime(activity.create_time) }}\r\n                      </span>\r\n                    </div>\r\n                  </el-card>\r\n                </el-timeline-item>\r\n              </el-timeline>\r\n            </el-card>\r\n\r\n            <el-empty v-else description=\"暂无处理记录\" :image-size=\"200\"></el-empty>\r\n\r\n            <div class=\"drawer-actions\">\r\n              <el-button size=\"default\" @click=\"bugDetailVisible = false\">关闭</el-button>\r\n              <el-button size=\"default\" type=\"primary\" @click=\"openUpdateDialog(selectedBug)\">\r\n                <el-icon><Edit /></el-icon> 更新状态\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-scrollbar>\r\n      </el-drawer>\r\n\r\n      <!-- 更新Bug状态对话框 -->\r\n      <el-dialog\r\n        v-model=\"updateDialogVisible\"\r\n        title=\"更新Bug状态\"\r\n        width=\"50%\"\r\n        destroy-on-close\r\n        custom-class=\"update-dialog\"\r\n      >\r\n        <el-form :model=\"updateForm\" label-position=\"top\" status-icon class=\"update-form\">\r\n          <el-form-item label=\"选择状态\">\r\n            <div class=\"status-options\">\r\n              <div\r\n                v-for=\"(status, key) in statusOptions\"\r\n                :key=\"key\"\r\n                class=\"status-option\"\r\n                :class=\"{ 'active': updateForm.status === status.value }\"\r\n                @click=\"updateForm.status = status.value\"\r\n              >\r\n                <el-icon class=\"option-icon\">\r\n                  <component :is=\"status.icon\" />\r\n                </el-icon>\r\n                <div class=\"option-label\">{{ status.label }}</div>\r\n              </div>\r\n            </div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"处理备注\">\r\n            <el-input\r\n              v-model=\"updateForm.remark\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n              placeholder=\"请输入处理备注...\"\r\n              class=\"remark-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <template #footer>\r\n          <div class=\"dialog-footer\">\r\n            <el-button @click=\"updateDialogVisible = false\">取消</el-button>\r\n            <el-button type=\"primary\" @click=\"updateBug\" :loading=\"updateLoading\">\r\n              <el-icon><Check /></el-icon> 确认更新\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-dialog>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted, watch, nextTick, reactive, getCurrentInstance } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport Result from '../components/common/caseResult.vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport {\r\n  Warning, View, Edit, Delete, User, Tickets,\r\n  Loading, CircleCheck, WarningFilled, CircleClose, \r\n  Remove, Search, Refresh, Check, InfoFilled\r\n} from '@element-plus/icons-vue'\r\n\r\n// 获取当前实例，在 setup 阶段保存代理对象\r\nconst { proxy } = getCurrentInstance() || { proxy: null }\r\n// 获取工具函数\r\nconst tools = proxy?.$tools || {}\r\n\r\n// Store\r\nconst store = useStore()\r\nconst project = computed(() => store.state.pro)\r\n\r\n// 数据\r\nconst bugs = ref([])\r\nconst selectedBug = ref(null)\r\nconst bugLogs = ref(null)\r\nconst filteredBugs = ref([])\r\nconst searchQuery = ref('')\r\nconst currentFilter = ref('all')\r\nconst tableLoading = ref(false)\r\nconst bugDetailVisible = ref(false)\r\nconst updateDialogVisible = ref(false)\r\nconst updateLoading = ref(false)\r\nconst chart1Box = ref(null)\r\nconst chart2Box = ref(null)\r\n\r\n// 分页\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\n\r\n// Bug状态映射\r\nconst STATUS_MAP = {\r\n  '待处理': { type: 'danger', icon: 'Warning', class: 'pending' },\r\n  '处理中': { type: 'warning', icon: 'Loading', class: 'in-progress' },\r\n  '处理完成': { type: 'success', icon: 'CircleCheck', class: 'completed' },\r\n  '无需处理': { type: 'info', icon: 'Remove', class: 'unnecessary' },\r\n  '已关闭': { type: 'info', icon: 'CircleClose', class: 'closed' }\r\n}\r\n\r\n// 状态选项\r\nconst statusOptions = [\r\n  { label: '待处理', value: '待处理', icon: 'Warning' },\r\n  { label: '处理中', value: '处理中', icon: 'Loading' },\r\n  { label: '处理完成', value: '处理完成', icon: 'CircleCheck' },\r\n  { label: '无需处理', value: '无需处理', icon: 'Remove' },\r\n  { label: '已关闭', value: '已关闭', icon: 'CircleClose' }\r\n]\r\n\r\n// 表单数据\r\nconst updateForm = ref({\r\n  id: '',\r\n  status: '',\r\n  remark: ''\r\n})\r\n\r\n// 计算属性\r\nconst bugsByStatus = computed(() => {\r\n  return {\r\n    pending: bugs.value.filter(bug => bug.status === '待处理'),\r\n    inProgress: bugs.value.filter(bug => bug.status === '处理中'),\r\n    completed: bugs.value.filter(bug => bug.status === '处理完成'),\r\n    unnecessary: bugs.value.filter(bug => bug.status === '无需处理'),\r\n    closed: bugs.value.filter(bug => bug.status === '已关闭')\r\n  }\r\n})\r\n\r\nconst bugCounts = computed(() => {\r\n  return {\r\n    total: bugs.value.length,\r\n    pending: bugsByStatus.value.pending.length,\r\n    inProgress: bugsByStatus.value.inProgress.length,\r\n    completed: bugsByStatus.value.completed.length,\r\n    unnecessary: bugsByStatus.value.unnecessary.length,\r\n    closed: bugsByStatus.value.closed.length\r\n  }\r\n})\r\n\r\n// 统计卡片数据\r\nconst statCards = computed(() => [\r\n  { type: 'total', icon: 'Tickets', value: bugCounts.value.total, label: 'Bug总数' },\r\n  { type: 'pending', icon: 'Warning', value: bugCounts.value.pending, label: '待处理' },\r\n  { type: 'in-progress', icon: 'Loading', value: bugCounts.value.inProgress, label: '处理中' },\r\n  { type: 'completed', icon: 'CircleCheck', value: bugCounts.value.completed, label: '已完成' },\r\n  { type: 'unnecessary', icon: 'Remove', value: bugCounts.value.unnecessary, label: '无需处理' },\r\n  { type: 'closed', icon: 'CircleClose', value: bugCounts.value.closed, label: '已关闭' }\r\n])\r\n\r\n// 搜索过滤\r\nconst searchFilteredBugs = computed(() => {\r\n  if (!searchQuery.value) return filteredBugs.value\r\n\r\n  const query = searchQuery.value.toLowerCase()\r\n  return filteredBugs.value.filter(bug =>\r\n    bug.desc.toLowerCase().includes(query) ||\r\n    bug.interface_url.toLowerCase().includes(query) ||\r\n    String(bug.id).includes(query)\r\n  )\r\n})\r\n\r\n// 分页展示的Bug\r\nconst displayBugs = computed(() => {\r\n  const startIndex = (currentPage.value - 1) * pageSize.value\r\n  const endIndex = startIndex + pageSize.value\r\n  return searchFilteredBugs.value.slice(startIndex, endIndex)\r\n})\r\n\r\n// 方法\r\nconst fetchBugs = async () => {\r\n  if (!project.value?.id) return\r\n\r\n  tableLoading.value = true\r\n  try {\r\n    if (!proxy || !proxy.$api) {\r\n      ElMessage.error('API 初始化失败')\r\n      return\r\n    }\r\n\r\n    const response = await proxy.$api.getBugs({\r\n      project: project.value.id\r\n    })\r\n\r\n    if (response.status === 200) {\r\n      bugs.value = response.data\r\n      filterBugs(currentFilter.value)\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('获取Bug列表失败')\r\n    console.error('Failed to fetch bugs:', error)\r\n  } finally {\r\n    tableLoading.value = false\r\n  }\r\n}\r\n\r\nconst filterBugs = (filter) => {\r\n  currentFilter.value = filter\r\n  currentPage.value = 1 // 重置分页\r\n\r\n  switch (filter) {\r\n    case 'pending':\r\n      filteredBugs.value = bugsByStatus.value.pending\r\n      break\r\n    case 'inProgress':\r\n      filteredBugs.value = bugsByStatus.value.inProgress\r\n      break\r\n    case 'completed':\r\n      filteredBugs.value = bugsByStatus.value.completed\r\n      break\r\n    case 'unnecessary':\r\n      filteredBugs.value = bugsByStatus.value.unnecessary\r\n      break\r\n    case 'closed':\r\n      filteredBugs.value = bugsByStatus.value.closed\r\n      break\r\n    default:\r\n      filteredBugs.value = bugs.value\r\n  }\r\n}\r\n\r\nconst showBugInfo = async (bug) => {\r\n  selectedBug.value = bug\r\n  bugDetailVisible.value = true\r\n\r\n  try {\r\n    if (!proxy || !proxy.$api) return\r\n\r\n    const response = await proxy.$api.getBugLogs({ bug: bug.id })\r\n    if (response.status === 200 && response.data.length > 0) {\r\n      bugLogs.value = response.data\r\n    } else {\r\n      bugLogs.value = []\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('获取Bug处理记录失败')\r\n    console.error('Failed to fetch bug logs:', error)\r\n    bugLogs.value = []\r\n  }\r\n}\r\n\r\nconst openUpdateDialog = (bug) => {\r\n  updateForm.value = {\r\n    id: bug.id,\r\n    status: bug.status,\r\n    remark: ''\r\n  }\r\n  updateDialogVisible.value = true\r\n}\r\n\r\nconst updateBug = async () => {\r\n  if (!updateForm.value.id) return\r\n\r\n  if (!updateForm.value.remark.trim()) {\r\n    ElMessage.warning('请输入处理备注')\r\n    return\r\n  }\r\n\r\n  updateLoading.value = true\r\n  try {\r\n    if (!proxy || !proxy.$api) return\r\n\r\n    const response = await proxy.$api.updateBug(updateForm.value.id, updateForm.value)\r\n\r\n    if (response.status === 200) {\r\n      ElMessage.success({\r\n        message: 'Bug状态更新成功',\r\n        type: 'success',\r\n        duration: 2000\r\n      })\r\n      updateDialogVisible.value = false\r\n\r\n      // 刷新数据\r\n      await fetchBugs()\r\n\r\n      // 如果详情抽屉打开，刷新日志\r\n      if (bugDetailVisible.value && selectedBug.value) {\r\n        const logs = await proxy.$api.getBugLogs({ bug: selectedBug.value.id })\r\n        if (logs.status === 200) {\r\n          bugLogs.value = logs.data\r\n\r\n          // 更新选中的Bug信息\r\n          const updatedBug = bugs.value.find(b => b.id === selectedBug.value.id)\r\n          if (updatedBug) {\r\n            selectedBug.value = updatedBug\r\n          }\r\n        }\r\n      }\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('更新Bug状态失败')\r\n    console.error('Failed to update bug:', error)\r\n  } finally {\r\n    updateLoading.value = false\r\n  }\r\n}\r\n\r\nconst deleteBug = async (id) => {\r\n  tableLoading.value = true\r\n  try {\r\n    if (!proxy || !proxy.$api) return\r\n\r\n    const response = await proxy.$api.deleteBug(id)\r\n\r\n    if (response.status === 204) {\r\n      ElMessage.success({\r\n        message: '删除成功',\r\n        type: 'success',\r\n        duration: 2000\r\n      })\r\n      await fetchBugs()\r\n\r\n      // 如果删除的Bug正在显示详情，关闭抽屉\r\n      if (selectedBug.value && selectedBug.value.id === id) {\r\n        bugDetailVisible.value = false\r\n      }\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('删除Bug失败')\r\n    console.error('Failed to delete bug:', error)\r\n  } finally {\r\n    tableLoading.value = false\r\n  }\r\n}\r\n\r\nconst renderCharts = () => {\r\n  if (!chart1Box.value || !chart2Box.value) return\r\n  if (!proxy || !proxy.$chart) return\r\n\r\n  // 准备图表数据\r\n  const chartData = [\r\n    bugs.value.length,\r\n    bugsByStatus.value.completed.length,\r\n    bugsByStatus.value.inProgress.length,\r\n    bugsByStatus.value.pending.length,\r\n    bugsByStatus.value.unnecessary.length,\r\n    bugsByStatus.value.closed.length\r\n  ]\r\n\r\n  const chartLabels = ['Bug总数', '处理完成', '处理中', '待处理', '无需处理', '已关闭']\r\n\r\n  // 渲染柱状图\r\n  proxy.$chart.chart1(chart1Box.value, chartData, chartLabels)\r\n\r\n  // 渲染饼图\r\n  proxy.$chart.chart2(chart2Box.value, [\r\n    { value: bugsByStatus.value.completed.length, name: '处理完成' },\r\n    { value: bugsByStatus.value.inProgress.length, name: '处理中' },\r\n    { value: bugsByStatus.value.pending.length, name: '待处理' },\r\n    { value: bugsByStatus.value.unnecessary.length, name: '无需处理' },\r\n    { value: bugsByStatus.value.closed.length, name: '已关闭' }\r\n  ])\r\n}\r\n\r\n// 分页处理\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n}\r\n\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n}\r\n\r\n// 辅助函数\r\nconst getStatusType = (status) => {\r\n  return STATUS_MAP[status]?.type || 'info'\r\n}\r\n\r\nconst getStatusIcon = (status) => {\r\n  return STATUS_MAP[status]?.icon || 'InfoFilled'\r\n}\r\n\r\nconst getStatusClass = (status) => {\r\n  return STATUS_MAP[status]?.class || 'default'\r\n}\r\n\r\nconst getTimelineType = (handle) => {\r\n  if (handle.includes('处理完成')) return 'success'\r\n  if (handle.includes('处理中')) return 'warning'\r\n  if (handle.includes('待处理')) return 'danger'\r\n  if (handle.includes('关闭')) return 'info'\r\n  return 'primary'\r\n}\r\n\r\nconst getActivityIcon = (handle) => {\r\n  if (handle.includes('处理完成')) return 'CircleCheck'\r\n  if (handle.includes('处理中')) return 'Loading'\r\n  if (handle.includes('待处理')) return 'Warning'\r\n  if (handle.includes('关闭')) return 'CircleClose'\r\n  if (handle.includes('无需处理')) return 'Remove'\r\n  return 'InfoFilled'\r\n}\r\n\r\nconst getRowClass = ({ row }) => {\r\n  return `bug-row bug-status-${getStatusClass(row.status)}`\r\n}\r\n\r\n// 生命周期钩子\r\nonMounted(async () => {\r\n  await fetchBugs()\r\n  nextTick(() => {\r\n    renderCharts()\r\n  })\r\n})\r\n\r\n// 监听bugs变化更新图表\r\nwatch(() => bugs.value, () => {\r\n  nextTick(() => {\r\n    renderCharts()\r\n  })\r\n}, { deep: true })\r\n\r\n// 监听搜索，重置分页\r\nwatch(searchQuery, () => {\r\n  currentPage.value = 1\r\n})\r\n\r\n// 在模板中使用 $tools.rTime 的地方，替换为我们的本地函数\r\nconst rTime = (time) => {\r\n  if (!proxy || !proxy.$tools) return time\r\n  return proxy.$tools.rTime(time)\r\n}\r\n\r\n// 在模板中使用 $tools.rDate 的地方，替换为我们的本地函数\r\nconst rDate = (time) => {\r\n  if (!proxy || !proxy.$tools) return time\r\n  return proxy.$tools.rDate ? proxy.$tools.rDate(time) : proxy.$tools.rTime(time)\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 整体样式 */\r\n.bug-dashboard {\r\n  height: 100%;\r\n  background-color: #f8fafc;\r\n  color: #334155;\r\n}\r\n\r\n.dashboard-container {\r\n  padding: 24px;\r\n}\r\n\r\n/* 头部区域 */\r\n.dashboard-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-title {\r\n  margin: 0;\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #1e293b;\r\n  margin-bottom: 4px;\r\n  background: linear-gradient(90deg, #3b82f6, #7c3aed);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n}\r\n\r\n.sub-title {\r\n  color: #64748b;\r\n  font-size: 16px;\r\n}\r\n\r\n.pending-badge :deep(.el-badge__content) {\r\n  background-color: #ef4444;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 12px 20px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 统计卡片区域 */\r\n.stats-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.stat-card {\r\n  display: flex;\r\n  align-items: center;\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s ease;\r\n  height: 100px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  border-left: 5px solid #3b82f6;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stat-card-total {\r\n  border-left-color: #3b82f6;\r\n}\r\n\r\n.stat-card-pending {\r\n  border-left-color: #ef4444;\r\n}\r\n\r\n.stat-card-in-progress {\r\n  border-left-color: #f59e0b;\r\n}\r\n\r\n.stat-card-completed {\r\n  border-left-color: #10b981;\r\n}\r\n\r\n.stat-card-unnecessary {\r\n  border-left-color: #64748b;\r\n}\r\n\r\n.stat-card-closed {\r\n  border-left-color: #475569;\r\n}\r\n\r\n.stat-icon {\r\n  background: rgba(59, 130, 246, 0.1);\r\n  border-radius: 50%;\r\n  width: 48px;\r\n  height: 48px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  margin-right: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.stat-card-total .stat-icon {\r\n  background: rgba(59, 130, 246, 0.1);\r\n  color: #3b82f6;\r\n}\r\n\r\n.stat-card-pending .stat-icon {\r\n  background: rgba(239, 68, 68, 0.1);\r\n  color: #ef4444;\r\n}\r\n\r\n.stat-card-in-progress .stat-icon {\r\n  background: rgba(245, 158, 11, 0.1);\r\n  color: #f59e0b;\r\n}\r\n\r\n.stat-card-completed .stat-icon {\r\n  background: rgba(16, 185, 129, 0.1);\r\n  color: #10b981;\r\n}\r\n\r\n.stat-card-unnecessary .stat-icon {\r\n  background: rgba(100, 116, 139, 0.1);\r\n  color: #64748b;\r\n}\r\n\r\n.stat-card-closed .stat-icon {\r\n  background: rgba(71, 85, 105, 0.1);\r\n  color: #475569;\r\n}\r\n\r\n.stat-icon :deep(.el-icon) {\r\n  font-size: 24px;\r\n}\r\n\r\n.stat-data {\r\n  flex-grow: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #1e293b;\r\n  line-height: 1.2;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #64748b;\r\n}\r\n\r\n/* 图表区域 */\r\n.charts-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.chart-wrapper {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  height: 360px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.chart-header h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n}\r\n\r\n.chart-content {\r\n  flex-grow: 1;\r\n  width: 100%;\r\n}\r\n\r\n/* Bug列表区域 */\r\n.bug-list-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.list-header {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.title-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section h2 {\r\n  margin: 0;\r\n  font-size: 24px;\r\n  color: #1e293b;\r\n  font-weight: 600;\r\n}\r\n\r\n.bug-counter {\r\n  background-color: #e2e8f0;\r\n  padding: 4px 12px;\r\n  border-radius: 16px;\r\n  font-size: 14px;\r\n  color: #475569;\r\n  font-weight: 500;\r\n}\r\n\r\n.filter-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.search-input {\r\n  width: 300px;\r\n}\r\n\r\n/* Bug表格 */\r\n.bug-table {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.bug-table :deep(th.el-table__cell) {\r\n  background-color: #f1f5f9;\r\n  color: #475569;\r\n  font-weight: 600;\r\n}\r\n\r\n.bug-table :deep(.el-table__row) {\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.bug-table :deep(.el-table__row:hover) {\r\n  background-color: #f8fafc;\r\n}\r\n\r\n/* 确保表格内容垂直居中 */\r\n.bug-table :deep(.el-table__cell) {\r\n  vertical-align: middle;\r\n}\r\n\r\n/* 确保固定列正确显示 */\r\n.bug-table :deep(.el-table__fixed-right) {\r\n  height: auto !important;\r\n  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 响应式调整 */\r\n@media screen and (max-width: 768px) {\r\n  .bug-table {\r\n    width: 100%;\r\n    overflow-x: auto;\r\n  }\r\n  \r\n  .action-buttons {\r\n    justify-content: center;\r\n  }\r\n\r\n  /* 固定列样式优化 */\r\n  .bug-table :deep(.el-table__fixed-right) {\r\n    right: 0 !important;\r\n  }\r\n  \r\n  /* 确保按钮在小屏幕上间距合理且对齐 */\r\n  .action-buttons :deep(.el-button) {\r\n    margin: 0;\r\n    height: 30px;\r\n    width: 30px;\r\n  }\r\n}\r\n\r\n/* 超小屏幕设备 */\r\n@media screen and (max-width: 480px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n  \r\n  .action-buttons :deep(.el-button) {\r\n    height: 28px;\r\n    width: 28px;\r\n  }\r\n}\r\n\r\n.bug-status-dot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  margin: 0 auto;\r\n}\r\n\r\n.bug-status-pending {\r\n  background-color: #ef4444;\r\n}\r\n\r\n.bug-status-in-progress {\r\n  background-color: #f59e0b;\r\n}\r\n\r\n.bug-status-completed {\r\n  background-color: #10b981;\r\n}\r\n\r\n.bug-status-unnecessary {\r\n  background-color: #64748b;\r\n}\r\n\r\n.bug-status-closed {\r\n  background-color: #475569;\r\n}\r\n\r\n.bug-row {\r\n  cursor: pointer;\r\n}\r\n\r\n.bug-row.bug-status-pending:hover {\r\n  background-color: rgba(239, 68, 68, 0.05) !important;\r\n}\r\n\r\n.bug-row.bug-status-in-progress:hover {\r\n  background-color: rgba(245, 158, 11, 0.05) !important;\r\n}\r\n\r\n.bug-row.bug-status-completed:hover {\r\n  background-color: rgba(16, 185, 129, 0.05) !important;\r\n}\r\n\r\n.bug-row.bug-status-unnecessary:hover {\r\n  background-color: rgba(100, 116, 139, 0.05) !important;\r\n}\r\n\r\n.bug-row.bug-status-closed:hover {\r\n  background-color: rgba(71, 85, 105, 0.05) !important;\r\n}\r\n\r\n.bug-title {\r\n  font-weight: 500;\r\n  color: #1e293b;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #64748b;\r\n}\r\n\r\n.status-tag {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 4px;\r\n  min-width: 90px;\r\n  font-weight: 600;\r\n}\r\n\r\n.status-tag :deep(.el-icon) {\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.status-pending {\r\n  color: #ef4444;\r\n  background-color: rgba(239, 68, 68, 0.1);\r\n  border-color: rgba(239, 68, 68, 0.2);\r\n}\r\n\r\n.status-in-progress {\r\n  color: #f59e0b;\r\n  background-color: rgba(245, 158, 11, 0.1);\r\n  border-color: rgba(245, 158, 11, 0.2);\r\n}\r\n\r\n.status-completed {\r\n  color: #10b981;\r\n  background-color: rgba(16, 185, 129, 0.1);\r\n  border-color: rgba(16, 185, 129, 0.2);\r\n}\r\n\r\n.status-unnecessary {\r\n  color: #64748b;\r\n  background-color: rgba(100, 116, 139, 0.1);\r\n  border-color: rgba(100, 116, 139, 0.2);\r\n}\r\n\r\n.status-closed {\r\n  color: #475569;\r\n  background-color: rgba(71, 85, 105, 0.1);\r\n  border-color: rgba(71, 85, 105, 0.2);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.action-buttons :deep(.el-button) {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0;\r\n  height: 32px;\r\n  width: 32px;\r\n  position: relative;\r\n}\r\n\r\n.action-buttons :deep(.el-button .el-icon) {\r\n  font-size: 16px;\r\n  margin: 0;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* 确保Popconfirm组件内的按钮也正确对齐 */\r\n:deep(.el-popconfirm__action) {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 8px;\r\n  margin-top: 12px;\r\n}\r\n\r\n.bug-expand-detail {\r\n  padding: 16px;\r\n  background-color: #f8fafc;\r\n  border-radius: 8px;\r\n  margin: 0 20px 20px;\r\n}\r\n\r\n.bug-expand-detail p {\r\n  margin: 8px 0;\r\n  line-height: 1.6;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n}\r\n\r\n/* Bug详情抽屉 */\r\n:deep(.bug-detail-drawer .el-drawer__header) {\r\n  margin-bottom: 0;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.drawer-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n}\r\n\r\n.drawer-title {\r\n  margin: 0;\r\n  font-size: 24px;\r\n  color: #1e293b;\r\n  font-weight: 600;\r\n}\r\n\r\n.drawer-status-tag {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.bug-detail-content {\r\n  padding: 24px;\r\n}\r\n\r\n.detail-card {\r\n  margin-bottom: 24px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.card-header h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.card-header :deep(.el-icon) {\r\n  font-size: 18px;\r\n}\r\n\r\n.bug-description {\r\n  line-height: 1.6;\r\n  color: #475569;\r\n}\r\n\r\n.timeline-item-card {\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.activity-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-top: 0;\r\n  margin-bottom: 8px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n}\r\n\r\n.activity-title :deep(.el-icon) {\r\n  font-size: 18px;\r\n}\r\n\r\n.activity-remark {\r\n  color: #475569;\r\n  line-height: 1.6;\r\n  margin: 12px 0;\r\n  padding: 12px;\r\n  background-color: #f8fafc;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.activity-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 12px;\r\n  font-size: 14px;\r\n  color: #64748b;\r\n}\r\n\r\n.operator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #3b82f6;\r\n  font-weight: 500;\r\n}\r\n\r\n.time {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.drawer-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n  margin-top: 32px;\r\n}\r\n\r\n.drawer-actions :deep(.el-button .el-icon) {\r\n  margin-right: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 状态更新对话框 */\r\n:deep(.update-dialog .el-dialog__header) {\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n:deep(.update-dialog .el-dialog__body) {\r\n  padding: 24px;\r\n}\r\n\r\n.update-form {\r\n  margin-top: 12px;\r\n}\r\n\r\n.status-options {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.status-option {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16px;\r\n  width: 100%;\r\n  background-color: #f8fafc;\r\n  border-radius: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.status-option:hover {\r\n  background-color: #f1f5f9;\r\n}\r\n\r\n.status-option.active {\r\n  background-color: #eff6ff;\r\n  border-color: #3b82f6;\r\n}\r\n\r\n.option-icon {\r\n  font-size: 24px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.option-label {\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n}\r\n\r\n.remark-input {\r\n  margin-top: 8px;\r\n}\r\n\r\n:deep(.el-textarea__inner) {\r\n  padding: 12px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.dialog-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n}\r\n\r\n.dialog-footer :deep(.el-button .el-icon) {\r\n  margin-right: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* Element Plus 图标样式修复 */\r\n:deep(.el-icon) {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 针对Element Plus按钮的特殊处理 */\r\n:deep(.el-button.is-circle) {\r\n  padding: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.action-buttons :deep(.el-button .el-icon) {\r\n  font-size: 16px;\r\n  margin: 0;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 1em;\r\n  height: 1em;\r\n}\r\n</style>\r\n", "import script from \"./BugManage.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./BugManage.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./BugManage.vue?vue&type=style&index=0&id=8cb5ea16&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-8cb5ea16\"]])\n\nexport default __exports__", "<template>\n\t  <el-tabs model-value=\"rb\" style=\"min-height: 300px;\" type=\"border-card\" value=\"rb\" size=\"mini\">\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应体\" name=\"rb\">\n      <div v-if=\"result.response_header\">\n        <div v-if=\"result.response_header['Content-Type'].includes('application/json')\">\n          <!-- 如果 Content-Type 是 application/json，渲染 JSON 格式的 Editor -->\n          <Editor :readOnly=\"true\" v-model=\"result.response_body\" lang=\"json\" theme=\"chrome\"></Editor>\n        </div>\n        <div v-else>\n          <el-scrollbar height=\"400px\"  @wheel.stop>\n            <Editor :readOnly=\"true\" v-html=\"result.response_body\" lang=\"html\" theme=\"chrome\" height=\"400px\"></Editor>\n          </el-scrollbar>\n        </div>\n      </div>\n    </el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"响应头\" name=\"rh\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div class=\"tab-box-sli\" v-if=\"result.response_header\">\n\t\t\t\t<div v-for=\"(value, key) in result.response_header\">\n\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" type=\"info\">\n\t\t\t\t\t\t<b style=\"color: #747474;\">{{ key + ' : ' }}</b>\n\t\t\t\t\t\t<span>{{ value }}</span>\n\t\t\t\t\t</el-tag>\n\t\t\t\t</div>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" label=\"请求信息\" name=\"rq\">\n      <el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t  <div v-if=\"result.requests_body\">\n\t\t\t\t<el-collapse v-model=\"activeNames\" class=\"tab-box-sli\">\n\t\t\t\t\t<el-collapse-item name=\"1\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>General</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div>Request Method : {{ result.method }}</div>\n\t\t\t\t\t\t<div>Request URL : {{ result.url }}</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"2\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Headers</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<div v-for=\"(value, key) in result.requests_header\">\n\t\t\t\t\t\t\t<span>{{ key + ' : ' + value }}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t\t<el-collapse-item name=\"3\">\n\t\t\t\t\t\t<template #title>\n\t\t\t\t\t\t\t<b>Request Payload</b>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<span>{{ result.requests_body }}</span>\n\t\t\t\t\t</el-collapse-item>\n\t\t\t\t</el-collapse>\n\t\t\t</div>\n      </el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane label=\"日志\">\n\t\t\t<el-scrollbar height=\"400px\" @wheel.stop>\n\t\t\t\t<div class=\"tab-box-sli\">\n\t\t\t\t\t<div v-for=\"(item, index) in result.log_data\">\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-if=\"item[0] === 'DEBUG'\" >{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'WARNING'\" type=\"warning\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'ERROR'\" type=\"danger\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<el-tag style=\"margin-top: 3px;\" v-else-if=\"item[0] === 'INFO'\" type=\"success\">{{ item[1] }}</el-tag>\n\t\t\t\t\t\t<pre v-else-if=\"item[0] === 'EXCEPT'\" style=\"color: #d60000;\">{{ item[1] }}</pre>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</el-scrollbar>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.state === '成功'\" style=\"color: #00AA7F;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else-if=\"result.state === '失败'\" style=\"color: #d18d17;\">{{ 'Assert : ' + result.state }}</span>\n\t\t\t\t<span v-else style=\"color: #ff0000;\">{{ result.state }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane v-if=\"result.type == 'api'\" disabled>\n\t\t\t<template #label>\n\t\t\t\t<span v-if=\"result.status_cede <= 300\" style=\"color: #00AA7F;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t\t<span v-else style=\"color: #ff5500;\">{{ 'Status : ' + result.status_cede }}</span>\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t\t<el-tab-pane disabled>\n\t\t\t<template #label>\n\t\t\t\t{{ 'Time : ' + result.run_time }}\n\t\t\t</template>\n\t\t</el-tab-pane>\n\t</el-tabs>\n    <div style=\"margin-top: 10px;width: 100%;text-align: center;\" v-if=\"result.state === '失败' && showbtn\">\n      <el-button  @click=\"getInterfaces\" type=\"success\" plain size=\"mini\">提交bug</el-button>\n    </div>\n    <!-- 添加bug的弹框 -->\n    <el-dialog title=\"提交bug\" v-model=\"addBugDlg\" width=\"40%\" :before-close=\"closeDialogResult\">\n      <el-form :model=\"bugForm\">\n        <el-form-item label=\"所属接口\">\n          <el-select size=\"small\" v-model=\"bugForm.interface\" placeholder=\"bug对应的接口\" style=\"width: 100%;\">\n            <el-option :label=\"iter.name + ' ' + iter.url\" :value=\"iter.id\" v-for=\"iter in interfaces\" :key=\"iter.id\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"bug描述\"><el-input :autosize=\"{ minRows: 3, maxRows: 4 }\" v-model=\"bugForm.desc\" type=\"textarea\" autocomplete=\"off\"></el-input></el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"closeDialogResult\">取 消</el-button>\n          <el-button type=\"success\" @click=\"saveBug\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n</template>\n\n<script>\nimport Editor from './Editor.vue';\nimport { mapState } from 'vuex';\nexport default {\n\tprops: {\n\t\tresult: {\n\t\t\tdefault: {}\n\t\t},\n\t\tshowbtn: {\n\t\t\tdefault: true\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState(['pro'])\n\t},\n\tcomponents: {\n\t\tEditor\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveNames: ['1', '2', '3'],\n\t\t\t// 提交bug的显示窗口\n\t\t\taddBugDlg: false,\n\t\t\t// 添加bug的表单\n\t\t\tbugForm: {\n\t\t\t\tinterface: null,\n\t\t\t\tdesc: '',\n\t\t\t\tinfo: '',\n\t\t\t\tstatus: '待处理'\n\t\t\t},\n      interfaces:[]\n\t\t};\n\t},\n\tmethods: {\n\t\tasync saveBug() {\n\t\t\tthis.bugForm.project = this.pro.id;\n\t\t\tthis.bugForm.info = this.result;\n\t\t\tconst response = await this.$api.createBugs(this.bugForm);\n\t\t\tif (response.status === 201) {\n\t\t\t\tthis.$message({\n\t\t\t\t\ttype: 'success',\n\t\t\t\t\tmessage: 'bug提交成功',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\tthis.addBugDlg = false;\n\t\t\t\tthis.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n\t\t\t}\n\t\t},\n    // 取消按钮时重置输入信息\n    closeDialogResult() {\n      this.addBugDlg = false;\n      this.bugForm = {\n\t\t\t\t\tinterface: null,\n\t\t\t\t\tdesc: '',\n\t\t\t\t\tinfo: '',\n\t\t\t\t\tstatus: '待处理'\n\t\t\t\t};\n      },\n\n    // 获取接口列表\n    async getInterfaces() {\n      const response = await this.$api.getNewInterfaces();\n      if (response.status === 200) {\n        this.interfaces = response.data\n        this.addBugDlg = true\n      }\n    }\n\t}\n};\n</script>\n\n<style></style>\n", "import { render } from \"./caseResult.vue?vue&type=template&id=3a14eb2a\"\nimport script from \"./caseResult.vue?vue&type=script&lang=js\"\nexport * from \"./caseResult.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__"], "names": ["proxy", "getCurrentInstance", "store", "useStore", "project", "computed", "state", "pro", "bugs", "ref", "selectedBug", "bugLogs", "filteredBugs", "searchQuery", "currentFilter", "tableLoading", "bugDetailVisible", "updateDialogVisible", "updateLoading", "chart1Box", "chart2Box", "currentPage", "pageSize", "STATUS_MAP", "type", "icon", "class", "statusOptions", "label", "value", "updateForm", "id", "status", "remark", "bugsByStatus", "pending", "filter", "bug", "inProgress", "completed", "unnecessary", "closed", "bugCounts", "total", "length", "statCards", "searchFilteredBugs", "query", "toLowerCase", "desc", "includes", "interface_url", "String", "displayBugs", "startIndex", "endIndex", "slice", "fetchBugs", "async", "$api", "ElMessage", "error", "response", "getBugs", "data", "filterBugs", "console", "showBugInfo", "getBugLogs", "openUpdateDialog", "updateBug", "trim", "success", "message", "duration", "logs", "updatedBug", "find", "b", "warning", "deleteBug", "<PERSON><PERSON><PERSON><PERSON>", "$chart", "chartData", "chartLabels", "chart1", "chart2", "name", "handleSizeChange", "size", "handleCurrentChange", "page", "getStatusType", "getStatusClass", "getTimelineType", "handle", "getActivityIcon", "getRowClass", "row", "onMounted", "nextTick", "watch", "deep", "rTime", "time", "$tools", "rDate", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_scrollbar", "height", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_component_el_badge", "hidden", "_component_el_button", "_component_el_icon", "_unref", "Warning", "_toDisplayString", "_hoisted_5", "_component_el_row", "gutter", "_Fragment", "_renderList", "item", "index", "_createBlock", "_component_el_col", "span", "key", "_normalizeClass", "_hoisted_6", "_resolveDynamicComponent", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "text", "Refresh", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_el_radio_group", "$event", "onChange", "_cache", "val", "_component_el_radio_button", "_component_el_input", "placeholder", "clearable", "prefix", "_withCtx", "Search", "_component_el_table", "style", "onRowClick", "_component_el_table_column", "default", "props", "_hoisted_22", "create_time", "width", "scope", "prop", "_hoisted_23", "_hoisted_24", "_component_Time", "align", "_component_el_tag", "effect", "fixed", "_hoisted_25", "_component_el_tooltip", "content", "placement", "circle", "plain", "onClick", "_withModifiers", "View", "Edit", "_component_el_popconfirm", "title", "onConfirm", "reference", "Delete", "_hoisted_26", "_component_el_pagination", "layout", "onSizeChange", "onCurrentChange", "_component_el_drawer", "direction", "header", "_hoisted_27", "_hoisted_28", "_component_el_card", "shadow", "_hoisted_29", "InfoFilled", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "_hoisted_30", "_hoisted_31", "Tickets", "Result", "result", "info", "showbtn", "_hoisted_32", "_component_el_timeline", "activity", "_component_el_timeline_item", "timestamp", "hollow", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "User", "update_user", "_hoisted_37", "_component_el_empty", "description", "_hoisted_38", "_component_el_dialog", "footer", "_hoisted_42", "loading", "Check", "_component_el_form", "model", "_component_el_form_item", "_hoisted_39", "_hoisted_41", "autosize", "minRows", "maxRows", "__exports__", "_component_el_tabs", "$props", "_component_el_tab_pane", "response_header", "_component_Editor", "readOnly", "response_body", "lang", "theme", "onWheel", "innerHTML", "requests_body", "_component_el_collapse", "$data", "activeNames", "_component_el_collapse_item", "method", "url", "requests_header", "log_data", "disabled", "status_cede", "run_time", "$options", "getInterfaces", "addBugDlg", "closeDialogResult", "saveBug", "bugForm", "_component_el_select", "interface", "interfaces", "iter", "_component_el_option", "autocomplete", "mapState", "components", "Editor", "methods", "this", "createBugs", "$message", "getNewInterfaces", "render"], "sourceRoot": ""}