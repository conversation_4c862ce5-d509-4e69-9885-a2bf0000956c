"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[926],{64230:function(e,t,a){a.d(t,{A:function(){return g}});var i=a(56768),s=a(24232);function l(e,t,a,l,o,r){return(0,i.uX)(),(0,i.CE)("span",null,(0,s.v_)(o.displayValue),1)}let o=0;const r="webkit moz ms o".split(" ");let n,c;const d="undefined"===typeof window;if(d)n=function(){},c=function(){};else{let e;n=window.requestAnimationFrame,c=window.cancelAnimationFrame;for(let t=0;t<r.length;t++){if(n&&c)break;e=r[t],n=n||window[e+"RequestAnimationFrame"],c=c||window[e+"CancelAnimationFrame"]||window[e+"CancelRequestAnimationFrame"]}n&&c||(n=function(e){const t=(new Date).getTime(),a=Math.max(0,16-(t-o)),i=window.setTimeout(()=>{e(t+a)},a);return o=t+a,i},c=function(e){window.clearTimeout(e)})}var u={props:{startVal:{type:Number,required:!1,default:0},endVal:{type:Number,required:!1,default:2017},duration:{type:Number,required:!1,default:3e3},autoplay:{type:Boolean,required:!1,default:!0},decimals:{type:Number,required:!1,default:0,validator(e){return e>=0}},decimal:{type:String,required:!1,default:"."},separator:{type:String,required:!1,default:","},prefix:{type:String,required:!1,default:""},suffix:{type:String,required:!1,default:""},useEasing:{type:Boolean,required:!1,default:!0},easingFn:{type:Function,default(e,t,a,i){return a*(1-Math.pow(2,-10*e/i))*1024/1023+t}}},data(){return{localStartVal:this.startVal,displayValue:this.formatNumber(this.startVal),printVal:null,paused:!1,localDuration:this.duration,startTime:null,timestamp:null,remaining:null,rAF:null}},computed:{countDown(){return this.startVal>this.endVal}},watch:{startVal(){this.autoplay&&this.start()},endVal(){this.autoplay&&this.start()}},mounted(){this.autoplay&&this.start(),this.$emit("mountedCallback")},methods:{start(){this.localStartVal=this.startVal,this.startTime=null,this.localDuration=this.duration,this.paused=!1,this.rAF=n(this.count)},pauseResume(){this.paused?(this.resume(),this.paused=!1):(this.pause(),this.paused=!0)},pause(){c(this.rAF)},resume(){this.startTime=null,this.localDuration=+this.remaining,this.localStartVal=+this.printVal,n(this.count)},reset(){this.startTime=null,c(this.rAF),this.displayValue=this.formatNumber(this.startVal)},count(e){this.startTime||(this.startTime=e),this.timestamp=e;const t=e-this.startTime;this.remaining=this.localDuration-t,this.useEasing?this.countDown?this.printVal=this.localStartVal-this.easingFn(t,0,this.localStartVal-this.endVal,this.localDuration):this.printVal=this.easingFn(t,this.localStartVal,this.endVal-this.localStartVal,this.localDuration):this.countDown?this.printVal=this.localStartVal-(this.localStartVal-this.endVal)*(t/this.localDuration):this.printVal=this.localStartVal+(this.endVal-this.localStartVal)*(t/this.localDuration),this.countDown?this.printVal=this.printVal<this.endVal?this.endVal:this.printVal:this.printVal=this.printVal>this.endVal?this.endVal:this.printVal,this.displayValue=this.formatNumber(this.printVal),t<this.localDuration?this.rAF=n(this.count):this.$emit("callback")},isNumber(e){return!isNaN(parseFloat(e))},formatNumber(e){e=e.toFixed(this.decimals),e+="";const t=e.split(".");let a=t[0];const i=t.length>1?this.decimal+t[1]:"",s=/(\d+)(\d{3})/;if(this.separator&&!this.isNumber(this.separator))while(s.test(a))a=a.replace(s,"$1"+this.separator+"$2");return this.prefix+a+i+this.suffix}},destroyed(){c(this.rAF)}},h=a(71241);const m=(0,h.A)(u,[["render",l]]);var p=m,g=p;"undefined"!==typeof window&&window.Vue&&window.Vue.component("count-to",p)},78926:function(e,t,a){a.r(t),a.d(t,{default:function(){return je}});var i=a(56768),s=a(24232),l=a(45130);const o={class:"dashboard-container"},r={class:"welcome-card glass-effect"},n={class:"user-info"},c={key:0,class:"user-avatar animate__animated animate__fadeIn"},d={class:"icon-avatar"},u={class:"greeting-content"},h={class:"username animate__animated animate__fadeInDown"},m={class:"greeting animate__animated animate__fadeInUp"},p={class:"weather-info animate__animated animate__fadeInUp"},g={class:"stats-container"},k={class:"stat-icon"},f={class:"stat-content"},b={class:"stat-title"},v={class:"stat-value"},y={class:"main-content"},F={class:"projects-container"},_={class:"card-header"},w={class:"header-actions"},V={key:0,class:"loading-container"},L={key:1,class:"empty-container"},C={key:2,class:"projects-grid"},j={class:"project-header"},D={class:"project-icon"},x={class:"project-name"},$={class:"project-description"},A={class:"project-footer"},P={class:"project-leader"},S={class:"project-date"},T={class:"project-actions"},I={class:"card-header"},W={class:"activity-content"},R={class:"activity-user"},X={class:"activity-text"},E={class:"sidebar-container"},q={class:"card-header"},N={class:"shortcuts-grid"},U=["onClick"],z={class:"shortcut-icon"},M={class:"shortcut-name"},B={class:"card-header"},K={class:"chart-container"},O={class:"card-header"},H={class:"teams-list"},Y={class:"team-icon"},G={class:"team-name"},J={class:"team-action"},Q={class:"dialog-footer"},Z={class:"dialog-footer"};function ee(e,t,a,ee,te,ae){const ie=(0,i.g2)("icon"),se=(0,i.g2)("el-avatar"),le=(0,i.g2)("Sunny"),oe=(0,i.g2)("el-icon"),re=(0,i.g2)("count-to"),ne=(0,i.g2)("Collection"),ce=(0,i.g2)("Plus"),de=(0,i.g2)("el-button"),ue=(0,i.g2)("EditPen"),he=(0,i.g2)("Delete"),me=(0,i.g2)("el-skeleton"),pe=(0,i.g2)("el-empty"),ge=(0,i.g2)("User"),ke=(0,i.g2)("Calendar"),fe=(0,i.g2)("el-checkbox"),be=(0,i.g2)("Right"),ve=(0,i.g2)("el-card"),ye=(0,i.g2)("Bell"),Fe=(0,i.g2)("el-link"),_e=(0,i.g2)("el-timeline-item"),we=(0,i.g2)("el-timeline"),Ve=(0,i.g2)("Operation"),Le=(0,i.g2)("DataLine"),Ce=(0,i.g2)("RadarChart"),je=(0,i.g2)("UserFilled"),De=(0,i.g2)("el-input"),xe=(0,i.g2)("el-form-item"),$e=(0,i.g2)("el-form"),Ae=(0,i.g2)("el-dialog"),Pe=(0,i.g2)("el-scrollbar");return(0,i.uX)(),(0,i.Wv)(Pe,{height:"100vh"},{default:(0,i.k6)(()=>[(0,i.Lk)("div",o,[(0,i.Lk)("div",r,[(0,i.Lk)("div",n,[ae.avatar&&(ae.avatar.startsWith("logos:")||ae.avatar.startsWith("cryptocurrency-color:")||ae.avatar.startsWith("mdi:")||ae.avatar.startsWith("streamline-emojis:"))?((0,i.uX)(),(0,i.CE)("div",c,[(0,i.Lk)("div",d,[(0,i.bF)(ie,{icon:ae.avatar},null,8,["icon"])])])):((0,i.uX)(),(0,i.Wv)(se,{key:1,size:80,src:ae.avatar||"/avatar.png",class:"animate__animated animate__fadeIn"},null,8,["src"])),(0,i.Lk)("div",u,[(0,i.Lk)("h2",h,(0,s.v_)(ae.username),1),(0,i.Lk)("div",m,(0,s.v_)(ae.greeting()),1),(0,i.Lk)("div",p,[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(le)]),_:1}),t[12]||(t[12]=(0,i.eW)(" 今天晴，20℃ - 32℃ "))])])]),(0,i.Lk)("div",g,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(te.statistics,(e,t)=>((0,i.uX)(),(0,i.CE)("div",{class:"stat-item animate__animated animate__fadeInRight",key:t,style:(0,s.Tr)({animationDelay:.1*t+"s"})},[(0,i.Lk)("div",k,[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)(e.icon)))]),_:2},1024)]),(0,i.Lk)("div",f,[(0,i.Lk)("div",b,(0,s.v_)(e.title),1),(0,i.Lk)("div",v,[(0,i.bF)(re,{"start-val":0,"end-val":e.value,duration:2e3,separator:","},null,8,["end-val"])])])],4))),128))])]),(0,i.Lk)("div",y,[(0,i.Lk)("div",F,[(0,i.bF)(ve,{class:"main-card projects-card",shadow:"hover"},{header:(0,i.k6)(()=>[(0,i.Lk)("div",_,[(0,i.Lk)("h3",null,[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(ne)]),_:1}),t[13]||(t[13]=(0,i.eW)(" 我的项目 "))]),(0,i.Lk)("div",w,[(0,i.bF)(de,{type:"primary",onClick:ae.showAddDialog,plain:"",round:"",size:"small"},{default:(0,i.k6)(()=>[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(ce)]),_:1}),t[14]||(t[14]=(0,i.eW)("新建项目 "))]),_:1,__:[14]},8,["onClick"]),(0,i.bF)(de,{type:"warning",onClick:t[0]||(t[0]=e=>ae.handleCommand("update")),plain:"",round:"",size:"small",disabled:!ae.hasSelectedProject},{default:(0,i.k6)(()=>[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(ue)]),_:1}),t[15]||(t[15]=(0,i.eW)("修改项目 "))]),_:1,__:[15]},8,["disabled"]),(0,i.bF)(de,{type:"danger",onClick:t[1]||(t[1]=e=>ae.handleCommand("delete")),plain:"",round:"",size:"small",disabled:!ae.hasSelectedProject},{default:(0,i.k6)(()=>[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(he)]),_:1}),t[16]||(t[16]=(0,i.eW)("删除项目 "))]),_:1,__:[16]},8,["disabled"])])])]),default:(0,i.k6)(()=>[te.loading?((0,i.uX)(),(0,i.CE)("div",V,[(0,i.bF)(me,{rows:3,animated:""})])):0===te.pro_list.length?((0,i.uX)(),(0,i.CE)("div",L,[(0,i.bF)(pe,{description:"暂无项目，点击新建开始您的第一个项目吧！"})])):((0,i.uX)(),(0,i.CE)("div",C,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(te.pro_list,(e,a)=>((0,i.uX)(),(0,i.CE)("div",{key:e.id,class:"project-card-wrapper animate__animated animate__fadeIn",style:(0,s.Tr)({animationDelay:.05*a+"s"})},[(0,i.bF)(ve,{class:(0,s.C4)(["project-card",{"project-selected":ae.isProjectSelected(e.id)}]),shadow:"hover",onClick:t=>ae.clickView(e)},{default:(0,i.k6)(()=>[(0,i.Lk)("div",j,[(0,i.Lk)("div",D,[(0,i.bF)(ie,{icon:e.icon},null,8,["icon"])])]),(0,i.Lk)("h4",x,(0,s.v_)(e.name),1),(0,i.Lk)("div",$,(0,s.v_)(e.desc),1),(0,i.Lk)("div",A,[(0,i.Lk)("div",P,[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(ge)]),_:1}),(0,i.eW)(" "+(0,s.v_)(e.leader),1)]),(0,i.Lk)("div",S,[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(ke)]),_:1}),(0,i.eW)(" "+(0,s.v_)(ae.formatDate(e.create_time)),1)])]),(0,i.Lk)("div",T,[(0,i.bF)(fe,{modelValue:te.checkList,"onUpdate:modelValue":t[2]||(t[2]=e=>te.checkList=e),label:e.id,onClick:t[3]||(t[3]=(0,l.D$)(()=>{},["stop"])),onChange:ae.onProjectSelectChange,class:"select-checkbox"},null,8,["modelValue","label","onChange"]),(0,i.bF)(de,{type:"primary",circle:"",size:"small"},{default:(0,i.k6)(()=>[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(be)]),_:1})]),_:1})])]),_:2},1032,["class","onClick"])],4))),128))]))]),_:1}),(0,i.bF)(ve,{class:"main-card activities-card",shadow:"hover"},{header:(0,i.k6)(()=>[(0,i.Lk)("div",I,[(0,i.Lk)("h3",null,[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(ye)]),_:1}),t[17]||(t[17]=(0,i.eW)(" 最新动态 "))]),(0,i.bF)(Fe,{type:"primary"},{default:(0,i.k6)(()=>t[18]||(t[18]=[(0,i.eW)("查看全部")])),_:1,__:[18]})])]),default:(0,i.k6)(()=>[(0,i.bF)(we,null,{default:(0,i.k6)(()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(te.dynamics,(e,t)=>((0,i.uX)(),(0,i.Wv)(_e,{key:e.id,timestamp:e.time,type:ae.getActivityType(t),size:"large"},{default:(0,i.k6)(()=>[(0,i.Lk)("div",W,[(0,i.Lk)("span",R,(0,s.v_)(e.name),1),(0,i.Lk)("span",X,(0,s.v_)(e.content),1)])]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1})]),(0,i.Lk)("div",E,[(0,i.bF)(ve,{class:"main-card shortcut-card",shadow:"hover"},{header:(0,i.k6)(()=>[(0,i.Lk)("div",q,[(0,i.Lk)("h3",null,[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(Ve)]),_:1}),t[19]||(t[19]=(0,i.eW)(" 快捷导航 "))])])]),default:(0,i.k6)(()=>[(0,i.Lk)("div",N,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(te.shortcut,(e,t)=>((0,i.uX)(),(0,i.CE)("div",{key:e.id,class:"shortcut-item animate__animated animate__zoomIn",style:(0,s.Tr)({animationDelay:.1*t+"s"}),onClick:t=>ae.handleShortcutClick(e)},[(0,i.Lk)("div",z,[(0,i.bF)(ie,{icon:e.icon},null,8,["icon"])]),(0,i.Lk)("div",M,(0,s.v_)(e.name),1)],12,U))),128))])]),_:1}),(0,i.bF)(ve,{class:"main-card chart-card",shadow:"hover"},{header:(0,i.k6)(()=>[(0,i.Lk)("div",B,[(0,i.Lk)("h3",null,[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(Le)]),_:1}),t[20]||(t[20]=(0,i.eW)(" 最近7天用例数量 "))])])]),default:(0,i.k6)(()=>[(0,i.Lk)("div",K,[(0,i.bF)(Ce)])]),_:1}),(0,i.bF)(ve,{class:"main-card teams-card",shadow:"hover"},{header:(0,i.k6)(()=>[(0,i.Lk)("div",O,[(0,i.Lk)("h3",null,[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(je)]),_:1}),t[21]||(t[21]=(0,i.eW)(" 团队 "))])])]),default:(0,i.k6)(()=>[(0,i.Lk)("div",H,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(te.teamList,(e,t)=>((0,i.uX)(),(0,i.CE)("div",{key:e.name,class:"team-item animate__animated animate__fadeInUp",style:(0,s.Tr)({animationDelay:.1*t+"s"})},[(0,i.Lk)("div",Y,[(0,i.bF)(ie,{icon:e.icon},null,8,["icon"])]),(0,i.Lk)("div",G,(0,s.v_)(e.name),1),(0,i.Lk)("div",J,[(0,i.bF)(de,{type:"primary",text:"",circle:""},{default:(0,i.k6)(()=>[(0,i.bF)(oe,null,{default:(0,i.k6)(()=>[(0,i.bF)(be)]),_:1})]),_:1})])],4))),128))])]),_:1})])]),(0,i.bF)(Ae,{modelValue:te.dialogVisible.add,"onUpdate:modelValue":t[7]||(t[7]=e=>te.dialogVisible.add=e),title:"创建新项目",width:"500px","close-on-click-modal":!1,"before-close":ae.handleDialogClose,top:"5vh","align-center":""},{footer:(0,i.k6)(()=>[(0,i.Lk)("span",Q,[(0,i.bF)(de,{onClick:ae.handleDialogClose},{default:(0,i.k6)(()=>t[22]||(t[22]=[(0,i.eW)("取消")])),_:1,__:[22]},8,["onClick"]),(0,i.bF)(de,{type:"primary",onClick:t[6]||(t[6]=e=>ae.submitProjectForm("add"))},{default:(0,i.k6)(()=>t[23]||(t[23]=[(0,i.eW)("创建")])),_:1,__:[23]})])]),default:(0,i.k6)(()=>[(0,i.bF)($e,{ref:"addFormRef",model:te.projectForm,rules:te.formRules,"label-position":"top","status-icon":""},{default:(0,i.k6)(()=>[(0,i.bF)(xe,{label:"项目名称",prop:"name"},{default:(0,i.k6)(()=>[(0,i.bF)(De,{modelValue:te.projectForm.name,"onUpdate:modelValue":t[4]||(t[4]=e=>te.projectForm.name=e),placeholder:"请输入项目名称",clearable:""},null,8,["modelValue"])]),_:1}),(0,i.bF)(xe,{label:"项目描述",prop:"desc"},{default:(0,i.k6)(()=>[(0,i.bF)(De,{modelValue:te.projectForm.desc,"onUpdate:modelValue":t[5]||(t[5]=e=>te.projectForm.desc=e),type:"textarea",placeholder:"请输入项目描述",autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","before-close"]),(0,i.bF)(Ae,{modelValue:te.dialogVisible.edit,"onUpdate:modelValue":t[11]||(t[11]=e=>te.dialogVisible.edit=e),title:"编辑项目",width:"500px","close-on-click-modal":!1,"before-close":ae.handleDialogClose,top:"5vh","align-center":""},{footer:(0,i.k6)(()=>[(0,i.Lk)("span",Z,[(0,i.bF)(de,{onClick:ae.handleDialogClose},{default:(0,i.k6)(()=>t[24]||(t[24]=[(0,i.eW)("取消")])),_:1,__:[24]},8,["onClick"]),(0,i.bF)(de,{type:"primary",onClick:t[10]||(t[10]=e=>ae.submitProjectForm("edit"))},{default:(0,i.k6)(()=>t[25]||(t[25]=[(0,i.eW)("保存")])),_:1,__:[25]})])]),default:(0,i.k6)(()=>[(0,i.bF)($e,{ref:"editFormRef",model:te.projectForm,rules:te.formRules,"label-position":"top","status-icon":""},{default:(0,i.k6)(()=>[(0,i.bF)(xe,{label:"项目名称",prop:"name"},{default:(0,i.k6)(()=>[(0,i.bF)(De,{modelValue:te.projectForm.name,"onUpdate:modelValue":t[8]||(t[8]=e=>te.projectForm.name=e),placeholder:"请输入项目名称",clearable:""},null,8,["modelValue"])]),_:1}),(0,i.bF)(xe,{label:"项目描述",prop:"desc"},{default:(0,i.k6)(()=>[(0,i.bF)(De,{modelValue:te.projectForm.desc,"onUpdate:modelValue":t[9]||(t[9]=e=>te.projectForm.desc=e),type:"textarea",placeholder:"请输入项目描述",autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","before-close"])])]),_:1})}a(44114),a(18111),a(22489),a(20116),a(61701);var te=a(82484);const ae={ref:"chart",style:{height:"300px"}};function ie(e,t,a,s,l,o){return(0,i.uX)(),(0,i.CE)("div",null,[(0,i.Lk)("div",ae,null,512)])}var se=a(22212),le=a(82563),oe=a(40623),re=a(74016),ne=a(43285),ce=a(75095);se.Y([oe.a,re.a,ne.a,ce.a]);for(var de=new Date,ue=[],he=6;he>=0;he--){var me=new Date(de);me.setDate(de.getDate()-he);var pe=me.getMonth()+1,ge=me.getDate(),ke=pe+"-"+ge;ue.push(ke)}var fe={data(){return{chartData:null}},mounted(){this.renderChart()},methods:{renderChart(){const e=this.$refs.chart,t=le.Ts(e),a={xAxis:{type:"category",data:ue,axisTick:{show:!1},axisLine:{show:!1}},tooltip:{position:"top",trigger:"axis",axisPointer:{type:"none"},formatter:function(e){const t=e[0].value;return"用例数："+t}},yAxis:{type:"value"},series:[{data:[120,200,150,80,315,110,130],type:"bar"}]};a&&t.setOption(a)}}},be=a(71241);const ve=(0,be.A)(fe,[["render",ie]]);var ye=ve,Fe=a(64230),_e=a(51219),we=a(12933),Ve=a(57477),Le={components:{Icon:te.In,RadarChart:ye,CountTo:Fe.A},data(){return{loading:!1,pro_list:[],checkList:[],selectedProject:null,dialogVisible:{add:!1,edit:!1},projectForm:{id:"",name:"",desc:"",icon:"logos:github-icon"},formRules:{name:[{required:!0,message:"请输入项目名称",trigger:"blur"},{min:2,max:30,message:"长度在 2 到 30 个字符",trigger:"blur"}],desc:[{required:!0,message:"请输入项目描述",trigger:"blur"},{max:100,message:"长度不能超过100个字符",trigger:"blur"}]},statistics:[{title:"参与项目",value:99,icon:"Collection"},{title:"用例数量",value:16358,icon:"Document"},{title:"项目访问",value:53233069,icon:"View"}],dynamics:[{id:1,name:"admin",content:"查看了测试报告",time:"2小时前"},{id:2,name:"小明",content:"提交了bug",time:"一天前"},{id:3,name:"小泊",content:"修改了登录接口",time:"三天前"},{id:4,name:"小泊",content:"新增了接口",time:"七天前"},{id:5,name:"小文",content:"执行了测试计划",time:"七天前"},{id:6,name:"猴哥",content:"新增了定时任务",time:"七天前"}],teamList:[{name:"团队A",icon:"logos:web-dev-icon"},{name:"团队B",icon:"logos:github-icon"},{name:"团队C",icon:"cryptocurrency-color:sc"},{name:"团队D",icon:"cryptocurrency-color:sc"}],availableIcons:[{id:1,icon:"logos:github-icon"},{id:2,icon:"logos:baker-street"},{id:3,icon:"cryptocurrency-color:xpr"},{id:4,icon:"cryptocurrency-color:ethos"},{id:5,icon:"cryptocurrency-color:sc"},{id:6,icon:"logos:nocodb"},{id:7,icon:"logos:web-dev-icon"},{id:8,icon:"cryptocurrency-color:gxs"},{id:9,icon:"cryptocurrency-color:one"},{id:10,icon:"cryptocurrency-color:powr"},{id:11,icon:"cryptocurrency-color:uni"},{id:12,icon:"cryptocurrency-color:waves"},{id:13,icon:"cryptocurrency-color:atom"},{id:14,icon:"cryptocurrency-color:sky"}],shortcut:[{id:1,name:"退出登录",icon:"logos:google-360suite",action:"logout"}],chartLoaded:!1}},computed:{username(){return sessionStorage.getItem("username")||"用户"},avatar(){return sessionStorage.getItem("avatar")||"mdi:account-circle"},hasSelectedProject(){return this.checkList.length>0}},mounted(){this.getAllProjects(),setTimeout(()=>{this.chartLoaded=!0},500)},methods:{getActivityType(e){const t=["primary","success","info","warning","danger"];return t[e%t.length]},getActivityIcon(e){return e.content.includes("报告")?Ve.Bell:e.content.includes("bug")?Ve.Delete:e.content.includes("接口")?Ve.Setting:e.content.includes("测试")?Ve.DataLine:e.content.includes("任务")?Ve.Calendar:Ve.MoreFilled},formatDate(e){if(!e)return"";try{if(this.$tools&&this.$tools.rDate)return this.$tools.rDate(e);const t=new Date(e),a=new Date,i=Math.floor((a-t)/1e3);return i<60?"刚刚":i<3600?`${Math.floor(i/60)}分钟前`:i<86400?`${Math.floor(i/3600)}小时前`:i<2592e3?`${Math.floor(i/86400)}天前`:i<31536e3?`${Math.floor(i/2592e3)}个月前`:`${Math.floor(i/31536e3)}年前`}catch(t){return console.error("日期格式化错误:",t),e}},greeting(){const e=(new Date).getHours();let t="";return t=e>=0&&e<8?"早上好！今天是全新的一天，让我们充满活力地开始吧！":e>=8&&e<12?"上午好！加油！有计划地完成任务，让每一分钟都值得！":e>=12&&e<18?"下午好！继续保持精神和积极的态度，目标就在前方！":"晚上好！给自己一份轻松，给家人一份关爱，明天即将到来，期待更美好的一天！",t},async getAllProjects(){this.loading=!0;try{const e=await this.$api.getProjects();e&&200===e.status?this.pro_list=(e.data||[]).map((e,t)=>({...e,icon:this.availableIcons[t%this.availableIcons.length].icon})):(console.error("获取项目列表失败",e),_e.nk.error("获取项目列表失败"))}catch(e){console.error("获取项目列表失败",e),_e.nk.error("获取项目列表失败, 请检查网络或API连接")}finally{this.loading=!1}},selectProject(e){this.checkList.includes(e.id)?this.checkList=this.checkList.filter(t=>t!==e.id):this.checkList=[e.id],this.selectedProject=e},isProjectSelected(e){return this.checkList.includes(e)},onProjectSelectChange(){this.checkList.length>1&&(this.checkList=[this.checkList[this.checkList.length-1]])},clickView(e){this.$store.commit("selectPro",e),this.$router.push({name:"home"})},handleCommand(e){if("update"===e){if(0===this.checkList.length)return void _e.nk.warning("请勾选项目后再操作！");const e=this.checkList[0],t=this.pro_list.find(t=>t.id===e);t&&(Object.assign(this.projectForm,t),this.dialogVisible.edit=!0)}else if("delete"===e){if(0===this.checkList.length)return void _e.nk.warning("请勾选项目后再操作！");we.s.confirm("确定要删除该项目吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteProject(this.checkList[0])}).catch(()=>{(0,_e.nk)({type:"info",message:"已取消删除"})})}},handleShortcutClick(e){"logout"===e.action&&this.logout()},showAddDialog(){Object.assign(this.projectForm,{id:"",name:"",desc:"",icon:this.availableIcons[0].icon}),this.dialogVisible.add=!0},handleDialogClose(){this.dialogVisible.add=!1,this.dialogVisible.edit=!1,this.checkList=[],this.$nextTick(()=>{this.$refs.addFormRef&&this.$refs.addFormRef.resetFields(),this.$refs.editFormRef&&this.$refs.editFormRef.resetFields()})},async submitProjectForm(e){const t="add"===e?this.$refs.addFormRef:this.$refs.editFormRef;if(!t)return;const a=await t.validate().catch(()=>!1);if(a)try{if("add"===e){const e=await this.$api.createProjects(this.projectForm);201===e.status?(_e.nk.success("项目创建成功"),this.dialogVisible.add=!1,await this.getAllProjects()):_e.nk.error("项目创建失败")}else{const e=await this.$api.updateProjects(this.projectForm.id,this.projectForm);200===e.status?(_e.nk.success("项目更新成功"),this.dialogVisible.edit=!1,await this.getAllProjects()):_e.nk.error("项目更新失败")}}catch(i){console.error(i),_e.nk.error("add"===e?"创建项目失败":"更新项目失败")}},async deleteProject(e){try{const t=await this.$api.delProject(e);204===t.status?(_e.nk.success("项目已删除"),this.checkList=[],await this.getAllProjects()):_e.nk.error("删除项目失败")}catch(t){console.error(t),_e.nk.error("删除项目失败")}},logout(){(0,_e.nk)({message:"已注销登录状态",type:"warning",duration:1e3}),sessionStorage.removeItem("token"),sessionStorage.removeItem("username"),sessionStorage.removeItem("avatar"),this.$router.push({name:"login"})},goToApiTest(){this.$router.push("/api-test")},goToPerformanceTest(){this.$router.push("/performance-test")},goToTestPlan(){this.$router.push("/test-plan")},goToTestReport(){this.$router.push("/test-report")}}};const Ce=(0,be.A)(Le,[["render",ee],["__scopeId","data-v-767108c7"]]);var je=Ce}}]);
//# sourceMappingURL=926.8c4a395d.js.map