"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[103],{82484:function(t,n,e){e.d(n,{In:function(){return Ht}});e(44114),e(18111),e(22489),e(20116),e(7588),e(61701),e(17642),e(58004),e(33853),e(45876),e(32475),e(15024),e(31698),e(14603),e(47566),e(98721);var o=e(56768);const i=/^[a-z0-9]+(-[a-z0-9]+)*$/,r=(t,n,e,o="")=>{const i=t.split(":");if("@"===t.slice(0,1)){if(i.length<2||i.length>3)return null;o=i.shift().slice(1)}if(i.length>3||!i.length)return null;if(i.length>1){const t=i.pop(),e=i.pop(),r={provider:i.length>0?i[0]:o,prefix:e,name:t};return n&&!s(r)?null:r}const r=i[0],c=r.split("-");if(c.length>1){const t={provider:o,prefix:c.shift(),name:c.join("-")};return n&&!s(t)?null:t}if(e&&""===o){const t={provider:o,prefix:"",name:r};return n&&!s(t,e)?null:t}return null},s=(t,n)=>!!t&&!(!(n&&""===t.prefix||t.prefix)||!t.name),c=Object.freeze({left:0,top:0,width:16,height:16}),a=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),l=Object.freeze({...c,...a}),u=Object.freeze({...l,body:"",hidden:!1});function f(t,n){const e={};!t.hFlip!==!n.hFlip&&(e.hFlip=!0),!t.vFlip!==!n.vFlip&&(e.vFlip=!0);const o=((t.rotate||0)+(n.rotate||0))%4;return o&&(e.rotate=o),e}function d(t,n){const e=f(t,n);for(const o in u)o in a?o in t&&!(o in e)&&(e[o]=a[o]):o in n?e[o]=n[o]:o in t&&(e[o]=t[o]);return e}function p(t,n){const e=t.icons,o=t.aliases||Object.create(null),i=Object.create(null);function r(t){if(e[t])return i[t]=[];if(!(t in i)){i[t]=null;const n=o[t]&&o[t].parent,e=n&&r(n);e&&(i[t]=[n].concat(e))}return i[t]}return Object.keys(e).concat(Object.keys(o)).forEach(r),i}function h(t,n,e){const o=t.icons,i=t.aliases||Object.create(null);let r={};function s(t){r=d(o[t]||i[t],r)}return s(n),e.forEach(s),d(t,r)}function g(t,n){const e=[];if("object"!==typeof t||"object"!==typeof t.icons)return e;t.not_found instanceof Array&&t.not_found.forEach(t=>{n(t,null),e.push(t)});const o=p(t);for(const i in o){const r=o[i];r&&(n(i,h(t,i,r)),e.push(i))}return e}const b={provider:"",aliases:{},not_found:{},...c};function m(t,n){for(const e in n)if(e in t&&typeof t[e]!==typeof n[e])return!1;return!0}function y(t){if("object"!==typeof t||null===t)return null;const n=t;if("string"!==typeof n.prefix||!t.icons||"object"!==typeof t.icons)return null;if(!m(t,b))return null;const e=n.icons;for(const i in e){const t=e[i];if(!i||"string"!==typeof t.body||!m(t,u))return null}const o=n.aliases||Object.create(null);for(const i in o){const t=o[i],n=t.parent;if(!i||"string"!==typeof n||!e[n]&&!o[n]||!m(t,u))return null}return n}const v=Object.create(null);function x(t,n){return{provider:t,prefix:n,icons:Object.create(null),missing:new Set}}function w(t,n){const e=v[t]||(v[t]=Object.create(null));return e[n]||(e[n]=x(t,n))}function k(t,n){return y(n)?g(n,(n,e)=>{e?t.icons[n]=e:t.missing.add(n)}):[]}function j(t,n,e){try{if("string"===typeof e.body)return t.icons[n]={...e},!0}catch(o){}return!1}let O=!1;function I(t){return"boolean"===typeof t&&(O=t),O}function E(t){const n="string"===typeof t?r(t,!0,O):t;if(n){const t=w(n.provider,n.prefix),e=n.name;return t.icons[e]||(t.missing.has(e)?null:void 0)}}function F(t,n){const e=r(t,!0,O);if(!e)return!1;const o=w(e.provider,e.prefix);return n?j(o,e.name,n):(o.missing.add(e.name),!0)}function L(t,n){if("object"!==typeof t)return!1;if("string"!==typeof n&&(n=t.provider||""),O&&!n&&!t.prefix){let n=!1;return y(t)&&(t.prefix="",g(t,(t,e)=>{F(t,e)&&(n=!0)})),n}const e=t.prefix;if(!s({provider:n,prefix:e,name:"a"}))return!1;const o=w(n,e);return!!k(o,t)}const T=Object.freeze({width:null,height:null}),S=Object.freeze({...T,...a}),C=/(-?[0-9.]*[0-9]+[0-9.]*)/g,_=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function M(t,n,e){if(1===n)return t;if(e=e||100,"number"===typeof t)return Math.ceil(t*n*e)/e;if("string"!==typeof t)return t;const o=t.split(C);if(null===o||!o.length)return t;const i=[];let r=o.shift(),s=_.test(r);while(1){if(s){const t=parseFloat(r);isNaN(t)?i.push(r):i.push(Math.ceil(t*n*e)/e)}else i.push(r);if(r=o.shift(),void 0===r)return i.join("");s=!s}}function z(t,n="defs"){let e="";const o=t.indexOf("<"+n);while(o>=0){const i=t.indexOf(">",o),r=t.indexOf("</"+n);if(-1===i||-1===r)break;const s=t.indexOf(">",r);if(-1===s)break;e+=t.slice(i+1,r).trim(),t=t.slice(0,o).trim()+t.slice(s+1)}return{defs:e,content:t}}function A(t,n){return t?"<defs>"+t+"</defs>"+n:n}function P(t,n,e){const o=z(t);return A(o.defs,n+o.content+e)}const R=t=>"unset"===t||"undefined"===t||"none"===t;function $(t,n){const e={...l,...t},o={...S,...n},i={left:e.left,top:e.top,width:e.width,height:e.height};let r=e.body;[e,o].forEach(t=>{const n=[],e=t.hFlip,o=t.vFlip;let s,c=t.rotate;switch(e?o?c+=2:(n.push("translate("+(i.width+i.left).toString()+" "+(0-i.top).toString()+")"),n.push("scale(-1 1)"),i.top=i.left=0):o&&(n.push("translate("+(0-i.left).toString()+" "+(i.height+i.top).toString()+")"),n.push("scale(1 -1)"),i.top=i.left=0),c<0&&(c-=4*Math.floor(c/4)),c%=4,c){case 1:s=i.height/2+i.top,n.unshift("rotate(90 "+s.toString()+" "+s.toString()+")");break;case 2:n.unshift("rotate(180 "+(i.width/2+i.left).toString()+" "+(i.height/2+i.top).toString()+")");break;case 3:s=i.width/2+i.left,n.unshift("rotate(-90 "+s.toString()+" "+s.toString()+")");break}c%2===1&&(i.left!==i.top&&(s=i.left,i.left=i.top,i.top=s),i.width!==i.height&&(s=i.width,i.width=i.height,i.height=s)),n.length&&(r=P(r,'<g transform="'+n.join(" ")+'">',"</g>"))});const s=o.width,c=o.height,a=i.width,u=i.height;let f,d;null===s?(d=null===c?"1em":"auto"===c?u:c,f=M(d,a/u)):(f="auto"===s?a:s,d=null===c?M(f,u/a):"auto"===c?u:c);const p={},h=(t,n)=>{R(n)||(p[t]=n.toString())};h("width",f),h("height",d);const g=[i.left,i.top,a,u];return p.viewBox=g.join(" "),{attributes:p,viewBox:g,body:r}}const N=/\sid="(\S+)"/g,U="IconifyId"+Date.now().toString(16)+(16777216*Math.random()|0).toString(16);let q=0;function D(t,n=U){const e=[];let o;while(o=N.exec(t))e.push(o[1]);if(!e.length)return t;const i="suffix"+(16777216*Math.random()|Date.now()).toString(16);return e.forEach(e=>{const o="function"===typeof n?n(e):n+(q++).toString(),r=e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");t=t.replace(new RegExp('([#;"])('+r+')([")]|\\.[a-z])',"g"),"$1"+o+i+"$3")}),t=t.replace(new RegExp(i,"g"),""),t}const Q=Object.create(null);function B(t,n){Q[t]=n}function H(t){return Q[t]||Q[""]}function V(t){let n;if("string"===typeof t.resources)n=[t.resources];else if(n=t.resources,!(n instanceof Array)||!n.length)return null;const e={resources:n,path:t.path||"/",maxURL:t.maxURL||500,rotate:t.rotate||750,timeout:t.timeout||5e3,random:!0===t.random,index:t.index||0,dataAfterTimeout:!1!==t.dataAfterTimeout};return e}const G=Object.create(null),J=["https://api.simplesvg.com","https://api.unisvg.com"],K=[];while(J.length>0)1===J.length||Math.random()>.5?K.push(J.shift()):K.push(J.pop());function W(t,n){const e=V(n);return null!==e&&(G[t]=e,!0)}function X(t){return G[t]}G[""]=V({resources:["https://api.iconify.design"].concat(K)});const Y=()=>{let t;try{if(t=fetch,"function"===typeof t)return t}catch(n){}};let Z=Y();function tt(t,n){const e=X(t);if(!e)return 0;let o;if(e.maxURL){let t=0;e.resources.forEach(n=>{const e=n;t=Math.max(t,e.length)});const i=n+".json?icons=";o=e.maxURL-t-e.path.length-i.length}else o=0;return o}function nt(t){return 404===t}const et=(t,n,e)=>{const o=[],i=tt(t,n),r="icons";let s={type:r,provider:t,prefix:n,icons:[]},c=0;return e.forEach((e,a)=>{c+=e.length+1,c>=i&&a>0&&(o.push(s),s={type:r,provider:t,prefix:n,icons:[]},c=e.length),s.icons.push(e)}),o.push(s),o};function ot(t){if("string"===typeof t){const n=X(t);if(n)return n.path}return"/"}const it=(t,n,e)=>{if(!Z)return void e("abort",424);let o=ot(n.provider);switch(n.type){case"icons":{const t=n.prefix,e=n.icons,i=e.join(","),r=new URLSearchParams({icons:i});o+=t+".json?"+r.toString();break}case"custom":{const t=n.uri;o+="/"===t.slice(0,1)?t.slice(1):t;break}default:return void e("abort",400)}let i=503;Z(t+o).then(t=>{const n=t.status;if(200===n)return i=501,t.json();setTimeout(()=>{e(nt(n)?"abort":"next",n)})}).then(t=>{"object"===typeof t&&null!==t?setTimeout(()=>{e("success",t)}):setTimeout(()=>{404===t?e("abort",t):e("next",i)})}).catch(()=>{e("next",i)})},rt={prepare:et,send:it};function st(t){const n={loaded:[],missing:[],pending:[]},e=Object.create(null);t.sort((t,n)=>t.provider!==n.provider?t.provider.localeCompare(n.provider):t.prefix!==n.prefix?t.prefix.localeCompare(n.prefix):t.name.localeCompare(n.name));let o={provider:"",prefix:"",name:""};return t.forEach(t=>{if(o.name===t.name&&o.prefix===t.prefix&&o.provider===t.provider)return;o=t;const i=t.provider,r=t.prefix,s=t.name,c=e[i]||(e[i]=Object.create(null)),a=c[r]||(c[r]=w(i,r));let l;l=s in a.icons?n.loaded:""===r||a.missing.has(s)?n.missing:n.pending;const u={provider:i,prefix:r,name:s};l.push(u)}),n}function ct(t,n){t.forEach(t=>{const e=t.loaderCallbacks;e&&(t.loaderCallbacks=e.filter(t=>t.id!==n))})}function at(t){t.pendingCallbacksFlag||(t.pendingCallbacksFlag=!0,setTimeout(()=>{t.pendingCallbacksFlag=!1;const n=t.loaderCallbacks?t.loaderCallbacks.slice(0):[];if(!n.length)return;let e=!1;const o=t.provider,i=t.prefix;n.forEach(n=>{const r=n.icons,s=r.pending.length;r.pending=r.pending.filter(n=>{if(n.prefix!==i)return!0;const s=n.name;if(t.icons[s])r.loaded.push({provider:o,prefix:i,name:s});else{if(!t.missing.has(s))return e=!0,!0;r.missing.push({provider:o,prefix:i,name:s})}return!1}),r.pending.length!==s&&(e||ct([t],n.id),n.callback(r.loaded.slice(0),r.missing.slice(0),r.pending.slice(0),n.abort))})}))}let lt=0;function ut(t,n,e){const o=lt++,i=ct.bind(null,e,o);if(!n.pending.length)return i;const r={id:o,icons:n,callback:t,abort:i};return e.forEach(t=>{(t.loaderCallbacks||(t.loaderCallbacks=[])).push(r)}),i}function ft(t,n=!0,e=!1){const o=[];return t.forEach(t=>{const i="string"===typeof t?r(t,n,e):t;i&&o.push(i)}),o}var dt={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function pt(t,n,e,o){const i=t.resources.length,r=t.random?Math.floor(Math.random()*i):t.index;let s;if(t.random){let n=t.resources.slice(0);s=[];while(n.length>1){const t=Math.floor(Math.random()*n.length);s.push(n[t]),n=n.slice(0,t).concat(n.slice(t+1))}s=s.concat(n)}else s=t.resources.slice(r).concat(t.resources.slice(0,r));const c=Date.now();let a,l="pending",u=0,f=null,d=[],p=[];function h(){f&&(clearTimeout(f),f=null)}function g(){"pending"===l&&(l="aborted"),h(),d.forEach(t=>{"pending"===t.status&&(t.status="aborted")}),d=[]}function b(t,n){n&&(p=[]),"function"===typeof t&&p.push(t)}function m(){return{startTime:c,payload:n,status:l,queriesSent:u,queriesPending:d.length,subscribe:b,abort:g}}function y(){l="failed",p.forEach(t=>{t(void 0,a)})}function v(){d.forEach(t=>{"pending"===t.status&&(t.status="aborted")}),d=[]}function x(n,e,o){const i="success"!==e;switch(d=d.filter(t=>t!==n),l){case"pending":break;case"failed":if(i||!t.dataAfterTimeout)return;break;default:return}if("abort"===e)return a=o,void y();if(i)return a=o,void(d.length||(s.length?w():y()));if(h(),v(),!t.random){const e=t.resources.indexOf(n.resource);-1!==e&&e!==t.index&&(t.index=e)}l="completed",p.forEach(t=>{t(o)})}function w(){if("pending"!==l)return;h();const o=s.shift();if(void 0===o)return d.length?void(f=setTimeout(()=>{h(),"pending"===l&&(v(),y())},t.timeout)):void y();const i={status:"pending",resource:o,callback:(t,n)=>{x(i,t,n)}};d.push(i),u++,f=setTimeout(w,t.rotate),e(o,n,i.callback)}return"function"===typeof o&&p.push(o),setTimeout(w),m}function ht(t){const n={...dt,...t};let e=[];function o(){e=e.filter(t=>"pending"===t().status)}function i(t,i,r){const s=pt(n,t,i,(t,n)=>{o(),r&&r(t,n)});return e.push(s),s}function r(t){return e.find(n=>t(n))||null}const s={query:i,find:r,setIndex:t=>{n.index=t},getIndex:()=>n.index,cleanup:o};return s}function gt(){}const bt=Object.create(null);function mt(t){if(!bt[t]){const n=X(t);if(!n)return;const e=ht(n),o={config:n,redundancy:e};bt[t]=o}return bt[t]}function yt(t,n,e){let o,i;if("string"===typeof t){const n=H(t);if(!n)return e(void 0,424),gt;i=n.send;const r=mt(t);r&&(o=r.redundancy)}else{const n=V(t);if(n){o=ht(n);const e=t.resources?t.resources[0]:"",r=H(e);r&&(i=r.send)}}return o&&i?o.query(n,i,e)().abort:(e(void 0,424),gt)}function vt(){}function xt(t){t.iconsLoaderFlag||(t.iconsLoaderFlag=!0,setTimeout(()=>{t.iconsLoaderFlag=!1,at(t)}))}function wt(t){const n=[],e=[];return t.forEach(t=>{(t.match(i)?n:e).push(t)}),{valid:n,invalid:e}}function kt(t,n,e){function o(){const e=t.pendingIcons;n.forEach(n=>{e&&e.delete(n),t.icons[n]||t.missing.add(n)})}if(e&&"object"===typeof e)try{const n=k(t,e);if(!n.length)return void o()}catch(i){console.error(i)}o(),xt(t)}function jt(t,n){t instanceof Promise?t.then(t=>{n(t)}).catch(()=>{n(null)}):n(t)}function Ot(t,n){t.iconsToLoad?t.iconsToLoad=t.iconsToLoad.concat(n).sort():t.iconsToLoad=n,t.iconsQueueFlag||(t.iconsQueueFlag=!0,setTimeout(()=>{t.iconsQueueFlag=!1;const{provider:n,prefix:e}=t,o=t.iconsToLoad;if(delete t.iconsToLoad,!o||!o.length)return;const r=t.loadIcon;if(t.loadIcons&&(o.length>1||!r))return void jt(t.loadIcons(o,e,n),n=>{kt(t,o,n)});if(r)return void o.forEach(o=>{const i=r(o,e,n);jt(i,n=>{const i=n?{prefix:e,icons:{[o]:n}}:null;kt(t,[o],i)})});const{valid:s,invalid:c}=wt(o);if(c.length&&kt(t,c,null),!s.length)return;const a=e.match(i)?H(n):null;if(!a)return void kt(t,s,null);const l=a.prepare(n,e,s);l.forEach(e=>{yt(n,e,n=>{kt(t,e.icons,n)})})}))}const It=(t,n)=>{const e=ft(t,!0,I()),o=st(e);if(!o.pending.length){let t=!0;return n&&setTimeout(()=>{t&&n(o.loaded,o.missing,o.pending,vt)}),()=>{t=!1}}const i=Object.create(null),r=[];let s,c;return o.pending.forEach(t=>{const{provider:n,prefix:e}=t;if(e===c&&n===s)return;s=n,c=e,r.push(w(n,e));const o=i[n]||(i[n]=Object.create(null));o[e]||(o[e]=[])}),o.pending.forEach(t=>{const{provider:n,prefix:e,name:o}=t,r=w(n,e),s=r.pendingIcons||(r.pendingIcons=new Set);s.has(o)||(s.add(o),i[n][e].push(o))}),r.forEach(t=>{const n=i[t.provider][t.prefix];n.length&&Ot(t,n)}),n?ut(n,o,r):vt};function Et(t,n){const e={...t};for(const o in n){const t=n[o],i=typeof t;o in T?(null===t||t&&("string"===i||"number"===i))&&(e[o]=t):i===typeof e[o]&&(e[o]="rotate"===o?t%4:t)}return e}const Ft=/[\s,]+/;function Lt(t,n){n.split(Ft).forEach(n=>{const e=n.trim();switch(e){case"horizontal":t.hFlip=!0;break;case"vertical":t.vFlip=!0;break}})}function Tt(t,n=0){const e=t.replace(/^-?[0-9.]*/,"");function o(t){while(t<0)t+=4;return t%4}if(""===e){const n=parseInt(t);return isNaN(n)?0:o(n)}if(e!==t){let n=0;switch(e){case"%":n=25;break;case"deg":n=90}if(n){let i=parseFloat(t.slice(0,t.length-e.length));return isNaN(i)?0:(i/=n,i%1===0?o(i):0)}}return n}function St(t,n){let e=-1===t.indexOf("xlink:")?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const o in n)e+=" "+o+'="'+n[o]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+e+">"+t+"</svg>"}function Ct(t){return t.replace(/"/g,"'").replace(/%/g,"%25").replace(/#/g,"%23").replace(/</g,"%3C").replace(/>/g,"%3E").replace(/\s+/g," ")}function _t(t){return"data:image/svg+xml,"+Ct(t)}function Mt(t){return'url("'+_t(t)+'")'}const zt={...S,inline:!1},At={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img"},Pt={display:"inline-block"},Rt={backgroundColor:"currentColor"},$t={backgroundColor:"transparent"},Nt={Image:"var(--svg)",Repeat:"no-repeat",Size:"100% 100%"},Ut={webkitMask:Rt,mask:Rt,background:$t};for(const Gt in Ut){const t=Ut[Gt];for(const n in Nt)t[Gt+n]=Nt[n]}const qt={};function Dt(t){return t+(t.match(/^[-0-9.]+$/)?"px":"")}["horizontal","vertical"].forEach(t=>{const n=t.slice(0,1)+"Flip";qt[t+"-flip"]=n,qt[t.slice(0,1)+"-flip"]=n,qt[t+"Flip"]=n});const Qt=(t,n)=>{const e=Et(zt,n),i={...At},r=n.mode||"svg",s={},c=n.style,a="object"!==typeof c||c instanceof Array?{}:c;for(let o in n){const t=n[o];if(void 0!==t)switch(o){case"icon":case"style":case"onLoad":case"mode":case"ssr":break;case"inline":case"hFlip":case"vFlip":e[o]=!0===t||"true"===t||1===t;break;case"flip":"string"===typeof t&&Lt(e,t);break;case"color":s.color=t;break;case"rotate":"string"===typeof t?e[o]=Tt(t):"number"===typeof t&&(e[o]=t);break;case"ariaHidden":case"aria-hidden":!0!==t&&"true"!==t&&delete i["aria-hidden"];break;default:{const n=qt[o];n?!0!==t&&"true"!==t&&1!==t||(e[n]=!0):void 0===zt[o]&&(i[o]=t)}}}const l=$(t,e),u=l.attributes;if(e.inline&&(s.verticalAlign="-0.125em"),"svg"===r){i.style={...s,...a},Object.assign(i,u);let t=0,e=n.id;return"string"===typeof e&&(e=e.replace(/-/g,"_")),i["innerHTML"]=D(l.body,e?()=>e+"ID"+t++:"iconifyVue"),(0,o.h)("svg",i)}const{body:f,width:d,height:p}=t,h="mask"===r||"bg"!==r&&-1!==f.indexOf("currentColor"),g=St(f,{...u,width:d+"",height:p+""});return i.style={...s,"--svg":Mt(g),width:Dt(u.width),height:Dt(u.height),...Pt,...h?Rt:$t,...a},(0,o.h)("span",i)};if(I(!0),B("",rt),"undefined"!==typeof document&&"undefined"!==typeof window){const t=window;if(void 0!==t.IconifyPreload){const n=t.IconifyPreload,e="Invalid IconifyPreload syntax.";"object"===typeof n&&null!==n&&(n instanceof Array?n:[n]).forEach(t=>{try{("object"!==typeof t||null===t||t instanceof Array||"object"!==typeof t.icons||"string"!==typeof t.prefix||!L(t))&&console.error(e)}catch(n){console.error(e)}})}if(void 0!==t.IconifyProviders){const n=t.IconifyProviders;if("object"===typeof n&&null!==n)for(let t in n){const e="IconifyProviders["+t+"] is invalid.";try{const o=n[t];if("object"!==typeof o||!o||void 0===o.resources)continue;W(t,o)||console.error(e)}catch(Vt){console.error(e)}}}}const Bt={...l,body:""},Ht=(0,o.pM)({inheritAttrs:!1,data(){return{_name:"",_loadingIcon:null,iconMounted:!1,counter:0}},mounted(){this.iconMounted=!0},unmounted(){this.abortLoading()},methods:{abortLoading(){this._loadingIcon&&(this._loadingIcon.abort(),this._loadingIcon=null)},getIcon(t,n,e){if("object"===typeof t&&null!==t&&"string"===typeof t.body)return this._name="",this.abortLoading(),{data:t};let o;if("string"!==typeof t||null===(o=r(t,!1,!0)))return this.abortLoading(),null;let i=E(o);if(!i)return this._loadingIcon&&this._loadingIcon.name===t||(this.abortLoading(),this._name="",null!==i&&(this._loadingIcon={name:t,abort:It([o],()=>{this.counter++})})),null;if(this.abortLoading(),this._name!==t&&(this._name=t,n&&n(t)),e){i=Object.assign({},i);const t=e(i.body,o.name,o.prefix,o.provider);"string"===typeof t&&(i.body=t)}const s=["iconify"];return""!==o.prefix&&s.push("iconify--"+o.prefix),""!==o.provider&&s.push("iconify--"+o.provider),{data:i,classes:s}}},render(){this.counter;const t=this.$attrs,n=this.iconMounted||t.ssr?this.getIcon(t.icon,t.onLoad,t.customise):null;if(!n)return Qt(Bt,t);let e=t;return n.classes&&(e={...t,class:("string"===typeof t["class"]?t["class"]+" ":"")+n.classes.join(" ")}),Qt({...l,...n.data},e)}})}}]);
//# sourceMappingURL=103.68fc1f3e.js.map