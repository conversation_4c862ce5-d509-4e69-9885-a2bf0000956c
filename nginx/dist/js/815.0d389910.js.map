{"version": 3, "file": "js/815.0d389910.js", "mappings": "6LAEOA,MAAA,kB,+FAALC,EAAAA,EAAAA,IAkBM,MAlBNC,EAkBM,C,aAjBJC,EAAAA,EAAAA,IAAmB,UAAf,cAAU,I,aACdA,EAAAA,EAAAA,IAAmB,SAAhB,gBAAY,KACfC,EAAAA,EAAAA,IAIWC,EAAA,C,WAHAC,EAAAC,a,qCAAAD,EAAAC,aAAYC,GACrBC,YAAY,SACZT,MAAA,uC,wBAEFI,EAAAA,EAAAA,IAAsEM,EAAA,CAA3DC,KAAK,UAAWC,QAAOC,EAAAC,kB,kBAAkB,IAAMC,EAAA,KAAAA,EAAA,K,QAAN,a,wCAEpDZ,EAAAA,EAAAA,IAOM,OAPDH,MAAA,uBAAyB,EAC5BG,EAAAA,EAAAA,IAAc,UAAV,UACJA,EAAAA,EAAAA,IAIK,YAHHA,EAAAA,EAAAA,IAA0D,YAAtDA,EAAAA,EAAAA,IAAiD,KAA9Ca,KAAK,gCAA+B,aAC3Cb,EAAAA,EAAAA,IAA0D,YAAtDA,EAAAA,EAAAA,IAAiD,KAA9Ca,KAAK,gCAA+B,aAC3Cb,EAAAA,EAAAA,IAA0D,YAAtDA,EAAAA,EAAAA,IAAiD,KAA9Ca,KAAK,gCAA+B,gB,mBAOnD,GACEC,KAAM,mBACNC,IAAAA,GACE,MAAO,CACLX,aAAc,IAElB,EACAY,QAAS,CACPL,gBAAAA,GACOM,KAAKb,aAIVa,KAAKC,QAAQC,KAAK,CAChBL,KAAM,2BACNM,OAAQ,CAAEC,GAAIJ,KAAKb,gBALnBa,KAAKK,SAASC,MAAM,UAOxB,I,WCpCJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,O", "sources": ["webpack://frontend-web/./src/views/TestReportDetail.vue", "webpack://frontend-web/./src/views/TestReportDetail.vue?7eb0"], "sourcesContent": ["<!-- 这是一个临时测试页面，用于测试报告详情页面的路由 -->\r\n<template>\r\n  <div style=\"padding: 20px;\">\r\n    <h3>性能报告详情页面测试</h3>\r\n    <p>请输入报告ID进行测试：</p>\r\n    <el-input \r\n      v-model=\"testReportId\" \r\n      placeholder=\"输入报告ID\" \r\n      style=\"width: 200px; margin-right: 10px;\">\r\n    </el-input>\r\n    <el-button type=\"primary\" @click=\"goToReportDetail\">查看报告详情</el-button>\r\n    \r\n    <div style=\"margin-top: 20px;\">\r\n      <h4>测试链接：</h4>\r\n      <ul>\r\n        <li><a href=\"#/PerformanceResult-Detail/1\">报告ID=1</a></li>\r\n        <li><a href=\"#/PerformanceResult-Detail/2\">报告ID=2</a></li>\r\n        <li><a href=\"#/PerformanceResult-Detail/3\">报告ID=3</a></li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'TestReportDetail',\r\n  data() {\r\n    return {\r\n      testReportId: '1'\r\n    }\r\n  },\r\n  methods: {\r\n    goToReportDetail() {\r\n      if (!this.testReportId) {\r\n        this.$message.error('请输入报告ID')\r\n        return\r\n      }\r\n      this.$router.push({ \r\n        name: 'PerformanceResult-Detail', \r\n        params: { id: this.testReportId }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>", "import { render } from \"./TestReportDetail.vue?vue&type=template&id=a7418cb8\"\nimport script from \"./TestReportDetail.vue?vue&type=script&lang=js\"\nexport * from \"./TestReportDetail.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__"], "names": ["style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createVNode", "_component_el_input", "$data", "testReportId", "$event", "placeholder", "_component_el_button", "type", "onClick", "$options", "goToReportDetail", "_cache", "href", "name", "data", "methods", "this", "$router", "push", "params", "id", "$message", "error", "__exports__", "render"], "sourceRoot": ""}