{"version": 3, "file": "js/315.c350c2a4.js", "mappings": "yMACOA,MAAM,yB,GAEJA,MAAM,oB,GACJA,MAAM,gB,GAINA,MAAM,kB,GACJA,MAAM,kB,GAmBVA,MAAM,c,GACJA,MAAM,a,GACJA,MAAM,c,GAENA,MAAM,2B,GAIRA,MAAM,a,GACJA,MAAM,c,GAENA,MAAM,0B,GAIRA,MAAM,a,GACJA,MAAM,c,GAENA,MAAM,4B,GAIRA,MAAM,a,GACJA,MAAM,c,GAENA,MAAM,wB,GAQRA,MAAM,mB,GAkBEA,MAAM,a,GACJA,MAAM,yB,GAKNA,MAAM,e,GACJA,MAAM,a,GAKNA,MAAM,a,GAOJA,MAAM,a,GAUZA,MAAM,e,GAEHA,MAAM,e,GAiCTA,MAAM,a,GACJA,MAAM,e,GAKLA,MAAM,a,SAQ8BA,MAAM,e,GAC3CA,MAAM,a,GACNA,MAAM,e,SAEoBA,MAAM,e,GAChCA,MAAM,a,GACNA,MAAM,e,GAgBNA,MAAM,a,SAEAA,MAAM,W,GAOdA,MAAM,kB,GAsCRA,MAAM,c,GAYVA,MAAM,wB,GAsBVA,MAAM,kB,GAEFA,MAAM,Y,GASNA,MAAM,Y,GAIEA,MAAM,iB,GAMNA,MAAM,iB,SAMoBA,MAAM,mB,GAQhCA,MAAM,iB,GAMNA,MAAM,iB,UAMqCA,MAAM,gB,UAQVA,MAAM,sB,IACnDA,MAAM,Y,IAYEA,MAAM,iB,IACHA,MAAM,e,IAOTA,MAAM,kB,IAgBNA,MAAM,e,IACHA,MAAM,e,IAYjBA,MAAM,Y,IAYEA,MAAM,iB,IACHA,MAAM,e,IAOTA,MAAM,kB,IAMdA,MAAM,Y,IAQFA,MAAM,e,IAkBdA,MAAM,iB,0jCAhafC,EAAAA,EAAAA,IAyPM,MAzPNC,EAyPM,EAvPJD,EAAAA,EAAAA,IAsBM,MAtBNE,EAsBM,EArBJF,EAAAA,EAAAA,IAGM,MAHNG,EAGM,C,eAFJH,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVI,EAAAA,EAAAA,IAAmEC,GAAA,CAA3DC,KAAK,OAAOC,OAAO,QAAQR,MAAM,c,kBAAa,IAAIS,EAAA,MAAAA,EAAA,M,QAAJ,W,iBAExDR,EAAAA,EAAAA,IAgBM,MAhBNS,EAgBM,EAfJT,EAAAA,EAAAA,IAWM,MAXNU,EAWM,EAVJN,EAAAA,EAAAA,IAQWO,GAAA,C,WAPAC,GAAAC,W,qCAAAD,GAAAC,WAAUC,GACnBC,YAAY,SACZhB,MAAM,eACNiB,UAAA,I,CACWC,QAAMC,EAAAA,EAAAA,IACf,IAAiD,EAAjDd,EAAAA,EAAAA,IAAiDe,GAAA,CAAxCpB,MAAM,eAAa,C,iBAAC,IAAU,EAAVK,EAAAA,EAAAA,IAAUgB,M,gCAG3ChB,EAAAA,EAAAA,IAAgFiB,GAAA,CAArEf,KAAK,UAAWgB,QAAOC,GAAAC,YAAazB,MAAM,c,kBAAa,IAAES,EAAA,MAAAA,EAAA,M,QAAF,S,+BAEpEJ,EAAAA,EAAAA,IAEYiB,GAAA,CAFDf,KAAK,UAAWgB,QAAKd,EAAA,KAAAA,EAAA,GAAAM,GAAES,GAAAE,MAAM,QAAQ1B,MAAM,c,kBACpD,IAA2B,EAA3BK,EAAAA,EAAAA,IAA2Be,GAAA,M,iBAAlB,IAAQ,EAARf,EAAAA,EAAAA,IAAQsB,M,6BAAU,Y,mBAMjC1B,EAAAA,EAAAA,IA6BM,MA7BN2B,EA6BM,EA5BJ3B,EAAAA,EAAAA,IAMM,MANN4B,EAMM,EALJ5B,EAAAA,EAAAA,IAAmD,MAAnD6B,GAAmDC,EAAAA,EAAAA,IAAxBlB,GAAAmB,SAASC,QAAM,G,eAC1ChC,EAAAA,EAAAA,IAAkC,OAA7BD,MAAM,cAAa,QAAI,KAC5BC,EAAAA,EAAAA,IAEM,MAFNiC,EAEM,EADJ7B,EAAAA,EAAAA,IAA2Be,GAAA,M,iBAAlB,IAAQ,EAARf,EAAAA,EAAAA,IAAQ8B,M,WAGrBlC,EAAAA,EAAAA,IAMM,MANNmC,EAMM,EALJnC,EAAAA,EAAAA,IAAmD,MAAnDoC,GAAmDN,EAAAA,EAAAA,IAAxBP,GAAAc,iBAAe,G,eAC1CrC,EAAAA,EAAAA,IAAiC,OAA5BD,MAAM,cAAa,OAAG,KAC3BC,EAAAA,EAAAA,IAEM,MAFNsC,EAEM,EADJlC,EAAAA,EAAAA,IAA8Be,GAAA,M,iBAArB,IAAW,EAAXf,EAAAA,EAAAA,IAAWmC,M,WAGxBvC,EAAAA,EAAAA,IAMM,MANNwC,EAMM,EALJxC,EAAAA,EAAAA,IAAqD,MAArDyC,GAAqDX,EAAAA,EAAAA,IAA1BP,GAAAmB,mBAAiB,G,eAC5C1C,EAAAA,EAAAA,IAAiC,OAA5BD,MAAM,cAAa,OAAG,KAC3BC,EAAAA,EAAAA,IAEM,MAFN2C,EAEM,EADJvC,EAAAA,EAAAA,IAAoCe,GAAA,M,iBAA3B,IAAiB,EAAjBf,EAAAA,EAAAA,IAAiBwC,M,WAG9B5C,EAAAA,EAAAA,IAMM,MANN6C,EAMM,EALJ7C,EAAAA,EAAAA,IAAiD,MAAjD8C,GAAiDhB,EAAAA,EAAAA,IAAtBP,GAAAwB,eAAa,G,eACxC/C,EAAAA,EAAAA,IAAgC,OAA3BD,MAAM,cAAa,MAAE,KAC1BC,EAAAA,EAAAA,IAEM,MAFNgD,EAEM,EADJ5C,EAAAA,EAAAA,IAAoCe,GAAA,M,iBAA3B,IAAiB,EAAjBf,EAAAA,EAAAA,IAAiB6C,M,aAMhC7C,EAAAA,EAAAA,IA6Le8C,GAAA,CA7LDnD,MAAM,kBAAkBoD,OAAO,uB,kBAC3C,IA2LM,EA3LNnD,EAAAA,EAAAA,IA2LM,MA3LNoD,EA2LM,E,qBA1LJC,EAAAA,EAAAA,IA6KWC,GAAA,CA3KRC,KAAM3C,GAAAmB,SACPyB,MAAA,eACA,UAAQ,KACPC,QAAQ,EACTC,OAAA,GACCP,OAAQvC,GAAA+C,YACT,2BACC,oBAAmB,CAAAC,WAAA,UAAAC,MAAA,UAAAC,WAAA,S,CA0JTC,OAAK7C,EAAAA,EAAAA,IACd,IAOM,EAPNlB,EAAAA,EAAAA,IAOM,MAPNgE,EAOM,EANJ5D,EAAAA,EAAAA,IAKW6D,GAAA,CALA,aAAY,IAAKC,YAAY,U,CAC3BA,aAAWhD,EAAAA,EAAAA,IACpB,IAAaV,EAAA,MAAAA,EAAA,MAAbR,EAAAA,EAAAA,IAAa,SAAV,UAAM,M,iBAEX,IAAgE,EAAhEI,EAAAA,EAAAA,IAAgEiB,GAAA,CAArDf,KAAK,UAAWgB,QAAKd,EAAA,KAAAA,EAAA,GAAAM,GAAES,GAAAE,MAAM,S,kBAAQ,IAAIjB,EAAA,MAAAA,EAAA,M,QAAJ,W,2CA7JtD,IAA8D,EAA9DJ,EAAAA,EAAAA,IAA8D+D,GAAA,CAA7C7D,KAAK,YAAY8D,MAAM,KAAKC,MAAM,YAGnDjE,EAAAA,EAAAA,IA0BkB+D,GAAA,CA1BDG,MAAM,OAAO,YAAU,O,CAC3BC,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBxE,EAAAA,EAAAA,IAsBM,MAtBNyE,EAsBM,EArBJzE,EAAAA,EAAAA,IAIM,MAJN0E,EAIM,EAHJtE,EAAAA,EAAAA,IAEYuE,GAAA,CAFAC,KAAM,GAAI7E,MAAM,cAAeyD,OAAKqB,EAAAA,EAAAA,IAAA,CAAAC,gBAAoBvD,GAAAwD,eAAeP,EAAIQ,a,kBACrF,IAA0C,E,iBAAxCR,EAAIQ,SAASC,OAAO,EAAD,GAAMC,eAAW,K,wBAG1ClF,EAAAA,EAAAA,IAeM,MAfNmF,EAeM,EAdJnF,EAAAA,EAAAA,IAIM,MAJNoF,EAIM,EAHJhF,EAAAA,EAAAA,IAEciF,GAAA,CAFAC,GAAI,kBAAoBhE,QAAKR,GAAES,GAAAgE,gBAAgBf,GAAMzE,MAAM,kB,kBACvE,IAAkB,E,iBAAfyE,EAAIQ,UAAQ,K,0BAGnBhF,EAAAA,EAAAA,IAQM,MARNwF,EAQM,EAPJpF,EAAAA,EAAAA,IAKSC,GAAA,CALAC,KAAuB,OAAjBkE,EAAIiB,SAAoB,UAAY,UACjDb,KAAK,QACLrE,OAAO,QACPR,MAAM,Y,kBACN,IAA0D,E,iBAAvDa,GAAA8E,YAAYlB,EAAIiB,SAASE,aAAenB,EAAIiB,UAAQ,K,qBAEzDzF,EAAAA,EAAAA,IAAqE,MAArE4F,EAAuB,QAAI9D,EAAAA,EAAAA,IAAGP,GAAAsE,cAAcrB,EAAIsB,cAAW,W,OAQrE1F,EAAAA,EAAAA,IAOkB+D,GAAA,CAPDG,MAAM,OAAOD,MAAM,SAASD,MAAM,O,CACtCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBxE,EAAAA,EAAAA,IAGM,MAHN+F,EAGM,EAFJ/F,EAAAA,EAAAA,IAAqE,OAA/DD,OAAKiG,EAAAA,EAAAA,IAAA,oBAAuBzE,GAAA0E,eAAezB,EAAI0B,W,SACrDlG,EAAAA,EAAAA,IAA+F,OAA/FmG,GAA+FrE,EAAAA,EAAAA,IAAlE0C,EAAI4B,gBAAkB7E,GAAA8E,cAAc7B,EAAI0B,SAAW,OAAL,O,OAMjF9F,EAAAA,EAAAA,IAUkB+D,GAAA,CAVDG,MAAM,OAAOD,MAAM,SAASD,MAAM,O,CACtCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBpE,EAAAA,EAAAA,IAMSC,GAAA,CALPE,OAAO,QACPqE,KAAK,QACL7E,MAAM,WACLO,KAAMiB,GAAA+E,kBAAkB9B,EAAI+B,a,kBAC7B,IAAyB,E,iBAAtB/B,EAAIgC,kBAAgB,K,6BAM7BpG,EAAAA,EAAAA,IASkB+D,GAAA,CATDG,MAAM,QAAQD,MAAM,SAASD,MAAM,O,CACvCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBpE,EAAAA,EAAAA,IAKSC,GAAA,CAJPE,OAAO,QACPqE,KAAK,QACJtE,KAAMiB,GAAAkF,uBAAuBjC,EAAIkC,mB,kBAClC,IAAkD,E,iBAA/CnF,GAAAoF,uBAAuBnC,EAAIkC,mBAAgB,K,6BAMpDtG,EAAAA,EAAAA,IAWkB+D,GAAA,CAXDG,MAAM,QAAQD,MAAM,SAASD,MAAM,O,CACvCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBxE,EAAAA,EAAAA,IAOM,MAPN4G,EAOM,EANJ5G,EAAAA,EAAAA,IAIM,MAJN6G,EAIM,EAHJzG,EAAAA,EAAAA,IAEYuE,GAAA,CAFAC,KAAM,GAAKpB,OAAKqB,EAAAA,EAAAA,IAAA,CAAAC,gBAAoBvD,GAAAwD,eAAeP,EAAIsC,Y,kBACjE,IAA+D,E,iBAA5DtC,EAAIsC,QAAUtC,EAAIsC,QAAQ7B,OAAO,EAAD,GAAMC,cAAgB,KAAL,K,wBAGxDlF,EAAAA,EAAAA,IAAgD,OAAhD+G,GAAgDjF,EAAAA,EAAAA,IAArB0C,EAAIsC,SAAO,O,OAM5C1G,EAAAA,EAAAA,IAYkB+D,GAAA,CAZDG,MAAM,OAAOD,MAAM,SAASD,MAAM,O,CACtCG,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,CACZA,EAAIwC,UAAYxC,EAAIyC,c,WAA/BC,EAAAA,EAAAA,IAGM,MAHNC,EAGM,EAFJnH,EAAAA,EAAAA,IAA+C,MAA/CoH,GAA+CtF,EAAAA,EAAAA,IAArB0C,EAAIwC,UAAQ,IACtChH,EAAAA,EAAAA,IAAmE,MAAnEqH,GAAmEvF,EAAAA,EAAAA,IAAvCP,GAAAsE,cAAcrB,EAAIyC,cAAW,MAE3CzC,EAAIsB,c,WAApBoB,EAAAA,EAAAA,IAGM,MAHNI,EAGM,EAFJtH,EAAAA,EAAAA,IAAsD,MAAtDuH,GAAsDzF,EAAAA,EAAAA,IAA5B0C,EAAIsC,SAAW,MAAJ,IACrC9G,EAAAA,EAAAA,IAAmE,MAAnEwH,GAAmE1F,EAAAA,EAAAA,IAAvCP,GAAAsE,cAAcrB,EAAIsB,cAAW,Q,WAE3DzC,EAAAA,EAAAA,IAAmEhD,GAAA,C,MAApDuE,KAAK,QAAQrE,OAAO,QAAQD,KAAK,Q,kBAAO,IAAGE,EAAA,MAAAA,EAAA,M,QAAH,U,wBAK3DJ,EAAAA,EAAAA,IAakB+D,GAAA,CAbDG,MAAM,KAAKD,MAAM,SAAS,YAAU,O,CACxCE,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,CAEfA,EAAIiD,O,WADZpE,EAAAA,EAAAA,IAQaqE,GAAA,C,MANX3H,MAAM,WACNQ,OAAO,OACNoH,QAASnD,EAAIiD,KACdG,UAAU,a,kBAEV,IAA2C,EAA3C5H,EAAAA,EAAAA,IAA2C,MAA3C6H,GAA2C/F,EAAAA,EAAAA,IAAjB0C,EAAIiD,MAAI,K,qCAEpCP,EAAAA,EAAAA,IAAwC,OAAxCY,EAA6B,W,OAKjC1H,EAAAA,EAAAA,IAoCkB+D,GAAA,CApCDG,MAAM,KAAKyD,MAAM,QAAQ3D,MAAM,MAAMC,MAAM,U,CAC/CE,SAAOrD,EAAAA,EAAAA,IAChB,EADoBsD,SAAG,EACvBxE,EAAAA,EAAAA,IAgCM,MAhCNgI,EAgCM,EA/BJ5H,EAAAA,EAAAA,IAOYiB,GAAA,CANTC,QAAKR,GAAES,GAAA0G,QAAQzD,EAAI0D,IACpB5H,KAAK,UACL6H,MAAA,GACAvD,KAAK,QACL7E,MAAM,sB,kBACN,IAAgC,EAAhCK,EAAAA,EAAAA,IAAgCe,GAAA,M,iBAAvB,IAAa,EAAbf,EAAAA,EAAAA,IAAagI,M,6BAAU,W,gCAElChI,EAAAA,EAAAA,IAOYiB,GAAA,CANTC,QAAKR,GAAES,GAAAgE,gBAAgBf,GACxBlE,KAAK,UACL6H,MAAA,GACAvD,KAAK,QACL7E,MAAM,yB,kBACN,IAA2B,EAA3BK,EAAAA,EAAAA,IAA2Be,GAAA,M,iBAAlB,IAAQ,EAARf,EAAAA,EAAAA,IAAQiI,M,6BAAU,W,gCAE7BjI,EAAAA,EAAAA,IAcckI,GAAA,CAdDC,QAAQ,QAASC,UAAO1H,GAAES,GAAAkH,cAAc3H,EAAQ0D,GAAMzE,MAAM,mB,CAI5D2I,UAAQxH,EAAAA,EAAAA,IACjB,IAOmB,EAPnBd,EAAAA,EAAAA,IAOmBuI,GAAA,M,iBANjB,IAEmB,EAFnBvI,EAAAA,EAAAA,IAEmBwI,GAAA,CAFDC,QAAQ,QAAM,C,iBAC9B,IAAmC,EAAnCzI,EAAAA,EAAAA,IAAmCe,GAAA,M,iBAA1B,IAAgB,EAAhBf,EAAAA,EAAAA,IAAgB0I,M,6BAAU,a,eAErC1I,EAAAA,EAAAA,IAEmBwI,GAAA,CAFDG,QAAA,GAAQF,QAAQ,SAAS9I,MAAM,e,kBAC/C,IAA6B,EAA7BK,EAAAA,EAAAA,IAA6Be,GAAA,M,iBAApB,IAAU,EAAVf,EAAAA,EAAAA,IAAU4I,M,6BAAU,a,yCATnC,IAEY,EAFZ5I,EAAAA,EAAAA,IAEYiB,GAAA,CAFDuD,KAAK,QAAQ7E,MAAM,Y,kBAC5B,IAA2B,EAA3BK,EAAAA,EAAAA,IAA2Be,GAAA,M,iBAAlB,IAAQ,EAARf,EAAAA,EAAAA,IAAQ6I,M,qFAhJhBrI,GAAAsI,iBA+KblJ,EAAAA,EAAAA,IASM,MATNmJ,EASM,EARJ/I,EAAAA,EAAAA,IAOEgJ,GAAA,CANQ,eAAcxI,GAAAyI,MAAMC,Q,sCAAN1I,GAAAyI,MAAMC,QAAOxI,GACnC8C,WAAA,GACA2F,OAAO,mCACN,YAAW,IACXC,MAAO5I,GAAAyI,MAAMI,MACbC,gBAAgBnI,GAAAoI,c,kEAQ3BvJ,EAAAA,EAAAA,IAyKYwJ,GAAA,C,WAxKDhJ,GAAAiJ,c,uCAAAjJ,GAAAiJ,cAAa/I,GACrBgJ,MAAOlJ,GAAAmJ,YACR3F,MAAM,QACN,sBACC,eAAc7C,GAAAyI,gBACfjK,MAAM,e,CA6JKkK,QAAM/I,EAAAA,EAAAA,IACf,IAGM,EAHNlB,EAAAA,EAAAA,IAGM,MAHNkK,GAGM,EAFJ9J,EAAAA,EAAAA,IAAwDiB,GAAA,CAA5CC,QAAOC,GAAAyI,gBAAiB7B,MAAA,I,kBAAM,IAAE3H,EAAA,MAAAA,EAAA,M,QAAF,S,6BAC1CJ,EAAAA,EAAAA,IAAyDiB,GAAA,CAA9Cf,KAAK,UAAWgB,QAAOC,GAAA4I,S,kBAAS,IAAE3J,EAAA,MAAAA,EAAA,M,QAAF,S,iDA/J/C,IA2JM,EA3JNR,EAAAA,EAAAA,IA2JM,MA3JNoK,EA2JM,EA1JJhK,EAAAA,EAAAA,IAyJUiK,GAAA,CAzJAC,MAAO1J,GAAA2J,KAAOC,MAAO5J,GAAA6J,UAAWC,IAAI,UAAU,iBAAe,O,kBACrE,IAOM,EAPN1K,EAAAA,EAAAA,IAOM,MAPN2K,EAOM,EANJvK,EAAAA,EAAAA,IAEewK,GAAA,CAFDtG,MAAM,OAAOuG,KAAK,WAAW9K,MAAM,kB,kBAC/C,IAAyE,EAAzEK,EAAAA,EAAAA,IAAyEO,GAAA,C,WAAtDC,GAAA2J,KAAKvF,S,qCAALpE,GAAA2J,KAAKvF,SAAQlE,GAAEgK,UAAU,KAAK/J,YAAY,W,gCAE/DX,EAAAA,EAAAA,IAEewK,GAAA,CAFDtG,MAAM,OAAOuG,KAAK,UAAU9K,MAAM,kB,kBAC9C,IAA4C,EAA5CK,EAAAA,EAAAA,IAA4CO,GAAA,C,WAAzBC,GAAA2J,KAAKQ,Q,qCAALnK,GAAA2J,KAAKQ,QAAOjK,GAAEkK,SAAA,I,kCAIrChL,EAAAA,EAAAA,IAyCM,MAzCNiL,EAyCM,EAxCJ7K,EAAAA,EAAAA,IAmBewK,GAAA,CAnBDtG,MAAM,OAAOuG,KAAK,WAAW9K,MAAM,kB,kBAC/C,IAaiB,EAbjBK,EAAAA,EAAAA,IAaiB8K,GAAA,C,WAbQ3J,GAAA4J,e,qCAAA5J,GAAA4J,eAAcrK,GAAEf,MAAM,mB,kBAC7C,IAKW,EALXK,EAAAA,EAAAA,IAKWgL,GAAA,CALD9G,MAAM,MAAI,C,iBAClB,IAGM,EAHNtE,EAAAA,EAAAA,IAGM,MAHNqL,EAGM,EAFJjL,EAAAA,EAAAA,IAAiDe,GAAA,CAAxCpB,MAAM,cAAY,C,iBAAC,IAAW,EAAXK,EAAAA,EAAAA,IAAWkL,M,qBACvCtL,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,OAGdI,EAAAA,EAAAA,IAKWgL,GAAA,CALD9G,MAAM,MAAI,C,iBAClB,IAGM,EAHNtE,EAAAA,EAAAA,IAGM,MAHNuL,EAGM,EAFJnL,EAAAA,EAAAA,IAA+Ce,GAAA,CAAtCpB,MAAM,cAAY,C,iBAAC,IAAS,EAATK,EAAAA,EAAAA,IAASoL,M,qBACrCxL,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,+BAIa,OAAlBY,GAAA2J,KAAK9E,W,WAAhByB,EAAAA,EAAAA,IAGM,MAHNuE,EAGM,EAFJrL,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAcsL,M,qBACvB1L,EAAAA,EAAAA,IAA4B,YAAtB,mBAAe,Q,wBAGzBI,EAAAA,EAAAA,IAmBewK,GAAA,CAnBDtG,MAAM,OAAOuG,KAAK,mBAAmB9K,MAAM,kB,kBACvD,IAaiB,EAbjBK,EAAAA,EAAAA,IAaiB8K,GAAA,C,WAbQtK,GAAA2J,KAAK7D,iB,qCAAL9F,GAAA2J,KAAK7D,iBAAgB5F,GAAEf,MAAM,kB,kBACpD,IAKW,EALXK,EAAAA,EAAAA,IAKWgL,GAAA,CALD9G,MAAM,UAAQ,C,iBACtB,IAGM,EAHNtE,EAAAA,EAAAA,IAGM,MAHN2L,EAGM,EAFJvL,EAAAA,EAAAA,IAAiDe,GAAA,CAAxCpB,MAAM,cAAY,C,iBAAC,IAAW,EAAXK,EAAAA,EAAAA,IAAWwL,M,qBACvC5L,EAAAA,EAAAA,IAAiB,YAAX,QAAI,Q,OAGdI,EAAAA,EAAAA,IAKWgL,GAAA,CALD9G,MAAM,eAAa,C,iBAC3B,IAGM,EAHNtE,EAAAA,EAAAA,IAGM,MAHN6L,EAGM,EAFJzL,EAAAA,EAAAA,IAAoDe,GAAA,CAA3CpB,MAAM,cAAY,C,iBAAC,IAAc,EAAdK,EAAAA,EAAAA,IAAc0L,M,qBAC1C9L,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,Q,+BAIoB,gBAA1BY,GAAA2J,KAAK7D,mB,WAAhBQ,EAAAA,EAAAA,IAGM,MAHN6E,GAGM,EAFJ3L,EAAAA,EAAAA,IAAiCe,GAAA,M,iBAAxB,IAAc,EAAdf,EAAAA,EAAAA,IAAcsL,M,qBACvB1L,EAAAA,EAAAA,IAAgC,YAA1B,uBAAmB,Q,yBAMM,gBAA1BY,GAAA2J,KAAK7D,mB,WAAhBQ,EAAAA,EAAAA,IA0FM,MA1FN8E,GA0FM,EAzFJhM,EAAAA,EAAAA,IA+CM,MA/CNiM,GA+CM,EA9CJ7L,EAAAA,EAAAA,IAsBewK,GAAA,CAtBDtG,MAAM,OAAOuG,KAAK,gBAAgB9K,MAAM,kB,kBACpD,IAoBY,EApBZK,EAAAA,EAAAA,IAoBY8L,GAAA,C,WAnBDtL,GAAA2J,KAAK4B,c,qCAALvL,GAAA2J,KAAK4B,cAAarL,GAC3BC,YAAY,UACZyC,MAAA,eACC4I,SAAQ7K,GAAA8K,0B,kBAEP,IAAkC,G,aADpCnF,EAAAA,EAAAA,IAcYoF,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAbO3L,GAAA4L,iBAAVC,K,WADTpJ,EAAAA,EAAAA,IAcYqJ,GAAA,CAZTC,IAAKF,EAAOvE,GACZ5D,MAAK,GAAKmI,EAAOG,SAASH,EAAOI,WAAWJ,EAAOK,aACnDC,MAAON,EAAOvE,I,kBACf,IAOM,EAPNlI,EAAAA,EAAAA,IAOM,MAPNgN,GAOM,EANJhN,EAAAA,EAAAA,IAAkD,OAAlDiN,IAAkDnL,EAAAA,EAAAA,IAArB2K,EAAOG,MAAI,IACxCxM,EAAAA,EAAAA,IAISC,GAAA,CAHNC,KAAwB,WAAlBmM,EAAOvG,OAAsB,UAAY,SAChDtB,KAAK,S,kBACL,IAA+C,E,iBAA1B,WAAlB6H,EAAOvG,OAAsB,KAAO,OAAvB,K,uBAGpBlG,EAAAA,EAAAA,IAA6E,MAA7EkN,IAA6EpL,EAAAA,EAAAA,IAA9C2K,EAAOI,SAAU,KAAC/K,EAAAA,EAAAA,IAAG2K,EAAOK,WAAS,K,kFAK1E1M,EAAAA,EAAAA,IAqBewK,GAAA,CArBDtG,MAAM,QAAQuG,KAAK,WAAW9K,MAAM,kB,kBAChD,IAmBY,EAnBZK,EAAAA,EAAAA,IAmBY8L,GAAA,C,WAlBDtL,GAAA2J,KAAK4C,S,qCAALvM,GAAA2J,KAAK4C,SAAQrM,GACtBC,YAAY,WACZyC,MAAA,gB,kBAEE,IAAwB,G,aAD1B0D,EAAAA,EAAAA,IAcYoF,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAbK3L,GAAAwM,SAARC,K,WADThK,EAAAA,EAAAA,IAcYqJ,GAAA,CAZTC,IAAKU,EAAKA,KACV/I,MAAK,MAAQ+I,EAAKA,OAClBN,MAAOM,EAAKA,KACZrC,SAAUqC,EAAKC,U,kBAChB,IAOM,EAPNtN,EAAAA,EAAAA,IAOM,MAPNuN,GAOM,EANJvN,EAAAA,EAAAA,IAAgD,OAAhDwN,IAAgD1L,EAAAA,EAAAA,IAAnBuL,EAAKA,MAAI,IACtCjN,EAAAA,EAAAA,IAISC,GAAA,CAHNC,KAAM+M,EAAKC,SAAW,SAAW,UAClC1I,KAAK,S,kBACL,IAAkC,E,iBAA/ByI,EAAKC,SAAW,MAAQ,MAAX,K,4GAQ5BtN,EAAAA,EAAAA,IAwBM,MAxBNyN,GAwBM,EAvBJrN,EAAAA,EAAAA,IAsBewK,GAAA,CAtBDtG,MAAM,QAAQuG,KAAK,iBAAiB9K,MAAM,kB,kBACtD,IAoBY,EApBZK,EAAAA,EAAAA,IAoBY8L,GAAA,C,WAnBDtL,GAAA2J,KAAKmD,e,uCAAL9M,GAAA2J,KAAKmD,eAAc5M,GAC5B6M,SAAA,GACA5M,YAAY,WACZyC,MAAA,gB,kBAEE,IAAwC,G,aAD1C0D,EAAAA,EAAAA,IAcYoF,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAbOhL,GAAAqM,uBAAVnB,K,WADTpJ,EAAAA,EAAAA,IAcYqJ,GAAA,CAZTC,IAAKF,EAAOvE,GACZ5D,MAAK,GAAKmI,EAAOG,SAASH,EAAOI,WAAWJ,EAAOK,aACnDC,MAAON,EAAOvE,I,kBACf,IAOM,EAPNlI,EAAAA,EAAAA,IAOM,MAPN6N,GAOM,EANJ7N,EAAAA,EAAAA,IAAkD,OAAlD8N,IAAkDhM,EAAAA,EAAAA,IAArB2K,EAAOG,MAAI,IACxCxM,EAAAA,EAAAA,IAISC,GAAA,CAHNC,KAAwB,WAAlBmM,EAAOvG,OAAsB,UAAY,SAChDtB,KAAK,S,kBACL,IAA+C,E,iBAA1B,WAAlB6H,EAAOvG,OAAsB,KAAO,OAAvB,K,uBAGpBlG,EAAAA,EAAAA,IAA6E,MAA7E+N,IAA6EjM,EAAAA,EAAAA,IAA9C2K,EAAOI,SAAU,KAAC/K,EAAAA,EAAAA,IAAG2K,EAAOK,WAAS,K,yEAM5E9M,EAAAA,EAAAA,IAaM,MAbNgO,GAaM,EAZJ5N,EAAAA,EAAAA,IAWewK,GAAA,CAXDtG,MAAM,QAAQuG,KAAK,gBAAgB9K,MAAM,kB,kBACrD,IAKwB,EALxBK,EAAAA,EAAAA,IAKwB6N,GAAA,C,WAJbrN,GAAA2J,KAAK2D,c,uCAALtN,GAAA2J,KAAK2D,cAAapN,GAC1BqN,IAAK,EACLC,IAAK7M,GAAA8M,WACNtN,YAAY,SACZyC,MAAA,gB,8BACFxD,EAAAA,EAAAA,IAGM,MAHNsO,GAGM,EAFJtO,EAAAA,EAAAA,IAA4C,YAAtC,WAAO8B,EAAAA,EAAAA,IAAGP,GAAAgN,oBAAkB,IAClCvO,EAAAA,EAAAA,IAAmC,YAA7B,UAAM8B,EAAAA,EAAAA,IAAGP,GAAA8M,YAAU,O,4BAMjCjO,EAAAA,EAAAA,IAMewK,GAAA,CANDtG,MAAM,OAAOuG,KAAK,Q,kBAC9B,IAIc,EAJdzK,EAAAA,EAAAA,IAIcO,GAAA,CAHZL,KAAK,W,WACIM,GAAA2J,KAAK9C,K,uCAAL7G,GAAA2J,KAAK9C,KAAI3G,GAClBC,YAAY,UACXyN,KAAM,G,8NAqCnB,IACEC,WAAY,CACVC,KAAI,QACJC,UAAS,aACTC,aAAY,gBACZC,KAAI,QACJC,OAAM,UACNC,WAAU,cACVC,OAAM,UACNC,KAAI,QACJC,QAAO,WACPC,cAAa,iBACbC,cAAa,iBACbC,KAAI,QACJC,KAAI,QACJC,KAAI,QACJC,QAAO,WACPC,MAAK,SACLC,QAAO,WACPC,WAAUA,GAAAA,YAEZpM,IAAAA,GACE,MAAO,CAELmC,YAAa,CAAC,GAAM,OAAQ,GAAM,QAClC7E,WAAY,GACZkB,SAAU,GACVsH,MAAO,CACLI,MAAO,EACPH,QAAS,GAEXsG,QAAQ,EACRrF,KAAM,CACJvF,SAAU,GACVyC,KAAM,GACNoI,QAAS,GACTpK,SAAU,KACVqB,QAAU,GACVE,SAAW,GACXN,iBAAkB,SAClByF,cAAe,KACfuB,eAAgB,GAChBQ,cAAe,EACff,SAAU,MAEZtD,eAAe,EACfE,YAAa,GACbb,cAAc,EACduB,UAAW,CACTzF,SAAU,CACR,CAAE8K,UAAU,EAAMC,QAAS,UAAWxH,QAAS,QAC/C,CAAE4F,IAAK,EAAGC,IAAK,GAAI2B,QAAS,iBAAkBxH,QAAS,SAEzD7B,iBAAkB,CAChB,CAAEoJ,UAAU,EAAMC,QAAS,UAAWxH,QAAS,WAEjD4D,cAAe,CACb,CACE6D,UAAWA,CAACC,EAAMlD,EAAOmD,KACY,gBAA/BC,KAAK5F,KAAK7D,kBAAuCqG,EAGnDmD,IAFAA,EAAS,IAAIE,MAAM,oBAKvB7H,QAAS,WAGbmF,eAAgB,CACd,CACEsC,UAAWA,CAACC,EAAMlD,EAAOmD,KACY,gBAA/BC,KAAK5F,KAAK7D,kBAAwCqG,GAA0B,IAAjBA,EAAM/K,OAGnEkO,IAFAA,EAAS,IAAIE,MAAM,yBAKvB7H,QAAS,WAGb4E,SAAU,CACR,CACE6C,UAAWA,CAACC,EAAMlD,EAAOmD,KACY,gBAA/BC,KAAK5F,KAAK7D,kBAAuCqG,EAGnDmD,IAFAA,EAAS,IAAIE,MAAM,qBAKvB7H,QAAS,WAGb2F,cAAe,CACb,CACE8B,UAAWA,CAACC,EAAMlD,EAAOmD,KACvB,GAAmC,gBAA/BC,KAAK5F,KAAK7D,iBAAoC,CAChD,MAAM2H,EAAa8B,KAAK9B,YACnBtB,GAASA,EAAQ,EACpBmD,EAAS,IAAIE,MAAM,eACVrD,EAAQsB,EACjB6B,EAAS,IAAIE,MAAM,YAAY/B,MAE/B6B,GAEJ,MACEA,KAGJ3H,QAAS,UAIf8H,UAAW,CACT,EAAK,CAAEC,KAAM,OAAQhQ,KAAM,UAAWP,MAAO,oBAC7C,EAAK,CAAEuQ,KAAM,MAAOhQ,KAAM,UAAWP,MAAO,kBAC5C,GAAM,CAAEuQ,KAAM,OAAQhQ,KAAM,SAAUP,MAAO,iBAE/CwQ,WAAY,CAAC,EACb5M,YAAa,sBACb6M,eAAgB,KAGhBhE,iBAAkB,GAClBY,SAAU,CACR,CAAEC,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,GACxB,CAAED,KAAM,KAAMC,UAAU,IAE1BmD,eAAgB,KAEpB,EACAC,OAAAA,GACEP,KAAKQ,uBACLR,KAAKS,cACLT,KAAKU,iBAGLV,KAAKK,eAAiB,IAAIM,eAAe,KACvCX,KAAKQ,yBAIP,MAAMI,EAAYC,SAASC,cAAc,oBACrCF,GACFZ,KAAKK,eAAeU,QAAQH,GAI9BI,OAAOC,iBAAiB,SAAUjB,KAAKQ,qBACzC,EACAU,aAAAA,GAEEF,OAAOG,oBAAoB,SAAUnB,KAAKQ,sBAEtCR,KAAKK,gBACPL,KAAKK,eAAee,aAIlBpB,KAAKM,gBACPe,cAAcrB,KAAKM,eAEvB,EACAgB,SAAU,KACLC,EAAAA,GAAAA,IAAS,CACVC,IAAKC,GAASA,EAAMD,IACpBE,MAAOD,GAASA,EAAMC,QAExBC,QAAAA,GACE,OAAOX,OAAOY,eAAeC,QAAQ,WACvC,EACA7G,eAAgB,CACd8G,GAAAA,GACE,OAAO9B,KAAK5F,KAAK9E,SAASE,UAC5B,EACAuM,GAAAA,CAAInF,GACFoD,KAAK5F,KAAK9E,SAAWsH,CACvB,GAEF1K,eAAAA,GACE,OAAO8N,KAAKpO,SAASoQ,OAAOC,GAAwB,MAAhBA,EAAKlM,QAAgBlE,MAC3D,EACAU,iBAAAA,GACE,OAAOyN,KAAKpO,SAASoQ,OAAOC,GAAwB,MAAhBA,EAAKlM,QAAgBlE,MAC3D,EACAe,aAAAA,GACE,OAAOoN,KAAKpO,SAASoQ,OAAOC,GAAwB,OAAhBA,EAAKlM,QAAiBlE,MAC5D,EAEAqQ,cAAAA,GACE,OAAOlC,KAAKpO,SAASC,OAAS,OAAIsQ,EAAY,OAChD,EAGA1E,sBAAAA,GACE,OAAOuC,KAAK3D,iBAAiB2F,OAAO1F,GAClCA,EAAOvE,KAAOiI,KAAK5F,KAAK4B,cAE5B,EAGAkC,UAAAA,GACE,IAAK8B,KAAK5F,KAAKmD,gBAAsD,IAApCyC,KAAK5F,KAAKmD,eAAe1L,OACxD,OAAO,EAGT,MAAMuQ,EAAkBpC,KAAK3D,iBAAiB2F,OAAO1F,GACnD0D,KAAK5F,KAAKmD,eAAe8E,SAAS/F,EAAOvE,KAG3C,OAAOqK,EAAgBE,OAAO,CAACjJ,EAAOiD,IAC7BjD,GAASiD,EAAOiG,aAAe,GACrC,EACL,EAGAnE,kBAAAA,GACE,OAAOoE,KAAKC,KAAuB,GAAlBzC,KAAK9B,WACxB,GAEFwE,QAAS,KACJC,EAAAA,GAAAA,IAAa,CAAC,gBAGjBnC,oBAAAA,GAEER,KAAK4C,UAAU,KACb,MAAMC,EAAehC,SAASC,cAAc,sBAAsBgC,cAAgB,EAC5EC,EAAclC,SAASC,cAAc,gBAAgBgC,cAAgB,EACrEE,EAAmBnC,SAASC,cAAc,0BAA0BgC,cAAgB,EAGpFG,EAAkBjC,OAAOkC,YAAcL,EAAeE,EAAcC,EAAmB,IAG7FhD,KAAKxM,YAAcgP,KAAKvE,IAAIgF,EAAiB,KAAO,MAExD,EAEA,aAAME,CAAQC,EAAMC,GAClBrD,KAAKjH,cAAe,EACpB,IACE,MAAMuK,QAAiBtD,KAAKuD,KAAKC,mBAAmBxD,KAAKwB,IAAIzJ,GAAIqL,EAAMC,GAC/C,MAApBC,EAASvN,SACXiK,KAAKpO,SAAW0R,EAASlQ,KAAKqQ,OAC9BzD,KAAK9G,MAAMI,MAAQgK,EAASlQ,KAAKkG,MACjC0G,KAAK9G,MAAMC,QAAUmK,EAASlQ,KAAK+F,QAGnC6G,KAAK4C,UAAU,KACb5C,KAAKQ,yBAGX,CAAE,MAAOkD,IACPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,WACTgE,SAAU,KAEd,CAAE,QACA5D,KAAKjH,cAAe,CACtB,CACF,EAEAS,YAAAA,CAAaqK,GACX7D,KAAKmD,QAAQU,EAAa7D,KAAKtP,WACjC,EAEAW,WAAAA,GACE2O,KAAKmD,QAAQ,EAAGnD,KAAKtP,WACvB,EAEA0E,eAAAA,CAAgBhC,GACd4M,KAAK8D,QAAQC,KAAK,CAAEtH,KAAM,kBAC1BuD,KAAKgE,YAAY5Q,EACnB,EAEA9B,KAAAA,CAAMnB,GAGJ,OAFA6P,KAAKtG,eAAgB,EAEbvJ,GACN,IAAK,MACH6P,KAAKpG,YAAc,QACnBoG,KAAK5F,KAAKzD,QAAUqJ,KAAK2B,SACzB3B,KAAK5F,KAAKsF,QAAUM,KAAKwB,IAAIzJ,GAC7BiI,KAAK5F,KAAKQ,QAAUoF,KAAKwB,IAAI/E,KAE7BuD,KAAKS,qBACET,KAAK5F,KAAKvD,SACjB,MAEF,QACEmJ,KAAKpG,YAAc,GACnB,MAEN,EAGA,iBAAM6G,GACJ,IACE,MAAM6C,QAAiBtD,KAAKuD,KAAKU,WAAWjE,KAAKwB,IAAIzJ,GAAI,GACjC,MAApBuL,EAASvN,SACXiK,KAAK3D,iBAAmBiH,EAASlQ,KAAKqQ,QAAU,GAEpD,CAAE,MAAOC,GACPQ,QAAQR,MAAM,aAAcA,GAC5B1D,KAAKmE,SAAST,MAAM,YACtB,CACF,EAEA,qBAAMU,GACJ,GAAKpE,KAAK5F,KAAK4B,cAIf,IACE,MAAMsH,QAAiBtD,KAAKuD,KAAKc,iBAAiBrE,KAAK5F,KAAK4B,eAC5D,GAAwB,MAApBsH,EAASvN,OAAgB,CAC3B,MAAMuO,EAAgBhB,EAASlQ,KAAKmR,gBAAkB,GACtDvE,KAAK/C,SAASuH,QAAQtH,IACpBA,EAAKC,SAAWmH,EAAcjC,SAASnF,EAAKA,OAEhD,CACF,CAAE,MAAOwG,GACPQ,QAAQR,MAAM,YAAaA,EAC7B,CACF,EAEAhD,cAAAA,GAEEV,KAAKM,eAAiBmE,YAAY,KACG,gBAA/BzE,KAAK5F,KAAK7D,kBAAsCyJ,KAAK5F,KAAK4B,eAC5DgE,KAAKoE,mBAEN,IACL,EAEAlI,wBAAAA,GAEE8D,KAAK5F,KAAK4C,SAAW,KACrBgD,KAAKoE,kBAGDpE,KAAK5F,KAAKmD,eAAe8E,SAASrC,KAAK5F,KAAK4B,iBAC9CgE,KAAK5F,KAAKmD,eAAiByC,KAAK5F,KAAKmD,eAAeyE,OAClDjK,GAAMA,IAAOiI,KAAK5F,KAAK4B,eAG7B,EAEAnC,eAAAA,GACEmG,KAAKtG,eAAgB,EACjBsG,KAAK0E,MAAMC,SACb3E,KAAK0E,MAAMC,QAAQC,gBAErB5E,KAAK5F,KAAO,CACVvF,SAAU,GACVyC,KAAM,GACNoI,QAAS,GACTpK,SAAU,KACVqB,QAAU,GACVE,SAAW,GACXN,iBAAkB,SAClByF,cAAe,KACfuB,eAAgB,GAChBQ,cAAe,EACff,SAAU,KAEd,EAEA,aAAMhD,GACJgG,KAAK0E,MAAMC,QAAQE,SAASC,UAC1B,IAAKC,EAAO,OACZ,MAAMC,EAAShF,KAAK5F,KACpB,IACE,MAAMkJ,QAAiBtD,KAAKuD,KAAK0B,sBAAsBD,GAC/B,MAApB1B,EAASvN,SAEXiK,KAAK5F,KAAO,CACVvF,SAAU,GACVyC,KAAM,GACNoI,QAAS,GACTpK,SAAU,KACVqB,QAAU,GACVE,SAAW,GACXN,iBAAkB,SAClByF,cAAe,KACfuB,eAAgB,GAChBQ,cAAe,EACff,SAAU,MAIZgD,KAAKtG,eAAgB,GAGrBiK,EAAAA,GAAAA,IAAU,CACRxT,KAAM,UACNyP,QAAS,SACTgE,SAAU,MAEZ5D,KAAKmD,QAAQ,GAEjB,CAAE,MAAOO,IACPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,UAAY8D,EAAM9D,SAAW,QACtCgE,SAAU,KAEd,GAEJ,EAEAsB,OAAAA,CAAQnN,GACNoN,GAAAA,EAAaC,QAAQ,qBAAsB,OAAQ,CACjDC,kBAAmB,KACnBC,iBAAkB,KAClBnV,KAAM,UACNoV,mBAAmB,IAElBC,KAAKV,UACJ,IACE,MAAMxB,QAAiBtD,KAAKuD,KAAKkC,mBAAmB1N,GAC7B,MAApBuL,EAASvN,UACV4N,EAAAA,GAAAA,IAAU,CACRxT,KAAM,UACNyP,QAAS,UAGXI,KAAKmD,QAAQnD,KAAK9G,MAAMC,SAE5B,CAAE,MAAOuK,IACPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,UAAY8D,EAAM9D,SAAW,QACtCgE,SAAU,KAEd,IAED8B,MAAM,MACL/B,EAAAA,GAAAA,IAAU,CACRxT,KAAM,OACNyP,QAAS,WAGjB,EAEA,eAAM+F,CAAUvS,GACd,MAAM4R,EAAS,IAAI5R,GACnB4R,EAAOnQ,SAAWmQ,EAAOnQ,SAAW,MACpC,IACE,MAAMyO,QAAiBtD,KAAKuD,KAAK0B,sBAAsBD,GAC/B,MAApB1B,EAASvN,UACX4N,EAAAA,GAAAA,IAAU,CACRxT,KAAM,UACNyP,QAAS,OACTgE,SAAU,MAEZ5D,KAAKmD,QAAQnD,KAAK9G,MAAMC,SAE5B,CAAE,MAAOuK,IACPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,UAAY8D,EAAM9D,SAAW,QACtCgE,SAAU,KAEd,CACF,EAEA,aAAM9L,CAAQC,GACZ,IAAKiI,KAAK0B,MAMR,YALA1B,KAAKmE,SAAS,CACZhU,KAAM,UACNyP,QAAS,aACTgE,SAAU,MAId,MAAMoB,EAAS,CAAEY,OAAQ7N,EAAI8N,IAAK7F,KAAK0B,OACvC,IAEE,MAAM4B,QAAiBtD,KAAKuD,KAAKzL,QAAQC,EAAIiN,GACrB,MAApB1B,EAASvN,UACX+P,EAAAA,GAAAA,IAAe,CACbnM,MAAO,QACPiG,QAAS,cACTzP,KAAM,UACNyT,SAAU,IACVmC,WAAW,EACXC,SAAU,cAIZhG,KAAKiG,kBAET,CAAE,MAAOvC,IAEPC,EAAAA,GAAAA,IAAU,CACRxT,KAAM,QACNyP,QAAS,YAAc8D,EAAM9D,SAAW,QACxCgE,SAAU,MAIZ5D,KAAKiG,iBACP,CACF,EAGAA,eAAAA,GAEEC,WAAW,KACTlG,KAAKmD,QAAQnD,KAAK9G,MAAMC,UACvB,IACL,EAEAjD,aAAAA,CAAcH,GACZ,OAAOiK,KAAKE,UAAUnK,IAASoK,MAAQpK,CACzC,EAEAD,cAAAA,CAAeC,GACb,OAAOiK,KAAKE,UAAUnK,IAASnG,OAAS,gBAC1C,EAGAuG,iBAAAA,CAAkBC,GAChB,MAAM+P,EAAU,CACd,SAAY,UACZ,WAAc,UACd,UAAa,SACb,WAAc,UACd,cAAiB,OACjB,aAAgB,IAElB,OAAOA,EAAQ/P,IAAe,MAChC,EAGAI,sBAAAA,CAAuB4P,GACrB,MAAMC,EAAU,CACd,OAAU,OACV,YAAe,SAEjB,OAAOA,EAAQD,IAASA,GAAQ,KAClC,EAEA9P,sBAAAA,CAAuB8P,GACrB,MAAMD,EAAU,CACd,OAAU,OACV,YAAe,WAEjB,OAAOA,EAAQC,IAAS,MAC1B,EAEA1Q,aAAAA,CAAc4Q,GACZ,IAAKA,EAAW,MAAO,GAEvB,MAAMC,EAAO,IAAIC,KAAKF,GAChBG,EAAM,IAAID,KACVE,EAAUlE,KAAKmE,OAAOF,EAAMF,GAAQ,KAE1C,GAAIG,EAAU,GAAI,MAAO,KAEzB,MAAME,EAAUpE,KAAKmE,MAAMD,EAAU,IACrC,GAAIE,EAAU,GAAI,MAAO,GAAGA,OAE5B,MAAMC,EAAQrE,KAAKmE,MAAMC,EAAU,IACnC,GAAIC,EAAQ,GAAI,MAAO,GAAGA,OAE1B,MAAMC,EAAOtE,KAAKmE,MAAME,EAAQ,IAChC,OAAIC,EAAO,EAAU,GAAGA,MAEjB9G,KAAK+G,OAAOC,MAAMV,GAAWW,MAAM,KAAK,EACjD,EAEArS,cAAAA,CAAesS,GACb,IAAKA,EAAK,MAAO,UAEjB,GAAIlH,KAAKI,WAAW8G,GAClB,OAAOlH,KAAKI,WAAW8G,GAIzB,IAAIC,EAAO,EACX,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAIrV,OAAQuV,IAC9BD,EAAOD,EAAIG,WAAWD,KAAOD,GAAQ,GAAKA,GAG5C,MAAMG,EAAS,CACb,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAGxCC,EAAQ/E,KAAKgF,IAAIL,GAAQG,EAAOzV,OAEtC,OADAmO,KAAKI,WAAW8G,GAAOI,EAAOC,GACvBvH,KAAKI,WAAW8G,EACzB,EAEA5O,aAAAA,CAAcI,EAASrE,GACrB,OAAQqE,GACN,IAAK,OACHsH,KAAK2F,UAAUtR,GACf,MACF,IAAK,SACH2L,KAAKkF,QAAQ7Q,EAAI0D,IACjB,MAEN,GAEF0P,OAAAA,GACEzH,KAAKmD,QAAQ,EACf,G,YCniCF,MAAMuE,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASC,IAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceTask.vue", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceTask.vue?697b"], "sourcesContent": ["<template>\r\n  <div class=\"performance-container\">\r\n    <!-- 顶部区域 -->\r\n    <div class=\"dashboard-header\">\r\n      <div class=\"header-title\">\r\n        <h2>性能测试任务</h2>\r\n        <el-tag type=\"info\" effect=\"plain\" class=\"header-tag\">任务列表</el-tag>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <div class=\"search-wrapper\">\r\n          <el-input \r\n            v-model=\"filterText\" \r\n            placeholder=\"搜索任务名称\" \r\n            class=\"search-input\"\r\n            clearable>\r\n            <template #prefix>\r\n              <el-icon class=\"search-icon\"><Search /></el-icon>\r\n            </template>\r\n          </el-input>\r\n          <el-button type=\"primary\" @click=\"searchClick\" class=\"search-btn\">搜索</el-button>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"popup('add')\" class=\"create-btn\">\r\n          <el-icon><Plus /></el-icon>新建任务\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 数据卡片 -->\r\n    <div class=\"task-stats\">\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-value\">{{ taskList.length }}</div>\r\n        <div class=\"stat-label\">当前任务</div>\r\n        <div class=\"stat-icon running-icon1\">\r\n          <el-icon><List /></el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-value\">{{ getRunningCount }}</div>\r\n        <div class=\"stat-label\">运行中</div>\r\n        <div class=\"stat-icon running-icon\">\r\n          <el-icon><Loading /></el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-value\">{{ getCompletedCount }}</div>\r\n        <div class=\"stat-label\">已完成</div>\r\n        <div class=\"stat-icon completed-icon\">\r\n          <el-icon><SuccessFilled /></el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-value\">{{ getErrorCount }}</div>\r\n        <div class=\"stat-label\">异常</div>\r\n        <div class=\"stat-icon error-icon\">\r\n          <el-icon><WarningFilled /></el-icon>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 表格区域 - 使用滚动容器 -->\r\n    <el-scrollbar class=\"table-scrollbar\" height=\"calc(100vh - 280px)\">\r\n      <div class=\"task-table-card\">\r\n        <el-table\r\n          v-loading=\"tableLoading\"\r\n          :data=\"taskList\"\r\n          style=\"width: 100%\"\r\n          row-key=\"id\"\r\n          :border=\"false\"\r\n          stripe\r\n          :height=\"tableHeight\"\r\n          highlight-current-row\r\n          :header-cell-style=\"{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }\">\r\n          \r\n          <!-- 选择列 -->\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          \r\n          <!-- 任务信息 -->\r\n          <el-table-column label=\"任务信息\" min-width=\"300\">\r\n            <template #default=\"{ row }\">\r\n              <div class=\"task-info\">\r\n                <div class=\"task-avatar-container\">\r\n                  <el-avatar :size=\"40\" class=\"task-avatar\" :style=\"{backgroundColor: getRandomColor(row.taskName)}\">\r\n                    {{row.taskName.substr(0,1).toUpperCase()}}\r\n                  </el-avatar>\r\n                </div>\r\n                <div class=\"task-detail\">\r\n                  <div class=\"task-name\">\r\n                    <router-link :to=\"`/maskMgrDetail/`\" @click=\"clickTaskManage(row)\" class=\"task-name-link\">\r\n                      {{ row.taskName }}\r\n                    </router-link>\r\n                  </div>\r\n                  <div class=\"task-meta\">\r\n                    <el-tag :type=\"row.taskType === '10' ? 'primary' : 'success'\" \r\n                      size=\"small\" \r\n                      effect=\"plain\" \r\n                      class=\"meta-tag\">\r\n                      {{ taskTypeMap[row.taskType.toString()] || row.taskType }}\r\n                    </el-tag>\r\n                    <div class=\"meta-info\">创建于 {{ formatTimeAgo(row.create_time) }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <!-- 状态列 -->\r\n          <el-table-column label=\"运行状态\" align=\"center\" width=\"120\">\r\n            <template #default=\"{ row }\">\r\n              <div class=\"status-cell\">\r\n                <div :class=\"['status-indicator', getStatusClass(row.status)]\"></div>\r\n                <span class=\"status-text\">{{ row.status_display || getStatusText(row.status) || '未设置' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <!-- 运行模式 -->\r\n          <el-table-column label=\"运行模式\" align=\"center\" width=\"120\">\r\n            <template #default=\"{ row }\">\r\n              <el-tag \r\n                effect=\"plain\" \r\n                size=\"small\" \r\n                class=\"mode-tag\"\r\n                :type=\"getRunPatternType(row.runPattern)\">\r\n                {{ row.taskType_display}}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <!-- 分布式模式 -->\r\n          <el-table-column label=\"分布式模式\" align=\"center\" width=\"120\">\r\n            <template #default=\"{ row }\">\r\n              <el-tag \r\n                effect=\"plain\" \r\n                size=\"small\" \r\n                :type=\"getDistributedModeType(row.distributed_mode)\">\r\n                {{ getDistributedModeText(row.distributed_mode) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <!-- 创建/更新信息 -->\r\n          <el-table-column label=\"任务创建者\" align=\"center\" width=\"160\">\r\n            <template #default=\"{ row }\">\r\n              <div class=\"user-info\">\r\n                <div class=\"user-avatar\">\r\n                  <el-avatar :size=\"28\" :style=\"{backgroundColor: getRandomColor(row.creator)}\">\r\n                    {{ row.creator ? row.creator.substr(0,1).toUpperCase() : 'U' }}\r\n                  </el-avatar>\r\n                </div>\r\n                <span class=\"user-name\">{{ row.creator }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <!-- 更新者 -->\r\n          <el-table-column label=\"最后更新\" align=\"center\" width=\"160\">\r\n            <template #default=\"{ row }\">\r\n              <div v-if=\"row.modifier && row.update_time\" class=\"update-info\">\r\n                <div class=\"update-by\">{{ row.modifier }}</div>\r\n                <div class=\"update-time\">{{ formatTimeAgo(row.update_time) }}</div>\r\n              </div>\r\n              <div v-else-if=\"row.create_time\" class=\"update-info\">\r\n                <div class=\"update-by\">{{ row.creator || '系统' }}</div>\r\n                <div class=\"update-time\">{{ formatTimeAgo(row.create_time) }}</div>\r\n              </div>\r\n              <el-tag v-else size=\"small\" effect=\"plain\" type=\"info\">未更新</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <!-- 备注 -->\r\n          <el-table-column label=\"备注\" align=\"center\" min-width=\"200\">\r\n            <template #default=\"{ row }\">\r\n              <el-tooltip\r\n                v-if=\"row.desc\"\r\n                class=\"box-item\"\r\n                effect=\"dark\"\r\n                :content=\"row.desc\"\r\n                placement=\"top-start\"\r\n              >\r\n                <div class=\"desc-text\">{{ row.desc }}</div>\r\n              </el-tooltip>\r\n              <span v-else class=\"no-data\">暂无备注</span>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <!-- 操作列 -->\r\n          <el-table-column label=\"操作\" fixed=\"right\" width=\"280\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  @click=\"runTask(row.id)\" \r\n                  type=\"primary\" \r\n                  plain \r\n                  size=\"small\" \r\n                  class=\"action-btn run-btn\">\r\n                  <el-icon><Promotion /></el-icon> 运行\r\n                </el-button>\r\n                <el-button \r\n                  @click=\"clickTaskManage(row)\" \r\n                  type=\"success\" \r\n                  plain \r\n                  size=\"small\" \r\n                  class=\"action-btn manage-btn\">\r\n                  <el-icon><Menu /></el-icon> 管理\r\n                </el-button>\r\n                <el-dropdown trigger=\"click\" @command=\"handleCommand($event, row)\" class=\"action-dropdown\">\r\n                  <el-button size=\"small\" class=\"more-btn\">\r\n                    <el-icon><More /></el-icon>\r\n                  </el-button>\r\n                  <template #dropdown>\r\n                    <el-dropdown-menu>\r\n                      <el-dropdown-item command=\"copy\">\r\n                        <el-icon><CopyDocument /></el-icon> 复制任务\r\n                      </el-dropdown-item>\r\n                      <el-dropdown-item divided command=\"delete\" class=\"danger-item\">\r\n                        <el-icon><Delete /></el-icon> 删除任务\r\n                      </el-dropdown-item>\r\n                    </el-dropdown-menu>\r\n                  </template>\r\n                </el-dropdown>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <!-- 空状态处理 -->\r\n          <template #empty>\r\n            <div class=\"empty-data\">\r\n              <el-empty :image-size=\"120\" description=\"暂无任务数据\">\r\n                <template #description>\r\n                  <p>暂无任务数据</p>\r\n                </template>\r\n                <el-button type=\"primary\" @click=\"popup('add')\">新建任务</el-button>\r\n              </el-empty>\r\n            </div>\r\n          </template>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            v-model:current-page=\"pages.current\"\r\n            background\r\n            layout=\"total, prev, pager, next, jumper\"\r\n            :page-size=\"100\"\r\n            :total=\"pages.count\"\r\n            @current-change=\"currentPages\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </div>\r\n\r\n  <!-- 新增任务弹窗 -->\r\n  <el-dialog\r\n    v-model=\"dialogVisible\"\r\n    :title=\"dialogTitle\"\r\n    width=\"800px\"\r\n    destroy-on-close\r\n    :before-close=\"clearValidation\"\r\n    class=\"task-dialog\">\r\n    <div class=\"dialog-content\">\r\n      <el-form :model=\"form\" :rules=\"rulesPerf\" ref=\"perfRef\" label-position=\"top\">\r\n        <div class=\"form-row\">\r\n          <el-form-item label=\"任务名称\" prop=\"taskName\" class=\"form-item-half\">\r\n            <el-input v-model=\"form.taskName\" maxlength=\"50\" placeholder=\"请输入任务名称\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属项目\" prop=\"project\" class=\"form-item-half\">\r\n            <el-input v-model=\"form.proName\" disabled />\r\n          </el-form-item>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <el-form-item label=\"任务类型\" prop=\"taskType\" class=\"form-item-half\">\r\n            <el-radio-group v-model=\"selectTaskType\" class=\"task-type-radio\">\r\n              <el-radio label=\"10\">\r\n                <div class=\"radio-content\">\r\n                  <el-icon class=\"radio-icon\"><Tickets /></el-icon>\r\n                  <span>普通任务</span>\r\n                </div>\r\n              </el-radio>\r\n              <el-radio label=\"20\">\r\n                <div class=\"radio-content\">\r\n                  <el-icon class=\"radio-icon\"><Timer /></el-icon>\r\n                  <span>定时任务</span>\r\n                </div>\r\n              </el-radio>\r\n            </el-radio-group>\r\n            <div v-if=\"form.taskType === '20'\" class=\"tip-box warning\">\r\n              <el-icon><InfoFilled /></el-icon>\r\n              <span>定时任务下只允许创建一个场景！</span>\r\n            </div>\r\n          </el-form-item>\r\n          <el-form-item label=\"运行模式\" prop=\"distributed_mode\" class=\"form-item-half\">\r\n            <el-radio-group v-model=\"form.distributed_mode\" class=\"run-mode-radio\">\r\n              <el-radio label=\"single\">\r\n                <div class=\"radio-content\">\r\n                  <el-icon class=\"radio-icon\"><Monitor /></el-icon>\r\n                  <span>单机模式</span>\r\n                </div>\r\n              </el-radio>\r\n              <el-radio label=\"distributed\">\r\n                <div class=\"radio-content\">\r\n                  <el-icon class=\"radio-icon\"><Connection /></el-icon>\r\n                  <span>分布式模式</span>\r\n                </div>\r\n              </el-radio>\r\n            </el-radio-group>\r\n            <div v-if=\"form.distributed_mode === 'distributed'\" class=\"tip-box info\">\r\n              <el-icon><InfoFilled /></el-icon>\r\n              <span>分布式模式需要配置主服务器和工作服务器</span>\r\n            </div>\r\n          </el-form-item>\r\n        </div>\r\n\r\n        <!-- 分布式配置 -->\r\n        <div v-if=\"form.distributed_mode === 'distributed'\" class=\"distributed-config\">\r\n          <div class=\"form-row\">\r\n            <el-form-item label=\"主服务器\" prop=\"master_server\" class=\"form-item-half\">\r\n              <el-select \r\n                v-model=\"form.master_server\" \r\n                placeholder=\"请选择主服务器\"\r\n                style=\"width: 100%\"\r\n                @change=\"handleMasterServerChange\">\r\n                <el-option\r\n                  v-for=\"server in availableServers\"\r\n                  :key=\"server.id\"\r\n                  :label=\"`${server.name} (${server.host_ip}:${server.host_port})`\"\r\n                  :value=\"server.id\">\r\n                  <div class=\"server-option\">\r\n                    <span class=\"server-name\">{{ server.name }}</span>\r\n                    <el-tag \r\n                      :type=\"server.status === 'active' ? 'success' : 'danger'\" \r\n                      size=\"small\">\r\n                      {{ server.status === 'active' ? '可用' : '不可用' }}\r\n                    </el-tag>\r\n                  </div>\r\n                  <div class=\"server-address\">{{ server.host_ip }}:{{ server.host_port }}</div>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"GUI端口\" prop=\"gui_port\" class=\"form-item-half\">\r\n              <el-select \r\n                v-model=\"form.gui_port\" \r\n                placeholder=\"请选择GUI端口\"\r\n                style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"port in guiPorts\"\r\n                  :key=\"port.port\"\r\n                  :label=\"`端口 ${port.port}`\"\r\n                  :value=\"port.port\"\r\n                  :disabled=\"port.occupied\">\r\n                  <div class=\"port-option\">\r\n                    <span class=\"port-number\">{{ port.port }}</span>\r\n                    <el-tag \r\n                      :type=\"port.occupied ? 'danger' : 'success'\" \r\n                      size=\"small\">\r\n                      {{ port.occupied ? '已占用' : '可用' }}\r\n                    </el-tag>\r\n                  </div>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <el-form-item label=\"工作服务器\" prop=\"worker_servers\" class=\"form-item-full\">\r\n              <el-select \r\n                v-model=\"form.worker_servers\" \r\n                multiple\r\n                placeholder=\"请选择工作服务器\"\r\n                style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"server in availableWorkerServers\"\r\n                  :key=\"server.id\"\r\n                  :label=\"`${server.name} (${server.host_ip}:${server.host_port})`\"\r\n                  :value=\"server.id\">\r\n                  <div class=\"server-option\">\r\n                    <span class=\"server-name\">{{ server.name }}</span>\r\n                    <el-tag \r\n                      :type=\"server.status === 'active' ? 'success' : 'danger'\" \r\n                      size=\"small\">\r\n                      {{ server.status === 'active' ? '可用' : '不可用' }}\r\n                    </el-tag>\r\n                  </div>\r\n                  <div class=\"server-address\">{{ server.host_ip }}:{{ server.host_port }}</div>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <el-form-item label=\"工作进程数\" prop=\"total_workers\" class=\"form-item-half\">\r\n              <el-input-number \r\n                v-model=\"form.total_workers\" \r\n                :min=\"1\" \r\n                :max=\"maxWorkers\"\r\n                placeholder=\"总工作进程数\"\r\n                style=\"width: 100%\" />\r\n              <div class=\"worker-info\">\r\n                <span>建议进程数: {{ recommendedWorkers }}</span>\r\n                <span>最大可用: {{ maxWorkers }}</span>\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n        </div>\r\n\r\n        <el-form-item label=\"任务描述\" prop=\"desc\">\r\n          <el-input \r\n            type=\"textarea\" \r\n            v-model=\"form.desc\" \r\n            placeholder=\"请输入任务描述\" \r\n            :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <template #footer>\r\n      <div class=\"dialog-footer\">\r\n        <el-button @click=\"clearValidation\" plain>取消</el-button>\r\n        <el-button type=\"primary\" @click=\"addTask\">确定</el-button>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport {mapMutations, mapState} from \"vuex\";\r\nimport {ElNotification, ElMessage, ElMessageBox} from \"element-plus\";\r\nimport { \r\n  Plus, \r\n  Promotion, \r\n  CopyDocument, \r\n  Menu, \r\n  Delete, \r\n  InfoFilled, \r\n  Search, \r\n  List, \r\n  Loading, \r\n  SuccessFilled, \r\n  WarningFilled,\r\n  More,\r\n  User,\r\n  Edit,\r\n  Tickets,\r\n  Timer,\r\n  Monitor,\r\n  Connection\r\n} from '@element-plus/icons-vue';\r\n\r\nexport default {\r\n  components: {\r\n    Plus,\r\n    Promotion,\r\n    CopyDocument,\r\n    Menu,\r\n    Delete,\r\n    InfoFilled,\r\n    Search,\r\n    List,\r\n    Loading,\r\n    SuccessFilled,\r\n    WarningFilled,\r\n    More,\r\n    User,\r\n    Edit,\r\n    Tickets,\r\n    Timer,\r\n    Monitor,\r\n    Connection\r\n  },\r\n  data() {\r\n    return {\r\n      // 状态码映射\r\n      taskTypeMap: {'10': '普通任务', '20': '定时任务'},\r\n      filterText: '',\r\n      taskList: [],\r\n      pages: {\r\n        count: 0,\r\n        current: 1\r\n      },\r\n      addDlg: false,\r\n      form: {\r\n        taskName: '',\r\n        desc: '',\r\n        project: '',\r\n        taskType: '10',\r\n        creator : '',\r\n        modifier : '',\r\n        distributed_mode: 'single',\r\n        master_server: null,\r\n        worker_servers: [],\r\n        total_workers: 1,\r\n        gui_port: 8089\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableLoading: false,\r\n      rulesPerf: {\r\n        taskName: [\r\n          { required: true, message: '请输入任务名称', trigger: 'blur' },\r\n          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }\r\n        ],\r\n        distributed_mode: [\r\n          { required: true, message: '请选择运行模式', trigger: 'change' }\r\n        ],\r\n        master_server: [\r\n          { \r\n            validator: (rule, value, callback) => {\r\n              if (this.form.distributed_mode === 'distributed' && !value) {\r\n                callback(new Error('分布式模式下必须选择主服务器'));\r\n              } else {\r\n                callback();\r\n              }\r\n            }, \r\n            trigger: 'change' \r\n          }\r\n        ],\r\n        worker_servers: [\r\n          { \r\n            validator: (rule, value, callback) => {\r\n              if (this.form.distributed_mode === 'distributed' && (!value || value.length === 0)) {\r\n                callback(new Error('分布式模式下必须选择至少一个工作服务器'));\r\n              } else {\r\n                callback();\r\n              }\r\n            }, \r\n            trigger: 'change' \r\n          }\r\n        ],\r\n        gui_port: [\r\n          { \r\n            validator: (rule, value, callback) => {\r\n              if (this.form.distributed_mode === 'distributed' && !value) {\r\n                callback(new Error('分布式模式下必须选择GUI端口'));\r\n              } else {\r\n                callback();\r\n              }\r\n            }, \r\n            trigger: 'change' \r\n          }\r\n        ],\r\n        total_workers: [\r\n          { \r\n            validator: (rule, value, callback) => {\r\n              if (this.form.distributed_mode === 'distributed') {\r\n                const maxWorkers = this.maxWorkers;\r\n                if (!value || value < 1) {\r\n                  callback(new Error('工作进程数不能小于1'));\r\n                } else if (value > maxWorkers) {\r\n                  callback(new Error(`工作进程数不能超过${maxWorkers}`));\r\n                } else {\r\n                  callback();\r\n                }\r\n              } else {\r\n                callback();\r\n              }\r\n            }, \r\n            trigger: 'blur' \r\n          }\r\n        ]\r\n      },\r\n      statusMap: {\r\n        '0': { text: '执行完成', type: 'success', class: 'status-completed' },\r\n        '1': { text: '执行中', type: 'warning', class: 'status-running' },\r\n        '99': { text: '执行失败', type: 'danger', class: 'status-error' }\r\n      },\r\n      colorCache: {},\r\n      tableHeight: 'calc(100vh - 330px)', // 初始高度，将在mounted中重新计算\r\n      resizeObserver: null,\r\n      \r\n      // 新增字段\r\n      availableServers: [],\r\n      guiPorts: [\r\n        { port: 8089, occupied: false },\r\n        { port: 8090, occupied: false },\r\n        { port: 8091, occupied: false },\r\n        { port: 8092, occupied: false },\r\n        { port: 8093, occupied: false },\r\n        { port: 8094, occupied: false },\r\n        { port: 8095, occupied: false },\r\n        { port: 8096, occupied: false }\r\n      ],\r\n      portCheckTimer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.calculateTableHeight();\r\n    this.loadServers();\r\n    this.startPortCheck();\r\n    \r\n    // 监听窗口大小变化\r\n    this.resizeObserver = new ResizeObserver(() => {\r\n      this.calculateTableHeight();\r\n    });\r\n    \r\n    // 监听容器大小变化\r\n    const container = document.querySelector('.table-scrollbar');\r\n    if (container) {\r\n      this.resizeObserver.observe(container);\r\n    }\r\n    \r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.calculateTableHeight);\r\n  },\r\n  beforeUnmount() {\r\n    // 组件卸载时移除事件监听器和观察器\r\n    window.removeEventListener('resize', this.calculateTableHeight);\r\n    \r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect();\r\n    }\r\n    \r\n    // 清理端口检查定时器\r\n    if (this.portCheckTimer) {\r\n      clearInterval(this.portCheckTimer);\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      pro: state => state.pro,\r\n      envId: state => state.envId\r\n    }),\r\n    username() {\r\n      return window.sessionStorage.getItem('username');\r\n    },\r\n    selectTaskType: {\r\n      get() {\r\n        return this.form.taskType.toString();\r\n      },\r\n      set(value) {\r\n        this.form.taskType = value;\r\n      }\r\n    },\r\n    getRunningCount() {\r\n      return this.taskList.filter(task => task.status === '1').length;\r\n    },\r\n    getCompletedCount() {\r\n      return this.taskList.filter(task => task.status === '0').length;\r\n    },\r\n    getErrorCount() {\r\n      return this.taskList.filter(task => task.status === '99').length;\r\n    },\r\n    // 无数据时固定表格最小高度\r\n    minTableHeight() {\r\n      return this.taskList.length > 0 ? undefined : '400px';\r\n    },\r\n    \r\n    // 计算可用的工作服务器（排除已选为主服务器的）\r\n    availableWorkerServers() {\r\n      return this.availableServers.filter(server => \r\n        server.id !== this.form.master_server\r\n      );\r\n    },\r\n    \r\n    // 计算最大工作进程数\r\n    maxWorkers() {\r\n      if (!this.form.worker_servers || this.form.worker_servers.length === 0) {\r\n        return 1;\r\n      }\r\n      \r\n      const selectedServers = this.availableServers.filter(server => \r\n        this.form.worker_servers.includes(server.id)\r\n      );\r\n      \r\n      return selectedServers.reduce((total, server) => {\r\n        return total + (server.max_workers || 4);\r\n      }, 0);\r\n    },\r\n    \r\n    // 计算推荐工作进程数\r\n    recommendedWorkers() {\r\n      return Math.ceil(this.maxWorkers * 0.7); // 推荐使用70%的资源\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapMutations(['checkedTask']),\r\n\r\n    // 计算表格高度\r\n    calculateTableHeight() {\r\n      // 根据实际DOM高度计算\r\n      this.$nextTick(() => {\r\n        const headerHeight = document.querySelector('.dashboard-header')?.offsetHeight || 0;\r\n        const statsHeight = document.querySelector('.task-stats')?.offsetHeight || 0;\r\n        const paginationHeight = document.querySelector('.pagination-container')?.offsetHeight || 0;\r\n        \r\n        // 计算表格可用高度（减去其他元素高度和间距）\r\n        const availableHeight = window.innerHeight - headerHeight - statsHeight - paginationHeight - 120; // 120为各种padding和margin\r\n        \r\n        // 确保最小高度\r\n        this.tableHeight = Math.max(availableHeight, 300) + 'px';\r\n      });\r\n    },\r\n\r\n    async allTask(page, query) {\r\n      this.tableLoading = true;\r\n      try {\r\n        const response = await this.$api.getPerformanceTask(this.pro.id, page, query)\r\n        if (response.status === 200) {\r\n          this.taskList = response.data.result;\r\n          this.pages.count = response.data.count;\r\n          this.pages.current = response.data.current;\r\n          \r\n          // 数据加载完成后重新计算表格高度\r\n          this.$nextTick(() => {\r\n            this.calculateTableHeight();\r\n          });\r\n        }\r\n      } catch (error) {\r\n        ElMessage({\r\n          type: 'error',\r\n          message: '获取任务列表失败',\r\n          duration: 3000\r\n        });\r\n      } finally {\r\n        this.tableLoading = false;\r\n      }\r\n    },\r\n\r\n    currentPages(currentPage) {\r\n      this.allTask(currentPage, this.filterText);\r\n    },\r\n\r\n    searchClick() {\r\n      this.allTask(1, this.filterText)\r\n    },\r\n\r\n    clickTaskManage(data) {\r\n      this.$router.push({ name: 'maskMgrDetail' });\r\n      this.checkedTask(data)\r\n    },\r\n\r\n    popup(type) {\r\n      this.dialogVisible = true;\r\n      // 根据不同的对话框类型设置标题\r\n      switch (type) {\r\n        case 'add':\r\n          this.dialogTitle = '创建新任务';\r\n          this.form.creator = this.username;\r\n          this.form.project = this.pro.id;\r\n          this.form.proName = this.pro.name;\r\n          // 加载服务器列表\r\n          this.loadServers();\r\n          delete this.form.modifier;\r\n          break;\r\n\r\n        default:\r\n          this.dialogTitle = '';\r\n          break;\r\n      }\r\n    },\r\n\r\n    // 新增方法\r\n    async loadServers() {\r\n      try {\r\n        const response = await this.$api.getServers(this.pro.id, 1);\r\n        if (response.status === 200) {\r\n          this.availableServers = response.data.result || [];\r\n        }\r\n      } catch (error) {\r\n        console.error('加载服务器列表失败:', error);\r\n        this.$message.error('加载服务器列表失败');\r\n      }\r\n    },\r\n\r\n    async checkPortStatus() {\r\n      if (!this.form.master_server) {\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        const response = await this.$api.checkServerPorts(this.form.master_server);\r\n        if (response.status === 200) {\r\n          const occupiedPorts = response.data.occupied_ports || [];\r\n          this.guiPorts.forEach(port => {\r\n            port.occupied = occupiedPorts.includes(port.port);\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('检查端口状态失败:', error);\r\n      }\r\n    },\r\n\r\n    startPortCheck() {\r\n      // 每30秒检查一次端口状态\r\n      this.portCheckTimer = setInterval(() => {\r\n        if (this.form.distributed_mode === 'distributed' && this.form.master_server) {\r\n          this.checkPortStatus();\r\n        }\r\n      }, 30000);\r\n    },\r\n\r\n    handleMasterServerChange() {\r\n      // 主服务器变化时重新检查端口状态\r\n      this.form.gui_port = 8089; // 重置为默认端口\r\n      this.checkPortStatus();\r\n      \r\n      // 清空已选择的工作服务器中包含主服务器的选项\r\n      if (this.form.worker_servers.includes(this.form.master_server)) {\r\n        this.form.worker_servers = this.form.worker_servers.filter(\r\n          id => id !== this.form.master_server\r\n        );\r\n      }\r\n    },\r\n\r\n    clearValidation() {\r\n      this.dialogVisible = false;\r\n      if (this.$refs.perfRef) {\r\n        this.$refs.perfRef.clearValidate(); // 添加条件检查\r\n      }\r\n      this.form = {\r\n        taskName: '',\r\n        desc: '',\r\n        project: '',\r\n        taskType: '10',\r\n        creator : '',\r\n        modifier : '',\r\n        distributed_mode: 'single',\r\n        master_server: null,\r\n        worker_servers: [],\r\n        total_workers: 1,\r\n        gui_port: 8089\r\n      }\r\n    },\r\n\r\n    async addTask() {\r\n      this.$refs.perfRef.validate(async vaild => {\r\n        if (!vaild) return;\r\n        const params = this.form\r\n        try {\r\n          const response = await this.$api.createPerformanceTask(params);\r\n          if (response.status === 201) {\r\n            // 先重置表单数据\r\n            this.form = {\r\n              taskName: '',\r\n              desc: '',\r\n              project: '',\r\n              taskType: '10',\r\n              creator : '',\r\n              modifier : '',\r\n              distributed_mode: 'single',\r\n              master_server: null,\r\n              worker_servers: [],\r\n              total_workers: 1,\r\n              gui_port: 8089\r\n            };\r\n            \r\n            // 再关闭弹窗\r\n            this.dialogVisible = false;\r\n            \r\n            // 最后显示成功消息并刷新列表\r\n            ElMessage({\r\n              type: 'success',\r\n              message: '任务创建成功',\r\n              duration: 1000\r\n            });\r\n            this.allTask(1);\r\n          }\r\n        } catch (error) {\r\n          ElMessage({\r\n            type: 'error',\r\n            message: '添加失败: ' + (error.message || '未知错误'),\r\n            duration: 3000\r\n          });\r\n        }\r\n      })\r\n    },\r\n\r\n    delTask(id) {\r\n      ElMessageBox.confirm('此操作将永久删除该任务, 是否继续?', '删除确认', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n        closeOnClickModal: false\r\n      })\r\n        .then(async () => {\r\n          try {\r\n            const response = await this.$api.delPerformanceTask(id)\r\n            if(response.status === 204) {\r\n              ElMessage({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              });\r\n              // 刷新页面\r\n              this.allTask(this.pages.current);\r\n            }\r\n          } catch (error) {\r\n            ElMessage({\r\n              type: 'error',\r\n              message: '删除失败: ' + (error.message || '未知错误'),\r\n              duration: 3000\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          });\r\n        });\r\n    },\r\n\r\n    async clickCopy(data) {\r\n      const params = {...data};\r\n      params.taskName = params.taskName + '_副本';\r\n      try {\r\n        const response = await this.$api.createPerformanceTask(params);\r\n        if (response.status === 201) {\r\n          ElMessage({\r\n            type: 'success',\r\n            message: '复制成功',\r\n            duration: 1000\r\n          });\r\n          this.allTask(this.pages.current);\r\n        }\r\n      } catch (error) {\r\n        ElMessage({\r\n          type: 'error',\r\n          message: '复制失败: ' + (error.message || '未知错误'),\r\n          duration: 3000\r\n        });\r\n      }\r\n    },\r\n\r\n    async runTask(id) {\r\n      if (!this.envId) {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '当前未选中执行环境!',\r\n          duration: 1000\r\n        });\r\n        return\r\n      }\r\n      const params = { taskId: id, env: this.envId };\r\n      try {\r\n        // 注意：这里不再调用updateTaskStatus方法，后端需要在运行任务时更新状态为\"执行中\"\r\n        const response = await this.$api.runTask(id, params);\r\n        if (response.status === 200) {\r\n          ElNotification({\r\n            title: '任务已启动',\r\n            message: '请前往报告列表查看结果',\r\n            type: 'success',\r\n            duration: 3000,\r\n            showClose: true,\r\n            position: 'top-right',\r\n          });\r\n          \r\n          // 刷新任务列表以获取最新状态\r\n          this.refreshTaskList();\r\n        }\r\n      } catch (error) {\r\n        // 注意：后端需要在任务启动失败时更新状态为\"执行失败\"\r\n        ElMessage({\r\n          type: 'error',\r\n          message: '任务启动失败: ' + (error.message || '未知错误'),\r\n          duration: 3000\r\n        });\r\n        \r\n        // 刷新任务列表以获取最新状态\r\n        this.refreshTaskList();\r\n      }\r\n    },\r\n    \r\n    // 刷新任务列表\r\n    refreshTaskList() {\r\n      // 延迟一小段时间后刷新列表，确保后端状态已更新\r\n      setTimeout(() => {\r\n        this.allTask(this.pages.current);\r\n      }, 1000);\r\n    },\r\n\r\n    getStatusText(status) {\r\n      return this.statusMap[status]?.text || status;\r\n    },\r\n\r\n    getStatusClass(status) {\r\n      return this.statusMap[status]?.class || 'status-unknown';\r\n    },\r\n\r\n\r\n    getRunPatternType(runPattern) {\r\n      const typeMap = {\r\n        'loadtest': 'primary',\r\n        'stresstest': 'warning',\r\n        'spiketest': 'danger',\r\n        'volumetest': 'success',\r\n        'endurancetest': 'info',\r\n        'baselinetest': ''\r\n      };\r\n      return typeMap[runPattern] || 'info';\r\n    },\r\n\r\n    // 分布式模式相关方法\r\n    getDistributedModeText(mode) {\r\n      const modeMap = {\r\n        'single': '单机模式',\r\n        'distributed': '分布式模式'\r\n      };\r\n      return modeMap[mode] || mode || '未设置';\r\n    },\r\n\r\n    getDistributedModeType(mode) {\r\n      const typeMap = {\r\n        'single': 'info',\r\n        'distributed': 'warning'\r\n      };\r\n      return typeMap[mode] || 'info';\r\n    },\r\n\r\n    formatTimeAgo(timestamp) {\r\n      if (!timestamp) return '';\r\n      \r\n      const date = new Date(timestamp);\r\n      const now = new Date();\r\n      const seconds = Math.floor((now - date) / 1000);\r\n      \r\n      if (seconds < 60) return '刚刚';\r\n      \r\n      const minutes = Math.floor(seconds / 60);\r\n      if (minutes < 60) return `${minutes}分钟前`;\r\n      \r\n      const hours = Math.floor(minutes / 60);\r\n      if (hours < 24) return `${hours}小时前`;\r\n      \r\n      const days = Math.floor(hours / 24);\r\n      if (days < 7) return `${days}天前`;\r\n      \r\n      return this.$tools.rTime(timestamp).split(' ')[0];\r\n    },\r\n\r\n    getRandomColor(str) {\r\n      if (!str) return '#909399';\r\n      \r\n      if (this.colorCache[str]) {\r\n        return this.colorCache[str];\r\n      }\r\n      \r\n      // 生成一个基于字符串的伪随机颜色\r\n      let hash = 0;\r\n      for (let i = 0; i < str.length; i++) {\r\n        hash = str.charCodeAt(i) + ((hash << 5) - hash);\r\n      }\r\n      \r\n      const colors = [\r\n        '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',\r\n        '#3963BC', '#4EC9B0', '#FF9F43', '#9C27B0', '#00BCD4',\r\n        '#795548', '#607D8B', '#FF5722', '#9E9E9E', '#4CAF50'\r\n      ];\r\n      \r\n      const index = Math.abs(hash) % colors.length;\r\n      this.colorCache[str] = colors[index];\r\n      return this.colorCache[str];\r\n    },\r\n    \r\n    handleCommand(command, row) {\r\n      switch (command) {\r\n        case 'copy':\r\n          this.clickCopy(row);\r\n          break;\r\n        case 'delete':\r\n          this.delTask(row.id);\r\n          break;\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.allTask(1)\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器样式 */\r\n.performance-container {\r\n  padding: 20px;\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 头部区域样式 */\r\n.dashboard-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: white;\r\n  padding: 16px 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.header-title h2 {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.header-tag {\r\n  font-weight: normal;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.search-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.search-input {\r\n  width: 280px;\r\n}\r\n\r\n.search-input :deep(.el-input__wrapper) {\r\n  border-radius: 6px;\r\n  box-shadow: 0 0 0 1px #dcdfe6 inset;\r\n}\r\n\r\n.search-icon {\r\n  color: #909399;\r\n}\r\n\r\n.search-btn {\r\n  height: 32px;\r\n  border-radius: 6px;\r\n}\r\n\r\n.create-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  font-weight: 500;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.create-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.task-stats {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  gap: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  position: relative;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  flex-direction: column;\r\n  transition: all 0.3s;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stat-value {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  color: #303133;\r\n}\r\n\r\n.stat-label {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.stat-icon {\r\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 20px;\r\n  color: white;\r\n}\r\n\r\n.stat-card:nth-child(1) {\r\n  border-top: 3px solid #409EFF;\r\n}\r\n.stat-card:nth-child(2) {\r\n  border-top: 3px solid #E6A23C;\r\n}\r\n.stat-card:nth-child(3) {\r\n  border-top: 3px solid #67C23A;\r\n}\r\n.stat-card:nth-child(4) {\r\n  border-top: 3px solid #F56C6C;\r\n}\r\n\r\n.running-icon {\r\n  background-color: #E6A23C;\r\n}\r\n.running-icon1 {\r\n  background-color: #409EFF;\r\n}\r\n.completed-icon {\r\n  background-color: #67C23A;\r\n}\r\n\r\n.error-icon {\r\n  background-color: #F56C6C;\r\n}\r\n\r\n/* 滚动容器样式 */\r\n.table-scrollbar {\r\n  flex: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.table-scrollbar :deep(.el-scrollbar__wrap) {\r\n  overflow-x: hidden;\r\n}\r\n\r\n/* 表格卡片样式 */\r\n.task-table-card {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 表格内容样式 */\r\n:deep(.el-table) {\r\n  --el-table-header-bg-color: #f5f7fa;\r\n  --el-table-row-hover-bg-color: #f0f5ff;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  flex: 1;\r\n}\r\n\r\n:deep(.el-table__header) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.el-table__row) {\r\n  transition: all 0.2s;\r\n}\r\n\r\n:deep(.el-table__row:hover) {\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {\r\n  background-color: #fafafa;\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-data {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 60px 0;\r\n}\r\n\r\n.empty-data p {\r\n  margin-top: 0;\r\n  margin-bottom: 20px;\r\n  color: #909399;\r\n}\r\n\r\n/* 任务信息样式 */\r\n.task-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.task-avatar-container {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.task-avatar {\r\n  font-weight: 500;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.task-detail {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.task-name {\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.task-name-link {\r\n  color: #303133;\r\n  text-decoration: none;\r\n  transition: all 0.3s;\r\n  position: relative;\r\n}\r\n\r\n.task-name-link:hover {\r\n  color: var(--el-color-primary);\r\n}\r\n\r\n.task-name-link:hover::after {\r\n  content: '';\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 2px;\r\n  background-color: var(--el-color-primary);\r\n  bottom: -2px;\r\n  left: 0;\r\n  border-radius: 1px;\r\n}\r\n\r\n.task-meta {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.meta-tag {\r\n  padding: 0 6px;\r\n  height: 22px;\r\n  line-height: 20px;\r\n  font-size: 12px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.meta-info {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 状态样式 */\r\n.status-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n}\r\n\r\n.status-indicator {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.status-running {\r\n  background-color: #E6A23C;\r\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);\r\n}\r\n\r\n.status-completed {\r\n  background-color: #67C23A;\r\n  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);\r\n}\r\n\r\n.status-error {\r\n  background-color: #F56C6C;\r\n  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n}\r\n\r\n/* 模式标签 */\r\n.mode-tag {\r\n  min-width: 60px;\r\n}\r\n\r\n/* 用户信息 */\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.user-avatar {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.user-name {\r\n  font-size: 13px;\r\n  font-weight: 500;\r\n  max-width: 80px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 更新信息 */\r\n.update-info {\r\n  text-align: center;\r\n}\r\n\r\n.update-by {\r\n  font-size: 13px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.update-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n/* 备注样式 */\r\n.desc-text {\r\n  max-width: 200px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-size: 13px;\r\n}\r\n\r\n/* 操作区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  height: 32px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.action-btn:hover {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.more-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  padding: 0;\r\n  border-radius: 4px;\r\n}\r\n\r\n:deep(.danger-item) {\r\n  color: #F56C6C;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 对话框样式 */\r\n:deep(.task-dialog .el-dialog) {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  max-width: 90%;\r\n}\r\n\r\n:deep(.task-dialog .el-dialog__header) {\r\n  padding: 20px;\r\n  margin-right: 0;\r\n  background: linear-gradient(135deg, #409EFF, #2a62ff);\r\n}\r\n\r\n:deep(.task-dialog .el-dialog__title) {\r\n  color: white;\r\n  font-weight: 600;\r\n  font-size: 18px;\r\n}\r\n\r\n:deep(.task-dialog .el-dialog__headerbtn .el-dialog__close) {\r\n  color: white;\r\n}\r\n\r\n.dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n/* 表单布局样式 */\r\n.form-row {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.form-item-half {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.form-item-full {\r\n  width: 100%;\r\n}\r\n\r\n:deep(.task-dialog .el-form-item__label) {\r\n  padding-bottom: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n:deep(.task-dialog .el-input__wrapper),\r\n:deep(.task-dialog .el-textarea__inner) {\r\n  border-radius: 6px;\r\n  box-shadow: 0 0 0 1px #dcdfe6 inset;\r\n  transition: all 0.3s;\r\n}\r\n\r\n:deep(.task-dialog .el-input__wrapper:hover),\r\n:deep(.task-dialog .el-textarea__inner:hover) {\r\n  box-shadow: 0 0 0 1px var(--el-color-primary) inset;\r\n}\r\n\r\n:deep(.task-dialog .el-form-item:last-child) {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 任务类型和运行模式选择样式 */\r\n.task-type-radio,\r\n.run-mode-radio {\r\n  width: 100%;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.task-type-radio .el-radio,\r\n.run-mode-radio .el-radio {\r\n  margin-right: 0;\r\n  margin-bottom: 10px;\r\n  padding: 10px;\r\n  border: 2px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n  width: calc(50% - 5px);\r\n}\r\n\r\n.task-type-radio .el-radio:last-child,\r\n.run-mode-radio .el-radio:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.task-type-radio .el-radio:hover,\r\n.run-mode-radio .el-radio:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.task-type-radio .el-radio.is-checked,\r\n.run-mode-radio .el-radio.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.radio-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.radio-icon {\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 分布式配置区域样式 */\r\n.distributed-config {\r\n  margin-top: 10px;\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 服务器和端口选项样式 */\r\n.server-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  padding: 0 5px;\r\n}\r\n\r\n.server-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  flex: 1;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.server-address {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  padding-left: 5px;\r\n}\r\n\r\n/* 端口选项样式 */\r\n.port-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.port-number {\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 工作进程数信息 */\r\n.worker-info {\r\n  margin-top: 8px;\r\n  display: flex;\r\n  gap: 20px;\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n/* 提示框样式 */\r\n.tip-box {\r\n  margin-top: 8px;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.tip-box.warning {\r\n  background-color: #fdf6ec;\r\n  border: 1px solid #f5dab1;\r\n  color: #e6a23c;\r\n}\r\n\r\n.tip-box.info {\r\n  background-color: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  color: #409eff;\r\n}\r\n\r\n/* 下拉选项样式 */\r\n:deep(.el-select-dropdown__item) {\r\n  padding: 8px 10px;\r\n  height: auto;\r\n}\r\n\r\n:deep(.el-select-dropdown__item .server-option) {\r\n  padding: 0;\r\n}\r\n\r\n:deep(.el-tag) {\r\n  margin: 0;\r\n}\r\n</style>", "import { render } from \"./PerformanceTask.vue?vue&type=template&id=0ebe0c80&scoped=true\"\nimport script from \"./PerformanceTask.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceTask.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceTask.vue?vue&type=style&index=0&id=0ebe0c80&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0ebe0c80\"]])\n\nexport default __exports__"], "names": ["class", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_tag", "type", "effect", "_cache", "_hoisted_4", "_hoisted_5", "_component_el_input", "$data", "filterText", "$event", "placeholder", "clearable", "prefix", "_withCtx", "_component_el_icon", "_component_Search", "_component_el_button", "onClick", "$options", "searchClick", "popup", "_component_Plus", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "taskList", "length", "_hoisted_9", "_component_List", "_hoisted_10", "_hoisted_11", "getRunningCount", "_hoisted_12", "_component_Loading", "_hoisted_13", "_hoisted_14", "getCompletedCount", "_hoisted_15", "_component_SuccessFilled", "_hoisted_16", "_hoisted_17", "getErrorCount", "_hoisted_18", "_component_WarningFilled", "_component_el_scrollbar", "height", "_hoisted_19", "_createBlock", "_component_el_table", "data", "style", "border", "stripe", "tableHeight", "background", "color", "fontWeight", "empty", "_hoisted_40", "_component_el_empty", "description", "_component_el_table_column", "width", "align", "label", "default", "row", "_hoisted_20", "_hoisted_21", "_component_el_avatar", "size", "_normalizeStyle", "backgroundColor", "getRandomColor", "taskName", "substr", "toUpperCase", "_hoisted_22", "_hoisted_23", "_component_router_link", "to", "clickTaskManage", "_hoisted_24", "taskType", "taskTypeMap", "toString", "_hoisted_25", "formatTimeAgo", "create_time", "_hoisted_26", "_normalizeClass", "getStatusClass", "status", "_hoisted_27", "status_display", "getStatusText", "getRunPatternType", "runPattern", "taskType_display", "getDistributedModeType", "distributed_mode", "getDistributedModeText", "_hoisted_28", "_hoisted_29", "creator", "_hoisted_30", "modifier", "update_time", "_createElementBlock", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "desc", "_component_el_tooltip", "content", "placement", "_hoisted_37", "_hoisted_38", "fixed", "_hoisted_39", "runTask", "id", "plain", "_component_Promotion", "_component_Menu", "_component_el_dropdown", "trigger", "onCommand", "handleCommand", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "_component_CopyDocument", "divided", "_component_Delete", "_component_More", "tableLoading", "_hoisted_41", "_component_el_pagination", "pages", "current", "layout", "total", "count", "onCurrentChange", "currentPages", "_component_el_dialog", "dialogVisible", "title", "dialogTitle", "clearValidation", "footer", "_hoisted_64", "addTask", "_hoisted_42", "_component_el_form", "model", "form", "rules", "rulesPerf", "ref", "_hoisted_43", "_component_el_form_item", "prop", "maxlength", "proName", "disabled", "_hoisted_44", "_component_el_radio_group", "selectTaskType", "_component_el_radio", "_hoisted_45", "_component_Tickets", "_hoisted_46", "_component_Timer", "_hoisted_47", "_component_InfoFilled", "_hoisted_48", "_component_Monitor", "_hoisted_49", "_component_Connection", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_component_el_select", "master_server", "onChange", "handleMasterServerChange", "_Fragment", "_renderList", "availableServers", "server", "_component_el_option", "key", "name", "host_ip", "host_port", "value", "_hoisted_53", "_hoisted_54", "_hoisted_55", "gui_port", "gui<PERSON><PERSON><PERSON>", "port", "occupied", "_hoisted_56", "_hoisted_57", "_hoisted_58", "worker_servers", "multiple", "availableWorkerServers", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_component_el_input_number", "total_workers", "min", "max", "maxWorkers", "_hoisted_63", "recommendedWorkers", "rows", "components", "Plus", "Promotion", "CopyDocument", "<PERSON><PERSON>", "Delete", "InfoFilled", "Search", "List", "Loading", "SuccessFilled", "WarningFilled", "More", "User", "Edit", "Tickets", "Timer", "Monitor", "Connection", "addDlg", "project", "required", "message", "validator", "rule", "callback", "this", "Error", "statusMap", "text", "colorCache", "resizeObserver", "portCheckTimer", "mounted", "calculateTableHeight", "loadServers", "startPortCheck", "ResizeObserver", "container", "document", "querySelector", "observe", "window", "addEventListener", "beforeUnmount", "removeEventListener", "disconnect", "clearInterval", "computed", "mapState", "pro", "state", "envId", "username", "sessionStorage", "getItem", "get", "set", "filter", "task", "minTableHeight", "undefined", "selectedServers", "includes", "reduce", "max_workers", "Math", "ceil", "methods", "mapMutations", "$nextTick", "headerHeight", "offsetHeight", "statsHeight", "paginationHeight", "availableHeight", "innerHeight", "allTask", "page", "query", "response", "$api", "getPerformanceTask", "result", "error", "ElMessage", "duration", "currentPage", "$router", "push", "checkedTask", "getServers", "console", "$message", "checkPortStatus", "checkServerPorts", "occupiedPorts", "occupied_ports", "for<PERSON>ach", "setInterval", "$refs", "perfRef", "clearValidate", "validate", "async", "vaild", "params", "createPerformanceTask", "delTask", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "closeOnClickModal", "then", "delPerformanceTask", "catch", "clickCopy", "taskId", "env", "ElNotification", "showClose", "position", "refreshTaskList", "setTimeout", "typeMap", "mode", "modeMap", "timestamp", "date", "Date", "now", "seconds", "floor", "minutes", "hours", "days", "$tools", "rTime", "split", "str", "hash", "i", "charCodeAt", "colors", "index", "abs", "created", "__exports__", "render"], "sourceRoot": ""}