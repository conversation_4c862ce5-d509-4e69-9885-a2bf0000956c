"use strict";(self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[]).push([[233],{98233:function(e,s,t){t.r(s),t.d(s,{default:function(){return we}});var a=t(56768),l=t(24232);const n={class:"task-comparison"},i={class:"page-header"},r={class:"header-content"},o={class:"header-actions"},c={class:"card-header"},u={class:"filter-section"},d={class:"task-list"},k=["onClick"],m={class:"task-header"},p={class:"task-info"},h={class:"task-name"},_={class:"task-status"},g={class:"task-metrics"},f={class:"metric-item"},b={class:"metric-value"},v={class:"metric-item"},L={class:"metric-value"},y={class:"metric-item"},F={class:"metric-value"},R={class:"task-footer"},C={class:"executor"},T={class:"time"},w={key:0,class:"selection-indicator"},S={class:"selection-hint"},x={key:1,class:"comparison-result"},E={class:"result-actions"},X={class:"card-header"},$={key:0,class:"overview-stats"},D={class:"stat-item"},P={class:"stat-value"},I={class:"stat-item"},W={class:"stat-value"},N={class:"stat-item"},K={class:"stat-value"},V={key:1,class:"no-data-message"},O={class:"card-header"},z={class:"ranking-section"},A={class:"ranking-list"},M={class:"rank"},j={class:"name"},Q={class:"value"},Y={key:0,class:"no-ranking-data"},q={class:"ranking-section"},H={class:"ranking-list"},U={class:"rank"},B={class:"name"},J={class:"value"},G={key:0,class:"no-ranking-data"},Z={class:"ranking-section"},ee={class:"ranking-list"},se={class:"rank"},te={class:"name"},ae={class:"value"},le={key:0,class:"no-ranking-data"},ne={class:"ranking-section"},ie={class:"ranking-list"},re={class:"rank"},oe={class:"name"},ce={class:"value"},ue={key:0,class:"no-ranking-data"},de={class:"card-header"},ke={class:"card-header"},me={ref:"tpsChart",class:"chart-container"},pe={ref:"responseTimeChart",class:"chart-container"},he={class:"card-header"},_e={class:"summary-content"},ge={class:"summary-details"},fe={key:2,class:"loading-overlay"};function be(e,s,t,be,ve,Le){const ye=(0,a.g2)("el-breadcrumb-item"),Fe=(0,a.g2)("el-breadcrumb"),Re=(0,a.g2)("Back"),Ce=(0,a.g2)("el-icon"),Te=(0,a.g2)("el-button"),we=(0,a.g2)("DataAnalysis"),Se=(0,a.g2)("el-input"),xe=(0,a.g2)("el-col"),Ee=(0,a.g2)("el-option"),Xe=(0,a.g2)("el-select"),$e=(0,a.g2)("el-date-picker"),De=(0,a.g2)("el-row"),Pe=(0,a.g2)("el-tag"),Ie=(0,a.g2)("Check"),We=(0,a.g2)("el-alert"),Ne=(0,a.g2)("el-card"),Ke=(0,a.g2)("PieChart"),Ve=(0,a.g2)("InfoFilled"),Oe=(0,a.g2)("TrendCharts"),ze=(0,a.g2)("el-table-column"),Ae=(0,a.g2)("el-table"),Me=(0,a.g2)("Loading");return(0,a.uX)(),(0,a.CE)("div",n,[(0,a.Lk)("div",i,[(0,a.bF)(Fe,{separator:"/"},{default:(0,a.k6)(()=>[(0,a.bF)(ye,{to:{path:"/performance/result"}},{default:(0,a.k6)(()=>s[3]||(s[3]=[(0,a.eW)("性能测试")])),_:1,__:[3]}),(0,a.bF)(ye,null,{default:(0,a.k6)(()=>s[4]||(s[4]=[(0,a.eW)("任务对比")])),_:1,__:[4]})]),_:1}),(0,a.Lk)("div",r,[s[6]||(s[6]=(0,a.Lk)("div",{class:"header-title"},[(0,a.Lk)("h1",null,"任务性能对比分析"),(0,a.Lk)("p",null,"对比不同任务的性能指标，发现优化机会")],-1)),(0,a.Lk)("div",o,[(0,a.bF)(Te,{type:"primary",onClick:Le.returnToReportList,plain:""},{default:(0,a.k6)(()=>[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Re)]),_:1}),s[5]||(s[5]=(0,a.eW)("返回报告列表 "))]),_:1,__:[5]},8,["onClick"])])])]),ve.comparisonResult?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(Ne,{key:0,class:"selection-card",shadow:"hover"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",c,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(we)]),_:1}),s[7]||(s[7]=(0,a.Lk)("span",null,"选择对比任务",-1))])]),default:(0,a.k6)(()=>[(0,a.Lk)("div",u,[(0,a.bF)(De,{gutter:20},{default:(0,a.k6)(()=>[(0,a.bF)(xe,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(Se,{modelValue:ve.searchKeyword,"onUpdate:modelValue":s[0]||(s[0]=e=>ve.searchKeyword=e),placeholder:"搜索任务名称","prefix-icon":"Search",clearable:"",onInput:Le.handleSearch},null,8,["modelValue","onInput"])]),_:1}),(0,a.bF)(xe,{span:6},{default:(0,a.k6)(()=>[(0,a.bF)(Xe,{modelValue:ve.statusFilter,"onUpdate:modelValue":s[1]||(s[1]=e=>ve.statusFilter=e),placeholder:"报告状态",clearable:"",onChange:Le.handleSearch},{default:(0,a.k6)(()=>[(0,a.bF)(Ee,{label:"全部",value:""}),(0,a.bF)(Ee,{label:"已完成",value:"0"}),(0,a.bF)(Ee,{label:"执行中",value:"1"}),(0,a.bF)(Ee,{label:"运行失败",value:"99"})]),_:1},8,["modelValue","onChange"])]),_:1}),(0,a.bF)(xe,{span:8},{default:(0,a.k6)(()=>[(0,a.bF)($e,{modelValue:ve.timeRange,"onUpdate:modelValue":s[2]||(s[2]=e=>ve.timeRange=e),type:"datetimerange","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onChange:Le.handleSearch},null,8,["modelValue","onChange"])]),_:1}),(0,a.bF)(xe,{span:4},{default:(0,a.k6)(()=>[(0,a.bF)(Te,{type:"primary",onClick:Le.performComparison,disabled:ve.selectedTasks.length<2},{default:(0,a.k6)(()=>[(0,a.eW)(" 开始对比 ("+(0,l.v_)(ve.selectedTasks.length)+") ",1)]),_:1},8,["onClick","disabled"])]),_:1})]),_:1})]),(0,a.Lk)("div",d,[(0,a.bF)(De,{gutter:20},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(Le.filteredReports,e=>((0,a.uX)(),(0,a.Wv)(xe,{span:8,key:e.id},{default:(0,a.k6)(()=>[(0,a.Lk)("div",{class:(0,l.C4)(["task-card",{selected:Le.isSelected(e),disabled:!Le.canSelect(e)}]),onClick:s=>Le.toggleSelection(e)},[(0,a.Lk)("div",m,[(0,a.Lk)("div",p,[(0,a.Lk)("h4",null,(0,l.v_)(e.reportName),1),(0,a.Lk)("p",h,(0,l.v_)(e.task?.taskName||"未知任务"),1)]),(0,a.Lk)("div",_,[(0,a.bF)(Pe,{type:Le.getStatusType(e.reportStatus),size:"small"},{default:(0,a.k6)(()=>[(0,a.eW)((0,l.v_)(Le.getStatusText(e.reportStatus)),1)]),_:2},1032,["type"])])]),(0,a.Lk)("div",g,[(0,a.Lk)("div",f,[s[8]||(s[8]=(0,a.Lk)("span",{class:"metric-label"},"TPS",-1)),(0,a.Lk)("span",b,(0,l.v_)(Le.formatNumber(e.avgTps||0,2)),1)]),(0,a.Lk)("div",v,[s[9]||(s[9]=(0,a.Lk)("span",{class:"metric-label"},"响应时间",-1)),(0,a.Lk)("span",L,(0,l.v_)(Le.formatNumber(e.avgResponseTime||0,2))+"ms",1)]),(0,a.Lk)("div",y,[s[10]||(s[10]=(0,a.Lk)("span",{class:"metric-label"},"成功率",-1)),(0,a.Lk)("span",F,(0,l.v_)(Le.getSuccessRate(e))+"%",1)])]),(0,a.Lk)("div",R,[(0,a.Lk)("span",C,(0,l.v_)(e.executor||"未知"),1),(0,a.Lk)("span",T,(0,l.v_)(Le.formatDate(e.create_time)),1)]),Le.isSelected(e)?((0,a.uX)(),(0,a.CE)("div",w,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ie)]),_:1})])):(0,a.Q3)("",!0)],10,k)]),_:2},1024))),128))]),_:1})]),(0,a.Lk)("div",S,[(0,a.bF)(We,{title:`已选择 ${ve.selectedTasks.length} 个任务${ve.selectedTasks.length<2?"，至少需要选择2个任务进行对比":ve.selectedTasks.length>5?"，最多支持5个任务对比":""}`,type:ve.selectedTasks.length<2?"warning":ve.selectedTasks.length>5?"error":"success",closable:!1,"show-icon":""},null,8,["title","type"])])]),_:1})),ve.comparisonResult?((0,a.uX)(),(0,a.CE)("div",x,[(0,a.Lk)("div",E,[(0,a.bF)(Te,{onClick:Le.resetComparison,icon:"ArrowLeft"},{default:(0,a.k6)(()=>s[11]||(s[11]=[(0,a.eW)("重新选择")])),_:1,__:[11]},8,["onClick"]),(0,a.bF)(Te,{type:"success",onClick:Le.exportReport,icon:"Download"},{default:(0,a.k6)(()=>s[12]||(s[12]=[(0,a.eW)("导出报告")])),_:1,__:[12]},8,["onClick"])]),(0,a.bF)(Ne,{class:"overview-card",shadow:"hover"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",X,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ke)]),_:1}),s[13]||(s[13]=(0,a.Lk)("span",null,"对比概览",-1))])]),default:(0,a.k6)(()=>[(0,a.bF)(De,{gutter:20},{default:(0,a.k6)(()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ve.comparisonResult.task_info,(e,t)=>((0,a.uX)(),(0,a.Wv)(xe,{xs:24,sm:24,md:24/Math.min(Object.keys(ve.comparisonResult.task_info).length,4),key:t},{default:(0,a.k6)(()=>[(0,a.Lk)("div",{class:(0,l.C4)(["overview-item",{"no-data":!Le.hasMetricsData(t)}])},[(0,a.Lk)("h3",null,(0,l.v_)(e.task_name),1),Le.hasMetricsData(t)?((0,a.uX)(),(0,a.CE)("div",$,[(0,a.Lk)("div",D,[(0,a.Lk)("span",P,(0,l.v_)(Le.formatNumber(ve.comparisonResult.comparison_metrics[t]?.averages?.tps,2)),1),s[14]||(s[14]=(0,a.Lk)("span",{class:"stat-label"},"平均TPS",-1))]),(0,a.Lk)("div",I,[(0,a.Lk)("span",W,(0,l.v_)(Le.formatNumber(ve.comparisonResult.comparison_metrics[t]?.averages?.response_time,2))+"ms",1),s[15]||(s[15]=(0,a.Lk)("span",{class:"stat-label"},"响应时间",-1))]),(0,a.Lk)("div",N,[(0,a.Lk)("span",K,(0,l.v_)(Le.formatNumber(ve.comparisonResult.comparison_metrics[t]?.averages?.error_rate,2))+"%",1),s[16]||(s[16]=(0,a.Lk)("span",{class:"stat-label"},"错误率",-1))])])):((0,a.uX)(),(0,a.CE)("div",V,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ve)]),_:1}),s[17]||(s[17]=(0,a.eW)(" 无可用数据 "))]))],2)]),_:2},1032,["md"]))),128))]),_:1})]),_:1}),(0,a.bF)(Ne,{class:"ranking-card",shadow:"hover"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",O,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Oe)]),_:1}),s[19]||(s[19]=(0,a.Lk)("span",null,"性能排名",-1)),Object.keys(ve.comparisonResult.comparison_metrics).length<Object.keys(ve.comparisonResult.task_info).length?((0,a.uX)(),(0,a.Wv)(Te,{key:0,type:"warning",size:"small",plain:"",style:{"margin-left":"10px"}},{default:(0,a.k6)(()=>s[18]||(s[18]=[(0,a.eW)(" 部分任务数据不完整 ")])),_:1,__:[18]})):(0,a.Q3)("",!0)])]),default:(0,a.k6)(()=>[(0,a.bF)(De,{gutter:20},{default:(0,a.k6)(()=>[(0,a.bF)(xe,{span:6},{default:(0,a.k6)(()=>[(0,a.Lk)("div",z,[s[21]||(s[21]=(0,a.Lk)("h4",null,"TPS排名",-1)),(0,a.Lk)("div",A,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ve.comparisonResult.rankings.tps,(e,s)=>((0,a.uX)(),(0,a.CE)("div",{key:e.task_id,class:"ranking-item"},[(0,a.Lk)("span",M,(0,l.v_)(s+1),1),(0,a.Lk)("span",j,(0,l.v_)(e.task_name),1),(0,a.Lk)("span",Q,(0,l.v_)(Le.formatNumber(e.value,2)),1)]))),128)),0===ve.comparisonResult.rankings.tps.length?((0,a.uX)(),(0,a.CE)("div",Y,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ve)]),_:1}),s[20]||(s[20]=(0,a.eW)(" 无排名数据 "))])):(0,a.Q3)("",!0)])])]),_:1}),(0,a.bF)(xe,{span:6},{default:(0,a.k6)(()=>[(0,a.Lk)("div",q,[s[23]||(s[23]=(0,a.Lk)("h4",null,"响应时间排名",-1)),(0,a.Lk)("div",H,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ve.comparisonResult.rankings.response_time,(e,s)=>((0,a.uX)(),(0,a.CE)("div",{key:e.task_id,class:"ranking-item"},[(0,a.Lk)("span",U,(0,l.v_)(s+1),1),(0,a.Lk)("span",B,(0,l.v_)(e.task_name),1),(0,a.Lk)("span",J,(0,l.v_)(Le.formatNumber(e.value,2))+"ms",1)]))),128)),0===ve.comparisonResult.rankings.response_time.length?((0,a.uX)(),(0,a.CE)("div",G,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ve)]),_:1}),s[22]||(s[22]=(0,a.eW)(" 无排名数据 "))])):(0,a.Q3)("",!0)])])]),_:1}),(0,a.bF)(xe,{span:6},{default:(0,a.k6)(()=>[(0,a.Lk)("div",Z,[s[25]||(s[25]=(0,a.Lk)("h4",null,"错误率排名",-1)),(0,a.Lk)("div",ee,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ve.comparisonResult.rankings.error_rate,(e,s)=>((0,a.uX)(),(0,a.CE)("div",{key:e.task_id,class:"ranking-item"},[(0,a.Lk)("span",se,(0,l.v_)(s+1),1),(0,a.Lk)("span",te,(0,l.v_)(e.task_name),1),(0,a.Lk)("span",ae,(0,l.v_)(Le.formatNumber(e.value,2))+"%",1)]))),128)),0===ve.comparisonResult.rankings.error_rate.length?((0,a.uX)(),(0,a.CE)("div",le,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ve)]),_:1}),s[24]||(s[24]=(0,a.eW)(" 无排名数据 "))])):(0,a.Q3)("",!0)])])]),_:1}),(0,a.bF)(xe,{span:6},{default:(0,a.k6)(()=>[(0,a.Lk)("div",ne,[s[27]||(s[27]=(0,a.Lk)("h4",null,"稳定性排名",-1)),(0,a.Lk)("div",ie,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ve.comparisonResult.rankings.stability,(e,s)=>((0,a.uX)(),(0,a.CE)("div",{key:e.task_id,class:"ranking-item"},[(0,a.Lk)("span",re,(0,l.v_)(s+1),1),(0,a.Lk)("span",oe,(0,l.v_)(e.task_name),1),(0,a.Lk)("span",ce,(0,l.v_)(Le.formatNumber(100*e.value,2))+"%",1)]))),128)),0===ve.comparisonResult.rankings.stability.length?((0,a.uX)(),(0,a.CE)("div",ue,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ve)]),_:1}),s[26]||(s[26]=(0,a.eW)(" 无排名数据 "))])):(0,a.Q3)("",!0)])])]),_:1})]),_:1})]),_:1}),(0,a.bF)(Ne,{class:"metrics-card",shadow:"hover"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",de,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(we)]),_:1}),s[28]||(s[28]=(0,a.Lk)("span",null,"详细指标对比",-1))])]),default:(0,a.k6)(()=>[(0,a.bF)(Ae,{data:Le.metricsTableData,stripe:"",height:"350",border:""},{default:(0,a.k6)(()=>[(0,a.bF)(ze,{prop:"metric",label:"指标",width:"150",fixed:"left"}),((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ve.comparisonResult.task_info,(e,s)=>((0,a.uX)(),(0,a.Wv)(ze,{key:s,label:e.task_name,align:"center"},{default:(0,a.k6)(({row:e})=>[(0,a.Lk)("span",{class:(0,l.C4)([{"best-value":e.bestTaskId==s},"metric-cell"])},(0,l.v_)(e.values[s]),3)]),_:2},1032,["label"]))),128))]),_:1},8,["data"])]),_:1}),(0,a.bF)(Ne,{class:"charts-card",shadow:"hover"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",ke,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Oe)]),_:1}),s[29]||(s[29]=(0,a.Lk)("span",null,"性能趋势对比",-1))])]),default:(0,a.k6)(()=>[(0,a.bF)(De,{gutter:20},{default:(0,a.k6)(()=>[(0,a.bF)(xe,{span:12},{default:(0,a.k6)(()=>[(0,a.Lk)("div",me,null,512)]),_:1}),(0,a.bF)(xe,{span:12},{default:(0,a.k6)(()=>[(0,a.Lk)("div",pe,null,512)]),_:1})]),_:1})]),_:1}),(0,a.bF)(Ne,{class:"summary-card",shadow:"hover"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",he,[(0,a.bF)(Ce,null,{default:(0,a.k6)(()=>[(0,a.bF)(Ve)]),_:1}),s[30]||(s[30]=(0,a.Lk)("span",null,"总结与建议",-1))])]),default:(0,a.k6)(()=>[(0,a.Lk)("div",_e,[ve.comparisonResult.summary?.best_performance_task?((0,a.uX)(),(0,a.Wv)(We,{key:0,title:ve.comparisonResult.summary?.best_performance_task?.task_name+" 表现最佳",type:"success",closable:!1},{default:(0,a.k6)(()=>[(0,a.Lk)("div",ge,[s[31]||(s[31]=(0,a.Lk)("p",null,[(0,a.Lk)("strong",null,"最佳性能指标:")],-1)),(0,a.Lk)("ul",null,[(0,a.Lk)("li",null,"平均TPS: "+(0,l.v_)(Le.formatNumber(ve.comparisonResult.summary.best_performance_task.avg_tps,2)),1),(0,a.Lk)("li",null,"平均响应时间: "+(0,l.v_)(Le.formatNumber(ve.comparisonResult.summary.best_performance_task.avg_response_time,2))+"ms",1)]),s[32]||(s[32]=(0,a.Lk)("p",null,[(0,a.Lk)("strong",null,"优化建议:")],-1)),(0,a.Lk)("ul",null,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ve.comparisonResult.summary.recommendations,e=>((0,a.uX)(),(0,a.CE)("li",{key:e},(0,l.v_)(e),1))),128))])])]),_:1},8,["title"])):((0,a.uX)(),(0,a.Wv)(We,{key:1,title:"无法确定最佳表现任务",type:"info",closable:!1},{default:(0,a.k6)(()=>s[33]||(s[33]=[(0,a.Lk)("div",{class:"summary-details"},[(0,a.Lk)("p",null,"可能的原因:"),(0,a.Lk)("ul",null,[(0,a.Lk)("li",null,"部分任务没有足够的性能数据"),(0,a.Lk)("li",null,"任务性能指标不完整")]),(0,a.Lk)("p",null,"建议:"),(0,a.Lk)("ul",null,[(0,a.Lk)("li",null,"选择有完整性能数据的任务进行对比"),(0,a.Lk)("li",null,"确保任务已经执行完成并生成了性能报告")])],-1)])),_:1,__:[33]}))])]),_:1})])):(0,a.Q3)("",!0),ve.loading?((0,a.uX)(),(0,a.CE)("div",fe,[(0,a.bF)(Ce,{class:"is-loading"},{default:(0,a.k6)(()=>[(0,a.bF)(Me)]),_:1}),s[34]||(s[34]=(0,a.Lk)("p",null,"正在分析对比数据...",-1))])):(0,a.Q3)("",!0)])}t(44114),t(18111),t(22489),t(7588),t(61701),t(18237),t(13579),t(17642),t(58004),t(33853),t(45876),t(32475),t(15024),t(31698);var ve=t(51219),Le=t(60782),ye=t(91006),Fe=t(57477),Re={name:"TaskComparison",components:{Search:Fe.Search,DataAnalysis:Fe.DataAnalysis,View:Fe.View,Download:Fe.Download,Delete:Fe.Delete,RefreshRight:Fe.RefreshRight,Setting:Fe.Setting,Calendar:Fe.Calendar,Check:Fe.Check,Loading:Fe.Loading,TrendCharts:Fe.TrendCharts,PieChart:Fe.PieChart,InfoFilled:Fe.InfoFilled,Back:Fe.Back},data(){return{loading:!1,searchKeyword:"",statusFilter:"",timeRange:[],reportList:[],selectedTasks:[],comparisonResult:null,currentPage:1,pageSize:12}},computed:{...(0,Le.aH)({pro:e=>e.pro}),filteredReports(){let e=this.reportList;return this.searchKeyword&&(e=e.filter(e=>e.reportName?.toLowerCase().includes(this.searchKeyword.toLowerCase())||e.task?.taskName?.toLowerCase().includes(this.searchKeyword.toLowerCase()))),this.statusFilter&&(e=e.filter(e=>e.reportStatus===this.statusFilter)),e},metricsTableData(){if(!this.comparisonResult?.comparison_metrics)return[];const e=[{key:"averages.tps",label:"平均TPS",format:e=>e},{key:"averages.response_time",label:"平均响应时间(ms)",format:e=>e+"ms"},{key:"averages.error_rate",label:"错误率(%)",format:e=>e+"%"},{key:"extremes.max_tps",label:"最大TPS",format:e=>e},{key:"extremes.min_tps",label:"最小TPS",format:e=>e},{key:"totals.total_requests",label:"总请求数",format:e=>e},{key:"stability.tps_stability",label:"TPS稳定性",format:e=>(100*e).toFixed(1)+"%"},{key:"stability.response_time_stability",label:"响应时间稳定性",format:e=>(100*e).toFixed(1)+"%"}];return e.map(e=>{const s={};let t=null,a=null;const l=Object.keys(this.comparisonResult.task_info);return l.forEach(l=>{if(!this.comparisonResult.comparison_metrics[l])return void(s[l]="-");const n=this.getNestedValue(this.comparisonResult.comparison_metrics[l],e.key);if(s[l]=e.format(n||0),null!==n&&void 0!==n)if(null===t)t=n,a=l;else{const s=e.key.includes("response_time")||e.key.includes("error_rate")?n<t:n>t;s&&(t=n,a=l)}}),{metric:e.label,values:s,bestTaskId:a}})}},async mounted(){await this.loadReports()},beforeUnmount(){window.removeEventListener("resize",this.handleResize),this.$refs.tpsChart&&ye.FP(this.$refs.tpsChart)?.dispose(),this.$refs.responseTimeChart&&ye.FP(this.$refs.responseTimeChart)?.dispose()},methods:{async loadReports(){this.loading=!0;try{const e={project_id:this.pro.id,page_size:50},s=await this.$api.getTaskReports(e);this.reportList=s.data.result||s.data.data||[]}catch(e){ve.nk.error("加载报告列表失败: "+(e.message||"未知错误"))}finally{this.loading=!1}},handleSearch(){},isSelected(e){return this.selectedTasks.some(s=>s.id===e.id)},canSelect(e){if(this.selectedTasks.length>=5)return!1;const s=this.getTaskId(e);return!!s&&!this.selectedTasks.some(e=>this.getTaskId(e)===s)},getTaskId(e){return e.task?.id||e.taskId||e.task_id},toggleSelection(e){(this.canSelect(e)||this.isSelected(e))&&(this.isSelected(e)?this.selectedTasks=this.selectedTasks.filter(s=>s.id!==e.id):this.selectedTasks.push(e))},async performComparison(){if(this.selectedTasks.length<2)ve.nk.warning("请至少选择2个任务进行对比");else{this.loading=!0;try{const e=this.selectedTasks.map(e=>this.getTaskId(e)),s={task_ids:[...new Set(e)],time_range_hours:72};console.log("对比参数:",s);const t=await this.$api.compareTaskPerformance(s);this.comparisonResult=t.data.comparison_report,console.log("对比结果完整数据:",JSON.stringify(this.comparisonResult)),console.log("任务信息:",this.comparisonResult.task_info),console.log("对比指标:",this.comparisonResult.comparison_metrics),console.log("排名数据:",this.comparisonResult.rankings),console.log("时间线数据:",this.comparisonResult.timeline_data);const a=[];Object.keys(this.comparisonResult.task_info).forEach(e=>{this.comparisonResult.comparison_metrics[e]||a.push(this.comparisonResult.task_info[e].task_name)}),a.length>0&&ve.nk.warning(`以下任务没有足够的性能数据: ${a.join(", ")}`),this.$nextTick(()=>{this.renderCharts()}),ve.nk.success("对比分析完成")}catch(e){ve.nk.error("对比分析失败: "+(e.message||"未知错误")),console.error("对比分析错误:",e)}finally{this.loading=!1}}},renderCharts(){this.renderTpsChart(),this.renderResponseTimeChart(),window.addEventListener("resize",this.handleResize)},handleResize(){this.$refs.tpsChart&&this.$refs.responseTimeChart&&(ye.FP(this.$refs.tpsChart)?.resize(),ye.FP(this.$refs.responseTimeChart)?.resize())},renderTpsChart(){if(!this.$refs.tpsChart)return void console.warn("TPS图表DOM元素不存在");const e=ye.Ts(this.$refs.tpsChart),s={title:{text:"TPS对比",left:"center"},tooltip:{trigger:"axis"},legend:{bottom:0},xAxis:{type:"category",data:[]},yAxis:{type:"value",name:"TPS"},series:[],grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0}};let t=!1,a=[];if(this.comparisonResult?.timeline_data){console.log("渲染TPS图表，时间线数据:",this.comparisonResult.timeline_data);const e=Object.keys(this.comparisonResult.task_info);console.log("所有任务ID:",e),e.forEach(e=>{if(!this.comparisonResult.timeline_data[e])return void console.log(`任务 ${e} 没有时间线数据`);const s=this.comparisonResult.timeline_data[e];s&&s.length>0&&(console.log(`任务 ${e} 有 ${s.length} 个时间点`),0===a.length&&(a=s.map(e=>e.time)))}),a.length>0&&(s.xAxis.data=a.map(e=>new Date(e).toLocaleTimeString())),e.forEach(e=>{const a=this.comparisonResult.task_info[e]?.task_name||`任务${e}`;if(!this.comparisonResult.timeline_data[e])return void s.series.push({name:a,type:"line",data:[],smooth:!0,showSymbol:!1,lineStyle:{width:3}});const l=this.comparisonResult.timeline_data[e];l&&l.length>0?(t=!0,s.series.push({name:a,type:"line",data:l.map(e=>e.tps),smooth:!0,showSymbol:!1,lineStyle:{width:3}})):s.series.push({name:a,type:"line",data:[],smooth:!0,showSymbol:!1,lineStyle:{width:3}})})}t||(s.title.text="TPS对比 (无数据)"),console.log("TPS图表配置:",s),e.setOption(s)},renderResponseTimeChart(){if(!this.$refs.responseTimeChart)return void console.warn("响应时间图表DOM元素不存在");const e=ye.Ts(this.$refs.responseTimeChart),s={title:{text:"响应时间对比",left:"center"},tooltip:{trigger:"axis"},legend:{bottom:0},xAxis:{type:"category",data:[]},yAxis:{type:"value",name:"响应时间(ms)"},series:[],grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0}};let t=!1,a=[];if(this.comparisonResult?.timeline_data){console.log("渲染响应时间图表，时间线数据:",this.comparisonResult.timeline_data);const e=Object.keys(this.comparisonResult.task_info);e.forEach(e=>{if(!this.comparisonResult.timeline_data[e])return void console.log(`任务 ${e} 没有时间线数据`);const s=this.comparisonResult.timeline_data[e];s&&s.length>0&&(console.log(`任务 ${e} 有 ${s.length} 个时间点`),0===a.length&&(a=s.map(e=>e.time)))}),a.length>0&&(s.xAxis.data=a.map(e=>new Date(e).toLocaleTimeString())),e.forEach(e=>{const a=this.comparisonResult.task_info[e]?.task_name||`任务${e}`;if(!this.comparisonResult.timeline_data[e])return void s.series.push({name:a,type:"line",data:[],smooth:!0,showSymbol:!1,lineStyle:{width:3}});const l=this.comparisonResult.timeline_data[e];l&&l.length>0?(t=!0,s.series.push({name:a,type:"line",data:l.map(e=>e.response_time),smooth:!0,showSymbol:!1,lineStyle:{width:3}})):s.series.push({name:a,type:"line",data:[],smooth:!0,showSymbol:!1,lineStyle:{width:3}})})}t||(s.title.text="响应时间对比 (无数据)"),console.log("响应时间图表配置:",s),e.setOption(s)},resetComparison(){this.comparisonResult=null,this.selectedTasks=[]},exportReport(){ve.nk.info("导出功能开发中...")},returnToReportList(){window.history.back()},getStatusType(e){const s={0:"success",1:"warning",99:"danger"};return s[e]||"info"},getStatusText(e){const s={0:"已完成",1:"执行中",99:"运行失败"};return s[e]||"未知"},getSuccessRate(e){return e.totalRequests&&0!==e.totalRequests?((e.successRequests||0)/e.totalRequests*100).toFixed(1):0},formatDate(e){return e?new Date(e).toLocaleString():"-"},formatNumber(e,s=2){return null===e||void 0===e?"-":e.toFixed(s)},getNestedValue(e,s){return s.split(".").reduce((e,s)=>e?.[s],e)},hasMetricsData(e){return void 0!==this.comparisonResult?.comparison_metrics?.[e]?.averages}}},Ce=t(71241);const Te=(0,Ce.A)(Re,[["render",be],["__scopeId","data-v-265f2092"]]);var we=Te}}]);
//# sourceMappingURL=233.591c523d.js.map