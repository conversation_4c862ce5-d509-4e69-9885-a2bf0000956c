{"version": 3, "file": "js/491.19e3847b.js", "mappings": "sMASaA,MAAM,e,GAMNA,MAAM,kB,GACJA,MAAM,gB,GAkBNA,MAAM,Y,GACHC,MAAA,0G,GAmCFC,KAAK,SAASF,MAAM,iB,GAMvBA,MAAM,mB,GAEJA,MAAM,kB,GAUFA,MAAM,sB,iBAOJA,MAAM,kB,GAQNA,MAAM,gB,2BAyBhBC,MAAA,uB,aAUSA,MAAA,wC,GAcVC,KAAK,SAASF,MAAM,iB,4lBAvJ5BG,EAAAA,EAAAA,IAsHYC,EAAA,C,WAtHQC,EAAAC,U,qCAAAD,EAAAC,UAASC,GAAEC,MAAM,OAAOC,MAAM,MAAO,eAAcC,EAAAC,WAAYC,IAAI,K,kBACrF,IAoHS,EApHTT,EAAAA,EAAAA,IAoHSU,EAAA,CApHAC,OAAQ,GAAId,MAAM,gB,kBAEzB,IAES,EAFTG,EAAAA,EAAAA,IAESY,EAAA,CAFAC,GAAI,GAAKC,GAAI,EAAIC,GAAI,EAAIC,GAAI,EAAIC,GAAI,EAAGpB,MAAM,c,kBACrD,IAAqF,EAArFG,EAAAA,EAAAA,IAAqFkB,EAAA,CAA1EC,YAAWZ,EAAAa,gBAAkBA,gBAAiBb,EAAAa,iB,mDAG3DpB,EAAAA,EAAAA,IA6GSY,EAAA,CA7GAC,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAKC,GAAI,GAAIpB,MAAM,iB,kBACzD,IAKM,EALNwB,EAAAA,EAAAA,IAKM,MALNC,EAKM,EAJJtB,EAAAA,EAAAA,IACWuB,EAAA,CADDzB,MAAA,gF,WAAyFI,EAAAsB,W,qCAAAtB,EAAAsB,WAAUpB,GAAEqB,YAAY,cAAcC,UAAA,I,wBAEzI1B,EAAAA,EAAAA,IAAuG2B,EAAA,CAA5FC,KAAK,UAAWC,QAAOtB,EAAAuB,yBAA0BhC,MAAA,0B,kBAA6B,IAAEiC,EAAA,MAAAA,EAAA,M,QAAF,S,6BACzF/B,EAAAA,EAAAA,IAA6E2B,EAAA,CAAjEE,QAAKE,EAAA,KAAAA,EAAA,GAAA3B,GAAEF,EAAAsB,WAAU,IAAK1B,MAAA,0B,kBAA6B,IAAEiC,EAAA,MAAAA,EAAA,M,QAAF,S,iBAEjEV,EAAAA,EAAAA,IA4CM,MA5CNW,EA4CM,EA3CJX,EAAAA,EAAAA,IAiBM,MAjBNY,EAiBM,EAhBJjC,EAAAA,EAAAA,IAOY2B,EAAA,CANVC,KAAK,UACJC,QAAOtB,EAAA2B,cACRpC,MAAA,gD,kBAEA,IAAqD,EAArDE,EAAAA,EAAAA,IAAqDmC,EAAA,CAA5CrC,MAAA,wBAAyB,C,iBAAC,IAAQ,EAARE,EAAAA,EAAAA,IAAQoC,K,eAAU,KACrDC,EAAAA,EAAAA,IAAE9B,EAAA+B,YAAU,K,qBAEdtC,EAAAA,EAAAA,IAOY2B,EAAA,CANVC,KAAK,UACJC,QAAKE,EAAA,KAAAA,EAAA,GAAA3B,GAAEF,EAAAqC,eAAgB,GACxBzC,MAAA,gD,kBAEA,IAAqD,EAArDE,EAAAA,EAAAA,IAAqDmC,EAAA,CAA5CrC,MAAA,wBAAyB,C,iBAAC,IAAQ,EAARE,EAAAA,EAAAA,IAAQwC,K,6BAAU,a,iBAIzDnB,EAAAA,EAAAA,IAwBM,MAxBNoB,EAwBM,EAvBJpB,EAAAA,EAAAA,IAMO,OANPqB,EAMO,C,uBANwG,YAC3G1C,EAAAA,EAAAA,IAI4C2B,EAAA,CAH1CC,KAAK,OACLe,SAAA,GACAC,MAAA,I,kBACC,IAA6B,E,iBAA1B1C,EAAA2C,yBAAuB,K,SAEjC7C,EAAAA,EAAAA,IAOY2B,EAAA,CANVC,KAAK,UACL9B,MAAA,+CACC+B,QAAOtB,EAAAC,Y,kBAER,IAAsD,EAAtDR,EAAAA,EAAAA,IAAsDmC,EAAA,CAA7CrC,MAAA,wBAAyB,C,iBAAC,IAAS,EAATE,EAAAA,EAAAA,IAAS8C,K,6BAAU,a,6BAGxD9C,EAAAA,EAAAA,IAOY2B,EAAA,CANVC,KAAK,UACL9B,MAAA,yBACC+B,QAAOtB,EAAAwC,gB,kBAER,IAAsD,EAAtD/C,EAAAA,EAAAA,IAAsDmC,EAAA,CAA7CrC,MAAA,wBAAyB,C,iBAAC,IAAS,EAATE,EAAAA,EAAAA,IAASgD,K,6BAAU,a,iCAK5DhD,EAAAA,EAAAA,IAeYC,EAAA,C,WAfQC,EAAAqC,c,qCAAArC,EAAAqC,cAAanC,GAAEE,MAAM,MAAMD,MAAM,Q,CASxC4C,QAAMC,EAAAA,EAAAA,IACjB,IAGO,EAHP7B,EAAAA,EAAAA,IAGO,OAHP8B,EAGO,EAFLnD,EAAAA,EAAAA,IAAwD2B,EAAA,CAA5CE,QAAKE,EAAA,KAAAA,EAAA,GAAA3B,GAAEF,EAAAqC,eAAgB,I,kBAAO,IAAER,EAAA,MAAAA,EAAA,M,QAAF,S,eAC1C/B,EAAAA,EAAAA,IAAkE2B,EAAA,CAAvDC,KAAK,UAAWC,QAAOtB,EAAA6C,kB,kBAAkB,IAAErB,EAAA,MAAAA,EAAA,M,QAAF,S,iDAXtD,IAOU,EAPV/B,EAAAA,EAAAA,IAOUqD,EAAA,CAPAC,MAAOC,EAAAC,eAAgBC,IAAI,gB,kBACnC,IAKe,EALfzD,EAAAA,EAAAA,IAKe0D,EAAA,CALDC,MAAM,OAAOC,KAAK,O,kBAC9B,IAGY,EAHZ5D,EAAAA,EAAAA,IAGY6D,EAAA,C,WAHa3D,EAAA4D,oB,qCAAA5D,EAAA4D,oBAAmB1D,G,eAAjC,CAAA2D,MAAA,GAAmCtC,YAAY,QAAQ3B,MAAA,cAAoB,eAAa,Q,kBACtF,IAAwB,G,aAAnCkE,EAAAA,EAAAA,IACYC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IADcX,EAAAY,SAARC,K,WAAlBC,EAAAA,EAAAA,IACYC,EAAA,CADyBC,IAAKH,EAAKI,GAAKb,MAAOS,EAAKK,KAAOC,MAAON,EAAKI,I,iHAY3FnD,EAAAA,EAAAA,IAA+D,MAA/DsD,EAA6B,WAAOtC,EAAAA,EAAAA,IAAEnC,EAAA0E,gBAAgB,MAAG,IACzD5E,EAAAA,EAAAA,IAuCe6E,EAAA,CAvCDhF,MAAM,uBAAqB,C,iBACvC,IAqCM,EArCNwB,EAAAA,EAAAA,IAqCM,MArCNyD,EAqCM,G,aApCJd,EAAAA,EAAAA,IAmCMC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAlCWhE,EAAA6E,UAARX,K,WADTJ,EAAAA,EAAAA,IAmCM,OAjCHO,IAAKH,EAAKI,GACX3E,OAAKmF,EAAAA,EAAAA,IAAA,CAAC,iBAAgB,C,2BACgC9E,EAAA+E,gBAAgBC,gBAAgBC,SAASf,EAAKI,K,UAAgCJ,EAAKgB,OAAOC,oB,EAKhJhE,EAAAA,EAAAA,IAKM,MALNiE,EAKM,EAJJtF,EAAAA,EAAAA,IAGeuF,EAAA,CAFZb,MAAOxE,EAAA+E,gBAAgBC,gBAAgBC,SAASf,EAAKI,IACrDgB,SAAMpF,GAAEG,EAAAkF,mBAAmBrB,I,gCAGhC/C,EAAAA,EAAAA,IAmBM,OAnBDxB,MAAM,oBAAqBgC,QAAKzB,GAAEG,EAAAmF,UAAUtB,EAAKI,K,EACpDnD,EAAAA,EAAAA,IAOM,MAPNsE,EAOM,CAN0B,SAAhBvB,EAAKgB,S,WAAnBf,EAAAA,EAAAA,IAAgFuB,EAAA,C,MAA1CC,MAAM,W,kBAAU,IAAiB,E,iBAAdzB,EAAKgB,QAAM,K,4BACtC,QAAhBhB,EAAKgB,S,WAAnBf,EAAAA,EAAAA,IAA+EuB,EAAA,C,MAA1CC,MAAM,W,kBAAU,IAAiB,E,iBAAdzB,EAAKgB,QAAM,K,4BACrC,QAAhBhB,EAAKgB,S,WAAnBf,EAAAA,EAAAA,IAA+EuB,EAAA,C,MAA1CC,MAAM,W,kBAAU,IAAiB,E,iBAAdzB,EAAKgB,QAAM,K,4BACrC,UAAhBhB,EAAKgB,S,WAAnBf,EAAAA,EAAAA,IAAiFuB,EAAA,C,MAA1CC,MAAM,W,kBAAU,IAAiB,E,iBAAdzB,EAAKgB,QAAM,K,4BACvC,WAAhBhB,EAAKgB,S,WAAnBf,EAAAA,EAAAA,IAAkFuB,EAAA,C,MAA1CC,MAAM,W,kBAAU,IAAiB,E,iBAAdzB,EAAKgB,QAAM,K,4BACxC,SAAhBhB,EAAKgB,S,WAAnBf,EAAAA,EAAAA,IAA2FuB,EAAA,C,MAArDC,MAAM,sB,kBAAqB,IAAiB,E,iBAAdzB,EAAKgB,QAAM,K,+BAEjF/D,EAAAA,EAAAA,IAGM,MAHNyE,EAGM,EAFJzE,EAAAA,EAAAA,IAAiE,OAA5DxB,MAAM,gBAAiBQ,MAAO+D,EAAK2B,M,QAAQ3B,EAAK2B,KAAG,EAAAC,IACxD3E,EAAAA,EAAAA,IAAoE,OAA/DxB,MAAM,iBAAkBQ,MAAO+D,EAAKK,O,QAASL,EAAKK,MAAI,EAAAwB,MAE7D5E,EAAAA,EAAAA,IAKM,OALDxB,MAAM,iBAAkBgC,QAAKE,EAAA,KAAAA,EAAA,IAAAmE,EAAAA,EAAAA,IAAN,OAAW,Y,EACrClG,EAAAA,EAAAA,IAAqE2B,EAAA,CAA1DC,KAAK,OAAQC,QAAKzB,GAAEG,EAAA4F,cAAc/B,EAAKI,K,kBAAK,IAAEzC,EAAA,MAAAA,EAAA,M,QAAF,S,gCACvD/B,EAAAA,EAAAA,IAAiE2B,EAAA,CAAtDC,KAAK,OAAQC,QAAKzB,GAAEG,EAAAmF,UAAUtB,EAAKI,K,kBAAK,IAAEzC,EAAA,MAAAA,EAAA,M,QAAF,S,gCACnD/B,EAAAA,EAAAA,IAAgE2B,EAAA,CAArDC,KAAK,OAAQC,QAAKzB,GAAEG,EAAA6F,SAAShC,EAAKI,K,kBAAK,IAAEzC,EAAA,MAAAA,EAAA,M,QAAF,S,gCAClD/B,EAAAA,EAAAA,IAAyD2B,EAAA,CAA9CC,KAAK,OAAQC,QAAOtB,EAAA8F,U,kBAAU,IAAItE,EAAA,MAAAA,EAAA,M,QAAJ,W,mHAWzD/B,EAAAA,EAAAA,IAA2QsG,EAAA,C,WAAvPpG,EAAAqG,Y,qCAAArG,EAAAqG,YAAWnG,GAAG,oBAAkB,EAAO,eAAa,EAAOoG,KAAK,MAAOC,QAAOlG,EAAAmG,a,kBAAa,IAAgJ,EAAhJ1G,EAAAA,EAAAA,IAAgJ2G,EAAA,CAAnIlD,IAAI,WAAYmD,cAAarG,EAAAmG,YAAeG,aAAc3G,EAAA2G,aAAeC,QAAS5G,EAAA4G,QAAUhH,MAAA,oB,uFAGxNE,EAAAA,EAAAA,IAoBYsG,EAAA,C,WApBQpG,EAAA6G,O,qCAAA7G,EAAA6G,OAAM3G,GAAG,eAAa,EAAOoG,KAAK,O,kBACpD,IAkBU,EAlBVxG,EAAAA,EAAAA,IAkBUgH,EAAA,M,iBAjBR,IAAa,C,eAAb3F,EAAAA,EAAAA,IAAa,SAAV,UAAM,KACTA,EAAAA,EAAAA,IAeM,MAfN4F,EAeM,EAdJjH,EAAAA,EAAAA,IAackH,EAAA,M,iBAZM,IAAoC,G,aAAtDlD,EAAAA,EAAAA,IAWmBC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAX2BhE,EAAAiH,QAAO,CAA3BC,EAAUC,M,WAApChD,EAAAA,EAAAA,IAWmBiD,EAAA,CAVD/C,IAAK8C,EACLE,UAAWhE,EAAAiE,OAAOC,MAAML,EAASM,aAClCC,UAAU,MACV9B,MAAM,W,kBACrB,IAKU,EALV7F,EAAAA,EAAAA,IAKUgH,EAAA,M,iBAJR,IAA8B,EAA9B3F,EAAAA,EAAAA,IAA8B,WAAAgB,EAAAA,EAAAA,IAAvB+E,EAASQ,QAAM,GACbR,EAASS,S,WAAlB7D,EAAAA,EAAAA,IAAwD,IAAA8D,EAA9B,SAAKzF,EAAAA,EAAAA,IAAG+E,EAASS,QAAM,K,gBACjDxG,EAAAA,EAAAA,IAAgF,OAAhF0G,GAAgF1F,EAAAA,EAAAA,IAA9B+E,EAASY,aAAW,IACtE3G,EAAAA,EAAAA,IAAyD,YAAnD,QAAIgB,EAAAA,EAAAA,IAAGkB,EAAAiE,OAAOS,MAAMb,EAASM,cAAW,K,kGAS1D1H,EAAAA,EAAAA,IASYC,EAAA,C,WATQC,EAAAgI,U,uCAAAhI,EAAAgI,UAAS9H,GAAEE,MAAM,MAAMD,MAAM,Q,CAGpC4C,QAAMC,EAAAA,EAAAA,IACjB,IAGO,EAHP7B,EAAAA,EAAAA,IAGO,OAHP8G,EAGO,EAFLnI,EAAAA,EAAAA,IAAoD2B,EAAA,CAAxCE,QAAKE,EAAA,MAAAA,EAAA,IAAA3B,GAAEF,EAAAgI,WAAY,I,kBAAO,IAAEnG,EAAA,MAAAA,EAAA,M,QAAF,S,eACtC/B,EAAAA,EAAAA,IAAmE2B,EAAA,CAAxDC,KAAK,UAAWC,QAAO0B,EAAA6E,mB,kBAAmB,IAAErG,EAAA,MAAAA,EAAA,M,QAAF,S,iDALvD,IACU,EADV/B,EAAAA,EAAAA,IACUqD,EAAA,CADAC,MAAOC,EAAAC,eAAgBC,IAAI,gB,0JAkBzC,GACE4E,MAAO,CACLC,WAAY,CACV1G,KAAM2G,SAGVC,WAAY,CACVC,SAAQ,IACRC,YAAW,IACXC,MAAK,QACLC,MAAK,QACLC,KAAI,OACJC,KAAIA,EAAAA,MAENC,SAAU,CACRzG,UAAAA,GACE,OAAO0G,KAAKC,aAAe,WAAa,QAC1C,MACGC,EAAAA,EAAAA,IAAS,CAAC,MAAM,WAAW,UAC9BC,QAAAA,GACD,OAAOC,OAAOC,eAAeC,QAAQ,WACtC,EACEC,IAAK,CACNC,GAAAA,GACC,OAAOR,KAAKS,KACb,EACAC,GAAAA,CAAIC,GACHX,KAAKY,UAAUD,EAChB,IAGDE,IAAAA,GACE,MAAO,CACL1J,WAAU,EACV2J,OAAQ,GACRtI,WAAW,GACXuD,UAAW,GACXwB,aAAa,EACbQ,QAAO,EACPmB,WAAU,EACVrB,aAAc,GACdC,SAAS,EACTmC,cAAc,EACdc,eAAe,GACfxH,eAAe,EACfuB,oBAAqB,GACrBjB,wBAAyB,OACzBoC,gBAAiB,CACfC,gBAAiB,GACjB8E,gBAAiBhB,KAAKiB,uBAExB9C,QAAS,CACP,CACEO,YAAa,sBACbE,OAAQ,WACRC,OAAQ,aACRG,YAAa,MAEf,CACEN,YAAa,sBACbE,OAAQ,WACRC,OAAQ,mBACRG,YAAa,MAEf,CACEN,YAAa,sBACbE,OAAQ,aACRI,YAAa,OAGjBpD,eAAe,EAEnB,EACAsF,QAAS,KACJC,EAAAA,EAAAA,IAAa,CAAC,cACjBC,UAAAA,GACEpB,KAAKqB,MAAM,cACb,EAEA7J,UAAAA,GACEwI,KAAKoB,YACP,EAGA3E,kBAAAA,CAAmBrB,GACb4E,KAAKV,WACLU,KAAK/D,gBAAgBC,gBAAgBoF,KAAKlG,GAE5C4E,KAAK/D,gBAAgBC,gBAAgBoF,KAAKlG,EAAKI,GAEnD,EAGAyF,qBAAAA,CAAsBM,GAChBvB,KAAKV,WACPU,KAAK/D,gBAAgBC,gBAAkBqF,EAASC,IAAIpG,GAAQA,GAG5D4E,KAAK/D,gBAAgBC,gBAAkBqF,EAASC,IAAIpG,GAAQA,EAAKI,GAErE,EAGAiG,eAAAA,CAAgBC,GACd,OAAQA,GACN,IAAK,MACH,MAAO,iCACT,IAAK,OACH,MAAO,iCACT,IAAK,MACH,MAAO,iCACT,IAAK,SACH,MAAO,iCACT,IAAK,QACH,MAAO,iCACT,QACE,MAAO,GAEb,EAGA,qBAAMtJ,CAAgBoD,EAAGC,EAAKkG,GAC5B,GAAGlG,EAAM,CACP,MAAMmG,QAAiB5B,KAAK6B,KAAKC,iBAAiBtG,EAAGC,GAC7B,MAApBmG,EAASG,SACX/B,KAAKc,OAAStF,EACdwE,KAAKjE,UAAY6F,EAASf,KAC1Bb,KAAKpE,eAAiBgG,EAASf,KAAKmB,OAExC,MAAO,GAAGL,EAAQ,CAChB,MAAMC,QAAiB5B,KAAK6B,KAAKC,iBAAiBtG,EAAGC,EAAKkG,GAClC,MAApBC,EAASG,SACX/B,KAAKc,OAAStF,EACdwE,KAAKjE,UAAY6F,EAASf,KAC1Bb,KAAKpE,eAAiBgG,EAASf,KAAKmB,OAExC,KAAO,CACL,MAAMJ,QAAiB5B,KAAK6B,KAAKC,iBAAiBtG,GAC1B,MAApBoG,EAASG,SACX/B,KAAKc,OAAStF,EACdwE,KAAKjE,UAAY6F,EAASf,KAC1Bb,KAAKpE,eAAiBgG,EAASf,KAAKmB,OAExC,CACF,EAGA,kBAAMC,CAAazG,GACjB,MAAMoG,QAAiB5B,KAAK6B,KAAKK,gBAAgB1G,GAC5B,MAApBoG,EAASG,UACRI,EAAAA,EAAAA,IAAU,CACRvJ,KAAM,UACNwJ,QAAS,OACTC,SAAU,MAEZrC,KAAK5H,gBAAgB4H,KAAKc,QAC1Bd,KAAKxH,WAAa,GAClBwH,KAAK/D,gBAAgBC,gBAAkB,GAE3C,EAGAnC,cAAAA,GACE,GAAoD,IAAhDiG,KAAK/D,gBAAgBC,gBAAgB8F,OAMvC,YALAG,EAAAA,EAAAA,IAAU,CACRvJ,KAAM,UACNwJ,QAAS,aACTC,SAAU,MAId,MAAMC,EAAO,IAAItC,KAAK/D,gBAAgBC,iBACtC8D,KAAKqB,MAAM,aAAciB,GACzBtC,KAAKxH,WAAa,GAClBwH,KAAK/D,gBAAgBC,gBAAkB,GACvC8D,KAAKxI,YACP,EAGAsB,wBAAAA,GACEkH,KAAK5H,gBAAgB4H,KAAKc,OAAOd,KAAKxH,WACxC,EAGA4E,QAAAA,CAAS5B,GACV+G,EAAAA,EAAaC,QAAQ,aAAc,KAAM,CACxCC,kBAAmB,KACnBC,iBAAkB,KAClB9J,KAAM,YAEL+J,KAAK,KACL3C,KAAKiC,aAAazG,KAElBoH,MAAM,MACNT,EAAAA,EAAAA,IAAU,CACTvJ,KAAM,OACNwJ,QAAS,OACTC,SAAU,OAGd,EAEE3E,WAAAA,GACEsC,KAAKzC,aAAc,EACnByC,KAAKlC,SAAU,EACfkC,KAAK5H,gBAAgB4H,KAAKc,OAC5B,EAEA3D,aAAAA,CAAc3B,GACZwE,KAAKnC,aAAerC,EACpBwE,KAAKzC,aAAc,EACnByC,KAAK6C,UAAU,KACb7C,KAAK8C,MAAMC,SAASC,iBAAiBhD,KAAKnC,eAE9C,EAGFnB,SAAAA,CAAUlB,GACNwE,KAAKlC,SAAU,EACfkC,KAAK7C,cAAc3B,EACrB,EAGF6B,QAAAA,GACI2C,KAAKjC,QAAS,CAChB,EAGA7E,aAAAA,GACE8G,KAAKC,cAAgBD,KAAKC,aACtBD,KAAKC,aAEPD,KAAK5H,gBAAgB4H,KAAKc,OAAO,GAAGd,KAAKG,UAGzCH,KAAK5H,gBAAgB4H,KAAKc,OAE9B,EAEA1G,gBAAAA,GACE4F,KAAKO,IAAMP,KAAKlF,oBAChBkF,KAAKnG,wBAA0BmG,KAAK7E,SAAS8H,KAAK1C,GAAOA,EAAI/E,KAAOwE,KAAKlF,qBAAqBW,KAC9FuE,KAAKzG,eAAgB,CACvB,I,WCnZJ,MAAM2J,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://frontend-web/./src/views/TestCase/apiCiteDlg.vue", "webpack://frontend-web/./src/views/TestCase/apiCiteDlg.vue?3c15"], "sourcesContent": ["<template>\n  <el-dialog v-model=\"addApiDlg\" title=\"引用接口\" width=\"88%\" :before-close=\"clickClear\" top=\"0\">\n    <el-row :gutter=\"10\" class=\"main-content\">\n      <!-- 左边内容 -->\n      <el-col :xs=\"24\" :sm=\"8\" :md=\"6\" :lg=\"6\" :xl=\"5\" class=\"left-panel\">\n        <treeNode @treeClick=\"handleTreeClick\" :handleTreeClick=\"handleTreeClick\"></treeNode>\n      </el-col>\n      <!-- 右边内容 -->\n      <el-col :xs=\"24\" :sm=\"16\" :md=\"18\" :lg=\"18\" :xl=\"19\" class=\"right-content\">\n        <div class=\"search-area\">\n          <el-input style=\"width: 100%; max-width: 300px; margin-right: 10px; margin-bottom: 10px;\" v-model=\"filterText\" placeholder=\"请输入接口名称进行搜索\" clearable>\n          </el-input>\n          <el-button type=\"primary\" @click=\"handlenewInterfacesClick\" style=\"margin-bottom: 10px;\">查询</el-button>\n          <el-button @click=\"filterText=''\" style=\"margin-bottom: 10px;\">重置</el-button>\n        </div>\n        <div class=\"action-buttons\">\n          <div class=\"button-group\">\n            <el-button\n              type=\"primary\"\n              @click=\"userInterface\"\n              style=\"margin-right: 10px; margin-bottom: 10px;\"\n                >\n              <el-icon style=\"margin-right: 6px\"><View /></el-icon>\n              {{buttonText}}\n            </el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"dialogVisible = true\"\n              style=\"margin-right: 10px; margin-bottom: 10px;\"\n                >\n              <el-icon style=\"margin-right: 6px\"><Star /></el-icon>\n              选择环境\n            </el-button>\n          </div>\n          <div class=\"env-info\">\n            <span style=\"font-size: 14px; color: #909399; margin-right: 10px; margin-bottom: 10px; display: inline-block;\">当前环境：\n                <el-button\n                  type=\"info\"\n                  disabled\n                  plain\n                  >{{ selectedEnvironmentName }}</el-button>\n            </span>\n            <el-button\n              type=\"warning\"\n              style=\"margin-right: 10px; margin-bottom: 10px;\"\n              @click=\"clickClear\"\n                >\n              <el-icon style=\"margin-right: 6px\"><Close /></el-icon>\n              关闭窗口\n            </el-button>\n            <el-button\n              type=\"primary\"\n              style=\"margin-bottom: 10px;\"\n              @click=\"handleApiClick\"\n                >\n              <el-icon style=\"margin-right: 6px\"><Check /></el-icon>\n              确认选择\n            </el-button>\n          </div>\n        </div>\n        <el-dialog v-model=\"dialogVisible\" width=\"30%\" title=\"选择环境\">\n          <el-form :rules=\"rulesinterface\" ref=\"interfaceRef\" >\n            <el-form-item label=\"测试环境\" prop=\"env\">\n              <el-select v-model.lazy=\"selectedEnvironment\" placeholder=\"请选择环境\" style=\"width: 70%;\" no-data-text=\"暂无数据\">\n                <el-option v-for=\"item in testEnvs\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-form>\n          <template #footer>\n          <span slot=\"footer\" class=\"dialog-footer\">\n            <el-button @click=\"dialogVisible = false\">取消</el-button>\n            <el-button type=\"primary\" @click=\"confirmSelection\">确定</el-button>\n          </span>\n          </template>\n        </el-dialog>\n        <div class=\"interface-title\">全部接口共 ({{interfaceCount}}) 个</div>\n        <el-scrollbar class=\"interface-scrollbar\">\n          <div class=\"interface-list\">\n            <div\n              v-for=\"item in tableData\"\n              :key=\"item.id\"\n              class=\"interface-item\"\n              :class=\"[\n                {'interface-item-selected': selectionConfig.selectedRowKeys.includes(item.id)},\n                `method-${item.method.toLowerCase()}`\n              ]\"\n            >\n              <div class=\"interface-checkbox\">\n                <el-checkbox\n                  :value=\"selectionConfig.selectedRowKeys.includes(item.id)\"\n                  @change=\"handleSingleSelect(item)\"\n                ></el-checkbox>\n              </div>\n              <div class=\"interface-content\" @click=\"clickCopy(item.id)\">\n                <div class=\"method-section\">\n                  <el-tag v-if=\"item.method === 'POST'\" color=\"#49cc90\">{{ item.method }}</el-tag>\n                  <el-tag v-if=\"item.method === 'GET'\" color=\"#61affe\">{{ item.method }}</el-tag>\n                  <el-tag v-if=\"item.method === 'PUT'\" color=\"#fca130\">{{ item.method }}</el-tag>\n                  <el-tag v-if=\"item.method === 'PATCH'\" color=\"#50e3c2\">{{ item.method }}</el-tag>\n                  <el-tag v-if=\"item.method === 'DELETE'\" color=\"#f93e3e\">{{ item.method }}</el-tag>\n                  <el-tag v-if=\"item.method === 'DEAD'\" color=\"rgb(201, 233, 104)\">{{ item.method }}</el-tag>\n                </div>\n                <div class=\"info-section\">\n                  <div class=\"interface-url\" :title=\"item.url\">{{ item.url }}</div>\n                  <div class=\"interface-name\" :title=\"item.name\">{{ item.name }}</div>\n                </div>\n                <div class=\"action-section\" @click.stop>\n                  <el-button type=\"text\" @click=\"clickEditStep(item.id)\">调试</el-button>\n                  <el-button type=\"text\" @click=\"clickCopy(item.id)\">复制</el-button>\n                  <el-button type=\"text\" @click=\"clickDel(item.id)\">删除</el-button>\n                  <el-button type=\"text\" @click=\"clickLog\">操作记录</el-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-scrollbar>\n      </el-col>\n    </el-row>\n  </el-dialog>\n\n  <!-- 调试测试步骤窗口 -->\n  <el-drawer v-model=\"editCaseDlg\" :destroy-on-close=\"true\" :with-header=\"false\" size=\"50%\" @close=\"handleClose\"><newEditCase ref=\"childRef\" @closeDrawer=\"handleClose\"  :Interface_id=\"Interface_id\" :copyDlg=\"copyDlg\"  style=\"padding: 0 10px;\"></newEditCase></el-drawer>\n\n  <!--  接口操作记录窗口-->\n  <el-drawer v-model=\"logDlg\" :with-header=\"false\" size=\"50%\">\n    <el-card>\n      <b>接口操作记录</b>\n      <div style=\"margin-top: 10px;\">\n        <el-timeline>\n          <el-timeline-item v-for=\"(activity, index) in bugLogs\"\n                           :key=\"index\"\n                           :timestamp=\"$tools.rDate(activity.create_time)\"\n                           placement=\"top\"\n                           color=\"#0bbd87\">\n            <el-card>\n              <h4>{{ activity.handle }}</h4>\n              <p v-if=\"activity.remark\">变更记录：{{ activity.remark }}</p>\n              <span style=\"color: #409eff;margin-right: 8px\">{{ activity.update_user }}</span>\n              <span>操作于 {{ $tools.rTime(activity.create_time) }}</span>\n            </el-card>\n          </el-timeline-item>\n        </el-timeline>\n      </div>\n    </el-card>\n  </el-drawer>\n\n  <!--  导入接口窗口-->\n  <el-dialog v-model=\"importDlg\" width=\"30%\" title=\"导入接口\">\n    <el-form :rules=\"rulesinterface\" ref=\"interfaceRef\" >\n    </el-form>\n    <template #footer>\n    <span slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"importDlg = false\">取消</el-button>\n      <el-button type=\"primary\" @click=\"confirmSelection1\">导入</el-button>\n    </span>\n    </template>\n  </el-dialog>\n</template>\n\n<script>\nimport treeNode from '../Interface/treeNode.vue';\nimport {ElMessage, ElMessageBox} from \"element-plus\";\nimport newEditCase from '../../components/common/InterfaceNew/neweditCase.vue';\nimport {mapMutations, mapState} from \"vuex\";\nimport { Check, Close, View, Star } from '@element-plus/icons-vue';\n\nexport default {\n  props: {\n    selectType: {\n      type: String\n    }\n  },\n  components: {\n    treeNode,\n    newEditCase,\n    Check,\n    Close,\n    View,\n    Star\n  },\n  computed: {\n    buttonText() {\n      return this.showOnlySelf ? '取消只看自己创建' : '只看自己创建';\n    },\n    ...mapState(['pro','testEnvs','envId']),\n    username() {\n\t\t\treturn window.sessionStorage.getItem('username');\n\t\t},\n    env: {\n\t\t\tget() {\n\t\t\t\treturn this.envId;\n\t\t\t},\n\t\t\tset(val) {\n\t\t\t\tthis.selectEnv(val);\n\t\t\t}\n\t\t}\n    },\n  data() {\n    return {\n      addApiDlg:true,\n      treeId: '',\n      filterText:'',\n      tableData: [],\n      editCaseDlg: false,\n      logDlg:false,\n      importDlg:false,\n      Interface_id: '',\n      copyDlg: false,\n      showOnlySelf: false,\n      selectedOption:'',\n      dialogVisible: false,\n      selectedEnvironment: '',\n      selectedEnvironmentName: '暂未选择',\n      selectionConfig: {\n        selectedRowKeys: [], // 已选中行的 key\n        selectionChange: this.handleSelectionChange // 选择变化时的回调函数\n      },\n      bugLogs: [\n        {\n          create_time: \"2024-02-18T10:30:00\",\n          handle: \"修复了一个bug\",\n          remark: \"这是修复bug的备注\",\n          update_user: \"张三\"\n        },\n        {\n          create_time: \"2024-02-17T14:20:00\",\n          handle: \"重新测试了bug\",\n          remark: \"接口名称登录变更为tms登录接口\",\n          update_user: \"李四\"\n        },\n        {\n          create_time: \"2024-02-16T09:45:00\",\n          handle: \"提交了一个新的bug\",\n          update_user: \"王五\"\n        }\n      ],\n      interfaceCount:0,\n    };\n  },\n  methods: {\n    ...mapMutations(['selectEnv']),\n    closeModal() {\n      this.$emit('close-modal');\n    },\n    // 点击取消\n    clickClear(){\n      this.closeModal()\n    },\n\n    // 处理单个选择\n    handleSingleSelect(item) {\n      if (this.selectType) {\n          this.selectionConfig.selectedRowKeys.push(item);\n      } else {\n        this.selectionConfig.selectedRowKeys.push(item.id);\n      }\n    },\n\n    // 把批量选择完成的数据取出id重新生成数组\n    handleSelectionChange(selected) {\n      if (this.selectType) {\n        this.selectionConfig.selectedRowKeys = selected.map(item => item);\n      }\n      else {\n        this.selectionConfig.selectedRowKeys = selected.map(item => item.id);\n      }\n    },\n\n    // 根据接口类型展示不同的样式\n    getRowClassName(row) {\n      switch (row) {\n        case 'GET':\n          return '--el-card-border-color:#61affe'\n        case 'POST':\n          return '--el-card-border-color:#49cc90'\n        case 'PUT':\n          return '--el-card-border-color:#fca130'\n        case 'DELETE':\n          return '--el-card-border-color:#f93e3e'\n        case 'PATCH':\n          return '--el-card-border-color:#50e3c2'\n        default:\n          return '';\n      }\n    },\n\n    // 根据对应节点展示接口信息\n    async handleTreeClick(id,name,creator) {\n      if(name) {\n        const response = await this.$api.getNewInterfaces(id,name);\n        if (response.status === 200) {\n          this.treeId = id;\n          this.tableData = response.data;\n          this.interfaceCount = response.data.length;\n        }\n      } else if(creator){\n        const response = await this.$api.getNewInterfaces(id,name,creator);\n        if (response.status === 200) {\n          this.treeId = id;\n          this.tableData = response.data;\n          this.interfaceCount = response.data.length;\n        }\n      } else {\n        const response = await this.$api.getNewInterfaces(id);\n        if (response.status === 200) {\n          this.treeId = id;\n          this.tableData = response.data;\n          this.interfaceCount = response.data.length;\n        }\n      }\n    },\n\n    // 单个接口信息删除接口\n    async delInterface(id){\n      const response = await this.$api.delnewInterface(id);\n\t\t\tif (response.status === 204) {\n        ElMessage({\n          type: 'success',\n          message: '删除成功',\n          duration: 1000\n        });\n        this.handleTreeClick(this.treeId);\n        this.filterText = '';\n        this.selectionConfig.selectedRowKeys = [];\n      }\n    },\n\n    // 确定添加\n    handleApiClick(){\n      if (this.selectionConfig.selectedRowKeys.length === 0) {\n        ElMessage({\n          type: 'warning',\n          message: '请勾选数据后再操作！',\n          duration: 2000\n        });\n        return;\n      }\n      const apis = [...this.selectionConfig.selectedRowKeys]\n      this.$emit('childEvent', apis);\n      this.filterText = '';\n      this.selectionConfig.selectedRowKeys = [];\n      this.clickClear()\n    },\n\n    // 点击查询\n    handlenewInterfacesClick() {\n      this.handleTreeClick(this.treeId,this.filterText)\n    },\n\n    // 点击删除\n    clickDel(id) {\n\t\t\tElMessageBox.confirm('确定要删除该接口吗?', '提示', {\n\t\t\t\tconfirmButtonText: '确定',\n\t\t\t\tcancelButtonText: '取消',\n\t\t\t\ttype: 'warning'\n\t\t\t})\n\t\t\t\t.then(() => {\n\t\t\t\t\tthis.delInterface(id);\n\t\t\t\t})\n\t\t\t\t.catch(() => {\n\t\t\t\t\tElMessage({\n\t\t\t\t\t\ttype: 'info',\n\t\t\t\t\t\tmessage: '取消删除',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t},\n\n    handleClose() {\n      this.editCaseDlg = false;\n      this.copyDlg = false;\n      this.handleTreeClick(this.treeId);\n    },\n\n    clickEditStep(id) {\n      this.Interface_id = id\n      this.editCaseDlg = true\n      this.$nextTick(() => {\n        this.$refs.childRef.getInterfaceInfo(this.Interface_id);\n      })\n    },\n\n    // 复制用例\n\t\tclickCopy(id) {\n      this.copyDlg = true;\n      this.clickEditStep(id)\n    },\n\n    // 操作记录\n\t\tclickLog() {\n      this.logDlg = true;\n    },\n\n    // 只看自己创建的接口\n    userInterface() {\n      this.showOnlySelf = !this.showOnlySelf;\n      if (this.showOnlySelf) {\n        // 只看自己创建的逻辑\n        this.handleTreeClick(this.treeId,'',this.username);\n      } else {\n        // 取消只看自己创建的逻辑\n        this.handleTreeClick(this.treeId);\n      }\n    },\n\n    confirmSelection() {\n      this.env = this.selectedEnvironment; // 在确认选择时更新 env 数据\n      this.selectedEnvironmentName = this.testEnvs.find(env => env.id === this.selectedEnvironment).name;\n      this.dialogVisible = false;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.main-content {\n  margin: 0;\n  width: 100%;\n}\n\n.left-panel {\n  padding: 10px;\n  height: 100%;\n}\n\n.right-content {\n  padding: 10px 15px;\n  background-color: #f5f7fa;\n}\n\n.search-area {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  margin-top: 20px;\n}\n\n.action-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  align-items: center;\n  margin: 20px 0;\n}\n\n.button-group {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.env-info {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.interface-title {\n  clear: both;\n  font-weight: 500;\n  margin-top: 10px;\n  margin-bottom: 15px;\n  border-left: 3px solid #2395f1;\n  padding-left: 10px;\n  font-size: 15px;\n  color: #303133;\n}\n\n.interface-scrollbar {\n  height: calc(100vh - 250px);\n  min-height: 400px;\n}\n\n.interface-list {\n  width: 100%;\n}\n\n.interface-item {\n  display: flex;\n  margin-bottom: 12px;\n  background: #fff;\n  border-radius: 6px;\n  border: 1px solid #ebeef5;\n  transition: all 0.3s;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\n  border-left-width: 5px;\n}\n\n.method-get {\n  border-left-color: #61affe;\n}\n\n.method-post {\n  border-left-color: #49cc90;\n}\n\n.method-put {\n  border-left-color: #fca130;\n}\n\n.method-delete {\n  border-left-color: #f93e3e;\n}\n\n.method-patch {\n  border-left-color: #50e3c2;\n}\n\n.method-dead {\n  border-left-color: rgb(201, 233, 104);\n}\n\n.interface-item:hover {\n  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.12);\n  transform: translateY(-2px);\n}\n\n.interface-item-selected {\n  border: 1px solid #409eff;\n  border-left-width: 5px;\n  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);\n}\n\n.interface-checkbox {\n  display: flex;\n  align-items: center;\n  padding: 0 15px;\n}\n\n.interface-content {\n  flex: 1;\n  display: flex;\n  padding: 18px 0;\n  align-items: center;\n  cursor: pointer;\n  flex-wrap: wrap;\n}\n\n.method-section {\n  width: 90px;\n  display: flex;\n  justify-content: center;\n  padding: 0 10px;\n  margin: 5px 0;\n}\n\n.info-section {\n  flex: 1;\n  padding: 0 15px;\n  min-width: 200px;\n  overflow: hidden;\n  margin: 5px 0;\n}\n\n.interface-url {\n  font-weight: bold;\n  font-size: 15px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-bottom: 8px;\n  color: #303133;\n}\n\n.interface-name {\n  font-size: 14px;\n  color: #606266;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.action-section {\n  width: 100%;\n  max-width: 300px;\n  display: flex;\n  flex-wrap: wrap;\n  padding: 0 15px;\n  margin: 5px 0;\n}\n\n.action-section .el-button {\n  margin: 3px 8px;\n  font-size: 13px;\n}\n\n.el-tag {\n  color: #ffffff;\n  width: 70px;\n  height: 30px;\n  text-align: center;\n  font-size: 14px;\n  line-height: 30px;\n}\n\n/* 响应式调整 */\n@media screen and (max-width: 768px) {\n  .interface-content {\n    flex-direction: column;\n    align-items: flex-start;\n    padding: 10px 0;\n  }\n\n  .method-section, .info-section, .action-section {\n    width: 100%;\n    justify-content: flex-start;\n    padding: 5px 15px;\n  }\n\n  .info-section {\n    order: -1;\n  }\n\n  .action-section {\n    max-width: 100%;\n  }\n\n  .interface-scrollbar {\n    height: calc(100vh - 350px);\n  }\n\n  .el-tag {\n    margin-bottom: 5px;\n  }\n}\n</style>\n", "import { render } from \"./apiCiteDlg.vue?vue&type=template&id=0c3c4d2d&scoped=true\"\nimport script from \"./apiCiteDlg.vue?vue&type=script&lang=js\"\nexport * from \"./apiCiteDlg.vue?vue&type=script&lang=js\"\n\nimport \"./apiCiteDlg.vue?vue&type=style&index=0&id=0c3c4d2d&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0c3c4d2d\"]])\n\nexport default __exports__"], "names": ["class", "style", "slot", "_createVNode", "_component_el_dialog", "$data", "addApiDlg", "$event", "title", "width", "$options", "clickClear", "top", "_component_el_row", "gutter", "_component_el_col", "xs", "sm", "md", "lg", "xl", "_component_treeNode", "onTreeClick", "handleTreeClick", "_createElementVNode", "_hoisted_1", "_component_el_input", "filterText", "placeholder", "clearable", "_component_el_button", "type", "onClick", "handlenewInterfacesClick", "_cache", "_hoisted_2", "_hoisted_3", "userInterface", "_component_el_icon", "_component_View", "_toDisplayString", "buttonText", "dialogVisible", "_component_Star", "_hoisted_4", "_hoisted_5", "disabled", "plain", "selectedEnvironmentName", "_component_Close", "handleApiClick", "_component_Check", "footer", "_withCtx", "_hoisted_6", "confirmSelection", "_component_el_form", "rules", "_ctx", "rulesinterface", "ref", "_component_el_form_item", "label", "prop", "_component_el_select", "selectedEnvironment", "lazy", "_createElementBlock", "_Fragment", "_renderList", "testEnvs", "item", "_createBlock", "_component_el_option", "key", "id", "name", "value", "_hoisted_7", "interfaceCount", "_component_el_scrollbar", "_hoisted_8", "tableData", "_normalizeClass", "selectionConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "includes", "method", "toLowerCase", "_hoisted_9", "_component_el_checkbox", "onChange", "handleSingleSelect", "clickCopy", "_hoisted_11", "_component_el_tag", "color", "_hoisted_12", "url", "_hoisted_13", "_hoisted_14", "_withModifiers", "clickEditStep", "clickDel", "clickLog", "_component_el_drawer", "editCaseDlg", "size", "onClose", "handleClose", "_component_newEditCase", "onCloseDrawer", "Interface_id", "copyDlg", "logDlg", "_component_el_card", "_hoisted_15", "_component_el_timeline", "bugLogs", "activity", "index", "_component_el_timeline_item", "timestamp", "$tools", "rDate", "create_time", "placement", "handle", "remark", "_hoisted_16", "_hoisted_17", "update_user", "rTime", "importDlg", "_hoisted_18", "confirmSelection1", "props", "selectType", "String", "components", "treeNode", "newEditCase", "Check", "Close", "View", "Star", "computed", "this", "showOnlySelf", "mapState", "username", "window", "sessionStorage", "getItem", "env", "get", "envId", "set", "val", "selectEnv", "data", "treeId", "selectedOption", "selectionChange", "handleSelectionChange", "methods", "mapMutations", "closeModal", "$emit", "push", "selected", "map", "getRowClassName", "row", "creator", "response", "$api", "getNewInterfaces", "status", "length", "delInterface", "delnewInterface", "ElMessage", "message", "duration", "apis", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "$nextTick", "$refs", "childRef", "getInterfaceInfo", "find", "__exports__", "render"], "sourceRoot": ""}