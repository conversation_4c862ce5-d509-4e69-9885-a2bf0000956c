<template>
  <div class="page-container">
    <div class="page-header-card">
      <div class="header-content">
        <div class="action-toolbar">
          <el-tag color="#61649f" class="action-tag" @click="clickApiDlg">
            <el-icon><Plus /></el-icon>HTTP请求
          </el-tag>
          <el-tag color="#E6A23C" class="action-tag" @click="addController([],'if')">
            <el-icon><Plus /></el-icon>条件控制器
          </el-tag>
          <el-tag color="#7B4D12FF" class="action-tag" @click="addController([],'script')">
            <el-icon><Plus /></el-icon>自定义脚本
          </el-tag>
          <el-tag color="#67C23AFF" class="action-tag" @click="addController([],'py')">
            <el-icon><Plus /></el-icon>导入PY脚本
          </el-tag>
        </div>
        <div class="weight-settings">
          <el-tooltip content="场景运行权重设置，默认1设置后会影响运行权重，请谨慎设置！" :enterable="false" placement="top">
            <el-icon style="margin-right: 10px"><QuestionFilled /></el-icon>
          </el-tooltip>
          <el-input-number size="small" v-model="scenceData.weight" :min="1" :max="10" @change="handleChange">
          </el-input-number>
        </div>
      </div>
    </div>

    <el-scrollbar height="calc(100vh - 220px)">
      <el-tree
        :data="steps"
        :props="defaultProps"
        draggable
        :default-expand-all="isExpand"
        :expand-on-click-node="false"
        @node-click="handleStepClick"
        :allow-drop="allowDrop"
        @node-drop="updateStepOrder"
        :node-drag-start="handleDragScroll"
        class="custom-tree"
      >
        <template v-slot="{ node,data }">
          <el-card v-if="data.stepInfo" :class="['step-card', `step-card-${data.stepInfo.type}`]">
            <div slot="header" class="card-header">
              <el-row :gutter="10" type="flex" align="middle" justify="center">
                <el-col :span="20" class="card-main-content">
                  <div class="card-content-wrapper">
                    <!--HTTP接口展示-->
                    <div v-if="data.stepInfo.type==='api'" class="card-inner">
                      <div class="card-left">
                        <span class="step-icon">{{ getCardIndex(node.parent, node) }}</span>
                        <el-tag color="#61649f" class="step-tag">HTTP请求</el-tag>
                        <span class="method-tag">
                          <span v-if="data.stepInfo.content.method === 'POST'">
                            <b style="color: #49cc90;">{{ data.stepInfo.content.method }}</b>
                          </span>
                          <span v-if="data.stepInfo.content.method === 'GET'">
                            <b style="color: #61affe;">{{ data.stepInfo.content.method }}</b>
                          </span>
                          <span v-if="data.stepInfo.content.method === 'PUT'">
                            <b style="color: #fca130;">{{ data.stepInfo.content.method }}</b>
                          </span>
                          <span v-if="data.stepInfo.content.method === 'PATCH'">
                            <b style="color: #50e3c2;">{{ data.stepInfo.content.method }}</b>
                          </span>
                          <span v-if="data.stepInfo.content.method === 'DELETE'">
                            <b style="color: #f93e3e;">{{ data.stepInfo.content.method }}</b>
                          </span>
                          <span v-if="data.stepInfo.content.method === 'DEAD'">
                            <b style="color: rgb(201, 233, 104);">{{ data.stepInfo.content.method }}</b>
                          </span>
                        </span>
                      </div>
                      <div class="card-center">
                        <b class="card-url">{{ data.stepInfo.content.url }}</b>
                        <span class="card-name">{{data.stepInfo.content.name }}</span>
                      </div>
                    </div>

                    <!--if控制器展示-->
                    <div v-if="data.stepInfo.type==='if'" class="card-inner">
                      <div class="card-left">
                        <span class="step-icon">{{ getCardIndex(node.parent, node) }}</span>
                        <el-tag color="rgb(230, 162, 60)" class="step-tag">条件控制器</el-tag>
                      </div>
                      <div class="card-center if-content">
                        <div class="if-controls-wrapper">
                          <el-input class="input-def" placeholder="变量，例如{{name}}" v-model="data.stepInfo.content.variable"/>
                          <el-select v-model="data.stepInfo.content.JudgmentMode" placeholder="请选择" class="judgment-select">
                            <el-option
                              v-for="item in options"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                          <el-input class="input-def" placeholder="值" v-model="data.stepInfo.content.value"/>
                        </div>
                      </div>
                    </div>

                    <!--循环控制器展示-->
                    <div v-if="data.stepInfo.type==='for'" class="card-inner">
                      <div class="card-left">
                        <span class="step-icon">{{ getCardIndex(node.parent, node) }}</span>
                        <el-tag color="rgb(2, 167, 240)" class="step-tag">循环控制器</el-tag>
                      </div>
                      <div class="card-center">
                        <div class="for-controls-wrapper">
                          <el-radio-group v-model="data.stepInfo.content.select" @click.stop class="radio-group">
                            <el-radio label="count" value="count">次数循环</el-radio>
                            <el-radio label="for" value="for">for循环</el-radio>
                            <el-radio label="while" value="while">while循环</el-radio>
                          </el-radio-group>
                        </div>
                      </div>
                    </div>
                    <div v-if="data.stepInfo.type==='for' && data.stepInfo.dlg" class="loop-details" @click.stop>
                      <div v-if="data.stepInfo.content.select==='count' || data.stepInfo.content.select===''">
                        <div class="loop">
                          <div class="loop-control">
                            <span>循环次数</span>
                            <el-input v-model="data.stepInfo.content.cycleIndex" style="width: 200px" placeholder="循环次数" />
                          </div>
                          <div class="loop-control">
                            <span>循环间隔</span>
                            <el-input-number
                              v-model="data.stepInfo.content.cycleInterval"
                              :min="0"
                              :max="999"
                              size="small"
                              controls-position="right"
                              placeholder="秒"
                            />
                            <span>秒</span>
                          </div>
                        </div>
                      </div>
                      <div v-if="data.stepInfo.content.select==='for'">
                        <div class="loop">
                          <div class="loop-control">
                            <el-input style="width: 200px" placeholder="定义变量名称" v-model="data.stepInfo.content.variableName"/>
                            <b style="margin-left: 10px;margin-right: 10px">in</b>
                            <el-input style="width: 200px" placeholder="变量，例如{{name}}" v-model="data.stepInfo.content.variable"/>
                          </div>
                          <div class="loop-control">
                            <span>循环间隔</span>
                            <el-input-number
                              v-model="data.stepInfo.content.cycleInterval"
                              :min="0"
                              :max="999"
                              size="small"
                              controls-position="right"
                              placeholder="秒"
                            />
                            <span>秒</span>
                          </div>
                        </div>
                      </div>
                      <div v-if="data.stepInfo.content.select==='while'">
                        <div class="loop">
                          <div class="while-loop-section">
                            <h4 style="margin: 0 0 10px 0; color: #409eff; font-size: 14px;">循环条件设置</h4>

                            <!-- 条件类型选择 -->
                            <div class="loop-control">
                              <span style="min-width: 80px;">条件类型</span>
                              <el-select v-model="data.stepInfo.content.whileConditionType" placeholder="选择条件类型" style="width: 200px;">
                                <el-option label="变量比较" value="variable" />
                                <el-option label="表达式" value="expression" />
                                <el-option label="脚本函数" value="function" />
                              </el-select>
                            </div>

                            <!-- 变量比较模式 -->
                            <div v-if="data.stepInfo.content.whileConditionType === 'variable'" class="condition-config">
                              <div class="loop-control">
                                <span>左操作数</span>
                                <el-input style="width: 180px" placeholder="变量，例如{{counter}}" v-model="data.stepInfo.content.whileLeftOperand"/>
                              </div>
                              <div class="loop-control">
                                <span>比较操作符</span>
                                <el-select v-model="data.stepInfo.content.whileOperator" placeholder="选择操作符" style="width: 120px;">
                                  <el-option label="<" value="lt" />
                                  <el-option label="<=" value="lte" />
                                  <el-option label=">" value="gt" />
                                  <el-option label=">=" value="gte" />
                                  <el-option label="==" value="eq" />
                                  <el-option label="!=" value="ne" />
                                  <el-option label="包含" value="contains" />
                                  <el-option label="不包含" value="not_contains" />
                                </el-select>
                              </div>
                              <div class="loop-control">
                                <span>右操作数</span>
                                <el-input style="width: 180px" placeholder="值或变量，例如10或{{max}}" v-model="data.stepInfo.content.whileRightOperand"/>
                              </div>
                            </div>

                            <!-- 表达式模式 -->
                            <div v-if="data.stepInfo.content.whileConditionType === 'expression'" class="condition-config">
                              <div class="loop-control">
                                <span>条件表达式</span>
                                <el-input
                                  style="width: 100%; max-width: 400px;"
                                  placeholder="例如: {{counter}} < 100 and {{status}} == 'running'"
                                  v-model="data.stepInfo.content.whileExpression"
                                  type="textarea"
                                  :rows="2"
                                />
                              </div>
                              <div class="expression-help">
                                <el-alert
                                  title="表达式说明"
                                  type="info"
                                  show-icon
                                  :closable="false"
                                  style="margin-top: 10px;">
                                  <template #default>
                                    <div style="font-size: 12px;">
                                      <div>• 支持变量引用: {{variable_name}}</div>
                                      <div>• 支持比较操作: <, <=, >, >=, ==, !=</div>
                                      <div>• 支持逻辑操作: and, or, not</div>
                                      <div>• 支持函数调用: len({{list_var}}), int({{str_var}})</div>
                                    </div>
                                  </template>
                                </el-alert>
                              </div>
                            </div>

                            <!-- 脚本函数模式 -->
                            <div v-if="data.stepInfo.content.whileConditionType === 'function'" class="condition-config">
                              <div class="loop-control">
                                <span>函数名称</span>
                                <el-input style="width: 200px" placeholder="例如: check_condition" v-model="data.stepInfo.content.whileFunctionName"/>
                              </div>
                              <div class="loop-control">
                                <span>函数参数</span>
                                <el-input
                                  style="width: 300px;"
                                  placeholder="例如: {{var1}}, {{var2}}, 'constant'"
                                  v-model="data.stepInfo.content.whileFunctionArgs"
                                />
                              </div>
                              <div class="function-help">
                                <el-alert
                                  title="函数使用说明"
                                  type="warning"
                                  show-icon
                                  :closable="false"
                                  style="margin-top: 10px;">
                                  <template #default>
                                    <div style="font-size: 12px;">
                                      <div>• 函数必须返回布尔值 (True/False)</div>
                                      <div>• 函数需要在全局作用域中定义</div>
                                      <div>• 参数支持变量引用和常量值</div>
                                    </div>
                                  </template>
                                </el-alert>
                              </div>
                            </div>

                            <!-- 通用配置 -->
                            <div class="while-common-config">
                              <h4 style="margin: 15px 0 10px 0; color: #409eff; font-size: 14px;">循环控制设置</h4>

                              <div class="loop-control">
                                <span>最大循环次数</span>
                                <el-input-number
                                  v-model="data.stepInfo.content.whileMaxIterations"
                                  :min="1"
                                  :max="10000"
                                  size="small"
                                  controls-position="right"
                                  placeholder="次"
                                  style="width: 150px;"
                                />
                                <span style="margin-left: 10px; font-size: 12px; color: #666;">防止无限循环</span>
                              </div>

                              <div class="loop-control">
                                <span>循环间隔</span>
                                <el-input-number
                                  v-model="data.stepInfo.content.cycleInterval"
                                  :min="0"
                                  :max="999"
                                  size="small"
                                  controls-position="right"
                                  placeholder="秒"
                                  style="width: 120px;"
                                />
                                <span>秒</span>
                              </div>

                              <div class="loop-control">
                                <span>超时时间</span>
                                <el-input-number
                                  v-model="data.stepInfo.content.whileTimeout"
                                  :min="0"
                                  :max="3600"
                                  size="small"
                                  controls-position="right"
                                  placeholder="秒"
                                  style="width: 120px;"
                                />
                                <span>秒 (0表示无超时)</span>
                              </div>

                              <!-- 循环变量配置 -->
                              <div class="loop-control">
                                <span>循环计数器变量</span>
                                <el-input
                                  style="width: 200px"
                                  placeholder="例如: loop_counter"
                                  v-model="data.stepInfo.content.whileCounterVar"
                                />
                                <span style="margin-left: 10px; font-size: 12px; color: #666;">可在条件和子步骤中使用</span>
                              </div>

                              <!-- 高级选项 -->
                              <div class="advanced-options">
                                <el-divider content-position="left" style="margin: 15px 0 10px 0;">
                                  <span style="color: #909399; font-size: 12px;">高级选项</span>
                                </el-divider>

                                <div class="loop-control">
                                  <el-checkbox v-model="data.stepInfo.content.whileBreakOnError" style="margin-right: 20px;">
                                    遇到错误时终止循环
                                  </el-checkbox>
                                  <el-checkbox v-model="data.stepInfo.content.whileLogIterations">
                                    记录每次迭代日志
                                  </el-checkbox>
                                </div>

                                <div class="loop-control">
                                  <span>条件检查时机</span>
                                  <el-radio-group v-model="data.stepInfo.content.whileCheckTiming" size="small">
                                    <el-radio label="before">执行前检查</el-radio>
                                    <el-radio label="after">执行后检查</el-radio>
                                  </el-radio-group>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!--自定义脚本展示-->
                    <div v-if="data.stepInfo.type==='script'" class="card-inner">
                      <div class="card-left">
                        <span class="step-icon">{{ getCardIndex(node.parent, node) }}</span>
                        <el-tag color="rgb(123, 77, 18)" class="step-tag">自定义脚本</el-tag>
                      </div>
                      <div class="card-center">
                        <el-input @click.stop v-if="data.stepInfo.inputDlg" v-model="data.stepInfo.name" @blur="cancelEditing(data.stepInfo)" ref="input" maxlength="50" class="script-name-input"></el-input>
                        <el-button v-else class="script-button" plain type="text" @click="startEditing(data.stepInfo)" @click.stop>
                          {{data.stepInfo.name}}
                          <el-icon><Edit /></el-icon>
                        </el-button>
                      </div>
                    </div>
                    <div v-if="data.stepInfo.type==='script' && data.stepInfo.dlg" class="script-editor" @click.stop>
                      <el-row :gutter="10">
                        <el-col :span="24"><Editor v-model="data.stepInfo.script" lang="python" theme="chrome"></Editor></el-col>
                      </el-row>
                    </div>

                    <!--py脚本展示-->
                    <div v-if="data.stepInfo.type==='py'" class="card-inner">
                      <div class="card-left">
                        <span class="step-icon">{{ getCardIndex(node.parent, node) }}</span>
                        <el-tag color="rgb(103, 194, 58)" class="step-tag">导入PY脚本</el-tag>
                      </div>
                      <div class="card-center">
                        <el-input @click.stop v-if="data.stepInfo.inputDlg" v-model="data.stepInfo.name" @blur="cancelEditing(data.stepInfo)" ref="input" maxlength="50" class="script-name-input"></el-input>
                        <el-button v-else class="script-button" plain type="text" @click="startEditing(data.stepInfo)" @click.stop>
                          {{data.stepInfo.name}}
                          <el-icon><Edit /></el-icon>
                        </el-button>
                      </div>
                    </div>

                    <!--time控制器展示-->
                    <div v-if="data.stepInfo.type==='time'" class="card-inner">
                      <div class="card-left">
                        <span class="step-icon">{{ getCardIndex(node.parent, node) }}</span>
                        <el-tag color="rgb(103, 194, 58)" class="step-tag">等待控制器</el-tag>
                      </div>
                      <div class="card-center time-controller">
                        <div class="time-control">
                          <el-input-number
                            v-model="data.stepInfo.content.time"
                            :min="0"
                            :max="999"
                            size="small"
                            controls-position="right"
                            placeholder="秒"
                          />
                          <span>秒</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="4" class="card-actions">
                  <div class="action-buttons">
                    <el-tooltip v-if="data.stepInfo.type==='api'" class="item" effect="light" content="接口任务运行权重默认为1,设置后将影响运行结果！" placement="top">
                      <el-input-number
                        @click.stop
                        v-model="data.stepInfo.weight"
                        :min="1"
                        :max="10"
                        size="default"
                        controls-position="right"
                        placeholder=1
                        style="width: 80px;margin-right: 10px"
                      >
                      </el-input-number>
                    </el-tooltip>
                    <el-switch
                      @click.stop
                      v-model="data.stepInfo.status"
                      inline-prompt
                      size="default"
                      @click="switchClick(data)"
                      style="--el-switch-on-color: #53a8ff; --el-switch-off-color: #dcdfe6"
                    />
                    <el-button @click.stop size="default" circle type="danger" @click="delTree(data)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </template>
      </el-tree>
    </el-scrollbar>

    <!--  选择接口弹窗-->
    <apiCite v-if="addApiDlg" :selectType="selectType" @childEvent="addController" @close-modal="handleCloseModal"></apiCite>
    <!--  编辑接口弹窗-->
    <el-drawer v-model="editApiDlg" :with-header="false" size="50%">
      <editApi ref="childRef" @closeDrawer="handleClose" :interfaceData="interfaceData" style="padding: 0 10px;"></editApi>
    </el-drawer>
  </div>
</template>

<script>
import {mapMutations, mapState} from "vuex";
import {ElNotification, ElMessageBox, ElMessage} from "element-plus";
import apiCite from '../../views/TestCase/apiCiteDlg.vue';
import Editor from '@/components/common/Editor.vue';
import editApi from '../../views/PerformanceTest/editApiDlg.vue';
import { Plus, Edit, Delete, QuestionFilled } from '@element-plus/icons-vue';
import * as uuid from 'uuid';

export default {
  components:{
    apiCite,
    Editor,
    editApi,
    Plus,
    Edit,
    Delete,
    QuestionFilled
  },
  props: {
    scenceId: {
      type: Number,
      default: []
    },
    steps: {
      type: Array,
    },
    scenceData:{
      type: String,
    },
  },
  data() {
    return {
      addApiDlg:false,
      editApiDlg:false,
      selectType:'perf',
      treeKey: '',
      isExpand: false,
      ControllerData:{
        scence:'',
        name: '',
        type: '',
        content: {},
        desc:"",
        script:"",
        creator:'',
        weight: '',
      },
      step_id: '',
      interfaceData:'',
      options: [
          { value: 'equal', label: '等于' },
          { value: 'notEqual', label: '不等于' },
          { value: 'contains', label: '包含' },
          { value: 'notContains', label: '不包含' },
          { value: 'greaterThan', label: '大于' },
          { value: 'lessThan', label: '小于' },
          { value: 'greaterThanOrEqual', label: '大于等于' },
          { value: 'lessThanOrEqual', label: '小于等于' },
          { value: 'empty', label: '空' },
          { value: 'notEmpty', label: '非空' }
        ],

    }
  },
  methods: {

    rowOpenORFold(isExpand) {
	      this.treeKey = +new Date()
	      this.isExpand = isExpand
	    },

    handleStepClick(data) {
      if (data.stepInfo.type==='api'){
        this.editApiDlg = true;
        this.interfaceData = data.stepInfo;
      }
      else if(['for','script'].includes(data.stepInfo.type)) {
        data.stepInfo.dlg = !data.stepInfo.dlg;
      }
    },

    allowDrop(draggingNode, dropNode,type) {
      // 只有 type 为 api, for, if 的节点可以作为父级节点
      const allowedParentTypes = ['for', 'if'];
      if (!allowedParentTypes.includes(dropNode.data.stepInfo.type)) {
        return type === "prev" || type === "next";

      }else {
        return true
      };
  },

    fetchSteps(scenceId) {
      this.$emit('fetch-steps', scenceId);
    },

    async updateStepOrder() {
      const setParentIds = (node, parentId, parentSort) => {
        // 设置父节点的排序字段
        node.sort = parentSort;
        // 如果节点有子节点，则递归设置子节点的 parent 和排序字段
        if (node.children && node.children.length > 0) {
            node.children.forEach((child, childIndex) => {
                // 设置子节点的 parent 为当前节点的 id
                child.parent = node.id;
                // 设置子节点的排序字段
                child.sort = childIndex + 1;
                // 递归调用，处理子节点的子节点
                setParentIds(child, node.id, child.sort);
              });
            }
        };
      // 遍历步骤数组，设置父节点的排序字段和子节点的 parent 和排序字段
      this.steps.forEach((parent, parentIndex) => {
          // 设置父节点的排序字段
          parent.sort = parentIndex + 1;
          // 如果父节点有子节点，则设置子节点的 parent 和排序字段
          if (parent.children && parent.children.length > 0) {
              // 调用函数设置父节点和子节点的属性
              setParentIds(parent, parent.id, parent.sort);
          }else {
            parent.parent = null;
          }
      })
		},

    handleDragScroll() {
      document.addEventListener('mousemove', function(event) {
      const mouseY = event.clientY;
      const elementTop = document.querySelector('.el-tree').getBoundingClientRect().top;

      if (mouseY < 100 && elementTop > 0) {
        window.scrollBy(0, -10);
      } else if (mouseY > window.innerHeight - 100) {
        window.scrollBy(0, 10);
      }
    });
    },

    getCardIndex(parent, node) {
      const index = parent.childNodes.indexOf(node);
      return index + 1;
    },

    async addController(data, type) {
      const params = {...this.ControllerData};
      params.creator = this.username;
      params.type = type;
      params.scence = this.scenceId;
      const DataArray = [];
      let order_s = this.steps.length > 0 ? this.steps.length + 1 : 1;

      if(type ==='if'){
        params.name = "条件控制器";
        params.content = {
          variable:"",
          JudgmentMode:"",
          value:"",
        };
        delete params.weight;
      }
      else if(type ==='script'){
        params.name = "自定义脚本";
        delete params.weight;

      }
      else if(type ==='time'){
        params.name = "定时控制器";
        params.content = {
          time:""
        };
        delete params.weight;
      }
      else {
        params.name = 'HTTP接口';
        params.type = 'api';
        params.weight = 1;
        data.forEach(item => {
        let newItem = {
          ...params,
          content:item
        };
        DataArray.push(newItem);
      })
      }

      if (['if', 'for', 'time', 'script'].includes(type)) {
        const response = await this.$api.createSceneStep(params)
        if (response.status === 201) {
            this.step_id = response.data.id
        }
      }
      else {
        const response = await this.$api.createSceneStep(DataArray)
        if (response.status === 201) {
            this.step_id = response.data.map(item => item.id);
        }
      }
      const response = await this.$api.createTaskSceneStep({
        task: this.perfTask.id,
        scence: this.scenceId,
        step: this.step_id,
        sort: order_s,
        creator: this.username,
        parent: null,
      })
        if (response.status === 201) {
          ElNotification({
              duration: 500,
              title: '添加成功',
              type: 'success',
            })
        }
      this.fetchSteps(this.scenceId)
    },

    handleCloseModal() {
      this.addApiDlg = false;
    },
    clickApiDlg() {
      this.addApiDlg = true;
    },

    cancelEditing(data) {
      data.inputDlg = false;
    },

    startEditing(data) {
      if(data.type==='script'){data.inputDlg = true}else {data.inputDlg = true}
      this.$nextTick(() => {
        this.$refs.input.focus();
      });
    },


    async delTree(data) {
      event.stopPropagation();
      console.log(data)
      const response = await this.$api.deleteTaskSceneStep(data.id,this.scenceId);
			if (response.status === 204) {
			  const res = await this.$api.deleteSceneStep(data.stepInfo.id);
			  if (res.status === 204){
          ElNotification({
              duration: 500,
              title: '删除成功',
              type: 'success',
            });
          this.fetchSteps(this.scenceId)
          }
			}
  },
   handleClose() {
      this.editApiDlg = false;
    },

  },
  computed: {
    ...mapState({
      envId: state => state.envId,
      testEnvs: state => state.testEnvs,
      pro: state => state.pro,
      perfTask: state => state.perfTask,
    }),
    defaultProps() {
      return {
        children: 'children',
        label: 'name',
      }
    },
    username() {
      return window.sessionStorage.getItem('username');
    },
  },
}

</script>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.page-header-card {
  background-color: #fff;
  padding: 10px 20px;
  margin-bottom: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-toolbar {
  display: flex;
  gap: 10px;
}

.action-tag {
  color: #ffffff;
  width: 100px;
  height: 32px;
  text-align: center;
  font-size: 13px;
  line-height: 32px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.action-tag:hover {
  opacity: 0.9;
}

.weight-settings {
  display: flex;
  align-items: center;
  color: #606266;
}

.custom-tree {
  padding: 10px 20px 10px 10px;
  width: 100%;
}

/* 卡片样式 */
.step-card {
  margin-bottom: 8px;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  width: 100%;
  position: relative;
  z-index: 5;
  overflow: visible !important;
}

.step-card:hover {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 不同类型卡片的边框颜色 */
.step-card-api {
  border-left: 4px solid #61649f;
}
.step-card-if {
  border-left: 4px solid rgb(230, 162, 60);
}
.step-card-for {
  border-left: 4px solid rgb(2, 167, 240);
}
.step-card-script {
  border-left: 4px solid rgb(123, 77, 18);
}
.step-card-py {
  border-left: 4px solid rgb(103, 194, 58);
}
.step-card-time {
  border-left: 4px solid rgb(103, 194, 58);
}

.card-header {
  padding: 5px;
  width: 100%;
}

.card-main-content {
  flex-grow: 1;
  padding: 2px 0;
}

.card-content-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: visible;
  max-width: 100%;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 2px 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-right: 15px;
}

/* 卡片内部布局样式 */
.card-inner {
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: relative;
  width: 100%;
  min-height: 32px;
  overflow: visible;
}

.card-left {
  display: flex;
  align-items: center;
  width: 170px;
  min-width: 170px;
  flex-shrink: 0;
  justify-content: flex-start;
  margin-right: 10px;
  gap: 6px;
}

.card-center {
  margin-left: 5px;
  flex-grow: 1;
  overflow: hidden;
}

.step-tag {
  color: #ffffff;
  height: 22px;
  text-align: center;
  font-size: 11px;
  line-height: 22px;
  padding: 0 4px;
  width: 80px;
  flex-shrink: 0;
}

.method-tag {
  min-width: 50px;
  display: inline-block;
  font-size: 12px;
  flex-shrink: 0;
}

.card-name {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-url {
  font-size: 13px;
  margin-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60%;
}

.step-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  font-weight: bold;
  background-color: white;
  border: 2px solid currentColor;
  text-align: center;
  line-height: 22px;
  font-size: 12px;
  box-sizing: border-box;
  flex-shrink: 0;
}

/* 为不同类型步骤的序号设置不同颜色 */
.step-card-api .step-icon {
  color: rgb(97, 100, 159) !important;
  border-color: rgb(97, 100, 159) !important;
}

.step-card-if .step-icon {
  color: rgb(230, 162, 60) !important;
  border-color: rgb(230, 162, 60) !important;
}

.step-card-for .step-icon {
  color: rgb(2, 167, 240) !important;
  border-color: rgb(2, 167, 240) !important;
}

.step-card-script .step-icon {
  color: rgb(123, 77, 18) !important;
  border-color: rgb(123, 77, 18) !important;
}

.step-card-py .step-icon {
  color: rgb(103, 194, 58) !important;
  border-color: rgb(103, 194, 58) !important;
}

.step-card-time .step-icon {
  color: rgb(103, 194, 58) !important;
  border-color: rgb(103, 194, 58) !important;
}

/* 条件控制器样式 */
.if-content {
  width: 100%;
  flex-wrap: nowrap;
  gap: 8px;
  justify-content: flex-start;
  padding: 8px 0;
  overflow: visible;
  position: relative;
}

.if-controls-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  width: 100%;
  position: relative;
  overflow: visible !important;
  min-width: min-content;
  z-index: 20;
}

.judgment-select {
  width: 90px;
  flex-shrink: 0;
}

.input-def {
  width: 160px;
  flex-shrink: 0;
}

/* 脚本样式 */
.script-name-input {
  height: 28px;
  max-width: 380px;
}

.script-button {
  color: black;
  border: none;
  outline: none;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  margin: 0;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.script-button .el-icon {
  margin-left: 5px;
  font-size: 12px;
}

.script-editor {
  padding: 15px 0;
  border-top: 1px dashed #eee;
  margin-top: 8px;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* 循环控制器样式 */
.for-controls-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  overflow: visible;
}

.radio-group {
  display: flex;
  align-items: center;
  gap: 20px;
  white-space: nowrap;
}

.loop-details {
  background-color: #f9f9f9;
  border-radius: 0 0 8px 8px;
  padding: 10px 15px;
  margin-top: 5px;
  width: 100%;
  overflow: visible;
  position: relative;
}

.loop {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 20px;
  margin: 15px 0;
  width: 100%;
  overflow: visible;
  position: relative;
}

.loop-control {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 等待控制器样式 */
.time-controller {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  flex-wrap: nowrap;
}

.time-control {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 树节点样式 */
:deep(.el-tree-node__content) {
  padding: 2px 3px;
  height: auto;
  width: 100%;
}

:deep(.el-tree-node__expand-icon) {
  padding: 4px;
}

:deep(.el-tree-node.is-drop-inner>.el-tree-node__content .el-tree-node__label) {
  background-color: #409EFF;
  color: white;
}

:deep(.el-tree-node) {
  width: 100%;
}

:deep(.el-tree) {
  width: 100%;
}

:deep(.el-card) {
  width: 100%;
  margin-right: 20px;
  --el-card-padding: 5px;
  margin-bottom: 8px;
}

:deep(.el-tree-node__children) {
  width: 100%;
}

/* 设置Input和其他表单元素的尺寸 */
:deep(.el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.el-input-number) {
  line-height: normal;
}

:deep(.el-switch) {
  --el-switch-size: 18px;
  margin-right: 5px;
}

:deep(.el-button.is-circle) {
  width: 28px;
  height: 28px;
  padding: 6px;
  margin-left: 5px;
}

/* while循环样式 */
.while-loop-section {
  width: 100%;
  max-width: 800px;
}

.condition-config {
  background: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
  margin: 10px 0;
}

.while-common-config {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
  margin: 15px 0;
}

.expression-help,
.function-help {
  margin-top: 10px;
}

.advanced-options {
  margin-top: 15px;
}

.advanced-options .loop-control {
  margin-bottom: 10px;
}

.advanced-options .el-checkbox {
  margin-bottom: 8px;
}

.advanced-options .el-radio-group {
  margin-left: 10px;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .card-inner {
    flex-wrap: wrap;
    padding: 10px 20px;
  }

  .card-left {
    width: auto;
    min-width: 150px;
    margin-right: 10px;
  }

  .if-controls-wrapper {
    flex-wrap: wrap;
  }

  .card-center {
    width: 100%;
    margin-top: 10px;
    margin-left: 0;
  }

  .card-url {
    max-width: 100%;
    margin: 5px 0;
  }

  .input-def {
    width: 100%;
    margin-bottom: 8px;
  }
  
  /* while循环条件配置响应式布局 */
  .condition-config .loop-control,
  .while-common-config .loop-control {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 15px;
  }

  .condition-config .loop-control span,
  .while-common-config .loop-control span {
    min-width: auto;
    margin-bottom: 5px;
  }
}
</style>