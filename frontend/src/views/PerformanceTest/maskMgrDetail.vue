<template>
  <div class="box">
    <el-card class="task-card">
      <div class="task-header">
        <div class="task-info">
          <div class="task-navigation">
            <el-button class="back-button" type="text" @click="back">
              <el-icon><CaretLeft /></el-icon>返回
            </el-button>
            <div class="task-title">
              <span class="title-label">任务管理 /</span>
              <el-button
                class="task-name-button"
                type="text"
                @click="popup('task-edit')"
                @click.stop
              >
                {{ perfTask.taskName }}
                <el-icon class="edit-icon"><Edit /></el-icon>
              </el-button>
              <el-button class="task-type-tag" :type="perfTask.taskType.toString() === '20' ? 'warning' : 'primary'">
                {{ taskTypeMap[perfTask.taskType.toString()] || perfTask.taskType }}
              </el-button>
            </div>
          </div>
          <div class="task-details">
            <div class="detail-item">
              <span class="detail-label">创建人:</span>
              <span class="detail-value">{{perfTask.creator}}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建时间:</span>
              <span class="detail-value">{{ $tools.rTime(perfTask.create_time) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">最后修改:</span>
              <span class="detail-value">{{ $tools.rTime(perfTask.update_time) }}</span>
            </div>
            <div class="detail-item description">
              <span class="detail-label">任务描述:</span>
              <span class="detail-value">{{perfTask.desc || '暂无描述'}}</span>
            </div>
          </div>
        </div>
        <div class="task-actions">
          <el-button @click="clickRun" type="success" class="run-task-button">
            <el-icon><CaretRight /></el-icon>执行任务
          </el-button>
        </div>
      </div>
    </el-card>
    <el-row :gutter="2" style=" justify-content: flex-end;">
      <el-col :span="4">
        <div class="tree-component">
          <div class="search-container">
            <el-input v-model="filterText" placeholder="请输入场景名称搜索" clearable>
              <template #append>
                <el-button type="primary" @click="searchClick">查询</el-button>
              </template>
            </el-input>
          </div>
          <el-button
            type="primary"
            class="add-scene-button"
            @click="popup('add')"
          >
            <el-icon><Plus /></el-icon>添加场景
          </el-button>
          <el-scrollbar height="calc(100vh - 265px)">
            <el-tree
              v-if="scenceId"
              node-key="id"
              :current-node-key="scenceId"
              class="filter-tree"
              :data="sceneList"
              :props="defaultProps"
              default-expand-all
              :expand-on-click-node="false"
              @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                <el-scrollbar>
                  <span class="bold-node">
                    {{ node.label }}
                  </span>
                </el-scrollbar>
                <div class="node-content">
                  <span class="tree-actions">
                    <el-button type="primary" size="small" circle class="tree-action-btn edit-btn" @click="popup('edit',node.data)">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                    <el-button type="danger" size="small" circle class="tree-action-btn delete-btn" @click="delScene(node.data.id)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </span>
                </div>
              </template>
            </el-tree>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="14" style="background:#ffffff;">
        <div  class="title">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span >场景步骤</span>
            <div class="action-buttons-group">
                <div class="env-selector">
                  <el-tooltip v-if="scenceData.env" class="box-item" effect="dark" content="查看环境信息" placement="top">
                    <el-button  class="icon-button" @click="clickShowEnv">
                      <el-icon><View /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-select clearable v-model="scenceData.env" placeholder="选择环境" style="width: 180px;" no-data-text="暂无数据" size="small" class="env-select">
                    <el-option v-for="item in testEnvs" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                  <el-dialog v-model="showEnv" title="环境变量" top="10px" width="70%">
                  <el-descriptions border :column="1" label-align>
                    <el-descriptions-item :label="key" v-for="(value, key) in envInfo.debug_global_variable">
                      <template #label>
                        <el-tag color="#E6A23C">debug</el-tag>
                        {{ key }}
                      </template>
                      {{ value }}
                    </el-descriptions-item>
                    <el-descriptions-item :label="key" v-for="(value, key) in envInfo.global_variable">
                      <template #label>
                        <el-tag color="#67C23AFF">global</el-tag>
                        {{ key }}
                      </template>
                      {{ value }}
                    </el-descriptions-item>
                  </el-descriptions>
                  <template #footer>
                    <span class="dialog-footer">
                      <el-button @click="editEnv(envInfo)" type="success" plain>编辑</el-button>
                      <el-button @click="showEnv = false">关闭</el-button>
                    </span>
                  </template>
                </el-dialog>
                </div>
                <div class="operation-buttons">
                  <el-dropdown trigger="click" placement="bottom-end">
                    <el-button type="info" size="small" class="action-button batch-button" style="margin-right: 12px">
                      <span>批量操作</span>
                      <el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="批量禁用" @click="clickOts('stop')">
                          <el-icon><Remove /></el-icon>
                          批量禁用
                        </el-dropdown-item>
                        <el-dropdown-item command="批量启用" @click="clickOts('start')">
                          <el-icon><SuccessFilled /></el-icon>
                          批量启用
                        </el-dropdown-item>
                        <el-dropdown-item command="批量删除" @click="clickOts('delete')">
                          <el-icon><CircleClose /></el-icon>
                          批量删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <el-button type="warning" size="small" class="action-button" @click="clickOts('sync')">
                    <el-icon><Refresh /></el-icon>
                    <span>同步接口</span>
                  </el-button>
                  <el-button type="primary" size="small" class="action-button" @click="clickScenceStep">
                    <el-icon><Document /></el-icon>
                    <span>保存</span>
                  </el-button>
                  <el-button type="success" size="small" class="action-button" @click="debugScence">
                    <el-icon><VideoPlay /></el-icon>
                    <span>调试</span>
                  </el-button>
                </div>
              </div>
          </div>
        </div>
        <perfStep :scenceId="scenceId" :steps="steps" :scenceData="scenceData" @fetch-steps="getTaskScenceStep"></perfStep>
      </el-col>
      <el-col :span="6">
        <configuration></configuration>
      </el-col>
    </el-row>
  </div>
  <!--  新增/修改场景弹窗-->
  <el-dialog  :title="dialogTitle" v-model="dialogVisible"  width="30%" custom-class="class_dialog" :required="true" style="text-align:left" :before-close="clearValidation">
    <el-form :model="sceneForm" :rules="rulesPerf" ref="perfRef">
      <el-form-item label="场景名称"  prop="name" >
        <el-input v-model="sceneForm.name"  maxlength="50" placeholder="请输入场景名称"/>
      </el-form-item>
    </el-form>
    <template #footer>
			<span class="dialog-footer">
				<el-button @click="clearValidation" >取消</el-button>
        <el-button v-if="dialogType==='add'" type="primary" @click="addScene" >确定</el-button>
				<el-button v-else type="primary" @click="editScene" >确定</el-button>
			</span>
		</template>
  </el-dialog>
  <!--  修改任务名称弹窗-->
  <el-dialog  :title="dialogTitle" v-model="dialogVisible1"  width="30%" custom-class="class_dialog" :required="true" style="text-align:left" :before-close="clearValidation">
    <el-form :model="taskForm" :rules="rulesPerf" ref="perfRef">
      <el-form-item label="任务名称"  prop="taskName" >
        <el-input v-model="taskForm.taskName"  maxlength="50" placeholder="请输入任务名称"/>
      </el-form-item>
      <el-form-item label="任务描述" prop="desc">
        <el-input type="textarea" v-model="taskForm.desc" placeholder="请输入备注"/>
      </el-form-item>
    </el-form>
    <template #footer>
			<span class="dialog-footer">
				<el-button @click="clearValidation" >取消</el-button>
				<el-button  type="primary" @click="editTask" >确定</el-button>
			</span>
		</template>
  </el-dialog>
  <!--  批量同步、批量禁用/启用、批量删除弹窗-->
  <el-drawer v-model="otsDlg"  :title="titleOts" :destroy-on-close="true" :show-close="false" @close="handleClose" size="23%">
    <template #default>
			<el-tabs type="card" style="margin-left: 10px">
        <div>
          <el-checkbox size="mini" :indeterminate="isIndeterminate"  v-model="new_task_form.case_checkAll" @change="handleCheckAllChange" style="padding:0px;margin-right:5px;">全选</el-checkbox>
        </div>
        <el-scrollbar height="calc(100vh - 160px)">
					<el-tree
              v-if="typeOts !== 'sync'"
              ref="casetree"
              :data="steps"
              show-checkbox
              :props="defaultProps"
              @check-change="case_check_change"
              node-key="id"
              :default-expand-all="false"
              highlight-current
              empty-text="暂无数据">
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<div>
                  <el-icon class="step-icon" style="color: #909399">{{ getCardIndex(node.parent, node) }}</el-icon>
                  {{ data.stepInfo.name }}
								</div>
							</span>
						</template>
					</el-tree>
          <el-tree
              v-else
              ref="casetree"
              :data="steps"
              show-checkbox
              :props="defaultProps"
              @check-change="case_check_change"
              node-key="id"
              :default-expand-all="false"
              highlight-current
              empty-text="暂无数据">
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<div>
                  <el-icon class="step-icon" style="color: #909399">{{ getCardIndex(node.parent, node) }}</el-icon>
                   {{ data.content?.name || '未定义' }}  {{ data.content?.url || '未定义' }}
								</div>
							</span>
						</template>
					</el-tree>
        </el-scrollbar>
			</el-tabs>
			<div class="add-btns">
        <el-button v-if="typeOts==='start'" class="drawer-action-btn enable-btn" size="default" @click="makeOts(typeOts)">
          <el-icon><SuccessFilled /></el-icon>确认启用
        </el-button>
        <el-button v-if="typeOts==='stop'" class="drawer-action-btn disable-btn" size="default" @click="makeOts(typeOts)">
          <el-icon><Remove /></el-icon>确认禁用
        </el-button>
        <el-button v-if="typeOts==='delete'" class="drawer-action-btn delete-btn" size="default" @click="makeOts(typeOts)">
          <el-icon><CircleClose /></el-icon>确认删除
        </el-button>
        <el-button v-if="typeOts==='sync'" class="drawer-action-btn sync-btn" size="default" @click="makeOts(typeOts)">
          <el-icon><Refresh /></el-icon>确认同步
        </el-button>
        <el-button class="drawer-action-btn cancel-btn" size="default" @click="handleClose">
          <el-icon><CircleClose /></el-icon>关闭窗口
        </el-button>
			</div>
		</template>
  </el-drawer>
</template>

<script >
import {mapMutations, mapState} from "vuex";
import {ElNotification} from "element-plus";
import perfStep from './perfStep.vue'
import configuration from './maskMgrDetail_set.vue'
import {ElMessage, ElMessageBox} from "element-plus";
import { CaretLeft, Edit, CaretRight, Plus, View, Remove, SuccessFilled,
  CircleClose, Refresh, Document, VideoPlay, Delete, ArrowDown } from '@element-plus/icons-vue';
export default {
  components: {
    perfStep,
    configuration,
    CaretLeft,
    Edit,
    CaretRight,
    Plus,
    View,
    Remove,
    SuccessFilled,
    CircleClose,
    Refresh,
    Document,
    VideoPlay,
    Delete,
    ArrowDown
  },
  data() {
    return {
      dialogType: '',
      dialogTitle: '',
      dialogVisible: false,
      dialogVisible1: false,
      steps:[],
      scenceId: '',
      sceneForm: {
        name : '',
        task :'',
      },
      importSetData:null,
      taskForm:{
        taskName: '',
        desc: '',
        update_time: '',
        modifier : '',
      },
      taskTypeMap: {'10': '普通任务', '20': '定时任务'},
      inputDlg: false,
      filterText:'',
      sceneList: [],
      scenceData:'',
      envInfo:{},
      showEnv: false,
      otsDlg:false,
      titleOts: '',
      typeOts:'',
      new_task_form:{
            "case_checkAll":false
        },
      isIndeterminate:false,

    };
  },
 	methods: {
    ...mapMutations(['clearTask', 'checkedTask', 'selectEnv', 'selectEnvInfo']),

    back() {
      window.history.back();
      this.clearTask()
    },

    async getScenes(query) {
     const response = await this.$api.getTaskScenes(this.perfTask.id,query)
     if (response.status ===200){
				this.sceneList = response.data;
				if (response.data.length>0){
				  this.scenceId = response.data[0].id
          this.handleNodeClick(response.data[0])
        }
			}
    },

    async addScene() {
      const params = this.sceneForm
      const response = await this.$api.createTaskScene(params);
      if (response.status === 201) {
        this.$message({
          type: 'success',
          message: '添加成功',
          duration: 1000
        });
        this.clearValidation();
        this.getScenes()
      }
    },
    async editScene() {
      const params = this.sceneForm
      const response = await this.$api.updateTaskScene(params.id,params);
      if (response.status === 200) {
        this.$message({
          type: 'success',
          message: '编辑成功',
          duration: 1000
        });
        this.clearValidation();
        this.getScenes()
      }
    },
    delScene(id) {
    this.$confirm('此操作将永久删除该场景, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        const response = await this.$api.deleteTaskScene(id)
        if(response.status ===204){
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          // 刷新页面
          this.getScenes();
        }
      })
      .catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
  },
    async clickOts(type) {
      this.otsDlg = type;
      switch (type){
        case 'sync':
          this.titleOts = '批量同步接口数据';
          this.typeOts = type;
          const response = await this.$api.getSceneStep(this.scenceId)
          if(response.status ===200){
            this.steps = response.data;
          }
          break;

        case 'delete':
          this.titleOts = '批量删除';
          this.typeOts = type;
          break;

        case 'start':
          this.titleOts = '批量启用';
          this.typeOts = type;
          break;

        case 'stop':
          this.titleOts = '批量禁用';
          this.typeOts = type;
          break;

      };
    },

    handleClose(){
      this.otsDlg = false;
      this.new_task_form.case_checkAll = false;
      this.getTaskScenceStep(this.scenceId);
    },

    popup(type,data) {
      this.dialogType = type;
      // 根据不同的对话框类型设置标题
      switch (type) {
        case 'add':
          this.dialogVisible = true;
          this.dialogTitle = '新增场景';
          this.sceneForm.task = this.perfTask.id;
          break;

        case 'edit':
          this.dialogVisible = true;
          this.dialogTitle = '编辑场景';
          this.sceneForm = {...data};
          this.sceneForm.id = data.id
          break;

        case 'task-edit':
          this.dialogVisible1 = true;
          this.dialogTitle = '编辑任务';
          this.taskForm.taskName = this.perfTask.taskName;
          this.taskForm.desc = this.perfTask.desc;
          break;

        default:
          this.dialogTitle = '';
          break;
      }
    },

    clearValidation() {
      this.dialogVisible = false;
      this.dialogVisible1 = false;
      this.sceneForm = {
        name : '',
        task :'',
      };
      this.taskForm = {
        taskName: '',
        desc: '',
        update_time: '',
        modifier : ''
      }
    },

    handleNodeClick(data) {
      this.scenceId = data.id;
      this.scenceData = data
      this.getTaskScenceStep(this.scenceId)
    },

    searchClick() {
      this.getScenes(this.filterText)
    },

    startEditing() {
      this.inputDlg = true;
      this.$nextTick(() => {
        this.$refs.input.focus();
      })
    },
    cancelEditing() {
    this.inputDlg = false;
  },
    async clickShowEnv() {
			// 获取单个环境信息
			const response = await this.$api.getEnvInfo(this.scenceData.env,this.pro.id);
			if (response.status === 200) {
				this.envInfo = response.data;
			}
			this.showEnv = true;
		},

		editEnv(envInfo) {
			this.showEnv = false;
			this.selectEnvInfo(envInfo);
			this.$router.push({ name: 'testenv' });
		},
    async editTask() {
      const params = this.taskForm
      params.modifier = this.username;
      params.update_time = this.$tools.newTime()
      const response = await this.$api.updatePerformanceTask(this.perfTask.id, params);
      if (response.status === 200) {
        this.$message({
          type: 'success',
          message: '编辑成功',
          duration: 1000
        });
        this.checkedTask(response.data)
        this.clearValidation();
      }
    },

    async getTaskScenceStep(scenceId) {
      const response = await this.$api.getTaskSceneStep(scenceId);
      if (response.status === 200) {
        this.steps = response.data;
      }
    },
  async clickScenceStep(){
    const prams = {...this.scenceData}
    if(prams.env){
      prams.env = [prams.env]
    }else {
      delete prams.env
    }
    await this.$api.updateTaskScene(this.scenceId,prams)
    const  response = await this.$api.batchUpdateSceneStep(this.steps)
    if (response.status === 200) {
        ElNotification({
            duration: 500,
            title: '保存成功',
            type: 'success',
          });
    }
  },
  getCardIndex(parent, node) {
    const index = parent.childNodes.indexOf(node);
    return index + 1;
  },

  case_check_change(node1,node2,node3){//树节点check事件
            let checked_count = 0;//被勾选上的一级节点个数
            let disabled_count = 0;//置灰的一级节点个数
            let indeterminate_flag = false;//有没有一级节点处于半选状态
            //遍历所有一级节点
            for(let i=0;i<this.steps.length;i++){
                if(this.$refs.casetree.getNode(this.steps[i]).disabled==true){
                    disabled_count += 1;//如果有置灰的节点，置灰变量加1
                }
                if(this.$refs.casetree.getNode(this.steps[i]).checked==true){
                    checked_count += 1;//如果有勾选的节点，勾选变量加1
                }
                if(this.$refs.casetree.getNode(this.steps[i]).indeterminate==true){
                    indeterminate_flag = true;//如果有半选的节点，半选变量设为true
                }
            }

            if(checked_count==0){
                this.isIndeterminate = false;
                this.new_task_form.case_checkAll = false;//如果勾选的一级节点数为0，则设置全选按钮样式不为半选样式，全选的值为false

                if(indeterminate_flag==true){//如果下面有半选的，设置全选按钮的样式为半选样式
                    this.isIndeterminate = true;
                    this.new_task_form.case_checkAll = false;
                }

            }
            else if((checked_count+disabled_count)==this.steps.length){//如果树上勾上的和置灰的加起来等于tree上data的长度，设置全选按钮样式不为半选样式，全选值为true
                this.isIndeterminate = false;
                this.new_task_form.case_checkAll = true;

            }
            else{//上面条件不满足，则说明没有全部勾上，设置样式为半选，全选值为false
                this.isIndeterminate = true;
                this.new_task_form.case_checkAll = false;

            }
            return;

        },

  handleCheckAllChange(val) {
    this.isIndeterminate = false; // 设置全选按钮样式不为半选
    if (val) { // 当前值是全选
        for (let i = 0; i < this.steps.length; i++) {
            if (!this.$refs.casetree.getNode(this.steps[i]).disabled) {
                this.$refs.casetree.setChecked(this.steps[i].id, true, true);
            }
        }
    } else { // 当前值不是全选
        this.$refs.casetree.setCheckedKeys([]); // 清空勾选状态
    }
  },

  async makeOts(type){
    const params = {type:type,data:this.$refs.casetree.getCheckedNodes()}
    if (type!=='sync'){
      const response = await this.$api.batchTaskSceneStep(params);
        if (response.status === 200) {
          ElNotification({
              duration: 500,
              title: '批量变更成功',
              type: 'success',
            });
          this.handleClose();
        }}
    else {
      const response = await this.$api.batchSaveApiStep(this.$refs.casetree.getCheckedNodes());
      if (response.status === 200) {
        ElNotification({
            duration: 500,
            title: '批量同步成功',
            type: 'success',
          });
        this.handleClose();
      }

    }
  },

  async debugScence(){
    if (!this.scenceId) {
      this.$message({
        type: 'warning',
        message: '请先选择场景',
        duration: 2000
      });
      return;
    }

    if (!this.steps || this.steps.length === 0) {
      this.$message({
        type: 'warning',
        message: '当前场景没有步骤，无法调试',
        duration: 2000
      });
      return;
    }

    // 验证场景配置
    const validationResult = this.validateScenario();
    if (!validationResult.isValid) {
      this.$confirm(
        `场景配置存在问题：\n${validationResult.errors.join('\n')}\n\n是否继续调试？`,
        '场景验证',
        {
          confirmButtonText: '继续调试',
          cancelButtonText: '取消',
          type: 'warning',
          customClass: 'debug-confirm-dialog'
        }
      ).then(() => {
        this.startDebugMode();
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消调试'
        });
      });
    } else {
      this.startDebugMode();
    }
  },

  validateScenario() {
    const errors = [];
    let isValid = true;

    // 检查环境配置
    if (!this.scenceData.env) {
      errors.push('• 未选择测试环境');
      isValid = false;
    }

    // 检查步骤配置
    this.steps.forEach((step, index) => {
      const stepInfo = step.stepInfo;
      if (!stepInfo) return;

      switch (stepInfo.type) {
        case 'api':
          if (!stepInfo.content?.url) {
            errors.push(`• 步骤${index + 1}: HTTP请求缺少URL`);
            isValid = false;
          }
          if (!stepInfo.content?.method) {
            errors.push(`• 步骤${index + 1}: HTTP请求缺少请求方法`);
            isValid = false;
          }
          break;
        case 'if':
          if (!stepInfo.content?.variable || !stepInfo.content?.JudgmentMode || !stepInfo.content?.value) {
            errors.push(`• 步骤${index + 1}: 条件控制器配置不完整`);
            isValid = false;
          }
          break;
        case 'for':
          if (stepInfo.content?.select === 'count' && !stepInfo.content?.cycleIndex) {
            errors.push(`• 步骤${index + 1}: 循环控制器缺少循环次数`);
            isValid = false;
          }
          if (stepInfo.content?.select === 'for' && (!stepInfo.content?.variable || !stepInfo.content?.variableName)) {
            errors.push(`• 步骤${index + 1}: for循环控制器配置不完整`);
            isValid = false;
          }
          break;
        case 'script':
          if (!stepInfo.script || stepInfo.script.trim() === '') {
            errors.push(`• 步骤${index + 1}: 自定义脚本内容为空`);
            isValid = false;
          }
          break;
        case 'time':
          if (!stepInfo.content?.time) {
            errors.push(`• 步骤${index + 1}: 等待控制器缺少等待时间`);
            isValid = false;
          }
          break;
      }
    });

    return { isValid, errors };
  },

  startDebugMode() {
    // 构建调试信息
    const debugInfo = this.buildDebugInfo();

    // 显示调试对话框
    this.$alert(debugInfo, '场景调试信息', {
      confirmButtonText: '开始执行调试',
      cancelButtonText: '关闭',
      showCancelButton: true,
      type: 'info',
      dangerouslyUseHTMLString: true,
      customClass: 'debug-dialog',
      width: '80%'
    }).then(() => {
      this.executeDebug();
    }).catch(() => {
      this.$message({
        type: 'info',
        message: '已关闭调试窗口'
      });
    });
  },

  buildDebugInfo() {
    const env = this.testEnvs.find(e => e.id === this.scenceData.env);
    const envName = env ? env.name : '未知环境';

    return `
      <div style="max-height: 500px; overflow-y: auto; text-align: left;">
        <!-- 场景基本信息 -->
        <div style="margin-bottom: 20px;">
          <h3 style="color: #409eff; margin-bottom: 10px;">🎯 场景基本信息</h3>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
            <div><strong>场景名称:</strong> ${this.scenceData.name || '未命名场景'}</div>
            <div><strong>测试环境:</strong> ${envName}</div>
            <div><strong>场景权重:</strong> ${this.scenceData.weight || 1}</div>
            <div><strong>步骤数量:</strong> ${this.steps.length}</div>
            <div><strong>启用步骤:</strong> ${this.steps.filter(s => s.stepInfo?.status).length}</div>
          </div>
        </div>

        <!-- 执行步骤预览 -->
        <div style="margin-bottom: 20px;">
          <h3 style="color: #409eff; margin-bottom: 10px;">📋 执行步骤预览</h3>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
            ${this.steps.map((step, index) => this.buildStepDebugInfo(step, index)).join('')}
          </div>
        </div>

        <!-- 环境变量 -->
        ${this.buildEnvironmentInfo()}

        <!-- 调试建议 -->
        <div style="margin-bottom: 20px;">
          <h3 style="color: #409eff; margin-bottom: 10px;">💡 调试建议</h3>
          <div style="background: #fff3cd; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
            ${this.generateDebugSuggestions()}
          </div>
        </div>
      </div>
    `;
  },

  buildStepDebugInfo(step, index) {
    const stepInfo = step.stepInfo;
    if (!stepInfo) return '';

    const statusIcon = stepInfo.status ? '✅' : '❌';
    const stepNumber = index + 1;

    let stepDetails = '';
    switch (stepInfo.type) {
      case 'api':
        stepDetails = `
          <div style="margin-left: 20px; font-size: 12px; color: #666;">
            <div>方法: <code>${stepInfo.content?.method || 'GET'}</code></div>
            <div>URL: <code>${stepInfo.content?.url || '未设置'}</code></div>
            <div>权重: ${stepInfo.weight || 1}</div>
          </div>
        `;
        break;
      case 'if':
        stepDetails = `
          <div style="margin-left: 20px; font-size: 12px; color: #666;">
            <div>条件: <code>${stepInfo.content?.variable || ''} ${stepInfo.content?.JudgmentMode || ''} ${stepInfo.content?.value || ''}</code></div>
          </div>
        `;
        break;
      case 'for':
        stepDetails = `
          <div style="margin-left: 20px; font-size: 12px; color: #666;">
            <div>循环类型: ${stepInfo.content?.select === 'count' ? '次数循环' : 'for循环'}</div>
            ${stepInfo.content?.select === 'count' ?
              `<div>循环次数: ${stepInfo.content?.cycleIndex || '未设置'}</div>` :
              `<div>循环变量: ${stepInfo.content?.variableName || ''} in ${stepInfo.content?.variable || ''}</div>`
            }
            <div>循环间隔: ${stepInfo.content?.cycleInterval || 0}秒</div>
          </div>
        `;
        break;
      case 'script':
        stepDetails = `
          <div style="margin-left: 20px; font-size: 12px; color: #666;">
            <div>脚本行数: ${stepInfo.script ? stepInfo.script.split('\n').length : 0}</div>
          </div>
        `;
        break;
      case 'time':
        stepDetails = `
          <div style="margin-left: 20px; font-size: 12px; color: #666;">
            <div>等待时间: ${stepInfo.content?.time || 0}秒</div>
          </div>
        `;
        break;
    }

    return `
      <div style="margin-bottom: 10px; padding: 10px; border: 1px solid #e4e7ed; border-radius: 4px; ${stepInfo.status ? 'background: #f0f9ff;' : 'background: #fff2f0;'}">
        <div style="font-weight: 500;">
          ${statusIcon} 步骤${stepNumber}: ${this.getStepTypeName(stepInfo.type)} - ${stepInfo.name || stepInfo.content?.name || '未命名'}
        </div>
        ${stepDetails}
      </div>
    `;
  },

  buildEnvironmentInfo() {
    if (!this.scenceData.env) {
      return `
        <div style="margin-bottom: 20px;">
          <h3 style="color: #409eff; margin-bottom: 10px;">🌍 环境变量</h3>
          <div style="background: #fff2f0; padding: 15px; border-radius: 6px; border-left: 4px solid #f56c6c;">
            <div style="color: #f56c6c;">⚠️ 未选择测试环境</div>
          </div>
        </div>
      `;
    }

    return `
      <div style="margin-bottom: 20px;">
        <h3 style="color: #409eff; margin-bottom: 10px;">🌍 环境变量</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
          <div style="margin-bottom: 10px;">
            <strong>当前环境:</strong> ${this.testEnvs.find(e => e.id === this.scenceData.env)?.name || '未知'}
          </div>
          <div style="font-size: 12px; color: #666;">
            <div>调试将使用当前环境的配置变量</div>
            <div>点击环境信息按钮可查看详细变量配置</div>
          </div>
        </div>
      </div>
    `;
  },

  generateDebugSuggestions() {
    const suggestions = [];

    // 检查禁用的步骤
    const disabledSteps = this.steps.filter(s => !s.stepInfo?.status).length;
    if (disabledSteps > 0) {
      suggestions.push(`有 ${disabledSteps} 个步骤被禁用，调试时将跳过这些步骤`);
    }

    // 检查API步骤
    const apiSteps = this.steps.filter(s => s.stepInfo?.type === 'api').length;
    if (apiSteps > 0) {
      suggestions.push(`包含 ${apiSteps} 个HTTP请求，建议先测试单个接口的连通性`);
    }

    // 检查循环步骤
    const loopSteps = this.steps.filter(s => s.stepInfo?.type === 'for').length;
    if (loopSteps > 0) {
      suggestions.push(`包含 ${loopSteps} 个循环控制器，注意循环次数设置避免过度执行`);
    }

    // 检查脚本步骤
    const scriptSteps = this.steps.filter(s => s.stepInfo?.type === 'script').length;
    if (scriptSteps > 0) {
      suggestions.push(`包含 ${scriptSteps} 个自定义脚本，确保脚本语法正确`);
    }

    if (suggestions.length === 0) {
      suggestions.push('场景配置良好，可以开始调试');
    }

    return suggestions.map(s => `• ${s}`).join('<br>');
  },

  getStepTypeName(type) {
    const typeNames = {
      'api': 'HTTP请求',
      'if': '条件控制器',
      'for': '循环控制器',
      'script': '自定义脚本',
      'py': '导入PY脚本',
      'time': '等待控制器'
    };
    return typeNames[type] || '未知类型';
  },

  async executeDebug() {
    try {
      this.$message({
        type: 'info',
        message: '开始执行场景调试...',
        duration: 2000
      });


      // 调用后端调试API
      const params = {
        scene_id: this.scenceId,
        env_id: this.scenceData.env,
        debug_mode: true
      };

      const response = await this.$api.debugScenario(params);

      if (response.status === 200) {
        // 显示调试结果
        this.showDebugResults(response.data);
      }
    } catch (error) {
      console.error('调试执行错误:', error);
      this.$message({
        type: 'error',
        message: '调试执行失败: ' + (error.response?.data?.message || error.message || '未知错误'),
        duration: 3000
      });
    }
  },

  showDebugResults(responseData) {
    // 构建完整的结果对象，包含顶层统计数据
    const results = {
      // 从顶层获取统计数据（后端提供的兼容字段）
      total_steps: responseData.total_steps || responseData.totalSteps || 0,
      successful_steps: responseData.successful_steps || responseData.successfulSteps || 0,
      failed_steps: responseData.failed_steps || responseData.failedSteps || 0,
      execution_time: responseData.execution_time || responseData.executionTime || 0,
      success_rate: responseData.success_rate || responseData.successRate || 0,
      // 从debug_results中获取详细数据
      step_results: responseData.debug_results?.step_results || [],
      error_summary: responseData.debug_results?.error_summary || [],
      overall_result: responseData.overall_result || responseData.overallResult || 'unknown'
    };
    
    const resultsHtml = this.buildDebugResults(results);

    this.$alert(resultsHtml, '调试执行结果', {
      confirmButtonText: '关闭',
      type: 'info',
      dangerouslyUseHTMLString: true,
      customClass: 'debug-results-dialog',
      width: '85%'
    });
  },

  buildDebugResults(results) {
    if (!results) {
      return '<div style="text-align: center; color: #999;">暂无调试结果</div>';
    }

    return `
      <div style="max-height: 600px; overflow-y: auto; text-align: left;">
        <div style="margin-bottom: 20px;">
          <h3 style="color: #409eff; margin-bottom: 10px;">📊 执行概览</h3>
          <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px;">
            <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;">
              <div style="font-size: 18px; font-weight: bold; color: #409eff;">${results.total_steps || results.totalSteps || 0}</div>
              <div style="font-size: 12px; color: #666;">总步骤</div>
            </div>
            <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;">
              <div style="font-size: 18px; font-weight: bold; color: #67c23a;">${results.successful_steps || results.successfulSteps || results.success_steps || 0}</div>
              <div style="font-size: 12px; color: #666;">成功</div>
            </div>
            <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;">
              <div style="font-size: 18px; font-weight: bold; color: #f56c6c;">${results.failed_steps || results.failedSteps || 0}</div>
              <div style="font-size: 12px; color: #666;">失败</div>
            </div>
            <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; text-align: center;">
              <div style="font-size: 18px; font-weight: bold; color: #e6a23c;">${((results.execution_time || results.executionTime || results.duration || 0) * 1000).toFixed(0)}ms</div>
              <div style="font-size: 12px; color: #666;">耗时</div>
            </div>
          </div>
        </div>

        ${results.step_results ? this.buildStepResults(results.step_results) : ''}

        ${results.logs ? this.buildDebugLogs(results.logs) : ''}
      </div>
    `;
  },

  buildStepResults(stepResults) {
    return `
      <div style="margin-bottom: 20px;">
        <h3 style="color: #409eff; margin-bottom: 10px;">🎯 步骤执行结果</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
          ${stepResults.map((result, index) => `
            <div style="margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid ${result.status === 'success' ? '#67c23a' : '#f56c6c'};">
              <div style="font-weight: 500; margin-bottom: 5px;">
                ${result.status === 'success' ? '✅' : result.status === 'failed' ? '❌' : '⏭️'} 步骤${index + 1}: ${result.step_name || '未命名'}
              </div>
              <div style="font-size: 12px; color: #666;">
                <div>执行时间: ${((result.execution_time || result.duration || 0) * 1000).toFixed(0)}ms</div>
                <div>状态: ${result.status || '未知'}</div>
                ${result.message ? `<div>信息: ${result.message}</div>` : ''}
                ${result.details && result.details.status_code ? `<div>响应码: ${result.details.status_code}</div>` : ''}
                ${result.status === 'failed' && result.message ? `<div style="color: #f56c6c;">错误: ${result.message}</div>` : ''}
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  },

  buildDebugLogs(logs) {
    return `
      <div style="margin-bottom: 20px;">
        <h3 style="color: #409eff; margin-bottom: 10px;">📝 执行日志</h3>
        <div style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
          ${logs.map(log => `<div style="margin-bottom: 2px;">[${log.timestamp}] ${log.level}: ${log.message}</div>`).join('')}
        </div>
      </div>
    `;
  },

  async clickRun() {
    if (!this.envId) {
      this.$message({
        type: 'warning',
        message: '当前未选中执行环境!',
        duration: 1000
      });
      return
    }
    const params = { taskId: this.perfTask.id, env: this.envId };
    const response = await this.$api.runTask(this.perfTask.id, params);

    if (response.status === 200) {
      ElNotification({
        title: '任务已启动',
        message: '请前往报告列表查看结果',
        type: 'success',
        duration: 3000,
        showClose: true,
        position: 'top-right',
      });
    }
  }


	},
  computed: {
    ...mapState(['pro','perfTask','envId','testEnvs']),
    defaultProps() {
      return {
        children: 'children',
        label: 'name',
      }
    },
    selectTaskType: {
      get() {
        return this.form.taskType.toString();
      },
      set(value) {
        this.form.taskType = value;
      }
    },
    username() {
			return window.sessionStorage.getItem('username');
		},
	},

  mounted() {

  },

  watch: {

  },
	created() {
    this.getScenes();
    setTimeout(() => {
        this.getTaskScenceStep(this.scenceId);
    }, 500);
  },

};
</script>

<style scoped>
.box{
  padding:5px 5px 5px 5px;
  background:#f5f7f9;
}
.el-icon-caret-right:before {
    padding-right: 3px;
}

.projectInfo{
  justify-content: flex-end;
  align-items: center;
  display: flex;
  height: 85px;
}

.task-button{
  color:black;
  border: none;
  outline: none;
  font-size: 17px;
  padding-left: 5px;
}
.right-info {
  margin-right: 10px;
  font-size: 14px;
}

.tree-component {
  padding: 10px;
  box-shadow: 5px 0 5px rgba(0, 0, 0, 0.06); /* 添加此样式来设置阴影 */
  background:#ffffff;
}
.filter-tree {
  margin-top: 10px;
  padding-right:0px;
}
.tree-component[data-v-1b4274da] {
    width: 300px;
    padding-right: 0px;
    box-shadow: 5px 0 5px rgba(0, 0, 0, 0.06);
}
.node-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 16px;

}
.el-icon {
  margin-left: 10px;
  transition: transform 0.3s ease;
}
.el-icon:hover {
  transform: scale(1.2);
}
.bold-node {
  font-weight: bold;
}
.el-tag {
  color: #ffffff;
  width: 80px;
  height: 30px;
  text-align: center;
  font-size: 13px;
  line-height: 30px;

}

.title {
    padding: 9px;
    border: 1px solid var(--el-card-border-color);
    background-color: #fff;
    overflow: hidden;
}

.el-dropdown-menu__item {
  color: #606266;
  &:hover {
    background-color: #ebf5ff;
  }
}
.add-btns {
	text-align: center;
}

.step-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  font-weight: bold;
}

/* 调试对话框样式 */
:deep(.debug-dialog) {
  .el-message-box {
    width: 80% !important;
    max-width: 1000px;
  }

  .el-message-box__content {
    max-height: 600px;
    overflow-y: auto;
    padding: 20px !important;
  }

  .el-message-box__message {
    margin: 0 !important;
  }
}

:deep(.debug-confirm-dialog) {
  .el-message-box {
    width: 500px !important;
  }

  .el-message-box__message {
    white-space: pre-line !important;
    line-height: 1.6 !important;
  }
}

:deep(.debug-results-dialog) {
  .el-message-box {
    width: 85% !important;
    max-width: 1200px;
  }

  .el-message-box__content {
    max-height: 700px;
    overflow-y: auto;
    padding: 20px !important;
  }

  .el-message-box__message {
    margin: 0 !important;
  }
}

/* 调试结果HTML内容样式 */
:deep(.debug-dialog) h3,
:deep(.debug-results-dialog) h3 {
  margin: 0 0 10px 0 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}

:deep(.debug-dialog) code,
:deep(.debug-results-dialog) code {
  background: #f5f5f5 !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'Courier New', monospace !important;
  font-size: 11px !important;
}

/* 操作按钮区域样式 */
.action-buttons-group {
  display: flex;
  gap: 15px;
  align-items: center;
}

.env-selector {
  display: flex;
  align-items: center;
  margin-right: 5px;
}

.icon-button {
  margin-right: 5px;
}


.env-select :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  border-radius: 4px;
}

.env-select :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.operation-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 5px;
  border-radius: 4px;
  padding: 8px 15px;
  transition: all 0.3s;
  border: none;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.batch-button {
  background: linear-gradient(to right, #909399, #a6a9ad);
  color: white;
}

.action-button:nth-child(2) {
  background: linear-gradient(to right, #e6a23c, #f0c78a);
  color: white;
}

.action-button:nth-child(3) {
  background: linear-gradient(to right, #409eff, #53a8ff);
  color: white;
}

.action-button:nth-child(4) {
  background: linear-gradient(to right, #67c23a, #85ce61);
  color: white;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  filter: brightness(1.05);
}

.action-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  transition: all 0.2s;
}

.el-dropdown-menu__item:hover {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 场景树操作按钮样式 */
.tree-actions {
  display: flex;
  gap: 5px;
}

.tree-action-btn {
  padding: 4px;
  transform: scale(0.8);
  transition: all 0.3s;
  opacity: 0.8;
  border: none;
}

.edit-btn {
  background: linear-gradient(to right, #409eff, #53a8ff);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.delete-btn {
  background: linear-gradient(to right, #f56c6c, #f78989);
  box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
}

.tree-action-btn:hover {
  transform: scale(0.9);
  opacity: 1;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.tree-action-btn .el-icon {
  margin-left: 0;
}

/* 优化场景树样式 */
.filter-tree {
  margin-top: 10px;
  padding-right: 0px;
  font-size: 14px;
}

.filter-tree :deep(.el-tree-node__content) {
  height: 36px;
  border-radius: 4px;
  margin-bottom: 3px;
  transition: all 0.2s;
}

.filter-tree :deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

.filter-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
}

.bold-node {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

/* 执行任务按钮样式 */
.run-task-button {
  font-weight: 600;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
  background: linear-gradient(to right, #67c23a, #4CCB7E);
  border: none;
  box-shadow: 0 4px 12px rgba(76, 203, 126, 0.3);
}

.run-task-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(76, 203, 126, 0.4);
}

.run-task-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(76, 203, 126, 0.3);
}

/* 添加场景按钮样式 */
.add-scene-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 4px;
  transition: all 0.3s;
  font-weight: 500;
  background: linear-gradient(to right, #409eff, #53a8ff);
  border: none;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
  margin-bottom: 15px;
  padding: 10px 0;
  font-size: 14px;
}

.add-scene-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.3);
  background: linear-gradient(to right, #53a8ff, #66b1ff);
}

.add-scene-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.search-container {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.search-input :deep(.el-input__inner) {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  padding: 8px 12px;
  font-size: 14px;
  color: #303133;
  background-color: #f5f7fa;
}

.search-input :deep(.el-input__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.search-btn {
  border-radius: 4px;
  background: linear-gradient(to right, #409eff, #53a8ff);
  border: none;
  padding: 8px 12px;
  font-size: 14px;
  color: white;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
  transition: all 0.3s;
}

.search-btn:hover {
  background: linear-gradient(to right, #53a8ff, #66b1ff);
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.3);
}

.search-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

/* 批量操作弹窗按钮样式 */
.add-btns {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.drawer-action-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  border-radius: 4px;
  padding: 10px 20px;
  transition: all 0.3s;
  font-weight: 500;
  border: none;
  color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.drawer-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  filter: brightness(1.05);
}

.drawer-action-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enable-btn {
  background: linear-gradient(to right, #67c23a, #85ce61);
}

.disable-btn {
  background: linear-gradient(to right, #909399, #a6a9ad);
}

.delete-btn {
  background: linear-gradient(to right, #f56c6c, #f78989);
}

.sync-btn {
  background: linear-gradient(to right, #e6a23c, #f0c78a);
}

.cancel-btn {
  background: linear-gradient(to right, #909399, #a6a9ad);
}

/* 新增优化的任务卡片样式 */
.task-card {
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: none;
  transition: all 0.3s ease;
}

.task-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.task-card :deep(.el-card__body) {
  padding: 0;
}

.task-header {
  display: flex;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(to right, #ffffff, #f9fafc);
  border-bottom: 1px solid #ebeef5;
}

.task-info {
  flex: 1;
}

.task-navigation {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.back-button {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: #606266;
  font-weight: 500;
  padding: 0;
  margin-right: 10px;
  transition: all 0.2s;
}

.back-button:hover {
  color: #409EFF;
  transform: translateX(-2px);
}

.back-button .el-icon {
  margin-right: 4px;
  font-size: 16px;
}

.task-title {
  display: flex;
  align-items: center;
}

.title-label {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
}

.task-name-button {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding: 0;
  margin-right: 10px;
  transition: all 0.2s;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

.task-name-button:hover {
  color: #409EFF;
}

.edit-icon {
  margin-left: 6px;
  font-size: 14px;
  color: #409EFF;
  transition: transform 0.2s;
}

.task-name-button:hover .edit-icon {
  transform: scale(1.2);
}

.task-type-tag {
  font-size: 12px;
  padding: 0 10px;
  height: 24px;
  line-height: 24px;
  border-radius: 12px;
  font-weight: 500;
}

.task-details {
  display: flex;
  flex-wrap: wrap;
  margin-top: 6px;
  padding-left: 2px;
}

.detail-item {
  margin-right: 24px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 13px;
  color: #909399;
  margin-right: 6px;
}

.detail-value {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.description {
  flex-basis: 100%;
  margin-top: 4px;
}

.description .detail-value {
  color: #606266;
  max-width: 500px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

</style>
