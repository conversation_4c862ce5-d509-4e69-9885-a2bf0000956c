<template>
  <div class="performance-alert-container">
    <div v-if="!pro || !pro.id" class="no-project-warning">
      <el-alert
        title="未选择项目"
        type="warning"
        description="请先选择一个项目，再使用告警功能"
        show-icon
        :closable="false"
      />
    </div>
    <AlertManager 
      v-else
      :project-id="pro.id"
      @alert-triggered="handleAlertTriggered"
      @alert-acknowledged="handleAlertAcknowledged"
      @alert-rule-created="handleAlertRuleCreated"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import AlertManager from '@/views/PerformanceTest/AlertManager.vue'
import { ElMessage, ElNotification } from 'element-plus'

export default {
  name: 'PerformanceAlert',
  components: {
    AlertManager
  },
  computed: {
    ...mapState({
      pro: state => state.pro
    })
  },
  created() {
    console.log('PerformanceAlert 组件已创建，项目ID:', this.pro?.id || '未选择项目')
  },
  mounted() {
    console.log('PerformanceAlert 组件已加载，项目ID:', this.pro?.id || '未选择项目')
    if (!this.pro || !this.pro.id) {
      ElMessage.warning('请先选择一个项目，再使用告警功能')
    }
  },
  methods: {
    handleAlertTriggered(alertData) {
      // 处理告警触发事件
      ElNotification({
        title: '告警触发',
        message: `告警 "${alertData.rule_name}" 已触发，请注意查看`,
        type: 'warning',
        duration: 8000,
        position: 'top-right'
      })
      console.log('告警已触发:', alertData)
    },
    
    handleAlertAcknowledged(alertData) {
      // 处理告警确认事件  
      ElMessage({
        type: 'success',
        message: `告警 "${alertData.rule_name}" 已确认`,
        duration: 3000
      })
      console.log('告警已确认:', alertData)
    },
    
    handleAlertRuleCreated(ruleData) {
      // 处理告警规则创建事件
      ElMessage({
        type: 'success',
        message: `新告警规则 "${ruleData.name}" 已创建`,
        duration: 3000
      })
      console.log('新告警规则已创建:', ruleData)
    }
  }
}
</script>

<style scoped>
.performance-alert-container {
  padding: 20px;
  height: 100%;
}

.no-project-warning {
  max-width: 600px;
  margin: 100px auto;
}
</style>