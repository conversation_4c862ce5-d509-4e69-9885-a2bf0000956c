<template>
  <div class="performance-baseline-container">
    <BaselineManager 
      :project-id="pro.id"
      @baseline-created="handleBaselineCreated"
      @baseline-updated="handleBaselineUpdated"
      @baseline-deleted="handleBaselineDeleted"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import BaselineManager from '@/views/PerformanceTest/BaselineManager.vue'

export default {
  name: 'PerformanceBaseline',
  components: {
    BaselineManager
  },
  computed: {
    ...mapState({
      pro: state => state.pro
    })
  },
  methods: {
    handleBaselineCreated(baselineData) {
      // 处理基准线创建事件
      this.$message({
        type: 'success',
        message: `基准线创建成功: ${baselineData.name}`,
        duration: 3000
      })
    },
    
    handleBaselineUpdated(baselineData) {
      // 处理基准线更新事件
      this.$message({
        type: 'success',
        message: `基准线更新成功: ${baselineData.name}`,
        duration: 3000
      })
    },
    
    handleBaselineDeleted(baselineData) {
      // 处理基准线删除事件
      this.$message({
        type: 'success',
        message: `基准线删除成功: ${baselineData.name}`,
        duration: 3000
      })
    }
  }
}
</script>

<style scoped>
.performance-baseline-container {
  padding: 20px;
  height: 100%;
}
</style>