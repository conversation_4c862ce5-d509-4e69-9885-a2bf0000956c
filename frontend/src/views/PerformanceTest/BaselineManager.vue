<template>
  <div class="baseline-manager">
    <el-card class="box-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon"><TrendCharts /></el-icon>
            <span class="header-title">基准线管理</span>
          </div>
          <el-button type="primary" size="small" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            创建基准线
          </el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="基准线名称">
            <el-input 
              v-model="searchForm.name" 
              placeholder="请输入基准线名称" 
              clearable 
              size="small"
              prefix-icon="Search"
              style="width: 220px;">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="loadBaselines">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button size="small" @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 基准线列表 -->
      <el-table
        :data="baselineList"
        v-loading="tableLoading"
        empty-text="暂无基准线数据"
        @selection-change="handleSelectionChange"
        border
        stripe
        highlight-current-row
        class="baseline-table">
        
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="基准线名称" prop="name" min-width="200">
          <template #default="scope">
            <div class="baseline-name">
              <el-icon><TrendCharts /></el-icon>
              <span class="baseline-name-text">{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="描述" prop="description" min-width="200" show-overflow-tooltip />
        
        <el-table-column label="关联任务" prop="task_name" min-width="150">
          <template #default="scope">
            <div class="task-name">
              <el-icon><Document /></el-icon>
              <span>{{ scope.row.task_name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="基准指标" min-width="300">
          <template #default="scope">
            <div class="metrics-display">
              <el-tooltip content="平均响应时间" placement="top" effect="light">
                <el-tag size="small" class="metric-tag" effect="plain">
                  <el-icon><Timer /></el-icon>
                  {{ scope.row.avg_response_time }}ms
                </el-tag>
              </el-tooltip>
              <el-tooltip content="每秒事务数" placement="top" effect="light">
                <el-tag size="small" type="success" class="metric-tag" effect="plain">
                  <el-icon><PieChart /></el-icon>
                  {{ scope.row.avg_tps }} TPS
                </el-tag>
              </el-tooltip>
              <el-tooltip content="成功率" placement="top" effect="light">
                <el-tag size="small" type="warning" class="metric-tag" effect="plain">
                  <el-icon><CircleCheckFilled /></el-icon>
                  {{ scope.row.success_rate }}%
                </el-tag>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" prop="create_time" width="180">
          <template #default="scope">
            <div class="time-display">
              <el-icon><Calendar /></el-icon>
              <span>{{ formatTime(scope.row.create_time) }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="创建人" prop="creator" width="100">
          <template #default="scope">
            <div class="creator-display">
              <el-icon><User /></el-icon>
              <span>{{ scope.row.creator }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.is_active ? 'success' : 'info'" 
              size="small"
              effect="dark">
              {{ scope.row.is_active ? '活跃' : '非活跃' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-tooltip content="查看详情" placement="top">
                <el-button type="primary" size="small" circle @click="viewBaseline(scope.row)">
                <el-icon><View /></el-icon>
              </el-button>
              </el-tooltip>
              <el-tooltip content="编辑基准线" placement="top">
                <el-button type="warning" size="small" circle @click="editBaseline(scope.row)">
                <el-icon><Edit /></el-icon>
              </el-button>
              </el-tooltip>
              <el-tooltip content="删除基准线" placement="top">
                <el-button type="danger" size="small" circle @click="deleteBaseline(scope.row)">
                <el-icon><Delete /></el-icon>
              </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          @size-change="loadBaselines"
          @current-change="loadBaselines"
        />
      </div>
    </el-card>

    <!-- 创建/编辑基准线对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingBaseline ? '编辑基准线' : '创建基准线'"
      width="650px"
      @close="resetForm"
      destroy-on-close>
      
      <el-form :model="baselineForm" :rules="formRules" ref="baselineFormRef" label-width="120px" class="baseline-form">
        <el-form-item label="基准线名称" prop="name">
          <el-input 
            v-model="baselineForm.name" 
            placeholder="请输入基准线名称"
            prefix-icon="Document" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="baselineForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入基准线描述" />
        </el-form-item>
        
        <el-form-item label="关联任务" prop="task_id" v-if="!editingBaseline">
          <el-select 
            v-model="baselineForm.task_id" 
            placeholder="请选择关联的性能任务"
            filterable
            style="width: 100%"
            @change="onTaskChange">
            <el-option 
              v-for="task in taskList" 
              :key="task.id" 
              :label="task.taskName" 
              :value="task.id" />
          </el-select>
          <div class="form-help-text" v-if="baselineForm.task_id">
            <el-icon><InfoFilled /></el-icon> 
            <span>如不填写指标值，将自动使用该任务的最近一次报告数据作为基准线</span>
          </div>
        </el-form-item>
        
        <el-divider content-position="center">
          <div style="display: flex; align-items: center; gap: 10px;">
            <span>性能指标</span>
            <el-button v-if="lastTaskReport && !editingBaseline" type="text" @click="fillMetricsFromReport(lastTaskReport)">
              <el-icon><Timer /></el-icon> 从最新报告填充
            </el-button>
          </div>
        </el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="平均响应时间" prop="avg_response_time">
              <el-input-number 
                v-model="baselineForm.avg_response_time" 
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%">
                <template #append>ms</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平均TPS" prop="avg_tps">
              <el-input-number 
                v-model="baselineForm.avg_tps" 
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%">
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="成功率" prop="success_rate">
              <el-input-number 
                v-model="baselineForm.success_rate" 
                :min="0"
                :max="100"
                :precision="2"
                controls-position="right"
                style="width: 100%">
                <template #append>%</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="CPU使用率" prop="avg_cpu">
              <el-input-number 
                v-model="baselineForm.avg_cpu" 
                :min="0"
                :max="100"
                :precision="2"
                controls-position="right"
                style="width: 100%">
                <template #append>%</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="内存使用率" prop="avg_memory">
              <el-input-number 
                v-model="baselineForm.avg_memory" 
                :min="0"
                :max="100"
                :precision="2"
                controls-position="right"
                style="width: 100%">
                <template #append>%</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否激活">
              <el-switch 
                v-model="baselineForm.is_active"
                active-text="激活"
                inactive-text="非激活"
                inline-prompt>
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            {{ editingBaseline ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 基准线详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="基准线详情"
      width="750px"
      destroy-on-close>
      
      <div v-if="selectedBaseline" class="baseline-detail">
        <el-row :gutter="20" class="detail-header">
          <el-col :span="16">
            <div class="detail-title">
              <el-icon><TrendCharts /></el-icon>
              <h3>{{ selectedBaseline.name }}</h3>
              <el-tag :type="selectedBaseline.is_active ? 'success' : 'info'" class="status-tag">
                {{ selectedBaseline.is_active ? '活跃' : '非活跃' }}
              </el-tag>
            </div>
            <div class="detail-desc">{{ selectedBaseline.description }}</div>
          </el-col>
          <el-col :span="8">
            <div class="detail-info">
              <div class="info-item">
                <el-icon><User /></el-icon>
                <span>创建人: {{ selectedBaseline.creator }}</span>
              </div>
              <div class="info-item">
                <el-icon><Calendar /></el-icon>
                <span>创建时间: {{ formatTime(selectedBaseline.create_time) }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <el-divider content-position="center">性能指标</el-divider>
        
        <div class="metrics-cards">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card shadow="hover" class="metric-card response-time">
                <div class="metric-title">
                  <el-icon><Timer /></el-icon>
                  <span>平均响应时间</span>
                </div>
                <div class="metric-value">{{ selectedBaseline.avg_response_time }} <span class="unit">ms</span></div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover" class="metric-card tps">
                <div class="metric-title">
                  <el-icon><PieChart /></el-icon>
                  <span>平均TPS</span>
                </div>
                <div class="metric-value">{{ selectedBaseline.avg_tps }}</div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover" class="metric-card success-rate">
                <div class="metric-title">
                  <el-icon><CircleCheckFilled /></el-icon>
                  <span>成功率</span>
                </div>
                <div class="metric-value">{{ selectedBaseline.success_rate }}<span class="unit">%</span></div>
              </el-card>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-card shadow="hover" class="metric-card cpu">
                <div class="metric-title">
                  <el-icon><CPU /></el-icon>
                  <span>CPU使用率</span>
                </div>
                <div class="metric-value">{{ selectedBaseline.avg_cpu }}<span class="unit">%</span></div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="hover" class="metric-card memory">
                <div class="metric-title">
                  <el-icon><Coin /></el-icon>
                  <span>内存使用率</span>
                </div>
                <div class="metric-value">{{ selectedBaseline.avg_memory }}<span class="unit">%</span></div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Search, Refresh, TrendCharts, View, Edit, Delete, 
  Timer, PieChart, CircleCheckFilled, Calendar, User, CPU, Coin, Document,
  InfoFilled
} from '@element-plus/icons-vue'

export default {
  name: 'BaselineManager',
  components: {
    Plus, Search, Refresh, TrendCharts, View, Edit, Delete, 
    Timer, PieChart, CircleCheckFilled, Calendar, User,  Coin, Document,
    InfoFilled
  },
  props: {
    projectId: {
      type: [String, Number],
      required: true
    }
  },
  watch: {
    // 监听对话框打开状态，加载任务列表
    showCreateDialog(newVal) {
      if (newVal && !this.editingBaseline) {
        this.loadTasks()
      }
    }
  },
  data() {
    return {
      baselineList: [],
      tableLoading: false,
      submitLoading: false,
      showCreateDialog: false,
      showDetailDialog: false,
      editingBaseline: null,
      selectedBaseline: null,
      selectedRows: [],
      taskList: [], // 任务列表
      lastTaskReport: null, // 任务的最新报告数据
      
      searchForm: {
        name: ''
      },
      
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },
      
      baselineForm: {
        name: '',
        description: '',
        task_id: null, // 添加任务ID字段
        avg_response_time: 0,
        avg_tps: 0,
        success_rate: 0,
        avg_cpu: 0,
        avg_memory: 0,
        is_active: true
      },
      
      formRules: {
        name: [
          { required: true, message: '请输入基准线名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        task_id: [
          { required: true, message: '请选择关联任务', trigger: 'change' }
        ],
        avg_response_time: [
          { required: true, message: '请输入平均响应时间', trigger: 'blur' },
          { type: 'number', message: '平均响应时间必须是数字', trigger: 'blur' }
        ],
        avg_tps: [
          { required: true, message: '请输入平均TPS', trigger: 'blur' },
          { type: 'number', message: '平均TPS必须是数字', trigger: 'blur' }
        ],
        success_rate: [
          { required: true, message: '请输入成功率', trigger: 'blur' },
          { type: 'number', message: '成功率必须是数字', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.loadBaselines()
  },
  methods: {
    // 加载性能任务列表
    async loadTasks() {
      try {
        const response = await this.$api.getPerformanceTasks({
          project_id: this.projectId,
          no_page: true // 不分页，获取所有任务
        })
        
        if (response.status === 200) {
          this.taskList = response.data || []
        }
      } catch (error) {
        console.error('加载任务列表失败:', error)
        ElMessage.error('加载任务列表失败: ' + (error.message || '网络错误'))
        this.taskList = []
      }
    },
    
    // 加载基准线列表
    async loadBaselines() {
      this.tableLoading = true
      try {
        const params = {
          project_id: this.projectId,
          page: this.pagination.current,
          page_size: this.pagination.size,
          name: this.searchForm.name || undefined
        }
        
        const response = await this.$api.getBaselines(params)
        
        if (response.status === 200) {
          let data = response.data || {}
          
          // 处理后端标准分页格式
          if (data.results && Array.isArray(data.results)) {
            this.baselineList = data.results
            this.pagination.total = data.count || data.results.length
          } 
          // 处理不同的数据结构 - 为了向后兼容
          else if (Array.isArray(data)) {
            this.baselineList = data
            this.pagination.total = data.length
          } else if (data.baselines && Array.isArray(data.baselines)) {
            this.baselineList = data.baselines
            this.pagination.total = data.total || data.baselines.length
          } else {
            // 其他情况，设置为空数组
            this.baselineList = []
            this.pagination.total = 0
          }
          
          // 调试日志
          console.log('获取到的基准线数据:', this.baselineList)
        }
      } catch (error) {
        console.error('加载基准线失败:', error)
        ElMessage.error('加载基准线失败: ' + (error.response?.data?.message || error.message || '网络错误'))
        // 设置默认值
        this.baselineList = []
        this.pagination.total = 0
      } finally {
        this.tableLoading = false
      }
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm.name = ''
      this.pagination.current = 1
      this.loadBaselines()
    },
    
    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    // 查看基准线
    viewBaseline(baseline) {
      this.selectedBaseline = baseline
      this.showDetailDialog = true
    },
    
    // 编辑基准线
    editBaseline(baseline) {
      this.editingBaseline = baseline
      this.baselineForm = { ...baseline }
      this.showCreateDialog = true
    },
    
    // 删除基准线
    async deleteBaseline(baseline) {
      try {
        await ElMessageBox.confirm(
          `确定要删除基准线 "${baseline.name}" 吗？`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const response = await this.$api.deleteBaseline(baseline.id)
        
        if (response.status === 204 || response.status === 200) {
          ElMessage.success('删除成功')
          this.loadBaselines()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除基准线失败:', error)
          ElMessage.error('删除失败: ' + (error.message || '未知错误'))
        }
      }
    },
    
    // 当任务选择变更时获取该任务最近的报告
    async onTaskChange(taskId) {
      if (!taskId) {
        this.lastTaskReport = null
        return
      }
      
      try {
        // 获取任务的最新报告
        const response = await this.$api.getTaskReports({
          task_id: taskId,
          page: 1,
          page_size: 1
        })
        
        if (response.status === 200 && response.data.results && response.data.results.length > 0) {
          this.lastTaskReport = response.data.results[0]
          
          // 如果性能指标都是0或空，可以自动填充最新报告的数据
          if (
            !this.baselineForm.avg_response_time && 
            !this.baselineForm.avg_tps && 
            !this.baselineForm.success_rate && 
            !this.baselineForm.avg_cpu && 
            !this.baselineForm.avg_memory
          ) {
            // 用户可能希望从报告中获取指标
            ElMessageBox.confirm(
              '是否要从最新报告中填充性能指标数据？',
              '填充指标数据',
              {
                confirmButtonText: '是',
                cancelButtonText: '否',
                type: 'info'
              }
            ).then(() => {
              // 用户确认后，填充指标数据
              this.fillMetricsFromReport(this.lastTaskReport)
            }).catch(() => {
              // 用户取消，不做操作
            })
          }
        } else {
          this.lastTaskReport = null
          ElMessage.warning('该任务没有测试报告数据')
        }
      } catch (error) {
        console.error('获取任务报告失败:', error)
        this.lastTaskReport = null
      }
    },
    
    // 从报告中填充性能指标
    fillMetricsFromReport(report) {
      if (!report) return
      
      this.baselineForm.avg_response_time = report.avgResponseTime || 0
      this.baselineForm.avg_tps = report.avgTps || 0
      this.baselineForm.success_rate = report.errorRate ? (100 - report.errorRate) : 100
      this.baselineForm.avg_cpu = report.avgCpu || 0
      this.baselineForm.avg_memory = report.avgMemory || 0
      
      ElMessage.success('已从最新报告中填充指标数据')
    },
    
    // 提交表单
    async submitForm() {
      try {
        const valid = await this.$refs.baselineFormRef.validate()
        if (!valid) return
        
        this.submitLoading = true
        
        const params = {
          ...this.baselineForm,
          project_id: this.projectId
        }
        
        // 添加task_id参数，这是必需的
        if (!this.editingBaseline) {
          // 如果没有选择任务，提示用户
          if (!this.baselineForm.task_id) {
            ElMessage.error('请选择关联的性能任务')
            this.submitLoading = false
            return
          }
          
          // 如果所有性能指标都为0，提示用户
          const allZero = 
            this.baselineForm.avg_response_time === 0 && 
            this.baselineForm.avg_tps === 0 && 
            this.baselineForm.avg_cpu === 0 && 
            this.baselineForm.avg_memory === 0
            
          if (allZero) {
            const result = await ElMessageBox.confirm(
              '您未填写任何性能指标值，系统将自动使用关联任务的最新报告数据作为基准线。是否继续？',
              '确认创建',
              {
                confirmButtonText: '创建',
                cancelButtonText: '取消',
                type: 'warning'
              }
            ).catch(() => 'cancel')
            
            if (result === 'cancel') {
              this.submitLoading = false
              return
            }
          }
        }
        
        // 调试日志：查看发送的数据
        console.log('提交基准线数据:', params)
        
        let response
        if (this.editingBaseline) {
          response = await this.$api.updateBaseline(this.editingBaseline.id, params)
        } else {
          response = await this.$api.createBaseline(params)
        }
        
        if (response.status === 200 || response.status === 201) {
          ElMessage.success(this.editingBaseline ? '更新成功' : '创建成功')
          this.showCreateDialog = false
          this.loadBaselines()
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败: ' + (error.response?.data?.message || error.message || '网络错误'))
      } finally {
        this.submitLoading = false
      }
    },
    
    // 重置表单
    resetForm() {
      this.editingBaseline = null
      this.baselineForm = {
        name: '',
        description: '',
        task_id: null,
        avg_response_time: 0,
        avg_tps: 0,
        success_rate: 0,
        avg_cpu: 0,
        avg_memory: 0,
        is_active: true
      }
      this.$refs.baselineFormRef?.resetFields()
    },
    
    // 格式化时间
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }
  }
}
</script>

<style scoped>
.baseline-manager {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-icon {
  font-size: 20px;
  color: var(--el-color-primary);
}

.header-title {
  font-size: 18px;
  font-weight: 600;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background: #f9fafc;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-form {
  margin: 0;
}

.baseline-table {
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 20px;
}

.baseline-name, .task-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.baseline-name-text {
  font-weight: 500;
}

.metrics-display {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.metric-tag {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
}

.time-display, .creator-display {
  display: flex;
  align-items: center;
  gap: 5px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 表单样式 */
.baseline-form {
  padding: 10px 20px;
}

/* 详情样式 */
.baseline-detail {
  padding: 10px 0;
}

.detail-header {
  margin-bottom: 20px;
}

.detail-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.detail-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.status-tag {
  padding: 4px 8px;
}

.detail-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  height: 100%;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
}

.metrics-cards {
  margin-top: 20px;
}

.metric-card {
  border-radius: 8px;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 15px;
}

.metric-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  margin-bottom: 15px;
}

.metric-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.unit {
  font-size: 16px;
  font-weight: normal;
  margin-left: 2px;
  color: #606266;
}

.response-time {
  border-left: 4px solid #409EFF;
}

.tps {
  border-left: 4px solid #67C23A;
}

.success-rate {
  border-left: 4px solid #E6A23C;
}

.cpu {
  border-left: 4px solid #F56C6C;
}

.memory {
  border-left: 4px solid #909399;
}

.dialog-footer {
  text-align: right;
}

.form-help-text {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  
  .el-icon {
    color: #409EFF;
  }
}
</style>