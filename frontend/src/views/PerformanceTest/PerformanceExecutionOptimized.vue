<template>
  <div class="execution-container">
    <!-- 测试配置面板 -->
    <el-card class="config-panel" shadow="hover">
      <template #header>
        <div class="panel-header">
          <h3>性能测试执行</h3>
          <el-button-group>
            <el-button 
              :type="isRunning ? 'danger' : 'primary'" 
              :loading="isStarting"
              @click="toggleTest"
              :disabled="!selectedTask">
              <el-icon><VideoPlay v-if="!isRunning" /><VideoPause v-else /></el-icon>
              {{ isRunning ? '停止测试' : '开始测试' }}
            </el-button>
            <el-button @click="showConfigDialog = true" :disabled="isRunning">
              <el-icon><Setting /></el-icon>
              配置
            </el-button>
          </el-button-group>
        </div>
      </template>

      <el-form :model="testConfig" label-width="120px" size="default">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="选择任务">
              <el-select 
                v-model="selectedTask" 
                placeholder="请选择性能任务"
                :disabled="isRunning"
                style="width: 100%">
                <el-option
                  v-for="task in tasks"
                  :key="task.id"
                  :label="task.taskName"
                  :value="task.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="测试环境">
              <el-select 
                v-model="testConfig.env_id" 
                placeholder="请选择测试环境"
                :disabled="isRunning"
                style="width: 100%">
                <el-option
                  v-for="env in environments"
                  :key="env.id"
                  :label="env.name"
                  :value="env.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行模式">
              <el-select 
                v-model="testConfig.mode" 
                :disabled="isRunning"
                style="width: 100%">
                <el-option label="单机模式" value="single"></el-option>
                <el-option label="分布式模式" value="distributed"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="并发用户数">
              <el-input-number 
                v-model="testConfig.users" 
                :min="1" 
                :max="10000"
                :disabled="isRunning"
                style="width: 100%">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="启动速率">
              <el-input-number 
                v-model="testConfig.spawn_rate" 
                :min="1" 
                :max="100"
                :disabled="isRunning"
                style="width: 100%">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="持续时间(分钟)">
              <el-input-number 
                v-model="testConfig.duration" 
                :min="1" 
                :max="1440"
                :disabled="isRunning"
                style="width: 100%">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="报告名称">
              <el-input 
                v-model="testConfig.report_name" 
                placeholder="自动生成"
                :disabled="isRunning">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 实时监控面板 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 左侧指标卡片 -->
      <el-col :span="18">
        <div class="metrics-grid">
          <el-card class="metric-card" shadow="hover">
            <div class="metric-content">
              <div class="metric-icon tps-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ currentMetrics.tps || 0 }}</div>
                <div class="metric-label">TPS</div>
              </div>
            </div>
          </el-card>

          <el-card class="metric-card" shadow="hover">
            <div class="metric-content">
              <div class="metric-icon response-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ currentMetrics.avg_response_time || 0 }}ms</div>
                <div class="metric-label">平均响应时间</div>
              </div>
            </div>
          </el-card>

          <el-card class="metric-card" shadow="hover">
            <div class="metric-content">
              <div class="metric-icon users-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ currentMetrics.users || 0 }}</div>
                <div class="metric-label">活跃用户</div>
              </div>
            </div>
          </el-card>

          <el-card class="metric-card" shadow="hover">
            <div class="metric-content">
              <div class="metric-icon error-icon" :class="getErrorRateClass(currentMetrics.error_rate)">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ (currentMetrics.error_rate || 0).toFixed(2) }}%</div>
                <div class="metric-label">错误率</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 实时图表 -->
        <el-card style="margin-top: 20px;" shadow="hover">
          <template #header>
            <div class="chart-header">
              <h4>实时性能趋势</h4>
              <el-radio-group v-model="chartType" size="small">
                <el-radio-button label="tps">TPS</el-radio-button>
                <el-radio-button label="response_time">响应时间</el-radio-button>
                <el-radio-button label="users">用户数</el-radio-button>
                <el-radio-button label="error_rate">错误率</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="chartContainer" style="height: 300px;"></div>
        </el-card>
      </el-col>

      <!-- 右侧状态面板 -->
      <el-col :span="6">
        <el-card class="status-panel" shadow="hover">
          <template #header>
            <h4>测试状态</h4>
          </template>

          <div class="status-item">
            <span class="status-label">测试状态:</span>
            <el-tag :type="getStatusType(testStatus)">{{ getStatusText(testStatus) }}</el-tag>
          </div>

          <div class="status-item">
            <span class="status-label">运行时长:</span>
            <span>{{ formatDuration(testDuration) }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">开始时间:</span>
            <span>{{ formatTime(testStartTime) }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">预计结束:</span>
            <span>{{ formatTime(estimatedEndTime) }}</span>
          </div>

          <el-divider></el-divider>

          <div class="progress-section">
            <div class="progress-label">
              测试进度
              <span class="progress-percent">{{ testProgress }}%</span>
            </div>
            <el-progress 
              :percentage="testProgress" 
              :status="testProgress === 100 ? 'success' : 'primary'"
              :stroke-width="8">
            </el-progress>
          </div>

          <el-divider></el-divider>

          <!-- 快捷操作 -->
          <div class="quick-actions">
            <el-button 
              size="small" 
              type="primary" 
              @click="viewReport"
              :disabled="!currentReportId">
              查看报告
            </el-button>
            <el-button 
              size="small" 
              type="info" 
              @click="exportResults"
              :disabled="!currentReportId">
              导出结果
            </el-button>
          </div>
        </el-card>

        <!-- 系统资源监控 -->
        <el-card style="margin-top: 20px;" shadow="hover">
          <template #header>
            <h4>系统资源</h4>
          </template>

          <div class="resource-item">
            <div class="resource-label">CPU使用率</div>
            <el-progress 
              :percentage="systemResources.cpu" 
              :status="getCpuStatus(systemResources.cpu)"
              :show-text="false"
              :stroke-width="6">
            </el-progress>
            <span class="resource-value">{{ systemResources.cpu }}%</span>
          </div>

          <div class="resource-item">
            <div class="resource-label">内存使用率</div>
            <el-progress 
              :percentage="systemResources.memory" 
              :status="getMemoryStatus(systemResources.memory)"
              :show-text="false"
              :stroke-width="6">
            </el-progress>
            <span class="resource-value">{{ systemResources.memory }}%</span>
          </div>

          <div class="resource-item">
            <div class="resource-label">网络IO</div>
            <div class="network-io">
              <span>↑ {{ formatBytes(systemResources.network_sent) }}</span>
              <span>↓ {{ formatBytes(systemResources.network_recv) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 配置对话框 -->
    <el-dialog v-model="showConfigDialog" title="高级配置" width="60%">
      <el-tabs v-model="configTab">
        <el-tab-pane label="基础配置" name="basic">
          <el-form :model="advancedConfig" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="思考时间(秒)">
                  <el-input-number v-model="advancedConfig.think_time" :min="0" :max="60"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="超时时间(秒)">
                  <el-input-number v-model="advancedConfig.timeout" :min="1" :max="300"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="重试次数">
                  <el-input-number v-model="advancedConfig.retry_count" :min="0" :max="10"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="日志级别">
                  <el-select v-model="advancedConfig.log_level" style="width: 100%">
                    <el-option label="关闭" value="off"></el-option>
                    <el-option label="错误" value="error"></el-option>
                    <el-option label="警告" value="warning"></el-option>
                    <el-option label="信息" value="info"></el-option>
                    <el-option label="调试" value="debug"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="分布式配置" name="distributed">
          <el-form :model="advancedConfig" label-width="120px">
            <el-form-item label="主节点">
              <el-select v-model="advancedConfig.master_server" style="width: 100%">
                <el-option
                  v-for="server in servers"
                  :key="server.id"
                  :label="`${server.name} (${server.host_ip})`"
                  :value="server.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="工作节点">
              <el-select v-model="advancedConfig.worker_servers" multiple style="width: 100%">
                <el-option
                  v-for="server in servers"
                  :key="server.id"
                  :label="`${server.name} (${server.host_ip})`"
                  :value="server.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="告警设置" name="alerts">
          <el-form :model="advancedConfig" label-width="120px">
            <el-form-item label="响应时间告警">
              <el-input-number v-model="advancedConfig.response_time_threshold" :min="0">
                <template #append>ms</template>
              </el-input-number>
            </el-form-item>
            <el-form-item label="错误率告警">
              <el-input-number v-model="advancedConfig.error_rate_threshold" :min="0" :max="100">
                <template #append>%</template>
              </el-input-number>
            </el-form-item>
            <el-form-item label="TPS告警">
              <el-input-number v-model="advancedConfig.tps_threshold" :min="0">
                <template #append>TPS</template>
              </el-input-number>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="saveAdvancedConfig">保存配置</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { VideoPlay, VideoPause, Setting, TrendCharts, Timer, User, WarningFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

export default {
  name: 'PerformanceExecutionOptimized',
  components: {
    VideoPlay,
    VideoPause, 
    Setting,
    TrendCharts,
    Timer,
    User,
    WarningFilled
  },
  data() {
    return {
      // 测试配置
      selectedTask: null,
      testConfig: {
        env_id: null,
        mode: 'single',
        users: 10,
        spawn_rate: 2,
        duration: 5,
        report_name: ''
      },
      
      // 高级配置
      advancedConfig: {
        think_time: 1,
        timeout: 30,
        retry_count: 0,
        log_level: 'info',
        master_server: null,
        worker_servers: [],
        response_time_threshold: 1000,
        error_rate_threshold: 5,
        tps_threshold: 100
      },
      
      // 状态管理
      isRunning: false,
      isStarting: false,
      testStatus: 'idle',
      testStartTime: null,
      testDuration: 0,
      testProgress: 0,
      currentReportId: null,
      
      // 实时数据
      currentMetrics: {
        tps: 0,
        avg_response_time: 0,
        users: 0,
        error_rate: 0
      },
      systemResources: {
        cpu: 0,
        memory: 0,
        network_sent: 0,
        network_recv: 0
      },
      
      // 图表
      chart: null,
      chartType: 'tps',
      chartData: {
        times: [],
        tps: [],
        response_time: [],
        users: [],
        error_rate: []
      },
      
      // 数据源
      tasks: [],
      environments: [],
      servers: [],
      
      // UI状态
      showConfigDialog: false,
      configTab: 'basic',
      
      // WebSocket
      wsConnection: null,
      updateTimer: null,
      
      // GUI相关
      guiUrl: '',
      webPort: 8089
    }
  },
  computed: {
    estimatedEndTime() {
      if (!this.testStartTime || !this.testConfig.duration) return null
      const startTime = new Date(this.testStartTime)
      return new Date(startTime.getTime() + this.testConfig.duration * 60 * 1000)
    }
  },
  async mounted() {
    await this.loadInitialData()
    this.initChart()
    this.startUpdateTimer()
  },
  beforeUnmount() {
    this.cleanup()
  },
  methods: {
    async loadInitialData() {
      try {
        // 加载任务列表
        const tasksResponse = await this.$api.getPerformanceTasksForExecution()
        this.tasks = tasksResponse.data.results || []
        
        // 加载环境列表
        const envResponse = await this.$api.getTestEnvironments()
        this.environments = envResponse.data.results || []
        
        // 加载服务器列表
        const serversResponse = await this.$api.getServersForExecution()
        this.servers = serversResponse.data.results || []
        
      } catch (error) {
        console.error('加载初始数据失败:', error)
        this.$message.error('加载数据失败')
      }
    },
    
    async toggleTest() {
      if (this.isRunning) {
        await this.stopTest()
      } else {
        await this.startTest()
      }
    },
    
    async startTest() {
      if (!this.selectedTask) {
        this.$message.warning('请选择要执行的任务')
        return
      }
      
      this.isStarting = true
      
      try {
        const response = await this.$api.runPerformanceTestOptimized(this.selectedTask, {
          ...this.testConfig,
          ...this.advancedConfig
        })
        
        this.isRunning = true
        this.testStatus = 'running'
        this.testStartTime = new Date()
        this.currentReportId = response.data.report_id
        
        // 保存GUI信息
        if (response.data.gui_url) {
          this.guiUrl = response.data.gui_url
          this.webPort = response.data.web_port
          this.$message.success(`测试启动成功！GUI地址: ${response.data.gui_url}`)
        }
        
        // 连接WebSocket
        this.connectWebSocket()
        
        this.$message.success('性能测试已开始')
        
      } catch (error) {
        console.error('启动测试失败:', error)
        this.$message.error('启动测试失败: ' + (error.response?.data?.message || error.message))
      } finally {
        this.isStarting = false
      }
    },
    
    async stopTest() {
      try {
        if (this.selectedTask) {
          await this.$api.stopPerformanceTestOptimized(this.selectedTask)
        }
        
        this.isRunning = false
        this.testStatus = 'stopped'
        this.disconnectWebSocket()
        
        this.$message.success('性能测试已停止')
        
      } catch (error) {
        console.error('停止测试失败:', error)
        this.$message.error('停止测试失败')
      }
    },
    
    connectWebSocket() {
      if (this.wsConnection) {
        this.wsConnection.close()
      }
      
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsUrl = `${protocol}//${window.location.host}/ws/performance/monitor/${this.selectedTask}/`
      
      this.wsConnection = new WebSocket(wsUrl)
      
      this.wsConnection.onopen = () => {
        console.log('WebSocket连接已建立')
        this.wsConnection.send(JSON.stringify({ type: 'start_monitoring' }))
      }
      
      this.wsConnection.onmessage = (event) => {
        const data = JSON.parse(event.data)
        this.handleWebSocketMessage(data)
      }
      
      this.wsConnection.onerror = (error) => {
        console.error('WebSocket连接错误:', error)
      }
      
      this.wsConnection.onclose = () => {
        console.log('WebSocket连接已关闭')
        if (this.isRunning) {
          // 如果测试还在运行，尝试重连
          setTimeout(() => {
            this.connectWebSocket()
          }, 5000)
        }
      }
    },
    
    disconnectWebSocket() {
      if (this.wsConnection) {
        this.wsConnection.close()
        this.wsConnection = null
      }
    },
    
    handleWebSocketMessage(data) {
      switch (data.type) {
        case 'performance_update':
          this.updateMetrics(data.data)
          break
        case 'test_completed':
          this.handleTestCompleted(data.data)
          break
        case 'test_failed':
          this.handleTestFailed(data.error)
          break
        case 'current_status':
          this.updateCurrentStatus(data.data)
          break
      }
    },
    
    updateMetrics(data) {
      this.currentMetrics = {
        tps: data.avg_tps || 0,
        avg_response_time: data.avg_response_time || 0,
        users: data.current_users || 0,
        error_rate: data.error_rate || 0
      }
      
      // 更新图表数据
      const now = new Date().toLocaleTimeString()
      this.chartData.times.push(now)
      this.chartData.tps.push(data.avg_tps || 0)
      this.chartData.response_time.push(data.avg_response_time || 0)
      this.chartData.users.push(data.current_users || 0)
      this.chartData.error_rate.push(data.error_rate || 0)
      
      // 保持最新50个数据点
      Object.keys(this.chartData).forEach(key => {
        if (this.chartData[key].length > 50) {
          this.chartData[key].shift()
        }
      })
      
      this.updateChart()
    },
    
    updateCurrentStatus(data) {
      if (data) {
        this.currentMetrics = {
          tps: data.avg_tps || 0,
          avg_response_time: data.avg_response_time || 0,
          users: data.current_users || 0,
          error_rate: data.error_rate || 0
        }
      }
    },
    
    handleTestCompleted(data) {
      this.isRunning = false
      this.testStatus = 'completed'
      this.testProgress = 100
      this.$message.success('性能测试已完成')
      this.disconnectWebSocket()
    },
    
    handleTestFailed(error) {
      this.isRunning = false
      this.testStatus = 'failed'
      this.$message.error('性能测试失败: ' + error)
      this.disconnectWebSocket()
    },
    
    initChart() {
      this.$nextTick(() => {
        if (this.$refs.chartContainer) {
          this.chart = echarts.init(this.$refs.chartContainer)
          this.updateChart()
        }
      })
    },
    
    updateChart() {
      if (!this.chart) return
      
      const option = {
        title: {
          text: this.getChartTitle(),
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.chartData.times,
          axisLabel: {
            interval: 'auto',
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: this.getChartUnit()
        },
        series: [{
          data: this.chartData[this.chartType],
          type: 'line',
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: this.getChartColor()
          }
        }],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        }
      }
      
      this.chart.setOption(option)
    },
    
    getChartTitle() {
      const titles = {
        tps: 'TPS趋势',
        response_time: '响应时间趋势', 
        users: '用户数趋势',
        error_rate: '错误率趋势'
      }
      return titles[this.chartType] || ''
    },
    
    getChartUnit() {
      const units = {
        tps: 'TPS',
        response_time: '毫秒(ms)',
        users: '用户数',
        error_rate: '百分比(%)'
      }
      return units[this.chartType] || ''
    },
    
    getChartColor() {
      const colors = {
        tps: '#409eff',
        response_time: '#67c23a',
        users: '#e6a23c',
        error_rate: '#f56c6c'
      }
      return colors[this.chartType] || '#409eff'
    },
    
    startUpdateTimer() {
      this.updateTimer = setInterval(() => {
        if (this.isRunning && this.testStartTime) {
          this.testDuration = Math.floor((Date.now() - new Date(this.testStartTime).getTime()) / 1000)
          
          // 计算进度
          if (this.testConfig.duration) {
            const totalSeconds = this.testConfig.duration * 60
            this.testProgress = Math.min(100, (this.testDuration / totalSeconds) * 100)
          }
          
          // 如果超过预定时间，自动停止
          if (this.testProgress >= 100 && this.isRunning) {
            this.stopTest()
          }
        }
        
        // 更新系统资源（模拟数据）
        this.updateSystemResources()
      }, 1000)
    },
    
    updateSystemResources() {
      // 模拟系统资源数据更新
      this.systemResources = {
        cpu: Math.max(0, Math.min(100, this.systemResources.cpu + (Math.random() - 0.5) * 10)),
        memory: Math.max(0, Math.min(100, this.systemResources.memory + (Math.random() - 0.5) * 5)),
        network_sent: this.systemResources.network_sent + Math.random() * 1000000,
        network_recv: this.systemResources.network_recv + Math.random() * 1000000
      }
    },
    
    cleanup() {
      this.disconnectWebSocket()
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
      }
      if (this.chart) {
        this.chart.dispose()
      }
    },
    
    saveAdvancedConfig() {
      this.showConfigDialog = false
      this.$message.success('配置已保存')
    },
    
    viewReport() {
      if (this.currentReportId) {
        this.$router.push({ 
          name: 'PerformanceResult-Detail', 
          params: { id: this.currentReportId }
        })
      }
    },
    
    exportResults() {
      if (this.currentReportId) {
        this.$api.exportTaskReport(this.currentReportId)
      }
    },
    
    // 工具方法
    getStatusType(status) {
      const types = {
        idle: 'info',
        running: 'primary', 
        completed: 'success',
        failed: 'danger',
        stopped: 'warning'
      }
      return types[status] || 'info'
    },
    
    getStatusText(status) {
      const texts = {
        idle: '待运行',
        running: '运行中',
        completed: '已完成',
        failed: '失败',
        stopped: '已停止'
      }
      return texts[status] || '未知'
    },
    
    getErrorRateClass(rate) {
      if (rate > 5) return 'error-high'
      if (rate > 1) return 'error-medium'
      return 'error-low'
    },
    
    getCpuStatus(percent) {
      if (percent > 80) return 'exception'
      if (percent > 60) return 'warning'
      return 'success'
    },
    
    getMemoryStatus(percent) {
      if (percent > 85) return 'exception'
      if (percent > 70) return 'warning'
      return 'success'
    },
    
    formatDuration(seconds) {
      if (!seconds) return '0s'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      return `${hours}h ${minutes}m ${secs}s`
    },
    
    formatTime(time) {
      if (!time) return '--'
      return new Date(time).toLocaleString()
    },
    
    formatBytes(bytes) {
      if (!bytes) return '0B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]
    }
  },
  
  watch: {
    chartType() {
      this.updateChart()
    }
  }
}
</script>

<style scoped>
.execution-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.config-panel {
  margin-bottom: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: #409eff;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.metric-card {
  border: none;
  border-radius: 8px;
  overflow: hidden;
}

.metric-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.tps-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.response-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.users-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.error-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.error-icon.error-medium {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.error-icon.error-high {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.status-panel {
  height: fit-content;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.status-label {
  color: #606266;
  font-size: 14px;
}

.progress-section {
  margin: 20px 0;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.progress-percent {
  font-weight: bold;
  color: #409eff;
}

.quick-actions {
  display: flex;
  gap: 10px;
}

.quick-actions .el-button {
  flex: 1;
}

.resource-item {
  margin-bottom: 15px;
}

.resource-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.resource-value {
  font-size: 12px;
  color: #606266;
  margin-left: 10px;
}

.network-io {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #606266;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h4 {
  margin: 0;
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .panel-header {
    flex-direction: column;
    gap: 10px;
  }
}
</style>