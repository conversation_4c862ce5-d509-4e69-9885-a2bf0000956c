<template>
 <div class="report-push-container">
  <div class="header-actions">
    <el-button @click="clickAdd" type="primary" :icon="Plus" class="add-button">新增推送信息</el-button>
  </div>
  
  <el-card class="table-card">
    <el-scrollbar>
      <div class="table-data">
        <el-table 
          :data="hookList" 
          v-loading="isLoading" 
          stripe 
          style="width: 100%" 
          empty-text="暂无数据" 
          border
          :header-cell-style="{background:'#f5f7fa', color: '#606266'}"
        >
          <el-table-column label="序号" align="center" width="60">
            <template #default="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="推送名称" prop="name" align="center" />
          <el-table-column label="推送类型" prop="pushType" align="center">
            <template #default="scope">
              <el-tag 
                :type="getPushTypeTagType(scope.row.pushType)" 
                effect="plain"
              >
                <span class="push-type-tag">
                  <span v-html="getIconSvg(scope.row.pushType)" class="iconfont"></span>
                  {{ getPushTypeName(scope.row.pushType) }}
                </span>
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="hook地址" prop="webhook" align="center">
            <template #default="scope">
              <el-tooltip placement="top-start" effect="dark" :content="scope.row.webhook">
                <div class="webhook-text">{{ scope.row.webhook.length > 25 ? scope.row.webhook.slice(0, 25) + '...' : scope.row.webhook }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="接收人" prop="user_ids" align="center">
            <template #default="scope">
              <div class="user-tags">
                <el-tooltip 
                  v-if="scope.row.user_ids.length > 2" 
                  :content="scope.row.user_ids.join(', ')" 
                  placement="top"
                >
                  <div>
                    <el-tag v-for="(user, index) in scope.row.user_ids.slice(0, 2)" :key="index" size="small" class="user-tag">
                      {{ user }}
                    </el-tag>
                    <el-tag size="small" type="info">+{{ scope.row.user_ids.length - 2 }}</el-tag>
                  </div>
                </el-tooltip>
                <template v-else>
                  <el-tag v-for="(user, index) in scope.row.user_ids" :key="index" size="small" class="user-tag">
                    {{ user }}
                  </el-tag>
                </template>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="测试计划" prop="testPlan.name" align="center"/>
          <el-table-column label="创建时间" align="center">
            <template #default="scope">
              {{ $tools.rTime(scope.row.create_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template #default="scope">
              <el-button @click="clickEdit(scope.row)"  type="primary" :icon="Edit" circle title="编辑"></el-button>
              <el-button @click="delHook(scope.row.id)"  type="danger" :icon="Delete" circle title="删除"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-container">
        <el-pagination 
          background 
          layout="total, prev, pager, next, jumper"
          @current-change="currentPages"
          :default-page-size="100"
          :total="pages.count"
          :current-page="pages.current"
          :next-text="'下一页'" 
          :prev-text="'上一页'"
        >
        </el-pagination>
      </div>
    </el-scrollbar>
  </el-card>

  <!--  新增弹窗-->
  <el-dialog 
    v-model="addDlg" 
    title="新增推送信息" 
    width="40%" 
    custom-class="push-dialog" 
    :required="true" 
    style="text-align:left" 
    :before-close="clearValidation"
    top="5vh"
  >
    <el-form :model="addForm" :rules="rulesHook" ref="HookRef" label-width="120px" style="max-width: 600px">
      <el-form-item prop="name" label="推送名称">
        <el-input v-model="addForm.name" maxlength="50" minlength="1" placeholder="请输入推送名称"/>
      </el-form-item>
      
      <el-form-item prop="pushType" label="推送类型">
        <div class="custom-radio-group">
          <div 
            class="custom-radio-btn" 
            :class="{ 'active': addForm.pushType === 'wechat' }"
            @click="addForm.pushType = 'wechat'"
          >
            <span class="push-icon-wrapper">
              <span v-html="svgIcons.wechat" class="iconfont"></span> 企业微信
            </span>
          </div>
          <div 
            class="custom-radio-btn" 
            :class="{ 'active': addForm.pushType === 'feishu' }"
            @click="addForm.pushType = 'feishu'"
          >
            <span class="push-icon-wrapper">
              <span v-html="svgIcons.feishu" class="iconfont"></span> 飞书
            </span>
          </div>
          <div 
            class="custom-radio-btn" 
            :class="{ 'active': addForm.pushType === 'dingtalk' }"
            @click="addForm.pushType = 'dingtalk'"
          >
            <span class="push-icon-wrapper">
              <span v-html="svgIcons.dingtalk" class="iconfont"></span> 钉钉
            </span>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item prop="webhook" label="webhook地址">
        <el-input v-model="addForm.webhook" minlength="3" placeholder="请输入webhook地址" show-word-limit>
          <template #prefix>
            <span v-html="getIconSvg(addForm.pushType)" class="iconfont"></span>
          </template>
        </el-input>
        <div class="webhook-tip" v-if="addForm.pushType">
          <el-link type="primary" :href="getWebhookHelpUrl(addForm.pushType)" target="_blank">
            <i class="el-icon-question"></i> 如何获取{{ getPushTypeName(addForm.pushType) }}的webhook地址?
          </el-link>
        </div>
      </el-form-item>
      
      <el-form-item prop="testPlan_id" label="测试计划">
        <el-select v-model="addForm.testPlan_id" placeholder="请选择测试计划" style="width: 100%;">
          <el-option :label="iter.name" :value="iter.id" v-for="iter in testPlans" :key="iter.id"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="接收人" prop="user_ids">
        <el-select multiple v-model="addForm.user_ids" placeholder="请选择接收人" style="width: 100%;" collapse-tags collapse-tags-tooltip @change="handleReceiversChange('add')">
          <el-option :label="'@all'" :value="'@all'" :key="'@all'" :disabled="addForm.user_ids.length > 0 && !addForm.user_ids.includes('@all')"></el-option>
          <el-option 
            :label="iter.weChat_name" 
            :value="iter.weChat_name" 
            v-for="iter in filteredUsers" 
            :key="iter.id" 
            :disabled="addForm.user_ids.includes('@all')"
          ></el-option>
        </el-select>
        <div class="form-tip">选择 @all 将通知所有人，与其他接收人互斥</div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="clearValidation" size="default">取消</el-button>
        <el-button type="primary" @click="AddInter" size="default">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!--  修改弹窗-->
  <el-dialog 
    v-model="editDlg" 
    title="修改推送信息" 
    width="40%"
    custom-class="push-dialog" 
    :required="true" 
    style="text-align:left" 
    :before-close="clearValidation"
    top="5vh"
  >
    <el-form :model="editForm" :rules="rulesHook" ref="HookRef" label-width="120px" style="max-width: 600px">
      <el-form-item prop="name" label="推送名称">
        <el-input v-model="editForm.name" maxlength="50" minlength="1" placeholder="请输入推送名称"/>
      </el-form-item>
      
      <el-form-item prop="pushType" label="推送类型">
        <div class="custom-radio-group">
          <div 
            class="custom-radio-btn" 
            :class="{ 'active': editForm.pushType === 'wechat' }"
            @click="editForm.pushType = 'wechat'"
          >
            <span class="push-icon-wrapper">
              <span v-html="svgIcons.wechat" class="iconfont"></span> 企业微信
            </span>
          </div>
          <div 
            class="custom-radio-btn" 
            :class="{ 'active': editForm.pushType === 'feishu' }"
            @click="editForm.pushType = 'feishu'"
          >
            <span class="push-icon-wrapper">
              <span v-html="svgIcons.feishu" class="iconfont"></span> 飞书
            </span>
          </div>
          <div 
            class="custom-radio-btn" 
            :class="{ 'active': editForm.pushType === 'dingtalk' }"
            @click="editForm.pushType = 'dingtalk'"
          >
            <span class="push-icon-wrapper">
              <span v-html="svgIcons.dingtalk" class="iconfont"></span> 钉钉
            </span>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item prop="webhook" label="webhook地址">
        <el-input v-model="editForm.webhook" minlength="3" placeholder="请输入webhook地址" show-word-limit>
          <template #prefix>
            <span v-html="getIconSvg(editForm.pushType)" class="iconfont"></span>
          </template>
        </el-input>
        <div class="webhook-tip" v-if="editForm.pushType">
          <el-link type="primary" :href="getWebhookHelpUrl(editForm.pushType)" target="_blank">
            <i class="el-icon-question"></i> 如何获取{{ getPushTypeName(editForm.pushType) }}的webhook地址?
          </el-link>
        </div>
      </el-form-item>
      
      <el-form-item prop="testPlan_id" label="测试计划">
        <el-select v-model="editForm.testPlan_id" placeholder="请选择测试计划" style="width: 100%;">
          <el-option :label="iter.name" :value="iter.id" v-for="iter in testPlans" :key="iter.id"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="接收人" prop="user_ids">
        <el-select multiple v-model="editForm.user_ids" placeholder="请选择接收人" style="width: 100%;" collapse-tags collapse-tags-tooltip @change="handleReceiversChange('edit')">
          <el-option :label="'@all'" :value="'@all'" :key="'@all'" :disabled="editForm.user_ids.length > 0 && !editForm.user_ids.includes('@all')"></el-option>
          <el-option 
            :label="iter.weChat_name" 
            :value="iter.weChat_name" 
            v-for="iter in filteredUsers" 
            :key="iter.id" 
            :disabled="editForm.user_ids.includes('@all')"
          ></el-option>
        </el-select>
        <div class="form-tip">选择 @all 将通知所有人，与其他接收人互斥</div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="clearValidation" size="default">取消</el-button>
        <el-button type="primary" @click="EditInter" size="default">确定</el-button>
      </span>
    </template>
  </el-dialog>
</div>
</template>


<script>
import {mapActions, mapState} from "vuex";
import { Plus, Edit, Delete } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

export default {
  data() {
    return {
      hookList:'',
      pages:'',
      addDlg:false,
      isLoading: false,
      editDlg: false,
      Plus,
      Edit,
      Delete,
      pushTypeOptions: [
        { value: 'wechat', label: '企业微信' },
        { value: 'feishu', label: '飞书' },
        { value: 'dingtalk', label: '钉钉' }
      ],
      svgIcons: {
        wechat: '<svg t="1626188374737" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7171" width="16" height="16"><path d="M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5.3-3.1 11-5 17.2-5 3.2 0 6.4 0.5 9.5 1.4 33.1 9.5 68.8 14.8 105.7 14.8 6 0 11.9-0.1 17.8-0.4-7.1-21-10.9-43.1-10.9-66 0-135.8 132.2-245.8 295.3-245.8z m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1z m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1 43.2 19.3 43.2 43.1-19.4 43.1-43.2 43.1z" p-id="7172" fill="#07c160"></path><path d="M866.7 792.7c56.9-41.2 93.2-102 93.2-169.7 0-124-120.8-224.5-269.9-224.5-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3 2.6-0.8 5.2-1.2 7.9-1.2 5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.3 1.7 5.2 1.7 5 0 9.3-4.1 9.3-9.1 0-2.1-0.8-4.1-1.4-6.1-0.4-1.3-7.7-28.8-11.4-42.9-0.5-2-0.9-3.8-0.9-5.7 0.1-5.9 3.1-11.2 7.5-14.3z m-126.7-138.5c-19.5 0-35.5-16-35.5-35.6 0-19.5 16-35.6 35.5-35.6s35.5 16 35.5 35.6c0 19.6-16 35.6-35.5 35.6z m175.4 0c-19.5 0-35.5-16-35.5-35.6 0-19.5 16-35.6 35.5-35.6s35.5 16 35.5 35.6c0 19.6-16 35.6-35.5 35.6z" p-id="7173" fill="#07c160"></path></svg>',
        feishu: '<svg t="1629787228583" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2770" width="16" height="16"><path d="M512 55.296C261.376 55.296 58.368 258.304 58.368 508.928c0 250.56 203.008 453.632 453.632 453.632s453.632-203.008 453.632-453.632c0-250.56-203.008-453.632-453.632-453.632z m-32.256 592.384c-45.056 42.496-108.032 68.608-177.664 68.608-8.192 0-16.384-0.512-24.064-1.536-11.776-1.024-22.528-6.656-28.672-16.384-6.144-9.728-8.192-21.504-4.096-32.768 7.168-20.992 20.48-40.448 38.4-56.832-15.872-19.968-25.088-44.544-25.088-71.168 0-63.488 51.2-114.688 114.688-114.688 27.648 0 55.808 10.24 85.504 29.696 18.944-67.584 80.896-117.248 154.624-117.248 88.576 0 160.256 71.68 160.256 160.256 0.512 81.408-62.464 148.992-142.848 154.624-3.072 0.512-6.656 0.512-9.728 0.512-56.832 2.048-106.496-15.36-141.312-53.248z" fill="#3370FF"></path></svg>',
        dingtalk: '<svg t="1626188493827" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9783" width="16" height="16"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m227 385.3c-1 4.2-3.5 10.4-7 17.8h0.1l-0.4 0.7c-20.3 43.1-73.1 127.7-73.1 127.7s-0.1-0.2-0.3-0.5l-15.5 26.8h74.5L575.1 810l32.3-128h-58.6l20.4-84.7c-16.5 3.9-35.9 9.4-59 16.8 0 0-31.2 18.2-89.9-35 0 0-39.6-34.7-16.6-43.4 9.8-3.7 47.4-8.4 77-12.3 40-5.4 64.6-8.2 64.6-8.2S422 517 392.7 512.5c-29.3-4.6-66.4-53.1-74.3-95.8 0 0-12.2-23.4 26.3-12.3 38.5 11.1 197.9 43.2 197.9 43.2s-207.4-63.3-221.2-78.7c-13.8-15.4-40.6-84.2-37.1-126.5 0 0 1.5-10.5 12.4-7.7 0 0 153.3 69.7 258.1 107.9 104.8 37.9 195.9 57.3 184.2 106.7z" p-id="9784" fill="#1890ff"></path></svg>'
      },
      addForm:{
        name: '',
        webhook: '',
        user_ids: [],
        project_id: '',
        testPlan_id: '',
        pushType: 'wechat', // 默认为企业微信
      },
      editForm:{
        name: '',
        webhook: '',
        user_ids: [],
        project_id: '',
        testPlan_id: '',
        pushType: 'wechat',
      },
      rulesHook: {
        // 验证用户名是否合法
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ],
        // 验证推送类型
        pushType: [
          {
            required: true,
            message: '请选择推送类型',
            trigger: 'change'
          }
        ],
        // 验证webhook地址
        webhook: [
          {
            required: true,
            message: '请输入webhook地址',
            trigger: 'blur'
          }
        ],
        testPlan_id: [
          {
            required: true,
            message: '请选择测试计划',
            trigger: 'blur'
          }
        ],
        user_ids: [
          {
            type: 'array',
            required: true,
            message: '请选择至少一个接收人',
            trigger: 'change'
          }
        ]
      }
    }},
  computed: {
    ...mapState(['pro','testPlans','Users']),
    filteredUsers() {
      const filtered = this.Users.filter(iter => iter.weChat_name !== null);
      return filtered
    }
  },

  methods: {
    ...mapActions(['getAllUser']),
    
    // 处理接收人选择逻辑
    handleReceiversChange(formType) {
      const form = formType === 'add' ? this.addForm : this.editForm;
      
      // 如果选择了@all，清除其他所有选择
      if (form.user_ids.includes('@all')) {
        form.user_ids = ['@all'];
      }
      
      // 确保数组不为空，如果为空则验证会失败
      if (form.user_ids.length === 0) {
        this.$refs.HookRef.validateField('user_ids');
      }
    },
    
    // 获取推送类型名称
    getPushTypeName(type) {
      const map = {
        'wechat': '企业微信',
        'feishu': '飞书',
        'dingtalk': '钉钉'
      };
      return map[type] || '未知';
    },
    
    // 获取推送类型对应的Tag类型
    getPushTypeTagType(type) {
      const map = {
        'wechat': 'success',
        'feishu': 'info',
        'dingtalk': 'warning'
      };
      return map[type] || 'info';
    },
    
    // 获取推送类型的图标样式
    getPushTypeIconClass(type) {
      const map = {
        'wechat': 'icon-wechat',
        'feishu': 'icon-feishu',
        'dingtalk': 'icon-dingtalk'
      };
      return map[type] || '';
    },
    
    // 获取不同推送类型的帮助链接
    getWebhookHelpUrl(type) {
      const map = {
        'wechat': 'https://developer.work.weixin.qq.com/document/path/91770',
        'feishu': 'https://open.feishu.cn/document/ukTMukTMukTM/ucTM5YjL3ETO24yNxkjN',
        'dingtalk': 'https://open.dingtalk.com/document/robots/custom-robot-access'
      };
      return map[type] || '#';
    },

    // 获取图标SVG
    getIconSvg(type) {
      return this.svgIcons[type] || '';
    },

    // 点击添加
    clickAdd() {
      this.addDlg = true;
      this.addForm = {
        name: '',
        webhook: '',
        user_ids: [],
        project_id: this.pro.id,
        testPlan_id: '',
        pushType: 'wechat', // 默认企业微信
      }
    },
    
    // 点击修改
    clickEdit(info) {
      this.editDlg = true;
      this.editForm = { ...info };
      // 如果没有pushType字段(兼容老数据)，默认为企业微信
      if (!this.editForm.pushType) {
        this.editForm.pushType = 'wechat';
      }
      this.editForm.project_id = this.pro.id;
    },

    // 点击删除
    delHook(id) {
      ElMessageBox.confirm('此操作将永久删除该推送信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const response = await this.$api.deleteHook(id)
          if(response.status ===204){
            ElMessage({
              type: 'success',
              message: '删除成功!'
            });
            // 刷新页面
            this.getAllHook(this.pro)
          }
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消删除'
          });
        });
    },

    // 点击关闭弹窗
    clearValidation() {
      this.addDlg = false;
      this.editDlg = false;
      this.$refs.HookRef.clearValidate(); // 清除验证信息
    },
    
    // 列表数据展示
    async getAllHook(){
      this.isLoading=true;
      const response = await this.$api.getHooks(this.pro.id)
      if (response.status === 200){
        this.hookList = response.data.result;
        this.pages = response.data
        // 遍历每条记录并处理user_ids字段
        this.hookList.forEach(record => {
          const userIDs = eval(record.user_ids);
          record.user_ids = userIDs;
          // 如果没有pushType字段(兼容老数据)，默认为企业微信
          if (!record.pushType) {
            record.pushType = 'wechat';
          }
        });
      }
      this.isLoading=false;
    },
    
    // 新增接口
    async AddInter(){
      this.$refs.HookRef.validate(async valid => {
        // 判断是否验证通过，不通过则直接return
        if (!valid) return;
        // 调用接口等逻辑
        const params = {...this.addForm}
        const formattedIds = params.user_ids.map(id => `'${id}'`); // 在每个元素周围添加单引号
        params.user_ids = `[${formattedIds.join(',')}]`; // 将数组转换为字符串，并使用方括号括起来

        const response = await this.$api.createHook(params)
        if (response.status===201) {
          ElMessage({
            type: 'success',
            message: '添加成功',
            duration: 1000})

          this.addForm = {
            name: '',
            webhook: '',
            user_ids: [],
            project_id: '',
            testPlan_id: '',
            pushType: 'wechat',
          };
          this.addDlg = false;
          this.getAllHook()
        }
      })
    },
    
    // 修改接口
    async EditInter(){
      this.$refs.HookRef.validate(async valid => {
        if (!valid) return
        const params = {...this.editForm}
        const formattedIds = params.user_ids.map(id => `'${id}'`); // 在每个元素周围添加单引号
        params.user_ids = `[${formattedIds.join(',')}]`; // 将数组转换为字符串，并使用方括号括起来
        const response = await this.$api.updateHook(params.id, params)
        if (response.status===200) {
          ElMessage({
            type: 'success',
            message: '修改成功',
            duration: 1000});

          this.editForm = {
            name: '',
            webhook: '',
            user_ids: [],
            project_id: '',
            testPlan_id: '',
            pushType: 'wechat',
          };
          this.editDlg = false;
          this.getAllHook()
        }
      })
    },
    
    currentPages(currentPage) {
      this.getAllHook(currentPage)
      this.hookList.page = currentPage;
    },
  },

  created() {
    this.getAllHook()
    this.getAllUser()
  }
}
</script>

<style scoped>
.report-push-container {
  padding: 20px;
}

.header-actions {
  margin-bottom: 20px;
}

.add-button {
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-data {
  margin-bottom: 20px;
}

.webhook-text {
  font-family: monospace;
  color: #606266;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4px;
}

.user-tag {
  margin: 2px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.push-dialog .el-dialog__body {
  padding-top: 10px;
}

/* 自定义单选按钮组 */
.custom-radio-group {
  display: flex;
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
}

.custom-radio-btn {
  flex: 1;
  text-align: center;
  background-color: #fff;
  padding: 0px 5px;
  cursor: pointer;
  border-right: 1px solid #dcdfe6;
  transition: all 0.3s;
  font-size: 14px;
  line-height: 1.5;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-radio-btn:last-child {
  border-right: none;
}

.custom-radio-btn.active {
  background-color: #409eff;
  color: #fff;
}

.custom-radio-btn.active .iconfont svg path {
  fill: #fff;
}

.webhook-tip {
  margin-top: 5px;
  font-size: 12px;
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.push-type-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.push-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  width: 100%;
}

/* 图标样式 */
.iconfont {
  font-size: 20px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.iconfont svg {
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
}

.icon-wechat {
  color: #07c160;
}

.icon-feishu {
  color: #3370ff;
}

.icon-dingtalk {
  color: #1890ff;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .push-dialog {
    width: 90% !important;
  }
  
  .custom-radio-group {
    flex-direction: column;
  }
  
  .custom-radio-btn {
    border-right: none;
    border-bottom: 1px solid #dcdfe6;
  }
  
  .custom-radio-btn:last-child {
    border-bottom: none;
  }
}
</style>