  <template>
	<div class="report-dashboard" v-if="record">
		<div v-if="report" class="dashboard-container">
			<!-- 顶部状态栏 -->
			<div class="status-bar">
				<div class="status-left">
					<el-button type="text" class="back-btn" @click="goBack">
						<el-icon><Back /></el-icon>
					</el-button>
					<div class="report-status">
						<div class="status-title">测试报告 #{{ $route.params.id }}</div>
						<div class="status-time">{{ $tools.rTime(record.create_time) }}</div>
					</div>
				</div>
				<div class="status-right">
					<div class="status-badge" :class="getPassRateClass">
						<span class="badge-label">通过率</span>
						<span class="badge-value">{{ record.pass_rate || 0 }}%</span>
					</div>
				</div>
			</div>
      <el-scrollbar :style="{ height: 'calc(100vh - 21vh)',minHeight: '700px'}">
        <!-- 主要内容区 -->
        <div class="dashboard-content">
          <!-- 左侧数据概览 -->
          <div class="overview-panel">
            <div class="overview-header">
              <h2>测试概览</h2>
              <div class="task-info">
                <div class="task-name">{{ record.plan_name || '未命名计划' }}</div>
                <div class="env-tag">{{ record.env_name || '未知环境' }}</div>
              </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stat-cards">
              <div class="stat-card total">
                <div class="stat-icon"><el-icon><DataAnalysis /></el-icon></div>
                <div class="stat-info">
                  <div class="stat-value">{{ report.results && report.results.length || 0 }}</div>
                  <div class="stat-label">场景总数</div>
                </div>
                <div class="stat-wave"></div>
              </div>
              <div class="stat-card success">
                <div class="stat-icon"><el-icon><SuccessFilled /></el-icon></div>
                <div class="stat-info">
                  <div class="stat-value">{{ successscent.length }}</div>
                  <div class="stat-label">通过场景</div>
                </div>
                <div class="stat-wave"></div>
              </div>
              <div class="stat-card fail">
                <div class="stat-icon"><el-icon><Warning /></el-icon></div>
                <div class="stat-info">
                  <div class="stat-value">{{ failscent.length }}</div>
                  <div class="stat-label">失败场景</div>
                </div>
                <div class="stat-wave"></div>
              </div>
              <div class="stat-card error">
                <div class="stat-icon"><el-icon><CircleCloseFilled /></el-icon></div>
                <div class="stat-info">
                  <div class="stat-value">{{ errorscent.length }}</div>
                  <div class="stat-label">错误场景</div>
                </div>
                <div class="stat-wave"></div>
              </div>
            </div>

            <!-- 图表区域 -->
            <div class="chart-section">
              <div class="chart-card">
                <div class="chart-header">
                  <h3>执行统计</h3>
                  <div class="chart-legend">
                    <span class="legend-item success">通过</span>
                    <span class="legend-item fail">失败</span>
                    <span class="legend-item error">错误</span>
                  </div>
                </div>
                <div class="chart-content" ref="chart1"></div>
              </div>
              <div class="chart-card">
                <div class="chart-header">
                  <h3>结果分布</h3>
                </div>
                <div class="chart-content" ref="chart2"></div>
              </div>
            </div>
          </div>

          <!-- 右侧场景详情 -->
          <div class="detail-panel">
            <div class="detail-header">
              <h2>测试场景</h2>
              <div class="filter-tabs">
                <div
                  v-for="tab in filterTabs"
                  :key="tab.value"
                  class="filter-tab"
                  :class="{ active: currentFilter === tab.value, [tab.value]: true }"
                  @click="handleFilterChange(tab.value)"
                >
                  <el-icon class="tab-icon"><component :is="tab.icon" /></el-icon>
                  <span>{{ tab.label }}</span>
                  <span class="tab-count">{{ getTabCount(tab.value) }}</span>
                </div>
              </div>
            </div>

            <el-scrollbar class="scene-list">
              <transition-group name="scene-fade">
                <div
                  v-for="(scent, index) in showScentDatas"
                  :key="scent.name + index"
                  class="scene-card"
                  :class="{ expanded: expandedScenes.includes(index) }"
                >
                  <div class="scene-header" @click="toggleScene(index)">
                    <div class="scene-status">
                      <el-icon><component :is="getSceneIcon(scent.state)" /></el-icon>
                    </div>
                    <div class="scene-info">
                      <div class="scene-name">{{ scent.name || '未命名场景' }}</div>
                      <div class="scene-state" :class="getSceneStateClass(scent.state)">
                        {{ getSceneStateText(scent.state) }}
                      </div>
                    </div>
                    <div class="scene-action">
                      <el-icon :class="{ rotate: expandedScenes.includes(index) }"><ArrowRight /></el-icon>
                    </div>
                  </div>

                  <div class="scene-detail" v-show="expandedScenes.includes(index)">
                    <el-table
                      :data="scent.cases || []"
                      class="case-table"
                      size="small"
                    >
                      <el-table-column type="expand">
                        <template #default="scope">
                          <div class="case-detail">
                            <caseRes :result="scope.row"></caseRes>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="用例名称" min-width="200">
                        <template #default="scope">
                          <div class="case-name">
                            <el-icon><component :is="getCaseTypeIcon(scope.row)" /></el-icon>
                            <span>{{ scope.row.name || '未命名用例' }}</span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="请求方法" width="100" align="center">
                        <template #default="scope">
                          <div
                            v-if="scope.row.type === 'api' && scope.row.method"
                            class="method-badge"
                            :class="scope.row.method.toLowerCase()"
                          >
                            {{ scope.row.method }}
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="状态码" width="100" align="center">
                        <template #default="scope">
                          <div
                            v-if="scope.row.type === 'api' && scope.row.status_cede !== undefined"
                            class="status-code"
                            :class="getStatusCodeClass(scope.row.status_cede)"
                          >
                            {{ scope.row.status_cede }}
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="结果" width="100" align="center">
                        <template #default="scope">
                          <div
                            v-if="scope.row.state"
                            class="result-badge"
                            :class="getCaseResultClass(scope.row.state)"
                          >
                            {{ scope.row.state }}
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </transition-group>
            </el-scrollbar>
          </div>
        </div>
      </el-scrollbar>
		</div>
	</div>
</template>

<script>
import caseRes from '../../components/common/caseResult.vue';
import { 
  Back, 
  DataAnalysis, 
  SuccessFilled, 
  Warning, 
  CircleCloseFilled, 
  ArrowRight, 
  Grid, 
  Connection, 
  Cpu
} from '@element-plus/icons-vue';
import { ElMessage, ElScrollbar } from 'element-plus';

export default {
	name: 'TestReport',
	components: { 
    caseRes,
    Back,
    DataAnalysis,
    SuccessFilled,
    Warning,
    CircleCloseFilled,
    ArrowRight,
    Grid,
    Connection,
    Cpu,
    ElScrollbar
  },
	data() {
		return {
			record: null,
			report: null,
			showScentDatas: [],
			currentFilter: 'all',
			expandedScenes: [],
			filterTabs: [
				{ label: '全部场景', value: 'all', icon: 'Grid' },
				{ label: '通过场景', value: 'success', icon: 'SuccessFilled' },
				{ label: '失败场景', value: 'fail', icon: 'Warning' },
				{ label: '错误场景', value: 'error', icon: 'CircleCloseFilled' }
			]
		};
	},
	computed: {
		successscent() {
			if (!this.report || !this.report.results) return [];
			return this.report.results.filter(val => val.state === 'success');
		},
		failscent() {
			if (!this.report || !this.report.results) return [];
			return this.report.results.filter(val => val.state === 'fail');
		},
		errorscent() {
			if (!this.report || !this.report.results) return [];
			return this.report.results.filter(val => val.state === 'error');
		},
		getPassRateClass() {
			if (!this.record || !this.record.pass_rate) return 'poor';
			const rate = parseFloat(this.record.pass_rate);
			if (rate >= 90) return 'excellent';
			if (rate >= 70) return 'good';
			if (rate >= 50) return 'fair';
			return 'poor';
		}
	},
	methods: {
		goBack() {
			window.history.back();
		},
		async getReportInfo(id) {
			try {
				console.log('获取报告信息, ID:', id);
				const response = await this.$api.getTestReport(id);
				console.log('报告API响应:', response);
				
				if (response.status === 200) {
					// 如果数据是直接在response.data，而不是response.data.info
					if (response.data && response.data.results) {
						this.report = response.data;
					} 
					// 或者数据在response.data.info
					else if (response.data && response.data.info) {
						this.report = response.data.info;
					}
					
					console.log('处理后的报告数据:', this.report);
					
					if (this.report && this.report.results) {
						this.handleFilterChange('all');
						console.log('过滤后的场景数据:', this.showScentDatas);
					} else {
						console.error('报告数据结构不正确');
						ElMessage.error('报告数据结构不正确');
					}
				}
			} catch (error) {
				console.error('获取报告信息失败:', error);
				ElMessage.error('获取报告信息失败');
			}
		},
		async getRecordInfo(id) {
			try {
				console.log('获取记录信息, ID:', id);
				const response = await this.$api.getRecordInfo(id);
				console.log('记录API响应:', response);
				
				if (response.status === 200 && response.data) {
					this.record = response.data;
					console.log('处理后的记录数据:', this.record);
				}
			} catch (error) {
				console.error('获取记录信息失败:', error);
				ElMessage.error('获取记录信息失败');
			}
		},
		chart1() {
			if (!this.report || !this.$refs.chart1) {
				console.log('无法渲染图表1 - 数据或DOM不存在');
				return;
			}
			
			const value = [
				this.report.all || 0, 
				this.report.success || 0, 
				this.report.fail || 0, 
				this.report.error || 0
			];
			const label = ['用例总数', '通过用例', '失败用例', '错误用例'];
			console.log('图表1数据:', {value, label});
			this.$chart.chart1(this.$refs.chart1, value, label);
		},
		chart2() {
			if (!this.report || !this.$refs.chart2) {
				console.log('无法渲染图表2 - 数据或DOM不存在');
				return;
			}
			
			const datas = [
				{ value: this.report.success || 0, name: '通过' },
				{ value: this.report.fail || 0, name: '失败' },
				{ value: this.report.error || 0, name: '错误' }
			];
			console.log('图表2数据:', datas);
			this.$chart.chart2(this.$refs.chart2, datas);
		},
		handleFilterChange(type) {
			if (!this.report || !this.report.results || !Array.isArray(this.report.results)) {
				console.error('没有场景数据或数据格式不正确');
				this.showScentDatas = [];
				return;
			}
			
			this.currentFilter = type;
			console.log('切换场景过滤:', type);
			
			if (type === 'all') {
				this.showScentDatas = [...this.report.results];
			} else if (type === 'success') {
				this.showScentDatas = [...this.successscent];
			} else if (type === 'fail') {
				this.showScentDatas = [...this.failscent];
			} else if (type === 'error') {
				this.showScentDatas = [...this.errorscent];
			}
			
			console.log('过滤后场景数量:', this.showScentDatas.length);
			this.expandedScenes = [];
		},
		toggleScene(index) {
			const position = this.expandedScenes.indexOf(index);
			if (position === -1) {
				this.expandedScenes.push(index);
			} else {
				this.expandedScenes.splice(position, 1);
			}
		},
		getTabCount(type) {
			if (!this.report || !this.report.results) return 0;
			if (type === 'all') return this.report.results.length;
			if (type === 'success') return this.successscent.length;
			if (type === 'fail') return this.failscent.length;
			return this.errorscent.length;
		},
		getSceneIcon(state) {
			if (state === 'success') return 'SuccessFilled';
			if (state === 'fail') return 'Warning';
			return 'CircleCloseFilled';
		},
		getSceneStateClass(state) {
			return `state-${state}`;
		},
		getSceneStateText(state) {
			const map = {
				success: '通过',
				fail: '失败',
				error: '错误'
			};
			return map[state] || state;
		},
		getCaseTypeIcon(row) {
			if (!row || !row.type) return 'Cpu';
			return row.type === 'api' ? 'Connection' : 'Cpu';
		},
		getStatusCodeClass(code) {
			code = parseInt(code || 0);
			if (code >= 200 && code < 300) return 'success';
			if (code >= 300 && code < 400) return 'redirect';
			if (code >= 400 && code < 500) return 'client-error';
			return 'server-error';
		},
		getCaseResultClass(state) {
			if (!state) return 'error';
			if (state === '成功') return 'success';
			if (state === '失败') return 'fail';
			return 'error';
		}
	},
	async created() {
		try {
			console.log('组件创建');
			const id = this.$route.params.id;
			if (id) {
				console.log('报告ID:', id);
				await this.getReportInfo(id);
				await this.getRecordInfo(id);
			} else {
				console.error('没有找到报告ID');
				ElMessage.error('没有找到报告ID');
			}
		} catch (error) {
			console.error('初始化报告失败:', error);
			ElMessage.error('初始化报告失败');
		}
	},
	mounted() {
		if (this.$refs.chart1) this.chart1();
		if (this.$refs.chart2) this.chart2();
	},
	updated() {
		if (this.$refs.chart1) this.chart1();
		if (this.$refs.chart2) this.chart2();
	}
};
</script>

<style scoped>
.report-dashboard {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);
	padding: 20px;
}

.dashboard-container {
	max-width: 1800px;
	margin: 0 auto;
}

/* 顶部状态栏 */
.status-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(255, 255, 255, 0.9);
	padding: 16px 24px;
	border-radius: 12px;
	margin-bottom: 24px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-left {
	display: flex;
	align-items: center;
	gap: 20px;
}

.back-btn {
	color: #606266;
	font-size: 20px;
	transition: all 0.3s;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8px;
}

.back-btn:hover {
	color: #409eff;
	transform: translateX(-2px);
}

.status-title {
	font-size: 20px;
	font-weight: 600;
	background: linear-gradient(45deg, #409eff, #36cfc9);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.status-time {
	font-size: 14px;
	color: #909399;
	margin-top: 4px;
}

.status-badge {
	padding: 8px 16px;
	border-radius: 20px;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: rgba(255, 255, 255, 0.8);
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
	transition: all 0.3s;
}

.status-badge:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.status-badge.excellent { background: linear-gradient(45deg, #67c23a, #95d475); color: #fff; }
.status-badge.good { background: linear-gradient(45deg, #409eff, #79bbff); color: #fff; }
.status-badge.fair { background: linear-gradient(45deg, #e6a23c, #ebb563); color: #fff; }
.status-badge.poor { background: linear-gradient(45deg, #f56c6c, #f78989); color: #fff; }

.badge-label {
	font-size: 12px;
	opacity: 0.9;
}

.badge-value {
	font-size: 20px;
	font-weight: 600;
	margin-top: 2px;
}

/* 主内容区布局 */
.dashboard-content {
	display: grid;
	grid-template-columns: 400px 1fr;
	gap: 24px;
}

/* 左侧概览面板 */
.overview-panel {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 12px;
	padding: 16px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.overview-header {
	margin-bottom: 16px;
}

.overview-header h2 {
	margin: 0 0 12px 0;
	font-size: 18px;
	color: #303133;
}

.task-info {
	background: rgba(255, 255, 255, 0.8);
	padding: 8px 12px;
	border-radius: 8px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.task-name {
	font-size: 14px;
	font-weight: 500;
	margin-bottom: 6px;
	color: #303133;
}

.env-tag {
	display: inline-block;
	padding: 4px 8px;
	background: linear-gradient(45deg, #409eff, #36cfc9);
	color: #fff;
	border-radius: 4px;
	font-size: 12px;
}

/* 统计卡片 */
.stat-cards {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 12px;
	margin-bottom: 16px;
}

.stat-card {
	position: relative;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 10px;
	padding: 12px;
	overflow: hidden;
	transition: all 0.3s;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon {
	font-size: 20px;
	margin-bottom: 8px;
	display: flex;
	align-items: center;
}

.stat-icon .el-icon {
	font-size: 20px;
}

.stat-value {
	font-size: 24px;
	font-weight: 600;
	margin-bottom: 2px;
}

.stat-label {
	font-size: 12px;
	color: #909399;
}

.stat-wave {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 4px;
	background: linear-gradient(90deg, transparent, currentColor, transparent);
	animation: wave 2s infinite;
}

@keyframes wave {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

.stat-card.total { color: #409eff; }
.stat-card.success { color: #67c23a; }
.stat-card.fail { color: #e6a23c; }
.stat-card.error { color: #f56c6c; }

/* 图表区域 */
.chart-card {
	background: rgba(255, 255, 255, 0.8);
	border-radius: 10px;
	padding: 12px;
	margin-bottom: 12px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
	transition: all 0.3s;
}

.chart-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.chart-header h3 {
	margin: 0;
	font-size: 14px;
	color: #303133;
	font-weight: 600;
}

.chart-legend {
	display: flex;
	gap: 8px;
}

.legend-item {
	font-size: 11px;
	display: flex;
	align-items: center;
	color: #606266;
}

.legend-item::before {
	content: '';
	display: inline-block;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	margin-right: 3px;
}

.legend-item.success::before { background: #67c23a; }
.legend-item.fail::before { background: #e6a23c; }
.legend-item.error::before { background: #f56c6c; }

.chart-content {
	height: 200px;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 右侧详情面板 */
.detail-panel {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 12px;
	padding: 20px;
	display: flex;
	flex-direction: column;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-header {
	margin-bottom: 24px;
}

.detail-header h2 {
	margin: 0 0 16px 0;
	font-size: 18px;
	color: #303133;
}

/* 过滤标签 */
.filter-tabs {
	display: flex;
	gap: 12px;
	margin-bottom: 20px;
}

.filter-tab {
	flex: 1;
	padding: 12px;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s;
	display: flex;
	align-items: center;
	gap: 8px;
	color: #606266;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
	position: relative;
	overflow: hidden;
}

.tab-icon {
	font-size: 16px;
	display: flex;
	align-items: center;
}

.filter-tab::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 4px;
	height: 100%;
	transition: all 0.3s;
	opacity: 0.7;
}

.filter-tab.all::before {
	background: linear-gradient(to bottom, #409eff, #36cfc9);
}

.filter-tab.success::before {
	background: linear-gradient(to bottom, #67c23a, #95d475);
}

.filter-tab.fail::before {
	background: linear-gradient(to bottom, #e6a23c, #ebb563);
}

.filter-tab.error::before {
	background: linear-gradient(to bottom, #f56c6c, #f78989);
}

.filter-tab:hover {
	background: rgba(255, 255, 255, 0.9);
	transform: translateY(-2px);
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.filter-tab:hover::before {
	width: 6px;
}

.filter-tab.active {
	background: #fff;
	padding-left: 16px;
}

.filter-tab.active::before {
	width: 8px;
}

.filter-tab.active.all {
	color: #409eff;
	border: 1px solid rgba(64, 158, 255, 0.2);
}

.filter-tab.active.success {
	color: #67c23a;
	border: 1px solid rgba(103, 194, 58, 0.2);
}

.filter-tab.active.fail {
	color: #e6a23c;
	border: 1px solid rgba(230, 162, 60, 0.2);
}

.filter-tab.active.error {
	color: #f56c6c;
	border: 1px solid rgba(245, 108, 108, 0.2);
}

.filter-tab i {
	font-size: 16px;
	transition: all 0.3s;
}

.filter-tab:hover i {
	transform: scale(1.1);
}

.filter-tab.active i {
	transform: scale(1.2);
}

.tab-count {
	margin-left: auto;
	background: rgba(255, 255, 255, 0.8);
	padding: 4px 10px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: bold;
	transition: all 0.3s;
	min-width: 24px;
	text-align: center;
}

.filter-tab.active .tab-count {
	background: rgba(255, 255, 255, 0.95);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-tab.active.all .tab-count {
	background: rgba(64, 158, 255, 0.1);
	color: #409eff;
}

.filter-tab.active.success .tab-count {
	background: rgba(103, 194, 58, 0.1);
	color: #67c23a;
}

.filter-tab.active.fail .tab-count {
	background: rgba(230, 162, 60, 0.1);
	color: #e6a23c;
}

.filter-tab.active.error .tab-count {
	background: rgba(245, 108, 108, 0.1);
	color: #f56c6c;
}

/* 场景列表 */
.scene-list {
	height: calc(100vh - 26vh);
	min-height: 400px;
	padding-right: 8px;
}

.scene-card {
	background: rgba(255, 255, 255, 0.8);
	border-radius: 8px;
	margin-bottom: 12px;
	transition: all 0.3s, max-height 0.5s ease;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.scene-card:hover {
	transform: translateX(4px);
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.scene-header {
	padding: 16px;
	display: flex;
	align-items: center;
	gap: 16px;
	cursor: pointer;
}

.scene-status {
	font-size: 20px;
	display: flex;
	align-items: center;
}

.scene-status .el-icon {
	font-size: 20px;
}

.scene-info {
	flex: 1;
}

.scene-name {
	font-weight: 500;
	margin-bottom: 4px;
	color: #303133;
}

.scene-state {
	font-size: 12px;
	padding: 2px 8px;
	border-radius: 4px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.state-success { background: rgba(103, 194, 58, 0.1); color: #67c23a; }
.state-fail { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }
.state-error { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }

.scene-action {
	display: flex;
	align-items: center;
}

.scene-action .el-icon {
	transition: transform 0.3s;
	color: #909399;
}

.scene-action .el-icon.rotate {
	transform: rotate(90deg);
}

.scene-detail {
	padding: 0 16px 16px;
	overflow: hidden;
	transition: all 0.3s;
}

/* 用例表格样式 */
.case-table {
	background: transparent !important;
}

.case-table :deep(.el-table__expanded-cell) {
	padding: 10px 20px;
	background: rgba(250, 250, 250, 0.5);
}

.case-name {
	display: flex;
	align-items: center;
	gap: 8px;
	color: #303133;
}

.case-name .el-icon {
	font-size: 16px;
	display: flex;
	align-items: center;
}

.method-badge {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 500;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.method-badge.get { background: rgba(103, 194, 58, 0.1); color: #67c23a; }
.method-badge.post { background: rgba(64, 158, 255, 0.1); color: #409eff; }
.method-badge.put { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }
.method-badge.delete { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }

.status-code {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.status-code.success { background: rgba(103, 194, 58, 0.1); color: #67c23a; }
.status-code.redirect { background: rgba(64, 158, 255, 0.1); color: #409eff; }
.status-code.client-error { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }
.status-code.server-error { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }

.result-badge {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.result-badge.success { background: rgba(103, 194, 58, 0.1); color: #67c23a; }
.result-badge.fail { background: rgba(230, 162, 60, 0.1); color: #e6a23c; }
.result-badge.error { background: rgba(245, 108, 108, 0.1); color: #f56c6c; }

/* 动画效果 */
.scene-fade-enter-active, .scene-fade-leave-active {
	transition: all 0.3s ease;
}

.scene-fade-enter-from, .scene-fade-leave-to {
	opacity: 0;
	transform: translateY(10px);
}

/* 展开/收起效果 */
.scene-detail {
  overflow: hidden;
  transition: all 0.3s;
}

.scene-card {
  transition: all 0.3s, max-height 0.5s ease;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
	width: 6px;
}

::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 3px;
}

::-webkit-scrollbar-thumb {
	background: rgba(144, 147, 153, 0.3);
	border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
	background: rgba(144, 147, 153, 0.5);
}

/* 自定义 Element Plus 表格样式 */
:deep(.el-table) {
  --el-table-header-bg-color: rgba(255, 255, 255, 0.6);
  --el-table-row-hover-bg-color: rgba(255, 255, 255, 0.8);
  background: transparent;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header-wrapper),
:deep(.el-table__body-wrapper) {
  background: transparent;
}

:deep(.el-table th) {
  background-color: var(--el-table-header-bg-color);
  font-weight: 600;
  color: #303133;
  padding: 10px 0;
}

:deep(.el-table td) {
  padding: 10px 0;
}

:deep(.el-table .el-table__cell) {
  vertical-align: middle;
}
</style>
