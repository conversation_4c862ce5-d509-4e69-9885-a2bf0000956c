<template>
  <el-scrollbar height="100vh">
    <div class="dashboard-container">
    <!-- 顶部信息栏 -->
    <div class="welcome-card glass-effect">
      <div class="user-info">
        <div v-if="avatar && (avatar.startsWith('logos:') || avatar.startsWith('cryptocurrency-color:') || avatar.startsWith('mdi:') || avatar.startsWith('streamline-emojis:'))" class="user-avatar animate__animated animate__fadeIn">
          <div class="icon-avatar">
            <icon :icon="avatar" />
          </div>
        </div>
        <el-avatar v-else :size="80" :src="avatar || '/avatar.png'" class="animate__animated animate__fadeIn"></el-avatar>
        <div class="greeting-content">
          <h2 class="username animate__animated animate__fadeInDown">{{username}}</h2>
          <div class="greeting animate__animated animate__fadeInUp">{{greeting()}}</div>
          <div class="weather-info animate__animated animate__fadeInUp">
            <el-icon><Sunny /></el-icon> 今天晴，20℃ - 32℃
              </div>
            </div>
          </div>
      <div class="stats-container">
        <div class="stat-item animate__animated animate__fadeInRight" v-for="(stat, index) in statistics" :key="index" 
             :style="{animationDelay: `${index * 0.1}s`}">
          <div class="stat-icon">
            <el-icon><component :is="stat.icon" /></el-icon>
            </div>
          <div class="stat-content">
            <div class="stat-title">{{stat.title}}</div>
            <div class="stat-value">
              <count-to
                  :start-val="0"
                :end-val="stat.value"
                :duration="2000"
                separator=","
              />
            </div>
            </div>
          </div>
      </div>
    </div>

    <div class="main-content">
      <!-- 项目列表区域 -->
      <div class="projects-container">
        <el-card class="main-card projects-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Collection /></el-icon>
                我的项目
              </h3>
              <div class="header-actions">
                <el-button type="primary" @click="showAddDialog" plain round size="small">
                  <el-icon><Plus /></el-icon>新建项目
                </el-button>
                <el-button type="warning" @click="handleCommand('update')" plain round size="small" :disabled="!hasSelectedProject">
                  <el-icon><EditPen /></el-icon>修改项目
                </el-button>
                <el-button type="danger" @click="handleCommand('delete')" plain round size="small" :disabled="!hasSelectedProject">
                  <el-icon><Delete /></el-icon>删除项目
                </el-button>
              </div>
            </div>
          </template>
          
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="3" animated />
          </div>
          <div v-else-if="pro_list.length === 0" class="empty-container">
            <el-empty description="暂无项目，点击新建开始您的第一个项目吧！" />
          </div>
          <div v-else class="projects-grid">
            <div 
              v-for="(project, index) in pro_list" 
              :key="project.id"
              class="project-card-wrapper animate__animated animate__fadeIn"
              :style="{animationDelay: `${index * 0.05}s`}"
            >
              <el-card 
                class="project-card" 
                :class="{'project-selected': isProjectSelected(project.id)}"
                shadow="hover"
                @click="clickView(project)"
              >
                <div class="project-header">
                  <div class="project-icon">
                    <icon :icon="project.icon" />
                  </div>
                </div>
                <h4 class="project-name">{{ project.name }}</h4>
                <div class="project-description">{{ project.desc }}</div>
                <div class="project-footer">
                  <div class="project-leader">
                    <el-icon><User /></el-icon> {{ project.leader }}
                  </div>
                  <div class="project-date">
                    <el-icon><Calendar /></el-icon> {{ formatDate(project.create_time) }}
                  </div>
                </div>
                <div class="project-actions">
                  <el-checkbox
                    v-model="checkList"
                    :label="project.id"
                    @click.stop
                    @change="onProjectSelectChange"
                    class="select-checkbox"
                  />
                  <el-button type="primary" circle size="small">
                    <el-icon><Right /></el-icon>
                  </el-button>
                </div>
              </el-card>
            </div>
          </div>
        </el-card>

        <!-- 活动动态 -->
        <el-card class="main-card activities-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Bell /></el-icon>
                最新动态
              </h3>
              <el-link type="primary">查看全部</el-link>
            </div>
          </template>
          
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in dynamics"
              :key="activity.id"
              :timestamp="activity.time"
              :type="getActivityType(index)"
              size="large"
            >
              <div class="activity-content">
                <span class="activity-user">{{ activity.name }}</span>
                <span class="activity-text">{{ activity.content }}</span>
                </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>

      <!-- 侧边栏 -->
      <div class="sidebar-container">
        <!-- 快捷导航 -->
        <el-card class="main-card shortcut-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Operation /></el-icon>
                快捷导航
              </h3>
            </div>
          </template>
          <div class="shortcuts-grid">
            <div 
              v-for="(item, index) in shortcut" 
              :key="item.id"
              class="shortcut-item animate__animated animate__zoomIn"
              :style="{animationDelay: `${index * 0.1}s`}"
              @click="handleShortcutClick(item)"
            >
              <div class="shortcut-icon">
                <icon :icon="item.icon" />
              </div>
              <div class="shortcut-name">{{ item.name }}</div>
            </div>
          </div>
        </el-card>

        <!-- 数据统计图表 -->
        <el-card class="main-card chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><DataLine /></el-icon>
                最近7天用例数量
              </h3>
            </div>
          </template>
          <div class="chart-container">
            <RadarChart></RadarChart>
          </div>
        </el-card>

        <!-- 团队列表 -->
        <el-card class="main-card teams-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><UserFilled /></el-icon>
                团队
              </h3>
            </div>
          </template>
          <div class="teams-list">
            <div 
              v-for="(team, index) in teamList" 
              :key="team.name"
              class="team-item animate__animated animate__fadeInUp"
              :style="{animationDelay: `${index * 0.1}s`}"
            >
              <div class="team-icon">
                <icon :icon="team.icon" />
              </div>
              <div class="team-name">{{ team.name }}</div>
              <div class="team-action">
                <el-button type="primary" text circle>
                  <el-icon><Right /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加项目对话框 -->
    <el-dialog
      v-model="dialogVisible.add"
      title="创建新项目"
      width="500px"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
      top="5vh"
      align-center
    >
      <el-form
        ref="addFormRef"
        :model="projectForm"
        :rules="formRules"
        label-position="top"
        status-icon
      >
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="projectForm.name" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="项目描述" prop="desc">
          <el-input
            v-model="projectForm.desc"
            type="textarea"
            placeholder="请输入项目描述"
            :autosize="{ minRows: 3, maxRows: 5 }"
            show-word-limit
            maxlength="100"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="submitProjectForm('add')">创建</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑项目对话框 -->
    <el-dialog
      v-model="dialogVisible.edit"
      title="编辑项目"
      width="500px"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
      top="5vh"
      align-center
    >
      <el-form
        ref="editFormRef"
        :model="projectForm"
        :rules="formRules"
        label-position="top"
        status-icon
      >
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="projectForm.name" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="项目描述" prop="desc">
          <el-input
            v-model="projectForm.desc"
            type="textarea"
            placeholder="请输入项目描述"
            :autosize="{ minRows: 3, maxRows: 5 }"
            show-word-limit
            maxlength="100"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="submitProjectForm('edit')">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  </el-scrollbar>
</template>

<script>
import { Icon } from '@iconify/vue'
import RadarChart from '../../components/echart/RadarChart.vue'
import CountTo from '../../components/to'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Sunny, Collection, Plus, ArrowDown, Edit, Delete, 
  User, Calendar, Right, Bell, Operation, DataLine, 
  UserFilled, Setting, MoreFilled, Download, Share,
  View, Document, EditPen
} from '@element-plus/icons-vue'

export default {
  components: {
    Icon,
    RadarChart,
    CountTo
  },
  data() {
    return {
      // 数据状态
      loading: false,
      pro_list: [],
      checkList: [],
      selectedProject: null,

      // 对话框状态
      dialogVisible: {
        add: false,
        edit: false
      },

      // 表单相关
      projectForm: {
        id: '',
        name: '',
        desc: '',
        icon: 'logos:github-icon'
      },

      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入项目名称', trigger: 'blur' },
          { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        desc: [
          { required: true, message: '请输入项目描述', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },

      // 统计数据
      statistics: [
        { title: '参与项目', value: 99, icon: 'Collection' },
        { title: '用例数量', value: 16358, icon: 'Document' },
        { title: '项目访问', value: 53233069, icon: 'View' }
      ],

      // 动态数据
      dynamics: [
        { id: 1, name: 'admin', content: '查看了测试报告', time: '2小时前' },
        { id: 2, name: '小明', content: '提交了bug', time: '一天前' },
        { id: 3, name: '小泊', content: '修改了登录接口', time: '三天前' },
        { id: 4, name: '小泊', content: '新增了接口', time: '七天前' },
        { id: 5, name: '小文', content: '执行了测试计划', time: '七天前' },
        { id: 6, name: '猴哥', content: '新增了定时任务', time: '七天前' }
      ],

      // 团队数据
      teamList: [
        { name: '团队A', icon: 'logos:web-dev-icon' },
        { name: '团队B', icon: 'logos:github-icon' },
        { name: '团队C', icon: 'cryptocurrency-color:sc' },
        { name: '团队D', icon: 'cryptocurrency-color:sc' }
      ],

      // 可用图标
      availableIcons: [
        { id: 1, icon: 'logos:github-icon' },
        { id: 2, icon: 'logos:baker-street' },
        { id: 3, icon: 'cryptocurrency-color:xpr' },
        { id: 4, icon: 'cryptocurrency-color:ethos' },
        { id: 5, icon: 'cryptocurrency-color:sc' },
        { id: 6, icon: 'logos:nocodb' },
        { id: 7, icon: 'logos:web-dev-icon' },
        { id: 8, icon: 'cryptocurrency-color:gxs' },
        { id: 9, icon: 'cryptocurrency-color:one' },
        { id: 10, icon: 'cryptocurrency-color:powr' },
        { id: 11, icon: 'cryptocurrency-color:uni' },
        { id: 12, icon: 'cryptocurrency-color:waves' },
        { id: 13, icon: 'cryptocurrency-color:atom' },
        { id: 14, icon: 'cryptocurrency-color:sky' }
      ],

      // 快捷导航
      shortcut: [
        { id: 1, name: '退出登录', icon: 'logos:google-360suite', action: 'logout' },
      ],

      // 图表是否已加载
      chartLoaded: false
    }
  },
  computed: {
    username() {
      return sessionStorage.getItem('username') || '用户'
    },
    avatar() {
      return sessionStorage.getItem('avatar') || 'mdi:account-circle'
    },
    hasSelectedProject() {
      return this.checkList.length > 0
    }
  },
  mounted() {
    this.getAllProjects()
    // 添加一个延迟，确保图表能够正确渲染
    setTimeout(() => {
      this.chartLoaded = true
    }, 500)
  },
  methods: {
    // 根据活动内容显示不同的类型和图标
    getActivityType(index) {
      const types = ['primary', 'success', 'info', 'warning', 'danger']
      return types[index % types.length]
    },

    getActivityIcon(activity) {
      // 根据活动内容判断返回对应的图标
      if (activity.content.includes('报告')) return Bell
      if (activity.content.includes('bug')) return Delete
      if (activity.content.includes('接口')) return Setting
      if (activity.content.includes('测试')) return DataLine
      if (activity.content.includes('任务')) return Calendar
      return MoreFilled
    },

    // 时间格式化
    formatDate(date) {
      if (!date) return '';
      
      try {
        // 如果有$tools.rDate可用，优先使用原来的方法
        if (this.$tools && this.$tools.rDate) {
          return this.$tools.rDate(date);
        }
        
        // 简单的日期格式化，不依赖date-fns
        const dateObj = new Date(date);
        const now = new Date();
        const diffSeconds = Math.floor((now - dateObj) / 1000);
        
        if (diffSeconds < 60) return '刚刚';
        if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}分钟前`;
        if (diffSeconds < 86400) return `${Math.floor(diffSeconds / 3600)}小时前`;
        if (diffSeconds < 2592000) return `${Math.floor(diffSeconds / 86400)}天前`;
        if (diffSeconds < 31536000) return `${Math.floor(diffSeconds / 2592000)}个月前`;
        return `${Math.floor(diffSeconds / 31536000)}年前`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return date;
      }
    },

    // 问候语
    greeting() {
      const currentHour = new Date().getHours()
      let greeting = ''

      if (currentHour >= 0 && currentHour < 8) {
        greeting = '早上好！今天是全新的一天，让我们充满活力地开始吧！'
      } else if (currentHour >= 8 && currentHour < 12) {
        greeting = '上午好！加油！有计划地完成任务，让每一分钟都值得！'
      } else if (currentHour >= 12 && currentHour < 18) {
        greeting = '下午好！继续保持精神和积极的态度，目标就在前方！'
      } else {
        greeting = '晚上好！给自己一份轻松，给家人一份关爱，明天即将到来，期待更美好的一天！'
      }
      return greeting
    },

    // 项目操作
    async getAllProjects() {
      this.loading = true
      try {
        const response = await this.$api.getProjects();
        if (response && response.status === 200) {
          this.pro_list = (response.data || []).map((item, index) => ({
            ...item,
            icon: this.availableIcons[index % this.availableIcons.length].icon
          }));
        } else {
          console.error('获取项目列表失败', response);
          ElMessage.error('获取项目列表失败');
        }
      } catch (error) {
        console.error('获取项目列表失败', error);
        ElMessage.error('获取项目列表失败, 请检查网络或API连接');
      } finally {
        this.loading = false;
      }
    },

    // 选择项目 - 方法已修改，不再被卡片点击直接调用
    selectProject(project) {
      if (this.checkList.includes(project.id)) {
        this.checkList = this.checkList.filter(id => id !== project.id)
      } else {
        this.checkList = [project.id]
      }
      this.selectedProject = project
    },

    // 检查项目是否被选中
    isProjectSelected(id) {
      return this.checkList.includes(id)
    },

    // 项目选择变更
    onProjectSelectChange() {
      if (this.checkList.length > 1) {
        this.checkList = [this.checkList[this.checkList.length - 1]]
      }
    },

    // 进入项目
    clickView(project) {
      this.$store.commit('selectPro', project)
      this.$router.push({ name: 'home' })
    },

    // 处理菜单命令
    handleCommand(command) {
      if (command === 'update') {
        if (this.checkList.length === 0) {
          ElMessage.warning('请勾选项目后再操作！')
          return
        }
        const selectedId = this.checkList[0]
        const project = this.pro_list.find(item => item.id === selectedId)
        if (project) {
          Object.assign(this.projectForm, project)
          this.dialogVisible.edit = true
        }
      } else if (command === 'delete') {
        if (this.checkList.length === 0) {
          ElMessage.warning('请勾选项目后再操作！')
          return
        }
        ElMessageBox.confirm('确定要删除该项目吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.deleteProject(this.checkList[0])
          })
          .catch(() => {
            ElMessage({
              type: 'info',
              message: '已取消删除'
            })
          })
      }
    },

    // 快捷导航点击
    handleShortcutClick(item) {
      if (item.action === 'logout') {
        this.logout();
      }
    },

    // 显示添加对话框
    showAddDialog() {
      Object.assign(this.projectForm, {
        id: '',
        name: '',
        desc: '',
        icon: this.availableIcons[0].icon
      })
      this.dialogVisible.add = true
    },

    // 关闭对话框
    handleDialogClose() {
      this.dialogVisible.add = false
      this.dialogVisible.edit = false
      this.checkList = []
      // 重置表单
      this.$nextTick(() => {
        if (this.$refs.addFormRef) this.$refs.addFormRef.resetFields()
        if (this.$refs.editFormRef) this.$refs.editFormRef.resetFields()
      })
    },

    // 提交表单
    async submitProjectForm(type) {
      const formRef = type === 'add' ? this.$refs.addFormRef : this.$refs.editFormRef
      if (!formRef) return
      
      const valid = await formRef.validate().catch(() => false)
      if (!valid) return
      
      try {
        if (type === 'add') {
          // 创建新项目
          const response = await this.$api.createProjects(this.projectForm);
          if (response.status === 201) {
            ElMessage.success('项目创建成功')
            this.dialogVisible.add = false
            await this.getAllProjects()
          } else {
            ElMessage.error('项目创建失败')
          }
        } else {
          // 更新项目
          const response = await this.$api.updateProjects(this.projectForm.id, this.projectForm);
          if (response.status === 200) {
            ElMessage.success('项目更新成功')
            this.dialogVisible.edit = false
            await this.getAllProjects()
          } else {
            ElMessage.error('项目更新失败')
          }
        }
      } catch (error) {
        console.error(error)
        ElMessage.error(type === 'add' ? '创建项目失败' : '更新项目失败')
      }
    },

    // 删除项目
    async deleteProject(id) {
      try {
        const response = await this.$api.delProject(id);
        if (response.status === 204) {
          ElMessage.success('项目已删除')
          this.checkList = []
          await this.getAllProjects()
        } else {
          ElMessage.error('删除项目失败')
        }
      } catch (error) {
        console.error(error)
        ElMessage.error('删除项目失败')
      }
    },

    // 退出登录
    logout() {
      ElMessage({
        message: '已注销登录状态',
        type: 'warning',
        duration: 1000
      })
      sessionStorage.removeItem('token')
      sessionStorage.removeItem('username')
      sessionStorage.removeItem('avatar')
      this.$router.push({ name: 'login' })
    },

    // 路由导航方法
    goToApiTest() {
      this.$router.push('/api-test')
    },
    
    goToPerformanceTest() {
      this.$router.push('/performance-test')
    },
    
    goToTestPlan() {
      this.$router.push('/test-plan')
    },
    
    goToTestReport() {
      this.$router.push('/test-report')
    }
  }
}
</script>

<style scoped>
/* 全局容器样式 */
.dashboard-container {
  padding: 24px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-x: hidden; /* 防止水平滚动 */
}

/* 玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 欢迎卡片高度调整 */
.welcome-card {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  position: relative;
  margin-bottom: 8px; /* 减少与下面内容的间距 */
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0)
  );
  transform: rotate(30deg);
  animation: shine 8s infinite linear;
  pointer-events: none;
}

@keyframes shine {
  from {
    transform: translateX(-100%) rotate(30deg);
  }
  to {
    transform: translateX(100%) rotate(30deg);
  }
}

/* 动画效果 */
.animate__animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInDown {
  animation-name: fadeInDown;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

.animate__fadeInRight {
  animation-name: fadeInRight;
}

.animate__zoomIn {
  animation-name: zoomIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-avatar {
  position: relative;
  transition: transform 0.3s ease;
  /* 移除白色边框 */
  border: none;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.user-avatar .icon-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  /* 调整为更协调的背景色 */
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.user-avatar .icon-avatar i {
  font-size: 42px;
}

.user-avatar .icon-avatar svg {
  width: 50px;
  height: 50px;
}

.greeting-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.username {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(45deg, #42b883, #347474);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.greeting {
  font-size: 16px;
  color: #606266;
  max-width: 450px;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 14px;
}

/* 统计数据部分 */
.stats-container {
  display: flex;
  gap: 24px;
}

.stat-item {
  padding: 16px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  min-width: 140px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 28px;
  margin-bottom: 8px;
  color: var(--el-color-primary);
}

.stat-title {
    font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
}

/* 主内容区布局 */
.main-content {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 24px;
  min-height: 0;  /* 允许子元素正确滚动 */
  align-content: start; /* 从顶部开始排列，避免垂直居中 */
  position: relative; /* 为绝对定位提供参考 */
  height: auto;
  min-height: 70vh; /* 使用视口高度的百分比 */
  width: 100%; /* 确保宽度充满 */
}

.projects-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  min-height: 0;  /* 重要：允许flex子元素正确滚动 */
  overflow: visible;
  /* 移除固定高度约束 */
  height: auto;
}

/* 添加项目卡片高度控制 */
.projects-card {
  /* 移除最大高度限制，让内容决定高度 */
  max-height: none;
  overflow: visible; /* 改为可见溢出，不限制内容 */
  display: flex;
  flex-direction: column;
  flex: 1 1 auto; /* 允许伸缩，尽可能占据可用空间 */
  margin-bottom: 24px; /* 确保与下面的卡片有足够间距 */
  background-color: #f7f9fc; /* 轻微的背景色，区别于内部卡片 */
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr); /* 修改为固定显示5列 */
  gap: 16px; /* 适当减小间距，确保5列能够显示 */
  padding: 16px; /* 保持内边距 */
  width: 100%; /* 确保宽度充满父容器 */
}

.main-card {
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none;
}

.main-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  flex-wrap: wrap; /* 允许在小屏幕上换行 */
  gap: 8px; /* 添加间距 */
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap; /* 防止标题换行 */
}

.header-actions {
  display: flex;
  gap: 8px; /* 减小按钮间距 */
  flex-wrap: wrap; /* 允许按钮在小屏幕上换行 */
}

.project-card-wrapper {
  position: relative;
  width: 100%; /* 确保宽度充满网格项 */
  height: 100%; /* 确保高度一致 */
}

.project-card {
  height: 100%; /* 使卡片填充整个容器 */
  position: relative;
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: visible; /* 改为可见溢出 */
  background-color: #ffffff; /* 确保为白色，与外层卡片区分 */
  display: flex;
  flex-direction: column;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.project-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-success));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
  border-radius: 12px 12px 0 0; /* 添加上边圆角，与卡片一致 */
}

.project-card:hover::after {
  transform: scaleX(1);
}

.project-selected {
  border: 2px solid var(--el-color-primary);
  box-shadow: 0 0 15px rgba(64, 158, 255, 0.3);
}

.project-header {
  display: flex;
  justify-content: center; /* 居中图标 */
  align-items: center;
  margin-bottom: 8px; /* 减小边距 */
}

.project-icon {
  font-size: 24px; /* 减小图标 */
  color: var(--el-color-primary);
}

.project-name {
  font-size: 14px; /* 略微减小字体 */
  font-weight: 600;
  margin: 0 0 8px 0; /* 保持边距 */
  color: #303133;
  text-align: center; /* 居中显示 */
  white-space: nowrap; /* 单行显示 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
  display: block;
  min-height: 20px; /* 确保至少有一行高度 */
  line-height: 1.3;
  width: 100%; /* 确保宽度撑满容器 */
  padding: 0 8px; /* 增加左右内边距，防止文字紧贴边缘和省略号显示不全 */
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}

.project-description {
  color: #606266;
  font-size: 12px; /* 减小字体 */
  margin-bottom: 12px; /* 减小边距 */
  line-height: 1.3;
  min-height: 20px; /* 使用最小高度而非固定高度 */
  max-height: 48px; /* 限制最大高度为3行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 末尾显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 最多显示三行 */
  -webkit-box-orient: vertical;
}

.project-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px; /* 减小字体 */
  color: #909399;
  margin-top: auto; /* 将footer推到底部 */
}

.project-leader,
.project-date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.project-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 6px;
  border-top: 1px dashed #eee;
}

/* 为选择框添加新样式 */
.select-checkbox {
  margin-left: 4px;
}

/* 隐藏复选框的序号 */
.select-checkbox :deep(.el-checkbox__label) {
  display: none;
}

/* 活动时间线 */
.activities-card {
  /* 移除最大高度限制 */
  max-height: none;
  overflow: visible;
  margin-top: 0;
  flex-shrink: 0; /* 防止被压缩 */
  flex: 0 0 auto; /* 不要拉伸，保持自身高度 */
  position: relative; /* 确保定位正确 */
  z-index: 1; /* 确保在正确的层级 */
  background-color: #f7f9fc; /* 轻微的背景色，区别于内部内容 */
}

.el-timeline {
  padding-right: 8px; /* 为滚动条留出空间 */
  /* 移除最大高度限制 */
  max-height: none;
  overflow: visible;
}

.activity-content {
  padding: 10px;
  background-color: #ffffff; /* 白色背景，与外层卡片区分 */
  border-radius: 8px;
  margin-bottom: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  word-break: break-word; /* 确保长文本能够适当换行 */
  overflow: visible; /* 确保内容可见 */
}

.activity-user {
  color: var(--el-color-primary);
  font-weight: bold;
  margin-right: 4px;
}

.activity-text {
  color: #606266;
}

/* 侧边栏 */
.sidebar-container {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 减小间距 */
  /* 移除固定高度 */
  height: auto;
  align-self: start; /* 从顶部开始排列 */
  overflow: visible; /* 改为可见溢出 */
}

/* 快捷导航 */
.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px; /* 减小间距 */
  padding: 10px;
}

.shortcut-card,
.chart-card,
.teams-card {
  background-color: #f7f9fc; /* 轻微的背景色，区别于内部内容 */
}

.shortcut-item {
  padding: 10px; /* 减小内边距 */
  border-radius: 10px;
  background-color: #ffffff; /* 白色背景，与外层卡片区分 */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px; /* 减小间距 */
  cursor: pointer;
  transition: all 0.3s ease;
}

.shortcut-item:hover {
  background-color: var(--el-color-primary-light-9);
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.15);
}

.shortcut-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.shortcut-name {
  font-size: 14px;
  text-align: center;
  color: #606266;
}

/* 图表容器 */
.chart-container {
  height: 300px;
}

/* 团队列表 */
.teams-list {
  display: flex;
  flex-direction: column;
  gap: 10px; /* 减小间距 */
  padding: 10px;
}

.team-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #ffffff; /* 白色背景，与外层卡片区分 */
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.team-item:hover {
  background-color: var(--el-color-primary-light-9);
  transform: translateX(5px);
}

.team-icon {
  font-size: 20px;
  color: var(--el-color-primary);
  margin-right: 16px;
}

.team-name {
  flex: 1;
  color: #303133;
}

.team-action {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team-item:hover .team-action {
  opacity: 1;
}

/* 加载和空状态 */
.loading-container,
.empty-container {
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 200px; /* 确保有足够的空间显示加载状态 */
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式布局 */
@media (max-width: 1600px) {
  .projects-grid {
    grid-template-columns: repeat(4, 1fr); /* 大屏幕显示4列 */
  }
}

@media (max-width: 1400px) {
  .projects-grid {
    grid-template-columns: repeat(4, 1fr); /* 中等屏幕显示4列 */
  }
}
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    height: auto; /* 在小屏幕上自适应高度 */
  }
  
  .welcome-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .stats-container {
    width: 100%;
    justify-content: space-between;
  }
  
  /* 确保在小屏幕上项目卡片和动态卡片都能正常显示 */
  .projects-card, .activities-card {
    max-height: none; /* 移除高度限制 */
    overflow: visible;
  }
  
  /* 确保在小屏幕上卡片垂直排列时不重叠 */
  .projects-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  
  /* 调整按钮在小屏幕上的布局 */
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .projects-grid {
    grid-template-columns: repeat(3, 1fr); /* 小屏幕显示3列 */
  }
}

@media (max-width: 992px) {
  .projects-grid {
    grid-template-columns: repeat(2, 1fr); /* 平板显示2列 */
  }
}

@media (max-width: 768px) {
  .welcome-card {
    padding: 16px;
  }
  
  .stats-container {
    flex-wrap: wrap;
  }
  
  .stat-item {
    min-width: 120px;
  }
  
  .projects-grid {
    grid-template-columns: 1fr; /* 手机显示1列 */
  }
  
  .shortcuts-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* 调整按钮在超小屏幕上的大小 */
  .header-actions .el-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* 按钮样式优化 */
.header-actions .el-button {
  margin: 2px; /* 增加按钮间距 */
}
</style>
