<template>
	<div class="tags">
		<!-- 标签栏 -->
		<div class="tag_box">
			<el-scrollbar>
				<span v-for="tag in tags" :key="tag.name" class="tag-item">
					<el-tag
						:closable="tags.length !== 1"
						@close="deletetag(tag.path)"
						@click="$router.push(tag.path)"
						:type="tag.path === $route.path ? 'primary' : ''"
						:effect="tag.path === $route.path ? 'dark' : 'light'"
						size="small"
						class="tag-element"
					>
						{{ tag.name }}
					</el-tag>
				</span>
			</el-scrollbar>
		</div>

		<!-- 选择环境 -->
		<div class="select_env">
			<el-button @click="closeAllTag" type="primary" size="small" style="margin-right: 50px;">关闭其他标签</el-button>
      <el-select v-model="env" placeholder="选择环境" style="width: 180px;" no-data-text="暂无数据">
        <el-option v-for="item in testEnvs" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
			<el-tooltip v-if="env" effect="dark" content="查看环境信息" placement="bottom">
				<el-button style="margin-left: 5px" @click="clickShowEnv">
					<el-icon><View /></el-icon>
				</el-button>
			</el-tooltip>
		</div>
	</div>
	<!-- 显示环境详情 -->
	<el-dialog v-model="showEnv" title="环境变量" class="env-dialog">
		<el-scrollbar height="500px">
			<el-descriptions border :column="1" class="env-descriptions" :label-width="200">
				<el-descriptions-item v-for="(value, key) in envInfo.debug_global_variable" :key="`debug-${key}`">
					<template #label>
						<div class="key-label">
							<el-tag type="warning">debug</el-tag>
							<span class="key-text">{{ key }}</span>
						</div>
					</template>
					<div class="env-value">{{ value }}</div>
				</el-descriptions-item>
				<el-descriptions-item v-for="(value, key) in envInfo.global_variable" :key="`global-${key}`">
					<template #label>
						<div class="key-label">
							<el-tag type="success">global</el-tag>
							<span class="key-text">{{ key }}</span>
						</div>
					</template>
					<div class="env-value">{{ value }}</div>
				</el-descriptions-item>
			</el-descriptions>
		</el-scrollbar>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="editEnv(envInfo)" type="success" plain>编辑</el-button>
				<el-button @click="showEnv = false">关闭</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { View } from '@element-plus/icons-vue';

export default {
	components: {
		View
	},
	data() {
		return {
			showEnv: false,
			env_variable: [],
			envInfo: {}
		};
	},
	computed: {
    ...mapState({
      tags: state => state.tags,
      envId: state => state.envId,
      testEnvs: state => state.testEnvs,
      pro: state => state.pro,
    }),
		env: {
			get() {
				return this.envId;
			},
			set(val) {
				this.selectEnv(val);
			}
		}
	},
	methods: {
		...mapMutations(['delTags', 'selectEnv', 'selectEnvInfo']),

		async clickShowEnv() {
			// 获取单个环境信息
			const response = await this.$api.getEnvInfo(this.envId,this.pro.id);
			if (response.status === 200) {
				this.envInfo = response.data;
			}
			this.showEnv = true;
		},
		// 删除标签页
		deletetag(path) {
			this.delTags(path);
			// 如果被激活的标签删除了，则跳转路由到前一个标签的路由
			if (this.$route.path === path) {
				this.$router.push(this.tags[this.tags.length - 1].path);
			}
		},
		// 关闭所有标签
		closeAllTag() {
			this.tags.forEach(item => {
				if (this.$route.path !== item.path) {
					this.delTags(item.path);
				}
			});
		},
		// 编辑环境
		editEnv(envInfo) {
			this.showEnv = false;
			this.selectEnvInfo(envInfo)
			this.$router.push({ name: 'testenv' });
		}
	}
};
</script>

<style>
.tags {
	background: #fff;
	height: 37px;
	margin: 2px 3px;
	line-height: 37px;
	display: flex;
}
.tag_box {
	flex: 1;
	display: flex;
	align-items: center;
}
.tag-item {
	display: inline-block;
	margin-left: 10px;
}
.tag-element {
	cursor: pointer;
}
.select_env {
	width: 400px;
	border-left: solid 2px #f7f7f7;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 环境弹窗样式 */
.env-dialog .el-dialog__body {
	padding: 10px 20px;
}

.env-descriptions .el-descriptions-item__content {
	word-break: break-word;
}

.env-descriptions .el-descriptions-item__label {
	width: 35% !important;
	min-width: 200px;
}

.key-label {
	display: flex;
	align-items: center;
	width: 100%;
}

.key-text {
	margin-left: 5px;
	word-break: break-word;
	font-weight: bold;
}

.env-value {
	white-space: pre-wrap;
	word-break: break-word;
	max-width: 100%;
	overflow-wrap: break-word;
}
</style>
