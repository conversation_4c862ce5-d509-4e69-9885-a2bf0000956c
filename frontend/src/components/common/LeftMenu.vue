<template>
	<!-- 用户信息 -->
	<div class="user_box">
		<el-dropdown trigger="click" :hide-on-click="false" @command="handleCommand" style="width: 100%; display: flex; justify-content: center; color: #fff; cursor: pointer;">
			<span class="el-dropdown-link">
        <i><icon :icon="avatar" class="u_head"/></i>
			<span class="username">{{ username }}
				<el-icon class="el-icon--right"><CaretBottom /></el-icon>
        </span>
			</span>
			<template #dropdown >
				<el-dropdown-menu >
					<el-dropdown-item command="select">选择项目</el-dropdown-item>
					<el-dropdown-item command="logout">注销登录</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
	</div>
	<!-- 左侧菜单 -->
	<div class="left_menu">
		<el-scrollbar height="calc(100vh - 54px)" >
			<el-menu :default-active="$route.path" router background-color='#001529' text-color='#fff'
		active-text-color='#fff' class="el-menu-vertical-demo" :default-openeds="['test', 'test2', 'test3', 'dashboard', 'submenu']" >
        <el-sub-menu index="dashboard">
          <template #title>
            <div class="centered-title">
              <el-icon><HomeFilled /></el-icon>
              <span>平台看板</span>
            </div>
          </template>
          <el-menu-item :index="'/project'">
            <el-icon><HomeFilled /></el-icon>
            <span>接口看板</span>
          </el-menu-item>
        </el-sub-menu>
				 <el-sub-menu index="test" v-if="isTestMenuActive">
          <template #title>
            <div class="centered-title">
              <el-icon><Link /></el-icon>
              <span>接口测试</span>
            </div>
          </template>
        <el-menu-item :index="item.path" v-for="item in menus" :key="item.path">
					<el-icon :class="{ 'colored-icon': item.iconComponent === 'InfoFilled' }">
            <component :is="item.iconComponent" />
          </el-icon>
					<span>{{ item.name }}</span>
				</el-menu-item>
       </el-sub-menu>
         <el-sub-menu index="test2" v-if="isTestsubMenuActive">
          <template #title>
            <div class="centered-title">
              <el-icon><Compass /></el-icon>
              <span>性能测试</span>
            </div>
          </template>
         <el-menu-item :index="item.path" v-for="item in menus1" :key="item.path">
					<el-icon><component :is="item.iconComponent" /></el-icon>
					<span>{{ item.name }}</span>
				</el-menu-item>
       </el-sub-menu>
       <el-sub-menu index="test3" v-if="isTestsubMenuActive">
          <template #title>
            <div class="centered-title">
              <el-icon><Setting /></el-icon>
              <span>其他工具</span>
            </div>
          </template>
         <el-menu-item :index="item.path" v-for="item in menus2" :key="item.path">
					<el-icon><component :is="item.iconComponent" /></el-icon>
					<span>{{ item.name }}</span>
				</el-menu-item>
       </el-sub-menu>
       <el-sub-menu index="submenu">
          <template #title>
            <div class="centered-title">
              <el-icon><Menu /></el-icon>
              <span>其他菜单</span>
            </div>
          </template>
          <el-menu-item :index="item.path" v-for="item in submenu" :key="item.path">
            <el-icon><component :is="item.iconComponent" /></el-icon>
            <span>{{ item.name }}</span>
          </el-menu-item>
        </el-sub-menu>
			</el-menu>
		</el-scrollbar>
	</div>
</template>

<script>
import { Icon } from '@iconify/vue'
import {mapMutations, mapState} from 'vuex';
import { 
  HomeFilled, Link, Compass, Setting, Menu, CaretBottom,
  Paperclip, QuestionFilled, CollectionTag, Coin, Timer, 
  Lightning, DataAnalysis, Promotion, User, Notebook, 
  Cpu, Stopwatch, Orange, VideoPlay, InfoFilled
} from '@element-plus/icons-vue';

const menuList1 = [
  {
		name: '接口管理',
		path: '/new-interface',
		iconComponent: 'Paperclip'
	},
	{
		name: '接口用例',
		path: '/TestCase',
		iconComponent: 'QuestionFilled'
	},
  {
		name: '测试计划',
		path: '/new-testplan',
		iconComponent: 'CollectionTag'
	},
	{
		name: '测试环境',
		path: '/testenv',
		iconComponent: 'Coin'
	},
	{
		name: '定时任务',
		path: '/crontab',
		iconComponent: 'Timer'
	},
	{
		name: 'bug管理',
		path: '/bugs',
		iconComponent: 'Lightning'
	},
	{
		name: '测试报表',
		path: '/records',
		iconComponent: 'DataAnalysis'
	}
];

const submenuList = [
    {
		name: '报告推送',
		path: '/reportPush',
		iconComponent: 'Promotion'
	},
  {
		name: '用户管理',
		path: '/users',
		iconComponent: 'User'
	},
  {
		name: '用例管理',
		path: '/caseManage',
		iconComponent: 'Notebook'
	},
];

const menuList2=[
  {
		name: '性能任务',
		path: '/performanceTask',
		iconComponent: 'Cpu'
	},
  {
		name: '性能报告',
		path: '/PerformanceResult',
		iconComponent: 'Stopwatch'
	},
  {
		name: '机器管理',
		path: '/server',
		iconComponent: 'Orange'
	},
  {
		name: '预配设置',
		path: '/makeSet',
		iconComponent: 'VideoPlay'
	},
  {
		name: '性能告警',
		path: '/PerformanceAlert',
		iconComponent: 'InfoFilled'
	},
  {
		name: '基准线管理',
		path: '/PerformanceBaseline',
		iconComponent: 'VideoPlay'
	},
]

const menuList3=[]

export default {
  components: {
    Icon,
    HomeFilled, Link, Compass, Setting, Menu, CaretBottom,
    Paperclip, QuestionFilled, CollectionTag, Coin, Timer, 
    Lightning, DataAnalysis, Promotion, User, Notebook, 
    Cpu, Stopwatch, Orange, VideoPlay, InfoFilled
  },
	data() {
		return {
			menus: menuList1,
      menus1: menuList2,
      menus2: menuList3,
      submenu:submenuList,
      isTestMenuActive: true,
      isTestsubMenuActive: true,
      openeds:['test', 'test2', 'test3']
		};
	},
	computed: {
    ...mapState({
      tags: state => state.tags,
    }),
		username() {
			return window.sessionStorage.getItem('username');
		},
    avatar() {
		  return window.sessionStorage.getItem('avatar');
    }
	},
	methods: {
     ...mapMutations(['clearEnvId','delTags']),
		handleCommand(cmd) {
			if (cmd === 'select') {
				this.$router.push({ name: 'allProject' });
				window.sessionStorage.removeItem('messageStore');
			  this.clearEnvId();
			  this.tags.forEach(item => {
          this.delTags(item.path)});

			}
			else if (cmd === 'logout') {
			  this.clearEnvId();
        this.tags.forEach(item => {
          this.delTags(item.path)});
				window.sessionStorage.removeItem('token');
				window.sessionStorage.removeItem('username');
				window.sessionStorage.removeItem('messageStore');
				window.sessionStorage.removeItem('avatar');
				this.$router.push({ name: 'login' });
			}
		}
	}
};
</script>

<style scoped>
.user_box {
  cursor: pointer;
	height: 53px;
	line-height: 53px;
	display: flex;
	align-items: center;
  background-color: #001529;
}
.u_head {
	height: 53px;
	border-radius: 50%;
  width: 40px;
  align-items: center;
}
.el-menu-item.is-active {
      background-color: #409eff !important;
      color: #fff;
      span {
        color: #fff !important;
      }
}
.username {
  height: 53px;
  position: relative;
  top: -23px;
  margin-left: 6px;
}
.colored-icon {
  color: rgb(245, 108, 108);
}

/* 添加样式使菜单项居中 */
:deep(.el-menu) {
  padding: 0;
  border-right: none;
}

:deep(.el-menu-item) {
  display: flex;
  justify-content: center;
  padding-left: 0 !important;
  text-align: center;
}

:deep(.el-sub-menu__title) {
  display: flex;
  justify-content: center;
  padding-left: 0 !important;
}

:deep(.el-sub-menu .el-menu-item) {
  display: flex;
  justify-content: center;
  padding-left: 0 !important;
  min-width: 100%;
  text-align: center;
}

.centered-title {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 调整图标与文本间距 */
:deep(.el-sub-menu__title span),
:deep(.el-menu-item span) {
  margin: 0 5px;
}
</style>
