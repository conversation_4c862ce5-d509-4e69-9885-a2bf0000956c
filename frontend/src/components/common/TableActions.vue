<template>
  <div class="table-actions">
    <template v-for="action in visibleActions" :key="action.name">
      <!-- 普通按钮 -->
      <el-button
        v-if="action.type !== 'dropdown'"
        :type="action.buttonType || 'primary'"
        :size="action.size || 'small'"
        :plain="action.plain !== false"
        :loading="action.loading"
        :disabled="action.disabled"
        @click="handleAction(action.name, row, action)"
        class="action-button"
      >
        <el-icon v-if="action.icon">
          <component :is="action.icon" />
        </el-icon>
        {{ action.label }}
      </el-button>
      
      <!-- 下拉菜单按钮 -->
      <el-dropdown
        v-else
        @command="(command) => handleAction(command, row)"
        trigger="click"
        :size="action.size || 'small'"
      >
        <el-button
          :type="action.buttonType || 'primary'"
          :size="action.size || 'small'"
          :plain="action.plain !== false"
        >
          <el-icon v-if="action.icon">
            <component :is="action.icon" />
          </el-icon>
          {{ action.label }}
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="item in action.items"
              :key="item.name"
              :command="item.name"
              :disabled="item.disabled"
              :divided="item.divided"
            >
              <el-icon v-if="item.icon">
                <component :is="item.icon" />
              </el-icon>
              {{ item.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>
  </div>
</template>

<script>
import { ArrowDown } from '@element-plus/icons-vue';

export default {
  name: 'TableActions',
  components: {
    ArrowDown
  },
  props: {
    // 表格行数据
    row: {
      type: Object,
      required: true
    },
    
    // 操作配置列表
    actions: {
      type: Array,
      required: true,
      validator: (actions) => {
        return actions.every(action => 
          action.name && action.label && typeof action.name === 'string'
        );
      }
    },
    
    // 是否紧凑模式
    compact: {
      type: Boolean,
      default: false
    },
    
    // 最大显示按钮数量，超过则使用下拉菜单
    maxButtons: {
      type: Number,
      default: 3
    }
  },
  
  emits: ['action'],
  
  computed: {
    /**
     * 可见的操作按钮
     */
    visibleActions() {
      return this.actions.filter(action => {
        // 检查是否应该显示此操作
        if (typeof action.show === 'function') {
          return action.show(this.row);
        }
        return action.show !== false;
      });
    }
  },
  
  methods: {
    /**
     * 处理操作点击
     * @param {string} actionName - 操作名称
     * @param {object} row - 行数据
     * @param {object} actionConfig - 操作配置
     */
    handleAction(actionName, row, actionConfig = null) {
      // 查找操作配置
      const action = actionConfig || this.actions.find(a => a.name === actionName);
      
      if (!action) {
        console.warn(`未找到操作配置: ${actionName}`);
        return;
      }
      
      // 检查是否需要确认
      if (action.confirm) {
        this.showConfirmDialog(actionName, row, action);
        return;
      }
      
      // 直接执行操作
      this.executeAction(actionName, row, action);
    },
    
    /**
     * 显示确认对话框
     * @param {string} actionName - 操作名称
     * @param {object} row - 行数据
     * @param {object} action - 操作配置
     */
    async showConfirmDialog(actionName, row, action) {
      const confirmConfig = {
        title: '提示',
        message: '确定要执行此操作吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        ...action.confirm
      };
      
      // 支持动态消息内容
      if (typeof confirmConfig.message === 'function') {
        confirmConfig.message = confirmConfig.message(row);
      }
      
      try {
        await this.$confirm(confirmConfig.message, confirmConfig.title, {
          confirmButtonText: confirmConfig.confirmButtonText,
          cancelButtonText: confirmConfig.cancelButtonText,
          type: confirmConfig.type
        });
        
        this.executeAction(actionName, row, action);
      } catch (error) {
        if (error === 'cancel') {
          this.$message({
            type: 'info',
            message: '已取消操作',
            duration: 1000
          });
        }
      }
    },
    
    /**
     * 执行操作
     * @param {string} actionName - 操作名称
     * @param {object} row - 行数据
     * @param {object} action - 操作配置
     */
    executeAction(actionName, row, action) {
      // 触发父组件事件
      this.$emit('action', {
        name: actionName,
        row: row,
        action: action
      });
      
      // 如果有直接处理函数，也执行它
      if (action.handler && typeof action.handler === 'function') {
        action.handler(row, actionName);
      }
    }
  }
};
</script>

<style scoped>
.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.action-button {
  margin: 0;
}

.table-actions .el-button + .el-button {
  margin-left: 0;
}

/* 紧凑模式 */
.table-actions--compact {
  gap: 4px;
}

.table-actions--compact .action-button {
  padding: 4px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  
  .action-button {
    width: 100%;
    justify-content: center;
  }
}
</style>