<template>
  <el-scrollbar height="calc(100vh)" style="padding-right:0">
    <div class="interface-container">
      <div class="section-header">
        <span class="section-title">API信息</span>
      </div>
      
      <el-form :rules="rulesinterface" ref="interfaceRef" :model="caseInfo" label-width="90px" size="small" class="api-form">
        <!-- URL和操作按钮行 -->
        <el-row :gutter="15" class="url-row">
          <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
            <el-form-item prop="url" label="请求地址" class="url-form-item">
              <el-input v-model="caseInfo.url" placeholder="请输入接口地址" class="url-input">
                <template #prepend >
                  <el-select v-model="caseInfo.method" placeholder="请求类型" class="method-select">
                    <el-option label="GET" value="GET" class="method-get"/>
                    <el-option label="POST" value="POST" class="method-post"/>
                    <el-option label="PUT" value="PUT" class="method-put"/>
                    <el-option label="PATCH" value="PATCH" class="method-patch"/>
                    <el-option label="DELETE" value="DELETE" class="method-delete"/>
                    <el-option label="HEAD" value="HEAD" class="method-head"/>
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
            <div class="action-buttons">
              <el-button @click="runCase" type="success" class="action-button"><el-icon><Promotion /></el-icon>调试</el-button>
              <el-button @click="addClick" type="primary" class="action-button"><el-icon><Plus /></el-icon>新建</el-button>
            </div>
          </el-col>
        </el-row>
        
        <!-- 基本信息区域 -->
        <div class="form-card">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="7" :lg="7" :xl="7">
              <el-form-item label="节点/模块" class="form-item">
                <el-cascader
                    v-model="caseInfo.treenode"
                    :options="options"
                    :props="{label:'name', value:'id',checkStrictly: true}"
                    @change="removeCascaderAriaOwns"
                    @visible-change="removeCascaderAriaOwns"
                    @expand-change="removeCascaderAriaOwns"
                    clearable
                    change-on-select
                    filterable
                    placeholder="请选择节点/模块"
                    class="full-width"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="10" :lg="10" :xl="10">
              <el-form-item label="接口名称" prop="name" class="form-item">
                <el-input v-model="caseInfo.name" placeholder="请输入接口名称" clearable class="full-width"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="7" :lg="7" :xl="7">
              <el-form-item label="数据锁定" class="form-item">
                <el-select v-model="caseInfo.YApi_status" placeholder="请选择" class="full-width">
                  <el-option label="已锁定" value="1"></el-option>
                  <el-option label="无需锁定" value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <el-form-item label="描述" class="form-item">
                <el-input v-model="caseInfo.desc" type="textarea" clearable class="full-width" :rows="2"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <el-form-item label="接口标签" class="form-item">
                <div class="tags-container">
                  <el-tag
                    v-for="tag in caseInfo.interface_tag"
                    :key="tag"
                    class="tag-item"
                    :type="getRandomType()"
                    closable
                    :disable-transitions="false"
                    @close="removeTag(tag)"
                    effect="light"
                    size="small"
                  >{{ tag }}</el-tag>
                  <el-input
                    v-if="state.editTag"
                    ref="caseTagInputRef"
                    v-model="state.tagValue"
                    size="small"
                    @keyup.enter="addTag"
                    @blur="addTag"
                    class="tag-input"
                    maxlength="30"
                  />
                  <el-button v-else size="small" @click="showEditTag" class="add-tag-btn">+ 添加</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        
        <!-- 元信息区域 - 重新设计 -->
        <div class="meta-card">
          <div class="meta-content">
            <div class="meta-item">
              <div class="meta-label">创建用户</div>
              <div class="meta-value">{{username}}</div>
            </div>
          </div>
        </div>
      </el-form>
      
      <div class="section-header">
        <span class="section-title">请求信息</span>
      </div>
      
      <!-- 请求信息选项卡 -->
      <el-tabs type="border-card" class="request-tabs">
        <el-tab-pane label="请求头(headers)"><Editor v-model="headers"></Editor></el-tab-pane>
        <el-tab-pane label="查询参数(Params)"><Editor v-model="params"></Editor></el-tab-pane>
        <el-tab-pane label="请求体(Body)">
          <div class="body-type-selector">
            <el-radio-group v-model="paramType" class="param-type-group">
              <el-radio label="json">application/json</el-radio>
              <el-radio label="data">x-www-form-urlencoded</el-radio>
              <el-radio label="formData">form-data</el-radio>
            </el-radio-group>
          </div>
          <div v-if="paramType === 'json'" class="editor-container"><Editor v-model="json"></Editor></div>
          <div v-else-if="paramType === 'data'" class="editor-container"><Editor v-model="data"></Editor></div>
          <div v-else-if="paramType === 'formData'" class="form-data-container">
            <FromData v-model="file"></FromData>
          </div>
        </el-tab-pane>
        <el-tab-pane label="前置脚本">
          <el-row :gutter="16">
            <el-col :xs="24" :sm="24" :md="18" :lg="18" :xl="18" class="script-editor">
              <Editor v-model="caseInfo.setup_script" lang="python" theme="monokai"></Editor>
            </el-col>
            <el-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6" class="script-templates">
              <div class="templates-header">脚本模板</div>
              <div class="templates-container">
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addSetUptCodeMod('ENV')">预设全局变量</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addSetUptCodeMod('env')">预设局部变量</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addSetUptCodeMod('func')">调用全局函数</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addSetUptCodeMod('sql')">执行sql查询</el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="后置脚本">
          <el-row :gutter="16">
            <el-col :xs="24" :sm="24" :md="18" :lg="18" :xl="18" class="script-editor">
              <Editor v-model="caseInfo.teardown_script" lang="python" theme="monokai"></Editor>
            </el-col>
            <el-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6" class="script-templates">
              <div class="templates-header">脚本模板</div>
              <div class="templates-container">
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('getBody')">获取响应体</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('JSextract')">jsonpath提取</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('REextract')">正则提取</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('ENV')">设置全局变量</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('env')">设置局部变量</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('func')">调用全局函数</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('sql')">执行sql查询</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('http')">断言HTTP状态</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('eq')">断言相等</el-button>
                </div>
                <div class="code-mod">
                  <el-button type="success" size="small" plain @click="addTearDownCodeMod('contain')">断言包含</el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      
      <!-- 执行结果 -->
      <div v-if="runResult" class="result-section">
        <div class="section-header">
          <span class="section-title">执行结果</span>
        </div>
        <caseResult :result="runResult"></caseResult>
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
import caseResult from '@/components/common/caseResult.vue';
import FromData from '@/components/common/FormData.vue'
import Editor from "@/components/common/Editor";
import {mapState} from "vuex";
import {ElMessage} from "element-plus";
import {
  Plus,
  Promotion
} from '@element-plus/icons-vue'

export default {
  props: ['treeId'],
  components: {
    caseResult,
    FromData,
    Editor,
    Plus,
    Promotion
  },
  data() {
    return {
      rulesinterface: {
        // 验证名称是否合法
        name: [
          {
            required: true,
            message: '请输入接口名称',
            trigger: 'blur'
          }
        ],
        // 验证url是否合法
        url: [
          {
            required: true,
            message: '请输入接口信息',
            trigger: 'blur'
          }
        ]
      },
      addForm:{},
      state: {
        form: {
          item:  [
          { type: ''},
          { type: 'success'},
          { type: 'info'},
          { type: 'danger'},
          { type: 'warning'}
        ]
        },
        editTag: false, // 标记是否处于编辑状态
        tagValue: '', // 输入框中的值
      },
      options:[],
      caseInfo: {
        method: 'POST',
        interface_tag:[],
        url: '',
        name:'',
        treenode:this.treeId,
        creator:'',
        modifier:'',
        desc:'',
        headers: {},
        request: {"json": {}, "data": null, "params": {}},
        file: [],
        setup_script: '# 前置脚本(python):\n' +
            '# global_tools:全局工具函数\n' +
            '# data:用例数据 \n' +
            '# env: 局部环境\n' +
            '# ENV: 全局环境\n' +
            '# db: 数据库操作对象',
        teardown_script: '# 后置脚本(python):\n' +
            '# global_tools:全局工具函数\n' +
            '# data:用例数据 \n' +
            '# response:响应对象response \n' +
            '# env: 局部环境\n' +
            '# ENV: 全局环境\n' +
            '# db: 数据库操作对象'
      },
      paramType: 'json',
      json: '{}',
      data: '{}',
      params: '{}',
      headers: '{}',
      interfaceparams: '{}',
      file: [],
      runResult: ""
    }
  },
  computed: {
    ...mapState(['pro', 'envId']),
    username() {
      return window.sessionStorage.getItem('username');
    }
  },
  methods:{
    // 标签功能点击自动聚焦
    focusInput() {
      this.$nextTick(() => {
        this.$refs.caseTagInputRef.focus();
      });
    },
    // 新增标签
    addTag() {
      if (this.state.editTag && this.state.tagValue) {
        if (!this.caseInfo.interface_tag) this.caseInfo.interface_tag = [];
        this.caseInfo.interface_tag.push(this.state.tagValue);
        this.focusInput();
      }
      this.state.editTag = false;
      this.state.tagValue = '';
    },

    // 删除标签
    removeTag(tag) {
      this.caseInfo.interface_tag.splice(this.caseInfo.interface_tag.indexOf(tag), 1);
    },

    // 确定保存标签
    showEditTag() {
      this.state.editTag = true;
      this.focusInput();
    },
    // 随机创建不一样type的标签
    getRandomType() {
      const randomIndex = Math.floor(Math.random() * this.state.form.item.length);
      return this.state.form.item[randomIndex].type;
    },

    // 树结构列表接口
    async allTree() {
      const response = await this.$api.getTreeNode({project_id: this.pro.id})
      if (response.status === 200) {
        this.options = response.data.result}
     },

    // 解决el-cascader组件页面卡顿问题
    removeCascaderAriaOwns() {
      this.$nextTick(() => {
        const $el = document.querySelectorAll(
                '.el-cascader-panel .el-cascader-node[aria-owns]'
        );
        Array.from($el).map(item => item.removeAttribute('aria-owns'));
      });
    },

    // 生成前置脚本的方法
    addSetUptCodeMod(tp) {
      switch (tp) {
        case 'ENV':
          this.caseInfo.setup_script += '\n# 设置全局变量 \ntest.save_global_variable("变量名",变量值)';
          break;
        case 'env':
          this.caseInfo.setup_script += '\n# 设置局部变量  \ntest.save_env_variable("变量名",变量值)';
          break;
        case 'func':
          this.caseInfo.setup_script += '\n# 调用全局工具函数random_mobile随机生成一个手机号码  \nmobile = global_func.random_mobile()';
          break;
        case 'sql':
          this.caseInfo.setup_script +=
              '\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\n# db.连接名.execute_all(sql语句) \nsql = "SELECT count(*) as count FROM futureloan.member"\nres = db.aliyun.execute_all(sql)';
          break;
      }
    },
    // 生成后置脚本的方法
    addTearDownCodeMod(tp) {
      switch (tp) {
        case 'getBody':
          this.caseInfo.teardown_script += '\n# Demo:获取响应体(json)  \nbody = response.json()';
          this.caseInfo.teardown_script += '\n# Demo2:获取响应体(字符串)  \nbody = response.text';
          break;
        case 'JSextract':
          this.caseInfo.teardown_script += '\n# Demo:jsonpath提取response中的msg字段  \nmsg = test.json_extract(response.json(),"$..msg")';
          break;
        case 'REextract':
          this.caseInfo.teardown_script += '\n# Demo:正则提取响应体中的数据  \nres = test.re_extract(response.text,"正则表达式",)';
          break;
        case 'ENV':
          this.caseInfo.teardown_script += '\n# 设置全局变量 \ntest.save_global_variable("变量名",变量值)';
          break;
        case 'env':
          this.caseInfo.teardown_script += '\n# 设置局部变量  \ntest.save_env_variable("变量名",变量值)';
          break;
        case 'func':
          this.caseInfo.teardown_script += '\n# 调用全局工具函数random_mobile随机生成一个手机号码  \nmobile = global_func.random_mobile()';
          break;
        case 'sql':
          this.caseInfo.teardown_script +=
              '\n# ----执行sql语句(需要在环境中配置数据库连接信息)----\n# db.连接名.execute_all(sql语句) \nsql = "SELECT count(*) as count FROM futureloan.member"\nres = db.aliyun.execute_all(sql)';
          break;
        case 'http':
          this.caseInfo.teardown_script += '\n# 断言http状态码 \n# Demo:断言http状态码是否为200  \ntest.assertion("相等",200,response.status_code)';
          break;
        case 'eq':
          this.caseInfo.teardown_script += '\n# 断言相等 \ntest.assertion("相等","预期结果","实际结果")';
          break;
        case 'contain':
          this.caseInfo.teardown_script += '\n# 断言包含:预期结果中的内容在实际结果中是否存在 \ntest.assertion("包含","预期结果","实际结果")';
          break;
      }
    },

    //  组装新增接口的数据
    getEditData() {
      let caseData = { ...this.caseInfo };
      caseData.project = this.pro.id;
      caseData.type = 'api'
      delete caseData.status
      // 获取最后一个节点的id
      if (caseData.treenode && caseData.treenode.length > 0) {  // 检查列表是否存在且不为空
        const lastValue = caseData.treenode[caseData.treenode.length - 1];  // 获取最后一个值
        console.log(lastValue);  // 输出最后一个值
        caseData.treenode = lastValue
      } else {
        console.log('列表为空');  // 如果列表为空，输出提示信息
      }
      // tag标签改成interface_tag:{tag:[值1,值2]}
      caseData.interface_tag = {tag:[...caseData.interface_tag]};
      caseData.creator = this.username;
      try {
        caseData.headers = JSON.parse(this.headers);
      } catch (e) {
          ElMessage({
              message: '提交的headers数据 json格式错误，请检查！',
              type: 'warning',
              duration: 1000
            });
          return null;
      }
      // 请求体格式的选择
      if (this.paramType === 'json') {
        const json5 = require('json5');
        try {
          caseData.request = { json: json5.parse(this.json) };
          caseData.request.data = null;
          caseData.file = [];
        } catch (e) {
          ElMessage({
              message: "提交的application/json数据json格式错误，请检查！",
              type: 'warning',
              duration: 1000
            });
          return null;
        }
      } else if (this.paramType === 'data') {
        try {
          caseData.request = { data: JSON.parse(this.data) };
          caseData.request.json = null
          caseData.file = []
        } catch (e) {
          ElMessage({
              message: "提交的x-www-form-urlencoded数据json格式错误，请检查！",
              type: 'warning',
              duration: 1000
            });
          return null;
        }
      } else if (this.paramType === 'formData') {
        caseData.file =  this.file ;
        caseData.request = {}
      }
      try {
        caseData.request.params = JSON.parse(this.params);
        // caseData.interface = this.caseInfo.interface.id;
        return caseData;
      } catch (e) {
        ElMessage({
            message: "提交的Params数据json格式错误，请检查！",
            type: 'warning',
            duration: 1000
          });
        return null;
      }
    },

    // 新增接口
    async addClick() {
      this.$refs.interfaceRef.validate(async vaild => {
        // 判断是否验证通过，不通过则直接return
        if (!vaild) return;
        const params = this.getEditData();
        console.log('新增的参数：',params)
        const response = await this.$api.createNewInterface(params);
        if (response.status === 201) {
          ElMessage({
            type: 'success',
            message: '添加成功',
            duration: 1000
          });
          this.triggerClose()
        }
      })
    },

    // 运行用例
    async runCase() {
      if (!this.envId) {
        ElMessage({
          type: 'warning',
          message: '当前未选中执行环境!',
          duration: 1000
        });
        return
      }
      this.$refs.interfaceRef.validate(async vaild => {
        // 判断是否验证通过，不通过则直接return
        if (!vaild) return;
        const runData = this.getEditData();
        runData.interface = {
          url: this.caseInfo.url,
          method: this.caseInfo.method
        };
        const params = {
          data: runData,
          env: this.envId
        };
        const response = await this.$api.runNewCase(params);
        if (response.status === 200) {
          this.runResult = response.data;
          ElMessage({
            type: 'success',
            message: '执行完毕',
            duration: 1000
          });
        }
      })
    },

    triggerClose() {
      this.$emit('close-dialog');
    }
  },
  created() {
    this.allTree()
  }
}
</script>

<style scoped>
/* 整体容器样式 */
.interface-container {
  margin: 10px;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);
}

/* 标题样式 */
.section-header {
  margin: 20px 0 15px;
  padding-left: 12px;
  border-left: 4px solid #409EFF;
  position: relative;
  height: 22px;
  display: flex;
  align-items: center;
}

.section-header:first-child {
  margin-top: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 22px;
}

/* 表单卡片样式 */
.form-card {
  background-color: #fafbfd;
  border-radius: 6px;
  padding: 20px 18px 10px;
  margin-bottom: 18px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
}

/* 元数据区域新样式 */
.meta-card {
  background-color: #f8fafc;
  border-left: 3px solid #409EFF;
  padding: 0;
  margin-bottom: 25px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #ebeef5;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.meta-header {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f0f7ff;
}

.meta-content {
  display: flex;
  flex-wrap: wrap;
  padding: 12px;
}

.meta-item {
  flex: 1 0 25%;
  min-width: 200px;
  padding: 8px 15px;
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
}

.meta-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.meta-value {
  font-size: 13px;
  color: #303133;
  padding: 6px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 2px solid #409EFF;
  word-break: break-all;
  line-height: 1.4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.meta-value.empty {
  color: #909399;
  font-style: italic;
  border-left: 2px dashed #c0c4cc;
  background-color: #f8f8f8;
}

/* API表单样式 */
.api-form {
  margin-bottom: 20px;
}

.api-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
  text-align: right;
  line-height: 32px;
  padding: 0 12px 0 0;
}

.api-form :deep(.el-form-item__content) {
  display: flex;
  align-items: center;
  line-height: 32px;
}

.api-form :deep(.el-form-item) {
  margin-bottom: 18px;
  align-items: center;
}

/* URL行样式 */
.url-row {
  margin-bottom: 20px;
}

.url-form-item {
  margin-bottom: 0 !important;
}

.url-form-item :deep(.el-form-item__content) {
  line-height: 40px;
}

.url-input {
  width: 100%;
  font-size: 14px;
}

.url-input :deep(.el-input__wrapper) {
  padding-left: 0;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.url-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.url-input :deep(.el-input__wrapper:focus-within) {
  box-shadow: 0 0 0 1px #409EFF inset;
}

.method-select {
  width: 100px;
  font-weight: bold;
}

.method-select :deep(.el-input__wrapper) {
  border-radius: 4px 0 0 4px;
  box-shadow: none;
}

.method-get { color: rgba(204, 73, 145, 0.87); font-weight: 600; }
.method-post { color: #61affe; font-weight: 600; }
.method-put { color: #fca130; font-weight: 600; }
.method-patch { color: #50e3c2; font-weight: 600; }
.method-delete { color: #f93e3e; font-weight: 600; }
.method-head { color: rgb(180, 200, 100); font-weight: 600; }

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
  height: 100%;
  padding-top: 3px;
}

.action-button {
  margin-left: 10px;
  font-weight: 500;
  padding: 0 15px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-button .el-icon {
  margin-right: 5px;
  font-size: 16px;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 15px;
}

.form-item :deep(.el-textarea__inner) {
  line-height: 1.5;
  min-height: 60px !important;
  padding: 8px 12px;
}

.full-width {
  width: 100%;
}

/* 标签区域样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 32px;
  padding: 10px 12px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 6px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
  padding: 0 8px;
}

.tag-input {
  width: 110px;
  margin-bottom: 6px;
  height: 24px;
  line-height: 24px;
}

.add-tag-btn {
  margin-bottom: 6px;
  height: 24px;
  padding: 0 10px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-color: #e1f3d8;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.add-tag-btn:hover {
  background-color: #e1f3d8;
  color: #67c23a;
}

/* 请求信息选项卡样式 */
.request-tabs {
  margin-bottom: 20px;
  border-radius: 6px;
  min-height: 380px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #dcdfe6;
}

.request-tabs :deep(.el-tabs__header) {
  background-color: #f5f7fa;
  padding: 0 15px;
  margin: 0;
}

.request-tabs :deep(.el-tabs__nav) {
  border: none;
}

.request-tabs :deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.request-tabs :deep(.el-tabs__item.is-active) {
  background-color: #fff;
  border-radius: 4px 4px 0 0;
  color: #409EFF;
}

.request-tabs :deep(.el-tabs__item:hover) {
  color: #409EFF;
}

.body-type-selector {
  margin-bottom: 15px;
  padding-top: 10px;
}

.param-type-group {
  margin-bottom: 12px;
  background: #f8f8f8;
  padding: 10px 12px;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
  display: flex;
  flex-wrap: wrap;
}

.param-type-group :deep(.el-radio) {
  margin-right: 20px;
  margin-bottom: 5px;
}

.editor-container, .form-data-container {
  margin-top: 5px;
}

/* 脚本编辑区域 */
.script-editor {
  height: 300px;
}

/* 脚本模板区域样式 */
.script-templates {
  padding: 0;
  margin-top: 0;
}

.templates-header {
  font-size: 14px;
  font-weight: 500;
  color: #409EFF;
  padding: 10px 0;
  margin-bottom: 8px;
  border-bottom: 1px solid #EBEEF5;
  text-align: center;
  background-color: #f0f7ff;
  border-radius: 4px 4px 0 0;
}

.templates-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #EBEEF5;
  border-radius: 0 0 4px 4px;
  padding: 10px;
  background-color: #F5F7FA;
}

.code-mod {
  margin-bottom: 8px;
  text-align: center;
  width: 100%;
}

.code-mod .el-button {
  width: 100%;
  padding: 0 10px;
  font-size: 12px;
  transition: all 0.3s;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-mod .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 结果区域样式 */
.result-section {
  margin-top: 15px;
  padding: 15px;
  border-radius: 6px;
  background-color: #F5F7FA;
  border: 1px solid #e6e6e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 响应式样式 */
@media (max-width: 992px) {
  .action-buttons {
    justify-content: flex-start;
    margin-top: 8px;
    padding-top: 0;
  }
  
  .action-button {
    margin-left: 0;
    margin-right: 8px;
    margin-bottom: 8px;
  }
  
  .script-templates {
    margin-top: 15px;
  }
  
  .interface-container {
    margin: 8px;
    padding: 15px;
  }
  
  .form-card {
    padding: 15px;
  }
  
  .meta-item {
    flex: 1 0 50%;
    min-width: 150px;
    padding: 5px 10px;
  }
  
  .meta-label {
    font-size: 11px;
  }
  
  .meta-value {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .code-mod .el-button {
    font-size: 12px;
    padding: 0 2px;
  }
}

@media (max-width: 768px) {
  .meta-item {
    flex: 1 0 100%;
  }
  
  .interface-container {
    padding: 10px;
  }
  
  .form-card {
    padding: 12px;
  }
  
  .section-header {
    margin: 15px 0 10px;
  }
}
</style>