<template>
  <div class="while-loop-config">
    <h4 class="section-title">While循环条件设置</h4>
    
    <!-- 条件类型选择 -->
    <div class="config-group">
      <label class="config-label">条件类型</label>
      <el-select 
        v-model="localConfig.whileConditionType" 
        placeholder="选择条件类型"
        style="width: 200px;"
        @change="handleConditionTypeChange"
      >
        <el-option label="变量比较" value="variable" />
        <el-option label="表达式" value="expression" />
        <el-option label="脚本函数" value="function" />
      </el-select>
    </div>

    <!-- 变量比较模式 -->
    <div v-if="isVariableMode" class="condition-config">
      <div class="config-group">
        <label class="config-label">左操作数</label>
        <el-input
          v-model="localConfig.whileLeftOperand"
          placeholder="变量，例如{{counter}}"
          style="width: 200px;"
        />
      </div>
      
      <div class="config-group">
        <label class="config-label">比较操作符</label>
        <el-select 
          v-model="localConfig.whileOperator" 
          placeholder="选择操作符"
          style="width: 150px;"
        >
          <el-option
            v-for="op in comparisonOperators"
            :key="op.value"
            :label="op.label"
            :value="op.value"
          />
        </el-select>
      </div>
      
      <div class="config-group">
        <label class="config-label">右操作数</label>
        <el-input
          v-model="localConfig.whileRightOperand"
          placeholder="值或变量，例如10或{{max}}"
          style="width: 200px;"
        />
      </div>
    </div>

    <!-- 表达式模式 -->
    <div v-if="isExpressionMode" class="condition-config">
      <div class="config-group full-width">
        <label class="config-label">条件表达式</label>
        <el-input
          v-model="localConfig.whileExpression"
          type="textarea"
          :rows="3"
          placeholder="例如: {{counter}} < 100 and {{status}} == 'running'"
          style="width: 100%; max-width: 500px;"
        />
      </div>
      
      <div class="help-info">
        <el-alert
          title="表达式说明"
          type="info"
          show-icon
          :closable="false"
        >
          <template #default>
            <div class="help-content">
              <div>• 支持变量引用: {{variable_name}}</div>
              <div>• 支持比较操作: &lt;, &lt;=, &gt;, &gt;=, ==, !=</div>
              <div>• 支持逻辑操作: and, or, not</div>
              <div>• 支持函数调用: len({{list_var}}), int({{str_var}})</div>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 脚本函数模式 -->
    <div v-if="isFunctionMode" class="condition-config">
      <div class="config-group">
        <label class="config-label">函数名称</label>
        <el-input
          v-model="localConfig.whileFunctionName"
          placeholder="例如: check_condition"
          style="width: 200px;"
        />
      </div>
      
      <div class="config-group">
        <label class="config-label">函数参数</label>
        <el-input
          v-model="localConfig.whileFunctionArgs"
          placeholder="例如: {{var1}}, {{var2}}, 'constant'"
          style="width: 300px;"
        />
      </div>
      
      <div class="help-info">
        <el-alert
          title="函数使用说明"
          type="warning"
          show-icon
          :closable="false"
        >
          <template #default>
            <div class="help-content">
              <div>• 函数必须返回布尔值 (True/False)</div>
              <div>• 函数需要在全局作用域中定义</div>
              <div>• 参数支持变量引用和常量值</div>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 通用配置 -->
    <div class="common-config">
      <h4 class="section-title">循环控制设置</h4>
      
      <div class="config-group">
        <label class="config-label">最大循环次数</label>
        <el-input-number
          v-model="localConfig.whileMaxIterations"
          :min="1"
          :max="10000"
          placeholder="次"
          style="width: 150px;"
        />
        <span class="help-text">防止无限循环</span>
      </div>
      
      <div class="config-group">
        <label class="config-label">循环间隔</label>
        <el-input-number
          v-model="localConfig.cycleInterval"
          :min="0"
          :max="999"
          placeholder="秒"
          style="width: 120px;"
        />
        <span class="unit">秒</span>
      </div>
      
      <div class="config-group">
        <label class="config-label">超时时间</label>
        <el-input-number
          v-model="localConfig.whileTimeout"
          :min="0"
          :max="3600"
          placeholder="秒"
          style="width: 120px;"
        />
        <span class="help-text">秒 (0表示无超时)</span>
      </div>
      
      <div class="config-group">
        <label class="config-label">循环计数器变量</label>
        <el-input
          v-model="localConfig.whileCounterVar"
          placeholder="例如: loop_counter"
          style="width: 200px;"
        />
        <span class="help-text">可在条件和子步骤中使用</span>
      </div>
    </div>

    <!-- 高级选项 -->
    <div class="advanced-options">
      <el-divider content-position="left">
        <span class="divider-text">高级选项</span>
      </el-divider>
      
      <div class="checkbox-group">
        <el-checkbox v-model="localConfig.whileBreakOnError">
          遇到错误时终止循环
        </el-checkbox>
        <el-checkbox v-model="localConfig.whileLogIterations">
          记录每次迭代日志
        </el-checkbox>
      </div>
      
      <div class="config-group">
        <label class="config-label">条件检查时机</label>
        <el-radio-group v-model="localConfig.whileCheckTiming" size="small">
          <el-radio label="before">执行前检查</el-radio>
          <el-radio label="after">执行后检查</el-radio>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>

<script>
import { WHILE_OPERATORS } from '@/constants/performanceConstants';

export default {
  name: 'WhileLoopConfig',
  
  props: {
    modelValue: {
      type: Object,
      required: true
    }
  },
  
  emits: ['update:modelValue'],
  
  data() {
    return {
      localConfig: { ...this.modelValue },
      comparisonOperators: WHILE_OPERATORS
    }
  },
  
  computed: {
    /**
     * 是否为变量比较模式
     */
    isVariableMode() {
      return this.localConfig.whileConditionType === 'variable';
    },
    
    /**
     * 是否为表达式模式
     */
    isExpressionMode() {
      return this.localConfig.whileConditionType === 'expression';
    },
    
    /**
     * 是否为函数模式
     */
    isFunctionMode() {
      return this.localConfig.whileConditionType === 'function';
    }
  },
  
  watch: {
    modelValue: {
      handler(newValue) {
        this.localConfig = { ...newValue };
      },
      deep: true,
      immediate: true
    },
    
    localConfig: {
      handler(newValue) {
        this.$emit('update:modelValue', { ...newValue });
      },
      deep: true
    }
  },
  
  methods: {
    /**
     * 处理条件类型变化
     */
    handleConditionTypeChange(newType) {
      // 清除其他模式的配置
      if (newType === 'variable') {
        this.localConfig.whileExpression = '';
        this.localConfig.whileFunctionName = '';
        this.localConfig.whileFunctionArgs = '';
      } else if (newType === 'expression') {
        this.localConfig.whileLeftOperand = '';
        this.localConfig.whileOperator = 'lt';
        this.localConfig.whileRightOperand = '';
        this.localConfig.whileFunctionName = '';
        this.localConfig.whileFunctionArgs = '';
      } else if (newType === 'function') {
        this.localConfig.whileLeftOperand = '';
        this.localConfig.whileOperator = 'lt';
        this.localConfig.whileRightOperand = '';
        this.localConfig.whileExpression = '';
      }
    }
  }
};
</script>

<style scoped>
.while-loop-config {
  padding: 16px;
  background: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.section-title {
  color: #409eff;
  font-size: 14px;
  margin: 0 0 16px 0;
  font-weight: 600;
}

.condition-config {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  margin: 16px 0;
}

.common-config {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  margin: 16px 0;
}

.config-group {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.config-group.full-width {
  flex-direction: column;
  align-items: flex-start;
}

.config-group.full-width .config-label {
  margin-bottom: 8px;
}

.config-label {
  min-width: 120px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.unit,
.help-text {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.help-info {
  margin-top: 12px;
}

.help-content {
  font-size: 12px;
  line-height: 1.6;
}

.help-content div {
  margin-bottom: 4px;
}

.advanced-options {
  margin-top: 20px;
}

.divider-text {
  color: #909399;
  font-size: 12px;
}

.checkbox-group {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .config-label {
    min-width: auto;
    margin-bottom: 4px;
  }
  
  .checkbox-group {
    flex-direction: column;
    gap: 8px;
  }
  
  .config-group .el-input,
  .config-group .el-select {
    width: 100% !important;
    max-width: 300px;
  }
}
</style>