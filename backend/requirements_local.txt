# Django核心依赖
Django>=3.2.13,<5.0
django-cors-headers==3.13.0
django-filter==22.1
djangorestframework==3.14.0
djangorestframework-simplejwt==5.2.2

# 数据库相关
PyMySQL==1.0.2

# HTTP请求库
requests==2.28.1
requests_toolbelt==0.10.1
aiohttp>=3.8.0

# 数据处理相关
jsonpath==0.82
jsonpath_ng==1.5.3
pandas>=1.5.0
numpy>=1.21.0
openpyxl>=3.0.0
PyYAML>=6.0

# 加密和安全
rsa>=4.0

# 测试数据生成
faker>=15.0.0

# 异步任务队列
celery>=5.2.0
redis>=4.0.0
django-celery-beat>=2.4.0
django-celery-results>=2.5.0

# WebSocket支持
channels>=3.0.0
channels_redis>=4.0.0
django-redis>=5.0.0

# SSH连接
paramiko>=3.0.0

# 性能测试工具
locust>=2.0.0
psutil>=5.0.0

# 报告生成
jinja2>=3.0.0
matplotlib>=3.0.0
seaborn>=0.11.0

# API文档
uritemplate>=4.0.0
coreapi>=2.3.0
drf-yasg>=1.20.0

# 部署相关
gunicorn>=20.0.0
supervisor>=4.0.0

# WebSocket异步支持
websockets>=10.0

# 统计分析
scipy>=1.7.0
