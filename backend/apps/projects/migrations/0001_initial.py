# Generated by Django 4.1.13 on 2025-07-07 07:38

from django.db import migrations, models
import django.db.models.deletion
import projects.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Mock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(blank=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('name', models.CharField(help_text='接口名称', max_length=50, verbose_name='接口名')),
                ('method', models.CharField(help_text='请求方法', max_length=50, verbose_name='请求方法')),
                ('url', models.CharField(help_text='接口路径', max_length=200, verbose_name='接口路径')),
                ('status', models.BooleanField(default=False, verbose_name='状态')),
                ('mockId', models.CharField(default=projects.models.generate_uuid, max_length=32, unique=True)),
            ],
            options={
                'verbose_name': 'mock接口表',
                'verbose_name_plural': 'mock接口表',
                'db_table': 'mock',
            },
        ),
        migrations.CreateModel(
            name='MockLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interface', models.CharField(help_text='接口路径', max_length=200, verbose_name='接口路径')),
                ('method', models.CharField(help_text='请求方法', max_length=50, verbose_name='请求方法')),
                ('status_code', models.IntegerField(blank=True, help_text='状态码', null=True, verbose_name='状态码')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('callIp', models.CharField(help_text='调用ip', max_length=50, verbose_name='调用ip')),
            ],
            options={
                'verbose_name': 'mock接口日志表',
                'verbose_name_plural': 'mock接口日志表',
                'db_table': 'mockLog',
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='项目名称', max_length=50, verbose_name='项目名')),
                ('desc', models.CharField(blank=True, help_text='项目描述', max_length=200, null=True, verbose_name='项目描述')),
                ('leader', models.CharField(blank=True, default='', help_text='负责人', max_length=50, null=True, verbose_name='负责人')),
                ('leader_id', models.SmallIntegerField(blank=True, help_text='负责人id', null=True, verbose_name='负责人id')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '项目表',
                'verbose_name_plural': '项目表',
                'db_table': 'project',
            },
        ),
        migrations.CreateModel(
            name='TreeNode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='结构名称', max_length=100, verbose_name='结构名称')),
                ('type', models.IntegerField(blank=True, help_text='暂时不用', null=True, verbose_name='接口类型')),
                ('enable_flag', models.IntegerField(default=10, help_text='是否可用 10-可用 20-删除', verbose_name='删除')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='projects.treenode')),
                ('project', models.ForeignKey(help_text='项目id', on_delete=django.db.models.deletion.CASCADE, related_name='treenodes', to='projects.project', verbose_name='项目id')),
            ],
            options={
                'verbose_name': '树结构表',
                'verbose_name_plural': '树结构表',
                'db_table': 'TreeNode',
            },
        ),
        migrations.CreateModel(
            name='TestEnv',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='环境名称', max_length=150, verbose_name='环境名称')),
                ('global_variable', models.JSONField(blank=True, default=dict, help_text='全局变量', null=True, verbose_name='全局变量')),
                ('debug_global_variable', models.JSONField(blank=True, default=dict, help_text='debug模式全局变量', null=True, verbose_name='debug模式全局变量')),
                ('db', models.JSONField(blank=True, default=projects.models.get_default_db_config, help_text='数据库配置', null=True, verbose_name='数据库配置')),
                ('host', models.CharField(blank=True, help_text='base_url地址', max_length=100, verbose_name='base_url地址')),
                ('headers', models.JSONField(blank=True, default=dict, help_text='请求头', null=True, verbose_name='请求头')),
                ('global_func', models.TextField(blank=True, default='# -*- coding: utf-8 -*-\n# @author: HRUN\n\n"""\n自定义全局工具函数\n============================\n"""\nfrom apitestengine.core.tools import *\n', help_text='用例工具文件', null=True, verbose_name='用例工具文件')),
                ('project', models.ForeignKey(help_text='项目id', on_delete=django.db.models.deletion.CASCADE, related_name='test_envs', to='projects.project', verbose_name='项目id')),
            ],
            options={
                'verbose_name': '测试环境表',
                'verbose_name_plural': '测试环境表',
                'db_table': 'tb_test_env',
            },
        ),
        migrations.CreateModel(
            name='newInterface',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='接口名称', max_length=50, verbose_name='接口名')),
                ('host', models.JSONField(blank=True, default=dict, help_text='域名', verbose_name='域名')),
                ('url', models.CharField(help_text='接口路径', max_length=200, verbose_name='接口路径')),
                ('method', models.CharField(help_text='请求方法', max_length=50, verbose_name='请求方法')),
                ('headers', models.JSONField(blank=True, default=dict, help_text='请求头', verbose_name='请求头')),
                ('request', models.JSONField(blank=True, default=dict, help_text='请求信息', verbose_name='请求信息')),
                ('file', models.JSONField(blank=True, default=list, help_text='上传的文件参数', verbose_name='上传的文件')),
                ('setup_script', models.TextField(blank=True, default='# 前置脚本(python):\n# global_tools:全局工具函数\n# data:用例数据 \n# env: 局部环境\n# ENV: 全局环境\n# db: 数据库操作对象\n', help_text='前置脚本', verbose_name='前置脚本')),
                ('teardown_script', models.TextField(blank=True, default='# 后置脚本(python):\n# global_tools:全局工具函数\n# data:用例数据 \n# response:响应对象response \n# env: 局部环境\n# ENV: 全局环境\n# db: 数据库操作对象\n', help_text='后置脚本', verbose_name='用例后置脚本')),
                ('interface_tag', models.JSONField(blank=True, default=list, help_text='tag标签', verbose_name='tag标签')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(blank=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('desc', models.CharField(blank=True, help_text='接口描述', max_length=200, null=True, verbose_name='接口描述')),
                ('type', models.CharField(default='api', help_text='类型', max_length=20, verbose_name='类型')),
                ('YApi_id', models.IntegerField(blank=True, help_text='YApi接口id', null=True, verbose_name='YApi接口id')),
                ('YApi_status', models.IntegerField(blank=True, default=0, help_text='YApi接口状态', null=True, verbose_name='YApi接口状态')),
                ('status', models.CharField(choices=[('开发中', '开发中'), ('测试中', '测试中'), ('已发布', '已发布'), ('已废弃', '已废弃')], default='状态', help_text='接口状态', max_length=40, verbose_name='状态')),
                ('project', models.ForeignKey(help_text='项目id', on_delete=django.db.models.deletion.CASCADE, related_name='new_interface', to='projects.project', verbose_name='项目id')),
                ('treenode', models.ForeignKey(help_text='节点id', on_delete=django.db.models.deletion.CASCADE, related_name='newinterface', to='projects.treenode', verbose_name='节点id')),
            ],
            options={
                'verbose_name': '新接口表',
                'verbose_name_plural': '新接口表',
                'db_table': 'tb_newInterface',
            },
        ),
        migrations.CreateModel(
            name='MockDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(blank=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('name', models.CharField(help_text='期望名称', max_length=50, verbose_name='期望名称')),
                ('conditionForm', models.JSONField(blank=True, default=list, help_text='条件', verbose_name='条件')),
                ('ipCode', models.BooleanField(default=False, verbose_name='是否开启生效ip')),
                ('ipInput', models.CharField(blank=True, help_text='生效ip', max_length=50, null=True, verbose_name='生效ip')),
                ('headers', models.JSONField(blank=True, default=dict, help_text='响应头', verbose_name='响应头')),
                ('response', models.JSONField(blank=True, default=dict, help_text='响应体', verbose_name='响应体')),
                ('config', models.JSONField(blank=True, default=dict, help_text='配置', verbose_name='配置')),
                ('mock', models.ForeignKey(help_text='mock', on_delete=django.db.models.deletion.CASCADE, related_name='MockDetail', to='projects.mock')),
            ],
            options={
                'verbose_name': 'mock接口详情表',
                'verbose_name_plural': 'mock接口详情表',
                'db_table': 'mockDetail',
            },
        ),
        migrations.AddField(
            model_name='mock',
            name='newInterface',
            field=models.OneToOneField(help_text='接口id', on_delete=django.db.models.deletion.CASCADE, to='projects.newinterface', verbose_name='mock'),
        ),
    ]
