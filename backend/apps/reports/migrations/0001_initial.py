# Generated by Django 4.1.13 on 2025-07-07 07:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('projects', '0001_initial'),
        ('testplans', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Record',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('all', models.IntegerField(blank=True, default=0, help_text='用例总数', verbose_name='用例总数')),
                ('success', models.IntegerField(blank=True, default=0, help_text='成功用例', verbose_name='成功用例')),
                ('fail', models.IntegerField(blank=True, default=0, help_text='失败用例', verbose_name='失败用例')),
                ('error', models.IntegerField(blank=True, default=0, help_text='错误用例', verbose_name='错误用例')),
                ('pass_rate', models.CharField(blank=True, default=0, help_text='执行通过率', max_length=100, verbose_name='执行通过率')),
                ('tester', models.CharField(blank=True, help_text='执行者', max_length=100, verbose_name='执行者')),
                ('status', models.CharField(help_text='执行状态', max_length=100, verbose_name='执行状态')),
                ('execute_type', models.CharField(blank=True, help_text='执行类型', max_length=100, null=True, verbose_name='执行类型')),
                ('plan', models.ForeignKey(help_text='执行计划', on_delete=django.db.models.deletion.PROTECT, related_name='records', to='testplans.testplan', verbose_name='执行计划')),
                ('project', models.ForeignKey(blank=True, help_text='项目id', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='test_record', to='projects.project', verbose_name='项目id')),
                ('test_env', models.ForeignKey(help_text='测试环境', on_delete=django.db.models.deletion.PROTECT, to='projects.testenv', verbose_name='测试环境')),
            ],
            options={
                'verbose_name': '运行记录表',
                'verbose_name_plural': '运行记录表',
                'db_table': 'tb_record',
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('info', models.JSONField(blank=True, default=dict, help_text='测试报告', verbose_name='测试报告')),
                ('record', models.OneToOneField(help_text='测试记录', on_delete=django.db.models.deletion.PROTECT, to='reports.record', verbose_name='测试记录')),
            ],
            options={
                'verbose_name': '报告表',
                'verbose_name_plural': '报告表',
                'db_table': 'tb_report',
            },
        ),
    ]
