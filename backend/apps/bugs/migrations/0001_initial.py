# Generated by Django 4.1.13 on 2025-07-07 07:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('projects', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Bug',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('desc', models.TextField(blank=True, help_text='bug描述', max_length=3000, verbose_name='bug描述')),
                ('info', models.JSONField(blank=True, default=dict, help_text='测试报告', verbose_name='测试报告')),
                ('status', models.CharField(choices=[('待处理', '待处理'), ('处理中', '处理中'), ('已关闭', '已关闭'), ('处理完成', '处理完成'), ('无需处理', '无需处理')], default='1', help_text='bug状态', max_length=40, verbose_name='状态')),
                ('user', models.CharField(blank=True, default='', help_text='提交者', max_length=40, verbose_name='提交者')),
                ('remark', models.TextField(blank=True, help_text='备注', max_length=3000, verbose_name='备注')),
                ('interface', models.ForeignKey(help_text='接口', on_delete=django.db.models.deletion.CASCADE, to='projects.newinterface', verbose_name='接口')),
                ('project', models.ForeignKey(help_text='项目id', on_delete=django.db.models.deletion.CASCADE, to='projects.project', verbose_name='项目id')),
            ],
            options={
                'verbose_name': 'bug表',
                'verbose_name_plural': 'bug表',
                'db_table': 'tb_bug',
            },
        ),
        migrations.CreateModel(
            name='BugHandle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('handle', models.TextField(blank=True, help_text='处理操作', verbose_name='处理操作')),
                ('update_user', models.CharField(blank=True, help_text='处理操作', max_length=32, verbose_name='更新用户')),
                ('remark', models.TextField(blank=True, help_text='备注', max_length=3000, verbose_name='备注')),
                ('bug', models.ForeignKey(help_text='bug ID', on_delete=django.db.models.deletion.CASCADE, to='bugs.bug', verbose_name='bug ID')),
            ],
            options={
                'verbose_name': 'bug操作记录表',
                'verbose_name_plural': 'bug操作记录表',
                'db_table': 'tb_bug_handle',
            },
        ),
    ]
