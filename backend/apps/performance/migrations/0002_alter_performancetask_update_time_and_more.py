# Generated by Django 4.1.13 on 2025-07-09 15:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0002_alter_mock_update_time_alter_mockdetail_update_time'),
        ('performance', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='performancetask',
            name='update_time',
            field=models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间'),
        ),
        migrations.AlterField(
            model_name='presetting',
            name='update_time',
            field=models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间'),
        ),
        migrations.AlterField(
            model_name='server',
            name='update_time',
            field=models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='taskreport',
            name='update_time',
            field=models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间'),
        ),
        migrations.AlterField(
            model_name='taskscencestep',
            name='update_time',
            field=models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间'),
        ),
        migrations.AlterField(
            model_name='taskstep',
            name='type',
            field=models.CharField(choices=[('api', 'HTTP接口'), ('tcp', 'TCP连接'), ('udp', 'UDP连接'), ('websocket', 'WebSocket'), ('wait', '等待'), ('script', '自定义脚本'), ('loop', '循环控制器'), ('if', '条件控制器')], help_text='步骤控制器类型', max_length=50, verbose_name='步骤控制器类型'),
        ),
        migrations.AlterField(
            model_name='taskstep',
            name='update_time',
            field=models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间'),
        ),
        migrations.CreateModel(
            name='PerformanceBaseline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('name', models.CharField(help_text='基准线名称', max_length=128, verbose_name='基准线名称')),
                ('description', models.TextField(blank=True, help_text='基准线描述', null=True, verbose_name='基准线描述')),
                ('environment', models.CharField(default='default', help_text='环境标识', max_length=50, verbose_name='环境标识')),
                ('baseline_tps', models.FloatField(blank=True, help_text='基准TPS', null=True, verbose_name='基准TPS')),
                ('baseline_response_time', models.FloatField(blank=True, help_text='基准响应时间(ms)', null=True, verbose_name='基准响应时间')),
                ('baseline_error_rate', models.FloatField(blank=True, help_text='基准错误率(%)', null=True, verbose_name='基准错误率')),
                ('baseline_cpu', models.FloatField(blank=True, help_text='基准CPU使用率(%)', null=True, verbose_name='基准CPU使用率')),
                ('baseline_memory', models.FloatField(blank=True, help_text='基准内存使用率(%)', null=True, verbose_name='基准内存使用率')),
                ('tps_tolerance', models.FloatField(default=10.0, help_text='TPS容差(%)', verbose_name='TPS容差')),
                ('response_time_tolerance', models.FloatField(default=15.0, help_text='响应时间容差(%)', verbose_name='响应时间容差')),
                ('error_rate_tolerance', models.FloatField(default=50.0, help_text='错误率容差(%)', verbose_name='错误率容差')),
                ('cpu_tolerance', models.FloatField(default=20.0, help_text='CPU容差(%)', verbose_name='CPU容差')),
                ('memory_tolerance', models.FloatField(default=20.0, help_text='内存容差(%)', verbose_name='内存容差')),
                ('is_active', models.BooleanField(default=True, help_text='是否激活', verbose_name='是否激活')),
                ('created_by', models.CharField(help_text='创建人', max_length=50, verbose_name='创建人')),
                ('baseline_data', models.TextField(blank=True, help_text='原始基准数据(JSON格式)', null=True, verbose_name='原始基准数据')),
                ('task', models.ForeignKey(help_text='关联任务', on_delete=django.db.models.deletion.CASCADE, related_name='baselines', to='performance.performancetask', verbose_name='关联任务')),
            ],
            options={
                'verbose_name': '性能基准线表',
                'verbose_name_plural': '性能基准线表',
                'db_table': 'performance_baseline',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='BaselineComparison',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('comparison_result', models.CharField(choices=[('better', '优于基准线'), ('similar', '接近基准线'), ('worse', '劣于基准线'), ('no_baseline', '无基准线')], help_text='对比结果', max_length=20, verbose_name='对比结果')),
                ('performance_score', models.FloatField(default=0.0, help_text='性能得分', verbose_name='性能得分')),
                ('metric_comparisons', models.TextField(blank=True, help_text='指标对比详情(JSON格式)', null=True, verbose_name='指标对比详情')),
                ('recommendations', models.TextField(blank=True, help_text='优化建议(JSON格式)', null=True, verbose_name='优化建议')),
                ('tps_comparison', models.CharField(blank=True, choices=[('better', '优于基准线'), ('similar', '接近基准线'), ('worse', '劣于基准线'), ('no_baseline', '无基准线')], help_text='TPS对比结果', max_length=20, null=True, verbose_name='TPS对比结果')),
                ('response_time_comparison', models.CharField(blank=True, choices=[('better', '优于基准线'), ('similar', '接近基准线'), ('worse', '劣于基准线'), ('no_baseline', '无基准线')], help_text='响应时间对比结果', max_length=20, null=True, verbose_name='响应时间对比结果')),
                ('error_rate_comparison', models.CharField(blank=True, choices=[('better', '优于基准线'), ('similar', '接近基准线'), ('worse', '劣于基准线'), ('no_baseline', '无基准线')], help_text='错误率对比结果', max_length=20, null=True, verbose_name='错误率对比结果')),
                ('cpu_comparison', models.CharField(blank=True, choices=[('better', '优于基准线'), ('similar', '接近基准线'), ('worse', '劣于基准线'), ('no_baseline', '无基准线')], help_text='CPU对比结果', max_length=20, null=True, verbose_name='CPU对比结果')),
                ('memory_comparison', models.CharField(blank=True, choices=[('better', '优于基准线'), ('similar', '接近基准线'), ('worse', '劣于基准线'), ('no_baseline', '无基准线')], help_text='内存对比结果', max_length=20, null=True, verbose_name='内存对比结果')),
                ('baseline', models.ForeignKey(help_text='基准线', on_delete=django.db.models.deletion.CASCADE, related_name='comparisons', to='performance.performancebaseline', verbose_name='基准线')),
                ('report', models.ForeignKey(help_text='测试报告', on_delete=django.db.models.deletion.CASCADE, related_name='baseline_comparisons', to='performance.taskreport', verbose_name='测试报告')),
            ],
            options={
                'verbose_name': '基准线对比记录表',
                'verbose_name_plural': '基准线对比记录表',
                'db_table': 'performance_baseline_comparison',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='AlertRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('name', models.CharField(help_text='规则名称', max_length=128, verbose_name='规则名称')),
                ('description', models.TextField(blank=True, help_text='规则描述', null=True, verbose_name='规则描述')),
                ('metric_type', models.CharField(choices=[('tps', 'TPS'), ('response_time', '响应时间'), ('error_rate', '错误率'), ('cpu', 'CPU使用率'), ('memory', '内存使用率'), ('concurrent_users', '并发用户数')], help_text='监控指标', max_length=50, verbose_name='监控指标')),
                ('condition', models.CharField(choices=[('gt', '大于'), ('lt', '小于'), ('gte', '大于等于'), ('lte', '小于等于'), ('eq', '等于')], help_text='比较条件', max_length=10, verbose_name='比较条件')),
                ('threshold', models.FloatField(help_text='阈值', verbose_name='阈值')),
                ('duration_minutes', models.IntegerField(default=5, help_text='持续时间(分钟)', verbose_name='持续时间')),
                ('severity', models.CharField(choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('critical', '严重')], help_text='告警级别', max_length=20, verbose_name='告警级别')),
                ('notification_types', models.TextField(blank=True, help_text='通知方式(JSON格式)', null=True, verbose_name='通知方式')),
                ('notification_config', models.TextField(blank=True, help_text='通知配置(JSON格式)', null=True, verbose_name='通知配置')),
                ('is_enabled', models.BooleanField(default=True, help_text='是否启用', verbose_name='是否启用')),
                ('task_ids', models.TextField(blank=True, help_text='应用任务ID(JSON格式)', null=True, verbose_name='应用任务ID')),
                ('created_by', models.CharField(help_text='创建人', max_length=50, verbose_name='创建人')),
                ('project', models.ForeignKey(help_text='项目', on_delete=django.db.models.deletion.CASCADE, related_name='alert_rules', to='projects.project', verbose_name='项目')),
            ],
            options={
                'verbose_name': '告警规则表',
                'verbose_name_plural': '告警规则表',
                'db_table': 'performance_alert_rule',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='AlertEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('alert_level', models.CharField(choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('critical', '严重')], help_text='告警级别', max_length=20, verbose_name='告警级别')),
                ('metric_value', models.FloatField(help_text='当前指标值', verbose_name='当前指标值')),
                ('threshold_value', models.FloatField(help_text='阈值', verbose_name='阈值')),
                ('message', models.TextField(help_text='告警消息', verbose_name='告警消息')),
                ('status', models.CharField(choices=[('active', '活跃'), ('resolved', '已解决'), ('acknowledged', '已确认')], default='active', help_text='状态', max_length=20, verbose_name='状态')),
                ('triggered_time', models.DateTimeField(auto_now_add=True, help_text='触发时间', verbose_name='触发时间')),
                ('resolved_time', models.DateTimeField(blank=True, help_text='解决时间', null=True, verbose_name='解决时间')),
                ('acknowledged_by', models.CharField(blank=True, help_text='确认人', max_length=50, null=True, verbose_name='确认人')),
                ('acknowledged_time', models.DateTimeField(blank=True, help_text='确认时间', null=True, verbose_name='确认时间')),
                ('notification_sent', models.BooleanField(default=False, help_text='是否已发送通知', verbose_name='是否已发送通知')),
                ('notification_details', models.TextField(blank=True, help_text='通知详情(JSON格式)', null=True, verbose_name='通知详情')),
                ('report', models.ForeignKey(blank=True, help_text='相关报告', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='alert_events', to='performance.taskreport', verbose_name='相关报告')),
                ('rule', models.ForeignKey(help_text='告警规则', on_delete=django.db.models.deletion.CASCADE, related_name='events', to='performance.alertrule', verbose_name='告警规则')),
                ('task', models.ForeignKey(help_text='任务', on_delete=django.db.models.deletion.CASCADE, related_name='alert_events', to='performance.performancetask', verbose_name='任务')),
            ],
            options={
                'verbose_name': '告警事件表',
                'verbose_name_plural': '告警事件表',
                'db_table': 'performance_alert_event',
                'ordering': ['-triggered_time'],
            },
        ),
    ]
