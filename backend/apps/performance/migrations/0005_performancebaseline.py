# Generated by Django 4.1.13 on 2025-07-24 07:29

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0002_alter_mock_update_time_alter_mockdetail_update_time'),
        ('performance', '0004_alter_alertrule_notification_config'),
    ]

    operations = [
        migrations.CreateModel(
            name='PerformanceBaseline',
            fields=[
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='基准线ID', primary_key=True, serialize=False, verbose_name='基准线ID')),
                ('name', models.CharField(help_text='基准线名称', max_length=128, verbose_name='基准线名称')),
                ('description', models.TextField(blank=True, help_text='基准线描述', null=True, verbose_name='基准线描述')),
                ('environment', models.CharField(default='default', help_text='环境', max_length=50, verbose_name='环境')),
                ('created_by', models.CharField(default='system', help_text='创建人', max_length=50, verbose_name='创建人')),
                ('is_active', models.BooleanField(default=True, help_text='是否活跃', verbose_name='是否活跃')),
                ('response_time', models.FloatField(default=0, help_text='响应时间(ms)', verbose_name='响应时间')),
                ('tps', models.FloatField(default=0, help_text='每秒事务数(TPS)', verbose_name='TPS')),
                ('error_rate', models.FloatField(default=0, help_text='错误率(%)', verbose_name='错误率')),
                ('cpu_usage', models.FloatField(default=0, help_text='CPU使用率(%)', verbose_name='CPU使用率')),
                ('memory_usage', models.FloatField(default=0, help_text='内存使用率(%)', verbose_name='内存使用率')),
                ('response_time_tolerance', models.FloatField(default=15.0, help_text='响应时间容差百分比(%)', verbose_name='响应时间容差')),
                ('tps_tolerance', models.FloatField(default=10.0, help_text='TPS容差百分比(%)', verbose_name='TPS容差')),
                ('error_rate_tolerance', models.FloatField(default=50.0, help_text='错误率容差百分比(%)', verbose_name='错误率容差')),
                ('cpu_usage_tolerance', models.FloatField(default=20.0, help_text='CPU使用率容差百分比(%)', verbose_name='CPU使用率容差')),
                ('memory_usage_tolerance', models.FloatField(default=20.0, help_text='内存使用率容差百分比(%)', verbose_name='内存使用率容差')),
                ('baseline_data', models.JSONField(blank=True, default=dict, help_text='原始基准数据', verbose_name='原始基准数据')),
                ('project', models.ForeignKey(help_text='项目', on_delete=django.db.models.deletion.CASCADE, related_name='baselines', to='projects.project', verbose_name='项目')),
                ('task', models.ForeignKey(help_text='关联任务', on_delete=django.db.models.deletion.CASCADE, related_name='baselines', to='performance.performancetask', verbose_name='关联任务')),
            ],
            options={
                'verbose_name': '性能基准线',
                'verbose_name_plural': '性能基准线',
                'db_table': 'performance_baseline',
                'ordering': ['-create_time'],
            },
        ),
    ]
