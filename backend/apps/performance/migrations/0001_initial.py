# Generated by Django 4.1.13 on 2025-07-07 07:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('projects', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PerformanceTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(blank=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('taskType', models.CharField(choices=[('10', '普通任务'), ('20', '定时任务')], help_text='任务类型', max_length=50, verbose_name='任务类型')),
                ('desc', models.CharField(blank=True, help_text='任务描述', max_length=200, null=True, verbose_name='任务描述')),
                ('taskName', models.CharField(help_text='任务名称', max_length=50, verbose_name='任务名称')),
                ('runPattern', models.CharField(blank=True, choices=[('10', '并发模式'), ('20', '阶梯模式')], help_text='运行模式', max_length=50, null=True, verbose_name='运行模式')),
                ('status', models.CharField(blank=True, choices=[('1', '执行中'), ('0', '执行完成'), ('99', '执行失败')], help_text='运行状态', max_length=50, null=True, verbose_name='运行状态')),
                ('distributed_mode', models.CharField(choices=[('single', '单机模式'), ('distributed', '分布式模式')], default='single', help_text='分布式模式', max_length=20, verbose_name='分布式模式')),
                ('total_workers', models.IntegerField(default=1, help_text='总工作进程数', verbose_name='总工作进程数')),
                ('worker_distribution', models.TextField(blank=True, default='{}', help_text='工作进程分配', verbose_name='工作进程分配')),
            ],
            options={
                'verbose_name': '性能任务表',
                'verbose_name_plural': '性能任务表',
                'db_table': 'performance_task',
            },
        ),
        migrations.CreateModel(
            name='TaskScence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='场景名称', max_length=100, verbose_name='场景名称')),
                ('weight', models.IntegerField(default=1, help_text='权重', verbose_name='权重')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('env', models.ManyToManyField(blank=True, help_text='环境id', null=True, related_name='taskScence', to='projects.testenv', verbose_name='环境id')),
                ('task', models.ForeignKey(help_text='性能任务id', on_delete=django.db.models.deletion.CASCADE, related_name='taskScence', to='performance.performancetask', verbose_name='性能任务id')),
            ],
            options={
                'verbose_name': '性能场景表',
                'verbose_name_plural': '性能场景表',
                'db_table': 'performance_TaskScence',
            },
        ),
        migrations.CreateModel(
            name='TaskStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(blank=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('name', models.CharField(help_text='步骤控制器名称', max_length=50, verbose_name='步骤控制器名称')),
                ('dlg', models.BooleanField(default=False, help_text='循环控制器是否展开', verbose_name='循环控制器是否展开')),
                ('inputDlg', models.BooleanField(default=False, help_text='自定义脚本是否展开', verbose_name='自定义脚本是否展开')),
                ('type', models.CharField(choices=[('api', 'HTTP接口'), ('tcp', 'TCP连接'), ('udp', 'UDP连接'), ('websocket', 'WebSocket'), ('wait', '等待'), ('script', '自定义脚本'), ('loop', '循环控制器')], help_text='步骤控制器类型', max_length=50, verbose_name='步骤控制器类型')),
                ('protocol', models.CharField(choices=[('HTTP', 'HTTP'), ('HTTPS', 'HTTPS'), ('TCP', 'TCP'), ('UDP', 'UDP'), ('WebSocket', 'WebSocket')], default='HTTP', help_text='协议类型', max_length=20, verbose_name='协议类型')),
                ('content', models.TextField(blank=True, default='{}', help_text='步骤控制器内容', verbose_name='步骤控制器内容')),
                ('script', models.TextField(blank=True, default='', help_text='步骤控制器脚本', null=True, verbose_name='步骤控制器脚本')),
                ('desc', models.CharField(blank=True, help_text='步骤控制器描述', max_length=200, null=True, verbose_name='步骤控制器描述')),
                ('status', models.BooleanField(default=True, help_text='是否启用', verbose_name='是否启用')),
                ('debugResult', models.BooleanField(default=True, help_text='是否正常', verbose_name='是否正常')),
                ('weight', models.IntegerField(blank=True, help_text='权重', null=True, verbose_name='权重')),
                ('timeout', models.IntegerField(default=10, help_text='超时时间(秒)', verbose_name='超时时间(秒)')),
                ('retry_count', models.IntegerField(default=0, help_text='重试次数', verbose_name='重试次数')),
                ('custom_payload', models.TextField(blank=True, help_text='自定义负载(TCP/UDP)', null=True, verbose_name='自定义负载')),
                ('ssl_verify', models.BooleanField(default=True, help_text='SSL验证', verbose_name='SSL验证')),
                ('scence', models.ForeignKey(help_text='性能场景id', on_delete=django.db.models.deletion.CASCADE, to='performance.taskscence', verbose_name='性能场景id')),
            ],
            options={
                'verbose_name': '性能步骤表',
                'verbose_name_plural': '性能步骤表',
                'db_table': 'performance_TaskStep',
            },
        ),
        migrations.CreateModel(
            name='TaskScenceStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(blank=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('sort', models.IntegerField(blank=True, help_text='执行顺序', verbose_name='执行顺序')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='performance.taskscencestep')),
                ('scence', models.ForeignKey(help_text='性能场景id', on_delete=django.db.models.deletion.CASCADE, to='performance.taskscence', verbose_name='性能场景id')),
                ('step', models.ForeignKey(help_text='性能步骤id', on_delete=django.db.models.deletion.CASCADE, to='performance.taskstep', verbose_name='性能步骤id')),
                ('task', models.ForeignKey(help_text='性能任务id', on_delete=django.db.models.deletion.CASCADE, to='performance.performancetask', verbose_name='性能任务id')),
            ],
            options={
                'verbose_name': '性能任务场景步骤表',
                'verbose_name_plural': '性能任务场景步骤表',
                'db_table': 'performance_taskScenceStep',
            },
        ),
        migrations.CreateModel(
            name='TaskReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(blank=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('reportName', models.CharField(help_text='报告名称', max_length=50, verbose_name='报告名称')),
                ('desc', models.CharField(blank=True, help_text='报告描述', max_length=200, null=True, verbose_name='报告描述')),
                ('reportStatus', models.CharField(blank=True, choices=[('1', '报告-执行中'), ('0', '报告-已完成'), ('99', '报告-运行失败')], help_text='运行状态', max_length=50, null=True, verbose_name='运行状态')),
                ('avgTps', models.FloatField(blank=True, help_text='平均TPS', null=True, verbose_name='平均TPS')),
                ('avgCpu', models.FloatField(blank=True, help_text='平均CPU使用率', null=True, verbose_name='平均CPU使用率')),
                ('avgMemory', models.FloatField(blank=True, help_text='平均内存使用率', null=True, verbose_name='平均内存使用率')),
                ('maxTps', models.FloatField(blank=True, help_text='最大TPS', null=True, verbose_name='最大TPS')),
                ('minTps', models.FloatField(blank=True, help_text='最小TPS', null=True, verbose_name='最小TPS')),
                ('avgResponseTime', models.FloatField(blank=True, help_text='平均响应时间(ms)', null=True, verbose_name='平均响应时间')),
                ('maxResponseTime', models.FloatField(blank=True, help_text='最大响应时间(ms)', null=True, verbose_name='最大响应时间')),
                ('minResponseTime', models.FloatField(blank=True, help_text='最小响应时间(ms)', null=True, verbose_name='最小响应时间')),
                ('p50ResponseTime', models.FloatField(blank=True, help_text='50%响应时间(ms)', null=True, verbose_name='50%响应时间')),
                ('p90ResponseTime', models.FloatField(blank=True, help_text='90%响应时间(ms)', null=True, verbose_name='90%响应时间')),
                ('p95ResponseTime', models.FloatField(blank=True, help_text='95%响应时间(ms)', null=True, verbose_name='95%响应时间')),
                ('p99ResponseTime', models.FloatField(blank=True, help_text='99%响应时间(ms)', null=True, verbose_name='99%响应时间')),
                ('totalRequests', models.IntegerField(blank=True, help_text='总请求数', null=True, verbose_name='总请求数')),
                ('successRequests', models.IntegerField(blank=True, help_text='成功请求数', null=True, verbose_name='成功请求数')),
                ('failedRequests', models.IntegerField(blank=True, help_text='失败请求数', null=True, verbose_name='失败请求数')),
                ('errorRate', models.FloatField(blank=True, help_text='错误率(%)', null=True, verbose_name='错误率')),
                ('maxUsers', models.IntegerField(blank=True, help_text='最大用户数', null=True, verbose_name='最大用户数')),
                ('avgUsers', models.FloatField(blank=True, help_text='平均用户数', null=True, verbose_name='平均用户数')),
                ('startTime', models.DateTimeField(blank=True, help_text='测试开始时间', null=True, verbose_name='测试开始时间')),
                ('endTime', models.DateTimeField(blank=True, help_text='测试结束时间', null=True, verbose_name='测试结束时间')),
                ('duration', models.IntegerField(blank=True, help_text='测试持续时间(秒)', null=True, verbose_name='测试持续时间')),
                ('reportResult', models.TextField(blank=True, help_text='详细报告数据(JSON)', null=True, verbose_name='详细报告数据')),
                ('resultAnalyse', models.TextField(blank=True, help_text='结果分析', null=True, verbose_name='结果分析')),
                ('executor', models.CharField(blank=True, help_text='执行人', max_length=50, null=True, verbose_name='执行人')),
                ('env', models.ForeignKey(blank=True, help_text='测试环境', null=True, on_delete=django.db.models.deletion.SET_NULL, to='projects.testenv', verbose_name='测试环境')),
                ('task', models.ForeignKey(help_text='性能任务id', on_delete=django.db.models.deletion.CASCADE, to='performance.performancetask', verbose_name='性能任务id')),
            ],
            options={
                'verbose_name': '性能测试报告表',
                'verbose_name_plural': '性能测试报告表',
                'db_table': 'performance_task_report',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='server',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(blank=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('name', models.CharField(help_text='服务器名称', max_length=50, verbose_name='服务器名称')),
                ('host_ip', models.CharField(help_text='服务器ip', max_length=50, verbose_name='服务器ip')),
                ('host_port', models.IntegerField(blank=True, help_text='端口号', null=True, verbose_name='端口号')),
                ('sys_user_name', models.CharField(help_text='用户名', max_length=50, verbose_name='用户名')),
                ('sys_user_passwd', models.CharField(help_text='密码', max_length=128, verbose_name='密码')),
                ('default_code', models.BooleanField(default=False, verbose_name='是否设置为默认服务器')),
                ('server_type', models.CharField(choices=[('master', '主节点'), ('worker', '工作节点')], default='master', help_text='服务器类型', max_length=10, verbose_name='服务器类型')),
                ('server_status', models.CharField(choices=[('active', '活跃'), ('inactive', '不活跃'), ('error', '错误')], default='inactive', help_text='服务器状态', max_length=10, verbose_name='服务器状态')),
                ('locust_master_host', models.CharField(blank=True, help_text='Locust主节点地址', max_length=50, null=True, verbose_name='Locust主节点地址')),
                ('locust_master_port', models.IntegerField(blank=True, default=5557, help_text='Locust主节点端口', null=True, verbose_name='Locust主节点端口')),
                ('max_workers', models.IntegerField(default=4, help_text='最大工作进程数', verbose_name='最大工作进程数')),
                ('cpu_cores', models.IntegerField(blank=True, help_text='CPU核心数', null=True, verbose_name='CPU核心数')),
                ('memory_gb', models.FloatField(blank=True, help_text='内存大小(GB)', null=True, verbose_name='内存大小(GB)')),
                ('project', models.ForeignKey(help_text='项目id', on_delete=django.db.models.deletion.CASCADE, related_name='server', to='projects.project', verbose_name='项目id')),
            ],
            options={
                'verbose_name': '性能测试服务器表',
                'verbose_name_plural': '性能测试服务器表',
                'db_table': 'performance_server',
            },
        ),
        migrations.CreateModel(
            name='presetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creator', models.CharField(blank=True, default='', help_text='创建人', max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('modifier', models.CharField(blank=True, default='', help_text='修改人', max_length=50, null=True, verbose_name='修改人')),
                ('update_time', models.DateTimeField(blank=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('name', models.CharField(help_text='预置配置名称', max_length=128, verbose_name='预置配置名称')),
                ('rule', models.CharField(default='* * * * *', help_text='定时执行规则', max_length=80, verbose_name='定时任务')),
                ('taskType', models.CharField(choices=[('10', '普通任务'), ('20', '定时任务')], help_text='任务类型', max_length=50, verbose_name='任务类型')),
                ('logMode', models.CharField(choices=[('0', '关闭'), ('10', '开启-全部日志'), ('20', '开启-仅成功日志'), ('30', '开启-仅失败日志')], help_text='日志模式', max_length=50, verbose_name='日志模式')),
                ('pressureMode', models.CharField(choices=[('10', '并发模式'), ('20', '阶梯模式')], help_text='压测模式', max_length=50, verbose_name='压测模式')),
                ('timeUnit', models.CharField(choices=[('s', '秒'), ('m', '分'), ('h', '时')], help_text='时间单位', max_length=50, verbose_name='时间单位')),
                ('control', models.CharField(choices=[('10', '集合模式'), ('20', '单独模式')], help_text='控制模式', max_length=50, verbose_name='控制模式')),
                ('resource', models.CharField(choices=[('10', '默认'), ('20', '自定义')], help_text='机器选择方式', max_length=50, verbose_name='机器选择方式')),
                ('pressureConfig', models.TextField(default='{}', help_text='压测配置', verbose_name='压测配置')),
                ('isSetting', models.BooleanField(default=False, help_text='区分任务配置和预配置', verbose_name='区分预配置和任务配置')),
                ('thinkTime', models.TextField(blank=True, default='[]', help_text='思考时间', null=True, verbose_name='思考时间')),
                ('thinkTimeType', models.CharField(choices=[('10', '固定'), ('20', '随机')], help_text='思考时间类型', max_length=50, verbose_name='思考时间类型')),
                ('project', models.ForeignKey(help_text='项目id', on_delete=django.db.models.deletion.CASCADE, related_name='presetting', to='projects.project', verbose_name='项目id')),
                ('serverArray', models.ManyToManyField(blank=True, help_text='服务器id', to='performance.server', verbose_name='服务器id')),
                ('task', models.OneToOneField(help_text='性能任务id', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='presetting', to='performance.performancetask', verbose_name='性能任务id')),
            ],
            options={
                'verbose_name': '性能预配置表',
                'verbose_name_plural': '性能预配置表',
                'db_table': 'performance_presetting',
            },
        ),
        migrations.AddField(
            model_name='performancetask',
            name='master_server',
            field=models.ForeignKey(blank=True, help_text='主服务器', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='master_tasks', to='performance.server', verbose_name='主服务器'),
        ),
        migrations.AddField(
            model_name='performancetask',
            name='project',
            field=models.ForeignKey(help_text='项目id', on_delete=django.db.models.deletion.CASCADE, related_name='perfTask', to='projects.project', verbose_name='项目id'),
        ),
        migrations.AddField(
            model_name='performancetask',
            name='worker_servers',
            field=models.ManyToManyField(blank=True, help_text='工作服务器', related_name='worker_tasks', to='performance.server', verbose_name='工作服务器'),
        ),
    ]
