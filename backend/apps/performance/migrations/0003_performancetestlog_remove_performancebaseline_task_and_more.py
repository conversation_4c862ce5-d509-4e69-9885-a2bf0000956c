# Generated by Django 4.1.13 on 2025-07-23 09:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('performance', '0002_alter_performancetask_update_time_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PerformanceTestLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(help_text='日志时间', verbose_name='日志时间')),
                ('level', models.CharField(choices=[('info', '信息'), ('warning', '警告'), ('error', '错误'), ('debug', '调试')], help_text='日志级别', max_length=20, verbose_name='日志级别')),
                ('message', models.TextField(help_text='日志内容', verbose_name='日志内容')),
                ('source', models.CharField(blank=True, help_text='日志来源', max_length=100, null=True, verbose_name='日志来源')),
                ('user_count', models.IntegerField(blank=True, help_text='当前用户数', null=True, verbose_name='当前用户数')),
                ('request_name', models.CharField(blank=True, help_text='请求名称', max_length=200, null=True, verbose_name='请求名称')),
                ('response_time', models.FloatField(blank=True, help_text='响应时间(ms)', null=True, verbose_name='响应时间')),
                ('exception', models.TextField(blank=True, help_text='异常信息', null=True, verbose_name='异常信息')),
                ('create_time', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '性能测试日志',
                'verbose_name_plural': '性能测试日志',
                'db_table': 'performance_test_log',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.RemoveField(
            model_name='performancebaseline',
            name='task',
        ),
        migrations.AddField(
            model_name='taskreport',
            name='failuresPerSecond',
            field=models.FloatField(blank=True, help_text='每秒失败数', null=True, verbose_name='每秒失败数'),
        ),
        migrations.AlterField(
            model_name='alertrule',
            name='metric_type',
            field=models.CharField(choices=[('tps', 'TPS'), ('response_time', '响应时间'), ('error_rate', '错误率'), ('cpu_usage', 'CPU使用率'), ('memory_usage', '内存使用率'), ('concurrent_users', '并发用户数'), ('avg_response_time', '平均响应时间')], help_text='监控指标', max_length=50, verbose_name='监控指标'),
        ),
        migrations.AlterField(
            model_name='performancetask',
            name='worker_distribution',
            field=models.JSONField(blank=True, default=dict, help_text='工作进程分配', verbose_name='工作进程分配'),
        ),
        migrations.AlterField(
            model_name='presetting',
            name='pressureConfig',
            field=models.JSONField(default=dict, help_text='压测配置', verbose_name='压测配置'),
        ),
        migrations.AlterField(
            model_name='presetting',
            name='thinkTime',
            field=models.JSONField(blank=True, default=[], help_text='思考时间', null=True, verbose_name='思考时间'),
        ),
        migrations.AlterField(
            model_name='taskstep',
            name='content',
            field=models.JSONField(blank=True, default=dict, help_text='步骤控制器内容', verbose_name='步骤控制器内容'),
        ),
        migrations.DeleteModel(
            name='BaselineComparison',
        ),
        migrations.DeleteModel(
            name='PerformanceBaseline',
        ),
        migrations.AddField(
            model_name='performancetestlog',
            name='report',
            field=models.ForeignKey(help_text='关联的测试报告', on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='performance.taskreport', verbose_name='测试报告'),
        ),
        migrations.AddIndex(
            model_name='performancetestlog',
            index=models.Index(fields=['report', '-timestamp'], name='performance_report__6b1884_idx'),
        ),
        migrations.AddIndex(
            model_name='performancetestlog',
            index=models.Index(fields=['level', '-timestamp'], name='performance_level_d852b4_idx'),
        ),
    ]
