#!/bin/bash

# HRUN 测试平台生产环境停止脚本

echo "🛑 停止 HRUN 测试平台生产环境..."

# 检查PID文件是否存在
if [ -f "logs/backend.pid" ]; then
    BACKEND_PID=$(cat logs/backend.pid)
    if ps -p $BACKEND_PID > /dev/null 2>&1; then
        echo "🔄 停止后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        sleep 2
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            echo "⚠️  强制停止后端服务..."
            kill -9 $BACKEND_PID
        fi
        echo "✅ 后端服务已停止"
    else
        echo "⚠️  后端服务进程不存在"
    fi
    rm -f logs/backend.pid
else
    echo "⚠️  未找到后端PID文件，尝试查找gunicorn进程..."
    pkill -f "gunicorn.*primaryApp.wsgi" && echo "✅ 后端服务已停止" || echo "⚠️  未找到后端进程"
fi

if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null 2>&1; then
        echo "🔄 停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        sleep 2
        if ps -p $FRONTEND_PID > /dev/null 2>&1; then
            echo "⚠️  强制停止前端服务..."
            kill -9 $FRONTEND_PID
        fi
        echo "✅ 前端服务已停止"
    else
        echo "⚠️  前端服务进程不存在"
    fi
    rm -f logs/frontend.pid
else
    echo "⚠️  未找到前端PID文件，尝试查找serve进程..."
    pkill -f "serve.*nginx/dist" && echo "✅ 前端服务已停止" || echo "⚠️  未找到前端进程"
fi

echo ""
echo "✅ HRUN 测试平台生产环境已停止"
echo ""
echo "📝 日志文件保留在 logs/ 目录中"
echo "🚀 重新启动请运行: ./start_production.sh"
echo ""
