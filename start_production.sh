#!/bin/bash

# HRUN 测试平台生产环境启动脚本

echo "🚀 启动 HRUN 测试平台生产环境..."

# 检查是否在项目根目录
if [ ! -d "frontend" ] || [ ! -d "backend" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 检查必要的服务
echo "🔍 检查系统服务..."

# 检查MySQL
if ! pgrep -x "mysqld" > /dev/null; then
    echo "⚠️  MySQL服务未运行，尝试启动..."
    brew services start mysql || {
        echo "❌ MySQL启动失败，请手动启动MySQL服务"
        exit 1
    }
fi

# 检查Redis
if ! pgrep -x "redis-server" > /dev/null; then
    echo "⚠️  Redis服务未运行，尝试启动..."
    brew services start redis || {
        echo "❌ Redis启动失败，请手动启动Redis服务"
        exit 1
    }
fi

echo "✅ 系统服务检查完成"

# 构建前端生产版本
echo "🔨 构建前端生产版本..."
cd frontend
npm run build
if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi
cd ..

echo "✅ 前端构建完成"

# 启动后端生产服务
echo "🌐 启动后端生产服务..."
cd backend
source venv/bin/activate

# 检查gunicorn是否安装
if ! command -v gunicorn &> /dev/null; then
    echo "📦 安装gunicorn..."
    pip install gunicorn
fi

# 启动后端服务（后台运行）
nohup gunicorn primaryApp.wsgi:application --bind 0.0.0.0:8000 --workers 4 --timeout 120 > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../logs/backend.pid

cd ..
echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"

# 启动前端生产服务
echo "🎨 启动前端生产服务..."

# 检查serve是否安装
if ! command -v serve &> /dev/null; then
    echo "📦 安装serve..."
    npm install -g serve
fi

# 创建日志目录
mkdir -p logs

# 启动前端服务（后台运行）
nohup serve -s nginx/dist -l 8080 --cors > logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > logs/frontend.pid

echo "✅ 前端服务已启动 (PID: $FRONTEND_PID)"

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
echo "🔍 检查服务状态..."

# 检查后端
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/ | grep -q "200"; then
    echo "✅ 后端服务正常 (http://localhost:8000)"
else
    echo "❌ 后端服务异常"
fi

# 检查前端
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/ | grep -q "200"; then
    echo "✅ 前端服务正常 (http://localhost:8080)"
else
    echo "❌ 前端服务异常"
fi

echo ""
echo "🎉 HRUN 测试平台生产环境启动完成！"
echo ""
echo "📋 访问信息："
echo "   前端界面: http://localhost:8080"
echo "   网络访问: http://*************:8080"
echo "   后端API:  http://localhost:8000"
echo "   管理后台: http://localhost:8000/admin"
echo ""
echo "🔑 默认账户："
echo "   用户名: admin"
echo "   密码:   123456"
echo ""
echo "📝 日志文件："
echo "   后端日志: logs/backend.log"
echo "   前端日志: logs/frontend.log"
echo ""
echo "🛑 停止服务："
echo "   运行: ./stop_production.sh"
echo ""
